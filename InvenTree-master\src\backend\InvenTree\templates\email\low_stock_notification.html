{% extends "email/email.html" %}

{% load i18n %}
{% load inventree_extras %}

{% block title %}
{{ message }}
{% if link %}
<p>{% trans "Click on the following link to view this part" %}: <a href="{{ link }}">{{ link }}</a></p>
{% endif %}
{% endblock title %}

{% block body %}
<tr style="height: 3rem; border-bottom: 1px solid">
    <th>{% trans "Part" %}</th>
    <th>{% trans "Total Stock" %}</th>
    <th>{% trans "Available" %}</th>
    <th>{% trans "Minimum Quantity" %}</th>
</tr>

<tr style="height: 3rem">
    <td style="text-align: center;">{{ part.full_name }}</td>
    <td style="text-align: center;">{% decimal part.total_stock %}</td>
    <td style="text-align: center;">{% decimal part.available_stock %}</td>
    <td style="text-align: center;">{% decimal part.minimum_stock %}</td>
</tr>
{% endblock body %}

{% block footer_prefix %}
<p><em>{% blocktrans with part=part.name %}You are receiving this email because you are subscribed to notifications for this part {% endblocktrans %}.</em></p>
{% endblock footer_prefix %}
