# Generated by Django 3.0.7 on 2021-01-11 21:54

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('users', '0005_owner_model'),
        ('stock', '0056_stockitem_expiry_date'),
    ]

    operations = [
        migrations.AddField(
            model_name='stockitem',
            name='owner',
            field=models.ForeignKey(blank=True, help_text='Select Owner', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='stock_items', to='users.Owner'),
        ),
        migrations.AddField(
            model_name='stocklocation',
            name='owner',
            field=models.ForeignKey(blank=True, help_text='Select Owner', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='stock_locations', to='users.Owner'),
        ),
    ]
