flags:
  apps:
    paths:
      - src/backend/InvenTree/build
      - src/backend/InvenTree/company
      - src/backend/InvenTree/data_exporter
      - src/backend/InvenTree/importer
      - src/backend/InvenTree/machine
      - src/backend/InvenTree/order
      - src/backend/InvenTree/part
      - src/backend/InvenTree/plugin
      - src/backend/InvenTree/report
      - src/backend/InvenTree/stock
      - src/backend/InvenTree/users
      - src/backend/InvenTree/web
  general:
    paths:
      - src/backend/InvenTree/generic
      - src/backend/InvenTree/common

coverage:
  status:
    project:
      default:
        target: 82%
      apps:
        flags:
          - apps
        target: 90%
      general:
        flags:
          - general
        target: 95%
    patch: off

github_checks:
    annotations: true

flag_management:
  default_rules:
    carryforward: true
  individual_flags:
    - name: backend
      carryforward: true
      statuses:
        - type: project
          target: 85%
    - name: migrations
      carryforward: true
      statuses:
        - type: project
          target: 40%
    - name: web
      carryforward: true
      statuses:
        - type: project
          target: 45%

comment:
  require_bundle_changes: True
  bundle_change_threshold: "1Kb"

bundle_analysis:
  warning_threshold: "5%"
  status: "informational"
