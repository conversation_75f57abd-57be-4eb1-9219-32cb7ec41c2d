#!/usr/bin/env python
"""
Simple Pesapal Integration Test
Tests core functionality without Django test client
"""
import os
import sys
import django
from decimal import Decimal

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'deepforest_backend.settings')
django.setup()

from django.contrib.auth.models import User
from payments.models import Order, OrderItem, Payment
from payments.pesapal_client import PesapalClient
from payments.pesapal_webhooks import PesapalWebhookHandler
from ecommerce.models import Product, Category, DeliveryZone


def test_pesapal_client():
    """Test Pesapal client functionality"""
    print("=== Testing Pesapal Client ===")
    
    try:
        client = PesapalClient()
        print("✓ Pesapal client initialized successfully")
        
        # Test payment initiation
        order_data = {
            'amount': '1200.00',
            'description': 'Test Order Payment',
            'type': 'MERCHANT',
            'reference': 'TEST_REF_001',
            'first_name': '<PERSON>',
            'last_name': 'Doe',
            'email': '<EMAIL>',
            'phone': '254700000000'
        }
        
        iframe_url = client.initiate_payment(order_data)
        print(f"✓ Payment initiation successful")
        print(f"  URL length: {len(iframe_url)} characters")
        print(f"  Contains oauth_signature: {'oauth_signature' in iframe_url}")
        
        return True
        
    except Exception as e:
        print(f"✗ Pesapal client test failed: {str(e)}")
        return False


def test_order_creation():
    """Test order creation and management"""
    print("\n=== Testing Order Creation ===")
    
    try:
        # Create test user
        user, created = User.objects.get_or_create(
            username='ordertest',
            defaults={
                'email': '<EMAIL>',
                'first_name': 'Order',
                'last_name': 'Test'
            }
        )
        
        # Create test category and product
        category, _ = Category.objects.get_or_create(
            name='Test Category',
            defaults={'description': 'Test category'}
        )
        
        product, _ = Product.objects.get_or_create(
            sku='TEST_PRODUCT_001',
            defaults={
                'name': 'Test Product',
                'description': 'Test product for orders',
                'price_ksh': Decimal('1000.00'),
                'category': category,
                'stock_quantity': 10
            }
        )
        
        # Create order
        import time
        order_ref = f'ORDER_TEST_{int(time.time())}'
        order = Order.objects.create(
            user=user,
            order_reference=order_ref,
            total_amount_ksh=Decimal('1000.00'),
            delivery_cost_ksh=Decimal('200.00'),
            delivery_address='123 Test Street, Nairobi',
            delivery_county='nairobi'
        )
        
        # Create order item
        order_item = OrderItem.objects.create(
            order=order,
            product=product,
            quantity=1,
            unit_price_ksh=product.price_ksh
        )
        
        print(f"✓ Order created successfully")
        print(f"  Order Reference: {order.order_reference}")
        print(f"  Total with delivery: KSH {order.total_with_delivery}")
        print(f"  Order items: {order.items.count()}")
        
        return order
        
    except Exception as e:
        print(f"✗ Order creation failed: {str(e)}")
        return None


def test_payment_status_update():
    """Test payment status update functionality"""
    print("\n=== Testing Payment Status Update ===")
    
    try:
        # Create an order first
        order = test_order_creation()
        if not order:
            return False
        
        # Create payment record
        payment = Payment.objects.create(
            order=order,
            pesapal_transaction_id='TEST_TRANSACTION_123',
            amount_ksh=order.total_with_delivery,
            payment_method='mpesa',
            pesapal_merchant_reference=order.order_reference,
            pesapal_tracking_id='TEST_TRACKING_123'
        )
        
        print(f"✓ Payment record created")
        print(f"  Transaction ID: {payment.pesapal_transaction_id}")
        print(f"  Initial status: {payment.status}")
        
        # Test status update
        payment.mark_as_completed()
        order.refresh_from_db()
        
        print(f"✓ Payment marked as completed")
        print(f"  Payment status: {payment.status}")
        print(f"  Order status: {order.status}")
        print(f"  Order payment status: {order.payment_status}")
        
        return True
        
    except Exception as e:
        print(f"✗ Payment status update failed: {str(e)}")
        return False


def test_webhook_handler():
    """Test webhook handler functionality"""
    print("\n=== Testing Webhook Handler ===")
    
    try:
        webhook_handler = PesapalWebhookHandler()
        print("✓ Webhook handler initialized")
        
        # Test callback verification
        valid_callback = {
            'pesapal_notification_type': 'CHANGE',
            'pesapal_transaction_tracking_id': 'TEST_TRACKING_123',
            'pesapal_merchant_reference': 'TEST_REF_001'
        }
        
        is_valid = webhook_handler.client.verify_callback(valid_callback)
        print(f"✓ Callback verification: {is_valid}")
        
        # Test status mapping
        mapped_status = webhook_handler._map_pesapal_status('COMPLETED')
        print(f"✓ Status mapping: COMPLETED -> {mapped_status}")
        
        return True
        
    except Exception as e:
        print(f"✗ Webhook handler test failed: {str(e)}")
        return False


def test_database_models():
    """Test database model relationships"""
    print("\n=== Testing Database Models ===")
    
    try:
        # Test model counts
        user_count = User.objects.count()
        order_count = Order.objects.count()
        payment_count = Payment.objects.count()
        product_count = Product.objects.count()
        
        print(f"✓ Database models accessible")
        print(f"  Users: {user_count}")
        print(f"  Orders: {order_count}")
        print(f"  Payments: {payment_count}")
        print(f"  Products: {product_count}")
        
        # Test model relationships
        if order_count > 0:
            order = Order.objects.first()
            print(f"  Sample order items: {order.items.count()}")
            print(f"  Sample order payments: {order.payments.count()}")
        
        return True
        
    except Exception as e:
        print(f"✗ Database model test failed: {str(e)}")
        return False


def main():
    """Run all simple tests"""
    print("🚀 Starting Simple Pesapal Integration Tests")
    print("=" * 60)
    
    results = {}
    
    # Test 1: Pesapal Client
    results['client'] = test_pesapal_client()
    
    # Test 2: Order Creation
    results['order'] = test_order_creation() is not None
    
    # Test 3: Payment Status Update
    results['payment'] = test_payment_status_update()
    
    # Test 4: Webhook Handler
    results['webhook'] = test_webhook_handler()
    
    # Test 5: Database Models
    results['database'] = test_database_models()
    
    print("\n" + "=" * 60)
    print("✅ Simple Pesapal Integration Tests Completed")
    
    # Summary
    print(f"\n📊 Test Results Summary:")
    for test_name, result in results.items():
        status = "✓" if result else "✗"
        print(f"   {test_name.capitalize()}: {status}")
    
    passed = sum(results.values())
    total = len(results)
    print(f"\n🎯 Overall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Pesapal integration is working correctly.")
    else:
        print("⚠️  Some tests failed. Check the output above for details.")
    
    print(f"\n💡 Next Steps:")
    print(f"   1. Configure real Pesapal credentials in .env file")
    print(f"   2. Test with actual payment methods in sandbox")
    print(f"   3. Implement frontend integration")
    print(f"   4. Set up production webhook endpoints")


if __name__ == '__main__':
    main()
