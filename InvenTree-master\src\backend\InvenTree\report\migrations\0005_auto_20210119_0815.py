# Generated by Django 3.0.7 on 2021-01-18 21:15

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('report', '0004_auto_20200823_1104'),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name='testreport',
            name='description',
            field=models.Char<PERSON>ield(help_text='Report template description', max_length=250, verbose_name='Description'),
        ),
        migrations.AlterField(
            model_name='testreport',
            name='filters',
            field=models.CharField(blank=True, help_text='Part query filters (comma-separated list of key=value pairs)', max_length=250, verbose_name='Filters'),
        ),
        migrations.AlterField(
            model_name='testreport',
            name='name',
            field=models.CharField(help_text='Template name', max_length=100, unique=True, verbose_name='Name'),
        ),
        migrations.AlterField(
            model_name='testreport',
            name='template',
            field=models.<PERSON><PERSON>ield(help_text='Report template file', upload_to='report', validators=[django.core.validators.FileExtensionValidator(allowed_extensions=['html', 'htm', 'tex'])], verbose_name='Template'),
        ),
    ]
