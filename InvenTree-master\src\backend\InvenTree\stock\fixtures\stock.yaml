# Create some sample stock items

# 4,000 screws in the dining room
- model: stock.stockitem
  pk: 1
  fields:
    part: 1
    location: 3
    batch: 'B123'
    quantity: 4000
    level: 0
    tree_id: 0
    lft: 0
    rght: 0
    purchase_price: 123
    purchase_price_currency: AUD

# 5,000 screws in the bathroom
- model: stock.stockitem
  pk: 2
  fields:
    part: 1
    location: 2
    quantity: 5000
    level: 0
    tree_id: 0
    lft: 0
    rght: 0

# Capacitor C_22N_0805 in 'Office'
- model: stock.stockitem
  pk: 11
  fields:
    part: 5
    location: 4
    quantity: 666
    level: 0
    tree_id: 0
    lft: 0
    rght: 0

# 1234 2K2 resistors in 'Drawer_1'
- model: stock.stockitem
  pk: 1234
  fields:
    part: 3
    location: 5
    quantity: 1234
    level: 0
    tree_id: 0
    lft: 0
    rght: 0

# Some widgets in drawer 3
- model: stock.stockitem
  pk: 100
  fields:
    batch: "B1234"
    part: 25
    location: 7
    quantity: 10
    delete_on_deplete: False
    level: 0
    tree_id: 0
    lft: 0
    rght: 0

- model: stock.stockitem
  pk: 101
  fields:
    part: 25
    batch: "B2345"
    location: 7
    quantity: 5
    level: 0
    tree_id: 0
    lft: 0
    rght: 0

- model: stock.stockitem
  pk: 102
  fields:
    part: 25
    batch: 'BCDE'
    location: 7
    quantity: 0
    level: 0
    tree_id: 0
    lft: 0
    rght: 0

- model: stock.stockitem
  pk: 105
  fields:
    part: 25
    location: 7
    quantity: 1
    serial: 1000
    serial_int: 1000
    level: 0
    tree_id: 0
    lft: 0
    rght: 0

# Stock items for template / variant parts
- model: stock.stockitem
  pk: 500
  fields:
    part: 10001
    location: 7
    quantity: 5
    batch: "BBAAA"
    level: 0
    tree_id: 0
    lft: 0
    rght: 0

- model: stock.stockitem
  pk: 501
  fields:
    part: 10001
    location: 7
    quantity: 1
    serial: 1
    serial_int: 1
    level: 0
    tree_id: 0
    lft: 0
    rght: 0

- model: stock.stockitem
  pk: 502
  fields:
    part: 10001
    location: 7
    quantity: 1
    serial: 2
    serial_int: 2
    level: 0
    tree_id: 0
    lft: 0
    rght: 0

- model: stock.stockitem
  pk: 503
  fields:
    part: 10001
    location: 7
    quantity: 1
    serial: 3
    serial_int: 3
    level: 0
    tree_id: 0
    lft: 0
    rght: 0

- model: stock.stockitem
  pk: 504
  fields:
    part: 10001
    location: 7
    quantity: 1
    serial: 4
    serial_int: 4
    level: 0
    tree_id: 0
    lft: 0
    rght: 0

- model: stock.stockitem
  pk: 505
  fields:
    part: 10001
    location: 7
    quantity: 1
    serial: 5
    serial_int: 5
    level: 0
    tree_id: 0
    lft: 0
    rght: 0

- model: stock.stockitem
  pk: 510
  fields:
    part: 10002
    location: 7
    quantity: 1
    serial: 10
    serial_int: 10
    level: 0
    tree_id: 0
    lft: 0
    rght: 0

- model: stock.stockitem
  pk: 511
  fields:
    part: 10002
    location: 7
    quantity: 1
    serial: 11
    serial_int: 11
    level: 0
    tree_id: 0
    lft: 0
    rght: 0

- model: stock.stockitem
  pk: 512
  fields:
    part: 10002
    location: 7
    quantity: 1
    serial: 12
    serial_int: 12
    level: 0
    tree_id: 0
    lft: 0
    rght: 0

- model: stock.stockitem
  pk: 520
  fields:
    part: 10004
    location: 7
    quantity: 1
    serial: 20
    serial_int: 20
    level: 0
    tree_id: 0
    lft: 0
    rght: 0
    expiry_date: "1990-10-10"
    barcode_hash: 9e5ae7fc20568ed4814c10967bba8b65

- model: stock.stockitem
  pk: 521
  fields:
    part: 10004
    location: 7
    quantity: 1
    serial: 21
    serial_int: 21
    level: 0
    tree_id: 0
    lft: 0
    rght: 0
    status: 60
    barcode_hash: 1be0dfa925825c5c6c79301449e50c2d

- model: stock.stockitem
  pk: 522
  fields:
    part: 10004
    location: 7
    quantity: 1
    serial: 22
    serial_int: 22
    level: 0
    tree_id: 0
    lft: 0
    rght: 0
    expiry_date: "1990-10-10"
    status: 70

# Multiple stock items for "Bob" (PK 100)
- model: stock.stockitem
  pk: 1000
  fields:
    part: 100
    location: 1
    quantity: 10
    level: 0
    tree_id: 0
    lft: 0
    rght: 0

- model: stock.stockitem
  pk: 1001
  fields:
    part: 100
    location: 1
    quantity: 11
    level: 0
    tree_id: 0
    lft: 0
    rght: 0

- model: stock.stockitem
  pk: 1002
  fields:
    part: 100
    location: 1
    quantity: 12
    level: 0
    tree_id: 0
    lft: 0
    rght: 0

- model: stock.stockitem
  pk: 1003
  fields:
    part: 100
    location: 1
    quantity: 13
    level: 0
    tree_id: 0
    lft: 0
    rght: 0

- model: stock.stockitem
  pk: 1004
  fields:
    part: 100
    location: 1
    quantity: 14
    level: 0
    tree_id: 0
    lft: 0
    rght: 0

- model: stock.stockitem
  pk: 1005
  fields:
    part: 100
    location: 1
    quantity: 15
    level: 0
    tree_id: 0
    lft: 0
    rght: 0

- model: stock.stockitem
  pk: 1006
  fields:
    part: 100
    location: 1
    quantity: 16
    level: 0
    tree_id: 0
    lft: 0
    rght: 0

- model: stock.stockitem
  pk: 1007
  fields:
    part: 100
    location: 7
    quantity: 17
    level: 0
    tree_id: 0
    lft: 0
    rght: 0

- model: stock.stockitem
  pk: 1008
  fields:
    part: 100
    location: 7
    quantity: 18
    level: 0
    tree_id: 0
    lft: 0
    rght: 0
