# Generated by Django 4.2.12 on 2024-07-04 10:23

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


def migrate_userthemes(apps, schema_editor):
    """Mgrate text-based user references to ForeignKey references."""
    ColorTheme = apps.get_model("common", "ColorTheme")
    User = apps.get_model(settings.AUTH_USER_MODEL)

    for theme in ColorTheme.objects.all():
        try:
            theme.user_obj = User.objects.get(username=theme.user)
            theme.save()
        except User.DoesNotExist:
            pass

class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("common", "0027_alter_customunit_symbol"),
    ]

    operations = [
        migrations.AddField(
            model_name="colortheme",
            name="user_obj",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.RunPython(migrate_userthemes, migrations.RunPython.noop),
    ]
