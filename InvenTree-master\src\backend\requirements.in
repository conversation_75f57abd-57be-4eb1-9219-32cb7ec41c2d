# Please keep this list sorted - if you pin a version provide a reason
Django<5.0                              # Django package
coreapi                                 # API documentation for djangorestframework
cryptography>=44.0.0                    # Core cryptographic functionality
django-allauth[mfa,socialaccount,saml,openid]   # SSO for external providers via OpenID
django-cleanup                          # Automated deletion of old / unused uploaded files
django-cors-headers                     # CORS headers extension for DRF
django-dbbackup                         # Backup / restore of database and media files
django-error-report-2                   # Error report viewer for the admin interface
django-filter                           # Extended filtering options
django-flags                            # Feature flags
django-ical                             # iCal export for calendar views
django-maintenance-mode                 # Shut down application while reloading etc.
django-markdownify                      # Markdown rendering
django-mptt                             # Modified Preorder Tree Traversal
django-markdownify                      # Markdown rendering
django-money                            # Django app for currency management
django-mptt                             # Modified Preorder Tree Traversal
django-redis>=5.0.0                     # Redis integration
django-q2                               # Background task scheduling
django-q-sentry                         # sentry.io integration for django-q
django-sesame                           # Magic link authentication
django-sql-utils                        # Advanced query annotation / aggregation
django-sslserver                        # Secure HTTP development server
django-structlog                        # Structured logging
django-stdimage                         # Advanced ImageField management
django-taggit                           # Tagging support
django-otp==1.3.0                       # Two-factor authentication (legacy to ensure migrations) https://github.com/inventree/InvenTree/pull/6293
django-oauth-toolkit                    # OAuth2 provider
djangorestframework                     # DRF framework
djangorestframework-simplejwt[crypto]   # JWT authentication
django-xforwardedfor-middleware         # IP forwarding metadata
dulwich                                 # pure Python git integration
docutils                                # Documentation utilities for auto admin docs
drf-spectacular                         # DRF API documentation
feedparser                              # RSS newsfeed parser
gunicorn                                # Gunicorn web server
pdf2image                               # PDF to image conversion
pillow                                  # Image manipulation
pint                                    # Unit conversion
pip-licenses                            # License information for installed packages
ppf.datamatrix                          # Data Matrix barcode generator
pypdf                                   # PDF manipulation tools
python-barcode[images]                  # Barcode generator
python-dotenv                           # Environment variable management
pyyaml>=6.0.1                           # YAML parsing
qrcode[pil]                             # QR code generator
rapidfuzz                               # Fuzzy string matching
sentry-sdk                              # Error reporting (optional)
setuptools                              # Standard dependency
tablib[xls,xlsx,yaml]                   # Support for XLS and XLSX formats
weasyprint                              # PDF generation
whitenoise                              # Enhanced static file serving

# OpenTelemetry dependencies
grpcio
opentelemetry-api
opentelemetry-sdk
opentelemetry-exporter-otlp
opentelemetry-instrumentation-django
opentelemetry-instrumentation-requests
opentelemetry-instrumentation-redis
