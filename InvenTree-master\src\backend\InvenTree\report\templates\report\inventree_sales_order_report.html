{% extends "report/inventree_order_report_base.html" %}

{% load i18n %}
{% load report %}
{% load barcode %}
{% load inventree_extras %}
{% load markdownify %}

{% block header_content %}

    <img class='logo' src='{% company_image customer %}' alt="{{ customer }}" width='150'>

    <div class='header-right'>
        <h3>{% trans "Sales Order" %} {{ prefix }}{{ reference }}</h3>
        {{ customer.name }}
    </div>

{% endblock header_content %}

{% block page_content %}

<h3>{% trans "Line Items" %}</h3>

<table class='table table-striped table-condensed'>
    <thead>
        <tr>
            <th>{% trans "Part" %}</th>
            <th>{% trans "Reference" %}</th>
            <th>{% trans "Quantity" %}</th>
            <th>{% trans "Unit Price" %}</th>
            <th>{% trans "Total Price" %}</th>
            <th>{% trans "Note" %}</th>
        </tr>
    </thead>
    <tbody>
        {% for line in lines.all %}
        <tr>
            <td>
                <div class='thumb-container'>
                    <img src='{% part_image line.part height=240 %}' alt='{% trans "Part image" %}' class='part-thumb'>
                </div>
                <div class='part-text'>
                    {{ line.part.full_name }}
                </div>
            </td>
            <td>{{ line.reference }}</td>
            <td>{% decimal line.quantity %}</td>
            <td>{% render_currency line.price %}</td>
            <td>{% render_currency line.total_line_price %}</td>
            <td>{{ line.notes }}</td>
        </tr>
        {% endfor %}

        {% if extra_lines %}
        <tr><th colspan='6'>{% trans "Extra Line Items" %}</th></tr>
        {% for line in extra_lines.all %}
        <tr>
            <td><!-- No part --></td>
            <td>{{ line.reference }}</td>
            <td>{% decimal line.quantity %}</td>
            <td>{% render_currency line.price %}</td>
            <td>{% render_currency line.total_line_price %}</td>
            <td>{{ line.notes }}</td>
        </tr>
        {% endfor %}
        {% endif %}

        <tr>
            <td></td>
            <td></td>
            <td></td>
            <th>{% trans "Total" %}</th>
            <td>{% render_currency order.total_price currency=order.currency %}</td>
            <td></td>
        </tr>
    </tbody>
</table>

{% endblock page_content %}
