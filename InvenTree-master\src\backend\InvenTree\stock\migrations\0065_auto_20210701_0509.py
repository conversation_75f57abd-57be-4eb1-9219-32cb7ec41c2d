# Generated by Django 3.2.4 on 2021-07-01 05:09

import InvenTree.fields
from django.db import migrations
import djmoney.models.fields


class Migration(migrations.Migration):

    dependencies = [
        ('stock', '0064_auto_20210621_1724'),
    ]

    operations = []

    xoperations = [
        migrations.AlterField(
            model_name='stockitem',
            name='purchase_price',
            field=InvenTree.fields.InvenTreeModelMoneyField(blank=True, currency_choices=[], decimal_places=4, default_currency='', help_text='Single unit purchase price at time of purchase', max_digits=19, null=True, verbose_name='Purchase Price'),
        ),
        migrations.AlterField(
            model_name='stockitem',
            name='purchase_price_currency',
            field=djmoney.models.fields.CurrencyField(choices=[], default='', editable=False, max_length=3),
        ),
    ]
