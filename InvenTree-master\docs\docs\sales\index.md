---
title: Sales
---

## Sales

InvenTree provides support for managing sales orders, allowing users to track the sale of parts and materials to customers. The sales system is designed to seamlessly control the distribution of stock items from the InvenTree database, ensuring that all parts are properly accounted for and tracked throughout their lifecycle.

### Customers

InvenTree allows users to define customers, which represent individuals or organizations that purchase parts or materials from the company. Customers can be linked to specific sales orders, enabling users to easily track customer relationships and manage sales processes.

Read more about customers in the [Customer documentation](./customer.md).

### Sales Orders

The core of the InvenTree sales system is the sales order (SO). A sales order is a formal request from a customer to purchase specific parts or materials. Each sales order is linked to one or more internal parts, allowing users to easily track the sales process and manage customer relationships.

Read more about sales orders in the [Sales Order documentation](./sales_order.md).

### Return Orders

InvenTree also supports return orders, which represent the return of parts or materials from a customer.

Read more about return orders in the [Return Order documentation](./return_order.md).
