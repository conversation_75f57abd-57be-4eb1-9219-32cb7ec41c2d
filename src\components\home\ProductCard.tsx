
import React from 'react';
import { Package } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import CurrencyFormatter from '@/components/ui/currency-formatter';
import { useCart } from '@/contexts/CartContext';
import { Product } from '@/data/products';

interface ProductCardProps {
  product: Product;
}

const ProductCard = ({ product }: ProductCardProps) => {
  const { addItem } = useCart();

  const handleAddToCart = () => {
    addItem({
      id: product.id,
      name: product.name,
      price: product.price,
      image: product.image,
      category: product.category
    });
  };

  return (
    <Card key={product.id} className="group hover:shadow-lg transition-all duration-300">
      <div className="relative overflow-hidden rounded-t-lg">
        <img
          src={product.image}
          alt={product.name}
          className="w-full h-64 object-cover group-hover:scale-105 transition-transform duration-300"
        />
        {product.onSale && (
          <div className="absolute top-2 left-2 bg-red-500 text-white px-2 py-1 rounded text-sm font-medium">
            SALE
          </div>
        )}
        {!product.inStock && (
          <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
            <span className="text-white font-medium">Out of Stock</span>
          </div>
        )}
      </div>
      <CardContent className="p-6">
        <h3 className="text-lg font-semibold text-forest-grey-800 mb-2">{product.name}</h3>
        <div className="flex items-center space-x-2 mb-4">
          <span className="text-2xl font-bold text-forest-green-600">
            <CurrencyFormatter amount={product.price} />
          </span>
          {product.originalPrice && (
            <span className="text-lg text-forest-grey-500 line-through">
              <CurrencyFormatter amount={product.originalPrice} />
            </span>
          )}
        </div>
        <Button
          className="w-full btn-primary"
          onClick={handleAddToCart}
          disabled={!product.inStock}
        >
          <Package className="w-4 h-4 mr-2" />
          {product.inStock ? 'Add to Cart' : 'Out of Stock'}
        </Button>
      </CardContent>
    </Card>
  );
};

export default ProductCard;
