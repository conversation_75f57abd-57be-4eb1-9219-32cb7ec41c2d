---
title: Release 0.1.6
---

## Release 0.1.6

[Release 0.1.6](https://github.com/inventree/InvenTree/releases/tag/0.1.6) (February 2021) provides a number of major new features and improvements, as well as some crucial bug fixes:

## New Features

### Report Templates

Support for report templates has been greatly improved, moving towards "out of the box" support for various reports.

[#1270](https://github.com/inventree/InvenTree/pull/1270) represents a significant refactor of code, and tooling for report functionality.

[#1279](https://github.com/inventree/InvenTree/pull/1279) adds the following report features:

- Adjustable page size for generated reports
- Debug mode (renders reports as simple HTML files)

Refer to the [report documentation](../report/report.md) for further information.

!!! warning "LaTeX Support"
    LaTeX report templates are no longer supported for a number of technical and ideological reasons

[#1292](https://github.com/inventree/InvenTree/pull/1292) adds support for build order / work order reports. Refer to the [report documentation](../report/index.md) for further information.

### Inherited BOM Items

[#1313](https://github.com/inventree/InvenTree/pull/1313) adds support for inherited BOM items, allowing greater flexibility for Bill of Materials management when combined with the Template / Variant part system.

Refer to the [BOM documentation](../manufacturing/bom.md) for further information.

### Stock Item Packaging

[#1329](https://github.com/inventree/InvenTree/pull/1329) adds a *packaging* field to the StockItem model, so the particular packaging information (e.g. tape / reel / loose / etc) can be captured per stock item.

### Visual Improvements

[#1333](https://github.com/inventree/InvenTree/pull/1333) provides a significant overhaul of the visual style of the "index" and "search" pages.

## Major Bug Fixes
| PR | Description |
| --- | --- |
| [#1258](https://github.com/inventree/InvenTree/pull/1258) | Fixes bug causing part images to sometimes be deleted |
| [#1267](https://github.com/inventree/InvenTree/pull/1267) | Fixes issue with legacy migration file when upgrading from very old installation |
| [#1288](https://github.com/inventree/InvenTree/pull/1288) | Fixes bug which caused errors with table search |
| [#1289](https://github.com/inventree/InvenTree/pull/1289) | Fixes display bug when a part "units" field is empty |
| [#1290](https://github.com/inventree/InvenTree/pull/1290) | Fixes CSS issues with long error messages in modal forms |
| [#1294](https://github.com/inventree/InvenTree/pull/1294) | Allows access to static files without being logged in |
| [#1320](https://github.com/inventree/InvenTree/pull/1320) | Fixes "Used In" display for parts that are referenced in inherited BOMs |
