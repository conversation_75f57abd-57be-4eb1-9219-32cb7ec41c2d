# Generated by Django 4.2.19 on 2025-03-03 00:43

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion

def backfill_user_profiles(apps, schema_editor):
    User = apps.get_model('auth', 'User')
    UserProfile = apps.get_model('users', 'UserProfile')
    for user in User.objects.all():
        UserProfile.objects.get_or_create(user=user)


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("auth", "0012_alter_user_first_name_max_length"),
        ("users", "0013_migrate_mfa_20240408_1659"),
    ]

    operations = [
        migrations.CreateModel(
            name="UserProfile",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "metadata",
                    models.JSONField(
                        blank=True,
                        help_text="JSON metadata field, for use by external plugins",
                        null=True,
                        verbose_name="Plugin Metadata",
                    ),
                ),
                (
                    "language",
                    models.CharField(
                        blank=True,
                        help_text="Preferred language for the user",
                        max_length=10,
                        null=True,
                        verbose_name="Language",
                    ),
                ),
                (
                    "theme",
                    models.JSONField(
                        blank=True,
                        help_text="Settings for the web UI as JSON - do not edit manually!",
                        null=True,
                        verbose_name="Theme",
                    ),
                ),
                (
                    "widgets",
                    models.JSONField(
                        blank=True,
                        help_text="Settings for the dashboard widgets as JSON - do not edit manually!",
                        null=True,
                        verbose_name="Widgets",
                    ),
                ),
                (
                    "displayname",
                    models.CharField(
                        blank=True,
                        help_text="Chosen display name for the user",
                        max_length=255,
                        null=True,
                        verbose_name="Display Name",
                    ),
                ),
                (
                    "position",
                    models.CharField(
                        blank=True,
                        help_text="Main job title or position",
                        max_length=255,
                        null=True,
                        verbose_name="Position",
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        blank=True,
                        help_text="User status message",
                        max_length=2000,
                        null=True,
                        verbose_name="Status",
                    ),
                ),
                (
                    "location",
                    models.CharField(
                        blank=True,
                        help_text="User location information",
                        max_length=2000,
                        null=True,
                        verbose_name="Location",
                    ),
                ),
                (
                    "active",
                    models.BooleanField(
                        default=True,
                        help_text="User is actively using the system",
                        verbose_name="Active",
                    ),
                ),
                (
                    "contact",
                    models.CharField(
                        blank=True,
                        help_text="Preferred contact information for the user",
                        max_length=255,
                        null=True,
                        verbose_name="Contact",
                    ),
                ),
                (
                    "type",
                    models.CharField(
                        choices=[
                            ("bot", "Bot"),
                            ("internal", "Internal"),
                            ("external", "External"),
                            ("guest", "Guest"),
                        ],
                        default="internal",
                        help_text="Which type of user is this?",
                        max_length=10,
                        verbose_name="Type",
                    ),
                ),
                (
                    "organisation",
                    models.CharField(
                        blank=True,
                        help_text="Users primary organisation/affiliation",
                        max_length=255,
                        null=True,
                        verbose_name="Organisation",
                    ),
                ),
                (
                    "primary_group",
                    models.ForeignKey(
                        blank=True,
                        help_text="Primary group for the user",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="primary_users",
                        to="auth.group",
                        verbose_name="Primary Group",
                    ),
                ),
                (
                    "user",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="profile",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="User",
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.RunPython(backfill_user_profiles),
    ]
