# Generated by Django 3.2.21 on 2023-10-20 23:56

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('users', '0008_apitoken'),
    ]

    operations = [
        migrations.AddField(
            model_name='apitoken',
            name='last_seen',
            field=models.DateField(blank=True, help_text='Last time the token was used', null=True, verbose_name='Last Seen'),
        ),
        migrations.AddField(
            model_name='apitoken',
            name='metadata',
            field=models.JSONField(blank=True, help_text='JSON metadata field, for use by external plugins', null=True, verbose_name='Plugin Metadata'),
        ),
        migrations.AlterUniqueTogether(
            name='apitoken',
            unique_together=set(),
        ),
    ]
