# Generated by Django 4.2.19 on 2025-03-11 08:22

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("common", "0036_alter_attachment_link"),
    ]

    operations = [
        migrations.CreateModel(
            name="DataOutput",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created", models.DateField(auto_now_add=True)),
                ("total", models.PositiveIntegerField(default=1)),
                ("progress", models.PositiveIntegerField(default=0)),
                ("complete", models.BooleanField(default=False)),
                (
                    "output_type",
                    models.CharField(blank=True, max_length=100, null=True),
                ),
                (
                    "template_name",
                    models.Char<PERSON>ield(blank=True, max_length=100, null=True),
                ),
                ("plugin", models.Char<PERSON>ield(blank=True, max_length=100, null=True)),
                (
                    "output",
                    models.FileField(blank=True, null=True, upload_to="data_output"),
                ),
                ("errors", models.JSONField(blank=True, null=True)),
                (
                    "user",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="+",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
        ),
    ]
