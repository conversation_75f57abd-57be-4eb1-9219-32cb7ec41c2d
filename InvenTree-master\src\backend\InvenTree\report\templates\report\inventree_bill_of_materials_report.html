{% extends "report/inventree_report_base.html" %}

{% load i18n %}
{% load report %}
{% load barcode %}
{% load inventree_extras %}

{% block page_margin %}
margin: 2cm;
margin-top: 4cm;
{% endblock page_margin %}

{% block bottom_left %}
content: "v{{ report_revision }} - {% format_date date %}";
{% endblock bottom_left %}

{% block bottom_center %}
content: "{% inventree_version shortstring=True %}";
{% endblock bottom_center %}

{% block style %}

.header-right {
    text-align: right;
    float: right;
}

.logo {
    height: 20mm;
    vertical-align: middle;
}

.thumb-container {
    width: 32px;
    display: inline;
}

.part-thumb {
    max-width: 32px;
    max-height: 32px;
    display: inline;
}

.part-text {
    display: inline;
}

.part-logo {
    max-width: 60px;
    max-height: 60px;
    display: inline;
}

table {
    border: 1px solid #eee;
    border-radius: 3px;
    border-collapse: collapse;
    width: 100%;
    font-size: 80%;
}

table td {
    border: 1px solid #eee;
}

table td.shrink {
    white-space: nowrap
}

table td.expand {
    width: 99%
}

.invisible-table {
    border: 0px solid transparent;
    border-collapse: collapse;
    width: 100%;
    font-size: 80%;
}

.invisible-table td {
    border: 0px solid transparent;
}

.main-part-text {
    display: inline;
}

.main-part-description {
    display: inline;
}

{% endblock style %}

{% block header_content %}

    <img class='logo' src='{% logo_image %}' alt="InvenTree logo" width='150'>

    <div class='header-right'>
        <h3>{% trans "Bill of Materials" %}</h3>
    </div>

{% endblock header_content %}

{% block page_content %}

<table class="invisible-table">
    <thead>
        <tr>
            <th colspan="2"><h3>{% trans "Part" %}</h3></th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>
                <div class='main-part-text'>
                    {{ part.full_name }}
                </div>
                <br/>
                <div class='main-part-description'>
                    {{ part.description }}
                </div>
            </td>
            <td>
                <div class='part-logo'>
                    <img src='{% part_image part height=480 %}' alt='{% trans "Image" %}' class='part-logo'>
                </div>
            </td>
        </tr>
    </tbody>
</table>

<h3>{% trans "Materials needed" %}</h3>
<table class='table table-striped table-condensed'>
    <thead>
        <tr>
            <th>{% trans "Part" %}</th>
            <th>{% trans "Quantity" %}</th>
            <th>{% trans "Reference" %}</th>
            <th>{% trans "Note" %}</th>
        </tr>
    </thead>
    <tbody>
        {% for line in bom_items.all %}
        <tr>
            <td>
                <div class='thumb-container'>
                    <img src='{% part_image line.sub_part height=240 %}' alt='{% trans "Image" %}' class='part-thumb'>
                </div>
                <div class='part-text'>
                    {{ line.sub_part.full_name }}
                </div>
            </td>
            <td>{% decimal line.quantity %}</td>
            <td>{{ line.reference }}</td>
            <td>{{ line.notes }}</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

{% endblock page_content %}
