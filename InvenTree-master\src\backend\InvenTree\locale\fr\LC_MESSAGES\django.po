msgid ""
msgstr ""
"Project-Id-Version: inventree\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-09 19:12+0000\n"
"PO-Revision-Date: 2025-05-17 05:36\n"
"Last-Translator: \n"
"Language-Team: French\n"
"Language: fr_FR\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"
"X-Crowdin-Project: inventree\n"
"X-Crowdin-Project-ID: 452300\n"
"X-Crowdin-Language: fr\n"
"X-Crowdin-File: /src/backend/InvenTree/locale/en/LC_MESSAGES/django.po\n"
"X-Crowdin-File-ID: 250\n"

#: InvenTree/AllUserRequire2FAMiddleware.py:42
msgid "You must enable two-factor authentication before doing anything else."
msgstr "Vous devez activer l'authentification à deux facteurs avant de faire quoi que ce soit d'autre."

#: InvenTree/api.py:355
msgid "API endpoint not found"
msgstr "Point de terminaison de l'API introuvable"

#: InvenTree/api.py:432
msgid "List of items or filters must be provided for bulk operation"
msgstr ""

#: InvenTree/api.py:439
msgid "Items must be provided as a list"
msgstr "Les éléments doivent être fournis en tant que liste"

#: InvenTree/api.py:447
msgid "Invalid items list provided"
msgstr "Unité fournie invalide"

#: InvenTree/api.py:453
msgid "Filters must be provided as a dict"
msgstr ""

#: InvenTree/api.py:460
msgid "Invalid filters provided"
msgstr "Filtres fournis invalides"

#: InvenTree/api.py:465
msgid "All filter must only be used with true"
msgstr ""

#: InvenTree/api.py:470
msgid "No items match the provided criteria"
msgstr ""

#: InvenTree/api.py:713
msgid "User does not have permission to view this model"
msgstr "L'utilisateur n'a pas la permission de voir ce modèle"

#: InvenTree/auth_overrides.py:57
msgid "Email (again)"
msgstr "Email (encore)"

#: InvenTree/auth_overrides.py:61
msgid "Email address confirmation"
msgstr "Confirmation de l'adresse email"

#: InvenTree/auth_overrides.py:84
msgid "You must type the same email each time."
msgstr "Vous devez taper le même e-mail à chaque fois."

#: InvenTree/auth_overrides.py:126 InvenTree/auth_overrides.py:134
msgid "The provided primary email address is not valid."
msgstr "L'adresse e-mail principale fournie n'est pas valide."

#: InvenTree/auth_overrides.py:140
msgid "The provided email domain is not approved."
msgstr "Le domaine e-mail fourni n'est pas approuvé."

#: InvenTree/conversion.py:162
#, python-brace-format
msgid "Invalid unit provided ({unit})"
msgstr "Unité fournie invalide ({unit})"

#: InvenTree/conversion.py:179
msgid "No value provided"
msgstr "Pas de valeur renseignée"

#: InvenTree/conversion.py:206
#, python-brace-format
msgid "Could not convert {original} to {unit}"
msgstr "Impossible de convertir {original} en {unit}"

#: InvenTree/conversion.py:208 InvenTree/conversion.py:222
#: InvenTree/helpers.py:548 order/models.py:707 order/models.py:975
msgid "Invalid quantity provided"
msgstr "Quantité fournie invalide"

#: InvenTree/exceptions.py:109
msgid "Error details can be found in the admin panel"
msgstr "Les détails de l'erreur peuvent être trouvées dans le panneau d'administration"

#: InvenTree/fields.py:138
msgid "Enter date"
msgstr "Entrer la date"

#: InvenTree/fields.py:161
msgid "Invalid decimal value"
msgstr "Valeur décimale invalide"

#: InvenTree/fields.py:210 InvenTree/models.py:887 build/serializers.py:526
#: build/serializers.py:597 company/models.py:834 order/models.py:1579
#: part/models.py:3366
#: report/templates/report/inventree_build_order_report.html:172
#: stock/models.py:2708 stock/models.py:2832 stock/serializers.py:756
#: stock/serializers.py:918 stock/serializers.py:1048 stock/serializers.py:1100
#: stock/serializers.py:1419 stock/serializers.py:1508
#: stock/serializers.py:1684
msgid "Notes"
msgstr "Notes"

#: InvenTree/format.py:166
#, python-brace-format
msgid "Value '{name}' does not appear in pattern format"
msgstr "La valeur '{name}' n'apparaît pas dans le format du modèle"

#: InvenTree/format.py:177
msgid "Provided value does not match required pattern: "
msgstr "La valeur fournie ne correspond pas au modèle requis : "

#: InvenTree/helpers.py:552
msgid "Cannot serialize more than 1000 items at once"
msgstr ""

#: InvenTree/helpers.py:558
msgid "Empty serial number string"
msgstr "Chaîne de numéro de série vide"

#: InvenTree/helpers.py:587
msgid "Duplicate serial"
msgstr "Numéro de série en doublon"

#: InvenTree/helpers.py:619 InvenTree/helpers.py:662 InvenTree/helpers.py:680
#: InvenTree/helpers.py:687 InvenTree/helpers.py:706
#, python-brace-format
msgid "Invalid group: {group}"
msgstr "Groupe invalide : {group}"

#: InvenTree/helpers.py:650
#, python-brace-format
msgid "Group range {group} exceeds allowed quantity ({expected_quantity})"
msgstr "La plage de groupe {group} dépasse la quantité autorisée ({expected_quantity})"

#: InvenTree/helpers.py:716
msgid "No serial numbers found"
msgstr "Aucun numéro de série trouvé"

#: InvenTree/helpers.py:721
msgid "Number of unique serial numbers ({len(serials)}) must match quantity ({expected_quantity})"
msgstr "Le nombre de numéros de série uniques ({len(serials)}) doit correspondre à la quantité ({expected_quantity})"

#: InvenTree/helpers.py:840
msgid "Remove HTML tags from this value"
msgstr "Retirer les balises HTML de cette valeur"

#: InvenTree/helpers.py:919
msgid "Data contains prohibited markdown content"
msgstr ""

#: InvenTree/helpers_model.py:131
msgid "Connection error"
msgstr "Erreur de connexion"

#: InvenTree/helpers_model.py:136 InvenTree/helpers_model.py:143
msgid "Server responded with invalid status code"
msgstr "Le serveur a répondu avec un code de statut invalide"

#: InvenTree/helpers_model.py:139
msgid "Exception occurred"
msgstr "Une erreur est survenue"

#: InvenTree/helpers_model.py:149
msgid "Server responded with invalid Content-Length value"
msgstr "Le serveur a répondu avec une valeur de longueur de contenu invalide"

#: InvenTree/helpers_model.py:152
msgid "Image size is too large"
msgstr "Image trop volumineuse"

#: InvenTree/helpers_model.py:164
msgid "Image download exceeded maximum size"
msgstr "La taille de l'image dépasse la taille maximale autorisée"

#: InvenTree/helpers_model.py:169
msgid "Remote server returned empty response"
msgstr "Le serveur distant a renvoyé une réponse vide"

#: InvenTree/helpers_model.py:177
msgid "Supplied URL is not a valid image file"
msgstr "L'URL fournie n'est pas un fichier image valide"

#: InvenTree/locales.py:20
msgid "Arabic"
msgstr "Arabe"

#: InvenTree/locales.py:21
msgid "Bulgarian"
msgstr "Bulgare"

#: InvenTree/locales.py:22
msgid "Czech"
msgstr "Tchèque"

#: InvenTree/locales.py:23
msgid "Danish"
msgstr "Danois"

#: InvenTree/locales.py:24
msgid "German"
msgstr "Allemand"

#: InvenTree/locales.py:25
msgid "Greek"
msgstr "Grec"

#: InvenTree/locales.py:26
msgid "English"
msgstr "Anglais"

#: InvenTree/locales.py:27
msgid "Spanish"
msgstr "Espagnol"

#: InvenTree/locales.py:28
msgid "Spanish (Mexican)"
msgstr "Espagnol (Mexique)"

#: InvenTree/locales.py:29
msgid "Estonian"
msgstr "Estonien"

#: InvenTree/locales.py:30
msgid "Farsi / Persian"
msgstr "Farsi / Perse"

#: InvenTree/locales.py:31
msgid "Finnish"
msgstr "Finnois"

#: InvenTree/locales.py:32
msgid "French"
msgstr "Français"

#: InvenTree/locales.py:33
msgid "Hebrew"
msgstr "Hébreu"

#: InvenTree/locales.py:34
msgid "Hindi"
msgstr "Hindi"

#: InvenTree/locales.py:35
msgid "Hungarian"
msgstr "Hongrois"

#: InvenTree/locales.py:36
msgid "Italian"
msgstr "Italien"

#: InvenTree/locales.py:37
msgid "Japanese"
msgstr "Japonais"

#: InvenTree/locales.py:38
msgid "Korean"
msgstr "Coréen"

#: InvenTree/locales.py:39
msgid "Lithuanian"
msgstr "Lituanien"

#: InvenTree/locales.py:40
msgid "Latvian"
msgstr "Letton"

#: InvenTree/locales.py:41
msgid "Dutch"
msgstr "Néerlandais"

#: InvenTree/locales.py:42
msgid "Norwegian"
msgstr "Norvégien"

#: InvenTree/locales.py:43
msgid "Polish"
msgstr "Polonais"

#: InvenTree/locales.py:44
msgid "Portuguese"
msgstr "Portugais"

#: InvenTree/locales.py:45
msgid "Portuguese (Brazilian)"
msgstr "Portugais (Brésilien)"

#: InvenTree/locales.py:46
msgid "Romanian"
msgstr "Roumain"

#: InvenTree/locales.py:47
msgid "Russian"
msgstr "Russe"

#: InvenTree/locales.py:48
msgid "Slovak"
msgstr "Slovaque"

#: InvenTree/locales.py:49
msgid "Slovenian"
msgstr "Slovénien"

#: InvenTree/locales.py:50
msgid "Serbian"
msgstr "Serbe"

#: InvenTree/locales.py:51
msgid "Swedish"
msgstr "Suédois"

#: InvenTree/locales.py:52
msgid "Thai"
msgstr "Thaïlandais"

#: InvenTree/locales.py:53
msgid "Turkish"
msgstr "Turc"

#: InvenTree/locales.py:54
msgid "Ukrainian"
msgstr "Ukrainien"

#: InvenTree/locales.py:55
msgid "Vietnamese"
msgstr "Vietnamien"

#: InvenTree/locales.py:56
msgid "Chinese (Simplified)"
msgstr "Chinois (Simplifié)"

#: InvenTree/locales.py:57
msgid "Chinese (Traditional)"
msgstr "Chinois (Traditionnel)"

#: InvenTree/magic_login.py:31
msgid "Log in to the app"
msgstr "Se connecter à l'application"

#: InvenTree/magic_login.py:41 company/models.py:136 users/serializers.py:247
msgid "Email"
msgstr "E-mail"

#: InvenTree/models.py:107
msgid "Error running plugin validation"
msgstr "Erreur lors de l'exécution de la validation du plugin"

#: InvenTree/models.py:184
msgid "Metadata must be a python dict object"
msgstr "Les metadata doivent être un objet python de type \"dict\""

#: InvenTree/models.py:190
msgid "Plugin Metadata"
msgstr "Métadonnées de l'Extension"

#: InvenTree/models.py:191
msgid "JSON metadata field, for use by external plugins"
msgstr "Champs metadata JSON, pour plugins tiers"

#: InvenTree/models.py:374
msgid "Improperly formatted pattern"
msgstr "Modèle mal formaté"

#: InvenTree/models.py:381
msgid "Unknown format key specified"
msgstr "Clé de format inconnu spécifiée"

#: InvenTree/models.py:387
msgid "Missing required format key"
msgstr "Clé de format requise manquante"

#: InvenTree/models.py:398
msgid "Reference field cannot be empty"
msgstr "Le champ de référence ne peut pas être vide"

#: InvenTree/models.py:406
msgid "Reference must match required pattern"
msgstr "La référence doit correspondre au modèle requis"

#: InvenTree/models.py:437
msgid "Reference number is too large"
msgstr "Le numéro de référence est trop grand"

#: InvenTree/models.py:688
msgid "Duplicate names cannot exist under the same parent"
msgstr "Les noms dupliqués ne peuvent pas exister sous le même parent"

#: InvenTree/models.py:705
msgid "Invalid choice"
msgstr "Choix invalide"

#: InvenTree/models.py:734 common/models.py:1325 common/models.py:1752
#: common/models.py:2011 common/models.py:2136 common/serializers.py:488
#: company/models.py:593 generic/states/serializers.py:20 machine/models.py:24
#: part/models.py:1039 part/models.py:3837 plugin/models.py:53
#: report/models.py:203 stock/models.py:83
msgid "Name"
msgstr "Nom"

#: InvenTree/models.py:740 build/models.py:242 common/models.py:123
#: common/models.py:2143 common/models.py:2256 company/models.py:521
#: company/models.py:825 order/models.py:429 order/models.py:1615
#: part/models.py:1062 part/models.py:3852 report/models.py:209
#: report/models.py:714 report/models.py:740
#: report/templates/report/inventree_build_order_report.html:117
#: stock/models.py:89
msgid "Description"
msgstr "Description"

#: InvenTree/models.py:741 stock/models.py:90
msgid "Description (optional)"
msgstr "Description (facultative)"

#: InvenTree/models.py:756 common/models.py:2309
msgid "Path"
msgstr "Chemin d'accès"

#: InvenTree/models.py:887
msgid "Markdown notes (optional)"
msgstr "Notes Markdown (option)"

#: InvenTree/models.py:918
msgid "Barcode Data"
msgstr "Données du code-barres"

#: InvenTree/models.py:919
msgid "Third party barcode data"
msgstr "Données de code-barres tierces"

#: InvenTree/models.py:925
msgid "Barcode Hash"
msgstr "Hash du code-barre"

#: InvenTree/models.py:926
msgid "Unique hash of barcode data"
msgstr "Hachage unique des données du code-barres"

#: InvenTree/models.py:1003
msgid "Existing barcode found"
msgstr "Code-barres existant trouvé"

#: InvenTree/models.py:1084
msgid "Task Failure"
msgstr "Échec de la tâche"

#: InvenTree/models.py:1085
#, python-brace-format
msgid "Background worker task '{f}' failed after {n} attempts"
msgstr ""

#: InvenTree/models.py:1112
msgid "Server Error"
msgstr "Erreur serveur"

#: InvenTree/models.py:1113
msgid "An error has been logged by the server."
msgstr "Une erreur a été loguée par le serveur."

#: InvenTree/serializers.py:69 part/models.py:4455
msgid "Must be a valid number"
msgstr "Doit être un nombre valide"

#: InvenTree/serializers.py:111 company/models.py:187 part/models.py:3184
msgid "Currency"
msgstr "Devise"

#: InvenTree/serializers.py:114 part/serializers.py:1335
msgid "Select currency from available options"
msgstr "Sélectionnez la devise à partir des options disponibles"

#: InvenTree/serializers.py:466
msgid "Invalid value"
msgstr "Valeur non valide"

#: InvenTree/serializers.py:503
msgid "Remote Image"
msgstr "Images distantes"

#: InvenTree/serializers.py:504
msgid "URL of remote image file"
msgstr "URL du fichier image distant"

#: InvenTree/serializers.py:522
msgid "Downloading images from remote URL is not enabled"
msgstr "Le téléchargement des images depuis une URL distante n'est pas activé"

#: InvenTree/serializers.py:529
msgid "Failed to download image from remote URL"
msgstr ""

#: InvenTree/validators.py:30
msgid "Invalid physical unit"
msgstr "Unité invalide"

#: InvenTree/validators.py:36
msgid "Not a valid currency code"
msgstr "Code de devise invalide"

#: InvenTree/validators.py:116 InvenTree/validators.py:132
msgid "Overage value must not be negative"
msgstr "La valeur de surplus ne doit pas être négative"

#: InvenTree/validators.py:134
msgid "Overage must not exceed 100%"
msgstr "Le surplus ne doit pas dépasser 100%"

#: InvenTree/validators.py:140
msgid "Invalid value for overage"
msgstr "Valeur invalide pour le dépassement"

#: build/api.py:39 order/api.py:104 order/api.py:263 order/serializers.py:128
msgid "Order Status"
msgstr "Statut de la commande"

#: build/api.py:65 build/models.py:254
msgid "Parent Build"
msgstr "Fabrication parente"

#: build/api.py:69 build/api.py:768 order/api.py:512 order/api.py:732
#: order/api.py:1106 order/api.py:1334 stock/api.py:522
msgid "Include Variants"
msgstr "Inclure les variantes"

#: build/api.py:85 build/api.py:470 build/api.py:782 build/models.py:260
#: build/serializers.py:1254 build/serializers.py:1376
#: build/serializers.py:1427 company/models.py:1044 company/serializers.py:432
#: order/api.py:291 order/api.py:295 order/api.py:880 order/api.py:1119
#: order/api.py:1122 order/models.py:1704 order/models.py:1863
#: order/models.py:1864 part/api.py:1448 part/api.py:1451 part/api.py:1517
#: part/api.py:1827 part/models.py:457 part/models.py:3195 part/models.py:3339
#: part/models.py:3487 part/models.py:3508 part/models.py:3530
#: part/models.py:3666 part/models.py:4027 part/models.py:4299
#: part/models.py:4665 part/serializers.py:1255 part/serializers.py:1924
#: report/templates/report/inventree_bill_of_materials_report.html:110
#: report/templates/report/inventree_bill_of_materials_report.html:137
#: report/templates/report/inventree_build_order_report.html:109
#: report/templates/report/inventree_purchase_order_report.html:27
#: report/templates/report/inventree_return_order_report.html:24
#: report/templates/report/inventree_sales_order_report.html:27
#: report/templates/report/inventree_sales_order_shipment_report.html:28
#: report/templates/report/inventree_stock_location_report.html:102
#: stock/api.py:535 stock/serializers.py:119 stock/serializers.py:171
#: stock/serializers.py:481 stock/serializers.py:643 stock/serializers.py:948
#: templates/email/build_order_completed.html:17
#: templates/email/build_order_required_stock.html:17
#: templates/email/low_stock_notification.html:15
#: templates/email/overdue_build_order.html:16
#: templates/email/part_event_notification.html:15
msgid "Part"
msgstr "Pièce"

#: build/api.py:105 build/api.py:108 part/api.py:1531 part/models.py:1080
#: part/models.py:3558 part/models.py:4136 part/serializers.py:1265
#: stock/api.py:818
msgid "Category"
msgstr "Catégorie"

#: build/api.py:116 build/api.py:120
msgid "Ancestor Build"
msgstr "Version Précédente"

#: build/api.py:137 order/api.py:122
msgid "Assigned to me"
msgstr "Attribué à moi"

#: build/api.py:152
#: report/templates/report/inventree_build_order_report.html:152
msgid "Issued By"
msgstr "Émis par"

#: build/api.py:167
msgid "Assigned To"
msgstr "Attribué à"

#: build/api.py:202
msgid "Created before"
msgstr "Créé avant"

#: build/api.py:206
msgid "Created after"
msgstr "Créé après"

#: build/api.py:210
msgid "Has start date"
msgstr ""

#: build/api.py:218
msgid "Start date before"
msgstr ""

#: build/api.py:222
msgid "Start date after"
msgstr ""

#: build/api.py:226
msgid "Has target date"
msgstr ""

#: build/api.py:234
msgid "Target date before"
msgstr ""

#: build/api.py:238
msgid "Target date after"
msgstr ""

#: build/api.py:242
msgid "Completed before"
msgstr ""

#: build/api.py:246
msgid "Completed after"
msgstr ""

#: build/api.py:249 order/api.py:219
msgid "Min Date"
msgstr ""

#: build/api.py:272 order/api.py:238
msgid "Max Date"
msgstr ""

#: build/api.py:297 build/api.py:300 part/api.py:224
msgid "Exclude Tree"
msgstr ""

#: build/api.py:409
msgid "Build must be cancelled before it can be deleted"
msgstr "La construction doit être annulée avant de pouvoir être supprimée"

#: build/api.py:453 build/serializers.py:1392 part/models.py:4333
msgid "Consumable"
msgstr "Consommable"

#: build/api.py:456 build/serializers.py:1395 part/models.py:4327
msgid "Optional"
msgstr "Facultatif"

#: build/api.py:459 common/setting/system.py:429 part/models.py:1211
#: part/serializers.py:1663 part/serializers.py:1672 stock/api.py:588
msgid "Assembly"
msgstr "Assemblage"

#: build/api.py:462
msgid "Tracked"
msgstr "Suivi"

#: build/api.py:465 build/serializers.py:1398 part/models.py:1229
msgid "Testable"
msgstr "Testable"

#: build/api.py:475 order/api.py:925
msgid "Order Outstanding"
msgstr ""

#: build/api.py:485 order/api.py:884
msgid "Allocated"
msgstr "Allouée"

#: build/api.py:495 company/models.py:889 company/serializers.py:427
#: templates/email/build_order_required_stock.html:19
#: templates/email/low_stock_notification.html:17
#: templates/email/part_event_notification.html:18
msgid "Available"
msgstr "Disponible"

#: build/api.py:805 build/models.py:116
#: report/templates/report/inventree_build_order_report.html:105
#: stock/serializers.py:92 templates/email/build_order_completed.html:16
#: templates/email/overdue_build_order.html:15
msgid "Build Order"
msgstr "Ordre de Fabrication"

#: build/models.py:117 users/ruleset.py:37
msgid "Build Orders"
msgstr "Ordres de Fabrication"

#: build/models.py:162
msgid "Assembly BOM has not been validated"
msgstr "La liste des composants de l'assemblage n'a pas été validée"

#: build/models.py:169
msgid "Build order cannot be created for an inactive part"
msgstr ""

#: build/models.py:176
msgid "Build order cannot be created for an unlocked part"
msgstr ""

#: build/models.py:188
msgid "Invalid choice for parent build"
msgstr "Choix invalide pour la fabrication parente"

#: build/models.py:197 order/models.py:363
msgid "Responsible user or group must be specified"
msgstr "Un utilisateur ou un groupe responsable doit être spécifié"

#: build/models.py:202
msgid "Build order part cannot be changed"
msgstr "La pièce de commande de construction ne peut pas être changée"

#: build/models.py:207 order/models.py:376
msgid "Target date must be after start date"
msgstr ""

#: build/models.py:235
msgid "Build Order Reference"
msgstr "Référence de l' Ordre de Fabrication"

#: build/models.py:236 build/serializers.py:1389 order/models.py:601
#: order/models.py:1140 order/models.py:1572 order/models.py:2403
#: part/models.py:4348
#: report/templates/report/inventree_bill_of_materials_report.html:139
#: report/templates/report/inventree_purchase_order_report.html:28
#: report/templates/report/inventree_return_order_report.html:26
#: report/templates/report/inventree_sales_order_report.html:28
msgid "Reference"
msgstr "Référence"

#: build/models.py:245
msgid "Brief description of the build (optional)"
msgstr "Brève description de la fabrication (optionnel)"

#: build/models.py:255
msgid "BuildOrder to which this build is allocated"
msgstr "BuildOrder associé a cette fabrication"

#: build/models.py:264
msgid "Select part to build"
msgstr "Sélectionnez la pièce à construire"

#: build/models.py:269
msgid "Sales Order Reference"
msgstr "Bon de commande de référence"

#: build/models.py:274
msgid "SalesOrder to which this build is allocated"
msgstr "Commande de vente à laquelle cette construction est allouée"

#: build/models.py:279 build/serializers.py:1104
msgid "Source Location"
msgstr "Emplacement d'origine"

#: build/models.py:285
msgid "Select location to take stock from for this build (leave blank to take from any stock location)"
msgstr "Sélectionner l'emplacement à partir duquel le stock doit être pris pour cette construction (laisser vide pour prendre à partir de n'importe quel emplacement de stock)"

#: build/models.py:291
msgid "Destination Location"
msgstr "Emplacement cible"

#: build/models.py:296
msgid "Select location where the completed items will be stored"
msgstr "Sélectionnez l'emplacement où les éléments complétés seront stockés"

#: build/models.py:300
msgid "Build Quantity"
msgstr "Quantité a fabriquer"

#: build/models.py:303
msgid "Number of stock items to build"
msgstr "Nombre de stock items à construire"

#: build/models.py:307
msgid "Completed items"
msgstr "Articles terminés"

#: build/models.py:309
msgid "Number of stock items which have been completed"
msgstr "Nombre d'articles de stock qui ont été terminés"

#: build/models.py:313
msgid "Build Status"
msgstr "État de la construction"

#: build/models.py:318
msgid "Build status code"
msgstr "Code de statut de construction"

#: build/models.py:327 build/serializers.py:376 order/serializers.py:785
#: stock/models.py:1023 stock/serializers.py:84 stock/serializers.py:1651
msgid "Batch Code"
msgstr "Code de lot"

#: build/models.py:331 build/serializers.py:377
msgid "Batch code for this build output"
msgstr "Code de lot pour ce build output"

#: build/models.py:335 order/models.py:466 order/serializers.py:166
#: part/models.py:1286
msgid "Creation Date"
msgstr "Date de création"

#: build/models.py:341
msgid "Build start date"
msgstr ""

#: build/models.py:342
msgid "Scheduled start date for this build order"
msgstr ""

#: build/models.py:348
msgid "Target completion date"
msgstr "Date d'achèvement cible"

#: build/models.py:350
msgid "Target date for build completion. Build will be overdue after this date."
msgstr "Date cible pour l'achèvement de la construction. La construction sera en retard après cette date."

#: build/models.py:355 order/models.py:654 order/models.py:2442
msgid "Completion Date"
msgstr "Date d'achèvement"

#: build/models.py:363
msgid "completed by"
msgstr "achevé par"

#: build/models.py:372
msgid "Issued by"
msgstr "Émis par"

#: build/models.py:373
msgid "User who issued this build order"
msgstr "Utilisateur ayant émis cette commande de construction"

#: build/models.py:382 common/models.py:132 order/api.py:172
#: order/models.py:491 part/models.py:1303
#: report/templates/report/inventree_build_order_report.html:158
msgid "Responsible"
msgstr "Responsable"

#: build/models.py:383
msgid "User or group responsible for this build order"
msgstr "Utilisateur ou groupe responsable de cet ordre de construction"

#: build/models.py:388 stock/models.py:1016
msgid "External Link"
msgstr "Lien Externe"

#: build/models.py:390 common/models.py:1899 part/models.py:1114
#: stock/models.py:1018
msgid "Link to external URL"
msgstr "Lien vers une url externe"

#: build/models.py:395
msgid "Build Priority"
msgstr "Priorité de fabrication"

#: build/models.py:398
msgid "Priority of this build order"
msgstr "Priorité de cet ordre de fabrication"

#: build/models.py:406 common/models.py:102 common/models.py:116
#: order/api.py:158 order/models.py:438
msgid "Project Code"
msgstr "Code du projet"

#: build/models.py:407
msgid "Project code for this build order"
msgstr "Code de projet pour cet ordre de construction"

#: build/models.py:680 build/models.py:803
msgid "Failed to offload task to complete build allocations"
msgstr "Échec du déchargement de la tâche pour terminer les allocations de construction"

#: build/models.py:703
#, python-brace-format
msgid "Build order {build} has been completed"
msgstr "La commande de construction {build} a été effectuée"

#: build/models.py:709
msgid "A build order has been completed"
msgstr "Une commande de construction a été effectuée"

#: build/models.py:881 build/serializers.py:424
msgid "Serial numbers must be provided for trackable parts"
msgstr "Les numéros de série doivent être fournis pour les pièces traçables"

#: build/models.py:991 build/models.py:1076
msgid "No build output specified"
msgstr "Pas d'ordre de production défini"

#: build/models.py:994
msgid "Build output is already completed"
msgstr "L'ordre de production a déjà été réalisé"

#: build/models.py:997
msgid "Build output does not match Build Order"
msgstr "L'ordre de production de correspond pas à l'ordre de commande"

#: build/models.py:1079 build/serializers.py:303 build/serializers.py:352
#: build/serializers.py:972 order/models.py:704 order/serializers.py:601
#: order/serializers.py:780 part/serializers.py:1657 stock/models.py:863
#: stock/models.py:1730 stock/serializers.py:727
msgid "Quantity must be greater than zero"
msgstr "La quantité doit être supérieure à zéro"

#: build/models.py:1083 build/serializers.py:307
msgid "Quantity cannot be greater than the output quantity"
msgstr "La quantité ne peut pas être supérieure à la quantité de sortie"

#: build/models.py:1148 build/serializers.py:615
#, python-brace-format
msgid "Build output {serial} has not passed all required tests"
msgstr "La sortie de compilation {serial} n'a pas réussi tous les tests requis"

#: build/models.py:1534
msgid "Build Order Line Item"
msgstr ""

#: build/models.py:1558
msgid "Build object"
msgstr "Création de l'objet"

#: build/models.py:1570 build/models.py:1829 build/serializers.py:291
#: build/serializers.py:337 build/serializers.py:1410 common/models.py:1255
#: order/models.py:1555 order/models.py:2288 order/serializers.py:1670
#: order/serializers.py:2131 part/models.py:3353 part/models.py:4321
#: part/serializers.py:275
#: report/templates/report/inventree_bill_of_materials_report.html:138
#: report/templates/report/inventree_build_order_report.html:113
#: report/templates/report/inventree_purchase_order_report.html:29
#: report/templates/report/inventree_sales_order_report.html:29
#: report/templates/report/inventree_sales_order_shipment_report.html:47
#: report/templates/report/inventree_sales_order_shipment_report.html:49
#: report/templates/report/inventree_stock_location_report.html:104
#: report/templates/report/inventree_test_report.html:90
#: report/templates/report/inventree_test_report.html:169
#: stock/serializers.py:135 stock/serializers.py:179 stock/serializers.py:718
#: templates/email/build_order_completed.html:18
msgid "Quantity"
msgstr "Quantité"

#: build/models.py:1571
msgid "Required quantity for build order"
msgstr "Quantité requise pour la commande de construction"

#: build/models.py:1655
msgid "Build item must specify a build output, as master part is marked as trackable"
msgstr "L'élément de construction doit spécifier une sortie de construction, la pièce maîtresse étant marquée comme objet traçable"

#: build/models.py:1666
#, python-brace-format
msgid "Allocated quantity ({q}) must not exceed available stock quantity ({a})"
msgstr "La quantité allouée ({q}) ne doit pas excéder la quantité disponible ({a})"

#: build/models.py:1687 order/models.py:2237
msgid "Stock item is over-allocated"
msgstr "L'article de stock est suralloué"

#: build/models.py:1692 order/models.py:2240
msgid "Allocation quantity must be greater than zero"
msgstr "La quantité allouée doit être supérieure à zéro"

#: build/models.py:1698
msgid "Quantity must be 1 for serialized stock"
msgstr "La quantité doit être de 1 pour stock sérialisé"

#: build/models.py:1758
msgid "Selected stock item does not match BOM line"
msgstr "L'article de stock sélectionné ne correspond pas à la ligne BOM"

#: build/models.py:1819 build/serializers.py:955 build/serializers.py:1263
#: order/serializers.py:1507 order/serializers.py:1528
#: report/templates/report/inventree_sales_order_shipment_report.html:29
#: stock/models.py:452 stock/serializers.py:101 stock/serializers.py:825
#: stock/serializers.py:1357 stock/serializers.py:1469
msgid "Stock Item"
msgstr "Article en stock"

#: build/models.py:1820
msgid "Source stock item"
msgstr "Stock d'origine de l'article"

#: build/models.py:1830
msgid "Stock quantity to allocate to build"
msgstr "Quantité de stock à allouer à la construction"

#: build/models.py:1839
msgid "Install into"
msgstr "Installer dans"

#: build/models.py:1840
msgid "Destination stock item"
msgstr "Stock de destination de l'article"

#: build/serializers.py:116
msgid "Build Level"
msgstr "Niveau de construction"

#: build/serializers.py:125 part/stocktake.py:219
msgid "Part Name"
msgstr "Nom de l'article"

#: build/serializers.py:143
msgid "Project Code Label"
msgstr ""

#: build/serializers.py:155
msgid "Create Child Builds"
msgstr ""

#: build/serializers.py:156
msgid "Automatically generate child build orders"
msgstr ""

#: build/serializers.py:239 build/serializers.py:981
msgid "Build Output"
msgstr "Sortie d'assemblage"

#: build/serializers.py:251
msgid "Build output does not match the parent build"
msgstr "L'ordre de production ne correspond pas à l'ordre parent"

#: build/serializers.py:255
msgid "Output part does not match BuildOrder part"
msgstr "La pièce en sortie ne correspond pas à la pièce de l'ordre de construction"

#: build/serializers.py:259
msgid "This build output has already been completed"
msgstr "Cet ordre de production a déjà été produit"

#: build/serializers.py:273
msgid "This build output is not fully allocated"
msgstr "Cet ordre de production n'est pas complètement attribué"

#: build/serializers.py:292 build/serializers.py:338
msgid "Enter quantity for build output"
msgstr "Entrer la quantité désiré pour la fabrication"

#: build/serializers.py:360
msgid "Integer quantity required for trackable parts"
msgstr "Quantité entière requise pour les pièces à suivre"

#: build/serializers.py:366
msgid "Integer quantity required, as the bill of materials contains trackable parts"
msgstr "Quantité entière requise, car la facture de matériaux contient des pièces à puce"

#: build/serializers.py:383 order/serializers.py:801 order/serializers.py:1674
#: stock/serializers.py:738
msgid "Serial Numbers"
msgstr "Numéros de série"

#: build/serializers.py:384
msgid "Enter serial numbers for build outputs"
msgstr "Entrer les numéros de séries pour la fabrication"

#: build/serializers.py:389 build/serializers.py:514 build/serializers.py:584
#: build/serializers.py:1267 build/serializers.py:1271 order/serializers.py:769
#: order/serializers.py:916 order/serializers.py:2022 part/serializers.py:1275
#: stock/serializers.py:110 stock/serializers.py:647 stock/serializers.py:749
#: stock/serializers.py:913 stock/serializers.py:1041 stock/serializers.py:1501
#: stock/serializers.py:1789 users/models.py:551
msgid "Location"
msgstr "Emplacement"

#: build/serializers.py:390
msgid "Stock location for build output"
msgstr "Emplacement de stock pour la sortie de la fabrication"

#: build/serializers.py:405
msgid "Auto Allocate Serial Numbers"
msgstr "Allouer automatiquement les numéros de série"

#: build/serializers.py:407
msgid "Automatically allocate required items with matching serial numbers"
msgstr "Affecter automatiquement les éléments requis avec les numéros de série correspondants"

#: build/serializers.py:440 order/serializers.py:894 stock/api.py:1077
#: stock/models.py:1753
msgid "The following serial numbers already exist or are invalid"
msgstr "Les numéros de série suivants existent déjà, ou sont invalides"

#: build/serializers.py:482 build/serializers.py:538 build/serializers.py:622
msgid "A list of build outputs must be provided"
msgstr "Une liste d'ordre de production doit être fourni"

#: build/serializers.py:515
msgid "Stock location for scrapped outputs"
msgstr "Emplacement du stock pour les sorties épuisées"

#: build/serializers.py:521
msgid "Discard Allocations"
msgstr "Ignorer les allocations"

#: build/serializers.py:522
msgid "Discard any stock allocations for scrapped outputs"
msgstr "Abandonner les allocations de stock pour les sorties abandonnées"

#: build/serializers.py:527
msgid "Reason for scrapping build output(s)"
msgstr "Motif de l'élimination des produits de construction(s)"

#: build/serializers.py:585
msgid "Location for completed build outputs"
msgstr "Emplacement des ordres de production achevés"

#: build/serializers.py:593
msgid "Accept Incomplete Allocation"
msgstr "Accepter l'allocation incomplète"

#: build/serializers.py:594
msgid "Complete outputs if stock has not been fully allocated"
msgstr "Compléter les sorties si le stock n'a pas été entièrement alloué"

#: build/serializers.py:709
msgid "Consume Allocated Stock"
msgstr "Consommation du stock alloué"

#: build/serializers.py:710
msgid "Consume any stock which has already been allocated to this build"
msgstr "Consommer tout stock qui a déjà été alloué à cette construction"

#: build/serializers.py:716
msgid "Remove Incomplete Outputs"
msgstr "Retirer les sorties incomplètes"

#: build/serializers.py:717
msgid "Delete any build outputs which have not been completed"
msgstr "Supprimer toutes les sorties de construction qui n'ont pas été complétées"

#: build/serializers.py:744
msgid "Not permitted"
msgstr "Non permis"

#: build/serializers.py:745
msgid "Accept as consumed by this build order"
msgstr "Accepter comme consommé par cet ordre de construction"

#: build/serializers.py:746
msgid "Deallocate before completing this build order"
msgstr "Désaffecter avant de terminer cette commande de fabrication"

#: build/serializers.py:773
msgid "Overallocated Stock"
msgstr "Stock suralloué"

#: build/serializers.py:776
msgid "How do you want to handle extra stock items assigned to the build order"
msgstr "Comment voulez-vous gérer les articles en stock supplémentaires assignés à l'ordre de construction"

#: build/serializers.py:787
msgid "Some stock items have been overallocated"
msgstr "Certains articles de stock ont été suralloués"

#: build/serializers.py:792
msgid "Accept Unallocated"
msgstr "Accepter les non-alloués"

#: build/serializers.py:794
msgid "Accept that stock items have not been fully allocated to this build order"
msgstr "Accepter les articles de stock qui n'ont pas été complètement alloués à cette ordre de production"

#: build/serializers.py:805
msgid "Required stock has not been fully allocated"
msgstr "Le stock requis n'a pas encore été totalement alloué"

#: build/serializers.py:810 order/serializers.py:444 order/serializers.py:1575
msgid "Accept Incomplete"
msgstr "Accepter les incomplèts"

#: build/serializers.py:812
msgid "Accept that the required number of build outputs have not been completed"
msgstr "Accepter que tous les ordres de production n'aient pas encore été achevés"

#: build/serializers.py:823
msgid "Required build quantity has not been completed"
msgstr "La quantité nécessaire n'a pas encore été complétée"

#: build/serializers.py:835
msgid "Build order has open child build orders"
msgstr ""

#: build/serializers.py:838
msgid "Build order must be in production state"
msgstr ""

#: build/serializers.py:841
msgid "Build order has incomplete outputs"
msgstr "L'ordre de production a des sorties incomplètes"

#: build/serializers.py:880
msgid "Build Line"
msgstr "Chaîne d'assemblage"

#: build/serializers.py:888
msgid "Build output"
msgstr "Sortie d'assemblage"

#: build/serializers.py:896
msgid "Build output must point to the same build"
msgstr "La sortie de la construction doit pointer vers la même construction"

#: build/serializers.py:927
msgid "Build Line Item"
msgstr "Élément de la ligne de construction"

#: build/serializers.py:945
msgid "bom_item.part must point to the same part as the build order"
msgstr "bom_item.part doit pointer sur la même pièce que l'ordre de construction"

#: build/serializers.py:961 stock/serializers.py:1370
msgid "Item must be in stock"
msgstr "L'article doit être en stock"

#: build/serializers.py:1004 order/serializers.py:1561
#, python-brace-format
msgid "Available quantity ({q}) exceeded"
msgstr "Quantité disponible ({q}) dépassée"

#: build/serializers.py:1010
msgid "Build output must be specified for allocation of tracked parts"
msgstr "La sortie de construction doit être spécifiée pour l'allocation des pièces suivies"

#: build/serializers.py:1018
msgid "Build output cannot be specified for allocation of untracked parts"
msgstr "La sortie de la construction ne peut pas être spécifiée pour l'allocation des pièces non suivies"

#: build/serializers.py:1042 order/serializers.py:1834
msgid "Allocation items must be provided"
msgstr "Les articles d'allocation doivent être fournis"

#: build/serializers.py:1106
msgid "Stock location where parts are to be sourced (leave blank to take from any location)"
msgstr "Emplacement de stock où les pièces doivent être fournies (laissez vide pour les prendre à partir de n'importe quel emplacement)"

#: build/serializers.py:1115
msgid "Exclude Location"
msgstr "Emplacements exclus"

#: build/serializers.py:1116
msgid "Exclude stock items from this selected location"
msgstr "Exclure les articles de stock de cet emplacement sélectionné"

#: build/serializers.py:1121
msgid "Interchangeable Stock"
msgstr "Stock interchangeable"

#: build/serializers.py:1122
msgid "Stock items in multiple locations can be used interchangeably"
msgstr "Les articles de stock à plusieurs emplacements peuvent être utilisés de manière interchangeable"

#: build/serializers.py:1127
msgid "Substitute Stock"
msgstr "Stock de substitution"

#: build/serializers.py:1128
msgid "Allow allocation of substitute parts"
msgstr "Autoriser l'allocation de pièces de remplacement"

#: build/serializers.py:1133
msgid "Optional Items"
msgstr "Objets Optionnels"

#: build/serializers.py:1134
msgid "Allocate optional BOM items to build order"
msgstr "Affecter des éléments de nomenclature facultatifs à l'ordre de fabrication"

#: build/serializers.py:1156
msgid "Failed to start auto-allocation task"
msgstr ""

#: build/serializers.py:1230
msgid "BOM Reference"
msgstr ""

#: build/serializers.py:1236
msgid "BOM Part ID"
msgstr ""

#: build/serializers.py:1243
msgid "BOM Part Name"
msgstr ""

#: build/serializers.py:1278 build/serializers.py:1434
msgid "Build"
msgstr ""

#: build/serializers.py:1286 company/models.py:662 order/api.py:304
#: order/api.py:309 order/api.py:508 order/serializers.py:593
#: stock/models.py:959 stock/serializers.py:631
msgid "Supplier Part"
msgstr "Pièce fournisseur"

#: build/serializers.py:1294 stock/serializers.py:662
msgid "Allocated Quantity"
msgstr "Quantité allouée"

#: build/serializers.py:1371
msgid "Build Reference"
msgstr ""

#: build/serializers.py:1381
msgid "Part Category Name"
msgstr ""

#: build/serializers.py:1401 common/setting/system.py:453 part/models.py:1223
msgid "Trackable"
msgstr "Traçable"

#: build/serializers.py:1404
msgid "Inherited"
msgstr "Reçu de quelqu'un"

#: build/serializers.py:1407 part/models.py:4381
msgid "Allow Variants"
msgstr "Autoriser les variantes"

#: build/serializers.py:1412 build/serializers.py:1416 part/models.py:4179
#: part/models.py:4657 stock/api.py:831
msgid "BOM Item"
msgstr "Article du BOM"

#: build/serializers.py:1445
msgid "Allocated Stock"
msgstr "Stock alloué"

#: build/serializers.py:1447 company/serializers.py:424
#: order/serializers.py:1271 part/serializers.py:943 part/serializers.py:1690
msgid "On Order"
msgstr "En Commande"

#: build/serializers.py:1449 order/serializers.py:1272 part/serializers.py:1694
msgid "In Production"
msgstr "En Production"

#: build/serializers.py:1451 part/serializers.py:964
msgid "External Stock"
msgstr "Stock externe"

#: build/serializers.py:1452 part/serializers.py:1723
msgid "Available Stock"
msgstr "Stock disponible"

#: build/serializers.py:1454
msgid "Available Substitute Stock"
msgstr "Stock de substitution disponible"

#: build/serializers.py:1457
msgid "Available Variant Stock"
msgstr "Stock de variantes disponibles"

#: build/status_codes.py:11 generic/states/tests.py:21
#: generic/states/tests.py:131 order/status_codes.py:12
#: order/status_codes.py:44 order/status_codes.py:76 order/status_codes.py:102
msgid "Pending"
msgstr "En attente"

#: build/status_codes.py:12
msgid "Production"
msgstr "Fabrication"

#: build/status_codes.py:13 order/status_codes.py:14 order/status_codes.py:51
#: order/status_codes.py:81
msgid "On Hold"
msgstr "En pause"

#: build/status_codes.py:14 order/status_codes.py:16 order/status_codes.py:53
#: order/status_codes.py:84
msgid "Cancelled"
msgstr "Annulé"

#: build/status_codes.py:15 generic/states/tests.py:23 importer/models.py:529
#: importer/status_codes.py:27 order/status_codes.py:15
#: order/status_codes.py:52 order/status_codes.py:83
msgid "Complete"
msgstr "Terminé"

#: build/tasks.py:193
msgid "Stock required for build order"
msgstr "Stock requis pour la commande de construction"

#: build/tasks.py:267
msgid "Overdue Build Order"
msgstr "Ordre de commande en retard"

#: build/tasks.py:272
#, python-brace-format
msgid "Build order {bo} is now overdue"
msgstr "L'ordre de commande {bo} est maintenant en retard"

#: common/api.py:710
msgid "Is Link"
msgstr "C'est un lien"

#: common/api.py:718
msgid "Is File"
msgstr "C'est un fichier"

#: common/api.py:761
msgid "User does not have permission to delete these attachments"
msgstr ""

#: common/api.py:778
msgid "User does not have permission to delete this attachment"
msgstr "L'utilisateur n'a pas les permissions de supprimer cette pièce jointe"

#: common/currency.py:120
msgid "Invalid currency code"
msgstr "Code de devise invalide"

#: common/currency.py:122
msgid "Duplicate currency code"
msgstr "Code de devise en double"

#: common/currency.py:127
msgid "No valid currency codes provided"
msgstr "Aucun code de devise valide fourni"

#: common/currency.py:144
msgid "No plugin"
msgstr "Pas de plugin"

#: common/models.py:89
msgid "Updated"
msgstr "Mise à jour"

#: common/models.py:90
msgid "Timestamp of last update"
msgstr "Date de la dernière mise à jour"

#: common/models.py:117
msgid "Unique project code"
msgstr "Code projet unique"

#: common/models.py:124
msgid "Project description"
msgstr "Description du projet"

#: common/models.py:133
msgid "User or group responsible for this project"
msgstr "Utilisateur ou groupe responsable de ce projet"

#: common/models.py:721 common/models.py:1187 common/models.py:1225
msgid "Settings key"
msgstr "Paramétrés des touches"

#: common/models.py:725
msgid "Settings value"
msgstr "Valeur du paramètre"

#: common/models.py:780
msgid "Chosen value is not a valid option"
msgstr "La valeur choisie n'est pas une option valide"

#: common/models.py:796
msgid "Value must be a boolean value"
msgstr "La valeur doit être une valeur booléenne"

#: common/models.py:804
msgid "Value must be an integer value"
msgstr "La valeur doit être un nombre entier"

#: common/models.py:812
msgid "Value must be a valid number"
msgstr "Valeur doit être un nombre valide"

#: common/models.py:837
msgid "Value does not pass validation checks"
msgstr ""

#: common/models.py:859
msgid "Key string must be unique"
msgstr "La chaîne de caractères constituant la clé doit être unique"

#: common/models.py:1233 common/models.py:1234 common/models.py:1338
#: common/models.py:1339 common/models.py:1584 common/models.py:1585
#: common/models.py:1915 common/models.py:1916 common/models.py:2297
#: importer/models.py:97 part/models.py:3376 part/models.py:3463
#: part/models.py:3537 part/models.py:3565 plugin/models.py:322
#: plugin/models.py:323 report/templates/report/inventree_test_report.html:105
#: users/models.py:130 users/models.py:503
msgid "User"
msgstr "Utilisateur"

#: common/models.py:1256
msgid "Price break quantity"
msgstr ""

#: common/models.py:1263 company/serializers.py:567 order/models.py:1632
#: order/models.py:2734
msgid "Price"
msgstr "Prix"

#: common/models.py:1264
msgid "Unit price at specified quantity"
msgstr ""

#: common/models.py:1315 common/models.py:1500
msgid "Endpoint"
msgstr ""

#: common/models.py:1316
msgid "Endpoint at which this webhook is received"
msgstr ""

#: common/models.py:1326
msgid "Name for this webhook"
msgstr ""

#: common/models.py:1330 common/models.py:2156 common/models.py:2263
#: company/models.py:164 company/models.py:799 machine/models.py:39
#: part/models.py:1246 plugin/models.py:68 stock/api.py:591 users/models.py:201
#: users/models.py:556 users/serializers.py:333
msgid "Active"
msgstr "Actif"

#: common/models.py:1330
msgid "Is this webhook active"
msgstr "Ce webhook (lien de rappel HTTP) est-il actif"

#: common/models.py:1346 users/models.py:178
msgid "Token"
msgstr "Jeton"

#: common/models.py:1347
msgid "Token for access"
msgstr "Jeton d'accès"

#: common/models.py:1355
msgid "Secret"
msgstr "Confidentiel"

#: common/models.py:1356
msgid "Shared secret for HMAC"
msgstr ""

#: common/models.py:1464
msgid "Message ID"
msgstr "ID message"

#: common/models.py:1465
msgid "Unique identifier for this message"
msgstr "Identifiant unique pour ce message"

#: common/models.py:1473
msgid "Host"
msgstr "Hôte"

#: common/models.py:1474
msgid "Host from which this message was received"
msgstr "Hôte à partir duquel ce message a été reçu"

#: common/models.py:1482
msgid "Header"
msgstr "Entête"

#: common/models.py:1483
msgid "Header of this message"
msgstr "En-tête de ce message"

#: common/models.py:1490
msgid "Body"
msgstr "Corps"

#: common/models.py:1491
msgid "Body of this message"
msgstr "Corps de ce message"

#: common/models.py:1501
msgid "Endpoint on which this message was received"
msgstr "Endpoint à partir duquel ce message a été reçu"

#: common/models.py:1506
msgid "Worked on"
msgstr ""

#: common/models.py:1507
msgid "Was the work on this message finished?"
msgstr "Le travail sur ce message est-il terminé ?"

#: common/models.py:1633
msgid "Id"
msgstr "Id"

#: common/models.py:1635 part/serializers.py:281
msgid "Title"
msgstr "Titre"

#: common/models.py:1637 common/models.py:1898 company/models.py:149
#: company/models.py:445 company/models.py:512 company/models.py:816
#: order/models.py:444 order/models.py:1585 order/models.py:2056
#: part/models.py:1113
#: report/templates/report/inventree_build_order_report.html:164
msgid "Link"
msgstr "Lien"

#: common/models.py:1639
msgid "Published"
msgstr "Publié"

#: common/models.py:1641
msgid "Author"
msgstr "Auteur"

#: common/models.py:1643
msgid "Summary"
msgstr "Résumé"

#: common/models.py:1646
msgid "Read"
msgstr "Lu"

#: common/models.py:1646
msgid "Was this news item read?"
msgstr "Cette nouvelle a-t-elle été lue ?"

#: common/models.py:1663 company/models.py:160 part/models.py:1124
#: report/templates/report/inventree_bill_of_materials_report.html:126
#: report/templates/report/inventree_bill_of_materials_report.html:148
#: report/templates/report/inventree_return_order_report.html:35
msgid "Image"
msgstr "Image"

#: common/models.py:1663
msgid "Image file"
msgstr "Fichier image"

#: common/models.py:1675
msgid "Target model type for this image"
msgstr ""

#: common/models.py:1679
msgid "Target model ID for this image"
msgstr ""

#: common/models.py:1701
msgid "Custom Unit"
msgstr ""

#: common/models.py:1719
msgid "Unit symbol must be unique"
msgstr ""

#: common/models.py:1734
msgid "Unit name must be a valid identifier"
msgstr ""

#: common/models.py:1753
msgid "Unit name"
msgstr ""

#: common/models.py:1760
msgid "Symbol"
msgstr "Symbole"

#: common/models.py:1761
msgid "Optional unit symbol"
msgstr "Symbole d'unité facultatif"

#: common/models.py:1767
msgid "Definition"
msgstr "Définition"

#: common/models.py:1768
msgid "Unit definition"
msgstr "Définition de l'unité"

#: common/models.py:1826 common/models.py:1889 stock/models.py:2827
#: stock/serializers.py:258
msgid "Attachment"
msgstr "Pièce jointe"

#: common/models.py:1843
msgid "Missing file"
msgstr "Fichier manquant"

#: common/models.py:1844
msgid "Missing external link"
msgstr "Lien externe manquant"

#: common/models.py:1881
msgid "Model type"
msgstr ""

#: common/models.py:1882
msgid "Target model type for image"
msgstr ""

#: common/models.py:1890
msgid "Select file to attach"
msgstr "Sélectionnez un fichier à joindre"

#: common/models.py:1906
msgid "Comment"
msgstr "Commentaire"

#: common/models.py:1907
msgid "Attachment comment"
msgstr ""

#: common/models.py:1923
msgid "Upload date"
msgstr ""

#: common/models.py:1924
msgid "Date the file was uploaded"
msgstr ""

#: common/models.py:1928
msgid "File size"
msgstr ""

#: common/models.py:1928
msgid "File size in bytes"
msgstr ""

#: common/models.py:1966 common/serializers.py:637
msgid "Invalid model type specified for attachment"
msgstr ""

#: common/models.py:1987
msgid "Custom State"
msgstr ""

#: common/models.py:1988
msgid "Custom States"
msgstr ""

#: common/models.py:1993
msgid "Reference Status Set"
msgstr ""

#: common/models.py:1994
msgid "Status set that is extended with this custom state"
msgstr ""

#: common/models.py:1998 generic/states/serializers.py:18
msgid "Logical Key"
msgstr ""

#: common/models.py:2000
msgid "State logical key that is equal to this custom state in business logic"
msgstr ""

#: common/models.py:2005 common/models.py:2244 company/models.py:600
#: report/templates/report/inventree_test_report.html:104 stock/models.py:2819
msgid "Value"
msgstr "Valeur"

#: common/models.py:2006
msgid "Numerical value that will be saved in the models database"
msgstr ""

#: common/models.py:2012
msgid "Name of the state"
msgstr ""

#: common/models.py:2021 common/models.py:2250 generic/states/serializers.py:22
#: part/serializers.py:283
msgid "Label"
msgstr "Étiquette"

#: common/models.py:2022
msgid "Label that will be displayed in the frontend"
msgstr ""

#: common/models.py:2029 generic/states/serializers.py:24
msgid "Color"
msgstr ""

#: common/models.py:2030
msgid "Color that will be displayed in the frontend"
msgstr ""

#: common/models.py:2038 part/serializers.py:285
msgid "Model"
msgstr ""

#: common/models.py:2039
msgid "Model this state is associated with"
msgstr ""

#: common/models.py:2054
msgid "Model must be selected"
msgstr ""

#: common/models.py:2057
msgid "Key must be selected"
msgstr ""

#: common/models.py:2060
msgid "Logical key must be selected"
msgstr ""

#: common/models.py:2064
msgid "Key must be different from logical key"
msgstr ""

#: common/models.py:2071
msgid "Valid reference status class must be provided"
msgstr ""

#: common/models.py:2077
msgid "Key must be different from the logical keys of the reference status"
msgstr ""

#: common/models.py:2084
msgid "Logical key must be in the logical keys of the reference status"
msgstr ""

#: common/models.py:2091
msgid "Name must be different from the names of the reference status"
msgstr ""

#: common/models.py:2131 common/models.py:2238 part/models.py:3876
msgid "Selection List"
msgstr ""

#: common/models.py:2132
msgid "Selection Lists"
msgstr ""

#: common/models.py:2137
msgid "Name of the selection list"
msgstr ""

#: common/models.py:2144
msgid "Description of the selection list"
msgstr ""

#: common/models.py:2150 part/models.py:1251
msgid "Locked"
msgstr ""

#: common/models.py:2151
msgid "Is this selection list locked?"
msgstr ""

#: common/models.py:2157
msgid "Can this selection list be used?"
msgstr ""

#: common/models.py:2165
msgid "Source Plugin"
msgstr ""

#: common/models.py:2166
msgid "Plugin which provides the selection list"
msgstr ""

#: common/models.py:2171
msgid "Source String"
msgstr ""

#: common/models.py:2172
msgid "Optional string identifying the source used for this list"
msgstr ""

#: common/models.py:2181
msgid "Default Entry"
msgstr ""

#: common/models.py:2182
msgid "Default entry for this selection list"
msgstr ""

#: common/models.py:2187
msgid "Created"
msgstr "Créé le"

#: common/models.py:2188
msgid "Date and time that the selection list was created"
msgstr ""

#: common/models.py:2193
msgid "Last Updated"
msgstr "Dernière mise à jour"

#: common/models.py:2194
msgid "Date and time that the selection list was last updated"
msgstr ""

#: common/models.py:2228
msgid "Selection List Entry"
msgstr ""

#: common/models.py:2229
msgid "Selection List Entries"
msgstr ""

#: common/models.py:2239
msgid "Selection list to which this entry belongs"
msgstr ""

#: common/models.py:2245
msgid "Value of the selection list entry"
msgstr ""

#: common/models.py:2251
msgid "Label for the selection list entry"
msgstr ""

#: common/models.py:2257
msgid "Description of the selection list entry"
msgstr ""

#: common/models.py:2264
msgid "Is this selection list entry active?"
msgstr ""

#: common/models.py:2282
msgid "Barcode Scan"
msgstr "Analyse du code-barres"

#: common/models.py:2286 importer/models.py:523 part/models.py:4041
msgid "Data"
msgstr "Données"

#: common/models.py:2287
msgid "Barcode data"
msgstr "Données du code-barres"

#: common/models.py:2298
msgid "User who scanned the barcode"
msgstr "Utilisateur qui a scanné le code-barres"

#: common/models.py:2303 importer/models.py:66
msgid "Timestamp"
msgstr ""

#: common/models.py:2304
msgid "Date and time of the barcode scan"
msgstr "Date et heure du scan de code-barres"

#: common/models.py:2310
msgid "URL endpoint which processed the barcode"
msgstr ""

#: common/models.py:2317 order/models.py:1622 plugin/serializers.py:93
msgid "Context"
msgstr "Contexte"

#: common/models.py:2318
msgid "Context data for the barcode scan"
msgstr ""

#: common/models.py:2325
msgid "Response"
msgstr "Réponse"

#: common/models.py:2326
msgid "Response data from the barcode scan"
msgstr ""

#: common/models.py:2332 report/templates/report/inventree_test_report.html:103
#: stock/models.py:2813
msgid "Result"
msgstr "Résultat"

#: common/models.py:2333
msgid "Was the barcode scan successful?"
msgstr ""

#: common/notifications.py:332
#, python-brace-format
msgid "New {verbose_name}"
msgstr "Nouveau {verbose_name}"

#: common/notifications.py:334
msgid "A new order has been created and assigned to you"
msgstr "Une nouvelle commande a été créée et vous a été assignée"

#: common/notifications.py:340
#, python-brace-format
msgid "{verbose_name} canceled"
msgstr "{verbose_name} annulé"

#: common/notifications.py:342
msgid "A order that is assigned to you was canceled"
msgstr "Une commande qui vous est assignée a été annulée"

#: common/notifications.py:348 common/notifications.py:355 order/api.py:559
msgid "Items Received"
msgstr "Articles reçus"

#: common/notifications.py:350
msgid "Items have been received against a purchase order"
msgstr "Des articles d'un bon de commande ont été reçus"

#: common/notifications.py:357
msgid "Items have been received against a return order"
msgstr ""

#: common/notifications.py:505
msgid "Error raised by plugin"
msgstr "Erreur déclenchée par le plugin"

#: common/serializers.py:451
msgid "Is Running"
msgstr "En cours d'exécution"

#: common/serializers.py:457
msgid "Pending Tasks"
msgstr "Tâches en attente"

#: common/serializers.py:463
msgid "Scheduled Tasks"
msgstr "Tâches planifiées"

#: common/serializers.py:469
msgid "Failed Tasks"
msgstr "Tâches échouées"

#: common/serializers.py:484
msgid "Task ID"
msgstr "ID de la tâche"

#: common/serializers.py:484
msgid "Unique task ID"
msgstr "ID unique de la tâche"

#: common/serializers.py:486
msgid "Lock"
msgstr "Verrouillé"

#: common/serializers.py:486
msgid "Lock time"
msgstr "Heure verrouillé"

#: common/serializers.py:488
msgid "Task name"
msgstr "Nom de la tâche"

#: common/serializers.py:490
msgid "Function"
msgstr "Fonction"

#: common/serializers.py:490
msgid "Function name"
msgstr "Nom de la fonction"

#: common/serializers.py:492
msgid "Arguments"
msgstr "Arguments"

#: common/serializers.py:492
msgid "Task arguments"
msgstr "Arguments tâche"

#: common/serializers.py:495
msgid "Keyword Arguments"
msgstr "Mots-clés Arguments"

#: common/serializers.py:495
msgid "Task keyword arguments"
msgstr "Mots-clés arguments tâche"

#: common/serializers.py:605
msgid "Filename"
msgstr "Nom du fichier"

#: common/serializers.py:612 importer/models.py:86 report/api.py:41
#: report/models.py:277 report/serializers.py:53
msgid "Model Type"
msgstr ""

#: common/serializers.py:640
msgid "User does not have permission to create or edit attachments for this model"
msgstr ""

#: common/serializers.py:684 common/serializers.py:787
msgid "Selection list is locked"
msgstr ""

#: common/setting/system.py:97
msgid "No group"
msgstr "Pas de groupe"

#: common/setting/system.py:156
msgid "Site URL is locked by configuration"
msgstr "L'URL du site est verrouillée par configuration"

#: common/setting/system.py:167
msgid "Restart required"
msgstr "Redémarrage nécessaire"

#: common/setting/system.py:168
msgid "A setting has been changed which requires a server restart"
msgstr "Un paramètre a été modifié, ce qui nécessite un redémarrage du serveur"

#: common/setting/system.py:174
msgid "Pending migrations"
msgstr "Migration en attente"

#: common/setting/system.py:175
msgid "Number of pending database migrations"
msgstr "Nombre de migrations de base de données en attente"

#: common/setting/system.py:180
msgid "Instance ID"
msgstr ""

#: common/setting/system.py:181
msgid "Unique identifier for this InvenTree instance"
msgstr ""

#: common/setting/system.py:186
msgid "Announce ID"
msgstr ""

#: common/setting/system.py:188
msgid "Announce the instance ID of the server in the server status info (unauthenticated)"
msgstr ""

#: common/setting/system.py:194
msgid "Server Instance Name"
msgstr "Nom de l'instance du serveur"

#: common/setting/system.py:196
msgid "String descriptor for the server instance"
msgstr "Chaîne de caractères descriptive pour l'instance serveur"

#: common/setting/system.py:200
msgid "Use instance name"
msgstr "Utiliser le nom de l'instance"

#: common/setting/system.py:201
msgid "Use the instance name in the title-bar"
msgstr "Utiliser le nom de l’instance dans la barre de titre"

#: common/setting/system.py:206
msgid "Restrict showing `about`"
msgstr "Limiter l'affichage de `about`"

#: common/setting/system.py:207
msgid "Show the `about` modal only to superusers"
msgstr "Afficher la modale `about` uniquement aux super-utilisateurs"

#: common/setting/system.py:212 company/models.py:108 company/models.py:109
msgid "Company name"
msgstr "Nom de la société"

#: common/setting/system.py:213
msgid "Internal company name"
msgstr "Nom de société interne"

#: common/setting/system.py:217
msgid "Base URL"
msgstr "URL de base"

#: common/setting/system.py:218
msgid "Base URL for server instance"
msgstr "URL de base pour l'instance serveur"

#: common/setting/system.py:224
msgid "Default Currency"
msgstr "Devise par défaut"

#: common/setting/system.py:225
msgid "Select base currency for pricing calculations"
msgstr "Sélectionnez la devise de base pour les calculs de prix"

#: common/setting/system.py:231
msgid "Supported Currencies"
msgstr "Devises supportées"

#: common/setting/system.py:232
msgid "List of supported currency codes"
msgstr "Liste des codes de devises supportés"

#: common/setting/system.py:238
msgid "Currency Update Interval"
msgstr "Intervalle de mise à jour des devises"

#: common/setting/system.py:239
msgid "How often to update exchange rates (set to zero to disable)"
msgstr "Fréquence de mise à jour des taux de change (définir à zéro pour désactiver)"

#: common/setting/system.py:241 common/setting/system.py:281
#: common/setting/system.py:294 common/setting/system.py:302
#: common/setting/system.py:309 common/setting/system.py:318
#: common/setting/system.py:567 common/setting/system.py:587
#: common/setting/system.py:684 common/setting/system.py:1068
msgid "days"
msgstr "jours"

#: common/setting/system.py:245
msgid "Currency Update Plugin"
msgstr "Plugin de mise à jour de devise"

#: common/setting/system.py:246
msgid "Currency update plugin to use"
msgstr "Plugin de mise à jour des devises à utiliser"

#: common/setting/system.py:251
msgid "Download from URL"
msgstr "Télécharger depuis l'URL"

#: common/setting/system.py:252
msgid "Allow download of remote images and files from external URL"
msgstr "Autoriser le téléchargement d'images distantes et de fichiers à partir d'URLs externes"

#: common/setting/system.py:257
msgid "Download Size Limit"
msgstr "Limite du volume de téléchargement"

#: common/setting/system.py:258
msgid "Maximum allowable download size for remote image"
msgstr "Taille maximale autorisée pour le téléchargement de l'image distante"

#: common/setting/system.py:264
msgid "User-agent used to download from URL"
msgstr "Agent utilisateur utilisé pour télécharger depuis l'URL"

#: common/setting/system.py:266
msgid "Allow to override the user-agent used to download images and files from external URL (leave blank for the default)"
msgstr "Permettre de remplacer l'agent utilisateur utilisé pour télécharger des images et des fichiers à partir d'URL externe (laisser vide pour la valeur par défaut)"

#: common/setting/system.py:271
msgid "Strict URL Validation"
msgstr "Validation stricte d'URL"

#: common/setting/system.py:272
msgid "Require schema specification when validating URLs"
msgstr "Spécification du schéma nécessaire lors de la validation des URL"

#: common/setting/system.py:277
msgid "Update Check Interval"
msgstr "Intervalle de vérification des mises à jour"

#: common/setting/system.py:278
msgid "How often to check for updates (set to zero to disable)"
msgstr "À quelle fréquence vérifier les mises à jour (définir à zéro pour désactiver)"

#: common/setting/system.py:284
msgid "Automatic Backup"
msgstr "Backup automatique"

#: common/setting/system.py:285
msgid "Enable automatic backup of database and media files"
msgstr "Activer le backup automatique de la base de données et des fichiers médias"

#: common/setting/system.py:290
msgid "Auto Backup Interval"
msgstr "Intervalle de sauvegarde automatique"

#: common/setting/system.py:291
msgid "Specify number of days between automated backup events"
msgstr "Spécifiez le nombre de jours entre les sauvegardes automatique"

#: common/setting/system.py:297
msgid "Task Deletion Interval"
msgstr "Intervalle de suppression des tâches"

#: common/setting/system.py:299
msgid "Background task results will be deleted after specified number of days"
msgstr "Les résultats de la tâche en arrière-plan seront supprimés après le nombre de jours spécifié"

#: common/setting/system.py:306
msgid "Error Log Deletion Interval"
msgstr "Intervalle de suppression du journal d'erreur"

#: common/setting/system.py:307
msgid "Error logs will be deleted after specified number of days"
msgstr "Les logs d'erreur seront supprimés après le nombre de jours spécifié"

#: common/setting/system.py:313
msgid "Notification Deletion Interval"
msgstr "Intervalle de suppression du journal de notification"

#: common/setting/system.py:315
msgid "User notifications will be deleted after specified number of days"
msgstr "Les notifications de l'utilisateur seront supprimées après le nombre de jours spécifié"

#: common/setting/system.py:322
msgid "Barcode Support"
msgstr "Support des code-barres"

#: common/setting/system.py:323
msgid "Enable barcode scanner support in the web interface"
msgstr "Activer le support du scanner de codes-barres dans l'interface web"

#: common/setting/system.py:328
msgid "Store Barcode Results"
msgstr ""

#: common/setting/system.py:329
msgid "Store barcode scan results in the database"
msgstr ""

#: common/setting/system.py:334
msgid "Barcode Scans Maximum Count"
msgstr ""

#: common/setting/system.py:335
msgid "Maximum number of barcode scan results to store"
msgstr ""

#: common/setting/system.py:340
msgid "Barcode Input Delay"
msgstr "Délai d'entrée du code-barres"

#: common/setting/system.py:341
msgid "Barcode input processing delay time"
msgstr "Délai de traitement du code-barres"

#: common/setting/system.py:347
msgid "Barcode Webcam Support"
msgstr "Prise en charge de la webcam code-barres"

#: common/setting/system.py:348
msgid "Allow barcode scanning via webcam in browser"
msgstr "Autoriser la numérisation de codes-barres via la webcam dans le navigateur"

#: common/setting/system.py:353
msgid "Barcode Show Data"
msgstr ""

#: common/setting/system.py:354
msgid "Display barcode data in browser as text"
msgstr ""

#: common/setting/system.py:359
msgid "Barcode Generation Plugin"
msgstr ""

#: common/setting/system.py:360
msgid "Plugin to use for internal barcode data generation"
msgstr ""

#: common/setting/system.py:365
msgid "Part Revisions"
msgstr "Modifications de la pièce"

#: common/setting/system.py:366
msgid "Enable revision field for Part"
msgstr "Activer le champ de modification de la pièce"

#: common/setting/system.py:371
msgid "Assembly Revision Only"
msgstr ""

#: common/setting/system.py:372
msgid "Only allow revisions for assembly parts"
msgstr ""

#: common/setting/system.py:377
msgid "Allow Deletion from Assembly"
msgstr ""

#: common/setting/system.py:378
msgid "Allow deletion of parts which are used in an assembly"
msgstr "Permettre la suppression de pièces utilisées dans un assemblage"

#: common/setting/system.py:383
msgid "IPN Regex"
msgstr "Regex IPN"

#: common/setting/system.py:384
msgid "Regular expression pattern for matching Part IPN"
msgstr "Expression régulière pour la correspondance avec l'IPN de la Pièce"

#: common/setting/system.py:387
msgid "Allow Duplicate IPN"
msgstr "Autoriser les IPN dupliqués"

#: common/setting/system.py:388
msgid "Allow multiple parts to share the same IPN"
msgstr "Permettre à plusieurs pièces de partager le même IPN"

#: common/setting/system.py:393
msgid "Allow Editing IPN"
msgstr "Autoriser l'édition de l'IPN"

#: common/setting/system.py:394
msgid "Allow changing the IPN value while editing a part"
msgstr "Permettre de modifier la valeur de l'IPN lors de l'édition d'une pièce"

#: common/setting/system.py:399
msgid "Copy Part BOM Data"
msgstr "Copier les données de la pièce"

#: common/setting/system.py:400
msgid "Copy BOM data by default when duplicating a part"
msgstr "Copier les données des paramètres par défaut lors de la duplication d'une pièce"

#: common/setting/system.py:405
msgid "Copy Part Parameter Data"
msgstr "Copier les données des paramètres de la pièce"

#: common/setting/system.py:406
msgid "Copy parameter data by default when duplicating a part"
msgstr "Copier les données des paramètres par défaut lors de la duplication d'une pièce"

#: common/setting/system.py:411
msgid "Copy Part Test Data"
msgstr "Copier les données de test de la pièce"

#: common/setting/system.py:412
msgid "Copy test data by default when duplicating a part"
msgstr "Copier les données de test par défaut lors de la duplication d'une pièce"

#: common/setting/system.py:417
msgid "Copy Category Parameter Templates"
msgstr "Copier les templates de paramètres de catégorie"

#: common/setting/system.py:418
msgid "Copy category parameter templates when creating a part"
msgstr "Copier les templates de paramètres de la catégorie lors de la création d'une pièce"

#: common/setting/system.py:423 part/models.py:4035 report/models.py:357
#: report/models.py:563 report/serializers.py:90 report/serializers.py:131
#: stock/serializers.py:247
msgid "Template"
msgstr "Modèle"

#: common/setting/system.py:424
msgid "Parts are templates by default"
msgstr "Les pièces sont des templates par défaut"

#: common/setting/system.py:430
msgid "Parts can be assembled from other components by default"
msgstr "Les pièces peuvent être assemblées à partir d'autres composants par défaut"

#: common/setting/system.py:435 part/models.py:1217 part/serializers.py:1677
#: part/serializers.py:1683
msgid "Component"
msgstr "Composant"

#: common/setting/system.py:436
msgid "Parts can be used as sub-components by default"
msgstr "Les pièces peuvent être utilisées comme sous-composants par défaut"

#: common/setting/system.py:441 part/models.py:1235
msgid "Purchaseable"
msgstr "Achetable"

#: common/setting/system.py:442
msgid "Parts are purchaseable by default"
msgstr "Les pièces sont achetables par défaut"

#: common/setting/system.py:447 part/models.py:1241 stock/api.py:592
msgid "Salable"
msgstr "Vendable"

#: common/setting/system.py:448
msgid "Parts are salable by default"
msgstr "Les pièces sont vendables par défaut"

#: common/setting/system.py:454
msgid "Parts are trackable by default"
msgstr "Les pièces sont traçables par défaut"

#: common/setting/system.py:459 part/models.py:1257
msgid "Virtual"
msgstr "Virtuelle"

#: common/setting/system.py:460
msgid "Parts are virtual by default"
msgstr "Les pièces sont virtuelles par défaut"

#: common/setting/system.py:465
msgid "Show Import in Views"
msgstr "Afficher l'import dans les vues"

#: common/setting/system.py:466
msgid "Display the import wizard in some part views"
msgstr "Afficher l'assistant d'importation pour certaine vues de produits"

#: common/setting/system.py:471
msgid "Show related parts"
msgstr "Afficher les pièces connexes"

#: common/setting/system.py:472
msgid "Display related parts for a part"
msgstr "Afficher les pièces connexes à une pièce"

#: common/setting/system.py:477
msgid "Initial Stock Data"
msgstr "Stock initial"

#: common/setting/system.py:478
msgid "Allow creation of initial stock when adding a new part"
msgstr "Permettre la création d'un stock initial lors de l'ajout d'une nouvelle pièce"

#: common/setting/system.py:483
msgid "Initial Supplier Data"
msgstr "Données initiales du fournisseur"

#: common/setting/system.py:485
msgid "Allow creation of initial supplier data when adding a new part"
msgstr "Permettre la création des données initiales du fournisseur lors de l'ajout d'une nouvelle pièce"

#: common/setting/system.py:491
msgid "Part Name Display Format"
msgstr "Format d'affichage du nom de la pièce"

#: common/setting/system.py:492
msgid "Format to display the part name"
msgstr "Format pour afficher le nom de la pièce"

#: common/setting/system.py:498
msgid "Part Category Default Icon"
msgstr "Icône de catégorie par défaut"

#: common/setting/system.py:499
msgid "Part category default icon (empty means no icon)"
msgstr "Icône par défaut de la catégorie de la pièce (vide signifie aucune icône)"

#: common/setting/system.py:504
msgid "Enforce Parameter Units"
msgstr "Renforcer les unités des paramètres"

#: common/setting/system.py:506
msgid "If units are provided, parameter values must match the specified units"
msgstr "Si des unités sont fournies, les valeurs de paramètre doivent correspondre aux unités spécifiées"

#: common/setting/system.py:512
msgid "Minimum Pricing Decimal Places"
msgstr "Nombre minimal de décimales"

#: common/setting/system.py:514
msgid "Minimum number of decimal places to display when rendering pricing data"
msgstr "Nombre minimum de décimales à afficher lors de l'affichage des prix"

#: common/setting/system.py:525
msgid "Maximum Pricing Decimal Places"
msgstr ""

#: common/setting/system.py:527
msgid "Maximum number of decimal places to display when rendering pricing data"
msgstr ""

#: common/setting/system.py:538
msgid "Use Supplier Pricing"
msgstr "Utiliser le prix fournisseur"

#: common/setting/system.py:540
msgid "Include supplier price breaks in overall pricing calculations"
msgstr "Inclure les réductions de prix dans le calcul du prix global"

#: common/setting/system.py:546
msgid "Purchase History Override"
msgstr "Remplacer l'historique des achats"

#: common/setting/system.py:548
msgid "Historical purchase order pricing overrides supplier price breaks"
msgstr "La tarification historique des bons de commande remplace les réductions de prix des fournisseurs"

#: common/setting/system.py:554
msgid "Use Stock Item Pricing"
msgstr "Utiliser les prix des articles en stock"

#: common/setting/system.py:556
msgid "Use pricing from manually entered stock data for pricing calculations"
msgstr "Utiliser les prix des données de stock saisies manuellement pour calculer les prix"

#: common/setting/system.py:562
msgid "Stock Item Pricing Age"
msgstr "Âge de tarification des articles de stock"

#: common/setting/system.py:564
msgid "Exclude stock items older than this number of days from pricing calculations"
msgstr "Exclure les articles en stock datant de plus de ce nombre de jours des calculs de prix"

#: common/setting/system.py:571
msgid "Use Variant Pricing"
msgstr "Utiliser les prix variants"

#: common/setting/system.py:572
msgid "Include variant pricing in overall pricing calculations"
msgstr "Inclure la tarification variante dans le calcul global des prix"

#: common/setting/system.py:577
msgid "Active Variants Only"
msgstr "Variantes actives uniquement"

#: common/setting/system.py:579
msgid "Only use active variant parts for calculating variant pricing"
msgstr "N'utiliser que des pièces de variante actives pour calculer le prix de la variante"

#: common/setting/system.py:585
msgid "Pricing Rebuild Interval"
msgstr "Intervalle de regénération des prix"

#: common/setting/system.py:586
msgid "Number of days before part pricing is automatically updated"
msgstr "Nombre de jours avant la mise à jour automatique du prix de la pièce"

#: common/setting/system.py:592
msgid "Internal Prices"
msgstr "Prix internes"

#: common/setting/system.py:593
msgid "Enable internal prices for parts"
msgstr "Activer les prix internes pour les pièces"

#: common/setting/system.py:598
msgid "Internal Price Override"
msgstr "Substitution du prix interne"

#: common/setting/system.py:600
msgid "If available, internal prices override price range calculations"
msgstr "Si disponible, les prix internes remplacent les calculs de la fourchette de prix"

#: common/setting/system.py:606
msgid "Enable label printing"
msgstr "Activer l'impression d'étiquettes"

#: common/setting/system.py:607
msgid "Enable label printing from the web interface"
msgstr "Activer l'impression d'étiquettes depuis l'interface Web"

#: common/setting/system.py:612
msgid "Label Image DPI"
msgstr "Étiquette image DPI"

#: common/setting/system.py:614
msgid "DPI resolution when generating image files to supply to label printing plugins"
msgstr "Résolution DPI lors de la génération de fichiers image pour fournir aux plugins d'impression d'étiquettes"

#: common/setting/system.py:620
msgid "Enable Reports"
msgstr "Activer les rapports"

#: common/setting/system.py:621
msgid "Enable generation of reports"
msgstr "Activer la génération de rapports"

#: common/setting/system.py:626
msgid "Debug Mode"
msgstr "Mode Débogage"

#: common/setting/system.py:627
msgid "Generate reports in debug mode (HTML output)"
msgstr "Générer des rapports en mode debug (sortie HTML)"

#: common/setting/system.py:632
msgid "Log Report Errors"
msgstr "Journal des erreurs"

#: common/setting/system.py:633
msgid "Log errors which occur when generating reports"
msgstr ""

#: common/setting/system.py:638 plugin/builtin/labels/label_sheet.py:29
#: report/models.py:365
msgid "Page Size"
msgstr "Taille de la page"

#: common/setting/system.py:639
msgid "Default page size for PDF reports"
msgstr "Taille de page par défaut pour les rapports PDF"

#: common/setting/system.py:644
msgid "Globally Unique Serials"
msgstr "Numéro de Série Universellement Unique"

#: common/setting/system.py:645
msgid "Serial numbers for stock items must be globally unique"
msgstr "Les numéros de série pour les articles en stock doivent être uniques au niveau global"

#: common/setting/system.py:650
msgid "Autofill Serial Numbers"
msgstr "Remplir automatiquement les Numéros de Série"

#: common/setting/system.py:651
msgid "Autofill serial numbers in forms"
msgstr "Remplir automatiquement les numéros de série dans les formulaires"

#: common/setting/system.py:656
msgid "Delete Depleted Stock"
msgstr "Supprimer le stock épuisé"

#: common/setting/system.py:657
msgid "Determines default behavior when a stock item is depleted"
msgstr "Détermine le comportement par défaut lorsqu'un article de stock est épuisé"

#: common/setting/system.py:662
msgid "Batch Code Template"
msgstr "Modèle de code de lot"

#: common/setting/system.py:663
msgid "Template for generating default batch codes for stock items"
msgstr "Modèle pour générer des codes par défaut pour les articles en stock"

#: common/setting/system.py:667
msgid "Stock Expiry"
msgstr "Expiration du stock"

#: common/setting/system.py:668
msgid "Enable stock expiry functionality"
msgstr "Activer la fonctionnalité d'expiration du stock"

#: common/setting/system.py:673
msgid "Sell Expired Stock"
msgstr "Vendre le stock expiré"

#: common/setting/system.py:674
msgid "Allow sale of expired stock"
msgstr "Autoriser la vente de stock expiré"

#: common/setting/system.py:679
msgid "Stock Stale Time"
msgstr "Délai de péremption du stock"

#: common/setting/system.py:681
msgid "Number of days stock items are considered stale before expiring"
msgstr "Nombre de jours pendant lesquels les articles en stock sont considérés comme périmés avant d'expirer"

#: common/setting/system.py:688
msgid "Build Expired Stock"
msgstr "Construction de stock expirée"

#: common/setting/system.py:689
msgid "Allow building with expired stock"
msgstr "Autoriser la construction avec un stock expiré"

#: common/setting/system.py:694
msgid "Stock Ownership Control"
msgstr "Contrôle de la propriété des stocks"

#: common/setting/system.py:695
msgid "Enable ownership control over stock locations and items"
msgstr "Activer le contrôle de la propriété sur les emplacements de stock et les articles"

#: common/setting/system.py:700
msgid "Stock Location Default Icon"
msgstr "Icône par défaut de l'emplacement du stock"

#: common/setting/system.py:701
msgid "Stock location default icon (empty means no icon)"
msgstr "Icône par défaut de l'emplacement du stock (vide signifie aucune icône)"

#: common/setting/system.py:706
msgid "Show Installed Stock Items"
msgstr "Afficher les pièces en stock installées"

#: common/setting/system.py:707
msgid "Display installed stock items in stock tables"
msgstr ""

#: common/setting/system.py:712
msgid "Check BOM when installing items"
msgstr ""

#: common/setting/system.py:714
msgid "Installed stock items must exist in the BOM for the parent part"
msgstr ""

#: common/setting/system.py:720
msgid "Allow Out of Stock Transfer"
msgstr ""

#: common/setting/system.py:722
msgid "Allow stock items which are not in stock to be transferred between stock locations"
msgstr ""

#: common/setting/system.py:728
msgid "Build Order Reference Pattern"
msgstr "Modèle de référence de commande de construction"

#: common/setting/system.py:729
msgid "Required pattern for generating Build Order reference field"
msgstr "Modèle requis pour générer le champ de référence de l'ordre de construction"

#: common/setting/system.py:734 common/setting/system.py:788
#: common/setting/system.py:808 common/setting/system.py:844
msgid "Require Responsible Owner"
msgstr "Nécessite un Responsable propriétaire"

#: common/setting/system.py:735 common/setting/system.py:789
#: common/setting/system.py:809 common/setting/system.py:845
msgid "A responsible owner must be assigned to each order"
msgstr ""

#: common/setting/system.py:740
msgid "Require Active Part"
msgstr ""

#: common/setting/system.py:741
msgid "Prevent build order creation for inactive parts"
msgstr ""

#: common/setting/system.py:746
msgid "Require Locked Part"
msgstr "Requiert une pièce verrouillée"

#: common/setting/system.py:747
msgid "Prevent build order creation for unlocked parts"
msgstr ""

#: common/setting/system.py:752
msgid "Require Valid BOM"
msgstr ""

#: common/setting/system.py:753
msgid "Prevent build order creation unless BOM has been validated"
msgstr ""

#: common/setting/system.py:758
msgid "Require Closed Child Orders"
msgstr ""

#: common/setting/system.py:760
msgid "Prevent build order completion until all child orders are closed"
msgstr ""

#: common/setting/system.py:766
msgid "Block Until Tests Pass"
msgstr ""

#: common/setting/system.py:768
msgid "Prevent build outputs from being completed until all required tests pass"
msgstr ""

#: common/setting/system.py:774
msgid "Enable Return Orders"
msgstr "Activer les retours de commandes"

#: common/setting/system.py:775
msgid "Enable return order functionality in the user interface"
msgstr "Activer la fonctionnalité de retour de commande dans l'interface utilisateur"

#: common/setting/system.py:780
msgid "Return Order Reference Pattern"
msgstr "Modèle de référence de retour de commande"

#: common/setting/system.py:782
msgid "Required pattern for generating Return Order reference field"
msgstr ""

#: common/setting/system.py:794
msgid "Edit Completed Return Orders"
msgstr "Modifier les retours de commandes terminées"

#: common/setting/system.py:796
msgid "Allow editing of return orders after they have been completed"
msgstr "Autoriser la modification des retours après leur enregistrement"

#: common/setting/system.py:802
msgid "Sales Order Reference Pattern"
msgstr "Modèle de référence de bon de commande"

#: common/setting/system.py:803
msgid "Required pattern for generating Sales Order reference field"
msgstr "Modèle requis pour générer le champ de référence du bon de commande"

#: common/setting/system.py:814
msgid "Sales Order Default Shipment"
msgstr "Expédition par défaut du bon de commande"

#: common/setting/system.py:815
msgid "Enable creation of default shipment with sales orders"
msgstr "Activer la création d'expédition par défaut avec les bons de commandes"

#: common/setting/system.py:820
msgid "Edit Completed Sales Orders"
msgstr "Modifier les commandes de vente terminées"

#: common/setting/system.py:822
msgid "Allow editing of sales orders after they have been shipped or completed"
msgstr "Autoriser la modification des commandes de vente après avoir été expédiées ou complétées"

#: common/setting/system.py:828
msgid "Mark Shipped Orders as Complete"
msgstr "Marquer les commandes expédiées comme achevées"

#: common/setting/system.py:830
msgid "Sales orders marked as shipped will automatically be completed, bypassing the \"shipped\" status"
msgstr "Les commandes marquées comme expédiées seront automatiquement complétées, en contournant le statut « expédié »"

#: common/setting/system.py:836
msgid "Purchase Order Reference Pattern"
msgstr "Modèle de référence de commande d'achat"

#: common/setting/system.py:838
msgid "Required pattern for generating Purchase Order reference field"
msgstr "Modèle requis pour générer le champ de référence de bon de commande"

#: common/setting/system.py:850
msgid "Edit Completed Purchase Orders"
msgstr "Modifier les bons de commande terminés"

#: common/setting/system.py:852
msgid "Allow editing of purchase orders after they have been shipped or completed"
msgstr "Autoriser la modification des bons de commande après avoir été expédiés ou complétés"

#: common/setting/system.py:858
msgid "Convert Currency"
msgstr ""

#: common/setting/system.py:859
msgid "Convert item value to base currency when receiving stock"
msgstr ""

#: common/setting/system.py:864
msgid "Auto Complete Purchase Orders"
msgstr "Achat automatique des commandes"

#: common/setting/system.py:866
msgid "Automatically mark purchase orders as complete when all line items are received"
msgstr "Marquer automatiquement les bons de commande comme terminés lorsque tous les articles de la ligne sont reçus"

#: common/setting/system.py:873
msgid "Enable password forgot"
msgstr "Activer les mots de passe oubliés"

#: common/setting/system.py:874
msgid "Enable password forgot function on the login pages"
msgstr "Activer la fonction \"Mot de passe oublié\" sur les pages de connexion"

#: common/setting/system.py:879
msgid "Enable registration"
msgstr "Activer les inscriptions"

#: common/setting/system.py:880
msgid "Enable self-registration for users on the login pages"
msgstr "Activer l'auto-inscription pour les utilisateurs sur les pages de connexion"

#: common/setting/system.py:885
msgid "Enable SSO"
msgstr "Activer le SSO"

#: common/setting/system.py:886
msgid "Enable SSO on the login pages"
msgstr "Activer le SSO sur les pages de connexion"

#: common/setting/system.py:891
msgid "Enable SSO registration"
msgstr "Activer l'inscription SSO"

#: common/setting/system.py:893
msgid "Enable self-registration via SSO for users on the login pages"
msgstr "Activer l'auto-inscription via SSO pour les utilisateurs sur les pages de connexion"

#: common/setting/system.py:899
msgid "Enable SSO group sync"
msgstr "Activer la synchronisation du groupe SSO"

#: common/setting/system.py:901
msgid "Enable synchronizing InvenTree groups with groups provided by the IdP"
msgstr ""

#: common/setting/system.py:907
msgid "SSO group key"
msgstr "Clé du groupe SSO"

#: common/setting/system.py:908
msgid "The name of the groups claim attribute provided by the IdP"
msgstr ""

#: common/setting/system.py:913
msgid "SSO group map"
msgstr "Carte de groupe SSO"

#: common/setting/system.py:915
msgid "A mapping from SSO groups to local InvenTree groups. If the local group does not exist, it will be created."
msgstr ""

#: common/setting/system.py:921
msgid "Remove groups outside of SSO"
msgstr ""

#: common/setting/system.py:923
msgid "Whether groups assigned to the user should be removed if they are not backend by the IdP. Disabling this setting might cause security issues"
msgstr ""

#: common/setting/system.py:929
msgid "Email required"
msgstr "Email requis"

#: common/setting/system.py:930
msgid "Require user to supply mail on signup"
msgstr "Exiger que l'utilisateur fournisse un mail lors de l'inscription"

#: common/setting/system.py:935
msgid "Auto-fill SSO users"
msgstr "Saisie automatique des utilisateurs SSO"

#: common/setting/system.py:936
msgid "Automatically fill out user-details from SSO account-data"
msgstr "Remplir automatiquement les détails de l'utilisateur à partir des données de compte SSO"

#: common/setting/system.py:941
msgid "Mail twice"
msgstr "Courriel en double"

#: common/setting/system.py:942
msgid "On signup ask users twice for their mail"
msgstr "Lors de l'inscription, demandez deux fois aux utilisateurs leur mail"

#: common/setting/system.py:947
msgid "Password twice"
msgstr "Mot de passe deux fois"

#: common/setting/system.py:948
msgid "On signup ask users twice for their password"
msgstr "Lors de l'inscription, demandez deux fois aux utilisateurs leur mot de passe"

#: common/setting/system.py:953
msgid "Allowed domains"
msgstr "Domaines autorisés"

#: common/setting/system.py:955
msgid "Restrict signup to certain domains (comma-separated, starting with @)"
msgstr ""

#: common/setting/system.py:961
msgid "Group on signup"
msgstr "Grouper sur inscription"

#: common/setting/system.py:963
msgid "Group to which new users are assigned on registration. If SSO group sync is enabled, this group is only set if no group can be assigned from the IdP."
msgstr ""

#: common/setting/system.py:969
msgid "Enforce MFA"
msgstr "Forcer l'authentification multifacteurs"

#: common/setting/system.py:970
msgid "Users must use multifactor security."
msgstr "Les utilisateurs doivent utiliser l'authentification multifacteurs."

#: common/setting/system.py:975
msgid "Check plugins on startup"
msgstr "Vérifier les plugins au démarrage"

#: common/setting/system.py:977
msgid "Check that all plugins are installed on startup - enable in container environments"
msgstr "Vérifier que tous les plugins sont installés au démarrage - activer dans les environnements conteneurs"

#: common/setting/system.py:984
msgid "Check for plugin updates"
msgstr "Vérifier les mises à jour des plugins"

#: common/setting/system.py:985
msgid "Enable periodic checks for updates to installed plugins"
msgstr "Activer les vérifications périodiques pour les mises à jour des plugins installés"

#: common/setting/system.py:991
msgid "Enable URL integration"
msgstr "Activer l'intégration d'URL"

#: common/setting/system.py:992
msgid "Enable plugins to add URL routes"
msgstr "Autoriser les plugins à ajouter des chemins URL"

#: common/setting/system.py:998
msgid "Enable navigation integration"
msgstr "Activer l'intégration de navigation"

#: common/setting/system.py:999
msgid "Enable plugins to integrate into navigation"
msgstr "Activer les plugins à s'intégrer dans la navigation"

#: common/setting/system.py:1005
msgid "Enable app integration"
msgstr "Activer l'intégration de plugins"

#: common/setting/system.py:1006
msgid "Enable plugins to add apps"
msgstr "Activer l'intégration de plugin pour ajouter des apps"

#: common/setting/system.py:1012
msgid "Enable schedule integration"
msgstr "Activer l'intégration du planning"

#: common/setting/system.py:1013
msgid "Enable plugins to run scheduled tasks"
msgstr "Autoriser les plugins à éxécuter des tâches planifiées"

#: common/setting/system.py:1019
msgid "Enable event integration"
msgstr "Activer l'intégration des évènements"

#: common/setting/system.py:1020
msgid "Enable plugins to respond to internal events"
msgstr "Autoriser les plugins à répondre aux évènements internes"

#: common/setting/system.py:1026
msgid "Enable interface integration"
msgstr ""

#: common/setting/system.py:1027
msgid "Enable plugins to integrate into the user interface"
msgstr ""

#: common/setting/system.py:1033
msgid "Enable project codes"
msgstr ""

#: common/setting/system.py:1034
msgid "Enable project codes for tracking projects"
msgstr ""

#: common/setting/system.py:1039
msgid "Stocktake Functionality"
msgstr "Fonctionnalité d'inventaire"

#: common/setting/system.py:1041
msgid "Enable stocktake functionality for recording stock levels and calculating stock value"
msgstr "Activer la fonctionnalité d'inventaire pour enregistrer les niveaux de stock et le calcul de la valeur du stock"

#: common/setting/system.py:1047
msgid "Exclude External Locations"
msgstr "Exclure les localisations externes"

#: common/setting/system.py:1049
msgid "Exclude stock items in external locations from stocktake calculations"
msgstr ""

#: common/setting/system.py:1055
msgid "Automatic Stocktake Period"
msgstr "Période de l'inventaire automatique"

#: common/setting/system.py:1057
msgid "Number of days between automatic stocktake recording (set to zero to disable)"
msgstr "Nombre de jours entre l'enregistrement automatique des stocks (définir à zéro pour désactiver)"

#: common/setting/system.py:1063
msgid "Report Deletion Interval"
msgstr "Intervalle de suppression des tâches"

#: common/setting/system.py:1065
msgid "Stocktake reports will be deleted after specified number of days"
msgstr "Les rapports d'inventaire seront supprimés après le nombre de jours spécifié"

#: common/setting/system.py:1072
msgid "Display Users full names"
msgstr "Afficher les noms des utilisateurs"

#: common/setting/system.py:1073
msgid "Display Users full names instead of usernames"
msgstr ""

#: common/setting/system.py:1078
msgid "Display User Profiles"
msgstr ""

#: common/setting/system.py:1079
msgid "Display Users Profiles on their profile page"
msgstr ""

#: common/setting/system.py:1084
msgid "Enable Test Station Data"
msgstr "Activer les données de station de test"

#: common/setting/system.py:1085
msgid "Enable test station data collection for test results"
msgstr "Activer la collecte des données de la station de test pour les résultats de test"

#: common/setting/system.py:1090
msgid "Create Template on Upload"
msgstr ""

#: common/setting/system.py:1092
msgid "Create a new test template when uploading test data which does not match an existing template"
msgstr ""

#: common/setting/user.py:23
msgid "Inline label display"
msgstr "Affichage du libellé en ligne"

#: common/setting/user.py:25
msgid "Display PDF labels in the browser, instead of downloading as a file"
msgstr "Afficher les étiquettes PDF dans le navigateur, au lieu de les télécharger en tant que fichier"

#: common/setting/user.py:31
msgid "Default label printer"
msgstr "Imprimante d'étiquettes par défaut"

#: common/setting/user.py:32
msgid "Configure which label printer should be selected by default"
msgstr "Configurer quelle imprimante d'étiquette doit être sélectionnée par défaut"

#: common/setting/user.py:37
msgid "Inline report display"
msgstr "Affichage du rapport en ligne"

#: common/setting/user.py:39
msgid "Display PDF reports in the browser, instead of downloading as a file"
msgstr "Afficher les rapports PDF dans le navigateur, au lieu de les télécharger en tant que fichier"

#: common/setting/user.py:45
msgid "Search Parts"
msgstr "Rechercher de pièces"

#: common/setting/user.py:46
msgid "Display parts in search preview window"
msgstr "Afficher les pièces dans la fenêtre d'aperçu de la recherche"

#: common/setting/user.py:51
msgid "Search Supplier Parts"
msgstr "Recherche du fournisseur de pièces"

#: common/setting/user.py:52
msgid "Display supplier parts in search preview window"
msgstr "Afficher les pièces du fournisseur dans la fenêtre de prévisualisation de la recherche"

#: common/setting/user.py:57
msgid "Search Manufacturer Parts"
msgstr "Rechercher les pièces du fabricant"

#: common/setting/user.py:58
msgid "Display manufacturer parts in search preview window"
msgstr "Afficher les pièces du fabricant dans la fenêtre de prévisualisation de recherche"

#: common/setting/user.py:63
msgid "Hide Inactive Parts"
msgstr "Masquer les pièces inactives"

#: common/setting/user.py:64
msgid "Excluded inactive parts from search preview window"
msgstr "Exclure les pièces inactives de la fenêtre de prévisualisation de recherche"

#: common/setting/user.py:69
msgid "Search Categories"
msgstr "Rechercher des catégories"

#: common/setting/user.py:70
msgid "Display part categories in search preview window"
msgstr "Afficher les catégories de pièces dans la fenêtre de prévisualisation de recherche"

#: common/setting/user.py:75
msgid "Search Stock"
msgstr "Rechercher dans le stock"

#: common/setting/user.py:76
msgid "Display stock items in search preview window"
msgstr "Afficher les pièces en stock dans la fenêtre d'aperçu de la recherche"

#: common/setting/user.py:81
msgid "Hide Unavailable Stock Items"
msgstr "Cacher les pièces indisponibles"

#: common/setting/user.py:83
msgid "Exclude stock items which are not available from the search preview window"
msgstr "Exclure les articles en stock qui ne sont pas disponibles de la fenêtre de prévisualisation de recherche"

#: common/setting/user.py:89
msgid "Search Locations"
msgstr "Chercher des Emplacements"

#: common/setting/user.py:90
msgid "Display stock locations in search preview window"
msgstr "Afficher les emplacements dans la fenêtre d'aperçu de la recherche"

#: common/setting/user.py:95
msgid "Search Companies"
msgstr "Rechercher les entreprises"

#: common/setting/user.py:96
msgid "Display companies in search preview window"
msgstr "Afficher les entreprises dans la fenêtre de prévisualisation de recherche"

#: common/setting/user.py:101
msgid "Search Build Orders"
msgstr "Rechercher les commandes de construction"

#: common/setting/user.py:102
msgid "Display build orders in search preview window"
msgstr "Afficher les commandes de construction dans la fenêtre de prévisualisation de recherche"

#: common/setting/user.py:107
msgid "Search Purchase Orders"
msgstr "Rechercher des bons de commande"

#: common/setting/user.py:108
msgid "Display purchase orders in search preview window"
msgstr "Afficher les bons de commande dans la fenêtre de prévisualisation de recherche"

#: common/setting/user.py:113
msgid "Exclude Inactive Purchase Orders"
msgstr "Exclure les bons de commande inactifs"

#: common/setting/user.py:114
msgid "Exclude inactive purchase orders from search preview window"
msgstr "Exclure les commandes d’achat inactives de la fenêtre de prévisualisation de recherche"

#: common/setting/user.py:119
msgid "Search Sales Orders"
msgstr "Rechercher les bons de commande"

#: common/setting/user.py:120
msgid "Display sales orders in search preview window"
msgstr "Afficher les bons de commande dans la fenêtre de prévisualisation de la recherche"

#: common/setting/user.py:125
msgid "Exclude Inactive Sales Orders"
msgstr "Exclure les bons de commande inactives"

#: common/setting/user.py:126
msgid "Exclude inactive sales orders from search preview window"
msgstr "Exclure les bons de commande inactifs de la fenêtre de prévisualisation de recherche"

#: common/setting/user.py:131
msgid "Search Sales Order Shipments"
msgstr ""

#: common/setting/user.py:132
msgid "Display sales order shipments in search preview window"
msgstr ""

#: common/setting/user.py:137
msgid "Search Return Orders"
msgstr "Rechercher les commandes retournées"

#: common/setting/user.py:138
msgid "Display return orders in search preview window"
msgstr ""

#: common/setting/user.py:143
msgid "Exclude Inactive Return Orders"
msgstr ""

#: common/setting/user.py:144
msgid "Exclude inactive return orders from search preview window"
msgstr ""

#: common/setting/user.py:149
msgid "Search Preview Results"
msgstr "Résultats de l'aperçu de la recherche"

#: common/setting/user.py:151
msgid "Number of results to show in each section of the search preview window"
msgstr "Nombre de résultats à afficher dans chaque section de la fenêtre de prévisualisation de recherche"

#: common/setting/user.py:157
msgid "Regex Search"
msgstr "Recherche Regex"

#: common/setting/user.py:158
msgid "Enable regular expressions in search queries"
msgstr ""

#: common/setting/user.py:163
msgid "Whole Word Search"
msgstr "Recherche de mot complet"

#: common/setting/user.py:164
msgid "Search queries return results for whole word matches"
msgstr ""

#: common/setting/user.py:169
msgid "Search Notes"
msgstr ""

#: common/setting/user.py:171
msgid "Search queries return results for matches from the item's notes"
msgstr ""

#: common/setting/user.py:177
msgid "Show Quantity in Forms"
msgstr "Afficher la quantité dans les formulaires"

#: common/setting/user.py:178
msgid "Display available part quantity in some forms"
msgstr "Afficher la quantité disponible dans certains formulaires"

#: common/setting/user.py:183
msgid "Escape Key Closes Forms"
msgstr "La touche Echap ferme les formulaires"

#: common/setting/user.py:184
msgid "Use the escape key to close modal forms"
msgstr "Utilisez la touche Echap pour fermer les formulaires modaux"

#: common/setting/user.py:189
msgid "Fixed Navbar"
msgstr "Barre de navigation fixe"

#: common/setting/user.py:190
msgid "The navbar position is fixed to the top of the screen"
msgstr "La position de la barre de navigation est fixée en haut de l'écran"

#: common/setting/user.py:195
msgid "Navigation Icons"
msgstr ""

#: common/setting/user.py:196
msgid "Display icons in the navigation bar"
msgstr ""

#: common/setting/user.py:201
msgid "Date Format"
msgstr "Format de date"

#: common/setting/user.py:202
msgid "Preferred format for displaying dates"
msgstr "Format préféré pour l'affichage des dates"

#: common/setting/user.py:215
msgid "Part Scheduling"
msgstr "Planification des pièces"

#: common/setting/user.py:216
msgid "Display part scheduling information"
msgstr "Afficher les informations de planification des pièces"

#: common/setting/user.py:221
msgid "Part Stocktake"
msgstr "Inventaire des pièces"

#: common/setting/user.py:223
msgid "Display part stocktake information (if stocktake functionality is enabled)"
msgstr ""

#: common/setting/user.py:229
msgid "Table String Length"
msgstr "Longueur de la chaîne dans les Tableau"

#: common/setting/user.py:230
msgid "Maximum length limit for strings displayed in table views"
msgstr "Longueur maximale des chaînes affichées dans les tableaux"

#: common/setting/user.py:235
msgid "Show Last Breadcrumb"
msgstr ""

#: common/setting/user.py:236
msgid "Show the current page in breadcrumbs"
msgstr ""

#: common/setting/user.py:241
msgid "Receive error reports"
msgstr "Recevoir des rapports d'erreur"

#: common/setting/user.py:242
msgid "Receive notifications for system errors"
msgstr ""

#: common/setting/user.py:247
msgid "Last used printing machines"
msgstr ""

#: common/setting/user.py:248
msgid "Save the last used printing machines for a user"
msgstr ""

#: common/validators.py:35
msgid "No attachment model type provided"
msgstr ""

#: common/validators.py:41
msgid "Invalid attachment model type"
msgstr ""

#: common/validators.py:82
msgid "Minimum places cannot be greater than maximum places"
msgstr ""

#: common/validators.py:94
msgid "Maximum places cannot be less than minimum places"
msgstr ""

#: common/validators.py:105
msgid "An empty domain is not allowed."
msgstr "Un domaine vide n'est pas autorisé."

#: common/validators.py:107
#, python-brace-format
msgid "Invalid domain name: {domain}"
msgstr "Nom de domaine invalide : {domain}"

#: common/validators.py:123
msgid "Value must be uppercase"
msgstr ""

#: common/validators.py:129
msgid "Value must be a valid variable identifier"
msgstr ""

#: company/api.py:141
msgid "Part is Active"
msgstr "La pièce est active"

#: company/api.py:145
msgid "Manufacturer is Active"
msgstr "Le fabricant est actif"

#: company/api.py:278
msgid "Supplier Part is Active"
msgstr "Le fournisseur de la pièce est active"

#: company/api.py:282
msgid "Internal Part is Active"
msgstr "La pièce interne est active"

#: company/api.py:287
msgid "Supplier is Active"
msgstr "Le fournisseur est actif"

#: company/api.py:299 company/models.py:498 company/serializers.py:444
#: part/serializers.py:577
msgid "Manufacturer"
msgstr "Fabricant"

#: company/api.py:306 company/models.py:97 company/models.py:370
#: stock/api.py:849
msgid "Company"
msgstr "Société"

#: company/api.py:316
msgid "Has Stock"
msgstr ""

#: company/models.py:98
msgid "Companies"
msgstr "Entreprises"

#: company/models.py:114
msgid "Company description"
msgstr "Description de la société"

#: company/models.py:115
msgid "Description of the company"
msgstr "Description de la société"

#: company/models.py:121
msgid "Website"
msgstr "Site web"

#: company/models.py:122
msgid "Company website URL"
msgstr "Site Web de la société"

#: company/models.py:128
msgid "Phone number"
msgstr "Numéro de téléphone"

#: company/models.py:130
msgid "Contact phone number"
msgstr "Numéro de téléphone de contact"

#: company/models.py:137
msgid "Contact email address"
msgstr "Adresse e-mail de contact"

#: company/models.py:142 company/models.py:274 order/models.py:500
#: users/models.py:563
msgid "Contact"
msgstr "Contact"

#: company/models.py:144
msgid "Point of contact"
msgstr "Point de contact"

#: company/models.py:150
msgid "Link to external company information"
msgstr "Lien externe vers les informations de l'entreprise"

#: company/models.py:164
msgid "Is this company active?"
msgstr "Cette entreprise est-elle active ?"

#: company/models.py:169
msgid "Is customer"
msgstr ""

#: company/models.py:170
msgid "Do you sell items to this company?"
msgstr "Vendez-vous des objets à cette entreprise?"

#: company/models.py:175
msgid "Is supplier"
msgstr ""

#: company/models.py:176
msgid "Do you purchase items from this company?"
msgstr "Est-ce que vous achetez des articles à cette entreprise?"

#: company/models.py:181
msgid "Is manufacturer"
msgstr ""

#: company/models.py:182
msgid "Does this company manufacture parts?"
msgstr "Cette entreprise fabrique-t-elle des pièces?"

#: company/models.py:190
msgid "Default currency used for this company"
msgstr "Devise par défaut utilisée pour cette entreprise"

#: company/models.py:313 order/models.py:510
msgid "Address"
msgstr "Adresse"

#: company/models.py:314
msgid "Addresses"
msgstr "Adresses"

#: company/models.py:371
msgid "Select company"
msgstr "Sélectionner une entreprise"

#: company/models.py:376
msgid "Address title"
msgstr "Intitulé de l'adresse"

#: company/models.py:377
msgid "Title describing the address entry"
msgstr "Titre décrivant la saisie de l'adresse"

#: company/models.py:383
msgid "Primary address"
msgstr "Adresse principale"

#: company/models.py:384
msgid "Set as primary address"
msgstr "Sélectionner comme adresse principale"

#: company/models.py:389
msgid "Line 1"
msgstr "Ligne 1"

#: company/models.py:390
msgid "Address line 1"
msgstr "Adresse"

#: company/models.py:396
msgid "Line 2"
msgstr "Ligne 2"

#: company/models.py:397
msgid "Address line 2"
msgstr "Seconde ligne d'adresse"

#: company/models.py:403 company/models.py:404
msgid "Postal code"
msgstr "Code postal"

#: company/models.py:410
msgid "City/Region"
msgstr "Ville / Région"

#: company/models.py:411
msgid "Postal code city/region"
msgstr "Code postal Ville / Région"

#: company/models.py:417
msgid "State/Province"
msgstr "État / Province"

#: company/models.py:418
msgid "State or province"
msgstr "État ou province"

#: company/models.py:424
msgid "Country"
msgstr "Pays"

#: company/models.py:425
msgid "Address country"
msgstr "Pays"

#: company/models.py:431
msgid "Courier shipping notes"
msgstr "Notes du livreur"

#: company/models.py:432
msgid "Notes for shipping courier"
msgstr "Instructions pour le livreur"

#: company/models.py:438
msgid "Internal shipping notes"
msgstr "Notes pour la livraison interne"

#: company/models.py:439
msgid "Shipping notes for internal use"
msgstr "Notes internes pour la livraison"

#: company/models.py:446
msgid "Link to address information (external)"
msgstr "Lien vers les informations de l'adresse (externe)"

#: company/models.py:470 company/models.py:587 company/models.py:809
#: company/serializers.py:458
msgid "Manufacturer Part"
msgstr "Pièces du fabricant"

#: company/models.py:487 company/models.py:777 stock/models.py:948
#: stock/serializers.py:480
msgid "Base Part"
msgstr "Pièce de base"

#: company/models.py:489 company/models.py:779
msgid "Select part"
msgstr ""

#: company/models.py:499
msgid "Select manufacturer"
msgstr "Sélectionner un fabricant"

#: company/models.py:505 company/serializers.py:466 order/serializers.py:665
#: part/serializers.py:587
msgid "MPN"
msgstr ""

#: company/models.py:506 stock/serializers.py:625
msgid "Manufacturer Part Number"
msgstr "Référence du fabricant"

#: company/models.py:513
msgid "URL for external manufacturer part link"
msgstr ""

#: company/models.py:522
msgid "Manufacturer part description"
msgstr ""

#: company/models.py:575
msgid "Manufacturer Part Parameter"
msgstr ""

#: company/models.py:594
msgid "Parameter name"
msgstr "Nom du paramètre"

#: company/models.py:601
msgid "Parameter value"
msgstr "Valeur du paramètre"

#: company/models.py:608 part/models.py:1204 part/models.py:3844
msgid "Units"
msgstr "Unités"

#: company/models.py:609
msgid "Parameter units"
msgstr "Unités du paramètre"

#: company/models.py:717
msgid "Pack units must be compatible with the base part units"
msgstr ""

#: company/models.py:724
msgid "Pack units must be greater than zero"
msgstr ""

#: company/models.py:738
msgid "Linked manufacturer part must reference the same base part"
msgstr "La pièce du fabricant liée doit faire référence à la même pièce de base"

#: company/models.py:787 company/serializers.py:436 company/serializers.py:454
#: order/models.py:626 part/serializers.py:561
#: plugin/builtin/suppliers/digikey.py:25 plugin/builtin/suppliers/lcsc.py:26
#: plugin/builtin/suppliers/mouser.py:24 plugin/builtin/suppliers/tme.py:26
#: stock/api.py:516 templates/email/overdue_purchase_order.html:16
msgid "Supplier"
msgstr "Fournisseur"

#: company/models.py:788
msgid "Select supplier"
msgstr "Sélectionner un fournisseur"

#: company/models.py:794 part/serializers.py:572
msgid "Supplier stock keeping unit"
msgstr "Unité de gestion des stocks des fournisseurs"

#: company/models.py:800
msgid "Is this supplier part active?"
msgstr ""

#: company/models.py:810
msgid "Select manufacturer part"
msgstr "Sélectionner un fabricant"

#: company/models.py:817
msgid "URL for external supplier part link"
msgstr "Lien de la pièce du fournisseur externe"

#: company/models.py:826
msgid "Supplier part description"
msgstr "Description de la pièce du fournisseur"

#: company/models.py:833 order/serializers.py:819 order/serializers.py:2027
#: part/models.py:4356 part/models.py:4697
#: report/templates/report/inventree_bill_of_materials_report.html:140
#: report/templates/report/inventree_purchase_order_report.html:32
#: report/templates/report/inventree_return_order_report.html:27
#: report/templates/report/inventree_sales_order_report.html:32
#: report/templates/report/inventree_stock_location_report.html:105
#: stock/serializers.py:838
msgid "Note"
msgstr "Note"

#: company/models.py:842 part/models.py:2182
msgid "base cost"
msgstr "coût de base"

#: company/models.py:843 part/models.py:2183
msgid "Minimum charge (e.g. stocking fee)"
msgstr "Frais minimums (par exemple frais de stock)"

#: company/models.py:850 order/serializers.py:811 stock/models.py:979
#: stock/serializers.py:1666
msgid "Packaging"
msgstr "Conditionnement"

#: company/models.py:851
msgid "Part packaging"
msgstr "Conditionnement de l'article"

#: company/models.py:856
msgid "Pack Quantity"
msgstr "Nombre de paquet"

#: company/models.py:858
msgid "Total quantity supplied in a single pack. Leave empty for single items."
msgstr ""

#: company/models.py:877 part/models.py:2189
msgid "multiple"
msgstr "plusieurs"

#: company/models.py:878
msgid "Order multiple"
msgstr "Commande multiple"

#: company/models.py:890
msgid "Quantity available from supplier"
msgstr "Quantité disponible auprès du fournisseur"

#: company/models.py:896
msgid "Availability Updated"
msgstr "Disponibilité mise à jour"

#: company/models.py:897
msgid "Date of last update of availability data"
msgstr "Date de dernière mise à jour des données de disponibilité"

#: company/models.py:1025
msgid "Supplier Price Break"
msgstr ""

#: company/serializers.py:166
msgid "Return the string representation for the primary address. This property exists for backwards compatibility."
msgstr ""

#: company/serializers.py:185
msgid "Default currency used for this supplier"
msgstr "Devise par défaut utilisée pour ce fournisseur"

#: company/serializers.py:221
msgid "Company Name"
msgstr ""

#: company/serializers.py:420 part/serializers.py:940 stock/serializers.py:498
msgid "In Stock"
msgstr "En Stock"

#: data_exporter/mixins.py:324 data_exporter/mixins.py:393
msgid "Error occurred during data export"
msgstr ""

#: data_exporter/mixins.py:375
msgid "Data export plugin returned incorrect data format"
msgstr ""

#: data_exporter/serializers.py:74
msgid "Export Format"
msgstr ""

#: data_exporter/serializers.py:75
msgid "Select export file format"
msgstr ""

#: data_exporter/serializers.py:82
msgid "Export Plugin"
msgstr ""

#: data_exporter/serializers.py:83
msgid "Select export plugin"
msgstr ""

#: generic/states/fields.py:146
msgid "Additional status information for this item"
msgstr ""

#: generic/states/fields.py:160
msgid "Custom status key"
msgstr ""

#: generic/states/serializers.py:16 plugin/models.py:45 users/models.py:119
msgid "Key"
msgstr ""

#: generic/states/serializers.py:26
msgid "Custom"
msgstr ""

#: generic/states/serializers.py:37
msgid "Class"
msgstr ""

#: generic/states/serializers.py:40
msgid "Values"
msgstr ""

#: generic/states/tests.py:22 order/status_codes.py:13
msgid "Placed"
msgstr "Placé"

#: generic/states/validators.py:21
msgid "Invalid status code"
msgstr ""

#: importer/models.py:70
msgid "Data File"
msgstr "Fichier de données"

#: importer/models.py:71
msgid "Data file to import"
msgstr ""

#: importer/models.py:80
msgid "Columns"
msgstr ""

#: importer/models.py:87
msgid "Target model type for this import session"
msgstr ""

#: importer/models.py:93
msgid "Import status"
msgstr ""

#: importer/models.py:103
msgid "Field Defaults"
msgstr ""

#: importer/models.py:110
msgid "Field Overrides"
msgstr ""

#: importer/models.py:117
msgid "Field Filters"
msgstr ""

#: importer/models.py:250
msgid "Some required fields have not been mapped"
msgstr ""

#: importer/models.py:407
msgid "Column is already mapped to a database field"
msgstr ""

#: importer/models.py:412
msgid "Field is already mapped to a data column"
msgstr ""

#: importer/models.py:421
msgid "Column mapping must be linked to a valid import session"
msgstr ""

#: importer/models.py:426
msgid "Column does not exist in the data file"
msgstr ""

#: importer/models.py:433
msgid "Field does not exist in the target model"
msgstr ""

#: importer/models.py:437
msgid "Selected field is read-only"
msgstr ""

#: importer/models.py:442 importer/models.py:513
msgid "Import Session"
msgstr ""

#: importer/models.py:446
msgid "Field"
msgstr ""

#: importer/models.py:448
msgid "Column"
msgstr ""

#: importer/models.py:517
msgid "Row Index"
msgstr ""

#: importer/models.py:520
msgid "Original row data"
msgstr ""

#: importer/models.py:525 machine/models.py:110
msgid "Errors"
msgstr "Erreurs"

#: importer/models.py:527 part/api.py:864
msgid "Valid"
msgstr "Valide"

#: importer/operations.py:28 importer/operations.py:49
msgid "Unsupported data file format"
msgstr ""

#: importer/operations.py:40
msgid "Failed to open data file"
msgstr ""

#: importer/operations.py:51
msgid "Invalid data file dimensions"
msgstr ""

#: importer/serializers.py:91
msgid "Invalid field defaults"
msgstr ""

#: importer/serializers.py:104
msgid "Invalid field overrides"
msgstr ""

#: importer/serializers.py:117
msgid "Invalid field filters"
msgstr ""

#: importer/serializers.py:178
msgid "Rows"
msgstr ""

#: importer/serializers.py:179
msgid "List of row IDs to accept"
msgstr ""

#: importer/serializers.py:192
msgid "No rows provided"
msgstr ""

#: importer/serializers.py:196
msgid "Row does not belong to this session"
msgstr ""

#: importer/serializers.py:199
msgid "Row contains invalid data"
msgstr ""

#: importer/serializers.py:202
msgid "Row has already been completed"
msgstr ""

#: importer/status_codes.py:13
msgid "Initializing"
msgstr ""

#: importer/status_codes.py:18
msgid "Mapping Columns"
msgstr ""

#: importer/status_codes.py:21
msgid "Importing Data"
msgstr ""

#: importer/status_codes.py:24
msgid "Processing Data"
msgstr ""

#: importer/validators.py:21
msgid "Data file exceeds maximum size limit"
msgstr ""

#: importer/validators.py:26
msgid "Data file contains no headers"
msgstr ""

#: importer/validators.py:29
msgid "Data file contains too many columns"
msgstr ""

#: importer/validators.py:32
msgid "Data file contains too many rows"
msgstr ""

#: importer/validators.py:53
msgid "Value must be a valid dictionary object"
msgstr ""

#: machine/machine_types/label_printer.py:212
msgid "Copies"
msgstr "Copies"

#: machine/machine_types/label_printer.py:213
msgid "Number of copies to print for each label"
msgstr "Nombre de copies à imprimer pour chaque étiquette"

#: machine/machine_types/label_printer.py:228
msgid "Connected"
msgstr "Connecté"

#: machine/machine_types/label_printer.py:229 order/api.py:1679
msgid "Unknown"
msgstr "Inconnu"

#: machine/machine_types/label_printer.py:230
msgid "Printing"
msgstr "Impression"

#: machine/machine_types/label_printer.py:231
msgid "No media"
msgstr "Aucun média"

#: machine/machine_types/label_printer.py:232
msgid "Paper jam"
msgstr "Bourrage de papier"

#: machine/machine_types/label_printer.py:233
msgid "Disconnected"
msgstr "Déconnecté"

#: machine/machine_types/label_printer.py:240
msgid "Label Printer"
msgstr "Imprimante Etiquette"

#: machine/machine_types/label_printer.py:241
msgid "Directly print labels for various items."
msgstr "Impression directe des étiquettes pour divers articles."

#: machine/machine_types/label_printer.py:247
msgid "Printer Location"
msgstr "Emplacement Imprimante"

#: machine/machine_types/label_printer.py:248
msgid "Scope the printer to a specific location"
msgstr "Porter de l'imprimante sur un emplacement spécifique"

#: machine/models.py:25
msgid "Name of machine"
msgstr "Nom de la machine"

#: machine/models.py:29
msgid "Machine Type"
msgstr "Machine Type"

#: machine/models.py:29
msgid "Type of machine"
msgstr "Type de machine"

#: machine/models.py:34 machine/models.py:146
msgid "Driver"
msgstr "Pilote"

#: machine/models.py:35
msgid "Driver used for the machine"
msgstr "Pilote utilisé pour la machine"

#: machine/models.py:39
msgid "Machines can be disabled"
msgstr "Les machines peuvent être inactivées"

#: machine/models.py:95
msgid "Driver available"
msgstr "Pilote disponible"

#: machine/models.py:100
msgid "No errors"
msgstr "Aucune erreur"

#: machine/models.py:105
msgid "Initialized"
msgstr "Initialisé"

#: machine/models.py:117
msgid "Machine status"
msgstr "Statut de la machine"

#: machine/models.py:145
msgid "Machine"
msgstr "Machine"

#: machine/models.py:151
msgid "Machine Config"
msgstr "Configuration de la machine"

#: machine/models.py:156
msgid "Config type"
msgstr "Type de configuration"

#: order/api.py:118
msgid "Order Reference"
msgstr "Référence de commande"

#: order/api.py:146 order/api.py:1139
msgid "Outstanding"
msgstr ""

#: order/api.py:162
msgid "Has Project Code"
msgstr ""

#: order/api.py:176 order/models.py:475
msgid "Created By"
msgstr "Créé par"

#: order/api.py:180
msgid "Created Before"
msgstr ""

#: order/api.py:184
msgid "Created After"
msgstr ""

#: order/api.py:188
msgid "Has Start Date"
msgstr ""

#: order/api.py:196
msgid "Start Date Before"
msgstr ""

#: order/api.py:200
msgid "Start Date After"
msgstr ""

#: order/api.py:204
msgid "Has Target Date"
msgstr ""

#: order/api.py:212
msgid "Target Date Before"
msgstr ""

#: order/api.py:216
msgid "Target Date After"
msgstr ""

#: order/api.py:267
msgid "Has Pricing"
msgstr "Possède un Tarif"

#: order/api.py:320 order/api.py:774 order/api.py:1375
msgid "Completed Before"
msgstr ""

#: order/api.py:324 order/api.py:778 order/api.py:1379
msgid "Completed After"
msgstr ""

#: order/api.py:491 order/api.py:876 order/api.py:1102 order/models.py:1685
#: order/models.py:1803 order/models.py:1854 order/models.py:2002
#: order/models.py:2168 order/models.py:2690 order/models.py:2756
msgid "Order"
msgstr "Commande"

#: order/api.py:495 order/api.py:914
msgid "Order Complete"
msgstr "Commande Complétée"

#: order/api.py:527 order/api.py:531 order/serializers.py:676
msgid "Internal Part"
msgstr "Pièces Internes"

#: order/api.py:549
msgid "Order Pending"
msgstr "Commande En Attente"

#: order/api.py:899
msgid "Completed"
msgstr "Terminé"

#: order/api.py:1155
msgid "Has Shipment"
msgstr ""

#: order/api.py:1673 order/models.py:539 order/models.py:1686
#: order/models.py:1804
#: report/templates/report/inventree_purchase_order_report.html:14
#: stock/serializers.py:128 templates/email/overdue_purchase_order.html:15
msgid "Purchase Order"
msgstr "Commande d’achat"

#: order/api.py:1675 order/models.py:1080 order/models.py:1855
#: order/models.py:2003 order/models.py:2169
#: report/templates/report/inventree_build_order_report.html:135
#: report/templates/report/inventree_sales_order_report.html:14
#: report/templates/report/inventree_sales_order_shipment_report.html:15
#: templates/email/overdue_sales_order.html:15
msgid "Sales Order"
msgstr "Commandes"

#: order/api.py:1677 order/models.py:2340 order/models.py:2691
#: order/models.py:2757
#: report/templates/report/inventree_return_order_report.html:13
#: templates/email/overdue_return_order.html:15
msgid "Return Order"
msgstr "Retour de commande"

#: order/models.py:89
#: report/templates/report/inventree_purchase_order_report.html:31
#: report/templates/report/inventree_sales_order_report.html:31
msgid "Total Price"
msgstr "Prix Total"

#: order/models.py:90
msgid "Total price for this order"
msgstr "Prix total pour cette commande"

#: order/models.py:95 order/serializers.py:75
msgid "Order Currency"
msgstr "Devise de la commande"

#: order/models.py:98 order/serializers.py:76
msgid "Currency for this order (leave blank to use company default)"
msgstr ""

#: order/models.py:323
msgid "This order is locked and cannot be modified"
msgstr ""

#: order/models.py:370
msgid "Contact does not match selected company"
msgstr ""

#: order/models.py:377
msgid "Start date must be before target date"
msgstr ""

#: order/models.py:430
msgid "Order description (optional)"
msgstr "Description de la commande (facultatif)"

#: order/models.py:439
msgid "Select project code for this order"
msgstr ""

#: order/models.py:445 order/models.py:1586 order/models.py:2057
msgid "Link to external page"
msgstr "Lien vers une page externe"

#: order/models.py:452
msgid "Start date"
msgstr ""

#: order/models.py:453
msgid "Scheduled start date for this order"
msgstr ""

#: order/models.py:459 order/models.py:1593 order/serializers.py:269
#: report/templates/report/inventree_build_order_report.html:125
msgid "Target Date"
msgstr "Date Cible"

#: order/models.py:461
msgid "Expected date for order delivery. Order will be overdue after this date."
msgstr "Date prévue pour la livraison de la commande. La commande sera en retard après cette date."

#: order/models.py:481
msgid "Issue Date"
msgstr "Date d'émission"

#: order/models.py:482
msgid "Date order was issued"
msgstr "Date d'émission de la commande"

#: order/models.py:490
msgid "User or group responsible for this order"
msgstr "Utilisateur ou groupe responsable de cette commande"

#: order/models.py:501
msgid "Point of contact for this order"
msgstr ""

#: order/models.py:511
msgid "Company address for this order"
msgstr "Adresse de l'entreprise pour cette commande"

#: order/models.py:602 order/models.py:1141
msgid "Order reference"
msgstr "Référence de la commande"

#: order/models.py:611 order/models.py:1165 order/models.py:2428
#: stock/serializers.py:612 stock/serializers.py:1010 users/models.py:544
msgid "Status"
msgstr "État"

#: order/models.py:612
msgid "Purchase order status"
msgstr "Statut de la commande d'achat"

#: order/models.py:627
msgid "Company from which the items are being ordered"
msgstr "Société de laquelle les articles sont commandés"

#: order/models.py:638
msgid "Supplier Reference"
msgstr "Référence du fournisseur"

#: order/models.py:639
msgid "Supplier order reference code"
msgstr "Code de référence de la commande fournisseur"

#: order/models.py:648
msgid "received by"
msgstr "reçu par"

#: order/models.py:655 order/models.py:2443
msgid "Date order was completed"
msgstr "Date à laquelle la commande a été complété"

#: order/models.py:664 order/models.py:1733
msgid "Destination"
msgstr "Destination"

#: order/models.py:665 order/models.py:1737
msgid "Destination for received items"
msgstr ""

#: order/models.py:711
msgid "Part supplier must match PO supplier"
msgstr ""

#: order/models.py:971
msgid "Quantity must be a positive number"
msgstr "La quantité doit être un nombre positif"

#: order/models.py:1152 order/models.py:2415 stock/models.py:1001
#: stock/models.py:1002 stock/serializers.py:1405
#: templates/email/overdue_return_order.html:16
#: templates/email/overdue_sales_order.html:16
msgid "Customer"
msgstr "Client"

#: order/models.py:1153
msgid "Company to which the items are being sold"
msgstr "Société à laquelle les articles sont vendus"

#: order/models.py:1166
msgid "Sales order status"
msgstr ""

#: order/models.py:1177 order/models.py:2435
msgid "Customer Reference "
msgstr "Référence client "

#: order/models.py:1178 order/models.py:2436
msgid "Customer order reference code"
msgstr ""

#: order/models.py:1182 order/models.py:2009
msgid "Shipment Date"
msgstr "Nom de l’expédition"

#: order/models.py:1191
msgid "shipped by"
msgstr "expédié par"

#: order/models.py:1230
msgid "Order is already complete"
msgstr ""

#: order/models.py:1233
msgid "Order is already cancelled"
msgstr ""

#: order/models.py:1237
msgid "Only an open order can be marked as complete"
msgstr ""

#: order/models.py:1241
msgid "Order cannot be completed as there are incomplete shipments"
msgstr "La commande ne peut pas être terminée car il y a des envois incomplets"

#: order/models.py:1246
msgid "Order cannot be completed as there are incomplete allocations"
msgstr ""

#: order/models.py:1251
msgid "Order cannot be completed as there are incomplete line items"
msgstr ""

#: order/models.py:1535 order/models.py:1548
msgid "The order is locked and cannot be modified"
msgstr ""

#: order/models.py:1556
msgid "Item quantity"
msgstr "Nombre d'élement"

#: order/models.py:1573
msgid "Line item reference"
msgstr ""

#: order/models.py:1580
msgid "Line item notes"
msgstr ""

#: order/models.py:1595
msgid "Target date for this line item (leave blank to use the target date from the order)"
msgstr ""

#: order/models.py:1616
msgid "Line item description (optional)"
msgstr ""

#: order/models.py:1623
msgid "Additional context for this line"
msgstr ""

#: order/models.py:1633
msgid "Unit price"
msgstr "Prix unitaire"

#: order/models.py:1647
msgid "Purchase Order Line Item"
msgstr ""

#: order/models.py:1671
msgid "Supplier part must match supplier"
msgstr ""

#: order/models.py:1705
msgid "Supplier part"
msgstr "Pièce fournisseur"

#: order/models.py:1712
msgid "Received"
msgstr "Reçu"

#: order/models.py:1713
msgid "Number of items received"
msgstr "Nombre d'éléments reçus"

#: order/models.py:1721 stock/models.py:1124 stock/serializers.py:679
msgid "Purchase Price"
msgstr "Prix d'achat"

#: order/models.py:1722
msgid "Unit purchase price"
msgstr "Prix d'achat unitaire"

#: order/models.py:1792
msgid "Purchase Order Extra Line"
msgstr ""

#: order/models.py:1821
msgid "Sales Order Line Item"
msgstr ""

#: order/models.py:1842
msgid "Virtual part cannot be assigned to a sales order"
msgstr "La pièce virtuelle ne peut pas être affectée à une commande"

#: order/models.py:1847
msgid "Only salable parts can be assigned to a sales order"
msgstr "Seules les pièces vendues peuvent être attribuées à une commande"

#: order/models.py:1873
msgid "Sale Price"
msgstr "Prix de vente"

#: order/models.py:1874
msgid "Unit sale price"
msgstr "Prix de vente unitaire"

#: order/models.py:1883 order/status_codes.py:50
msgid "Shipped"
msgstr "Expédié"

#: order/models.py:1884
msgid "Shipped quantity"
msgstr "Quantité expédiée"

#: order/models.py:1978
msgid "Sales Order Shipment"
msgstr ""

#: order/models.py:2010
msgid "Date of shipment"
msgstr "Date d'expédition"

#: order/models.py:2016
msgid "Delivery Date"
msgstr "Date de Livraison"

#: order/models.py:2017
msgid "Date of delivery of shipment"
msgstr ""

#: order/models.py:2025
msgid "Checked By"
msgstr "Vérifié par"

#: order/models.py:2026
msgid "User who checked this shipment"
msgstr "Utilisateur qui a vérifié cet envoi"

#: order/models.py:2033 order/models.py:2265 order/serializers.py:1685
#: order/serializers.py:1809
#: report/templates/report/inventree_sales_order_shipment_report.html:14
msgid "Shipment"
msgstr "Envoi"

#: order/models.py:2034
msgid "Shipment number"
msgstr "Numéro d'expédition"

#: order/models.py:2042
msgid "Tracking Number"
msgstr "N° de suivi"

#: order/models.py:2043
msgid "Shipment tracking information"
msgstr "Information de suivi des colis"

#: order/models.py:2050
msgid "Invoice Number"
msgstr "N° de facture"

#: order/models.py:2051
msgid "Reference number for associated invoice"
msgstr "Numéro de référence de la facture associée"

#: order/models.py:2074
msgid "Shipment has already been sent"
msgstr "Le colis a déjà été envoyé"

#: order/models.py:2077
msgid "Shipment has no allocated stock items"
msgstr "L'expédition n'a pas d'articles en stock alloués"

#: order/models.py:2157
msgid "Sales Order Extra Line"
msgstr ""

#: order/models.py:2186
msgid "Sales Order Allocation"
msgstr ""

#: order/models.py:2209 order/models.py:2211
msgid "Stock item has not been assigned"
msgstr "L'article de stock n'a pas été assigné"

#: order/models.py:2218
msgid "Cannot allocate stock item to a line with a different part"
msgstr "Impossible d'allouer l'article en stock à une ligne avec une autre pièce"

#: order/models.py:2221
msgid "Cannot allocate stock to a line without a part"
msgstr "Impossible d'allouer le stock à une ligne sans pièce"

#: order/models.py:2224
msgid "Allocation quantity cannot exceed stock quantity"
msgstr "La quantité d'allocation ne peut pas excéder la quantité en stock"

#: order/models.py:2243 order/serializers.py:1555
msgid "Quantity must be 1 for serialized stock item"
msgstr ""

#: order/models.py:2246
msgid "Sales order does not match shipment"
msgstr ""

#: order/models.py:2247 plugin/base/barcodes/api.py:642
msgid "Shipment does not match sales order"
msgstr ""

#: order/models.py:2255
msgid "Line"
msgstr "Ligne"

#: order/models.py:2266
msgid "Sales order shipment reference"
msgstr ""

#: order/models.py:2279 order/models.py:2698
msgid "Item"
msgstr "Article"

#: order/models.py:2280
msgid "Select stock item to allocate"
msgstr ""

#: order/models.py:2289
msgid "Enter stock allocation quantity"
msgstr ""

#: order/models.py:2404
msgid "Return Order reference"
msgstr ""

#: order/models.py:2416
msgid "Company from which items are being returned"
msgstr ""

#: order/models.py:2429
msgid "Return order status"
msgstr "Statut du retour de commande"

#: order/models.py:2656
msgid "Return Order Line Item"
msgstr ""

#: order/models.py:2669
msgid "Stock item must be specified"
msgstr ""

#: order/models.py:2673
msgid "Return quantity exceeds stock quantity"
msgstr ""

#: order/models.py:2678
msgid "Return quantity must be greater than zero"
msgstr ""

#: order/models.py:2683
msgid "Invalid quantity for serialized stock item"
msgstr ""

#: order/models.py:2699
msgid "Select item to return from customer"
msgstr ""

#: order/models.py:2714
msgid "Received Date"
msgstr ""

#: order/models.py:2715
msgid "The date this this return item was received"
msgstr ""

#: order/models.py:2727
msgid "Outcome"
msgstr ""

#: order/models.py:2728
msgid "Outcome for this line item"
msgstr ""

#: order/models.py:2735
msgid "Cost associated with return or repair for this line item"
msgstr ""

#: order/models.py:2745
msgid "Return Order Extra Line"
msgstr ""

#: order/serializers.py:89
msgid "Order ID"
msgstr "ID de commande"

#: order/serializers.py:89
msgid "ID of the order to duplicate"
msgstr ""

#: order/serializers.py:95
msgid "Copy Lines"
msgstr "Copier des lignes"

#: order/serializers.py:96
msgid "Copy line items from the original order"
msgstr ""

#: order/serializers.py:102
msgid "Copy Extra Lines"
msgstr "Copier les lignes supplémentaires"

#: order/serializers.py:103
msgid "Copy extra line items from the original order"
msgstr ""

#: order/serializers.py:116
#: report/templates/report/inventree_purchase_order_report.html:22
#: report/templates/report/inventree_return_order_report.html:19
#: report/templates/report/inventree_sales_order_report.html:22
msgid "Line Items"
msgstr ""

#: order/serializers.py:121
msgid "Completed Lines"
msgstr ""

#: order/serializers.py:172
msgid "Duplicate Order"
msgstr ""

#: order/serializers.py:173
msgid "Specify options for duplicating this order"
msgstr ""

#: order/serializers.py:249
msgid "Invalid order ID"
msgstr ""

#: order/serializers.py:388
msgid "Supplier Name"
msgstr "Nom du fournisseur"

#: order/serializers.py:430
msgid "Order cannot be cancelled"
msgstr "La commande ne peut pas être annulée"

#: order/serializers.py:445 order/serializers.py:1576
msgid "Allow order to be closed with incomplete line items"
msgstr ""

#: order/serializers.py:455 order/serializers.py:1586
msgid "Order has incomplete line items"
msgstr ""

#: order/serializers.py:608
msgid "Order is not open"
msgstr "La commande n'est pas ouverte"

#: order/serializers.py:629
msgid "Auto Pricing"
msgstr ""

#: order/serializers.py:631
msgid "Automatically calculate purchase price based on supplier part data"
msgstr ""

#: order/serializers.py:641
msgid "Purchase price currency"
msgstr "Devise du prix d'achat"

#: order/serializers.py:649
msgid "Merge Items"
msgstr ""

#: order/serializers.py:651
msgid "Merge items with the same part, destination and target date into one line item"
msgstr ""

#: order/serializers.py:658 part/serializers.py:571
msgid "SKU"
msgstr ""

#: order/serializers.py:672 part/models.py:1089 part/serializers.py:412
msgid "Internal Part Number"
msgstr ""

#: order/serializers.py:680
msgid "Internal Part Name"
msgstr ""

#: order/serializers.py:696
msgid "Supplier part must be specified"
msgstr ""

#: order/serializers.py:699
msgid "Purchase order must be specified"
msgstr ""

#: order/serializers.py:707
msgid "Supplier must match purchase order"
msgstr ""

#: order/serializers.py:708
msgid "Purchase order must match supplier"
msgstr ""

#: order/serializers.py:754 order/serializers.py:1656
msgid "Line Item"
msgstr ""

#: order/serializers.py:760
msgid "Line item does not match purchase order"
msgstr ""

#: order/serializers.py:770 order/serializers.py:917 order/serializers.py:2023
msgid "Select destination location for received items"
msgstr ""

#: order/serializers.py:786
msgid "Enter batch code for incoming stock items"
msgstr ""

#: order/serializers.py:793 stock/models.py:1083 users/models.py:143
msgid "Expiry Date"
msgstr "Date d'expiration"

#: order/serializers.py:794
msgid "Enter expiry date for incoming stock items"
msgstr ""

#: order/serializers.py:802
msgid "Enter serial numbers for incoming stock items"
msgstr "Entrez les numéros de série pour les articles de stock entrants"

#: order/serializers.py:812
msgid "Override packaging information for incoming stock items"
msgstr ""

#: order/serializers.py:820 order/serializers.py:2028
msgid "Additional note for incoming stock items"
msgstr ""

#: order/serializers.py:827
msgid "Barcode"
msgstr "Code-barres"

#: order/serializers.py:828
msgid "Scanned barcode"
msgstr ""

#: order/serializers.py:844
msgid "Barcode is already in use"
msgstr "Le code-barres est déjà utilisé"

#: order/serializers.py:867
msgid "An integer quantity must be provided for trackable parts"
msgstr "Une quantité entière doit être fournie pour les pièces tracables"

#: order/serializers.py:934 order/serializers.py:2047
msgid "Line items must be provided"
msgstr ""

#: order/serializers.py:950
msgid "Destination location must be specified"
msgstr ""

#: order/serializers.py:961
msgid "Supplied barcode values must be unique"
msgstr ""

#: order/serializers.py:1092
msgid "Shipments"
msgstr ""

#: order/serializers.py:1096
msgid "Completed Shipments"
msgstr ""

#: order/serializers.py:1283
msgid "Sale price currency"
msgstr "Devise du prix de vente"

#: order/serializers.py:1331
msgid "Allocated Items"
msgstr ""

#: order/serializers.py:1458
msgid "No shipment details provided"
msgstr ""

#: order/serializers.py:1519 order/serializers.py:1665
msgid "Line item is not associated with this order"
msgstr ""

#: order/serializers.py:1538
msgid "Quantity must be positive"
msgstr ""

#: order/serializers.py:1675
msgid "Enter serial numbers to allocate"
msgstr "Entrez les numéros de série à allouer"

#: order/serializers.py:1697 order/serializers.py:1817
msgid "Shipment has already been shipped"
msgstr ""

#: order/serializers.py:1700 order/serializers.py:1820
msgid "Shipment is not associated with this order"
msgstr ""

#: order/serializers.py:1755
msgid "No match found for the following serial numbers"
msgstr "Aucune correspondance trouvée pour les numéros de série suivants"

#: order/serializers.py:1762
msgid "The following serial numbers are unavailable"
msgstr "Les numéros de série suivants sont indisponibles"

#: order/serializers.py:1989
msgid "Return order line item"
msgstr ""

#: order/serializers.py:1999
msgid "Line item does not match return order"
msgstr ""

#: order/serializers.py:2002
msgid "Line item has already been received"
msgstr ""

#: order/serializers.py:2039
msgid "Items can only be received against orders which are in progress"
msgstr ""

#: order/serializers.py:2131
msgid "Quantity to return"
msgstr ""

#: order/serializers.py:2143
msgid "Line price currency"
msgstr ""

#: order/status_codes.py:17 order/status_codes.py:54 stock/status_codes.py:16
msgid "Lost"
msgstr "Perdu"

#: order/status_codes.py:18 order/status_codes.py:55 stock/status_codes.py:24
msgid "Returned"
msgstr "Retourné"

#: order/status_codes.py:47 order/status_codes.py:79
msgid "In Progress"
msgstr "En Cours"

#: order/status_codes.py:105
msgid "Return"
msgstr "Retour"

#: order/status_codes.py:108
msgid "Repair"
msgstr "Réparer"

#: order/status_codes.py:111
msgid "Replace"
msgstr "Remplacer"

#: order/status_codes.py:114
msgid "Refund"
msgstr "Remboursement"

#: order/status_codes.py:117
msgid "Reject"
msgstr "Refuser"

#: order/tasks.py:44
msgid "Overdue Purchase Order"
msgstr ""

#: order/tasks.py:49
#, python-brace-format
msgid "Purchase order {po} is now overdue"
msgstr ""

#: order/tasks.py:112
msgid "Overdue Sales Order"
msgstr ""

#: order/tasks.py:117
#, python-brace-format
msgid "Sales order {so} is now overdue"
msgstr ""

#: order/tasks.py:177
msgid "Overdue Return Order"
msgstr ""

#: order/tasks.py:182
#, python-brace-format
msgid "Return order {ro} is now overdue"
msgstr ""

#: part/api.py:115
msgid "Starred"
msgstr ""

#: part/api.py:117
msgid "Filter by starred categories"
msgstr ""

#: part/api.py:134 stock/api.py:255
msgid "Depth"
msgstr "Profondeur"

#: part/api.py:134
msgid "Filter by category depth"
msgstr ""

#: part/api.py:152 stock/api.py:273
msgid "Top Level"
msgstr "Premier niveau"

#: part/api.py:154
msgid "Filter by top-level categories"
msgstr ""

#: part/api.py:167 stock/api.py:288
msgid "Cascade"
msgstr "Cascade"

#: part/api.py:169
msgid "Include sub-categories in filtered results"
msgstr ""

#: part/api.py:189
msgid "Parent"
msgstr ""

#: part/api.py:191
msgid "Filter by parent category"
msgstr ""

#: part/api.py:226
msgid "Exclude sub-categories under the specified category"
msgstr ""

#: part/api.py:438
msgid "Has Results"
msgstr ""

#: part/api.py:611
msgid "Incoming Purchase Order"
msgstr ""

#: part/api.py:625
msgid "Outgoing Sales Order"
msgstr ""

#: part/api.py:637
msgid "Stock produced by Build Order"
msgstr ""

#: part/api.py:719
msgid "Stock required for Build Order"
msgstr ""

#: part/api.py:865
msgid "Validate entire Bill of Materials"
msgstr ""

#: part/api.py:871
msgid "This option must be selected"
msgstr ""

#: part/api.py:907
msgid "Is Variant"
msgstr ""

#: part/api.py:915
msgid "Is Revision"
msgstr ""

#: part/api.py:925
msgid "Has Revisions"
msgstr ""

#: part/api.py:1116
msgid "BOM Valid"
msgstr ""

#: part/api.py:1775
msgid "Assembly part is testable"
msgstr ""

#: part/api.py:1784
msgid "Component part is testable"
msgstr ""

#: part/api.py:1835
msgid "Uses"
msgstr "Utilise"

#: part/models.py:90 part/models.py:4137
#: templates/email/part_event_notification.html:16
msgid "Part Category"
msgstr "Catégorie de composant"

#: part/models.py:91 users/ruleset.py:32
msgid "Part Categories"
msgstr "Catégories de composants"

#: part/models.py:109 part/models.py:1134
msgid "Default Location"
msgstr "Emplacement par défaut"

#: part/models.py:110
msgid "Default location for parts in this category"
msgstr ""

#: part/models.py:115 stock/models.py:212
msgid "Structural"
msgstr "Structurel"

#: part/models.py:117
msgid "Parts may not be directly assigned to a structural category, but may be assigned to child categories."
msgstr ""

#: part/models.py:126
msgid "Default keywords"
msgstr "Mots-clés par défaut"

#: part/models.py:127
msgid "Default keywords for parts in this category"
msgstr ""

#: part/models.py:134 stock/models.py:96 stock/models.py:194
msgid "Icon"
msgstr "Icône"

#: part/models.py:135 part/serializers.py:152 part/serializers.py:171
#: stock/models.py:195
msgid "Icon (optional)"
msgstr "Icône (facultatif)"

#: part/models.py:179
msgid "You cannot make this part category structural because some parts are already assigned to it!"
msgstr ""

#: part/models.py:458 part/serializers.py:130 part/serializers.py:343
#: users/ruleset.py:33
msgid "Parts"
msgstr "Pièces"

#: part/models.py:510
msgid "Cannot delete this part as it is locked"
msgstr ""

#: part/models.py:513
msgid "Cannot delete this part as it is still active"
msgstr ""

#: part/models.py:518
msgid "Cannot delete this part as it is used in an assembly"
msgstr ""

#: part/models.py:556
msgid "Invalid choice for parent part"
msgstr ""

#: part/models.py:604 part/models.py:611
#, python-brace-format
msgid "Part '{self}' cannot be used in BOM for '{parent}' (recursive)"
msgstr ""

#: part/models.py:623
#, python-brace-format
msgid "Part '{parent}' is  used in BOM for '{self}' (recursive)"
msgstr ""

#: part/models.py:690
#, python-brace-format
msgid "IPN must match regex pattern {pattern}"
msgstr "L'IPN doit correspondre au modèle de regex {pattern}"

#: part/models.py:698
msgid "Part cannot be a revision of itself"
msgstr ""

#: part/models.py:705
msgid "Cannot make a revision of a part which is already a revision"
msgstr ""

#: part/models.py:712
msgid "Revision code must be specified"
msgstr ""

#: part/models.py:719
msgid "Revisions are only allowed for assembly parts"
msgstr ""

#: part/models.py:726
msgid "Cannot make a revision of a template part"
msgstr ""

#: part/models.py:732
msgid "Parent part must point to the same template"
msgstr ""

#: part/models.py:829
msgid "Stock item with this serial number already exists"
msgstr "Il existe déjà un article en stock avec ce numéro de série"

#: part/models.py:971
msgid "Duplicate IPN not allowed in part settings"
msgstr "IPN dupliqué non autorisé dans les paramètres de la pièce"

#: part/models.py:983
msgid "Duplicate part revision already exists."
msgstr ""

#: part/models.py:992
msgid "Part with this Name, IPN and Revision already exists."
msgstr "Une pièce avec ce nom, IPN et révision existe déjà."

#: part/models.py:1007
msgid "Parts cannot be assigned to structural part categories!"
msgstr ""

#: part/models.py:1039
msgid "Part name"
msgstr "Nom de l'article"

#: part/models.py:1044
msgid "Is Template"
msgstr ""

#: part/models.py:1045
msgid "Is this part a template part?"
msgstr ""

#: part/models.py:1055
msgid "Is this part a variant of another part?"
msgstr ""

#: part/models.py:1056
msgid "Variant Of"
msgstr "Variante de"

#: part/models.py:1063
msgid "Part description (optional)"
msgstr ""

#: part/models.py:1070
msgid "Keywords"
msgstr "Mots-clés"

#: part/models.py:1071
msgid "Part keywords to improve visibility in search results"
msgstr ""

#: part/models.py:1081
msgid "Part category"
msgstr "Catégorie de la pièce"

#: part/models.py:1088 part/serializers.py:926
#: report/templates/report/inventree_stock_location_report.html:103
msgid "IPN"
msgstr "IPN"

#: part/models.py:1096
msgid "Part revision or version number"
msgstr ""

#: part/models.py:1097 report/models.py:215
msgid "Revision"
msgstr "Révision"

#: part/models.py:1106
msgid "Is this part a revision of another part?"
msgstr ""

#: part/models.py:1107
msgid "Revision Of"
msgstr ""

#: part/models.py:1132
msgid "Where is this item normally stored?"
msgstr ""

#: part/models.py:1178
msgid "Default Supplier"
msgstr ""

#: part/models.py:1179
msgid "Default supplier part"
msgstr ""

#: part/models.py:1186
msgid "Default Expiry"
msgstr ""

#: part/models.py:1187
msgid "Expiry time (in days) for stock items of this part"
msgstr ""

#: part/models.py:1195 part/serializers.py:977
msgid "Minimum Stock"
msgstr "Stock Minimum"

#: part/models.py:1196
msgid "Minimum allowed stock level"
msgstr ""

#: part/models.py:1205
msgid "Units of measure for this part"
msgstr ""

#: part/models.py:1212
msgid "Can this part be built from other parts?"
msgstr ""

#: part/models.py:1218
msgid "Can this part be used to build other parts?"
msgstr ""

#: part/models.py:1224
msgid "Does this part have tracking for unique items?"
msgstr ""

#: part/models.py:1230
msgid "Can this part have test results recorded against it?"
msgstr ""

#: part/models.py:1236
msgid "Can this part be purchased from external suppliers?"
msgstr ""

#: part/models.py:1242
msgid "Can this part be sold to customers?"
msgstr ""

#: part/models.py:1246
msgid "Is this part active?"
msgstr "Est-ce que cette pièce est active ?"

#: part/models.py:1252
msgid "Locked parts cannot be edited"
msgstr ""

#: part/models.py:1258
msgid "Is this a virtual part, such as a software product or license?"
msgstr ""

#: part/models.py:1264
msgid "BOM checksum"
msgstr ""

#: part/models.py:1265
msgid "Stored BOM checksum"
msgstr ""

#: part/models.py:1273
msgid "BOM checked by"
msgstr ""

#: part/models.py:1278
msgid "BOM checked date"
msgstr ""

#: part/models.py:1294
msgid "Creation User"
msgstr "Création Utilisateur"

#: part/models.py:1304
msgid "Owner responsible for this part"
msgstr "Propriétaire responsable de cette pièce"

#: part/models.py:1309
msgid "Last Stocktake"
msgstr ""

#: part/models.py:2190
msgid "Sell multiple"
msgstr "Ventes multiples"

#: part/models.py:3185
msgid "Currency used to cache pricing calculations"
msgstr ""

#: part/models.py:3201
msgid "Minimum BOM Cost"
msgstr ""

#: part/models.py:3202
msgid "Minimum cost of component parts"
msgstr ""

#: part/models.py:3208
msgid "Maximum BOM Cost"
msgstr ""

#: part/models.py:3209
msgid "Maximum cost of component parts"
msgstr ""

#: part/models.py:3215
msgid "Minimum Purchase Cost"
msgstr ""

#: part/models.py:3216
msgid "Minimum historical purchase cost"
msgstr ""

#: part/models.py:3222
msgid "Maximum Purchase Cost"
msgstr ""

#: part/models.py:3223
msgid "Maximum historical purchase cost"
msgstr ""

#: part/models.py:3229
msgid "Minimum Internal Price"
msgstr ""

#: part/models.py:3230
msgid "Minimum cost based on internal price breaks"
msgstr ""

#: part/models.py:3236
msgid "Maximum Internal Price"
msgstr ""

#: part/models.py:3237
msgid "Maximum cost based on internal price breaks"
msgstr ""

#: part/models.py:3243
msgid "Minimum Supplier Price"
msgstr ""

#: part/models.py:3244
msgid "Minimum price of part from external suppliers"
msgstr ""

#: part/models.py:3250
msgid "Maximum Supplier Price"
msgstr ""

#: part/models.py:3251
msgid "Maximum price of part from external suppliers"
msgstr ""

#: part/models.py:3257
msgid "Minimum Variant Cost"
msgstr ""

#: part/models.py:3258
msgid "Calculated minimum cost of variant parts"
msgstr ""

#: part/models.py:3264
msgid "Maximum Variant Cost"
msgstr ""

#: part/models.py:3265
msgid "Calculated maximum cost of variant parts"
msgstr ""

#: part/models.py:3271 part/models.py:3285
msgid "Minimum Cost"
msgstr "Coût minimal"

#: part/models.py:3272
msgid "Override minimum cost"
msgstr ""

#: part/models.py:3278 part/models.py:3292
msgid "Maximum Cost"
msgstr "Coût maximal"

#: part/models.py:3279
msgid "Override maximum cost"
msgstr ""

#: part/models.py:3286
msgid "Calculated overall minimum cost"
msgstr ""

#: part/models.py:3293
msgid "Calculated overall maximum cost"
msgstr ""

#: part/models.py:3299
msgid "Minimum Sale Price"
msgstr ""

#: part/models.py:3300
msgid "Minimum sale price based on price breaks"
msgstr ""

#: part/models.py:3306
msgid "Maximum Sale Price"
msgstr ""

#: part/models.py:3307
msgid "Maximum sale price based on price breaks"
msgstr ""

#: part/models.py:3313
msgid "Minimum Sale Cost"
msgstr "Coût minimum de vente"

#: part/models.py:3314
msgid "Minimum historical sale price"
msgstr ""

#: part/models.py:3320
msgid "Maximum Sale Cost"
msgstr ""

#: part/models.py:3321
msgid "Maximum historical sale price"
msgstr ""

#: part/models.py:3340
msgid "Part for stocktake"
msgstr ""

#: part/models.py:3345
msgid "Item Count"
msgstr ""

#: part/models.py:3346
msgid "Number of individual stock entries at time of stocktake"
msgstr ""

#: part/models.py:3354
msgid "Total available stock at time of stocktake"
msgstr ""

#: part/models.py:3358 part/models.py:3441 part/serializers.py:273
#: report/templates/report/inventree_test_report.html:106
msgid "Date"
msgstr "Date"

#: part/models.py:3359
msgid "Date stocktake was performed"
msgstr ""

#: part/models.py:3367
msgid "Additional notes"
msgstr "Notes additionnelles"

#: part/models.py:3377
msgid "User who performed this stocktake"
msgstr ""

#: part/models.py:3383
msgid "Minimum Stock Cost"
msgstr ""

#: part/models.py:3384
msgid "Estimated minimum cost of stock on hand"
msgstr ""

#: part/models.py:3390
msgid "Maximum Stock Cost"
msgstr ""

#: part/models.py:3391
msgid "Estimated maximum cost of stock on hand"
msgstr ""

#: part/models.py:3447
msgid "Report"
msgstr ""

#: part/models.py:3448
msgid "Stocktake report file (generated internally)"
msgstr ""

#: part/models.py:3453
msgid "Part Count"
msgstr ""

#: part/models.py:3454
msgid "Number of parts covered by stocktake"
msgstr ""

#: part/models.py:3464
msgid "User who requested this stocktake report"
msgstr ""

#: part/models.py:3474
msgid "Part Sale Price Break"
msgstr ""

#: part/models.py:3586
msgid "Part Test Template"
msgstr ""

#: part/models.py:3612
msgid "Invalid template name - must include at least one alphanumeric character"
msgstr ""

#: part/models.py:3633 part/models.py:3803
msgid "Choices must be unique"
msgstr ""

#: part/models.py:3644
msgid "Test templates can only be created for testable parts"
msgstr ""

#: part/models.py:3655
msgid "Test template with the same key already exists for part"
msgstr ""

#: part/models.py:3672
msgid "Test Name"
msgstr "Nom de test"

#: part/models.py:3673
msgid "Enter a name for the test"
msgstr ""

#: part/models.py:3679
msgid "Test Key"
msgstr ""

#: part/models.py:3680
msgid "Simplified key for the test"
msgstr ""

#: part/models.py:3687
msgid "Test Description"
msgstr ""

#: part/models.py:3688
msgid "Enter description for this test"
msgstr ""

#: part/models.py:3692 report/models.py:271
msgid "Enabled"
msgstr "Activé"

#: part/models.py:3692
msgid "Is this test enabled?"
msgstr ""

#: part/models.py:3697
msgid "Required"
msgstr "Requis"

#: part/models.py:3698
msgid "Is this test required to pass?"
msgstr ""

#: part/models.py:3703
msgid "Requires Value"
msgstr "Valeur requise"

#: part/models.py:3704
msgid "Does this test require a value when adding a test result?"
msgstr ""

#: part/models.py:3709
msgid "Requires Attachment"
msgstr ""

#: part/models.py:3711
msgid "Does this test require a file attachment when adding a test result?"
msgstr ""

#: part/models.py:3717 part/models.py:3865
msgid "Choices"
msgstr ""

#: part/models.py:3718
msgid "Valid choices for this test (comma-separated)"
msgstr ""

#: part/models.py:3751
msgid "Part Parameter Template"
msgstr ""

#: part/models.py:3778
msgid "Checkbox parameters cannot have units"
msgstr ""

#: part/models.py:3783
msgid "Checkbox parameters cannot have choices"
msgstr ""

#: part/models.py:3820
msgid "Parameter template name must be unique"
msgstr ""

#: part/models.py:3838
msgid "Parameter Name"
msgstr ""

#: part/models.py:3845
msgid "Physical units for this parameter"
msgstr ""

#: part/models.py:3853
msgid "Parameter description"
msgstr ""

#: part/models.py:3859
msgid "Checkbox"
msgstr ""

#: part/models.py:3860
msgid "Is this parameter a checkbox?"
msgstr ""

#: part/models.py:3866
msgid "Valid choices for this parameter (comma-separated)"
msgstr ""

#: part/models.py:3877
msgid "Selection list for this parameter"
msgstr ""

#: part/models.py:3913
msgid "Part Parameter"
msgstr ""

#: part/models.py:3939
msgid "Parameter cannot be modified - part is locked"
msgstr ""

#: part/models.py:3977
msgid "Invalid choice for parameter value"
msgstr ""

#: part/models.py:4028
msgid "Parent Part"
msgstr ""

#: part/models.py:4036 part/models.py:4144 part/models.py:4145
msgid "Parameter Template"
msgstr ""

#: part/models.py:4042
msgid "Parameter Value"
msgstr ""

#: part/models.py:4092
msgid "Part Category Parameter Template"
msgstr ""

#: part/models.py:4151
msgid "Default Value"
msgstr "Valeur par Défaut"

#: part/models.py:4152
msgid "Default Parameter Value"
msgstr ""

#: part/models.py:4283
msgid "BOM item cannot be modified - assembly is locked"
msgstr ""

#: part/models.py:4290
msgid "BOM item cannot be modified - variant assembly is locked"
msgstr ""

#: part/models.py:4300
msgid "Select parent part"
msgstr ""

#: part/models.py:4310
msgid "Sub part"
msgstr ""

#: part/models.py:4311
msgid "Select part to be used in BOM"
msgstr ""

#: part/models.py:4322
msgid "BOM quantity for this BOM item"
msgstr ""

#: part/models.py:4328
msgid "This BOM item is optional"
msgstr ""

#: part/models.py:4334
msgid "This BOM item is consumable (it is not tracked in build orders)"
msgstr ""

#: part/models.py:4341
msgid "Overage"
msgstr "Surplus"

#: part/models.py:4342
msgid "Estimated build wastage quantity (absolute or percentage)"
msgstr ""

#: part/models.py:4349
msgid "BOM item reference"
msgstr ""

#: part/models.py:4357
msgid "BOM item notes"
msgstr ""

#: part/models.py:4363
msgid "Checksum"
msgstr ""

#: part/models.py:4364
msgid "BOM line checksum"
msgstr ""

#: part/models.py:4369
msgid "Validated"
msgstr "Validée"

#: part/models.py:4370
msgid "This BOM item has been validated"
msgstr ""

#: part/models.py:4375
msgid "Gets inherited"
msgstr ""

#: part/models.py:4376
msgid "This BOM item is inherited by BOMs for variant parts"
msgstr ""

#: part/models.py:4382
msgid "Stock items for variant parts can be used for this BOM item"
msgstr ""

#: part/models.py:4467 stock/models.py:848
msgid "Quantity must be integer value for trackable parts"
msgstr ""

#: part/models.py:4477 part/models.py:4479
msgid "Sub part must be specified"
msgstr ""

#: part/models.py:4624
msgid "BOM Item Substitute"
msgstr ""

#: part/models.py:4645
msgid "Substitute part cannot be the same as the master part"
msgstr ""

#: part/models.py:4658
msgid "Parent BOM item"
msgstr ""

#: part/models.py:4666
msgid "Substitute part"
msgstr ""

#: part/models.py:4682
msgid "Part 1"
msgstr ""

#: part/models.py:4690
msgid "Part 2"
msgstr ""

#: part/models.py:4691
msgid "Select Related Part"
msgstr ""

#: part/models.py:4698
msgid "Note for this relationship"
msgstr ""

#: part/models.py:4717
msgid "Part relationship cannot be created between a part and itself"
msgstr ""

#: part/models.py:4722
msgid "Duplicate relationship already exists"
msgstr ""

#: part/serializers.py:125
msgid "Parent Category"
msgstr ""

#: part/serializers.py:126
msgid "Parent part category"
msgstr ""

#: part/serializers.py:134 part/serializers.py:168
msgid "Subcategories"
msgstr ""

#: part/serializers.py:207
msgid "Results"
msgstr ""

#: part/serializers.py:208
msgid "Number of results recorded against this template"
msgstr ""

#: part/serializers.py:235 part/serializers.py:253 stock/serializers.py:685
msgid "Purchase currency of this stock item"
msgstr "Devise d'achat de l'item"

#: part/serializers.py:278
msgid "Speculative Quantity"
msgstr ""

#: part/serializers.py:287
msgid "Model ID"
msgstr "Identifiant du Modèle"

#: part/serializers.py:313
msgid "File is not an image"
msgstr ""

#: part/serializers.py:344
msgid "Number of parts using this template"
msgstr ""

#: part/serializers.py:489
msgid "Original Part"
msgstr ""

#: part/serializers.py:490
msgid "Select original part to duplicate"
msgstr ""

#: part/serializers.py:495
msgid "Copy Image"
msgstr "Copier l'image"

#: part/serializers.py:496
msgid "Copy image from original part"
msgstr ""

#: part/serializers.py:502
msgid "Copy BOM"
msgstr ""

#: part/serializers.py:503
msgid "Copy bill of materials from original part"
msgstr ""

#: part/serializers.py:509
msgid "Copy Parameters"
msgstr "Copier les paramètres"

#: part/serializers.py:510
msgid "Copy parameter data from original part"
msgstr ""

#: part/serializers.py:516
msgid "Copy Notes"
msgstr ""

#: part/serializers.py:517
msgid "Copy notes from original part"
msgstr ""

#: part/serializers.py:535
msgid "Initial Stock Quantity"
msgstr ""

#: part/serializers.py:537
msgid "Specify initial stock quantity for this Part. If quantity is zero, no stock is added."
msgstr ""

#: part/serializers.py:544
msgid "Initial Stock Location"
msgstr ""

#: part/serializers.py:545
msgid "Specify initial stock location for this Part"
msgstr ""

#: part/serializers.py:562
msgid "Select supplier (or leave blank to skip)"
msgstr ""

#: part/serializers.py:578
msgid "Select manufacturer (or leave blank to skip)"
msgstr ""

#: part/serializers.py:588
msgid "Manufacturer part number"
msgstr ""

#: part/serializers.py:595
msgid "Selected company is not a valid supplier"
msgstr ""

#: part/serializers.py:604
msgid "Selected company is not a valid manufacturer"
msgstr ""

#: part/serializers.py:615
msgid "Manufacturer part matching this MPN already exists"
msgstr ""

#: part/serializers.py:622
msgid "Supplier part matching this SKU already exists"
msgstr ""

#: part/serializers.py:911 part/stocktake.py:222
msgid "Category Name"
msgstr "Nom catégorie"

#: part/serializers.py:937
msgid "Building"
msgstr "Construction"

#: part/serializers.py:952 part/stocktake.py:223 stock/serializers.py:1084
#: stock/serializers.py:1260 users/ruleset.py:36
msgid "Stock Items"
msgstr "Éléments en stock"

#: part/serializers.py:955
msgid "Revisions"
msgstr ""

#: part/serializers.py:958
msgid "Suppliers"
msgstr "Fournisseurs"

#: part/serializers.py:961 templates/email/low_stock_notification.html:16
#: templates/email/part_event_notification.html:17
msgid "Total Stock"
msgstr "Stock total"

#: part/serializers.py:967
msgid "Unallocated Stock"
msgstr ""

#: part/serializers.py:973
msgid "Variant Stock"
msgstr ""

#: part/serializers.py:1006
msgid "Duplicate Part"
msgstr ""

#: part/serializers.py:1007
msgid "Copy initial data from another Part"
msgstr ""

#: part/serializers.py:1013
msgid "Initial Stock"
msgstr ""

#: part/serializers.py:1014
msgid "Create Part with initial stock quantity"
msgstr ""

#: part/serializers.py:1020
msgid "Supplier Information"
msgstr ""

#: part/serializers.py:1021
msgid "Add initial supplier information for this part"
msgstr ""

#: part/serializers.py:1029
msgid "Copy Category Parameters"
msgstr ""

#: part/serializers.py:1030
msgid "Copy parameter templates from selected part category"
msgstr ""

#: part/serializers.py:1035
msgid "Existing Image"
msgstr ""

#: part/serializers.py:1036
msgid "Filename of an existing part image"
msgstr ""

#: part/serializers.py:1053
msgid "Image file does not exist"
msgstr ""

#: part/serializers.py:1257
msgid "Limit stocktake report to a particular part, and any variant parts"
msgstr ""

#: part/serializers.py:1267
msgid "Limit stocktake report to a particular part category, and any child categories"
msgstr ""

#: part/serializers.py:1277
msgid "Limit stocktake report to a particular stock location, and any child locations"
msgstr ""

#: part/serializers.py:1283
msgid "Exclude External Stock"
msgstr ""

#: part/serializers.py:1284
msgid "Exclude stock items in external locations"
msgstr ""

#: part/serializers.py:1289
msgid "Generate Report"
msgstr ""

#: part/serializers.py:1290
msgid "Generate report file containing calculated stocktake data"
msgstr ""

#: part/serializers.py:1295
msgid "Update Parts"
msgstr ""

#: part/serializers.py:1296
msgid "Update specified parts with calculated stocktake data"
msgstr ""

#: part/serializers.py:1304
msgid "Stocktake functionality is not enabled"
msgstr ""

#: part/serializers.py:1309
msgid "Background worker check failed"
msgstr "Échec de la vérification du processus d'arrière-plan"

#: part/serializers.py:1425
msgid "Minimum Price"
msgstr "Prix Minimum"

#: part/serializers.py:1426
msgid "Override calculated value for minimum price"
msgstr ""

#: part/serializers.py:1433
msgid "Minimum price currency"
msgstr ""

#: part/serializers.py:1440
msgid "Maximum Price"
msgstr "Prix Maximum"

#: part/serializers.py:1441
msgid "Override calculated value for maximum price"
msgstr ""

#: part/serializers.py:1448
msgid "Maximum price currency"
msgstr ""

#: part/serializers.py:1477
msgid "Update"
msgstr ""

#: part/serializers.py:1478
msgid "Update pricing for this part"
msgstr ""

#: part/serializers.py:1501
#, python-brace-format
msgid "Could not convert from provided currencies to {default_currency}"
msgstr ""

#: part/serializers.py:1508
msgid "Minimum price must not be greater than maximum price"
msgstr ""

#: part/serializers.py:1511
msgid "Maximum price must not be less than minimum price"
msgstr ""

#: part/serializers.py:1664
msgid "Select the parent assembly"
msgstr ""

#: part/serializers.py:1678
msgid "Select the component part"
msgstr ""

#: part/serializers.py:1698
msgid "Can Build"
msgstr ""

#: part/serializers.py:1925
msgid "Select part to copy BOM from"
msgstr ""

#: part/serializers.py:1933
msgid "Remove Existing Data"
msgstr ""

#: part/serializers.py:1934
msgid "Remove existing BOM items before copying"
msgstr ""

#: part/serializers.py:1939
msgid "Include Inherited"
msgstr ""

#: part/serializers.py:1940
msgid "Include BOM items which are inherited from templated parts"
msgstr ""

#: part/serializers.py:1945
msgid "Skip Invalid Rows"
msgstr ""

#: part/serializers.py:1946
msgid "Enable this option to skip invalid rows"
msgstr ""

#: part/serializers.py:1951
msgid "Copy Substitute Parts"
msgstr ""

#: part/serializers.py:1952
msgid "Copy substitute parts when duplicate BOM items"
msgstr ""

#: part/stocktake.py:218
msgid "Part ID"
msgstr "ID de composant"

#: part/stocktake.py:220
msgid "Part Description"
msgstr "Description pièce"

#: part/stocktake.py:221
msgid "Category ID"
msgstr "ID catégorie"

#: part/stocktake.py:224
msgid "Total Quantity"
msgstr ""

#: part/stocktake.py:225
msgid "Total Cost Min"
msgstr ""

#: part/stocktake.py:226
msgid "Total Cost Max"
msgstr ""

#: part/stocktake.py:284
msgid "Stocktake Report Available"
msgstr ""

#: part/stocktake.py:285
msgid "A new stocktake report is available for download"
msgstr ""

#: part/tasks.py:38
msgid "Low stock notification"
msgstr "Notification de stock faible"

#: part/tasks.py:40
#, python-brace-format
msgid "The available stock for {part.name} has fallen below the configured minimum level"
msgstr "Le stock disponible pour {part.name}, est tombé en dessous du niveau minimum configuré"

#: plugin/api.py:78
msgid "Builtin"
msgstr ""

#: plugin/api.py:92
msgid "Mandatory"
msgstr ""

#: plugin/api.py:103
msgid "Sample"
msgstr ""

#: plugin/api.py:117 plugin/models.py:159
msgid "Installed"
msgstr ""

#: plugin/api.py:184
msgid "Plugin cannot be deleted as it is currently active"
msgstr ""

#: plugin/base/action/api.py:34
msgid "No action specified"
msgstr "Aucune action spécifiée"

#: plugin/base/action/api.py:46
msgid "No matching action found"
msgstr "Aucune action correspondante trouvée"

#: plugin/base/barcodes/api.py:211
msgid "No match found for barcode data"
msgstr "Aucune correspondance trouvée pour les données du code-barres"

#: plugin/base/barcodes/api.py:215
msgid "Match found for barcode data"
msgstr "Correspondance trouvée pour les données du code-barres"

#: plugin/base/barcodes/api.py:253 plugin/base/barcodes/serializers.py:77
msgid "Model is not supported"
msgstr ""

#: plugin/base/barcodes/api.py:258
msgid "Model instance not found"
msgstr ""

#: plugin/base/barcodes/api.py:287
msgid "Barcode matches existing item"
msgstr ""

#: plugin/base/barcodes/api.py:418
msgid "No matching part data found"
msgstr ""

#: plugin/base/barcodes/api.py:434
msgid "No matching supplier parts found"
msgstr ""

#: plugin/base/barcodes/api.py:437
msgid "Multiple matching supplier parts found"
msgstr ""

#: plugin/base/barcodes/api.py:450 plugin/base/barcodes/api.py:677
msgid "No matching plugin found for barcode data"
msgstr ""

#: plugin/base/barcodes/api.py:460
msgid "Matched supplier part"
msgstr ""

#: plugin/base/barcodes/api.py:528
msgid "Item has already been received"
msgstr ""

#: plugin/base/barcodes/api.py:576
msgid "No plugin match for supplier barcode"
msgstr ""

#: plugin/base/barcodes/api.py:625
msgid "Multiple matching line items found"
msgstr ""

#: plugin/base/barcodes/api.py:628
msgid "No matching line item found"
msgstr ""

#: plugin/base/barcodes/api.py:674
msgid "No sales order provided"
msgstr ""

#: plugin/base/barcodes/api.py:683
msgid "Barcode does not match an existing stock item"
msgstr ""

#: plugin/base/barcodes/api.py:699
msgid "Stock item does not match line item"
msgstr ""

#: plugin/base/barcodes/api.py:729
msgid "Insufficient stock available"
msgstr ""

#: plugin/base/barcodes/api.py:742
msgid "Stock item allocated to sales order"
msgstr ""

#: plugin/base/barcodes/api.py:745
msgid "Not enough information"
msgstr ""

#: plugin/base/barcodes/mixins.py:308
#: plugin/builtin/barcodes/inventree_barcode.py:101
msgid "Found matching item"
msgstr ""

#: plugin/base/barcodes/mixins.py:374
msgid "Supplier part does not match line item"
msgstr ""

#: plugin/base/barcodes/mixins.py:377
msgid "Line item is already completed"
msgstr ""

#: plugin/base/barcodes/mixins.py:414
msgid "Further information required to receive line item"
msgstr ""

#: plugin/base/barcodes/mixins.py:422
msgid "Received purchase order line item"
msgstr ""

#: plugin/base/barcodes/mixins.py:429
msgid "Failed to receive line item"
msgstr ""

#: plugin/base/barcodes/serializers.py:53
msgid "Scanned barcode data"
msgstr ""

#: plugin/base/barcodes/serializers.py:62
msgid "Model name to generate barcode for"
msgstr ""

#: plugin/base/barcodes/serializers.py:67
msgid "Primary key of model object to generate barcode for"
msgstr ""

#: plugin/base/barcodes/serializers.py:137
msgid "Purchase Order to allocate items against"
msgstr ""

#: plugin/base/barcodes/serializers.py:143
msgid "Purchase order is not open"
msgstr ""

#: plugin/base/barcodes/serializers.py:161
msgid "Supplier to receive items from"
msgstr ""

#: plugin/base/barcodes/serializers.py:168
msgid "PurchaseOrder to receive items against"
msgstr ""

#: plugin/base/barcodes/serializers.py:174
msgid "Purchase order has not been placed"
msgstr ""

#: plugin/base/barcodes/serializers.py:182
msgid "Location to receive items into"
msgstr ""

#: plugin/base/barcodes/serializers.py:188
msgid "Cannot select a structural location"
msgstr ""

#: plugin/base/barcodes/serializers.py:196
msgid "Purchase order line item to receive items against"
msgstr ""

#: plugin/base/barcodes/serializers.py:202
msgid "Automatically allocate stock items to the purchase order"
msgstr ""

#: plugin/base/barcodes/serializers.py:215
msgid "Sales Order to allocate items against"
msgstr ""

#: plugin/base/barcodes/serializers.py:221
msgid "Sales order is not open"
msgstr ""

#: plugin/base/barcodes/serializers.py:229
msgid "Sales order line item to allocate items against"
msgstr ""

#: plugin/base/barcodes/serializers.py:236
msgid "Sales order shipment to allocate items against"
msgstr ""

#: plugin/base/barcodes/serializers.py:242
msgid "Shipment has already been delivered"
msgstr ""

#: plugin/base/barcodes/serializers.py:247
msgid "Quantity to allocate"
msgstr ""

#: plugin/base/label/label.py:39
msgid "Label printing failed"
msgstr ""

#: plugin/base/label/mixins.py:55
msgid "Error rendering label to PDF"
msgstr ""

#: plugin/base/label/mixins.py:69
msgid "Error rendering label to HTML"
msgstr ""

#: plugin/base/label/mixins.py:146
msgid "No items provided to print"
msgstr ""

#: plugin/base/ui/serializers.py:30
msgid "Plugin Name"
msgstr "Nom de l'extension"

#: plugin/base/ui/serializers.py:34
msgid "Feature Type"
msgstr "Type de fonctionnalité"

#: plugin/base/ui/serializers.py:39
msgid "Feature Label"
msgstr ""

#: plugin/base/ui/serializers.py:44
msgid "Feature Title"
msgstr "Titre de la fonctionnalité"

#: plugin/base/ui/serializers.py:49
msgid "Feature Description"
msgstr "Description de la fonctionnalité"

#: plugin/base/ui/serializers.py:54
msgid "Feature Icon"
msgstr "Icône de fonctionnalité"

#: plugin/base/ui/serializers.py:58
msgid "Feature Options"
msgstr "Options de fonctionnalité"

#: plugin/base/ui/serializers.py:61
msgid "Feature Context"
msgstr "Contexte de la fonctionnalité"

#: plugin/base/ui/serializers.py:64
msgid "Feature Source (javascript)"
msgstr "Source de la fonctionnalité (javascript)"

#: plugin/builtin/barcodes/inventree_barcode.py:27
msgid "InvenTree Barcodes"
msgstr ""

#: plugin/builtin/barcodes/inventree_barcode.py:28
msgid "Provides native support for barcodes"
msgstr ""

#: plugin/builtin/barcodes/inventree_barcode.py:30
#: plugin/builtin/exporter/bom_exporter.py:61
#: plugin/builtin/exporter/inventree_exporter.py:17
#: plugin/builtin/exporter/part_parameter_exporter.py:36
#: plugin/builtin/integration/core_notifications.py:33
#: plugin/builtin/integration/currency_exchange.py:21
#: plugin/builtin/integration/part_notifications.py:25
#: plugin/builtin/labels/inventree_label.py:26
#: plugin/builtin/labels/inventree_machine.py:64
#: plugin/builtin/labels/label_sheet.py:71
#: plugin/builtin/suppliers/digikey.py:19 plugin/builtin/suppliers/lcsc.py:21
#: plugin/builtin/suppliers/mouser.py:19 plugin/builtin/suppliers/tme.py:21
msgid "InvenTree contributors"
msgstr "Contributeurs d'InvenTree"

#: plugin/builtin/barcodes/inventree_barcode.py:34
msgid "Internal Barcode Format"
msgstr ""

#: plugin/builtin/barcodes/inventree_barcode.py:35
msgid "Select an internal barcode format"
msgstr ""

#: plugin/builtin/barcodes/inventree_barcode.py:37
msgid "JSON barcodes (human readable)"
msgstr ""

#: plugin/builtin/barcodes/inventree_barcode.py:38
msgid "Short barcodes (space optimized)"
msgstr ""

#: plugin/builtin/barcodes/inventree_barcode.py:43
msgid "Short Barcode Prefix"
msgstr ""

#: plugin/builtin/barcodes/inventree_barcode.py:45
msgid "Customize the prefix used for short barcodes, may be useful for environments with multiple InvenTree instances"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:17
msgid "Levels"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:18
msgid "Number of levels to export"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:23
#: plugin/builtin/exporter/part_parameter_exporter.py:17
msgid "Stock Data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:23
#: plugin/builtin/exporter/part_parameter_exporter.py:17
msgid "Include part stock data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:27
#: plugin/builtin/exporter/part_parameter_exporter.py:21
msgid "Pricing Data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:27
#: plugin/builtin/exporter/part_parameter_exporter.py:21
msgid "Include part pricing data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:31
msgid "Supplier Data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:31
msgid "Include supplier data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:36
msgid "Manufacturer Data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:37
msgid "Include manufacturer data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:42
msgid "Substitute Data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:43
msgid "Include substitute part data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:48
msgid "Parameter Data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:49
msgid "Include part parameter data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:58
msgid "Multi-Level BOM Exporter"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:59
msgid "Provides support for exporting multi-level BOMs"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:96
msgid "BOM Level"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:102
#, python-brace-format
msgid "Substitute {n}"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:108
#, python-brace-format
msgid "Supplier {n}"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:109
#, python-brace-format
msgid "Supplier {n} SKU"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:110
#, python-brace-format
msgid "Supplier {n} MPN"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:116
#, python-brace-format
msgid "Manufacturer {n}"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:117
#, python-brace-format
msgid "Manufacturer {n} MPN"
msgstr ""

#: plugin/builtin/exporter/inventree_exporter.py:14
msgid "InvenTree Generic Exporter"
msgstr ""

#: plugin/builtin/exporter/inventree_exporter.py:15
msgid "Provides support for exporting data from InvenTree"
msgstr ""

#: plugin/builtin/exporter/part_parameter_exporter.py:33
msgid "Part Parameter Exporter"
msgstr ""

#: plugin/builtin/exporter/part_parameter_exporter.py:34
msgid "Exporter for part parameter data"
msgstr ""

#: plugin/builtin/integration/core_notifications.py:32
msgid "InvenTree Notifications"
msgstr "Notifications InvenTree"

#: plugin/builtin/integration/core_notifications.py:34
msgid "Integrated outgoing notification methods"
msgstr ""

#: plugin/builtin/integration/core_notifications.py:39
#: plugin/builtin/integration/core_notifications.py:78
msgid "Enable email notifications"
msgstr ""

#: plugin/builtin/integration/core_notifications.py:40
#: plugin/builtin/integration/core_notifications.py:79
msgid "Allow sending of emails for event notifications"
msgstr ""

#: plugin/builtin/integration/core_notifications.py:45
msgid "Enable slack notifications"
msgstr ""

#: plugin/builtin/integration/core_notifications.py:47
msgid "Allow sending of slack channel messages for event notifications"
msgstr ""

#: plugin/builtin/integration/core_notifications.py:53
msgid "Slack incoming webhook url"
msgstr ""

#: plugin/builtin/integration/core_notifications.py:54
msgid "URL that is used to send messages to a slack channel"
msgstr ""

#: plugin/builtin/integration/core_notifications.py:164
msgid "Open link"
msgstr ""

#: plugin/builtin/integration/currency_exchange.py:22
msgid "InvenTree Currency Exchange"
msgstr ""

#: plugin/builtin/integration/currency_exchange.py:23
msgid "Default currency exchange integration"
msgstr ""

#: plugin/builtin/integration/part_notifications.py:24
msgid "Part Notifications"
msgstr ""

#: plugin/builtin/integration/part_notifications.py:26
msgid "Notify users about part changes"
msgstr ""

#: plugin/builtin/integration/part_notifications.py:31
msgid "Send notifications"
msgstr ""

#: plugin/builtin/integration/part_notifications.py:32
msgid "Send notifications for part changes to subscribed users"
msgstr ""

#: plugin/builtin/integration/part_notifications.py:49
msgid "Changed part notification"
msgstr ""

#: plugin/builtin/integration/part_notifications.py:59
#, python-brace-format
msgid "The part `{part.name}` has been triggered with a `{part_action}` event"
msgstr ""

#: plugin/builtin/labels/inventree_label.py:23
msgid "InvenTree PDF label printer"
msgstr ""

#: plugin/builtin/labels/inventree_label.py:24
msgid "Provides native support for printing PDF labels"
msgstr ""

#: plugin/builtin/labels/inventree_label.py:32
#: plugin/builtin/labels/label_sheet.py:77
msgid "Debug mode"
msgstr ""

#: plugin/builtin/labels/inventree_label.py:33
#: plugin/builtin/labels/label_sheet.py:78
msgid "Enable debug mode - returns raw HTML instead of PDF"
msgstr ""

#: plugin/builtin/labels/inventree_machine.py:61
msgid "InvenTree machine label printer"
msgstr ""

#: plugin/builtin/labels/inventree_machine.py:62
msgid "Provides support for printing using a machine"
msgstr ""

#: plugin/builtin/labels/inventree_machine.py:164
msgid "last used"
msgstr ""

#: plugin/builtin/labels/inventree_machine.py:181
msgid "Options"
msgstr ""

#: plugin/builtin/labels/label_sheet.py:30
msgid "Page size for the label sheet"
msgstr ""

#: plugin/builtin/labels/label_sheet.py:35
msgid "Skip Labels"
msgstr ""

#: plugin/builtin/labels/label_sheet.py:36
msgid "Skip this number of labels when printing label sheets"
msgstr ""

#: plugin/builtin/labels/label_sheet.py:42
msgid "Border"
msgstr ""

#: plugin/builtin/labels/label_sheet.py:43
msgid "Print a border around each label"
msgstr ""

#: plugin/builtin/labels/label_sheet.py:48 report/models.py:371
msgid "Landscape"
msgstr ""

#: plugin/builtin/labels/label_sheet.py:49
msgid "Print the label sheet in landscape mode"
msgstr ""

#: plugin/builtin/labels/label_sheet.py:54
msgid "Page Margin"
msgstr ""

#: plugin/builtin/labels/label_sheet.py:55
msgid "Margin around the page in mm"
msgstr ""

#: plugin/builtin/labels/label_sheet.py:68
msgid "InvenTree Label Sheet Printer"
msgstr ""

#: plugin/builtin/labels/label_sheet.py:69
msgid "Arrays multiple labels onto a single sheet"
msgstr ""

#: plugin/builtin/labels/label_sheet.py:121
msgid "Label is too large for page size"
msgstr ""

#: plugin/builtin/labels/label_sheet.py:160
msgid "No labels were generated"
msgstr ""

#: plugin/builtin/suppliers/digikey.py:16
msgid "Supplier Integration - DigiKey"
msgstr ""

#: plugin/builtin/suppliers/digikey.py:17
msgid "Provides support for scanning DigiKey barcodes"
msgstr ""

#: plugin/builtin/suppliers/digikey.py:26
msgid "The Supplier which acts as 'DigiKey'"
msgstr ""

#: plugin/builtin/suppliers/lcsc.py:18
msgid "Supplier Integration - LCSC"
msgstr ""

#: plugin/builtin/suppliers/lcsc.py:19
msgid "Provides support for scanning LCSC barcodes"
msgstr ""

#: plugin/builtin/suppliers/lcsc.py:27
msgid "The Supplier which acts as 'LCSC'"
msgstr ""

#: plugin/builtin/suppliers/mouser.py:16
msgid "Supplier Integration - Mouser"
msgstr ""

#: plugin/builtin/suppliers/mouser.py:17
msgid "Provides support for scanning Mouser barcodes"
msgstr ""

#: plugin/builtin/suppliers/mouser.py:25
msgid "The Supplier which acts as 'Mouser'"
msgstr ""

#: plugin/builtin/suppliers/tme.py:18
msgid "Supplier Integration - TME"
msgstr ""

#: plugin/builtin/suppliers/tme.py:19
msgid "Provides support for scanning TME barcodes"
msgstr ""

#: plugin/builtin/suppliers/tme.py:27
msgid "The Supplier which acts as 'TME'"
msgstr ""

#: plugin/installer.py:229 plugin/installer.py:307
msgid "Only staff users can administer plugins"
msgstr ""

#: plugin/installer.py:232
msgid "Plugin installation is disabled"
msgstr ""

#: plugin/installer.py:269
msgid "Installed plugin successfully"
msgstr ""

#: plugin/installer.py:274
#, python-brace-format
msgid "Installed plugin into {path}"
msgstr ""

#: plugin/installer.py:298
msgid "Plugin was not found in registry"
msgstr ""

#: plugin/installer.py:301
msgid "Plugin is not a packaged plugin"
msgstr ""

#: plugin/installer.py:304
msgid "Plugin package name not found"
msgstr ""

#: plugin/installer.py:324
msgid "Plugin uninstalling is disabled"
msgstr ""

#: plugin/installer.py:328
msgid "Plugin cannot be uninstalled as it is currently active"
msgstr ""

#: plugin/installer.py:332
msgid "Plugin is not installed"
msgstr ""

#: plugin/installer.py:348
msgid "Plugin installation not found"
msgstr ""

#: plugin/installer.py:363
msgid "Uninstalled plugin successfully"
msgstr ""

#: plugin/models.py:38
msgid "Plugin Configuration"
msgstr ""

#: plugin/models.py:39
msgid "Plugin Configurations"
msgstr ""

#: plugin/models.py:46
msgid "Key of plugin"
msgstr ""

#: plugin/models.py:54
msgid "PluginName of the plugin"
msgstr "Non du Plugin"

#: plugin/models.py:61 plugin/serializers.py:119
msgid "Package Name"
msgstr ""

#: plugin/models.py:63
msgid "Name of the installed package, if the plugin was installed via PIP"
msgstr ""

#: plugin/models.py:68
msgid "Is the plugin active"
msgstr ""

#: plugin/models.py:168
msgid "Sample plugin"
msgstr ""

#: plugin/models.py:176
msgid "Builtin Plugin"
msgstr "Extension Intégrée"

#: plugin/models.py:184
msgid "Mandatory Plugin"
msgstr ""

#: plugin/models.py:189
msgid "Package Plugin"
msgstr ""

#: plugin/models.py:268
msgid "Plugin"
msgstr "Extension"

#: plugin/models.py:315
msgid "Method"
msgstr ""

#: plugin/plugin.py:312
msgid "No author found"
msgstr ""

#: plugin/registry.py:611
#, python-brace-format
msgid "Plugin '{p}' is not compatible with the current InvenTree version {v}"
msgstr ""

#: plugin/registry.py:614
#, python-brace-format
msgid "Plugin requires at least version {v}"
msgstr ""

#: plugin/registry.py:616
#, python-brace-format
msgid "Plugin requires at most version {v}"
msgstr ""

#: plugin/samples/integration/sample.py:52
msgid "Enable PO"
msgstr ""

#: plugin/samples/integration/sample.py:53
msgid "Enable PO functionality in InvenTree interface"
msgstr ""

#: plugin/samples/integration/sample.py:58
msgid "API Key"
msgstr ""

#: plugin/samples/integration/sample.py:59
msgid "Key required for accessing external API"
msgstr ""

#: plugin/samples/integration/sample.py:63
msgid "Numerical"
msgstr "Numérique"

#: plugin/samples/integration/sample.py:64
msgid "A numerical setting"
msgstr ""

#: plugin/samples/integration/sample.py:69
msgid "Choice Setting"
msgstr ""

#: plugin/samples/integration/sample.py:70
msgid "A setting with multiple choices"
msgstr ""

#: plugin/samples/integration/sample_currency_exchange.py:15
msgid "Sample currency exchange plugin"
msgstr ""

#: plugin/samples/integration/sample_currency_exchange.py:18
msgid "InvenTree Contributors"
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:27
msgid "Enable Part Panels"
msgstr "Activer le panneau de pièces"

#: plugin/samples/integration/user_interface_sample.py:28
msgid "Enable custom panels for Part views"
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:33
msgid "Enable Purchase Order Panels"
msgstr "Activer les panneaux de commande"

#: plugin/samples/integration/user_interface_sample.py:34
msgid "Enable custom panels for Purchase Order views"
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:39
msgid "Enable Broken Panels"
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:40
msgid "Enable broken panels for testing"
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:45
msgid "Enable Dynamic Panel"
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:46
msgid "Enable dynamic panels for testing"
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:98
msgid "Part Panel"
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:131
msgid "Broken Dashboard Item"
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:133
msgid "This is a broken dashboard item - it will not render!"
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:139
msgid "Sample Dashboard Item"
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:141
msgid "This is a sample dashboard item. It renders a simple string of HTML content."
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:147
msgid "Context Dashboard Item"
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:161
msgid "Admin Dashboard Item"
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:162
msgid "This is an admin-only dashboard item."
msgstr ""

#: plugin/serializers.py:86
msgid "Source File"
msgstr ""

#: plugin/serializers.py:87
msgid "Path to the source file for admin integration"
msgstr ""

#: plugin/serializers.py:94
msgid "Optional context data for the admin integration"
msgstr ""

#: plugin/serializers.py:110
msgid "Source URL"
msgstr ""

#: plugin/serializers.py:112
msgid "Source for the package - this can be a custom registry or a VCS path"
msgstr ""

#: plugin/serializers.py:121
msgid "Name for the Plugin Package - can also contain a version indicator"
msgstr ""

#: plugin/serializers.py:128
msgid "Version"
msgstr ""

#: plugin/serializers.py:130
msgid "Version specifier for the plugin. Leave blank for latest version."
msgstr "Identifiant de version du plugin. Laissez vide pour la dernière version."

#: plugin/serializers.py:135
msgid "Confirm plugin installation"
msgstr ""

#: plugin/serializers.py:137
msgid "This will install this plugin now into the current instance. The instance will go into maintenance."
msgstr ""

#: plugin/serializers.py:150
msgid "Installation not confirmed"
msgstr ""

#: plugin/serializers.py:152
msgid "Either packagename of URL must be provided"
msgstr ""

#: plugin/serializers.py:188
msgid "Full reload"
msgstr ""

#: plugin/serializers.py:189
msgid "Perform a full reload of the plugin registry"
msgstr ""

#: plugin/serializers.py:195
msgid "Force reload"
msgstr ""

#: plugin/serializers.py:197
msgid "Force a reload of the plugin registry, even if it is already loaded"
msgstr ""

#: plugin/serializers.py:204
msgid "Collect plugins"
msgstr ""

#: plugin/serializers.py:205
msgid "Collect plugins and add them to the registry"
msgstr ""

#: plugin/serializers.py:232
msgid "Activate Plugin"
msgstr ""

#: plugin/serializers.py:233
msgid "Activate this plugin"
msgstr ""

#: plugin/serializers.py:253
msgid "Delete configuration"
msgstr ""

#: plugin/serializers.py:254
msgid "Delete the plugin configuration from the database"
msgstr ""

#: report/api.py:44 report/serializers.py:98 report/serializers.py:148
msgid "Items"
msgstr ""

#: report/api.py:121
msgid "Plugin not found"
msgstr ""

#: report/api.py:123
msgid "Plugin is not active"
msgstr ""

#: report/api.py:125
msgid "Plugin does not support label printing"
msgstr ""

#: report/api.py:173
msgid "Invalid label dimensions"
msgstr ""

#: report/api.py:188 report/api.py:267
msgid "No valid items provided to template"
msgstr ""

#: report/helpers.py:43
msgid "A4"
msgstr ""

#: report/helpers.py:44
msgid "A3"
msgstr ""

#: report/helpers.py:45
msgid "Legal"
msgstr ""

#: report/helpers.py:46
msgid "Letter"
msgstr ""

#: report/models.py:117
msgid "Template file with this name already exists"
msgstr ""

#: report/models.py:204
msgid "Template name"
msgstr "Nom du modèle"

#: report/models.py:210
msgid "Template description"
msgstr ""

#: report/models.py:216
msgid "Revision number (auto-increments)"
msgstr ""

#: report/models.py:222
msgid "Attach to Model on Print"
msgstr ""

#: report/models.py:224
msgid "Save report output as an attachment against linked model instance when printing"
msgstr ""

#: report/models.py:265
msgid "Filename Pattern"
msgstr "Modèle de nom de fichier"

#: report/models.py:266
msgid "Pattern for generating filenames"
msgstr ""

#: report/models.py:271
msgid "Template is enabled"
msgstr ""

#: report/models.py:278
msgid "Target model type for template"
msgstr ""

#: report/models.py:298
msgid "Filters"
msgstr "Filtres"

#: report/models.py:299
msgid "Template query filters (comma-separated list of key=value pairs)"
msgstr ""

#: report/models.py:358 report/models.py:564
msgid "Template file"
msgstr ""

#: report/models.py:366
msgid "Page size for PDF reports"
msgstr ""

#: report/models.py:372
msgid "Render report in landscape orientation"
msgstr ""

#: report/models.py:486
#, python-brace-format
msgid "Report generated from template {self.name}"
msgstr ""

#: report/models.py:511
msgid "Error generating report"
msgstr ""

#: report/models.py:570
msgid "Width [mm]"
msgstr "Largeur [mm]"

#: report/models.py:571
msgid "Label width, specified in mm"
msgstr "Largeur de l'étiquette, spécifiée en mm"

#: report/models.py:577
msgid "Height [mm]"
msgstr "Hauteur [mm]"

#: report/models.py:578
msgid "Label height, specified in mm"
msgstr "Hauteur de l'étiquette, spécifiée en mm"

#: report/models.py:688
msgid "Error printing labels"
msgstr ""

#: report/models.py:707
msgid "Snippet"
msgstr "Extrait "

#: report/models.py:708
msgid "Report snippet file"
msgstr ""

#: report/models.py:715
msgid "Snippet file description"
msgstr ""

#: report/models.py:733
msgid "Asset"
msgstr "Elément"

#: report/models.py:734
msgid "Report asset file"
msgstr ""

#: report/models.py:741
msgid "Asset file description"
msgstr ""

#: report/serializers.py:91
msgid "Select report template"
msgstr ""

#: report/serializers.py:99 report/serializers.py:149
msgid "List of item primary keys to include in the report"
msgstr ""

#: report/serializers.py:132
msgid "Select label template"
msgstr "Sélection du modèle d'étiquette"

#: report/serializers.py:140
msgid "Printing Plugin"
msgstr ""

#: report/serializers.py:141
msgid "Select plugin to use for label printing"
msgstr ""

#: report/templates/label/part_label.html:31
#: report/templates/label/stockitem_qr.html:21
#: report/templates/label/stocklocation_qr.html:20
msgid "QR Code"
msgstr "QR code"

#: report/templates/label/part_label_code128.html:31
#: report/templates/label/stocklocation_qr_and_text.html:31
msgid "QR code"
msgstr "QR code"

#: report/templates/report/inventree_bill_of_materials_report.html:100
msgid "Bill of Materials"
msgstr ""

#: report/templates/report/inventree_bill_of_materials_report.html:133
msgid "Materials needed"
msgstr ""

#: report/templates/report/inventree_build_order_report.html:98
#: report/templates/report/inventree_purchase_order_report.html:40
#: report/templates/report/inventree_sales_order_report.html:40
#: report/templates/report/inventree_sales_order_shipment_report.html:37
#: report/templates/report/inventree_test_report.html:84
#: report/templates/report/inventree_test_report.html:162
msgid "Part image"
msgstr ""

#: report/templates/report/inventree_build_order_report.html:121
msgid "Issued"
msgstr ""

#: report/templates/report/inventree_build_order_report.html:146
msgid "Required For"
msgstr "Requis pour"

#: report/templates/report/inventree_purchase_order_report.html:15
msgid "Supplier was deleted"
msgstr ""

#: report/templates/report/inventree_purchase_order_report.html:30
#: report/templates/report/inventree_sales_order_report.html:30
msgid "Unit Price"
msgstr ""

#: report/templates/report/inventree_purchase_order_report.html:55
#: report/templates/report/inventree_return_order_report.html:48
#: report/templates/report/inventree_sales_order_report.html:55
msgid "Extra Line Items"
msgstr ""

#: report/templates/report/inventree_purchase_order_report.html:72
#: report/templates/report/inventree_sales_order_report.html:72
msgid "Total"
msgstr ""

#: report/templates/report/inventree_return_order_report.html:25
#: report/templates/report/inventree_sales_order_shipment_report.html:45
#: report/templates/report/inventree_test_report.html:88 stock/models.py:1006
#: stock/serializers.py:163
msgid "Serial Number"
msgstr "Numéro de série"

#: report/templates/report/inventree_sales_order_shipment_report.html:23
msgid "Allocations"
msgstr ""

#: report/templates/report/inventree_sales_order_shipment_report.html:47
msgid "Batch"
msgstr "Lot"

#: report/templates/report/inventree_stock_location_report.html:97
msgid "Stock location items"
msgstr ""

#: report/templates/report/inventree_test_report.html:21
msgid "Stock Item Test Report"
msgstr ""

#: report/templates/report/inventree_test_report.html:97
msgid "Test Results"
msgstr ""

#: report/templates/report/inventree_test_report.html:102
msgid "Test"
msgstr "Test"

#: report/templates/report/inventree_test_report.html:129
msgid "Pass"
msgstr ""

#: report/templates/report/inventree_test_report.html:131
msgid "Fail"
msgstr ""

#: report/templates/report/inventree_test_report.html:138
msgid "No result (required)"
msgstr ""

#: report/templates/report/inventree_test_report.html:140
msgid "No result"
msgstr ""

#: report/templates/report/inventree_test_report.html:153
#: stock/serializers.py:668
msgid "Installed Items"
msgstr "Éléments installés"

#: report/templates/report/inventree_test_report.html:167
msgid "Serial"
msgstr "Numéro de série"

#: report/templatetags/report.py:144
msgid "Asset file does not exist"
msgstr ""

#: report/templatetags/report.py:201 report/templatetags/report.py:277
msgid "Image file not found"
msgstr ""

#: report/templatetags/report.py:302
msgid "part_image tag requires a Part instance"
msgstr ""

#: report/templatetags/report.py:349
msgid "company_image tag requires a Company instance"
msgstr ""

#: stock/api.py:255
msgid "Filter by location depth"
msgstr ""

#: stock/api.py:275
msgid "Filter by top-level locations"
msgstr ""

#: stock/api.py:290
msgid "Include sub-locations in filtered results"
msgstr "Inclure les sous-emplacements dans les résultats filtrés"

#: stock/api.py:311 stock/serializers.py:1256
msgid "Parent Location"
msgstr "Emplacement parent"

#: stock/api.py:312
msgid "Filter by parent location"
msgstr "Filtrer par emplacement parent"

#: stock/api.py:554
msgid "Part name (case insensitive)"
msgstr ""

#: stock/api.py:560
msgid "Part name contains (case insensitive)"
msgstr ""

#: stock/api.py:566
msgid "Part name (regex)"
msgstr ""

#: stock/api.py:571
msgid "Part IPN (case insensitive)"
msgstr ""

#: stock/api.py:577
msgid "Part IPN contains (case insensitive)"
msgstr ""

#: stock/api.py:583
msgid "Part IPN (regex)"
msgstr ""

#: stock/api.py:595
msgid "Minimum stock"
msgstr ""

#: stock/api.py:599
msgid "Maximum stock"
msgstr ""

#: stock/api.py:602
msgid "Status Code"
msgstr ""

#: stock/api.py:642
msgid "External Location"
msgstr "Emplacement externe"

#: stock/api.py:741
msgid "Consumed by Build Order"
msgstr ""

#: stock/api.py:751
msgid "Installed in other stock item"
msgstr ""

#: stock/api.py:840
msgid "Part Tree"
msgstr ""

#: stock/api.py:862
msgid "Updated before"
msgstr ""

#: stock/api.py:866
msgid "Updated after"
msgstr ""

#: stock/api.py:870
msgid "Stocktake Before"
msgstr ""

#: stock/api.py:874
msgid "Stocktake After"
msgstr ""

#: stock/api.py:879
msgid "Expiry date before"
msgstr "Date d'expiration avant"

#: stock/api.py:883
msgid "Expiry date after"
msgstr "Date d’expiration après"

#: stock/api.py:886 stock/serializers.py:673
msgid "Stale"
msgstr ""

#: stock/api.py:987
msgid "Quantity is required"
msgstr ""

#: stock/api.py:992
msgid "Valid part must be supplied"
msgstr ""

#: stock/api.py:1019
msgid "The given supplier part does not exist"
msgstr ""

#: stock/api.py:1029
msgid "The supplier part has a pack size defined, but flag use_pack_size not set"
msgstr ""

#: stock/api.py:1056
msgid "Serial numbers cannot be supplied for a non-trackable part"
msgstr ""

#: stock/models.py:70
msgid "Stock Location type"
msgstr ""

#: stock/models.py:71
msgid "Stock Location types"
msgstr ""

#: stock/models.py:97
msgid "Default icon for all locations that have no icon set (optional)"
msgstr ""

#: stock/models.py:155 stock/models.py:968
msgid "Stock Location"
msgstr ""

#: stock/models.py:156 users/ruleset.py:35
msgid "Stock Locations"
msgstr ""

#: stock/models.py:205 stock/models.py:1133
msgid "Owner"
msgstr "Propriétaire"

#: stock/models.py:206 stock/models.py:1134
msgid "Select Owner"
msgstr "Sélectionner un propriétaire"

#: stock/models.py:214
msgid "Stock items may not be directly located into a structural stock locations, but may be located to child locations."
msgstr ""

#: stock/models.py:221 users/models.py:499
msgid "External"
msgstr ""

#: stock/models.py:222
msgid "This is an external stock location"
msgstr ""

#: stock/models.py:228
msgid "Location type"
msgstr ""

#: stock/models.py:232
msgid "Stock location type of this location"
msgstr ""

#: stock/models.py:304
msgid "You cannot make this stock location structural because some stock items are already located into it!"
msgstr ""

#: stock/models.py:562
msgid "Part must be specified"
msgstr ""

#: stock/models.py:827
msgid "Stock items cannot be located into structural stock locations!"
msgstr ""

#: stock/models.py:854 stock/serializers.py:516
msgid "Stock item cannot be created for virtual parts"
msgstr ""

#: stock/models.py:871
#, python-brace-format
msgid "Part type ('{self.supplier_part.part}') must be {self.part}"
msgstr ""

#: stock/models.py:881 stock/models.py:894
msgid "Quantity must be 1 for item with a serial number"
msgstr "La quantité doit être de 1 pour un article avec un numéro de série"

#: stock/models.py:884
msgid "Serial number cannot be set if quantity greater than 1"
msgstr "Le numéro de série ne peut pas être défini si la quantité est supérieure à 1"

#: stock/models.py:906
msgid "Item cannot belong to itself"
msgstr ""

#: stock/models.py:911
msgid "Item must have a build reference if is_building=True"
msgstr ""

#: stock/models.py:924
msgid "Build reference does not point to the same part object"
msgstr ""

#: stock/models.py:938
msgid "Parent Stock Item"
msgstr ""

#: stock/models.py:950
msgid "Base part"
msgstr "Pièce de base"

#: stock/models.py:960
msgid "Select a matching supplier part for this stock item"
msgstr ""

#: stock/models.py:972
msgid "Where is this stock item located?"
msgstr ""

#: stock/models.py:980 stock/serializers.py:1667
msgid "Packaging this stock item is stored in"
msgstr ""

#: stock/models.py:986
msgid "Installed In"
msgstr "Installé dans"

#: stock/models.py:991
msgid "Is this item installed in another item?"
msgstr "L'article a été installé dans un autre article ?"

#: stock/models.py:1010
msgid "Serial number for this item"
msgstr "Numéro de série pour cet article"

#: stock/models.py:1027 stock/serializers.py:1652
msgid "Batch code for this stock item"
msgstr ""

#: stock/models.py:1032
msgid "Stock Quantity"
msgstr "Quantité en stock"

#: stock/models.py:1042
msgid "Source Build"
msgstr ""

#: stock/models.py:1045
msgid "Build for this stock item"
msgstr ""

#: stock/models.py:1052
msgid "Consumed By"
msgstr "Consommé par"

#: stock/models.py:1055
msgid "Build order which consumed this stock item"
msgstr ""

#: stock/models.py:1064
msgid "Source Purchase Order"
msgstr "Bon de commande source"

#: stock/models.py:1068
msgid "Purchase order for this stock item"
msgstr ""

#: stock/models.py:1074
msgid "Destination Sales Order"
msgstr ""

#: stock/models.py:1085
msgid "Expiry date for stock item. Stock will be considered expired after this date"
msgstr ""

#: stock/models.py:1103
msgid "Delete on deplete"
msgstr "Supprimer lors de l'épuisement"

#: stock/models.py:1104
msgid "Delete this Stock Item when stock is depleted"
msgstr ""

#: stock/models.py:1125
msgid "Single unit purchase price at time of purchase"
msgstr ""

#: stock/models.py:1156
msgid "Converted to part"
msgstr ""

#: stock/models.py:1721
msgid "Part is not set as trackable"
msgstr ""

#: stock/models.py:1727
msgid "Quantity must be integer"
msgstr "La quantité doit être un nombre entier"

#: stock/models.py:1735
#, python-brace-format
msgid "Quantity must not exceed available stock quantity ({self.quantity})"
msgstr "La quantité ne doit pas dépasser la quantité disponible en stock ({self.quantity})"

#: stock/models.py:1741
msgid "Serial numbers must be provided as a list"
msgstr ""

#: stock/models.py:1746
msgid "Quantity does not match serial numbers"
msgstr "La quantité ne correspond pas au nombre de numéros de série"

#: stock/models.py:1868 stock/models.py:2777
msgid "Test template does not exist"
msgstr "Le modèle de test n'existe pas"

#: stock/models.py:1886
msgid "Stock item has been assigned to a sales order"
msgstr ""

#: stock/models.py:1890
msgid "Stock item is installed in another item"
msgstr ""

#: stock/models.py:1893
msgid "Stock item contains other items"
msgstr ""

#: stock/models.py:1896
msgid "Stock item has been assigned to a customer"
msgstr ""

#: stock/models.py:1899 stock/models.py:2073
msgid "Stock item is currently in production"
msgstr "L'article de stock est actuellement en production"

#: stock/models.py:1902
msgid "Serialized stock cannot be merged"
msgstr ""

#: stock/models.py:1909 stock/serializers.py:1545
msgid "Duplicate stock items"
msgstr ""

#: stock/models.py:1913
msgid "Stock items must refer to the same part"
msgstr ""

#: stock/models.py:1921
msgid "Stock items must refer to the same supplier part"
msgstr ""

#: stock/models.py:1926
msgid "Stock status codes must match"
msgstr ""

#: stock/models.py:2196
msgid "StockItem cannot be moved as it is not in stock"
msgstr ""

#: stock/models.py:2678
msgid "Stock Item Tracking"
msgstr ""

#: stock/models.py:2709
msgid "Entry notes"
msgstr ""

#: stock/models.py:2749
msgid "Stock Item Test Result"
msgstr ""

#: stock/models.py:2780
msgid "Value must be provided for this test"
msgstr ""

#: stock/models.py:2784
msgid "Attachment must be uploaded for this test"
msgstr ""

#: stock/models.py:2789
msgid "Invalid value for this test"
msgstr ""

#: stock/models.py:2813
msgid "Test result"
msgstr "Résultat du test"

#: stock/models.py:2820
msgid "Test output value"
msgstr "Valeur de sortie du test"

#: stock/models.py:2828 stock/serializers.py:259
msgid "Test result attachment"
msgstr "Pièce jointe au résultat du test"

#: stock/models.py:2832
msgid "Test notes"
msgstr "Notes de test"

#: stock/models.py:2840
msgid "Test station"
msgstr "Station de test"

#: stock/models.py:2841
msgid "The identifier of the test station where the test was performed"
msgstr "L'identifiant de la station de test où le test a été effectué"

#: stock/models.py:2847
msgid "Started"
msgstr "Commencé"

#: stock/models.py:2848
msgid "The timestamp of the test start"
msgstr ""

#: stock/models.py:2854
msgid "Finished"
msgstr ""

#: stock/models.py:2855
msgid "The timestamp of the test finish"
msgstr ""

#: stock/serializers.py:84
msgid "Generated batch code"
msgstr ""

#: stock/serializers.py:93
msgid "Select build order"
msgstr ""

#: stock/serializers.py:102
msgid "Select stock item to generate batch code for"
msgstr ""

#: stock/serializers.py:111
msgid "Select location to generate batch code for"
msgstr ""

#: stock/serializers.py:120
msgid "Select part to generate batch code for"
msgstr ""

#: stock/serializers.py:129
msgid "Select purchase order"
msgstr ""

#: stock/serializers.py:136
msgid "Enter quantity for batch code"
msgstr ""

#: stock/serializers.py:162
msgid "Generated serial number"
msgstr ""

#: stock/serializers.py:172
msgid "Select part to generate serial number for"
msgstr ""

#: stock/serializers.py:180
msgid "Quantity of serial numbers to generate"
msgstr ""

#: stock/serializers.py:248
msgid "Test template for this result"
msgstr ""

#: stock/serializers.py:272
msgid "Template ID or test name must be provided"
msgstr ""

#: stock/serializers.py:304
msgid "The test finished time cannot be earlier than the test started time"
msgstr ""

#: stock/serializers.py:341
msgid "Serial number is too large"
msgstr ""

#: stock/serializers.py:487
msgid "Parent Item"
msgstr "Article Parent"

#: stock/serializers.py:488
msgid "Parent stock item"
msgstr "Article de stock parent"

#: stock/serializers.py:508
msgid "Use pack size when adding: the quantity defined is the number of packs"
msgstr ""

#: stock/serializers.py:618
msgid "Supplier Part Number"
msgstr "Référence du fournisseur"

#: stock/serializers.py:665 users/models.py:193
msgid "Expired"
msgstr "Expiré"

#: stock/serializers.py:671
msgid "Child Items"
msgstr "Éléments enfants"

#: stock/serializers.py:675
msgid "Tracking Items"
msgstr ""

#: stock/serializers.py:681
msgid "Purchase price of this stock item, per unit or pack"
msgstr ""

#: stock/serializers.py:719
msgid "Enter number of stock items to serialize"
msgstr "Entrez le nombre d'articles en stock à sérialiser"

#: stock/serializers.py:732
#, python-brace-format
msgid "Quantity must not exceed available stock quantity ({q})"
msgstr "La quantité ne doit pas dépasser la quantité disponible en stock ({q})"

#: stock/serializers.py:739
msgid "Enter serial numbers for new items"
msgstr "Entrez les numéros de série pour les nouveaux articles"

#: stock/serializers.py:750 stock/serializers.py:1502 stock/serializers.py:1790
msgid "Destination stock location"
msgstr ""

#: stock/serializers.py:757
msgid "Optional note field"
msgstr ""

#: stock/serializers.py:767
msgid "Serial numbers cannot be assigned to this part"
msgstr "Les numéros de série ne peuvent pas être assignés à cette pièce"

#: stock/serializers.py:787
msgid "Serial numbers already exist"
msgstr "Les numéros de série existent déjà"

#: stock/serializers.py:826
msgid "Select stock item to install"
msgstr ""

#: stock/serializers.py:833
msgid "Quantity to Install"
msgstr ""

#: stock/serializers.py:834
msgid "Enter the quantity of items to install"
msgstr ""

#: stock/serializers.py:839 stock/serializers.py:919 stock/serializers.py:1049
#: stock/serializers.py:1101
msgid "Add transaction note (optional)"
msgstr ""

#: stock/serializers.py:847
msgid "Quantity to install must be at least 1"
msgstr ""

#: stock/serializers.py:855
msgid "Stock item is unavailable"
msgstr ""

#: stock/serializers.py:866
msgid "Selected part is not in the Bill of Materials"
msgstr ""

#: stock/serializers.py:879
msgid "Quantity to install must not exceed available quantity"
msgstr ""

#: stock/serializers.py:914
msgid "Destination location for uninstalled item"
msgstr ""

#: stock/serializers.py:949
msgid "Select part to convert stock item into"
msgstr ""

#: stock/serializers.py:962
msgid "Selected part is not a valid option for conversion"
msgstr ""

#: stock/serializers.py:979
msgid "Cannot convert stock item with assigned SupplierPart"
msgstr ""

#: stock/serializers.py:1013
msgid "Stock item status code"
msgstr ""

#: stock/serializers.py:1042
msgid "Destination location for returned item"
msgstr ""

#: stock/serializers.py:1085
msgid "Select stock items to change status"
msgstr ""

#: stock/serializers.py:1091
msgid "No stock items selected"
msgstr ""

#: stock/serializers.py:1185 stock/serializers.py:1262
msgid "Sublocations"
msgstr ""

#: stock/serializers.py:1257
msgid "Parent stock location"
msgstr ""

#: stock/serializers.py:1374
msgid "Part must be salable"
msgstr ""

#: stock/serializers.py:1378
msgid "Item is allocated to a sales order"
msgstr ""

#: stock/serializers.py:1382
msgid "Item is allocated to a build order"
msgstr ""

#: stock/serializers.py:1406
msgid "Customer to assign stock items"
msgstr ""

#: stock/serializers.py:1412
msgid "Selected company is not a customer"
msgstr ""

#: stock/serializers.py:1420
msgid "Stock assignment notes"
msgstr ""

#: stock/serializers.py:1430 stock/serializers.py:1695
msgid "A list of stock items must be provided"
msgstr ""

#: stock/serializers.py:1509
msgid "Stock merging notes"
msgstr ""

#: stock/serializers.py:1514
msgid "Allow mismatched suppliers"
msgstr ""

#: stock/serializers.py:1515
msgid "Allow stock items with different supplier parts to be merged"
msgstr ""

#: stock/serializers.py:1520
msgid "Allow mismatched status"
msgstr ""

#: stock/serializers.py:1521
msgid "Allow stock items with different status codes to be merged"
msgstr ""

#: stock/serializers.py:1531
msgid "At least two stock items must be provided"
msgstr ""

#: stock/serializers.py:1598
msgid "No Change"
msgstr "Pas de changement"

#: stock/serializers.py:1627
msgid "StockItem primary key value"
msgstr ""

#: stock/serializers.py:1639
msgid "Stock item is not in stock"
msgstr ""

#: stock/serializers.py:1685
msgid "Stock transaction notes"
msgstr ""

#: stock/serializers.py:1831
msgid "Next Serial Number"
msgstr ""

#: stock/serializers.py:1837
msgid "Previous Serial Number"
msgstr ""

#: stock/status_codes.py:11
msgid "OK"
msgstr "OK"

#: stock/status_codes.py:12
msgid "Attention needed"
msgstr "Attention requise"

#: stock/status_codes.py:13
msgid "Damaged"
msgstr "Endommagé"

#: stock/status_codes.py:14
msgid "Destroyed"
msgstr "Détruit"

#: stock/status_codes.py:15
msgid "Rejected"
msgstr "Rejeté"

#: stock/status_codes.py:19
msgid "Quarantined"
msgstr "En quarantaine"

#: stock/status_codes.py:44
msgid "Legacy stock tracking entry"
msgstr "Ancienne entrée de suivi de stock"

#: stock/status_codes.py:46
msgid "Stock item created"
msgstr "Article en stock créé"

#: stock/status_codes.py:49
msgid "Edited stock item"
msgstr "Article de stock modifié"

#: stock/status_codes.py:50
msgid "Assigned serial number"
msgstr "Numéro de série attribué"

#: stock/status_codes.py:53
msgid "Stock counted"
msgstr "Stock comptabilisé"

#: stock/status_codes.py:54
msgid "Stock manually added"
msgstr "Stock ajouté manuellement"

#: stock/status_codes.py:55
msgid "Stock manually removed"
msgstr "Stock supprimé manuellement"

#: stock/status_codes.py:58
msgid "Location changed"
msgstr "Emplacement modifié"

#: stock/status_codes.py:59
msgid "Stock updated"
msgstr "Stock mis à jour"

#: stock/status_codes.py:62
msgid "Installed into assembly"
msgstr "Installé dans l'assemblage"

#: stock/status_codes.py:63
msgid "Removed from assembly"
msgstr "Retiré de l'assemblage"

#: stock/status_codes.py:65
msgid "Installed component item"
msgstr "Composant installé"

#: stock/status_codes.py:66
msgid "Removed component item"
msgstr "Composant retiré"

#: stock/status_codes.py:69
msgid "Split from parent item"
msgstr "Séparer de l'élément parent"

#: stock/status_codes.py:70
msgid "Split child item"
msgstr "Fractionner l'élément enfant"

#: stock/status_codes.py:73
msgid "Merged stock items"
msgstr "Articles de stock fusionnés"

#: stock/status_codes.py:76
msgid "Converted to variant"
msgstr "Converti en variante"

#: stock/status_codes.py:79
msgid "Build order output created"
msgstr "La sortie de l'ordre de construction a été créée"

#: stock/status_codes.py:80
msgid "Build order output completed"
msgstr "Sortie de l'ordre de construction terminée"

#: stock/status_codes.py:81
msgid "Build order output rejected"
msgstr "La sortie de l'ordre de construction a été refusée"

#: stock/status_codes.py:82
msgid "Consumed by build order"
msgstr "Consommé par ordre de construction"

#: stock/status_codes.py:85
msgid "Shipped against Sales Order"
msgstr "Commandes expédiées vs. ventes"

#: stock/status_codes.py:88
msgid "Received against Purchase Order"
msgstr "Livraisons reçues vs. commandes réalisées"

#: stock/status_codes.py:91
msgid "Returned against Return Order"
msgstr "Livraisons retournées vs. commandes retournées"

#: stock/status_codes.py:94
msgid "Sent to customer"
msgstr "Envoyé au client"

#: stock/status_codes.py:95
msgid "Returned from customer"
msgstr "Retourné par le client"

#: templates/403.html:6 templates/403.html:10 templates/403_csrf.html:7
msgid "Permission Denied"
msgstr "Autorisation refusée"

#: templates/403.html:11
msgid "You do not have permission to view this page."
msgstr ""

#: templates/403_csrf.html:11
msgid "Authentication Failure"
msgstr ""

#: templates/403_csrf.html:12
msgid "You have been logged out from InvenTree."
msgstr "Vous avez été déconnecté•e d'InvenTree."

#: templates/404.html:6 templates/404.html:10
msgid "Page Not Found"
msgstr ""

#: templates/404.html:11
msgid "The requested page does not exist"
msgstr ""

#: templates/500.html:6 templates/500.html:10
msgid "Internal Server Error"
msgstr ""

#: templates/500.html:11
#, python-format
msgid "The %(inventree_title)s server raised an internal error"
msgstr ""

#: templates/500.html:12
msgid "Refer to the error log in the admin interface for further details"
msgstr ""

#: templates/503.html:11 templates/503.html:15
msgid "Site is in Maintenance"
msgstr ""

#: templates/503.html:17
msgid "The site is currently in maintenance and should be up again soon!"
msgstr ""

#: templates/base.html:51
msgid "Server Restart Required"
msgstr "Redémarrage du serveur nécessaire"

#: templates/base.html:54
msgid "A configuration option has been changed which requires a server restart"
msgstr "Une option de configuration a été modifiée, ce qui nécessite un redémarrage du serveur"

#: templates/base.html:54 templates/base.html:64
msgid "Contact your system administrator for further information"
msgstr "Contactez votre administrateur système pour plus d'informations"

#: templates/base.html:61
msgid "Pending Database Migrations"
msgstr ""

#: templates/base.html:64
msgid "There are pending database migrations which require attention"
msgstr ""

#: templates/email/build_order_completed.html:9
#: templates/email/canceled_order_assigned.html:9
#: templates/email/new_order_assigned.html:9
#: templates/email/overdue_build_order.html:9
#: templates/email/overdue_purchase_order.html:9
#: templates/email/overdue_return_order.html:9
#: templates/email/overdue_sales_order.html:9
#: templates/email/purchase_order_received.html:9
#: templates/email/return_order_received.html:9
msgid "Click on the following link to view this order"
msgstr ""

#: templates/email/build_order_required_stock.html:7
msgid "Stock is required for the following build order"
msgstr "Du stock est requis pour la commande de construction suivante"

#: templates/email/build_order_required_stock.html:8
#, python-format
msgid "Build order %(build)s - building %(quantity)s x %(part)s"
msgstr ""

#: templates/email/build_order_required_stock.html:10
msgid "Click on the following link to view this build order"
msgstr ""

#: templates/email/build_order_required_stock.html:14
msgid "The following parts are low on required stock"
msgstr ""

#: templates/email/build_order_required_stock.html:18
msgid "Required Quantity"
msgstr "Quantité requise"

#: templates/email/build_order_required_stock.html:38
#: templates/email/low_stock_notification.html:30
msgid "You are receiving this email because you are subscribed to notifications for this part "
msgstr ""

#: templates/email/low_stock_notification.html:9
#: templates/email/part_event_notification.html:9
msgid "Click on the following link to view this part"
msgstr ""

#: templates/email/low_stock_notification.html:18
#: templates/email/part_event_notification.html:19
msgid "Minimum Quantity"
msgstr ""

#: templates/email/part_event_notification.html:32
msgid "You are receiving this email because you are subscribed to notifications for this part or a category that it is part of "
msgstr ""

#: users/admin.py:101
msgid "Users"
msgstr "Utilisateurs"

#: users/admin.py:102
msgid "Select which users are assigned to this group"
msgstr "Sélectionner quels utilisateurs sont assignés à ce groupe"

#: users/admin.py:137
msgid "Personal info"
msgstr "Informations personnelles"

#: users/admin.py:139
msgid "Permissions"
msgstr "Droits"

#: users/admin.py:142
msgid "Important dates"
msgstr "Dates importantes"

#: users/authentication.py:30 users/models.py:157
msgid "Token has been revoked"
msgstr ""

#: users/authentication.py:33
msgid "Token has expired"
msgstr ""

#: users/models.py:100
msgid "API Token"
msgstr ""

#: users/models.py:101
msgid "API Tokens"
msgstr ""

#: users/models.py:137
msgid "Token Name"
msgstr ""

#: users/models.py:138
msgid "Custom token name"
msgstr ""

#: users/models.py:144
msgid "Token expiry date"
msgstr ""

#: users/models.py:152
msgid "Last Seen"
msgstr ""

#: users/models.py:153
msgid "Last time the token was used"
msgstr ""

#: users/models.py:157
msgid "Revoked"
msgstr ""

#: users/models.py:235
msgid "Permission set"
msgstr "Droit défini"

#: users/models.py:244
msgid "Group"
msgstr "Groupe"

#: users/models.py:248
msgid "View"
msgstr "Vue"

#: users/models.py:248
msgid "Permission to view items"
msgstr "Droit de voir des éléments"

#: users/models.py:252
msgid "Add"
msgstr "Ajouter"

#: users/models.py:252
msgid "Permission to add items"
msgstr "Droit d'ajouter des éléments"

#: users/models.py:256
msgid "Change"
msgstr "Modifier"

#: users/models.py:258
msgid "Permissions to edit items"
msgstr "Droit de modifier des élément"

#: users/models.py:262
msgid "Delete"
msgstr "Supprimer"

#: users/models.py:264
msgid "Permission to delete items"
msgstr "Droit de supprimer des éléments"

#: users/models.py:497
msgid "Bot"
msgstr ""

#: users/models.py:498
msgid "Internal"
msgstr ""

#: users/models.py:500
msgid "Guest"
msgstr ""

#: users/models.py:509
msgid "Language"
msgstr ""

#: users/models.py:510
msgid "Preferred language for the user"
msgstr ""

#: users/models.py:515
msgid "Theme"
msgstr ""

#: users/models.py:516
msgid "Settings for the web UI as JSON - do not edit manually!"
msgstr ""

#: users/models.py:521
msgid "Widgets"
msgstr ""

#: users/models.py:523
msgid "Settings for the dashboard widgets as JSON - do not edit manually!"
msgstr ""

#: users/models.py:530
msgid "Display Name"
msgstr ""

#: users/models.py:531
msgid "Chosen display name for the user"
msgstr ""

#: users/models.py:537
msgid "Position"
msgstr ""

#: users/models.py:538
msgid "Main job title or position"
msgstr ""

#: users/models.py:545
msgid "User status message"
msgstr ""

#: users/models.py:552
msgid "User location information"
msgstr ""

#: users/models.py:557
msgid "User is actively using the system"
msgstr ""

#: users/models.py:564
msgid "Preferred contact information for the user"
msgstr ""

#: users/models.py:570
msgid "User Type"
msgstr ""

#: users/models.py:571
msgid "Which type of user is this?"
msgstr ""

#: users/models.py:577
msgid "Organisation"
msgstr ""

#: users/models.py:578
msgid "Users primary organisation/affiliation"
msgstr ""

#: users/models.py:586
msgid "Primary Group"
msgstr ""

#: users/models.py:587
msgid "Primary group for the user"
msgstr ""

#: users/ruleset.py:31
msgid "Admin"
msgstr ""

#: users/ruleset.py:34
msgid "Stocktake"
msgstr "Prise d'inventaire"

#: users/ruleset.py:38
msgid "Purchase Orders"
msgstr "Bons de commande"

#: users/ruleset.py:39
msgid "Sales Orders"
msgstr "Ventes"

#: users/ruleset.py:40
msgid "Return Orders"
msgstr ""

#: users/serializers.py:236
msgid "Username"
msgstr "Nom d'utilisateur"

#: users/serializers.py:239
msgid "First Name"
msgstr "Prénom"

#: users/serializers.py:239
msgid "First name of the user"
msgstr "Prénom de l'utilisateur"

#: users/serializers.py:243
msgid "Last Name"
msgstr "Nom"

#: users/serializers.py:243
msgid "Last name of the user"
msgstr "Nom de famille de l'utilisateur"

#: users/serializers.py:247
msgid "Email address of the user"
msgstr "Adresse e-mail de l'utilisateur"

#: users/serializers.py:323
msgid "Staff"
msgstr "Staff"

#: users/serializers.py:324
msgid "Does this user have staff permissions"
msgstr "Cet utilisateur a-t-il les permissions du staff"

#: users/serializers.py:329
msgid "Superuser"
msgstr "Super-utilisateur"

#: users/serializers.py:329
msgid "Is this user a superuser"
msgstr "Cet utilisateur est-il un super-utilisateur"

#: users/serializers.py:333
msgid "Is this user account active"
msgstr "Ce compte d'utilisateur est-il actif"

#: users/serializers.py:345
msgid "Only a superuser can adjust this field"
msgstr ""

#: users/serializers.py:399
msgid "Only staff users can create new users"
msgstr ""

#: users/serializers.py:404
msgid "You do not have permission to create users"
msgstr ""

#: users/serializers.py:425
msgid "Your account has been created."
msgstr "Votre compte a été créé."

#: users/serializers.py:427
msgid "Please use the password reset function to login"
msgstr "Veuillez utiliser la fonction de réinitialisation du mot de passe pour vous connecter"

#: users/serializers.py:433
msgid "Welcome to InvenTree"
msgstr "Bienvenue dans InvenTree"

