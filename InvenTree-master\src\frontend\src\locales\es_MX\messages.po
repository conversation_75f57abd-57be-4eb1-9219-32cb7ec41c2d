msgid ""
msgstr ""
"POT-Creation-Date: 2023-06-09 22:10+0200\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: @lingui/cli\n"
"Language: es_MX\n"
"Project-Id-Version: inventree\n"
"Report-Msgid-Bugs-To: \n"
"PO-Revision-Date: 2025-04-15 23:09\n"
"Last-Translator: \n"
"Language-Team: Spanish, Mexico\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Crowdin-Project: inventree\n"
"X-Crowdin-Project-ID: 452300\n"
"X-Crowdin-Language: es-MX\n"
"X-Crowdin-File: /src/frontend/src/locales/en/messages.po\n"
"X-Crowdin-File-ID: 252\n"

#: src/components/Boundary.tsx:12
msgid "Error rendering component"
msgstr "Error al renderizar componente"

#: src/components/Boundary.tsx:14
msgid "An error occurred while rendering this component. Refer to the console for more information."
msgstr "Ocurrió un error mientras se renderizaba este componente. Consulte la consola para más información."

#: src/components/DashboardItemProxy.tsx:34
#~ msgid "Title"
#~ msgstr "Title"

#: src/components/barcodes/BarcodeCameraInput.tsx:103
msgid "Error while scanning"
msgstr "Error al escanear"

#: src/components/barcodes/BarcodeCameraInput.tsx:117
msgid "Error while stopping"
msgstr "Error al detener"

#: src/components/barcodes/BarcodeCameraInput.tsx:159
msgid "Start scanning by selecting a camera and pressing the play button."
msgstr "Comienza a escanear seleccionando una cámara y presionando el botón reproducir."

#: src/components/barcodes/BarcodeCameraInput.tsx:180
msgid "Stop scanning"
msgstr "Dejar de escanear"

#: src/components/barcodes/BarcodeCameraInput.tsx:190
msgid "Start scanning"
msgstr "Comenzar a escanear"

#: src/components/barcodes/BarcodeInput.tsx:34
#: src/tables/general/BarcodeScanTable.tsx:55
#: src/tables/settings/BarcodeScanHistoryTable.tsx:63
msgid "Barcode"
msgstr "Código de barras"

#: src/components/barcodes/BarcodeInput.tsx:35
#: src/components/barcodes/BarcodeKeyboardInput.tsx:9
#: src/defaults/actions.tsx:69
msgid "Scan"
msgstr "Escanear"

#: src/components/barcodes/BarcodeInput.tsx:53
msgid "Camera Input"
msgstr "Entrada de cámara"

#: src/components/barcodes/BarcodeInput.tsx:63
msgid "Scanner Input"
msgstr "Entrada de escáner"

#: src/components/barcodes/BarcodeInput.tsx:105
msgid "Barcode Data"
msgstr "Datos del código de barras"

#: src/components/barcodes/BarcodeInput.tsx:109
msgid "No barcode data"
msgstr "Sin datos del código de barras"

#: src/components/barcodes/BarcodeInput.tsx:110
msgid "Scan or enter barcode data"
msgstr "Escanea o introduce datos de código de barras"

#: src/components/barcodes/BarcodeInput.tsx:114
#: src/components/editors/NotesEditor.tsx:74
#: src/components/editors/TemplateEditor/TemplateEditor.tsx:157
#: src/components/forms/fields/ApiFormField.tsx:351
#: src/components/forms/fields/TableField.tsx:45
#: src/components/importer/ImportDataSelector.tsx:188
#: src/components/importer/ImporterColumnSelector.tsx:216
#: src/components/modals/LicenseModal.tsx:88
#: src/components/nav/SearchDrawer.tsx:575
#: src/components/render/ModelType.tsx:273
#: src/functions/auth.tsx:527
#: src/pages/ErrorPage.tsx:11
#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:127
#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:638
#: src/pages/part/PartPricingPanel.tsx:71
#: src/states/IconState.tsx:46
#: src/states/IconState.tsx:76
#: src/tables/InvenTreeTable.tsx:512
#: src/tables/InvenTreeTableHeader.tsx:118
#: src/tables/bom/BomTable.tsx:455
#: src/tables/stock/StockItemTestResultTable.tsx:321
msgid "Error"
msgstr "Error"

#: src/components/barcodes/BarcodeKeyboardInput.tsx:39
msgid "Enter barcode data"
msgstr "Introduce datos del código de barras"

#: src/components/barcodes/BarcodeScanDialog.tsx:32
#: src/components/buttons/ScanButton.tsx:15
#: src/components/nav/NavigationDrawer.tsx:129
#: src/forms/PurchaseOrderForms.tsx:416
#: src/forms/PurchaseOrderForms.tsx:520
msgid "Scan Barcode"
msgstr "Escanear código de barras"

#: src/components/barcodes/BarcodeScanDialog.tsx:83
msgid "No matching item found"
msgstr "No se encontró el artículo"

#: src/components/barcodes/BarcodeScanDialog.tsx:90
#: src/pages/Index/Scan.tsx:129
msgid "Failed to scan barcode"
msgstr "No se pudo escanear el código de barras"

#: src/components/barcodes/QRCode.tsx:94
msgid "Low (7%)"
msgstr "Bajo (7%)"

#: src/components/barcodes/QRCode.tsx:95
msgid "Medium (15%)"
msgstr "Medio (15%)"

#: src/components/barcodes/QRCode.tsx:96
msgid "Quartile (25%)"
msgstr "Cuartil (25%)"

#: src/components/barcodes/QRCode.tsx:97
msgid "High (30%)"
msgstr "Alto (30%)"

#: src/components/barcodes/QRCode.tsx:107
msgid "Custom barcode"
msgstr "Código de barras personalizado"

#: src/components/barcodes/QRCode.tsx:108
msgid "A custom barcode is registered for this item. The shown code is not that custom barcode."
msgstr "Un código de barras personalizado está registrado para este artículo. El código mostrado no es ese código de barras personalizado."

#: src/components/barcodes/QRCode.tsx:127
msgid "Barcode Data:"
msgstr "Datos de código de barras:"

#: src/components/barcodes/QRCode.tsx:138
msgid "Select Error Correction Level"
msgstr "Seleccionar Nivel de Corrección de Error"

#: src/components/barcodes/QRCode.tsx:170
msgid "Failed to link barcode"
msgstr "No se pudo vincular el código de barras"

#: src/components/barcodes/QRCode.tsx:179
#: src/pages/part/PartDetail.tsx:240
#: src/pages/purchasing/PurchaseOrderDetail.tsx:205
#: src/pages/sales/ReturnOrderDetail.tsx:169
#: src/pages/sales/SalesOrderDetail.tsx:181
#: src/pages/sales/SalesOrderShipmentDetail.tsx:170
msgid "Link"
msgstr "Enlace"

#: src/components/barcodes/QRCode.tsx:200
msgid "This will remove the link to the associated barcode"
msgstr "Esto eliminará el enlace al código de barras asociado"

#: src/components/barcodes/QRCode.tsx:205
#: src/components/items/ActionDropdown.tsx:190
#: src/forms/PurchaseOrderForms.tsx:511
msgid "Unlink Barcode"
msgstr "Desvincular Código de Barras"

#: src/components/buttons/AdminButton.tsx:85
msgid "Open in admin interface"
msgstr "Abrir en interfaz de administrador"

#: src/components/buttons/CopyButton.tsx:18
#~ msgid "Copy to clipboard"
#~ msgstr "Copy to clipboard"

#: src/components/buttons/CopyButton.tsx:29
msgid "Copied"
msgstr "Copiado"

#: src/components/buttons/CopyButton.tsx:29
msgid "Copy"
msgstr "Copiar"

#: src/components/buttons/PrintingActions.tsx:51
msgid "Printing Labels"
msgstr "Imprimir etiquetas"

#: src/components/buttons/PrintingActions.tsx:56
msgid "Printing Reports"
msgstr "Imprimir reportes"

#: src/components/buttons/PrintingActions.tsx:77
#~ msgid "Printing"
#~ msgstr "Printing"

#: src/components/buttons/PrintingActions.tsx:78
#~ msgid "Printing completed successfully"
#~ msgstr "Printing completed successfully"

#: src/components/buttons/PrintingActions.tsx:114
#~ msgid "Label printing completed successfully"
#~ msgstr "Label printing completed successfully"

#: src/components/buttons/PrintingActions.tsx:118
msgid "Print Label"
msgstr "Imprimir etiqueta"

#: src/components/buttons/PrintingActions.tsx:121
#~ msgid "The label could not be generated"
#~ msgstr "The label could not be generated"

#: src/components/buttons/PrintingActions.tsx:124
#: src/components/buttons/PrintingActions.tsx:149
msgid "Print"
msgstr "Imprimir"

#: src/components/buttons/PrintingActions.tsx:133
msgid "Print Report"
msgstr "Imprimir informe"

#: src/components/buttons/PrintingActions.tsx:152
#~ msgid "Generate"
#~ msgstr "Generate"

#: src/components/buttons/PrintingActions.tsx:153
#~ msgid "Report printing completed successfully"
#~ msgstr "Report printing completed successfully"

#: src/components/buttons/PrintingActions.tsx:159
#~ msgid "The report could not be generated"
#~ msgstr "The report could not be generated"

#: src/components/buttons/PrintingActions.tsx:170
msgid "Printing Actions"
msgstr "Acciones de impresión"

#: src/components/buttons/PrintingActions.tsx:175
msgid "Print Labels"
msgstr "Imprimir etiquetas"

#: src/components/buttons/PrintingActions.tsx:181
msgid "Print Reports"
msgstr "Imprimir informes"

#: src/components/buttons/RemoveRowButton.tsx:8
msgid "Remove this row"
msgstr "Eliminar esta fila"

#: src/components/buttons/SSOButton.tsx:38
msgid "You will be redirected to the provider for further actions."
msgstr "Usted será redirigido al proveedor para más acciones."

#: src/components/buttons/SSOButton.tsx:44
#~ msgid "This provider is not full set up."
#~ msgstr "This provider is not full set up."

#: src/components/buttons/SSOButton.tsx:54
#~ msgid "Sign in redirect failed."
#~ msgstr "Sign in redirect failed."

#: src/components/buttons/ScanButton.tsx:15
#~ msgid "Scan QR code"
#~ msgstr "Scan QR code"

#: src/components/buttons/ScanButton.tsx:19
msgid "Open Barcode Scanner"
msgstr "Abrir Escáner de código de barras"

#: src/components/buttons/ScanButton.tsx:20
#~ msgid "Open QR code scanner"
#~ msgstr "Open QR code scanner"

#: src/components/buttons/SpotlightButton.tsx:12
msgid "Open spotlight"
msgstr "Abrir spotlight"

#: src/components/buttons/StarredToggleButton.tsx:57
#~ msgid "Unsubscribe from part"
#~ msgstr "Unsubscribe from part"

#: src/components/buttons/StarredToggleButton.tsx:59
msgid "Unsubscribe from notifications"
msgstr "Desuscribirse de las notificaciones"

#: src/components/buttons/StarredToggleButton.tsx:60
msgid "Subscribe to notifications"
msgstr "Suscribirse a las notificaciones"

#: src/components/buttons/YesNoButton.tsx:16
msgid "Pass"
msgstr "Aprobado"

#: src/components/buttons/YesNoButton.tsx:17
msgid "Fail"
msgstr "Falló"

#: src/components/buttons/YesNoButton.tsx:33
#: src/tables/Filter.tsx:83
msgid "Yes"
msgstr "Sí"

#: src/components/buttons/YesNoButton.tsx:33
#: src/tables/Filter.tsx:84
msgid "No"
msgstr "No"

#: src/components/calendar/Calendar.tsx:93
#: src/components/calendar/Calendar.tsx:156
msgid "Calendar Filters"
msgstr "Filtros de calendario"

#: src/components/calendar/Calendar.tsx:108
msgid "Previous month"
msgstr "Mes anterior"

#: src/components/calendar/Calendar.tsx:117
msgid "Select month"
msgstr "Seleccione el mes"

#: src/components/calendar/Calendar.tsx:138
msgid "Next month"
msgstr "Siguiente mes"

#: src/components/calendar/Calendar.tsx:169
#: src/tables/InvenTreeTableHeader.tsx:248
msgid "Download data"
msgstr "Descargar datos"

#: src/components/calendar/OrderCalendar.tsx:133
msgid "Order Updated"
msgstr "Orden actualizada"

#: src/components/calendar/OrderCalendar.tsx:143
msgid "Error updating order"
msgstr ""

#: src/components/calendar/OrderCalendar.tsx:179
#: src/tables/Filter.tsx:140
msgid "Overdue"
msgstr ""

#: src/components/dashboard/DashboardLayout.tsx:285
msgid "No Widgets Selected"
msgstr "No hay widgets seleccionados"

#: src/components/dashboard/DashboardLayout.tsx:288
msgid "Use the menu to add widgets to the dashboard"
msgstr "Usa el menú para añadir widgets al panel de control"

#: src/components/dashboard/DashboardMenu.tsx:60
#: src/components/dashboard/DashboardMenu.tsx:127
msgid "Accept Layout"
msgstr "Aceptar diseño"

#: src/components/dashboard/DashboardMenu.tsx:92
#: src/components/nav/NavigationDrawer.tsx:71
#: src/defaults/actions.tsx:25
#: src/defaults/links.tsx:31
#: src/pages/Index/Home.tsx:8
msgid "Dashboard"
msgstr "Panel de control"

#: src/components/dashboard/DashboardMenu.tsx:100
msgid "Edit Layout"
msgstr "Editar diseño"

#: src/components/dashboard/DashboardMenu.tsx:109
msgid "Add Widget"
msgstr "Añadir Widget"

#: src/components/dashboard/DashboardMenu.tsx:118
msgid "Remove Widgets"
msgstr "Eliminar widgets"

#: src/components/dashboard/DashboardWidget.tsx:63
msgid "Remove this widget from the dashboard"
msgstr "Eliminar este widget del panel de control"

#: src/components/dashboard/DashboardWidgetDrawer.tsx:77
msgid "Filter dashboard widgets"
msgstr "Filtrar widgets de panel de control"

#: src/components/dashboard/DashboardWidgetDrawer.tsx:98
msgid "Add this widget to the dashboard"
msgstr "Añadir este widget al panel de control"

#: src/components/dashboard/DashboardWidgetDrawer.tsx:123
msgid "No Widgets Available"
msgstr "No hay widgets disponibles"

#: src/components/dashboard/DashboardWidgetDrawer.tsx:124
msgid "There are no more widgets available for the dashboard"
msgstr "No hay más widgets disponibles para el panel de control"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:22
msgid "Subscribed Parts"
msgstr "Piezas suscritas"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:23
msgid "Show the number of parts which you have subscribed to"
msgstr "Mostrar el número de piezas a las que te has suscrito"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:29
msgid "Subscribed Categories"
msgstr "Categorías suscritas"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:30
msgid "Show the number of part categories which you have subscribed to"
msgstr "Mostrar el número de categorías de piezas a las que se ha suscrito"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:38
#: src/pages/part/PartSchedulingDetail.tsx:306
#: src/tables/part/PartTable.tsx:241
msgid "Low Stock"
msgstr "Existencias bajas"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:40
msgid "Show the number of parts which are low on stock"
msgstr "Mostrar el número de piezas que son bajas en existencia"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:45
msgid "Required for Build Orders"
msgstr "Requerido para construir pedidos"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:47
msgid "Show parts which are required for active build orders"
msgstr "Mostrar las partes requeridas para las órdenes de construcción activas"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:52
msgid "Expired Stock Items"
msgstr "Artículos de stock caducados"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:54
msgid "Show the number of stock items which have expired"
msgstr "Mostrar el número de elementos de stock que han caducado"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:60
msgid "Stale Stock Items"
msgstr "Elementos obsoletos"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:62
msgid "Show the number of stock items which are stale"
msgstr "Mostrar el número de artículos de stock que están obsoletos"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:68
msgid "Active Build Orders"
msgstr "Órdenes de construcción activas"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:70
msgid "Show the number of build orders which are currently active"
msgstr "Mostrar el número de órdenes de construcción que actualmente están activas"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:75
msgid "Overdue Build Orders"
msgstr "Órdenes de construcción atrasadas"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:77
msgid "Show the number of build orders which are overdue"
msgstr "Mostrar el número de órdenes de construcción vencidas"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:82
msgid "Assigned Build Orders"
msgstr "Órdenes de construcción asignadas"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:84
msgid "Show the number of build orders which are assigned to you"
msgstr "Mostrar el número de órdenes de construcción asignadas a usted"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:89
msgid "Active Sales Orders"
msgstr "Órdenes de Venta activas"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:91
msgid "Show the number of sales orders which are currently active"
msgstr "Mostrar el número de pedidos de venta que están activos actualmente"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:96
msgid "Overdue Sales Orders"
msgstr "Órdenes de venta vencidas"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:98
msgid "Show the number of sales orders which are overdue"
msgstr "Mostrar el número de pedidos que están retrasados"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:103
msgid "Assigned Sales Orders"
msgstr "Pedidos de venta asignados"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:105
msgid "Show the number of sales orders which are assigned to you"
msgstr "Mostrar el número de órdenes de venta que se le han asignado"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:110
msgid "Active Purchase Orders"
msgstr "Órdenes de Compra asignadas"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:112
msgid "Show the number of purchase orders which are currently active"
msgstr "Mostrar el número de órdenes de compra que están activas"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:117
msgid "Overdue Purchase Orders"
msgstr "Pedidos de Compra Atrasados"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:119
msgid "Show the number of purchase orders which are overdue"
msgstr "Mostrar el número de órdenes de compra que están atrasadas"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:124
msgid "Assigned Purchase Orders"
msgstr "Órdenes de Compra asignadas"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:126
msgid "Show the number of purchase orders which are assigned to you"
msgstr "Mostrar el número de órdenes de compra que se le asignaron"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:131
msgid "Active Return Orders"
msgstr "Pedidos de devolución activos"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:133
msgid "Show the number of return orders which are currently active"
msgstr "Mostrar el número de órdenes de devolución actualmente activas"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:138
msgid "Overdue Return Orders"
msgstr "Órdenes de devolución atrasadas"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:140
msgid "Show the number of return orders which are overdue"
msgstr "Mostrar el número de órdenes de devolución que están vencidas"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:145
msgid "Assigned Return Orders"
msgstr "Órdenes de devolución asignadas"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:147
msgid "Show the number of return orders which are assigned to you"
msgstr "Mostrar el número de órdenes de devolución que se le asignaron"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:158
#: src/components/dashboard/widgets/GetStartedWidget.tsx:15
#: src/defaults/links.tsx:86
msgid "Getting Started"
msgstr "Empezando"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:159
#: src/defaults/links.tsx:89
msgid "Getting started with InvenTree"
msgstr "Empezando con InvenTree"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:166
#: src/components/dashboard/widgets/NewsWidget.tsx:123
msgid "News Updates"
msgstr "Noticias actualizadas"

#: src/components/dashboard/DashboardWidgetLibrary.tsx:167
msgid "The latest news from InvenTree"
msgstr "Las últimas noticias de InvenTree"

#: src/components/dashboard/widgets/ColorToggleWidget.tsx:18
#: src/components/nav/MainMenu.tsx:77
msgid "Change Color Mode"
msgstr "Cambiar modo de color"

#: src/components/dashboard/widgets/ColorToggleWidget.tsx:23
msgid "Change the color mode of the user interface"
msgstr "Cambiar el modo de color de la interfaz de usuario"

#: src/components/dashboard/widgets/LanguageSelectWidget.tsx:18
msgid "Change Language"
msgstr "Cambiar el Idioma"

#: src/components/dashboard/widgets/LanguageSelectWidget.tsx:23
msgid "Change the language of the user interface"
msgstr "Cambiar el idioma de la interfaz de usuario"

#: src/components/dashboard/widgets/NewsWidget.tsx:60
#: src/components/nav/NotificationDrawer.tsx:89
#: src/pages/Notifications.tsx:53
msgid "Mark as read"
msgstr "Marcar como leído"

#: src/components/dashboard/widgets/NewsWidget.tsx:115
msgid "Requires Superuser"
msgstr "Requiere Superusuario"

#: src/components/dashboard/widgets/NewsWidget.tsx:116
msgid "This widget requires superuser permissions"
msgstr "Este widget requiere permisos de superusuario"

#: src/components/dashboard/widgets/NewsWidget.tsx:133
msgid "No News"
msgstr "Sin noticias"

#: src/components/dashboard/widgets/NewsWidget.tsx:134
msgid "There are no unread news items"
msgstr "No hay noticias sin leer"

#: src/components/details/Details.tsx:116
#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:76
#: src/pages/core/UserDetail.tsx:93
#: src/pages/core/UserDetail.tsx:203
#: src/tables/settings/UserTable.tsx:360
msgid "Superuser"
msgstr "Superusuario"

#: src/components/details/Details.tsx:117
#: src/pages/core/UserDetail.tsx:87
#: src/pages/core/UserDetail.tsx:200
#: src/tables/settings/UserTable.tsx:355
msgid "Staff"
msgstr "Personal"

#: src/components/details/Details.tsx:117
#~ msgid "Email:"
#~ msgstr "Email:"

#: src/components/details/Details.tsx:118
msgid "Email: "
msgstr ""

#: src/components/details/Details.tsx:387
msgid "No name defined"
msgstr "No hay nombre definido"

#: src/components/details/DetailsImage.tsx:77
msgid "Remove Image"
msgstr "Quitar imagen"

#: src/components/details/DetailsImage.tsx:80
msgid "Remove the associated image from this item?"
msgstr "¿Eliminar imagen asociada al artículo?"

#: src/components/details/DetailsImage.tsx:83
#: src/components/editors/TemplateEditor/TemplateEditor.tsx:187
#: src/components/items/ActionDropdown.tsx:275
#: src/components/items/ActionDropdown.tsx:276
#: src/contexts/ThemeContext.tsx:42
#: src/hooks/UseForm.tsx:40
#: src/pages/Index/Settings/AccountSettings/useConfirm.tsx:106
#: src/tables/FilterSelectDrawer.tsx:333
#: src/tables/RowActions.tsx:85
#: src/tables/build/BuildOutputTable.tsx:441
msgid "Cancel"
msgstr "Cancelar"

#: src/components/details/DetailsImage.tsx:83
#: src/forms/StockForms.tsx:749
#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:204
#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:407
#: src/pages/stock/StockDetail.tsx:728
msgid "Remove"
msgstr "Eliminar"

#: src/components/details/DetailsImage.tsx:109
msgid "Drag and drop to upload"
msgstr "Arrastra y suelta para subir"

#: src/components/details/DetailsImage.tsx:112
msgid "Click to select file(s)"
msgstr "Clic para seleccionar archivo(s)"

#: src/components/details/DetailsImage.tsx:172
msgid "Image uploaded"
msgstr "Imagen subida"

#: src/components/details/DetailsImage.tsx:173
msgid "Image has been uploaded successfully"
msgstr "La imagen se ha subido correctamente"

#: src/components/details/DetailsImage.tsx:180
#: src/tables/general/AttachmentTable.tsx:197
msgid "Upload Error"
msgstr "Error al subir"

#: src/components/details/DetailsImage.tsx:250
msgid "Clear"
msgstr "Borrar"

#: src/components/details/DetailsImage.tsx:256
#: src/components/forms/ApiForm.tsx:702
#: src/contexts/ThemeContext.tsx:41
#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:649
msgid "Submit"
msgstr "Aceptar"

#: src/components/details/DetailsImage.tsx:300
msgid "Select from existing images"
msgstr "Seleccionar desde imágenes existentes"

#: src/components/details/DetailsImage.tsx:308
msgid "Select Image"
msgstr "Seleccionar imagen"

#: src/components/details/DetailsImage.tsx:324
msgid "Download remote image"
msgstr "Descargar imagen remota"

#: src/components/details/DetailsImage.tsx:339
msgid "Upload new image"
msgstr "Subir nueva imagen"

#: src/components/details/DetailsImage.tsx:346
msgid "Upload Image"
msgstr "Subir Imagen"

#: src/components/details/DetailsImage.tsx:359
msgid "Delete image"
msgstr "Eliminar imagen"

#: src/components/details/DetailsImage.tsx:393
msgid "Download Image"
msgstr "Descargar imagen"

#: src/components/details/DetailsImage.tsx:398
msgid "Image downloaded successfully"
msgstr "Imagen descargada correctamente"

#: src/components/details/PartIcons.tsx:43
#~ msgid "Part is a template part (variants can be made from this part)"
#~ msgstr "Part is a template part (variants can be made from this part)"

#: src/components/details/PartIcons.tsx:49
#~ msgid "Part can be assembled from other parts"
#~ msgstr "Part can be assembled from other parts"

#: src/components/details/PartIcons.tsx:55
#~ msgid "Part can be used in assemblies"
#~ msgstr "Part can be used in assemblies"

#: src/components/details/PartIcons.tsx:61
#~ msgid "Part stock is tracked by serial number"
#~ msgstr "Part stock is tracked by serial number"

#: src/components/details/PartIcons.tsx:67
#~ msgid "Part can be purchased from external suppliers"
#~ msgstr "Part can be purchased from external suppliers"

#: src/components/details/PartIcons.tsx:73
#~ msgid "Part can be sold to customers"
#~ msgstr "Part can be sold to customers"

#: src/components/details/PartIcons.tsx:78
#~ msgid "Part is virtual (not a physical part)"
#~ msgstr "Part is virtual (not a physical part)"

#: src/components/editors/NotesEditor.tsx:75
msgid "Image upload failed"
msgstr "Error al cargar la imagen"

#: src/components/editors/NotesEditor.tsx:84
#: src/components/editors/NotesEditor.tsx:120
#: src/components/forms/ApiForm.tsx:500
#: src/pages/Index/Settings/AdminCenter/CurrencyManagementPanel.tsx:45
#: src/tables/bom/BomTable.tsx:446
msgid "Success"
msgstr "Completado"

#: src/components/editors/NotesEditor.tsx:85
msgid "Image uploaded successfully"
msgstr "Imagen cargada con éxito"

#: src/components/editors/NotesEditor.tsx:121
msgid "Notes saved successfully"
msgstr "Notas guardadas correctamente"

#: src/components/editors/NotesEditor.tsx:132
msgid "Failed to save notes"
msgstr "Error al guardar las notas"

#: src/components/editors/NotesEditor.tsx:135
msgid "Error Saving Notes"
msgstr "Error al guardar notas"

#: src/components/editors/NotesEditor.tsx:151
#~ msgid "Disable Editing"
#~ msgstr "Disable Editing"

#: src/components/editors/NotesEditor.tsx:155
msgid "Save Notes"
msgstr "Guardar notas"

#: src/components/editors/NotesEditor.tsx:174
msgid "Close Editor"
msgstr "Cerrar editor"

#: src/components/editors/NotesEditor.tsx:181
msgid "Enable Editing"
msgstr "Habilitar la edición"

#: src/components/editors/NotesEditor.tsx:198
#~ msgid "Preview Notes"
#~ msgstr "Preview Notes"

#: src/components/editors/NotesEditor.tsx:198
#~ msgid "Edit Notes"
#~ msgstr "Edit Notes"

#: src/components/editors/TemplateEditor/CodeEditor/index.tsx:9
msgid "Code"
msgstr "Código"

#: src/components/editors/TemplateEditor/PdfPreview/PdfPreview.tsx:44
#~ msgid "Failed to parse error response from server."
#~ msgstr "Failed to parse error response from server."

#: src/components/editors/TemplateEditor/PdfPreview/PdfPreview.tsx:81
msgid "Preview not available, click \"Reload Preview\"."
msgstr "Vista previa no disponible, haga clic en \"Recargar vista previa\"."

#: src/components/editors/TemplateEditor/PdfPreview/index.tsx:9
msgid "PDF Preview"
msgstr "Vista previa PDF"

#: src/components/editors/TemplateEditor/TemplateEditor.tsx:109
msgid "Error loading template"
msgstr "Error al cargar la plantilla"

#: src/components/editors/TemplateEditor/TemplateEditor.tsx:121
msgid "Error saving template"
msgstr "Error al guardar la plantilla"

#: src/components/editors/TemplateEditor/TemplateEditor.tsx:151
#~ msgid "Save & Reload preview?"
#~ msgstr "Save & Reload preview?"

#: src/components/editors/TemplateEditor/TemplateEditor.tsx:158
msgid "Could not load the template from the server."
msgstr "No se pudo cargar la plantilla del servidor."

#: src/components/editors/TemplateEditor/TemplateEditor.tsx:175
#: src/components/editors/TemplateEditor/TemplateEditor.tsx:306
msgid "Save & Reload Preview"
msgstr "Guardar y recargar vista previa"

#: src/components/editors/TemplateEditor/TemplateEditor.tsx:180
msgid "Are you sure you want to Save & Reload the preview?"
msgstr "¿Está seguro que desea guardar y recargar la vista previa?"

#: src/components/editors/TemplateEditor/TemplateEditor.tsx:182
msgid "To render the preview the current template needs to be replaced on the server with your modifications which may break the label if it is under active use. Do you want to proceed?"
msgstr "Para renderizar la vista previa la plantilla actual necesita ser reemplazada en el servidor con sus modificaciones que pueden romper la etiqueta si está en uso activo. ¿Quieres continuar?"

#: src/components/editors/TemplateEditor/TemplateEditor.tsx:186
msgid "Save & Reload"
msgstr "Guardar y recargar"

#: src/components/editors/TemplateEditor/TemplateEditor.tsx:218
msgid "Preview updated"
msgstr "Actualizar vista previa"

#: src/components/editors/TemplateEditor/TemplateEditor.tsx:219
msgid "The preview has been updated successfully."
msgstr "La vista previa se ha actualizado correctamente."

#: src/components/editors/TemplateEditor/TemplateEditor.tsx:263
#~ msgid "Save & Reload preview"
#~ msgstr "Save & Reload preview"

#: src/components/editors/TemplateEditor/TemplateEditor.tsx:298
msgid "Reload preview"
msgstr "Recargar vista previa"

#: src/components/editors/TemplateEditor/TemplateEditor.tsx:299
msgid "Use the currently stored template from the server"
msgstr "Usar la plantilla actualmente almacenada del servidor"

#: src/components/editors/TemplateEditor/TemplateEditor.tsx:307
msgid "Save the current template and reload the preview"
msgstr "Guardar la plantilla actual y recargar la vista previa"

#: src/components/editors/TemplateEditor/TemplateEditor.tsx:322
#~ msgid "to preview"
#~ msgstr "to preview"

#: src/components/editors/TemplateEditor/TemplateEditor.tsx:366
msgid "Select instance to preview"
msgstr "Seleccione la instancia a previsualizar"

#: src/components/editors/TemplateEditor/TemplateEditor.tsx:410
msgid "Error rendering template"
msgstr "Error al renderizar plantilla"

#: src/components/errors/ClientError.tsx:23
msgid "Client Error"
msgstr "Error del cliente"

#: src/components/errors/ClientError.tsx:24
msgid "Client error occurred"
msgstr "Ha ocurrido un error de cliente"

#: src/components/errors/GenericErrorPage.tsx:50
msgid "Status Code"
msgstr "Código de estado"

#: src/components/errors/GenericErrorPage.tsx:63
msgid "Return to the index page"
msgstr "Volver a la página índice"

#: src/components/errors/NotAuthenticated.tsx:8
msgid "Not Authenticated"
msgstr "No autenticado"

#: src/components/errors/NotAuthenticated.tsx:9
msgid "You are not logged in."
msgstr "No has iniciado sesión."

#: src/components/errors/NotFound.tsx:8
msgid "Page Not Found"
msgstr "Página no encontrada"

#: src/components/errors/NotFound.tsx:9
msgid "This page does not exist"
msgstr "Esta página no existe"

#: src/components/errors/PermissionDenied.tsx:8
#: src/functions/notifications.tsx:25
msgid "Permission Denied"
msgstr "Permiso denegado"

#: src/components/errors/PermissionDenied.tsx:9
msgid "You do not have permission to view this page."
msgstr "No tiene permisos para ver esta página."

#: src/components/errors/ServerError.tsx:8
msgid "Server Error"
msgstr "Error del servidor"

#: src/components/errors/ServerError.tsx:9
msgid "A server error occurred"
msgstr "Ha ocurrido un error con el servidor"

#: src/components/forms/ApiForm.tsx:163
#: src/components/forms/ApiForm.tsx:621
msgid "Form Error"
msgstr "Error de formulario"

#: src/components/forms/ApiForm.tsx:487
#~ msgid "Form Errors Exist"
#~ msgstr "Form Errors Exist"

#: src/components/forms/ApiForm.tsx:629
msgid "Errors exist for one or more form fields"
msgstr "Existen errores para uno o más campos del formulario"

#: src/components/forms/ApiForm.tsx:740
#: src/hooks/UseForm.tsx:131
#: src/tables/plugin/PluginListTable.tsx:203
msgid "Update"
msgstr "Actualizar"

#: src/components/forms/ApiForm.tsx:760
#: src/components/items/ActionDropdown.tsx:255
#: src/components/items/RoleTable.tsx:155
#: src/hooks/UseForm.tsx:162
#: src/pages/Notifications.tsx:109
#: src/tables/RowActions.tsx:75
#: src/tables/plugin/PluginListTable.tsx:242
msgid "Delete"
msgstr "Eliminar"

#: src/components/forms/AuthenticationForm.tsx:48
#: src/components/forms/AuthenticationForm.tsx:74
#: src/functions/auth.tsx:83
#~ msgid "Check your your input and try again."
#~ msgstr "Check your your input and try again."

#: src/components/forms/AuthenticationForm.tsx:52
#~ msgid "Welcome back!"
#~ msgstr "Welcome back!"

#: src/components/forms/AuthenticationForm.tsx:53
#~ msgid "Login successfull"
#~ msgstr "Login successfull"

#: src/components/forms/AuthenticationForm.tsx:64
msgid "Login successful"
msgstr "Inicio de sesión exitoso"

#: src/components/forms/AuthenticationForm.tsx:65
msgid "Logged in successfully"
msgstr "Se ha iniciado sesión con éxito"

#: src/components/forms/AuthenticationForm.tsx:65
#: src/functions/auth.tsx:74
#~ msgid "Mail delivery successfull"
#~ msgstr "Mail delivery successfull"

#: src/components/forms/AuthenticationForm.tsx:72
#: src/components/forms/AuthenticationForm.tsx:80
msgid "Login failed"
msgstr "Error al iniciar sesión"

#: src/components/forms/AuthenticationForm.tsx:73
#: src/components/forms/AuthenticationForm.tsx:81
#: src/components/forms/AuthenticationForm.tsx:97
#: src/functions/auth.tsx:244
msgid "Check your input and try again."
msgstr "Verifique su entrada e intente nuevamente."

#: src/components/forms/AuthenticationForm.tsx:91
#: src/functions/auth.tsx:235
msgid "Mail delivery successful"
msgstr "Envío de correo exitoso"

#: src/components/forms/AuthenticationForm.tsx:92
msgid "Check your inbox for the login link. If you have an account, you will receive a login link. Check in spam too."
msgstr "Revisa tu bandeja de entrada para el enlace de inicio de sesión. Si tienes una cuenta, recibirás un enlace de inicio de sesión. Revisa también el correo no deseado."

#: src/components/forms/AuthenticationForm.tsx:96
msgid "Mail delivery failed"
msgstr "Error al enviar el correo"

#: src/components/forms/AuthenticationForm.tsx:116
msgid "Or continue with other methods"
msgstr "O continúe con otros métodos"

#: src/components/forms/AuthenticationForm.tsx:127
#: src/components/forms/AuthenticationForm.tsx:278
#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:64
#: src/pages/core/UserDetail.tsx:48
msgid "Username"
msgstr "Nombre de usuario"

#: src/components/forms/AuthenticationForm.tsx:129
#: src/components/forms/AuthenticationForm.tsx:280
msgid "Your username"
msgstr "Tu nombre de usuario"

#: src/components/forms/AuthenticationForm.tsx:134
#: src/components/forms/AuthenticationForm.tsx:293
#: src/pages/Auth/ResetPassword.tsx:34
#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:703
msgid "Password"
msgstr "Contraseña"

#: src/components/forms/AuthenticationForm.tsx:136
#: src/components/forms/AuthenticationForm.tsx:295
msgid "Your password"
msgstr "Tu contraseña"

#: src/components/forms/AuthenticationForm.tsx:136
#~ msgid "I will use username and password"
#~ msgstr "I will use username and password"

#: src/components/forms/AuthenticationForm.tsx:148
msgid "Reset password"
msgstr "Restablecer contraseña"

#: src/components/forms/AuthenticationForm.tsx:157
#: src/components/forms/AuthenticationForm.tsx:285
#: src/pages/Auth/Reset.tsx:17
#: src/pages/core/UserDetail.tsx:71
msgid "Email"
msgstr "Correo electrónico"

#: src/components/forms/AuthenticationForm.tsx:158
#: src/pages/Auth/Reset.tsx:18
msgid "We will send you a link to login - if you are registered"
msgstr "Te enviaremos un enlace para iniciar sesión - si estás registrado"

#: src/components/forms/AuthenticationForm.tsx:174
msgid "Send me an email"
msgstr "Envíame un correo electrónico"

#: src/components/forms/AuthenticationForm.tsx:176
msgid "Use username and password"
msgstr "Usar nombre de usuario y contraseña"

#: src/components/forms/AuthenticationForm.tsx:185
msgid "Log In"
msgstr "Iniciar sesión"

#: src/components/forms/AuthenticationForm.tsx:187
#: src/pages/Auth/Reset.tsx:26
msgid "Send Email"
msgstr "Enviar correo electrónico"

#: src/components/forms/AuthenticationForm.tsx:221
msgid "Passwords do not match"
msgstr "Las contraseñas no coinciden"

#: src/components/forms/AuthenticationForm.tsx:238
msgid "Registration successful"
msgstr "Registro exitoso"

#: src/components/forms/AuthenticationForm.tsx:239
msgid "Please confirm your email address to complete the registration"
msgstr "Por favor, confirma tu dirección de correo electrónico para completar el registro"

#: src/components/forms/AuthenticationForm.tsx:262
msgid "Input error"
msgstr "Error de entrada"

#: src/components/forms/AuthenticationForm.tsx:263
msgid "Check your input and try again. "
msgstr ""

#: src/components/forms/AuthenticationForm.tsx:287
msgid "This will be used for a confirmation"
msgstr "Se utilizará para una confirmación"

#: src/components/forms/AuthenticationForm.tsx:300
msgid "Password repeat"
msgstr "Repetición de contraseña"

#: src/components/forms/AuthenticationForm.tsx:302
msgid "Repeat password"
msgstr "Repetir contraseña"

#: src/components/forms/AuthenticationForm.tsx:314
#: src/pages/Auth/Login.tsx:120
#: src/pages/Auth/Register.tsx:13
msgid "Register"
msgstr "Registro"

#: src/components/forms/AuthenticationForm.tsx:320
msgid "Or use SSO"
msgstr "O usar SSO"

#: src/components/forms/HostOptionsForm.tsx:37
#: src/components/forms/HostOptionsForm.tsx:68
msgid "Host"
msgstr "Servidor"

#: src/components/forms/HostOptionsForm.tsx:43
#: src/components/forms/HostOptionsForm.tsx:71
#: src/components/forms/InstanceOptions.tsx:125
#: src/components/plugins/PluginDrawer.tsx:68
#: src/pages/Index/Settings/AdminCenter/UnitManagementPanel.tsx:19
#: src/pages/part/CategoryDetail.tsx:87
#: src/pages/part/PartDetail.tsx:165
#: src/pages/stock/LocationDetail.tsx:85
#: src/tables/machine/MachineTypeTable.tsx:71
#: src/tables/machine/MachineTypeTable.tsx:120
#: src/tables/machine/MachineTypeTable.tsx:238
#: src/tables/machine/MachineTypeTable.tsx:341
#: src/tables/plugin/PluginErrorTable.tsx:33
#: src/tables/purchasing/ManufacturerPartParameterTable.tsx:32
#: src/tables/settings/ApiTokenTable.tsx:56
#: src/tables/settings/GroupTable.tsx:90
#: src/tables/settings/GroupTable.tsx:143
#: src/tables/settings/GroupTable.tsx:184
#: src/tables/settings/PendingTasksTable.tsx:32
#: src/tables/stock/LocationTypesTable.tsx:69
msgid "Name"
msgstr "Nombre"

#: src/components/forms/HostOptionsForm.tsx:76
msgid "No one here..."
msgstr "Nadie aquí..."

#: src/components/forms/HostOptionsForm.tsx:87
msgid "Add Host"
msgstr "Añadir servidor"

#: src/components/forms/HostOptionsForm.tsx:91
#: src/components/items/RoleTable.tsx:224
#: src/components/items/TransferList.tsx:215
#: src/components/items/TransferList.tsx:223
msgid "Save"
msgstr "Guardar"

#: src/components/forms/InstanceOptions.tsx:43
#~ msgid "Select destination instance"
#~ msgstr "Select destination instance"

#: src/components/forms/InstanceOptions.tsx:59
msgid "Select Server"
msgstr "Selecciona un servidor"

#: src/components/forms/InstanceOptions.tsx:69
#: src/components/forms/InstanceOptions.tsx:93
msgid "Edit host options"
msgstr "Editar las opciones del host"

#: src/components/forms/InstanceOptions.tsx:71
#~ msgid "Edit possible host options"
#~ msgstr "Edit possible host options"

#: src/components/forms/InstanceOptions.tsx:77
msgid "Save host selection"
msgstr ""

#: src/components/forms/InstanceOptions.tsx:98
#~ msgid "Version: {0}"
#~ msgstr "Version: {0}"

#: src/components/forms/InstanceOptions.tsx:100
#~ msgid "API:{0}"
#~ msgstr "API:{0}"

#: src/components/forms/InstanceOptions.tsx:102
#~ msgid "Name: {0}"
#~ msgstr "Name: {0}"

#: src/components/forms/InstanceOptions.tsx:104
#~ msgid "State: <0>worker</0> ({0}), <1>plugins</1>{1}"
#~ msgstr "State: <0>worker</0> ({0}), <1>plugins</1>{1}"

#: src/components/forms/InstanceOptions.tsx:119
#: src/components/modals/ServerInfoModal.tsx:19
#: src/pages/Index/Settings/SystemSettings.tsx:38
msgid "Server"
msgstr "Servidor"

#: src/components/forms/InstanceOptions.tsx:131
#: src/components/plugins/PluginDrawer.tsx:88
#: src/tables/plugin/PluginListTable.tsx:126
msgid "Version"
msgstr "Versión"

#: src/components/forms/InstanceOptions.tsx:137
#: src/components/modals/AboutInvenTreeModal.tsx:107
#: src/components/modals/ServerInfoModal.tsx:37
msgid "API Version"
msgstr "Versión de API"

#: src/components/forms/InstanceOptions.tsx:143
#: src/components/nav/NavigationDrawer.tsx:205
#: src/pages/Index/Settings/AdminCenter/Index.tsx:213
#: src/pages/Index/Settings/AdminCenter/PluginManagementPanel.tsx:45
msgid "Plugins"
msgstr "Complementos"

#: src/components/forms/InstanceOptions.tsx:144
#: src/tables/part/PartTestTemplateTable.tsx:117
#: src/tables/settings/TemplateTable.tsx:249
#: src/tables/settings/TemplateTable.tsx:365
#: src/tables/stock/StockItemTestResultTable.tsx:402
msgid "Enabled"
msgstr "Habilitado"

#: src/components/forms/InstanceOptions.tsx:144
msgid "Disabled"
msgstr "Deshabilitado"

#: src/components/forms/InstanceOptions.tsx:150
msgid "Worker"
msgstr ""

#: src/components/forms/InstanceOptions.tsx:151
#: src/tables/settings/FailedTasksTable.tsx:48
msgid "Stopped"
msgstr "Detenido"

#: src/components/forms/InstanceOptions.tsx:151
msgid "Running"
msgstr "En ejecución"

#: src/components/forms/fields/IconField.tsx:82
msgid "No icon selected"
msgstr "Ningún icono seleccionado"

#: src/components/forms/fields/IconField.tsx:160
msgid "Uncategorized"
msgstr "No clasificado"

#: src/components/forms/fields/IconField.tsx:210
#: src/components/nav/Layout.tsx:77
#: src/tables/part/PartThumbTable.tsx:201
msgid "Search..."
msgstr "Búsqueda..."

#: src/components/forms/fields/IconField.tsx:224
msgid "Select category"
msgstr "Seleccionar categoría"

#: src/components/forms/fields/IconField.tsx:233
msgid "Select pack"
msgstr "Seleccionar paquete"

#. placeholder {0}: filteredIcons.length
#: src/components/forms/fields/IconField.tsx:238
msgid "{0} icons"
msgstr "Iconos {0}"

#: src/components/forms/fields/RelatedModelField.tsx:320
#: src/components/nav/Header.tsx:131
#: src/pages/Index/Settings/UserSettings.tsx:67
#: src/tables/Search.tsx:27
msgid "Search"
msgstr "Buscar"

#: src/components/forms/fields/RelatedModelField.tsx:321
#: src/components/modals/AboutInvenTreeModal.tsx:79
#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:331
msgid "Loading"
msgstr "Cargando"

#: src/components/forms/fields/RelatedModelField.tsx:323
msgid "No results found"
msgstr "No hay resultados"

#: src/components/forms/fields/TableField.tsx:46
msgid "modelRenderer entry required for tables"
msgstr "entrada de modelRenderer requerida para tablas"

#: src/components/forms/fields/TableField.tsx:187
msgid "No entries available"
msgstr "No hay entradas disponibles"

#: src/components/forms/fields/TableField.tsx:198
msgid "Add new row"
msgstr "Añadir fila nueva"

#: src/components/images/DetailsImage.tsx:252
#~ msgid "Select image"
#~ msgstr "Select image"

#: src/components/images/Thumbnail.tsx:12
msgid "Thumbnail"
msgstr "Miniatura"

#: src/components/importer/ImportDataSelector.tsx:171
msgid "Importing Rows"
msgstr "Importando filas"

#: src/components/importer/ImportDataSelector.tsx:172
msgid "Please wait while the data is imported"
msgstr "Por favor espere mientras los datos son importados"

#: src/components/importer/ImportDataSelector.tsx:189
msgid "An error occurred while importing data"
msgstr "Se ha producido un error al importar datos"

#: src/components/importer/ImportDataSelector.tsx:210
msgid "Edit Data"
msgstr "Editar datos"

#: src/components/importer/ImportDataSelector.tsx:238
msgid "Delete Row"
msgstr "Eliminar fila"

#: src/components/importer/ImportDataSelector.tsx:268
msgid "Row"
msgstr "Fila"

#: src/components/importer/ImportDataSelector.tsx:286
msgid "Row contains errors"
msgstr "La fila contiene errores"

#: src/components/importer/ImportDataSelector.tsx:327
msgid "Accept"
msgstr "Aceptar"

#: src/components/importer/ImportDataSelector.tsx:360
msgid "Valid"
msgstr "Válido"

#: src/components/importer/ImportDataSelector.tsx:361
msgid "Filter by row validation status"
msgstr "Filtrar por estado de validación de fila"

#: src/components/importer/ImportDataSelector.tsx:366
#: src/components/wizards/WizardDrawer.tsx:101
#: src/tables/build/BuildOutputTable.tsx:414
msgid "Complete"
msgstr "Completado"

#: src/components/importer/ImportDataSelector.tsx:367
msgid "Filter by row completion status"
msgstr "Filtrar por estado de finalización de fila"

#: src/components/importer/ImportDataSelector.tsx:385
msgid "Import selected rows"
msgstr "Importar filas seleccionadas"

#: src/components/importer/ImportDataSelector.tsx:400
msgid "Processing Data"
msgstr "Procesando datos"

#: src/components/importer/ImporterColumnSelector.tsx:55
#: src/components/importer/ImporterColumnSelector.tsx:185
#: src/components/items/ErrorItem.tsx:12
#: src/functions/api.tsx:57
#: src/functions/auth.tsx:283
msgid "An error occurred"
msgstr "Se ha producido un error"

#: src/components/importer/ImporterColumnSelector.tsx:67
msgid "Select column, or leave blank to ignore this field."
msgstr "Seleccione la columna o deje en blanco para ignorar este campo."

#: src/components/importer/ImporterColumnSelector.tsx:91
#~ msgid "Select a column from the data file"
#~ msgstr "Select a column from the data file"

#: src/components/importer/ImporterColumnSelector.tsx:104
#~ msgid "Map data columns to database fields"
#~ msgstr "Map data columns to database fields"

#: src/components/importer/ImporterColumnSelector.tsx:119
#~ msgid "Imported Column Name"
#~ msgstr "Imported Column Name"

#: src/components/importer/ImporterColumnSelector.tsx:191
msgid "Ignore this field"
msgstr "Ignorar este campo"

#: src/components/importer/ImporterColumnSelector.tsx:205
msgid "Mapping data columns to database fields"
msgstr "Mapear datos de columnas a campos de la base de datos"

#: src/components/importer/ImporterColumnSelector.tsx:210
msgid "Accept Column Mapping"
msgstr "Aceptar mapeo de columnas"

#: src/components/importer/ImporterColumnSelector.tsx:223
msgid "Database Field"
msgstr "Cambo de base de datos"

#: src/components/importer/ImporterColumnSelector.tsx:224
msgid "Field Description"
msgstr "Descripción del campo"

#: src/components/importer/ImporterColumnSelector.tsx:225
msgid "Imported Column"
msgstr "Columna importada"

#: src/components/importer/ImporterColumnSelector.tsx:226
msgid "Default Value"
msgstr "Valor por defecto"

#: src/components/importer/ImporterDrawer.tsx:46
msgid "Upload File"
msgstr "Subir archivo"

#: src/components/importer/ImporterDrawer.tsx:47
msgid "Map Columns"
msgstr "Mapear columnas"

#: src/components/importer/ImporterDrawer.tsx:48
msgid "Import Data"
msgstr "Importar datos"

#: src/components/importer/ImporterDrawer.tsx:49
msgid "Process Data"
msgstr "Procesar datos"

#: src/components/importer/ImporterDrawer.tsx:50
msgid "Complete Import"
msgstr "Completar importación"

#: src/components/importer/ImporterDrawer.tsx:97
#~ msgid "Cancel import session"
#~ msgstr "Cancel import session"

#: src/components/importer/ImporterDrawer.tsx:107
msgid "Import Complete"
msgstr "Importación completada"

#: src/components/importer/ImporterDrawer.tsx:110
msgid "Data has been imported successfully"
msgstr "Los datos se han importado satisfactoriamente"

#: src/components/importer/ImporterDrawer.tsx:112
#: src/components/importer/ImporterDrawer.tsx:121
#: src/components/modals/AboutInvenTreeModal.tsx:182
#: src/components/modals/ServerInfoModal.tsx:131
msgid "Close"
msgstr "Cerrar"

#: src/components/importer/ImporterDrawer.tsx:118
msgid "Unknown Status"
msgstr "Estado desconocido"

#: src/components/importer/ImporterDrawer.tsx:119
msgid "Import session has unknown status"
msgstr "La sesión de importación tiene estado desconocido"

#: src/components/importer/ImporterDrawer.tsx:138
msgid "Importing Data"
msgstr "Importando datos"

#: src/components/importer/ImporterImportProgress.tsx:36
msgid "Importing Records"
msgstr "Importando registros"

#: src/components/importer/ImporterImportProgress.tsx:39
#: src/tables/settings/ImportSessionTable.tsx:80
msgid "Imported Rows"
msgstr ""

#: src/components/importer/ImporterImportProgress.tsx:39
#~ msgid "Imported rows"
#~ msgstr "Imported rows"

#: src/components/items/ActionDropdown.tsx:133
msgid "Options"
msgstr "Opciones"

#: src/components/items/ActionDropdown.tsx:162
#~ msgid "Link custom barcode"
#~ msgstr "Link custom barcode"

#: src/components/items/ActionDropdown.tsx:169
#: src/tables/InvenTreeTableHeader.tsx:181
#: src/tables/InvenTreeTableHeader.tsx:182
msgid "Barcode Actions"
msgstr "Acciones de código de barras"

#: src/components/items/ActionDropdown.tsx:174
msgid "View Barcode"
msgstr "Ver código de barras"

#: src/components/items/ActionDropdown.tsx:176
msgid "View barcode"
msgstr "Ver código de barras"

#: src/components/items/ActionDropdown.tsx:182
msgid "Link Barcode"
msgstr "Vincular Código de Barras"

#: src/components/items/ActionDropdown.tsx:184
msgid "Link a custom barcode to this item"
msgstr "Vincular un código de barras personalizado a este elemento"

#: src/components/items/ActionDropdown.tsx:192
msgid "Unlink custom barcode"
msgstr "Desvincular código de barras personalizado"

#: src/components/items/ActionDropdown.tsx:243
#: src/tables/RowActions.tsx:65
msgid "Edit"
msgstr "Editar"

#: src/components/items/ActionDropdown.tsx:244
msgid "Edit item"
msgstr "Editar elemento"

#: src/components/items/ActionDropdown.tsx:256
msgid "Delete item"
msgstr "Eliminar elemento"

#: src/components/items/ActionDropdown.tsx:264
#: src/components/items/ActionDropdown.tsx:265
msgid "Hold"
msgstr "Mantener"

#: src/components/items/ActionDropdown.tsx:287
#: src/pages/Index/Scan.tsx:64
#: src/tables/RowActions.tsx:55
msgid "Duplicate"
msgstr "Duplicar"

#: src/components/items/ActionDropdown.tsx:288
msgid "Duplicate item"
msgstr "Duplicar elemento"

#: src/components/items/BarcodeInput.tsx:24
#~ msgid "Scan barcode data here using barcode scanner"
#~ msgstr "Scan barcode data here using barcode scanner"

#: src/components/items/ColorToggle.tsx:17
msgid "Toggle color scheme"
msgstr "Cambiar el esquema de colores"

#: src/components/items/DocTooltip.tsx:92
#: src/components/items/GettingStartedCarousel.tsx:20
msgid "Read More"
msgstr "Leer más"

#: src/components/items/ErrorItem.tsx:8
#: src/tables/InvenTreeTable.tsx:504
msgid "Unknown error"
msgstr "Error desconocido"

#: src/components/items/ErrorItem.tsx:13
#~ msgid "An error occurred:"
#~ msgstr "An error occurred:"

#: src/components/items/GettingStartedCarousel.tsx:27
#~ msgid "Read more"
#~ msgstr "Read more"

#: src/components/items/InfoItem.tsx:27
msgid "None"
msgstr "Nada"

#: src/components/items/InvenTreeLogo.tsx:23
msgid "InvenTree Logo"
msgstr "Logo de InvenTree"

#: src/components/items/LanguageToggle.tsx:20
msgid "Select language"
msgstr "Selecciona el idioma"

#: src/components/items/OnlyStaff.tsx:9
#: src/components/modals/AboutInvenTreeModal.tsx:46
msgid "This information is only available for staff users"
msgstr "Esta información sólo está disponible para usuarios del personal"

#: src/components/items/Placeholder.tsx:14
#~ msgid "This feature/button/site is a placeholder for a feature that is not implemented, only partial or intended for testing."
#~ msgstr "This feature/button/site is a placeholder for a feature that is not implemented, only partial or intended for testing."

#: src/components/items/Placeholder.tsx:17
#~ msgid "PLH"
#~ msgstr "PLH"

#: src/components/items/RoleTable.tsx:81
msgid "Updating"
msgstr ""

#: src/components/items/RoleTable.tsx:82
msgid "Updating group roles"
msgstr ""

#: src/components/items/RoleTable.tsx:118
#: src/pages/part/pricing/BomPricingPanel.tsx:193
#: src/pages/part/pricing/VariantPricingPanel.tsx:53
#: src/tables/purchasing/SupplierPartTable.tsx:151
msgid "Updated"
msgstr "Actualizado"

#: src/components/items/RoleTable.tsx:119
msgid "Group roles updated"
msgstr ""

#: src/components/items/RoleTable.tsx:135
msgid "Role"
msgstr ""

#: src/components/items/RoleTable.tsx:140
#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:412
msgid "View"
msgstr ""

#: src/components/items/RoleTable.tsx:145
msgid "Change"
msgstr ""

#: src/components/items/RoleTable.tsx:150
#: src/forms/StockForms.tsx:788
#: src/pages/stock/StockDetail.tsx:719
#: src/tables/stock/StockItemTestResultTable.tsx:350
msgid "Add"
msgstr "Agregar"

#: src/components/items/RoleTable.tsx:203
msgid "Reset group roles"
msgstr ""

#: src/components/items/RoleTable.tsx:212
msgid "Reset"
msgstr ""

#: src/components/items/RoleTable.tsx:215
msgid "Save group roles"
msgstr ""

#: src/components/items/TransferList.tsx:65
msgid "No items"
msgstr ""

#: src/components/items/TransferList.tsx:161
#: src/pages/part/PartDetail.tsx:755
#: src/pages/stock/StockDetail.tsx:191
#: src/pages/stock/StockDetail.tsx:849
#: src/tables/build/BuildLineTable.tsx:186
#: src/tables/part/PartTable.tsx:122
#: src/tables/stock/StockItemTable.tsx:172
#: src/tables/stock/StockItemTable.tsx:327
msgid "Available"
msgstr "Disponible"

#: src/components/items/TransferList.tsx:162
msgid "Selected"
msgstr ""

#: src/components/modals/AboutInvenTreeModal.tsx:101
msgid "InvenTree Version"
msgstr "Versión de InvenTree"

#: src/components/modals/AboutInvenTreeModal.tsx:103
#~ msgid "Your InvenTree version status is"
#~ msgstr "Your InvenTree version status is"

#: src/components/modals/AboutInvenTreeModal.tsx:113
msgid "Python Version"
msgstr "Versión de Python"

#: src/components/modals/AboutInvenTreeModal.tsx:118
msgid "Django Version"
msgstr "Versión de Django"

#: src/components/modals/AboutInvenTreeModal.tsx:127
msgid "Commit Hash"
msgstr "Hash de Commit"

#: src/components/modals/AboutInvenTreeModal.tsx:132
msgid "Commit Date"
msgstr "Fecha del Commit"

#: src/components/modals/AboutInvenTreeModal.tsx:137
msgid "Commit Branch"
msgstr "Rama de Commit"

#: src/components/modals/AboutInvenTreeModal.tsx:148
msgid "Version Information"
msgstr "Información de la versión"

#: src/components/modals/AboutInvenTreeModal.tsx:157
msgid "Links"
msgstr "Enlaces"

#: src/components/modals/AboutInvenTreeModal.tsx:163
#: src/components/nav/NavigationDrawer.tsx:217
#: src/defaults/actions.tsx:32
msgid "Documentation"
msgstr "Documentación"

#: src/components/modals/AboutInvenTreeModal.tsx:164
msgid "Source Code"
msgstr "Código Fuente"

#: src/components/modals/AboutInvenTreeModal.tsx:165
msgid "Mobile App"
msgstr "Aplicación Móvil"

#: src/components/modals/AboutInvenTreeModal.tsx:165
#~ msgid "Credits"
#~ msgstr "Credits"

#: src/components/modals/AboutInvenTreeModal.tsx:166
msgid "Submit Bug Report"
msgstr "Enviar Informe de Error"

#: src/components/modals/AboutInvenTreeModal.tsx:168
#~ msgid "InvenTree Documentation"
#~ msgstr "InvenTree Documentation"

#: src/components/modals/AboutInvenTreeModal.tsx:169
#~ msgid "View Code on GitHub"
#~ msgstr "View Code on GitHub"

#: src/components/modals/AboutInvenTreeModal.tsx:175
msgid "Copy version information"
msgstr "Copiar información de versión"

#: src/components/modals/AboutInvenTreeModal.tsx:189
#: src/components/modals/ServerInfoModal.tsx:147
#~ msgid "Dismiss"
#~ msgstr "Dismiss"

#: src/components/modals/AboutInvenTreeModal.tsx:192
msgid "Development Version"
msgstr "Versión de Desarrollo"

#: src/components/modals/AboutInvenTreeModal.tsx:194
msgid "Up to Date"
msgstr "Actualizado"

#: src/components/modals/AboutInvenTreeModal.tsx:196
msgid "Update Available"
msgstr "Actualización Disponible"

#: src/components/modals/LicenseModal.tsx:41
msgid "No license text available"
msgstr "Texto de licencia no disponible"

#: src/components/modals/LicenseModal.tsx:48
msgid "No Information provided - this is likely a server issue"
msgstr "No se proporciona información - esto es probablemente un problema del servidor"

#: src/components/modals/LicenseModal.tsx:84
msgid "Loading license information"
msgstr "Cargando información de licencia"

#: src/components/modals/LicenseModal.tsx:90
msgid "Failed to fetch license information"
msgstr "Error al obtener la información de la licencia"

#: src/components/modals/LicenseModal.tsx:102
msgid "{key} Packages"
msgstr "Paquetes {key}"

#: src/components/modals/QrCodeModal.tsx:24
#~ msgid "Unknown response"
#~ msgstr "Unknown response"

#: src/components/modals/QrCodeModal.tsx:39
#~ msgid "No scans yet!"
#~ msgstr "No scans yet!"

#: src/components/modals/QrCodeModal.tsx:57
#~ msgid "Close modal"
#~ msgstr "Close modal"

#: src/components/modals/ServerInfoModal.tsx:25
msgid "Instance Name"
msgstr "Nombre de instancia"

#: src/components/modals/ServerInfoModal.tsx:31
msgid "Server Version"
msgstr "Versión del servidor"

#: src/components/modals/ServerInfoModal.tsx:38
#~ msgid "Bebug Mode"
#~ msgstr "Bebug Mode"

#: src/components/modals/ServerInfoModal.tsx:43
msgid "Database"
msgstr "Base de datos"

#: src/components/modals/ServerInfoModal.tsx:52
#: src/components/nav/Alerts.tsx:40
msgid "Debug Mode"
msgstr "Modo de depuración"

#: src/components/modals/ServerInfoModal.tsx:56
msgid "Server is running in debug mode"
msgstr "El servidor se está ejecutando en modo depuración"

#: src/components/modals/ServerInfoModal.tsx:63
msgid "Docker Mode"
msgstr "Modo Docker"

#: src/components/modals/ServerInfoModal.tsx:66
msgid "Server is deployed using docker"
msgstr "El servidor está desplegado usando Docker"

#: src/components/modals/ServerInfoModal.tsx:72
msgid "Plugin Support"
msgstr "Soporte para complementos"

#: src/components/modals/ServerInfoModal.tsx:77
msgid "Plugin support enabled"
msgstr "Soporte de complementos habilitado"

#: src/components/modals/ServerInfoModal.tsx:79
msgid "Plugin support disabled"
msgstr "Soporte de complementos deshabilitado"

#: src/components/modals/ServerInfoModal.tsx:86
msgid "Server status"
msgstr "Estado del servidor"

#: src/components/modals/ServerInfoModal.tsx:92
msgid "Healthy"
msgstr "Saludable"

#: src/components/modals/ServerInfoModal.tsx:94
msgid "Issues detected"
msgstr "Problemas detectados"

#: src/components/modals/ServerInfoModal.tsx:103
#: src/components/nav/Alerts.tsx:49
msgid "Background Worker"
msgstr "Trabajador en segundo plano"

#: src/components/modals/ServerInfoModal.tsx:107
msgid "The Background worker process is not running."
msgstr ""

#: src/components/modals/ServerInfoModal.tsx:114
msgid "Email Settings"
msgstr "Ajustes del correo electrónico"

#: src/components/modals/ServerInfoModal.tsx:118
#: src/components/nav/Alerts.tsx:60
msgid "Email settings not configured."
msgstr ""

#: src/components/modals/ServerInfoModal.tsx:118
#~ msgid "Email settings not configured"
#~ msgstr "Email settings not configured"

#: src/components/nav/Alerts.tsx:42
msgid "The server is running in debug mode."
msgstr ""

#: src/components/nav/Alerts.tsx:51
msgid "The background worker process is not running."
msgstr ""

#: src/components/nav/Alerts.tsx:58
msgid "Email settings"
msgstr ""

#: src/components/nav/Alerts.tsx:67
msgid "Server Restart"
msgstr ""

#: src/components/nav/Alerts.tsx:69
msgid "The server requires a restart to apply changes."
msgstr ""

#: src/components/nav/Alerts.tsx:79
msgid "Database Migrations"
msgstr ""

#: src/components/nav/Alerts.tsx:81
msgid "There are pending database migrations."
msgstr ""

#: src/components/nav/Alerts.tsx:97
msgid "Alerts"
msgstr ""

#: src/components/nav/Alerts.tsx:140
msgid "Learn more about {code}"
msgstr ""

#: src/components/nav/Header.tsx:150
#: src/components/nav/NavigationDrawer.tsx:141
#: src/components/nav/NotificationDrawer.tsx:179
#: src/pages/Index/Settings/SystemSettings.tsx:112
#: src/pages/Index/Settings/UserSettings.tsx:99
#: src/pages/Notifications.tsx:45
#: src/pages/Notifications.tsx:130
msgid "Notifications"
msgstr "Notificaciones"

#: src/components/nav/Layout.tsx:80
msgid "Nothing found..."
msgstr "No se encontró nada..."

#: src/components/nav/MainMenu.tsx:40
#: src/pages/Index/Profile/Profile.tsx:15
#~ msgid "Profile"
#~ msgstr "Profile"

#: src/components/nav/MainMenu.tsx:52
#: src/components/nav/NavigationDrawer.tsx:193
#: src/pages/Index/Settings/AdminCenter/UserManagementPanel.tsx:39
msgid "Settings"
msgstr "Ajustes"

#: src/components/nav/MainMenu.tsx:59
#: src/pages/Index/Settings/UserSettings.tsx:126
msgid "Account Settings"
msgstr "Ajustes de la cuenta"

#: src/components/nav/MainMenu.tsx:59
#: src/defaults/menuItems.tsx:15
#~ msgid "Account settings"
#~ msgstr "Account settings"

#: src/components/nav/MainMenu.tsx:67
#: src/components/nav/NavigationDrawer.tsx:153
#: src/components/nav/SettingsHeader.tsx:41
#: src/pages/Index/Settings/SystemSettings.tsx:319
#: src/pages/Index/Settings/SystemSettings.tsx:324
msgid "System Settings"
msgstr "Ajustes del sistema"

#: src/components/nav/MainMenu.tsx:68
#~ msgid "Current language {locale}"
#~ msgstr "Current language {locale}"

#: src/components/nav/MainMenu.tsx:71
#~ msgid "Switch to pseudo language"
#~ msgstr "Switch to pseudo language"

#: src/components/nav/MainMenu.tsx:86
#: src/components/nav/NavigationDrawer.tsx:160
#: src/components/nav/SettingsHeader.tsx:42
#: src/defaults/actions.tsx:80
#: src/pages/Index/Settings/AdminCenter/Index.tsx:230
#: src/pages/Index/Settings/AdminCenter/Index.tsx:235
msgid "Admin Center"
msgstr "Centro de administración"

#: src/components/nav/MainMenu.tsx:96
msgid "Logout"
msgstr "Cerrar sesión"

#: src/components/nav/NavHoverMenu.tsx:84
#~ msgid "View all"
#~ msgstr "View all"

#: src/components/nav/NavHoverMenu.tsx:100
#: src/components/nav/NavHoverMenu.tsx:110
#~ msgid "Get started"
#~ msgstr "Get started"

#: src/components/nav/NavHoverMenu.tsx:103
#~ msgid "Overview over high-level objects, functions and possible usecases."
#~ msgstr "Overview over high-level objects, functions and possible usecases."

#: src/components/nav/NavigationDrawer.tsx:60
#~ msgid "Pages"
#~ msgstr "Pages"

#: src/components/nav/NavigationDrawer.tsx:77
#: src/components/render/ModelType.tsx:30
#: src/defaults/links.tsx:36
#: src/enums/Roles.tsx:36
#: src/pages/Index/Settings/SystemSettings.tsx:184
#: src/pages/part/CategoryDetail.tsx:131
#: src/pages/part/CategoryDetail.tsx:274
#: src/pages/part/CategoryDetail.tsx:313
#: src/pages/part/PartDetail.tsx:731
msgid "Parts"
msgstr "Piezas"

#: src/components/nav/NavigationDrawer.tsx:84
#: src/components/render/Part.tsx:30
#: src/defaults/links.tsx:42
#: src/forms/StockForms.tsx:704
#: src/pages/Index/Settings/SystemSettings.tsx:217
#: src/pages/part/PartDetail.tsx:498
#: src/pages/stock/LocationDetail.tsx:351
#: src/pages/stock/StockDetail.tsx:548
#: src/tables/stock/StockItemTable.tsx:75
msgid "Stock"
msgstr "Existencias"

#: src/components/nav/NavigationDrawer.tsx:91
#: src/defaults/links.tsx:48
#: src/pages/build/BuildDetail.tsx:559
#: src/pages/build/BuildIndex.tsx:90
msgid "Manufacturing"
msgstr "Fabricación"

#: src/components/nav/NavigationDrawer.tsx:98
#: src/defaults/links.tsx:54
#: src/pages/company/ManufacturerDetail.tsx:9
#: src/pages/company/ManufacturerPartDetail.tsx:261
#: src/pages/company/SupplierDetail.tsx:9
#: src/pages/company/SupplierPartDetail.tsx:353
#: src/pages/purchasing/PurchaseOrderDetail.tsx:524
#: src/pages/purchasing/PurchasingIndex.tsx:122
msgid "Purchasing"
msgstr "Compras"

#: src/components/nav/NavigationDrawer.tsx:105
#: src/defaults/links.tsx:60
#: src/pages/company/CustomerDetail.tsx:9
#: src/pages/sales/ReturnOrderDetail.tsx:511
#: src/pages/sales/SalesIndex.tsx:139
#: src/pages/sales/SalesOrderDetail.tsx:575
#: src/pages/sales/SalesOrderShipmentDetail.tsx:362
msgid "Sales"
msgstr "Ventas"

#: src/components/nav/NavigationDrawer.tsx:112
#: src/components/render/ModelType.tsx:215
#: src/pages/Index/Settings/AdminCenter/UserManagementPanel.tsx:15
#: src/pages/core/CoreIndex.tsx:21
#: src/pages/core/UserDetail.tsx:226
msgid "Users"
msgstr "Usuarios"

#: src/components/nav/NavigationDrawer.tsx:118
#: src/components/render/ModelType.tsx:222
#: src/pages/Index/Settings/AdminCenter/UserManagementPanel.tsx:23
#: src/pages/core/CoreIndex.tsx:27
#: src/pages/core/GroupDetail.tsx:82
#: src/pages/core/UserDetail.tsx:99
#: src/tables/settings/UserTable.tsx:265
msgid "Groups"
msgstr "Grupos"

#: src/components/nav/NavigationDrawer.tsx:147
#: src/components/nav/SettingsHeader.tsx:40
#: src/pages/Index/Settings/UserSettings.tsx:122
msgid "User Settings"
msgstr "Ajustes del usuario"

#: src/components/nav/NavigationDrawer.tsx:188
msgid "Navigation"
msgstr "Navegación"

#: src/components/nav/NavigationDrawer.tsx:198
#: src/forms/PurchaseOrderForms.tsx:755
#: src/forms/StockForms.tsx:706
#: src/forms/StockForms.tsx:750
#: src/forms/StockForms.tsx:789
#: src/forms/StockForms.tsx:825
#: src/forms/StockForms.tsx:904
#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:453
#: src/tables/RowActions.tsx:155
#: src/tables/build/BuildLineTable.tsx:100
msgid "Actions"
msgstr "Acciones"

#: src/components/nav/NavigationDrawer.tsx:223
msgid "About"
msgstr "Acerca de"

#: src/components/nav/NotificationDrawer.tsx:181
#: src/pages/Notifications.tsx:74
msgid "Mark all as read"
msgstr "Marcar todos como leídos"

#: src/components/nav/NotificationDrawer.tsx:191
msgid "View all notifications"
msgstr "Ver todas las notificaciones"

#: src/components/nav/NotificationDrawer.tsx:211
msgid "You have no unread notifications."
msgstr "No tienes notificaciones sin leer."

#: src/components/nav/SearchDrawer.tsx:105
msgid "No Overview Available"
msgstr "No hay resumen disponible"

#: src/components/nav/SearchDrawer.tsx:106
msgid "No overview available for this model type"
msgstr "No hay resúmenes disponibles para este tipo de modelo"

#: src/components/nav/SearchDrawer.tsx:124
msgid "View all results"
msgstr "Ver todos los resultados"

#: src/components/nav/SearchDrawer.tsx:139
msgid "results"
msgstr "resultados"

#: src/components/nav/SearchDrawer.tsx:143
msgid "Remove search group"
msgstr "Eliminar grupo de búsqueda"

#: src/components/nav/SearchDrawer.tsx:287
#: src/pages/company/ManufacturerPartDetail.tsx:177
#: src/pages/part/PartDetail.tsx:561
#: src/pages/part/PartSupplierDetail.tsx:15
#: src/pages/purchasing/PurchasingIndex.tsx:81
msgid "Suppliers"
msgstr "Proveedores"

#: src/components/nav/SearchDrawer.tsx:297
#: src/pages/part/PartSupplierDetail.tsx:23
#: src/pages/purchasing/PurchasingIndex.tsx:98
msgid "Manufacturers"
msgstr "Fabricantes"

#: src/components/nav/SearchDrawer.tsx:307
#: src/pages/sales/SalesIndex.tsx:124
msgid "Customers"
msgstr "Clientes"

#: src/components/nav/SearchDrawer.tsx:462
#~ msgid "No results"
#~ msgstr "No results"

#: src/components/nav/SearchDrawer.tsx:480
msgid "Enter search text"
msgstr "Introduce texto a buscar"

#: src/components/nav/SearchDrawer.tsx:491
msgid "Refresh search results"
msgstr "Actualizar resultados de búsqueda"

#: src/components/nav/SearchDrawer.tsx:502
#: src/components/nav/SearchDrawer.tsx:509
msgid "Search Options"
msgstr "Opciones de búsqueda"

#: src/components/nav/SearchDrawer.tsx:512
msgid "Whole word search"
msgstr "Búsqueda de palabras completas"

#: src/components/nav/SearchDrawer.tsx:521
msgid "Regex search"
msgstr "Búsqueda por expresión regular"

#: src/components/nav/SearchDrawer.tsx:530
msgid "Notes search"
msgstr ""

#: src/components/nav/SearchDrawer.tsx:578
msgid "An error occurred during search query"
msgstr "Se ha producido un error durante la consulta de búsqueda"

#: src/components/nav/SearchDrawer.tsx:589
#: src/tables/part/PartTestTemplateTable.tsx:82
msgid "No Results"
msgstr "Sin Resultados"

#: src/components/nav/SearchDrawer.tsx:592
msgid "No results available for search query"
msgstr "No hay resultados disponibles para consulta de búsqueda"

#: src/components/panels/AttachmentPanel.tsx:18
msgid "Attachments"
msgstr "Archivos adjuntos"

#: src/components/panels/NotesPanel.tsx:23
#: src/tables/build/BuildOrderTestTable.tsx:150
#: src/tables/stock/StockTrackingTable.tsx:210
msgid "Notes"
msgstr "Notas"

#: src/components/plugins/LocateItemButton.tsx:68
#: src/components/plugins/LocateItemButton.tsx:88
msgid "Locate Item"
msgstr "Localizar Artículo"

#: src/components/plugins/LocateItemButton.tsx:70
msgid "Item location requested"
msgstr "Ubicación del artículo solicitada"

#: src/components/plugins/PluginDrawer.tsx:47
msgid "Plugin Inactive"
msgstr "Complemento inactivo"

#: src/components/plugins/PluginDrawer.tsx:50
msgid "Plugin is not active"
msgstr "El plugin no está activo"

#: src/components/plugins/PluginDrawer.tsx:59
msgid "Plugin Information"
msgstr "Información del complemento"

#: src/components/plugins/PluginDrawer.tsx:73
#: src/forms/selectionListFields.tsx:106
#: src/pages/build/BuildDetail.tsx:136
#: src/pages/company/CompanyDetail.tsx:93
#: src/pages/company/ManufacturerPartDetail.tsx:92
#: src/pages/company/ManufacturerPartDetail.tsx:119
#: src/pages/company/SupplierPartDetail.tsx:144
#: src/pages/part/CategoryDetail.tsx:107
#: src/pages/part/PartDetail.tsx:179
#: src/pages/purchasing/PurchaseOrderDetail.tsx:145
#: src/pages/sales/ReturnOrderDetail.tsx:110
#: src/pages/sales/SalesOrderDetail.tsx:119
#: src/pages/stock/LocationDetail.tsx:105
#: src/tables/ColumnRenderers.tsx:96
#: src/tables/bom/UsedInTable.tsx:44
#: src/tables/build/BuildAllocatedStockTable.tsx:83
#: src/tables/build/BuildLineTable.tsx:329
#: src/tables/machine/MachineTypeTable.tsx:75
#: src/tables/machine/MachineTypeTable.tsx:130
#: src/tables/machine/MachineTypeTable.tsx:241
#: src/tables/machine/MachineTypeTable.tsx:345
#: src/tables/plugin/PluginListTable.tsx:109
#: src/tables/sales/SalesOrderAllocationTable.tsx:110
#: src/tables/sales/SalesOrderAllocationTable.tsx:129
#: src/tables/sales/SalesOrderLineItemTable.tsx:92
#: src/tables/stock/LocationTypesTable.tsx:74
msgid "Description"
msgstr "Descripción"

#: src/components/plugins/PluginDrawer.tsx:78
msgid "Author"
msgstr "Autor"

#: src/components/plugins/PluginDrawer.tsx:83
#: src/pages/part/PartSchedulingDetail.tsx:279
#: src/pages/part/pricing/PurchaseHistoryPanel.tsx:41
#: src/pages/part/pricing/SaleHistoryPanel.tsx:38
#: src/tables/ColumnRenderers.tsx:241
#: src/tables/build/BuildOrderTestTable.tsx:158
#: src/tables/settings/StocktakeReportTable.tsx:41
msgid "Date"
msgstr "Fecha"

#: src/components/plugins/PluginDrawer.tsx:93
#: src/forms/selectionListFields.tsx:107
#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:68
#: src/pages/core/UserDetail.tsx:81
#: src/pages/core/UserDetail.tsx:209
#: src/pages/part/PartDetail.tsx:332
#: src/tables/bom/UsedInTable.tsx:85
#: src/tables/company/CompanyTable.tsx:62
#: src/tables/company/CompanyTable.tsx:96
#: src/tables/machine/MachineListTable.tsx:335
#: src/tables/machine/MachineListTable.tsx:605
#: src/tables/part/ParametricPartTable.tsx:224
#: src/tables/part/PartTable.tsx:181
#: src/tables/part/PartVariantTable.tsx:15
#: src/tables/plugin/PluginListTable.tsx:95
#: src/tables/plugin/PluginListTable.tsx:411
#: src/tables/purchasing/SupplierPartTable.tsx:101
#: src/tables/purchasing/SupplierPartTable.tsx:193
#: src/tables/settings/ApiTokenTable.tsx:61
#: src/tables/settings/UserTable.tsx:350
#: src/tables/stock/StockItemTable.tsx:306
msgid "Active"
msgstr "Activo"

#: src/components/plugins/PluginDrawer.tsx:105
msgid "Package Name"
msgstr "Nombre de Paquete"

#: src/components/plugins/PluginDrawer.tsx:111
msgid "Installation Path"
msgstr "Ruta de instalación"

#: src/components/plugins/PluginDrawer.tsx:116
#: src/tables/machine/MachineTypeTable.tsx:153
#: src/tables/machine/MachineTypeTable.tsx:277
#: src/tables/plugin/PluginListTable.tsx:100
#: src/tables/plugin/PluginListTable.tsx:416
msgid "Builtin"
msgstr "Integrado"

#: src/components/plugins/PluginDrawer.tsx:121
msgid "Package"
msgstr "Paquete"

#: src/components/plugins/PluginDrawer.tsx:133
#: src/pages/Index/Settings/AdminCenter/PluginManagementPanel.tsx:54
msgid "Plugin Settings"
msgstr "Ajustes del complemento"

#: src/components/plugins/PluginDrawer.tsx:145
#: src/components/render/ModelType.tsx:253
msgid "Plugin Configuration"
msgstr "Configuración de complemento"

#: src/components/plugins/PluginPanel.tsx:87
#~ msgid "Error occurred while rendering plugin content"
#~ msgstr "Error occurred while rendering plugin content"

#: src/components/plugins/PluginPanel.tsx:91
#~ msgid "Plugin did not provide panel rendering function"
#~ msgstr "Plugin did not provide panel rendering function"

#: src/components/plugins/PluginPanel.tsx:103
#~ msgid "No content provided for this plugin"
#~ msgstr "No content provided for this plugin"

#: src/components/plugins/PluginPanel.tsx:116
#: src/components/plugins/PluginSettingsPanel.tsx:76
#~ msgid "Error Loading Plugin"
#~ msgstr "Error Loading Plugin"

#: src/components/plugins/PluginSettingsPanel.tsx:51
#~ msgid "Error occurred while rendering plugin settings"
#~ msgstr "Error occurred while rendering plugin settings"

#: src/components/plugins/PluginSettingsPanel.tsx:55
#~ msgid "Plugin did not provide settings rendering function"
#~ msgstr "Plugin did not provide settings rendering function"

#: src/components/plugins/PluginUIFeature.tsx:101
msgid "Error occurred while rendering the template editor."
msgstr "Se ha producido un error al procesar el editor de plantillas."

#: src/components/plugins/PluginUIFeature.tsx:118
msgid "Error Loading Plugin Editor"
msgstr "Error al cargar el editor de complementos"

#: src/components/plugins/PluginUIFeature.tsx:154
msgid "Error occurred while rendering the template preview."
msgstr "Se ha producido un error al procesar la vista previa de la plantilla."

#: src/components/plugins/PluginUIFeature.tsx:165
msgid "Error Loading Plugin Preview"
msgstr "Error al cargar vista previa del complemento"

#: src/components/plugins/RemoteComponent.tsx:76
msgid "Invalid source or function name"
msgstr "Nombre de la fuente o función inválido"

#: src/components/plugins/RemoteComponent.tsx:94
msgid "Error Loading Content"
msgstr "Error al cargar el contenido"

#: src/components/plugins/RemoteComponent.tsx:98
msgid "Error occurred while loading plugin content"
msgstr "Ha ocurrido un error al cargar el contenido del complemento"

#: src/components/render/Instance.tsx:238
#~ msgid "Unknown model: {model}"
#~ msgstr "Unknown model: {model}"

#: src/components/render/Instance.tsx:239
msgid "Unknown model: {model_name}"
msgstr ""

#: src/components/render/ModelType.tsx:29
#: src/components/wizards/OrderPartsWizard.tsx:132
#: src/forms/BuildForms.tsx:280
#: src/forms/BuildForms.tsx:354
#: src/forms/BuildForms.tsx:408
#: src/forms/BuildForms.tsx:544
#: src/forms/PurchaseOrderForms.tsx:751
#: src/forms/ReturnOrderForms.tsx:238
#: src/forms/SalesOrderForms.tsx:266
#: src/forms/StockForms.tsx:282
#: src/forms/StockForms.tsx:701
#: src/forms/StockForms.tsx:745
#: src/forms/StockForms.tsx:784
#: src/forms/StockForms.tsx:820
#: src/forms/StockForms.tsx:858
#: src/forms/StockForms.tsx:900
#: src/forms/StockForms.tsx:948
#: src/forms/StockForms.tsx:992
#: src/pages/build/BuildDetail.tsx:93
#: src/pages/part/PartDetail.tsx:999
#: src/tables/build/BuildAllocatedStockTable.tsx:95
#: src/tables/build/BuildLineTable.tsx:71
#: src/tables/part/PartTable.tsx:35
#: src/tables/part/RelatedPartTable.tsx:49
#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:129
#: src/tables/sales/ReturnOrderLineItemTable.tsx:100
#: src/tables/sales/SalesOrderAllocationTable.tsx:122
#: src/tables/stock/StockTrackingTable.tsx:85
msgid "Part"
msgstr "Pieza"

#: src/components/render/ModelType.tsx:38
#: src/pages/Index/Settings/AdminCenter/PartParameterPanel.tsx:13
msgid "Part Parameter Template"
msgstr "Plantilla de parámetro de pieza"

#: src/components/render/ModelType.tsx:39
msgid "Part Parameter Templates"
msgstr "Plantillas de Parámetros de piezas"

#: src/components/render/ModelType.tsx:45
msgid "Part Test Template"
msgstr "Plantilla de prueba de pieza"

#: src/components/render/ModelType.tsx:46
msgid "Part Test Templates"
msgstr "Plantillas de prueba de piezas"

#: src/components/render/ModelType.tsx:52
#: src/components/wizards/OrderPartsWizard.tsx:143
#: src/pages/company/SupplierPartDetail.tsx:407
#: src/pages/stock/StockDetail.tsx:213
#: src/tables/build/BuildAllocatedStockTable.tsx:152
#: src/tables/part/PartPurchaseOrdersTable.tsx:49
#: src/tables/purchasing/SupplierPartTable.tsx:71
#: src/tables/stock/StockItemTable.tsx:236
msgid "Supplier Part"
msgstr "Pieza de proveedor"

#: src/components/render/ModelType.tsx:53
#: src/pages/purchasing/PurchasingIndex.tsx:92
msgid "Supplier Parts"
msgstr "Piezas de proveedor"

#: src/components/render/ModelType.tsx:61
#: src/tables/part/PartPurchaseOrdersTable.tsx:55
#: src/tables/stock/StockItemTable.tsx:241
msgid "Manufacturer Part"
msgstr "Pieza de fabricante"

#: src/components/render/ModelType.tsx:62
#: src/pages/purchasing/PurchasingIndex.tsx:109
msgid "Manufacturer Parts"
msgstr "Piezas del fabricante"

#: src/components/render/ModelType.tsx:70
#: src/pages/part/CategoryDetail.tsx:345
msgid "Part Category"
msgstr "Categoría de Pieza"

#: src/components/render/ModelType.tsx:71
#: src/enums/Roles.tsx:38
#: src/pages/part/CategoryDetail.tsx:336
#: src/pages/part/PartDetail.tsx:988
msgid "Part Categories"
msgstr "Categorías de Pieza"

#: src/components/render/ModelType.tsx:79
#: src/forms/BuildForms.tsx:355
#: src/forms/BuildForms.tsx:409
#: src/forms/BuildForms.tsx:546
#: src/forms/SalesOrderForms.tsx:268
#: src/pages/stock/StockDetail.tsx:910
#: src/tables/stock/StockTrackingTable.tsx:46
#: src/tables/stock/StockTrackingTable.tsx:53
msgid "Stock Item"
msgstr "Artículo de stock"

#: src/components/render/ModelType.tsx:80
#: src/enums/Roles.tsx:46
#: src/pages/company/CompanyDetail.tsx:205
#: src/pages/part/CategoryDetail.tsx:288
#: src/pages/part/PartStocktakeDetail.tsx:116
#: src/pages/stock/LocationDetail.tsx:124
#: src/pages/stock/LocationDetail.tsx:183
#: src/pages/stock/LocationDetail.tsx:380
msgid "Stock Items"
msgstr "Artículos de Stock"

#: src/components/render/ModelType.tsx:88
#: src/enums/Roles.tsx:48
msgid "Stock Location"
msgstr "Ubicación de almacén"

#: src/components/render/ModelType.tsx:89
#: src/pages/stock/LocationDetail.tsx:177
#: src/pages/stock/LocationDetail.tsx:372
#: src/pages/stock/StockDetail.tsx:901
msgid "Stock Locations"
msgstr "Ubicaciones de almacén"

#: src/components/render/ModelType.tsx:97
msgid "Stock Location Type"
msgstr "Tipos de ubicación de existencias"

#: src/components/render/ModelType.tsx:98
msgid "Stock Location Types"
msgstr "Tipos de ubicaciones de existencias"

#: src/components/render/ModelType.tsx:103
#: src/pages/part/PartDetail.tsx:603
msgid "Stock History"
msgstr "Histórico de existencias"

#: src/components/render/ModelType.tsx:104
msgid "Stock Histories"
msgstr "Históricos de existencias"

#: src/components/render/ModelType.tsx:109
msgid "Build"
msgstr "Construcción"

#: src/components/render/ModelType.tsx:110
msgid "Builds"
msgstr "Construcciones"

#: src/components/render/ModelType.tsx:118
msgid "Build Line"
msgstr "Línea de construcción"

#: src/components/render/ModelType.tsx:119
msgid "Build Lines"
msgstr "Líneas de construcción"

#: src/components/render/ModelType.tsx:126
msgid "Build Item"
msgstr "Construir elemento"

#: src/components/render/ModelType.tsx:127
msgid "Build Items"
msgstr "Construir elementos"

#: src/components/render/ModelType.tsx:132
#: src/pages/company/CompanyDetail.tsx:326
#: src/tables/company/ContactTable.tsx:63
msgid "Company"
msgstr "Empresa"

#: src/components/render/ModelType.tsx:133
msgid "Companies"
msgstr "Empresas"

#: src/components/render/ModelType.tsx:140
#: src/pages/build/BuildDetail.tsx:195
#: src/pages/purchasing/PurchaseOrderDetail.tsx:236
#: src/pages/sales/ReturnOrderDetail.tsx:200
#: src/pages/sales/SalesOrderDetail.tsx:212
#: src/tables/ColumnRenderers.tsx:175
#: src/tables/Filter.tsx:265
#: src/tables/TableHoverCard.tsx:81
msgid "Project Code"
msgstr "Código de proyecto"

#: src/components/render/ModelType.tsx:141
#: src/pages/Index/Settings/AdminCenter/Index.tsx:150
msgid "Project Codes"
msgstr "Códigos de proyecto"

#: src/components/render/ModelType.tsx:147
#: src/components/wizards/OrderPartsWizard.tsx:183
#: src/pages/part/pricing/PurchaseHistoryPanel.tsx:33
#: src/pages/purchasing/PurchaseOrderDetail.tsx:521
#: src/pages/stock/StockDetail.tsx:276
#: src/tables/part/PartPurchaseOrdersTable.tsx:31
#: src/tables/stock/StockItemTable.tsx:229
#: src/tables/stock/StockTrackingTable.tsx:118
msgid "Purchase Order"
msgstr "Pedido de compra"

#: src/components/render/ModelType.tsx:148
#: src/enums/Roles.tsx:40
#: src/pages/Index/Settings/SystemSettings.tsx:261
#: src/pages/company/CompanyDetail.tsx:198
#: src/pages/company/SupplierPartDetail.tsx:266
#: src/pages/part/PartDetail.tsx:574
#: src/pages/purchasing/PurchasingIndex.tsx:60
msgid "Purchase Orders"
msgstr "Órdenes de compra"

#: src/components/render/ModelType.tsx:156
msgid "Purchase Order Line"
msgstr "Línea de pedido de compra"

#: src/components/render/ModelType.tsx:157
msgid "Purchase Order Lines"
msgstr "Líneas de pedido de compra"

#: src/components/render/ModelType.tsx:162
#: src/pages/build/BuildDetail.tsx:168
#: src/pages/part/pricing/SaleHistoryPanel.tsx:24
#: src/pages/sales/SalesOrderDetail.tsx:570
#: src/pages/sales/SalesOrderShipmentDetail.tsx:96
#: src/pages/sales/SalesOrderShipmentDetail.tsx:360
#: src/pages/stock/StockDetail.tsx:285
#: src/tables/part/PartSalesAllocationsTable.tsx:38
#: src/tables/sales/SalesOrderAllocationTable.tsx:103
#: src/tables/stock/StockTrackingTable.tsx:129
msgid "Sales Order"
msgstr "Orden de venta"

#: src/components/render/ModelType.tsx:163
#: src/enums/Roles.tsx:44
#: src/pages/Index/Settings/SystemSettings.tsx:277
#: src/pages/company/CompanyDetail.tsx:218
#: src/pages/part/PartDetail.tsx:586
#: src/pages/sales/SalesIndex.tsx:82
msgid "Sales Orders"
msgstr "Órdenes de venta"

#: src/components/render/ModelType.tsx:171
#: src/pages/sales/SalesOrderShipmentDetail.tsx:359
msgid "Sales Order Shipment"
msgstr "Envío de orden de venta"

#: src/components/render/ModelType.tsx:172
msgid "Sales Order Shipments"
msgstr "Envíos de pedidos de venta"

#: src/components/render/ModelType.tsx:178
#: src/pages/sales/ReturnOrderDetail.tsx:506
#: src/tables/stock/StockTrackingTable.tsx:140
msgid "Return Order"
msgstr "Orden de devolución"

#: src/components/render/ModelType.tsx:179
#: src/enums/Roles.tsx:42
#: src/pages/Index/Settings/SystemSettings.tsx:293
#: src/pages/company/CompanyDetail.tsx:225
#: src/pages/part/PartDetail.tsx:593
#: src/pages/sales/SalesIndex.tsx:103
msgid "Return Orders"
msgstr "Ordenes de devolución"

#: src/components/render/ModelType.tsx:187
msgid "Return Order Line Item"
msgstr "Línea de pedido de devolución"

#: src/components/render/ModelType.tsx:188
msgid "Return Order Line Items"
msgstr "Línea de pedido de devolución"

#: src/components/render/ModelType.tsx:193
#: src/tables/company/AddressTable.tsx:48
msgid "Address"
msgstr "Dirección"

#: src/components/render/ModelType.tsx:194
#: src/pages/company/CompanyDetail.tsx:258
msgid "Addresses"
msgstr "Direcciones"

#: src/components/render/ModelType.tsx:200
#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:89
#: src/pages/core/UserDetail.tsx:135
#: src/pages/purchasing/PurchaseOrderDetail.tsx:212
#: src/pages/sales/ReturnOrderDetail.tsx:176
#: src/pages/sales/SalesOrderDetail.tsx:188
msgid "Contact"
msgstr "Contacto"

#: src/components/render/ModelType.tsx:201
#: src/pages/company/CompanyDetail.tsx:252
#: src/pages/core/CoreIndex.tsx:33
msgid "Contacts"
msgstr "Contactos"

#: src/components/render/ModelType.tsx:207
msgid "Owner"
msgstr "Propietario"

#: src/components/render/ModelType.tsx:208
msgid "Owners"
msgstr "Propietarios"

#: src/components/render/ModelType.tsx:214
#: src/pages/Auth/ChangePassword.tsx:36
#: src/pages/core/UserDetail.tsx:220
#: src/tables/Filter.tsx:314
#: src/tables/settings/ApiTokenTable.tsx:101
#: src/tables/settings/ApiTokenTable.tsx:119
#: src/tables/settings/BarcodeScanHistoryTable.tsx:78
#: src/tables/settings/ExportSessionTable.tsx:40
#: src/tables/settings/ImportSessionTable.tsx:74
#: src/tables/settings/StocktakeReportTable.tsx:45
#: src/tables/stock/StockItemTestResultTable.tsx:208
#: src/tables/stock/StockTrackingTable.tsx:188
#: src/tables/stock/StockTrackingTable.tsx:216
msgid "User"
msgstr "Usuario"

#: src/components/render/ModelType.tsx:221
#: src/pages/core/GroupDetail.tsx:78
msgid "Group"
msgstr "Grupo"

#: src/components/render/ModelType.tsx:229
msgid "Import Session"
msgstr "Importar sesión"

#: src/components/render/ModelType.tsx:230
msgid "Import Sessions"
msgstr "Importar sesiones"

#: src/components/render/ModelType.tsx:234
#~ msgid "Purchase Order Line Item"
#~ msgstr "Purchase Order Line Item"

#: src/components/render/ModelType.tsx:237
msgid "Label Template"
msgstr "Plantilla de etiqueta"

#: src/components/render/ModelType.tsx:238
#: src/pages/Index/Settings/AdminCenter/Index.tsx:194
msgid "Label Templates"
msgstr "Plantillas de etiqueta"

#: src/components/render/ModelType.tsx:245
msgid "Report Template"
msgstr "Plantilla de informe"

#: src/components/render/ModelType.tsx:246
#: src/pages/Index/Settings/AdminCenter/Index.tsx:200
msgid "Report Templates"
msgstr "Plantillas de informe"

#: src/components/render/ModelType.tsx:254
msgid "Plugin Configurations"
msgstr "Configuraciones de complemento"

#: src/components/render/ModelType.tsx:261
msgid "Content Type"
msgstr "Tipo de contenido"

#: src/components/render/ModelType.tsx:262
msgid "Content Types"
msgstr "Tipos de contenido"

#: src/components/render/ModelType.tsx:264
#~ msgid "Unknown Model"
#~ msgstr "Unknown Model"

#: src/components/render/ModelType.tsx:267
msgid "Selection List"
msgstr "Lista de selección"

#: src/components/render/ModelType.tsx:268
#: src/pages/Index/Settings/AdminCenter/PartParameterPanel.tsx:21
msgid "Selection Lists"
msgstr "Listas de Selección"

#: src/components/render/ModelType.tsx:274
#: src/tables/machine/MachineListTable.tsx:353
#: src/tables/machine/MachineTypeTable.tsx:283
msgid "Errors"
msgstr "Errores"

#: src/components/render/ModelType.tsx:307
#~ msgid "Purchase Order Line Items"
#~ msgstr "Purchase Order Line Items"

#: src/components/render/ModelType.tsx:337
#~ msgid "Unknown Models"
#~ msgstr "Unknown Models"

#: src/components/render/Order.tsx:121
#: src/tables/sales/SalesOrderAllocationTable.tsx:172
msgid "Shipment"
msgstr "Envío"

#: src/components/render/Part.tsx:25
#: src/components/render/Plugin.tsx:17
#: src/pages/company/CompanyDetail.tsx:312
#: src/pages/company/SupplierPartDetail.tsx:366
#: src/pages/core/UserDetail.tsx:211
#: src/pages/part/PartDetail.tsx:785
msgid "Inactive"
msgstr "Inactivo"

#: src/components/render/Part.tsx:28
#: src/tables/bom/BomTable.tsx:206
#: src/tables/part/PartTable.tsx:137
msgid "No stock"
msgstr "Sin existencias"

#: src/components/render/Stock.tsx:61
#: src/pages/stock/StockDetail.tsx:185
#: src/pages/stock/StockDetail.tsx:837
#: src/tables/build/BuildAllocatedStockTable.tsx:123
#: src/tables/build/BuildOutputTable.tsx:81
#: src/tables/sales/SalesOrderAllocationTable.tsx:141
#: src/tables/stock/StockItemTable.tsx:383
msgid "Serial Number"
msgstr "Número de serie"

#: src/components/render/Stock.tsx:63
#: src/components/wizards/OrderPartsWizard.tsx:222
#: src/forms/BuildForms.tsx:213
#: src/forms/BuildForms.tsx:547
#: src/forms/PurchaseOrderForms.tsx:754
#: src/forms/ReturnOrderForms.tsx:239
#: src/forms/SalesOrderForms.tsx:269
#: src/pages/part/PartSchedulingDetail.tsx:83
#: src/pages/part/PartStocktakeDetail.tsx:61
#: src/pages/part/PartStocktakeDetail.tsx:239
#: src/pages/part/PartStocktakeDetail.tsx:263
#: src/pages/part/pricing/BomPricingPanel.tsx:148
#: src/pages/part/pricing/PriceBreakPanel.tsx:89
#: src/pages/part/pricing/PriceBreakPanel.tsx:172
#: src/pages/stock/StockDetail.tsx:180
#: src/pages/stock/StockDetail.tsx:843
#: src/tables/build/BuildLineTable.tsx:78
#: src/tables/build/BuildOrderTestTable.tsx:205
#: src/tables/part/PartPurchaseOrdersTable.tsx:93
#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:147
#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:178
#: src/tables/purchasing/SupplierPriceBreakTable.tsx:77
#: src/tables/sales/ReturnOrderLineItemTable.tsx:114
#: src/tables/stock/StockTrackingTable.tsx:70
msgid "Quantity"
msgstr "Cantidad"

#: src/components/render/Stock.tsx:69
#: src/forms/BuildForms.tsx:282
#: src/forms/BuildForms.tsx:356
#: src/forms/BuildForms.tsx:410
#: src/forms/StockForms.tsx:703
#: src/forms/StockForms.tsx:747
#: src/forms/StockForms.tsx:786
#: src/forms/StockForms.tsx:822
#: src/forms/StockForms.tsx:860
#: src/forms/StockForms.tsx:902
#: src/forms/StockForms.tsx:950
#: src/forms/StockForms.tsx:994
#: src/tables/build/BuildLineTable.tsx:88
msgid "Batch"
msgstr "Lote"

#: src/components/settings/SettingItem.tsx:47
#: src/components/settings/SettingItem.tsx:100
#~ msgid "{0} updated successfully"
#~ msgstr "{0} updated successfully"

#: src/components/settings/SettingList.tsx:72
msgid "Edit Setting"
msgstr "Editar ajuste"

#: src/components/settings/SettingList.tsx:85
msgid "Setting {key} updated successfully"
msgstr "El ajuste {key} se ha actualizado correctamente"

#: src/components/settings/SettingList.tsx:114
msgid "Setting updated"
msgstr "Ajuste actualizado"

#. placeholder {0}: setting.key
#: src/components/settings/SettingList.tsx:115
msgid "Setting {0} updated successfully"
msgstr "El ajuste {0} se ha actualizado correctamente"

#: src/components/settings/SettingList.tsx:124
msgid "Error editing setting"
msgstr "Error al editar el ajuste"

#: src/components/settings/SettingList.tsx:169
msgid "No settings specified"
msgstr "No se especificaron ajustes"

#: src/components/tables/FilterGroup.tsx:29
#~ msgid "Add table filter"
#~ msgstr "Add table filter"

#: src/components/tables/FilterGroup.tsx:44
#~ msgid "Clear all filters"
#~ msgstr "Clear all filters"

#: src/components/tables/FilterGroup.tsx:51
#~ msgid "Add filter"
#~ msgstr "Add filter"

#: src/components/tables/FilterSelectModal.tsx:56
#~ msgid "True"
#~ msgstr "True"

#: src/components/tables/FilterSelectModal.tsx:57
#~ msgid "False"
#~ msgstr "False"

#: src/components/tables/FilterSelectModal.tsx:143
#~ msgid "Add Table Filter"
#~ msgstr "Add Table Filter"

#: src/components/tables/FilterSelectModal.tsx:145
#~ msgid "Select from the available filters"
#~ msgstr "Select from the available filters"

#: src/components/tables/bom/BomTable.tsx:113
#~ msgid "Substitutes"
#~ msgstr "Substitutes"

#: src/components/tables/bom/BomTable.tsx:200
#~ msgid "Validate"
#~ msgstr "Validate"

#: src/components/tables/bom/BomTable.tsx:250
#~ msgid "Has Available Stock"
#~ msgstr "Has Available Stock"

#: src/components/tables/bom/UsedInTable.tsx:40
#~ msgid "Required Part"
#~ msgstr "Required Part"

#: src/components/tables/build/BuildOrderTable.tsx:52
#~ msgid "Progress"
#~ msgstr "Progress"

#: src/components/tables/build/BuildOrderTable.tsx:65
#~ msgid "Priority"
#~ msgstr "Priority"

#: src/components/tables/company/AddressTable.tsx:68
#~ msgid "Postal Code"
#~ msgstr "Postal Code"

#: src/components/tables/company/AddressTable.tsx:74
#~ msgid "City"
#~ msgstr "City"

#: src/components/tables/company/AddressTable.tsx:80
#~ msgid "State / Province"
#~ msgstr "State / Province"

#: src/components/tables/company/AddressTable.tsx:86
#~ msgid "Country"
#~ msgstr "Country"

#: src/components/tables/company/AddressTable.tsx:92
#~ msgid "Courier Notes"
#~ msgstr "Courier Notes"

#: src/components/tables/company/AddressTable.tsx:98
#~ msgid "Internal Notes"
#~ msgstr "Internal Notes"

#: src/components/tables/company/AddressTable.tsx:130
#~ msgid "Address updated"
#~ msgstr "Address updated"

#: src/components/tables/company/AddressTable.tsx:142
#~ msgid "Address deleted"
#~ msgstr "Address deleted"

#: src/components/tables/company/CompanyTable.tsx:32
#~ msgid "Company Name"
#~ msgstr "Company Name"

#: src/components/tables/company/ContactTable.tsx:41
#~ msgid "Phone"
#~ msgstr "Phone"

#: src/components/tables/company/ContactTable.tsx:78
#~ msgid "Contact updated"
#~ msgstr "Contact updated"

#: src/components/tables/company/ContactTable.tsx:90
#~ msgid "Contact deleted"
#~ msgstr "Contact deleted"

#: src/components/tables/company/ContactTable.tsx:92
#~ msgid "Are you sure you want to delete this contact?"
#~ msgstr "Are you sure you want to delete this contact?"

#: src/components/tables/company/ContactTable.tsx:108
#~ msgid "Create Contact"
#~ msgstr "Create Contact"

#: src/components/tables/company/ContactTable.tsx:110
#~ msgid "Contact created"
#~ msgstr "Contact created"

#: src/components/tables/general/AttachmentTable.tsx:47
#~ msgid "Comment"
#~ msgstr "Comment"

#: src/components/tables/part/PartCategoryTable.tsx:122
#~ msgid "Part category updated"
#~ msgstr "Part category updated"

#: src/components/tables/part/PartParameterTable.tsx:41
#~ msgid "Parameter"
#~ msgstr "Parameter"

#: src/components/tables/part/PartParameterTable.tsx:114
#~ msgid "Part parameter updated"
#~ msgstr "Part parameter updated"

#: src/components/tables/part/PartParameterTable.tsx:130
#~ msgid "Part parameter deleted"
#~ msgstr "Part parameter deleted"

#: src/components/tables/part/PartParameterTable.tsx:132
#~ msgid "Are you sure you want to remove this parameter?"
#~ msgstr "Are you sure you want to remove this parameter?"

#: src/components/tables/part/PartParameterTable.tsx:159
#~ msgid "Part parameter added"
#~ msgstr "Part parameter added"

#: src/components/tables/part/PartParameterTemplateTable.tsx:67
#~ msgid "Choices"
#~ msgstr "Choices"

#: src/components/tables/part/PartParameterTemplateTable.tsx:83
#~ msgid "Remove parameter template"
#~ msgstr "Remove parameter template"

#: src/components/tables/part/PartParameterTemplateTable.tsx:84
#~ msgid "Parameter template updated"
#~ msgstr "Parameter template updated"

#: src/components/tables/part/PartParameterTemplateTable.tsx:96
#~ msgid "Parameter template deleted"
#~ msgstr "Parameter template deleted"

#: src/components/tables/part/PartParameterTemplateTable.tsx:98
#~ msgid "Are you sure you want to remove this parameter template?"
#~ msgstr "Are you sure you want to remove this parameter template?"

#: src/components/tables/part/PartParameterTemplateTable.tsx:110
#~ msgid "Create Parameter Template"
#~ msgstr "Create Parameter Template"

#: src/components/tables/part/PartParameterTemplateTable.tsx:112
#~ msgid "Parameter template created"
#~ msgstr "Parameter template created"

#: src/components/tables/part/PartTable.tsx:211
#~ msgid "Detail"
#~ msgstr "Detail"

#: src/components/tables/part/PartTestTemplateTable.tsx:30
#~ msgid "Test Name"
#~ msgstr "Test Name"

#: src/components/tables/part/PartTestTemplateTable.tsx:86
#~ msgid "Template updated"
#~ msgstr "Template updated"

#: src/components/tables/part/PartTestTemplateTable.tsx:98
#~ msgid "Test Template deleted"
#~ msgstr "Test Template deleted"

#: src/components/tables/part/PartTestTemplateTable.tsx:115
#~ msgid "Create Test Template"
#~ msgstr "Create Test Template"

#: src/components/tables/part/PartTestTemplateTable.tsx:117
#~ msgid "Template created"
#~ msgstr "Template created"

#: src/components/tables/part/RelatedPartTable.tsx:79
#~ msgid "Related Part"
#~ msgstr "Related Part"

#: src/components/tables/part/RelatedPartTable.tsx:82
#~ msgid "Related part added"
#~ msgstr "Related part added"

#: src/components/tables/part/RelatedPartTable.tsx:114
#~ msgid "Related part deleted"
#~ msgstr "Related part deleted"

#: src/components/tables/part/RelatedPartTable.tsx:115
#~ msgid "Are you sure you want to remove this relationship?"
#~ msgstr "Are you sure you want to remove this relationship?"

#: src/components/tables/plugin/PluginListTable.tsx:191
#~ msgid "Installation path"
#~ msgstr "Installation path"

#: src/components/tables/purchasing/PurchaseOrderLineItemTable.tsx:55
#~ msgid "Receive"
#~ msgstr "Receive"

#: src/components/tables/purchasing/PurchaseOrderLineItemTable.tsx:81
#~ msgid "Line item updated"
#~ msgstr "Line item updated"

#: src/components/tables/purchasing/PurchaseOrderLineItemTable.tsx:232
#~ msgid "Line item added"
#~ msgstr "Line item added"

#: src/components/tables/settings/CustomUnitsTable.tsx:37
#~ msgid "Definition"
#~ msgstr "Definition"

#: src/components/tables/settings/CustomUnitsTable.tsx:43
#~ msgid "Symbol"
#~ msgstr "Symbol"

#: src/components/tables/settings/CustomUnitsTable.tsx:59
#~ msgid "Edit custom unit"
#~ msgstr "Edit custom unit"

#: src/components/tables/settings/CustomUnitsTable.tsx:66
#~ msgid "Custom unit updated"
#~ msgstr "Custom unit updated"

#: src/components/tables/settings/CustomUnitsTable.tsx:76
#~ msgid "Delete custom unit"
#~ msgstr "Delete custom unit"

#: src/components/tables/settings/CustomUnitsTable.tsx:77
#~ msgid "Custom unit deleted"
#~ msgstr "Custom unit deleted"

#: src/components/tables/settings/CustomUnitsTable.tsx:79
#~ msgid "Are you sure you want to remove this custom unit?"
#~ msgstr "Are you sure you want to remove this custom unit?"

#: src/components/tables/settings/CustomUnitsTable.tsx:97
#~ msgid "Custom unit created"
#~ msgstr "Custom unit created"

#: src/components/tables/settings/GroupTable.tsx:45
#~ msgid "Group updated"
#~ msgstr "Group updated"

#: src/components/tables/settings/GroupTable.tsx:131
#~ msgid "Added group"
#~ msgstr "Added group"

#: src/components/tables/settings/ProjectCodeTable.tsx:49
#~ msgid "Edit project code"
#~ msgstr "Edit project code"

#: src/components/tables/settings/ProjectCodeTable.tsx:56
#~ msgid "Project code updated"
#~ msgstr "Project code updated"

#: src/components/tables/settings/ProjectCodeTable.tsx:66
#~ msgid "Delete project code"
#~ msgstr "Delete project code"

#: src/components/tables/settings/ProjectCodeTable.tsx:67
#~ msgid "Project code deleted"
#~ msgstr "Project code deleted"

#: src/components/tables/settings/ProjectCodeTable.tsx:69
#~ msgid "Are you sure you want to remove this project code?"
#~ msgstr "Are you sure you want to remove this project code?"

#: src/components/tables/settings/ProjectCodeTable.tsx:88
#~ msgid "Added project code"
#~ msgstr "Added project code"

#: src/components/tables/settings/UserDrawer.tsx:92
#~ msgid "User permission changed successfully"
#~ msgstr "User permission changed successfully"

#: src/components/tables/settings/UserDrawer.tsx:93
#~ msgid "Some changes might only take effect after the user refreshes their login."
#~ msgstr "Some changes might only take effect after the user refreshes their login."

#: src/components/tables/settings/UserDrawer.tsx:118
#~ msgid "Changed user active status successfully"
#~ msgstr "Changed user active status successfully"

#: src/components/tables/settings/UserDrawer.tsx:119
#~ msgid "Set to {active}"
#~ msgstr "Set to {active}"

#: src/components/tables/settings/UserDrawer.tsx:142
#~ msgid "User details for {0}"
#~ msgstr "User details for {0}"

#: src/components/tables/settings/UserDrawer.tsx:176
#~ msgid "Rights"
#~ msgstr "Rights"

#: src/components/tables/settings/UserTable.tsx:106
#~ msgid "User updated"
#~ msgstr "User updated"

#: src/components/tables/settings/UserTable.tsx:117
#~ msgid "user deleted"
#~ msgstr "user deleted"

#: src/components/tables/stock/StockItemTable.tsx:247
#~ msgid "Test Filter"
#~ msgstr "Test Filter"

#: src/components/tables/stock/StockItemTable.tsx:248
#~ msgid "This is a test filter"
#~ msgstr "This is a test filter"

#: src/components/tables/stock/StockLocationTable.tsx:145
#~ msgid "Stock location updated"
#~ msgstr "Stock location updated"

#: src/components/widgets/FeedbackWidget.tsx:19
#~ msgid "Something is new: Platform UI"
#~ msgstr "Something is new: Platform UI"

#: src/components/widgets/FeedbackWidget.tsx:21
#~ msgid "We are building a new UI with a modern stack. What you currently see is not fixed and will be redesigned but demonstrates the UI/UX possibilities we will have going forward."
#~ msgstr "We are building a new UI with a modern stack. What you currently see is not fixed and will be redesigned but demonstrates the UI/UX possibilities we will have going forward."

#: src/components/widgets/FeedbackWidget.tsx:32
#~ msgid "Provide Feedback"
#~ msgstr "Provide Feedback"

#: src/components/widgets/GetStartedWidget.tsx:11
#~ msgid "Getting started"
#~ msgstr "Getting started"

#: src/components/widgets/MarkdownEditor.tsx:108
#~ msgid "Failed to upload image"
#~ msgstr "Failed to upload image"

#: src/components/widgets/MarkdownEditor.tsx:146
#~ msgid "Notes saved"
#~ msgstr "Notes saved"

#: src/components/widgets/WidgetLayout.tsx:166
#~ msgid "Layout"
#~ msgstr "Layout"

#: src/components/widgets/WidgetLayout.tsx:172
#~ msgid "Reset Layout"
#~ msgstr "Reset Layout"

#: src/components/widgets/WidgetLayout.tsx:185
#~ msgid "Stop Edit"
#~ msgstr "Stop Edit"

#: src/components/widgets/WidgetLayout.tsx:191
#~ msgid "Appearance"
#~ msgstr "Appearance"

#: src/components/widgets/WidgetLayout.tsx:203
#~ msgid "Show Boxes"
#~ msgstr "Show Boxes"

#: src/components/wizards/OrderPartsWizard.tsx:61
msgid "New Purchase Order"
msgstr "Nueva orden de compra"

#: src/components/wizards/OrderPartsWizard.tsx:63
msgid "Purchase order created"
msgstr "Orden de compra creada"

#: src/components/wizards/OrderPartsWizard.tsx:75
msgid "New Supplier Part"
msgstr "Nueva Parte de Proveedor"

#: src/components/wizards/OrderPartsWizard.tsx:77
#: src/tables/purchasing/SupplierPartTable.tsx:175
msgid "Supplier part created"
msgstr ""

#: src/components/wizards/OrderPartsWizard.tsx:103
msgid "Add to Purchase Order"
msgstr "Añadir a la orden de compra"

#: src/components/wizards/OrderPartsWizard.tsx:115
msgid "Part added to purchase order"
msgstr "Parte añadida a la orden de compra"

#: src/components/wizards/OrderPartsWizard.tsx:156
msgid "Select supplier part"
msgstr ""

#: src/components/wizards/OrderPartsWizard.tsx:171
msgid "New supplier part"
msgstr "Nueva parte de proveedor"

#: src/components/wizards/OrderPartsWizard.tsx:195
msgid "Select purchase order"
msgstr ""

#: src/components/wizards/OrderPartsWizard.tsx:209
msgid "New purchase order"
msgstr "Nueva orden de compra"

#: src/components/wizards/OrderPartsWizard.tsx:257
msgid "Add to selected purchase order"
msgstr "Añadir a la orden de compra seleccionada"

#: src/components/wizards/OrderPartsWizard.tsx:269
#: src/components/wizards/OrderPartsWizard.tsx:382
msgid "No parts selected"
msgstr "No hay partes seleccionadas"

#: src/components/wizards/OrderPartsWizard.tsx:270
msgid "No purchaseable parts selected"
msgstr "Ninguna pieza comprable seleccionada"

#: src/components/wizards/OrderPartsWizard.tsx:306
msgid "Parts Added"
msgstr "Partes añadidas"

#: src/components/wizards/OrderPartsWizard.tsx:307
msgid "All selected parts added to a purchase order"
msgstr "Todas las piezas seleccionadas añadidas a una orden de compra"

#: src/components/wizards/OrderPartsWizard.tsx:383
msgid "You must select at least one part to order"
msgstr "Debe seleccionar al menos una parte para ordenar"

#: src/components/wizards/OrderPartsWizard.tsx:394
msgid "Supplier part is required"
msgstr "La pieza del proveedor es necesaria"

#: src/components/wizards/OrderPartsWizard.tsx:398
msgid "Quantity is required"
msgstr "Cantidad requerida"

#: src/components/wizards/OrderPartsWizard.tsx:411
msgid "Invalid part selection"
msgstr "Selección de pieza inválida"

#: src/components/wizards/OrderPartsWizard.tsx:413
msgid "Please correct the errors in the selected parts"
msgstr "Por favor, corrija los errores en las partes seleccionadas"

#: src/components/wizards/OrderPartsWizard.tsx:424
#: src/tables/build/BuildLineTable.tsx:648
#: src/tables/part/PartTable.tsx:377
#: src/tables/sales/SalesOrderLineItemTable.tsx:334
msgid "Order Parts"
msgstr "Pedir Piezas"

#: src/contexts/LanguageContext.tsx:20
msgid "Arabic"
msgstr "Árabe"

#: src/contexts/LanguageContext.tsx:21
msgid "Bulgarian"
msgstr "Búlgaro"

#: src/contexts/LanguageContext.tsx:22
msgid "Czech"
msgstr "Checo"

#: src/contexts/LanguageContext.tsx:23
msgid "Danish"
msgstr "Danés"

#: src/contexts/LanguageContext.tsx:24
msgid "German"
msgstr "Alemán"

#: src/contexts/LanguageContext.tsx:25
msgid "Greek"
msgstr "Griego"

#: src/contexts/LanguageContext.tsx:26
msgid "English"
msgstr "Inglés"

#: src/contexts/LanguageContext.tsx:27
msgid "Spanish"
msgstr "Español"

#: src/contexts/LanguageContext.tsx:28
msgid "Spanish (Mexican)"
msgstr "Español (México)"

#: src/contexts/LanguageContext.tsx:29
msgid "Estonian"
msgstr "Estonio"

#: src/contexts/LanguageContext.tsx:30
msgid "Farsi / Persian"
msgstr "Farsi / Persa"

#: src/contexts/LanguageContext.tsx:31
msgid "Finnish"
msgstr "Finés"

#: src/contexts/LanguageContext.tsx:32
msgid "French"
msgstr "Francés"

#: src/contexts/LanguageContext.tsx:33
msgid "Hebrew"
msgstr "Hebreo"

#: src/contexts/LanguageContext.tsx:34
msgid "Hindi"
msgstr "Hindú"

#: src/contexts/LanguageContext.tsx:35
msgid "Hungarian"
msgstr "Húngaro"

#: src/contexts/LanguageContext.tsx:36
msgid "Italian"
msgstr "Italiano"

#: src/contexts/LanguageContext.tsx:37
msgid "Japanese"
msgstr "Japonés"

#: src/contexts/LanguageContext.tsx:38
msgid "Korean"
msgstr "Coreano"

#: src/contexts/LanguageContext.tsx:39
msgid "Lithuanian"
msgstr "Lituano"

#: src/contexts/LanguageContext.tsx:40
msgid "Latvian"
msgstr "Letón"

#: src/contexts/LanguageContext.tsx:41
msgid "Dutch"
msgstr "Holandés"

#: src/contexts/LanguageContext.tsx:42
msgid "Norwegian"
msgstr "Noruego"

#: src/contexts/LanguageContext.tsx:43
msgid "Polish"
msgstr "Polaco"

#: src/contexts/LanguageContext.tsx:44
msgid "Portuguese"
msgstr "Portugués"

#: src/contexts/LanguageContext.tsx:45
msgid "Portuguese (Brazilian)"
msgstr "Portugués (Brasileño)"

#: src/contexts/LanguageContext.tsx:46
msgid "Romanian"
msgstr "Rumano"

#: src/contexts/LanguageContext.tsx:47
msgid "Russian"
msgstr "Ruso"

#: src/contexts/LanguageContext.tsx:48
msgid "Slovak"
msgstr "Eslovaco"

#: src/contexts/LanguageContext.tsx:49
msgid "Slovenian"
msgstr "Esloveno"

#: src/contexts/LanguageContext.tsx:50
msgid "Serbian"
msgstr "Serbio"

#: src/contexts/LanguageContext.tsx:51
msgid "Swedish"
msgstr "Sueco"

#: src/contexts/LanguageContext.tsx:52
msgid "Thai"
msgstr "Tailandés"

#: src/contexts/LanguageContext.tsx:53
msgid "Turkish"
msgstr "Turco"

#: src/contexts/LanguageContext.tsx:54
msgid "Ukrainian"
msgstr "Ucraniano"

#: src/contexts/LanguageContext.tsx:55
msgid "Vietnamese"
msgstr "Vietnamita"

#: src/contexts/LanguageContext.tsx:56
msgid "Chinese (Simplified)"
msgstr "Chino (Simplificado)"

#: src/contexts/LanguageContext.tsx:57
msgid "Chinese (Traditional)"
msgstr "Chino (Tradicional)"

#: src/defaults/actions.tsx:18
#: src/defaults/links.tsx:27
#: src/defaults/menuItems.tsx:9
#~ msgid "Home"
#~ msgstr "Home"

#: src/defaults/actions.tsx:26
msgid "Go to the InvenTree dashboard"
msgstr "Ir al panel de InvenTree"

#: src/defaults/actions.tsx:33
msgid "Visit the documentation to learn more about InvenTree"
msgstr "Visite la documentación para obtener más información sobre InvenTree"

#: src/defaults/actions.tsx:41
#: src/defaults/links.tsx:140
#: src/defaults/links.tsx:186
msgid "About InvenTree"
msgstr "Acerca de InvenTree"

#: src/defaults/actions.tsx:41
#: src/defaults/links.tsx:118
#~ msgid "About this Inventree instance"
#~ msgstr "About this Inventree instance"

#: src/defaults/actions.tsx:42
msgid "About the InvenTree org"
msgstr "Acerca de la organización InvenTree"

#: src/defaults/actions.tsx:48
msgid "Server Information"
msgstr "Información del Servidor"

#: src/defaults/actions.tsx:49
#: src/defaults/links.tsx:169
msgid "About this InvenTree instance"
msgstr "Acerca de esta instancia de InvenTree"

#: src/defaults/actions.tsx:55
#: src/defaults/links.tsx:153
#: src/defaults/links.tsx:175
msgid "License Information"
msgstr "Información de licencia"

#: src/defaults/actions.tsx:56
msgid "Licenses for dependencies of the service"
msgstr "Licencias para dependencias del servicio"

#: src/defaults/actions.tsx:62
msgid "Open Navigation"
msgstr "Abrir navegación"

#: src/defaults/actions.tsx:63
msgid "Open the main navigation menu"
msgstr "Abrir el menú de navegación principal"

#: src/defaults/actions.tsx:70
msgid "Scan a barcode or QR code"
msgstr "Escanear el código de barras o código QR"

#: src/defaults/actions.tsx:81
msgid "Go to the Admin Center"
msgstr "Ir al Centro de Administración"

#: src/defaults/dashboardItems.tsx:29
#~ msgid "Latest Parts"
#~ msgstr "Latest Parts"

#: src/defaults/dashboardItems.tsx:36
#~ msgid "BOM Waiting Validation"
#~ msgstr "BOM Waiting Validation"

#: src/defaults/dashboardItems.tsx:43
#~ msgid "Recently Updated"
#~ msgstr "Recently Updated"

#: src/defaults/dashboardItems.tsx:57
#~ msgid "Depleted Stock"
#~ msgstr "Depleted Stock"

#: src/defaults/dashboardItems.tsx:71
#~ msgid "Expired Stock"
#~ msgstr "Expired Stock"

#: src/defaults/dashboardItems.tsx:78
#~ msgid "Stale Stock"
#~ msgstr "Stale Stock"

#: src/defaults/dashboardItems.tsx:85
#~ msgid "Build Orders In Progress"
#~ msgstr "Build Orders In Progress"

#: src/defaults/dashboardItems.tsx:99
#~ msgid "Outstanding Purchase Orders"
#~ msgstr "Outstanding Purchase Orders"

#: src/defaults/dashboardItems.tsx:113
#~ msgid "Outstanding Sales Orders"
#~ msgstr "Outstanding Sales Orders"

#: src/defaults/dashboardItems.tsx:127
#~ msgid "Current News"
#~ msgstr "Current News"

#: src/defaults/defaultHostList.tsx:8
#~ msgid "InvenTree Demo"
#~ msgstr "InvenTree Demo"

#: src/defaults/defaultHostList.tsx:16
#~ msgid "Local Server"
#~ msgstr "Local Server"

#: src/defaults/links.tsx:17
#~ msgid "GitHub"
#~ msgstr "GitHub"

#: src/defaults/links.tsx:22
#~ msgid "Demo"
#~ msgstr "Demo"

#: src/defaults/links.tsx:41
#: src/defaults/menuItems.tsx:71
#: src/pages/Index/Playground.tsx:217
#~ msgid "Playground"
#~ msgstr "Playground"

#: src/defaults/links.tsx:76
#~ msgid "Instance"
#~ msgstr "Instance"

#: src/defaults/links.tsx:83
#~ msgid "InvenTree"
#~ msgstr "InvenTree"

#: src/defaults/links.tsx:93
msgid "API"
msgstr "API"

#: src/defaults/links.tsx:96
msgid "InvenTree API documentation"
msgstr "Documentación del API de InvenTree"

#: src/defaults/links.tsx:100
msgid "Developer Manual"
msgstr "Manual del desarrollador"

#: src/defaults/links.tsx:103
msgid "InvenTree developer manual"
msgstr "Manual del desarrollador de InvenTree"

#: src/defaults/links.tsx:107
msgid "FAQ"
msgstr "FAQ"

#: src/defaults/links.tsx:110
msgid "Frequently asked questions"
msgstr "Preguntas frecuentes"

#: src/defaults/links.tsx:114
msgid "GitHub Repository"
msgstr "Repositorio de GitHub"

#: src/defaults/links.tsx:117
msgid "InvenTree source code on GitHub"
msgstr "Código fuente de InvenTree en GitHub"

#: src/defaults/links.tsx:117
#~ msgid "Licenses for packages used by InvenTree"
#~ msgstr "Licenses for packages used by InvenTree"

#: src/defaults/links.tsx:127
#: src/defaults/links.tsx:168
msgid "System Information"
msgstr "Información del sistema"

#: src/defaults/links.tsx:134
#~ msgid "Licenses"
#~ msgstr "Licenses"

#: src/defaults/links.tsx:176
msgid "Licenses for dependencies of the InvenTree software"
msgstr "Licencias para dependencias del software de InvenTree"

#: src/defaults/links.tsx:187
msgid "About the InvenTree Project"
msgstr "Acerca del proyecto InvenTree"

#: src/defaults/menuItems.tsx:7
#~ msgid "Open sourcea"
#~ msgstr "Open sourcea"

#: src/defaults/menuItems.tsx:9
#~ msgid "Open source"
#~ msgstr "Open source"

#: src/defaults/menuItems.tsx:10
#~ msgid "Start page of your instance."
#~ msgstr "Start page of your instance."

#: src/defaults/menuItems.tsx:10
#~ msgid "This Pokémon’s cry is very loud and distracting"
#~ msgstr "This Pokémon’s cry is very loud and distracting"

#: src/defaults/menuItems.tsx:12
#~ msgid "This Pokémon’s cry is very loud and distracting and more and more and more"
#~ msgstr "This Pokémon’s cry is very loud and distracting and more and more and more"

#: src/defaults/menuItems.tsx:15
#~ msgid "Profile page"
#~ msgstr "Profile page"

#: src/defaults/menuItems.tsx:17
#~ msgid "User attributes and design settings."
#~ msgstr "User attributes and design settings."

#: src/defaults/menuItems.tsx:21
#~ msgid "Free for everyone"
#~ msgstr "Free for everyone"

#: src/defaults/menuItems.tsx:22
#~ msgid "The fluid of Smeargle’s tail secretions changes"
#~ msgstr "The fluid of Smeargle’s tail secretions changes"

#: src/defaults/menuItems.tsx:23
#~ msgid "View for interactive scanning and multiple actions."
#~ msgstr "View for interactive scanning and multiple actions."

#: src/defaults/menuItems.tsx:24
#~ msgid "The fluid of Smeargle’s tail secretions changes in the intensity"
#~ msgstr "The fluid of Smeargle’s tail secretions changes in the intensity"

#: src/defaults/menuItems.tsx:32
#~ msgid "abc"
#~ msgstr "abc"

#: src/defaults/menuItems.tsx:37
#~ msgid "Random image"
#~ msgstr "Random image"

#: src/defaults/menuItems.tsx:40
#~ msgid "Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Duis autem vel eum iriure dolor in hendrerit in vulputate velit esse molestie consequat, vel illum dolore eu feugiat nulla facilisis at vero eros et accumsan et iusto odio dignissim qui blandit praesent luptatum zzril delenit augue duis dolore the feugait nulla facilisi. Lorem ipsum dolor sit amet, consectetuer adipiscing elit, sed diam nonummy nibh euismod tincidunt ut laoreet dolore magna aliquam erat volutpat. Ut wisi enim ad minim veniam, quis nostrud exerci tation ullamcorper suscipit lobortis nisl ut aliquip ex ea commodo consequat. Duis autem vel eum iriure dolor in hendrerit in vulputate velit esse molestie consequat, vel illum dolore eu feugiat nulla facilisis at vero eros et accumsan et iusto odio dignissim qui blandit praesent luptatum zzril delenit augue duis dolore the feugait nulla facilisi. Name liber tempor cum soluta nobis eleifend option congue nihil imperdiet doming id quod mazim placerat facer possim assume. Lorem ipsum dolor sit amet, consectetuer adipiscing elit, sed diam nonummy nibh euismod tincidunt ut laoreet dolore magna aliquam erat volutpat. Ut wisi enim ad minim veniam, quis nostrud exerci tation ullamcorper suscipit lobortis nisl ut aliquip ex ea commodo consequat. Duis autem vel eum iriure dolor in hendrerit in vulputate velit esse molestie consequat, vel illum dolore eu feugiat nulla facilisis. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, At accusam aliquyam diam diam dolore dolores duo eirmod eos erat, et nonumy sed tempor et et invidunt justo labore Stet clita ea et gubergren, kasd magna no rebum. sanctus sea sed takimata ut vero voluptua. est Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat. Consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Lorem ipsum dolor"
#~ msgstr "Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Duis autem vel eum iriure dolor in hendrerit in vulputate velit esse molestie consequat, vel illum dolore eu feugiat nulla facilisis at vero eros et accumsan et iusto odio dignissim qui blandit praesent luptatum zzril delenit augue duis dolore the feugait nulla facilisi. Lorem ipsum dolor sit amet, consectetuer adipiscing elit, sed diam nonummy nibh euismod tincidunt ut laoreet dolore magna aliquam erat volutpat. Ut wisi enim ad minim veniam, quis nostrud exerci tation ullamcorper suscipit lobortis nisl ut aliquip ex ea commodo consequat. Duis autem vel eum iriure dolor in hendrerit in vulputate velit esse molestie consequat, vel illum dolore eu feugiat nulla facilisis at vero eros et accumsan et iusto odio dignissim qui blandit praesent luptatum zzril delenit augue duis dolore the feugait nulla facilisi. Name liber tempor cum soluta nobis eleifend option congue nihil imperdiet doming id quod mazim placerat facer possim assume. Lorem ipsum dolor sit amet, consectetuer adipiscing elit, sed diam nonummy nibh euismod tincidunt ut laoreet dolore magna aliquam erat volutpat. Ut wisi enim ad minim veniam, quis nostrud exerci tation ullamcorper suscipit lobortis nisl ut aliquip ex ea commodo consequat. Duis autem vel eum iriure dolor in hendrerit in vulputate velit esse molestie consequat, vel illum dolore eu feugiat nulla facilisis. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, At accusam aliquyam diam diam dolore dolores duo eirmod eos erat, et nonumy sed tempor et et invidunt justo labore Stet clita ea et gubergren, kasd magna no rebum. sanctus sea sed takimata ut vero voluptua. est Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat. Consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Lorem ipsum dolor"

#: src/defaults/menuItems.tsx:105
#~ msgid "Yanma is capable of seeing 360 degrees without"
#~ msgstr "Yanma is capable of seeing 360 degrees without"

#: src/defaults/menuItems.tsx:111
#~ msgid "The shell’s rounded shape and the grooves on its."
#~ msgstr "The shell’s rounded shape and the grooves on its."

#: src/defaults/menuItems.tsx:116
#~ msgid "Analytics"
#~ msgstr "Analytics"

#: src/defaults/menuItems.tsx:118
#~ msgid "This Pokémon uses its flying ability to quickly chase"
#~ msgstr "This Pokémon uses its flying ability to quickly chase"

#: src/defaults/menuItems.tsx:125
#~ msgid "Combusken battles with the intensely hot flames it spews"
#~ msgstr "Combusken battles with the intensely hot flames it spews"

#: src/enums/Roles.tsx:32
msgid "Admin"
msgstr "Admin"

#: src/enums/Roles.tsx:34
#: src/pages/Index/Settings/SystemSettings.tsx:243
#: src/pages/build/BuildIndex.tsx:63
#: src/pages/part/PartDetail.tsx:541
#: src/pages/sales/SalesOrderDetail.tsx:393
msgid "Build Orders"
msgstr "Ordenes de Producción"

#: src/enums/Roles.tsx:50
#: src/pages/Index/Settings/AdminCenter/Index.tsx:187
msgid "Stocktake"
msgstr "Inventario"

#: src/forms/AttachmentForms.tsx:57
#~ msgid "Add File"
#~ msgstr "Add File"

#: src/forms/AttachmentForms.tsx:57
#~ msgid "Add Link"
#~ msgstr "Add Link"

#: src/forms/AttachmentForms.tsx:58
#~ msgid "File added"
#~ msgstr "File added"

#: src/forms/AttachmentForms.tsx:58
#~ msgid "Link added"
#~ msgstr "Link added"

#: src/forms/AttachmentForms.tsx:99
#~ msgid "Edit File"
#~ msgstr "Edit File"

#: src/forms/AttachmentForms.tsx:99
#~ msgid "Edit Link"
#~ msgstr "Edit Link"

#: src/forms/AttachmentForms.tsx:100
#~ msgid "File updated"
#~ msgstr "File updated"

#: src/forms/AttachmentForms.tsx:100
#~ msgid "Link updated"
#~ msgstr "Link updated"

#: src/forms/AttachmentForms.tsx:125
#~ msgid "Attachment deleted"
#~ msgstr "Attachment deleted"

#: src/forms/AttachmentForms.tsx:128
#~ msgid "Are you sure you want to delete this attachment?"
#~ msgstr "Are you sure you want to delete this attachment?"

#: src/forms/BuildForms.tsx:248
#~ msgid "Remove output"
#~ msgstr "Remove output"

#: src/forms/BuildForms.tsx:281
#: src/tables/build/BuildAllocatedStockTable.tsx:147
#: src/tables/build/BuildOrderTestTable.tsx:184
#: src/tables/build/BuildOrderTestTable.tsx:208
#: src/tables/build/BuildOutputTable.tsx:467
msgid "Build Output"
msgstr ""

#: src/forms/BuildForms.tsx:283
#: src/forms/BuildForms.tsx:357
#: src/forms/BuildForms.tsx:411
#: src/forms/PurchaseOrderForms.tsx:674
#: src/forms/ReturnOrderForms.tsx:193
#: src/forms/ReturnOrderForms.tsx:240
#: src/forms/StockForms.tsx:625
#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:87
#: src/pages/build/BuildDetail.tsx:115
#: src/pages/core/UserDetail.tsx:151
#: src/pages/purchasing/PurchaseOrderDetail.tsx:151
#: src/pages/sales/ReturnOrderDetail.tsx:116
#: src/pages/sales/SalesOrderDetail.tsx:125
#: src/pages/stock/StockDetail.tsx:140
#: src/tables/Filter.tsx:253
#: src/tables/machine/MachineListTable.tsx:338
#: src/tables/part/PartPurchaseOrdersTable.tsx:37
#: src/tables/sales/ReturnOrderLineItemTable.tsx:128
#: src/tables/sales/ReturnOrderLineItemTable.tsx:165
#: src/tables/settings/CustomStateTable.tsx:79
#: src/tables/settings/ImportSessionTable.tsx:114
#: src/tables/stock/StockItemTable.tsx:311
#: src/tables/stock/StockTrackingTable.tsx:63
msgid "Status"
msgstr "Estado"

#: src/forms/BuildForms.tsx:305
msgid "Complete Build Outputs"
msgstr "Salidas de construcción completadas"

#: src/forms/BuildForms.tsx:308
msgid "Build outputs have been completed"
msgstr "Salidas de construcción se han completado"

#: src/forms/BuildForms.tsx:375
msgid "Scrap Build Outputs"
msgstr "Eliminar salidas de construcción"

#: src/forms/BuildForms.tsx:378
msgid "Build outputs have been scrapped"
msgstr "Salidas de construcción eliminadas"

#: src/forms/BuildForms.tsx:408
#~ msgid "Selected build outputs will be deleted"
#~ msgstr "Selected build outputs will be deleted"

#: src/forms/BuildForms.tsx:421
msgid "Cancel Build Outputs"
msgstr "Cancelar salidas de construcción"

#: src/forms/BuildForms.tsx:424
msgid "Build outputs have been cancelled"
msgstr "Las salidas de la construcción han sido canceladas"

#: src/forms/BuildForms.tsx:470
#~ msgid "Remove line"
#~ msgstr "Remove line"

#: src/forms/BuildForms.tsx:545
#: src/forms/SalesOrderForms.tsx:267
#: src/tables/build/BuildLineTable.tsx:181
#: src/tables/sales/SalesOrderLineItemTable.tsx:306
#: src/tables/stock/StockItemTable.tsx:322
msgid "Allocated"
msgstr "Asignado"

#: src/forms/BuildForms.tsx:579
#: src/forms/SalesOrderForms.tsx:256
#: src/pages/build/BuildDetail.tsx:205
msgid "Source Location"
msgstr "Ubicación origen"

#: src/forms/BuildForms.tsx:580
#: src/forms/SalesOrderForms.tsx:257
msgid "Select the source location for the stock allocation"
msgstr "Seleccione la ubicación de origen para la asignación de stock"

#: src/forms/BuildForms.tsx:600
#: src/forms/SalesOrderForms.tsx:297
#: src/tables/build/BuildLineTable.tsx:442
#: src/tables/build/BuildLineTable.tsx:574
#: src/tables/build/BuildLineTable.tsx:663
#: src/tables/sales/SalesOrderLineItemTable.tsx:344
#: src/tables/sales/SalesOrderLineItemTable.tsx:375
msgid "Allocate Stock"
msgstr "Stock Asignado"

#: src/forms/BuildForms.tsx:603
#: src/forms/SalesOrderForms.tsx:302
msgid "Stock items allocated"
msgstr "Artículos de stock seleccionados"

#: src/forms/CompanyForms.tsx:150
#~ msgid "Company updated"
#~ msgstr "Company updated"

#: src/forms/PartForms.tsx:70
#: src/forms/PartForms.tsx:157
#: src/pages/part/CategoryDetail.tsx:123
#: src/pages/part/PartDetail.tsx:385
#: src/tables/part/PartCategoryTable.tsx:93
#: src/tables/part/PartTable.tsx:303
msgid "Subscribed"
msgstr "Suscrito"

#: src/forms/PartForms.tsx:71
msgid "Subscribe to notifications for this part"
msgstr "Suscríbete a las notificaciones de esta pieza"

#: src/forms/PartForms.tsx:106
#~ msgid "Create Part"
#~ msgstr "Create Part"

#: src/forms/PartForms.tsx:108
#~ msgid "Part created"
#~ msgstr "Part created"

#: src/forms/PartForms.tsx:129
#~ msgid "Part updated"
#~ msgstr "Part updated"

#: src/forms/PartForms.tsx:143
msgid "Parent part category"
msgstr "Categoría superior de pieza"

#: src/forms/PartForms.tsx:158
msgid "Subscribe to notifications for this category"
msgstr "Suscribirse a las notificaciones de esta categoría"

#: src/forms/PurchaseOrderForms.tsx:350
msgid "Assign Batch Code and Serial Numbers"
msgstr "Asignar código de lote y números de serie"

#: src/forms/PurchaseOrderForms.tsx:352
msgid "Assign Batch Code"
msgstr "Asignar código de lote"

#: src/forms/PurchaseOrderForms.tsx:372
msgid "Choose Location"
msgstr "Elegir ubicación"

#: src/forms/PurchaseOrderForms.tsx:380
msgid "Item Destination selected"
msgstr "Destino de artículo seleccionado"

#: src/forms/PurchaseOrderForms.tsx:390
msgid "Part category default location selected"
msgstr "Ubicación por defecto de la categoría de pieza eleccionada"

#: src/forms/PurchaseOrderForms.tsx:400
msgid "Received stock location selected"
msgstr "Seleccionada ubicación de existencias recibidas"

#: src/forms/PurchaseOrderForms.tsx:405
msgid "Default location selected"
msgstr "Ubicación por defecto seleccionada"

#: src/forms/PurchaseOrderForms.tsx:421
#~ msgid "Assign Batch Code{0}"
#~ msgstr "Assign Batch Code{0}"

#: src/forms/PurchaseOrderForms.tsx:444
#: src/forms/StockForms.tsx:428
#~ msgid "Remove item from list"
#~ msgstr "Remove item from list"

#: src/forms/PurchaseOrderForms.tsx:464
msgid "Set Location"
msgstr "Establecer ubicación"

#: src/forms/PurchaseOrderForms.tsx:481
msgid "Set Expiry Date"
msgstr "Establecer la fecha de caducidad"

#: src/forms/PurchaseOrderForms.tsx:489
#: src/forms/StockForms.tsx:606
msgid "Adjust Packaging"
msgstr "Ajustar empaquetado"

#: src/forms/PurchaseOrderForms.tsx:497
#: src/forms/StockForms.tsx:597
msgid "Change Status"
msgstr "Cambiar Estado"

#: src/forms/PurchaseOrderForms.tsx:503
msgid "Add Note"
msgstr "Añadir Nota"

#: src/forms/PurchaseOrderForms.tsx:552
#: src/forms/StockForms.tsx:702
#: src/forms/StockForms.tsx:746
#: src/forms/StockForms.tsx:785
#: src/forms/StockForms.tsx:821
#: src/forms/StockForms.tsx:859
#: src/forms/StockForms.tsx:901
#: src/forms/StockForms.tsx:949
#: src/forms/StockForms.tsx:993
#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:88
#: src/pages/core/UserDetail.tsx:158
#: src/pages/stock/StockDetail.tsx:222
#: src/tables/ColumnRenderers.tsx:61
#: src/tables/stock/StockTrackingTable.tsx:96
msgid "Location"
msgstr "Ubicación"

#: src/forms/PurchaseOrderForms.tsx:566
#~ msgid "Serial numbers"
#~ msgstr "Serial numbers"

#: src/forms/PurchaseOrderForms.tsx:567
msgid "Store at default location"
msgstr "Guardar en la ubicación predeterminada"

#: src/forms/PurchaseOrderForms.tsx:582
msgid "Store at line item destination "
msgstr ""

#: src/forms/PurchaseOrderForms.tsx:582
#~ msgid "Store at line item destination"
#~ msgstr "Store at line item destination"

#: src/forms/PurchaseOrderForms.tsx:594
msgid "Store with already received stock"
msgstr "Guardar con cantidad ya recibida"

#: src/forms/PurchaseOrderForms.tsx:618
#: src/pages/build/BuildDetail.tsx:219
#: src/pages/stock/StockDetail.tsx:204
#: src/pages/stock/StockDetail.tsx:859
#: src/tables/build/BuildAllocatedStockTable.tsx:130
#: src/tables/build/BuildOrderTestTable.tsx:196
#: src/tables/build/BuildOutputTable.tsx:86
#: src/tables/sales/SalesOrderAllocationTable.tsx:148
#: src/tables/stock/StockItemTable.tsx:377
msgid "Batch Code"
msgstr "Código de lote"

#: src/forms/PurchaseOrderForms.tsx:619
msgid "Enter batch code for received items"
msgstr "Introduzca el código de lote para los artículos recibidos"

#: src/forms/PurchaseOrderForms.tsx:632
#: src/forms/StockForms.tsx:166
msgid "Serial Numbers"
msgstr "Números de serie"

#: src/forms/PurchaseOrderForms.tsx:633
msgid "Enter serial numbers for received items"
msgstr "Introduzca números de serie para los elementos recibidos"

#: src/forms/PurchaseOrderForms.tsx:647
#: src/pages/stock/StockDetail.tsx:306
#: src/tables/stock/StockItemTable.tsx:279
msgid "Expiry Date"
msgstr "Fecha de caducidad"

#: src/forms/PurchaseOrderForms.tsx:648
msgid "Enter an expiry date for received items"
msgstr "Introduzca una fecha de caducidad para los artículos recibidos"

#: src/forms/PurchaseOrderForms.tsx:658
#~ msgid "Receive line items"
#~ msgstr "Receive line items"

#: src/forms/PurchaseOrderForms.tsx:660
#: src/forms/StockForms.tsx:641
#: src/pages/company/SupplierPartDetail.tsx:172
#: src/pages/company/SupplierPartDetail.tsx:236
#: src/pages/stock/StockDetail.tsx:343
#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:199
msgid "Packaging"
msgstr "Empaquetado"

#: src/forms/PurchaseOrderForms.tsx:684
#: src/pages/company/SupplierPartDetail.tsx:119
#: src/tables/ColumnRenderers.tsx:147
msgid "Note"
msgstr "Nota"

#: src/forms/PurchaseOrderForms.tsx:752
#: src/pages/company/SupplierPartDetail.tsx:137
#: src/tables/purchasing/SupplierPriceBreakTable.tsx:57
msgid "SKU"
msgstr "SKU"

#: src/forms/PurchaseOrderForms.tsx:753
#: src/tables/part/PartPurchaseOrdersTable.tsx:126
#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:185
#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:252
#: src/tables/sales/ReturnOrderLineItemTable.tsx:160
msgid "Received"
msgstr "Recibido"

#: src/forms/PurchaseOrderForms.tsx:770
msgid "Receive Line Items"
msgstr "Recibir partidas"

#: src/forms/PurchaseOrderForms.tsx:776
msgid "Items received"
msgstr "Artículos recibidos"

#: src/forms/ReturnOrderForms.tsx:253
msgid "Receive Items"
msgstr "Recibir artículos"

#: src/forms/ReturnOrderForms.tsx:260
msgid "Item received into stock"
msgstr "Artículo recibido en existencias"

#: src/forms/StockForms.tsx:76
msgid "Next batch code"
msgstr "Próximo código de lote"

#: src/forms/StockForms.tsx:84
#: src/hooks/UsePlaceholder.tsx:57
msgid "Next serial number"
msgstr "Siguiente número de serie"

#: src/forms/StockForms.tsx:110
#~ msgid "Create Stock Item"
#~ msgstr "Create Stock Item"

#: src/forms/StockForms.tsx:145
msgid "Add given quantity as packs instead of individual items"
msgstr "Agrega una cantidad dada en conjunto en lugar de artículos individuales"

#: src/forms/StockForms.tsx:158
#~ msgid "Stock item updated"
#~ msgstr "Stock item updated"

#: src/forms/StockForms.tsx:159
msgid "Enter initial quantity for this stock item"
msgstr "Cantidad inicial de existencias que tendrá este artículo"

#: src/forms/StockForms.tsx:168
msgid "Enter serial numbers for new stock (or leave blank)"
msgstr "Introduzca números de serie para las nuevas existencias (o deje en blanco)"

#: src/forms/StockForms.tsx:183
msgid "Stock Status"
msgstr "Estado del stock"

#: src/forms/StockForms.tsx:239
#: src/pages/stock/StockDetail.tsx:575
#: src/tables/stock/StockItemTable.tsx:533
#: src/tables/stock/StockItemTable.tsx:666
msgid "Add Stock Item"
msgstr "Añadir artículo de stock"

#: src/forms/StockForms.tsx:283
msgid "Select the part to install"
msgstr "Selecciona la pieza a instalar"

#: src/forms/StockForms.tsx:404
msgid "Confirm Stock Transfer"
msgstr "Confirmar transferencia de existencias"

#: src/forms/StockForms.tsx:527
msgid "Loading..."
msgstr "Cargando..."

#: src/forms/StockForms.tsx:585
msgid "Move to default location"
msgstr "Mover a la ubicación predeterminada"

#: src/forms/StockForms.tsx:705
msgid "Move"
msgstr "Mover"

#: src/forms/StockForms.tsx:748
#: src/forms/StockForms.tsx:787
#: src/forms/StockForms.tsx:823
#: src/forms/StockForms.tsx:861
#: src/forms/StockForms.tsx:903
#: src/forms/StockForms.tsx:951
#: src/forms/StockForms.tsx:995
#: src/pages/company/SupplierPartDetail.tsx:190
#: src/pages/company/SupplierPartDetail.tsx:371
#: src/pages/part/PartDetail.tsx:252
#: src/pages/part/PartDetail.tsx:749
#: src/tables/purchasing/SupplierPartTable.tsx:208
#: src/tables/stock/StockItemTable.tsx:342
msgid "In Stock"
msgstr "En Stock"

#: src/forms/StockForms.tsx:824
#: src/pages/Index/Scan.tsx:182
#: src/pages/stock/StockDetail.tsx:708
msgid "Count"
msgstr "Contar"

#: src/forms/StockForms.tsx:1102
#: src/pages/stock/StockDetail.tsx:720
#: src/tables/stock/StockItemTable.tsx:584
msgid "Add Stock"
msgstr "Agregar existencias"

#: src/forms/StockForms.tsx:1103
msgid "Stock added"
msgstr "Existencias añadidas"

#: src/forms/StockForms.tsx:1112
#: src/pages/stock/StockDetail.tsx:729
#: src/tables/stock/StockItemTable.tsx:593
msgid "Remove Stock"
msgstr "Eliminar existencias"

#: src/forms/StockForms.tsx:1113
msgid "Stock removed"
msgstr "Existencias eliminadas"

#: src/forms/StockForms.tsx:1122
#: src/pages/part/PartDetail.tsx:925
#: src/pages/stock/StockDetail.tsx:742
#: src/tables/stock/StockItemTable.tsx:602
msgid "Transfer Stock"
msgstr "Transferir existencias"

#: src/forms/StockForms.tsx:1123
msgid "Stock transferred"
msgstr "Existencias transferidas"

#: src/forms/StockForms.tsx:1132
#: src/pages/part/PartDetail.tsx:914
#: src/pages/stock/LocationDetail.tsx:313
#: src/pages/stock/LocationDetail.tsx:317
#: src/tables/stock/StockItemTable.tsx:573
#: src/tables/stock/StockItemTable.tsx:577
msgid "Count Stock"
msgstr "Contar existencias"

#: src/forms/StockForms.tsx:1133
msgid "Stock counted"
msgstr "Existencias contadas"

#: src/forms/StockForms.tsx:1142
msgid "Change Stock Status"
msgstr "Cambiar estado de existencias"

#: src/forms/StockForms.tsx:1143
msgid "Stock status changed"
msgstr "Estado de existencias cambiado"

#: src/forms/StockForms.tsx:1152
msgid "Merge Stock"
msgstr "Juntar existencias"

#: src/forms/StockForms.tsx:1153
msgid "Stock merged"
msgstr "Existencias fusionadas"

#: src/forms/StockForms.tsx:1168
msgid "Assign Stock to Customer"
msgstr "Asignar existencias a cliente"

#: src/forms/StockForms.tsx:1169
msgid "Stock assigned to customer"
msgstr "Existencias asignadas a cliente"

#: src/forms/StockForms.tsx:1179
#: src/tables/stock/StockItemTable.tsx:655
msgid "Delete Stock Items"
msgstr "Eliminar existencias"

#: src/forms/StockForms.tsx:1180
msgid "Stock deleted"
msgstr "Existencias eliminadas"

#: src/forms/StockForms.tsx:1187
msgid "Parent stock location"
msgstr "Ubicación del stock padre"

#: src/forms/selectionListFields.tsx:99
msgid "Entries"
msgstr "Entradas"

#: src/forms/selectionListFields.tsx:100
msgid "List of entries to choose from"
msgstr "Lista de entradas entre las que elegir"

#: src/forms/selectionListFields.tsx:104
#: src/pages/part/PartStocktakeDetail.tsx:64
#: src/tables/FilterSelectDrawer.tsx:112
#: src/tables/FilterSelectDrawer.tsx:135
#: src/tables/FilterSelectDrawer.tsx:147
#: src/tables/build/BuildOrderTestTable.tsx:142
#: src/tables/purchasing/ManufacturerPartParameterTable.tsx:38
#: src/tables/stock/StockItemTestResultTable.tsx:193
msgid "Value"
msgstr "Valor"

#: src/forms/selectionListFields.tsx:105
msgid "Label"
msgstr "Etiqueta"

#: src/functions/api.tsx:33
#: src/tables/InvenTreeTable.tsx:491
msgid "Bad request"
msgstr "Solicitud incorrecta"

#: src/functions/api.tsx:36
#: src/tables/InvenTreeTable.tsx:494
msgid "Unauthorized"
msgstr "No autorizado"

#: src/functions/api.tsx:39
#: src/tables/InvenTreeTable.tsx:497
msgid "Forbidden"
msgstr "Prohibido"

#: src/functions/api.tsx:42
#: src/tables/InvenTreeTable.tsx:500
msgid "Not found"
msgstr "No encontrado"

#: src/functions/api.tsx:45
msgid "Method not allowed"
msgstr "Método no permitido"

#: src/functions/api.tsx:48
msgid "Internal server error"
msgstr "Error interno del servidor"

#: src/functions/auth.tsx:34
#~ msgid "Error fetching token from server."
#~ msgstr "Error fetching token from server."

#: src/functions/auth.tsx:36
#~ msgid "Logout successfull"
#~ msgstr "Logout successfull"

#: src/functions/auth.tsx:60
#~ msgid "See you soon."
#~ msgstr "See you soon."

#: src/functions/auth.tsx:70
#~ msgid "Logout successful"
#~ msgstr "Logout successful"

#: src/functions/auth.tsx:71
#~ msgid "You have been logged out"
#~ msgstr "You have been logged out"

#: src/functions/auth.tsx:117
#: src/functions/auth.tsx:276
msgid "Already logged in"
msgstr "Ya iniciaste sesión"

#: src/functions/auth.tsx:118
#: src/functions/auth.tsx:277
msgid "There is a conflicting session on the server for this browser. Please logout of that first."
msgstr "Hay una sesión en conflicto en el servidor para este navegador. Por favor, cierra la sesión primero."

#: src/functions/auth.tsx:142
#~ msgid "Found an existing login - using it to log you in."
#~ msgstr "Found an existing login - using it to log you in."

#: src/functions/auth.tsx:143
#~ msgid "Found an existing login - welcome back!"
#~ msgstr "Found an existing login - welcome back!"

#: src/functions/auth.tsx:150
msgid "Logged Out"
msgstr "Desconectado"

#: src/functions/auth.tsx:151
msgid "Successfully logged out"
msgstr "Se cerró sesión correctamente"

#: src/functions/auth.tsx:189
msgid "Language changed"
msgstr ""

#: src/functions/auth.tsx:190
msgid "Your active language has been changed to the one set in your profile"
msgstr ""

#: src/functions/auth.tsx:210
msgid "Theme changed"
msgstr ""

#: src/functions/auth.tsx:211
msgid "Your active theme has been changed to the one set in your profile"
msgstr ""

#: src/functions/auth.tsx:236
msgid "Check your inbox for a reset link. This only works if you have an account. Check in spam too."
msgstr "Revisa tu bandeja de entrada para un enlace de restablecimiento. Esto solo funciona si tienes una cuenta. Revisa el correo no deseado también."

#: src/functions/auth.tsx:243
#: src/functions/auth.tsx:453
msgid "Reset failed"
msgstr "Restablecimiento fallido"

#: src/functions/auth.tsx:316
msgid "Logged In"
msgstr "Conectado"

#: src/functions/auth.tsx:317
msgid "Successfully logged in"
msgstr "Sesión iniciada correctamente"

#: src/functions/auth.tsx:413
msgid "Failed to set up MFA"
msgstr "Error al configurar MFA"

#: src/functions/auth.tsx:443
msgid "Password set"
msgstr "Contraseña establecida"

#: src/functions/auth.tsx:444
#: src/functions/auth.tsx:553
msgid "The password was set successfully. You can now login with your new password"
msgstr "La contraseña fue establecida con éxito. Ahora puede iniciar sesión con su nueva contraseña"

#: src/functions/auth.tsx:518
msgid "Password could not be changed"
msgstr "No se ha podido cambiar la contraseña"

#: src/functions/auth.tsx:536
msgid "The two password fields didn’t match"
msgstr ""

#: src/functions/auth.tsx:552
msgid "Password Changed"
msgstr "Contraseña Cambiada"

#: src/functions/forms.tsx:50
#~ msgid "Form method not provided"
#~ msgstr "Form method not provided"

#: src/functions/forms.tsx:59
#~ msgid "Response did not contain action data"
#~ msgstr "Response did not contain action data"

#: src/functions/forms.tsx:182
#~ msgid "Invalid Form"
#~ msgstr "Invalid Form"

#: src/functions/forms.tsx:183
#~ msgid "method parameter not supplied"
#~ msgstr "method parameter not supplied"

#: src/functions/notifications.tsx:13
msgid "Not implemented"
msgstr "No implementado"

#: src/functions/notifications.tsx:14
msgid "This feature is not yet implemented"
msgstr "Esta función aún no está implementada"

#: src/functions/notifications.tsx:24
#~ msgid "Permission denied"
#~ msgstr "Permission denied"

#: src/functions/notifications.tsx:26
msgid "You do not have permission to perform this action"
msgstr "No tienes permisos para realizar esta acción"

#: src/functions/notifications.tsx:37
msgid "Invalid Return Code"
msgstr "Código de devolución inválido"

#: src/functions/notifications.tsx:38
msgid "Server returned status {returnCode}"
msgstr "El servidor devolvió el estado {returnCode}"

#: src/functions/notifications.tsx:48
msgid "Timeout"
msgstr "Tiempo de espera superado"

#: src/functions/notifications.tsx:49
msgid "The request timed out"
msgstr "La solicitud ha expirado"

#: src/hooks/UseDataExport.tsx:34
msgid "Exporting Data"
msgstr ""

#: src/hooks/UseDataExport.tsx:112
msgid "Export Data"
msgstr ""

#: src/hooks/UseDataExport.tsx:115
msgid "Export"
msgstr ""

#: src/hooks/UseDataOutput.tsx:57
msgid "Process completed successfully"
msgstr ""

#: src/hooks/UseDataOutput.tsx:73
#: src/hooks/UseDataOutput.tsx:103
msgid "Process failed"
msgstr ""

#: src/hooks/UseForm.tsx:90
msgid "Item Created"
msgstr "Artículo creado"

#: src/hooks/UseForm.tsx:110
msgid "Item Updated"
msgstr "Artículo actualizado"

#: src/hooks/UseForm.tsx:135
msgid "Items Updated"
msgstr ""

#: src/hooks/UseForm.tsx:137
msgid "Update multiple items"
msgstr ""

#: src/hooks/UseForm.tsx:167
msgid "Item Deleted"
msgstr "Artículo eliminado"

#: src/hooks/UseForm.tsx:171
msgid "Are you sure you want to delete this item?"
msgstr "¿Está seguro de querer eliminar este artículo?"

#: src/hooks/UsePlaceholder.tsx:59
msgid "Latest serial number"
msgstr "Último número de serie"

#: src/pages/Auth/ChangePassword.tsx:32
#: src/pages/Auth/Reset.tsx:14
msgid "Reset Password"
msgstr "Restablecer Contraseña"

#: src/pages/Auth/ChangePassword.tsx:46
msgid "Current Password"
msgstr ""

#: src/pages/Auth/ChangePassword.tsx:47
msgid "Enter your current password"
msgstr ""

#: src/pages/Auth/ChangePassword.tsx:53
msgid "New Password"
msgstr "Nueva Contraseña"

#: src/pages/Auth/ChangePassword.tsx:54
msgid "Enter your new password"
msgstr "Introduzca su nueva contraseña"

#: src/pages/Auth/ChangePassword.tsx:60
msgid "Confirm New Password"
msgstr "Confirmar Nueva Contraseña"

#: src/pages/Auth/ChangePassword.tsx:61
msgid "Confirm your new password"
msgstr "Confirme su nueva contraseña"

#: src/pages/Auth/ChangePassword.tsx:80
msgid "Confirm"
msgstr "Confirmar"

#: src/pages/Auth/Layout.tsx:67
msgid "Log off"
msgstr ""

#: src/pages/Auth/LoggedIn.tsx:19
msgid "Checking if you are already logged in"
msgstr "Comprobando si ya ha iniciado sesión"

#: src/pages/Auth/Login.tsx:33
msgid "No selection"
msgstr "Ninguna selección"

#: src/pages/Auth/Login.tsx:91
#~ msgid "Welcome, log in below"
#~ msgstr "Welcome, log in below"

#: src/pages/Auth/Login.tsx:93
#~ msgid "Register below"
#~ msgstr "Register below"

#: src/pages/Auth/Login.tsx:99
msgid "Login"
msgstr "Ingresar"

#: src/pages/Auth/Login.tsx:105
msgid "Logging you in"
msgstr ""

#: src/pages/Auth/Login.tsx:112
msgid "Don't have an account?"
msgstr "¿No tiene una cuenta?"

#: src/pages/Auth/Logout.tsx:22
#~ msgid "Logging out"
#~ msgstr "Logging out"

#: src/pages/Auth/MFA.tsx:16
#~ msgid "Multi-Factor Login"
#~ msgstr "Multi-Factor Login"

#: src/pages/Auth/MFA.tsx:17
#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:82
msgid "Multi-Factor Authentication"
msgstr ""

#: src/pages/Auth/MFA.tsx:20
#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:688
msgid "TOTP Code"
msgstr ""

#: src/pages/Auth/MFA.tsx:22
#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:690
msgid "Enter your TOTP or recovery code"
msgstr ""

#: src/pages/Auth/MFA.tsx:32
msgid "Log in"
msgstr ""

#: src/pages/Auth/MFASetup.tsx:23
msgid "MFA Setup Required"
msgstr ""

#: src/pages/Auth/MFASetup.tsx:34
msgid "Add TOTP"
msgstr ""

#: src/pages/Auth/Register.tsx:23
msgid "Go back to login"
msgstr "Volver al inicio de sesión"

#: src/pages/Auth/Reset.tsx:41
#: src/pages/Auth/Set-Password.tsx:112
#~ msgid "Send mail"
#~ msgstr "Send mail"

#: src/pages/Auth/ResetPassword.tsx:22
#: src/pages/Auth/VerifyEmail.tsx:19
msgid "Key invalid"
msgstr ""

#: src/pages/Auth/ResetPassword.tsx:23
msgid "You need to provide a valid key to set a new password. Check your inbox for a reset link."
msgstr ""

#: src/pages/Auth/ResetPassword.tsx:30
#~ msgid "Token invalid"
#~ msgstr "Token invalid"

#: src/pages/Auth/ResetPassword.tsx:31
msgid "Set new password"
msgstr "Establecer nueva contraseña"

#: src/pages/Auth/ResetPassword.tsx:31
#~ msgid "You need to provide a valid token to set a new password. Check your inbox for a reset link."
#~ msgstr "You need to provide a valid token to set a new password. Check your inbox for a reset link."

#: src/pages/Auth/ResetPassword.tsx:35
msgid "The desired new password"
msgstr ""

#: src/pages/Auth/ResetPassword.tsx:44
msgid "Send Password"
msgstr ""

#: src/pages/Auth/Set-Password.tsx:49
#~ msgid "No token provided"
#~ msgstr "No token provided"

#: src/pages/Auth/Set-Password.tsx:50
#~ msgid "You need to provide a token to set a new password. Check your inbox for a reset link."
#~ msgstr "You need to provide a token to set a new password. Check your inbox for a reset link."

#: src/pages/Auth/VerifyEmail.tsx:20
msgid "You need to provide a valid key."
msgstr ""

#: src/pages/Auth/VerifyEmail.tsx:28
msgid "Verify Email"
msgstr ""

#: src/pages/Auth/VerifyEmail.tsx:30
msgid "Verify"
msgstr ""

#. placeholder {0}: error.statusText
#: src/pages/ErrorPage.tsx:16
msgid "Error: {0}"
msgstr "Error: {0}"

#: src/pages/ErrorPage.tsx:23
msgid "An unexpected error has occurred"
msgstr "Se ha producido un error inesperado"

#: src/pages/ErrorPage.tsx:28
#~ msgid "Sorry, an unexpected error has occurred."
#~ msgstr "Sorry, an unexpected error has occurred."

#: src/pages/Index/Dashboard.tsx:22
#~ msgid "Autoupdate"
#~ msgstr "Autoupdate"

#: src/pages/Index/Dashboard.tsx:26
#~ msgid "This page is a replacement for the old start page with the same information. This page will be deprecated and replaced by the home page."
#~ msgstr "This page is a replacement for the old start page with the same information. This page will be deprecated and replaced by the home page."

#: src/pages/Index/Home.tsx:58
#~ msgid "Welcome to your Dashboard{0}"
#~ msgstr "Welcome to your Dashboard{0}"

#: src/pages/Index/Playground.tsx:222
#~ msgid "This page is a showcase for the possibilities of Platform UI."
#~ msgstr "This page is a showcase for the possibilities of Platform UI."

#: src/pages/Index/Profile/Profile.tsx:30
#: src/pages/Index/Profile/Profile.tsx:141
#~ msgid "Notification Settings"
#~ msgstr "Notification Settings"

#: src/pages/Index/Profile/Profile.tsx:33
#~ msgid "Global Settings"
#~ msgstr "Global Settings"

#: src/pages/Index/Profile/Profile.tsx:47
#~ msgid "Settings for the current user"
#~ msgstr "Settings for the current user"

#: src/pages/Index/Profile/Profile.tsx:51
#~ msgid "Home Page Settings"
#~ msgstr "Home Page Settings"

#: src/pages/Index/Profile/Profile.tsx:76
#~ msgid "Search Settings"
#~ msgstr "Search Settings"

#: src/pages/Index/Profile/Profile.tsx:115
#: src/pages/Index/Profile/Profile.tsx:211
#~ msgid "Label Settings"
#~ msgstr "Label Settings"

#: src/pages/Index/Profile/Profile.tsx:120
#: src/pages/Index/Profile/Profile.tsx:219
#~ msgid "Report Settings"
#~ msgstr "Report Settings"

#: src/pages/Index/Profile/Profile.tsx:142
#~ msgid "Settings for the notifications"
#~ msgstr "Settings for the notifications"

#: src/pages/Index/Profile/Profile.tsx:148
#~ msgid "Global Server Settings"
#~ msgstr "Global Server Settings"

#: src/pages/Index/Profile/Profile.tsx:149
#~ msgid "Global Settings for this instance"
#~ msgstr "Global Settings for this instance"

#: src/pages/Index/Profile/Profile.tsx:153
#~ msgid "Server Settings"
#~ msgstr "Server Settings"

#: src/pages/Index/Profile/Profile.tsx:187
#~ msgid "Login Settings"
#~ msgstr "Login Settings"

#: src/pages/Index/Profile/Profile.tsx:202
#~ msgid "Barcode Settings"
#~ msgstr "Barcode Settings"

#: src/pages/Index/Profile/Profile.tsx:230
#~ msgid "Part Settings"
#~ msgstr "Part Settings"

#: src/pages/Index/Profile/Profile.tsx:255
#~ msgid "Pricing Settings"
#~ msgstr "Pricing Settings"

#: src/pages/Index/Profile/Profile.tsx:270
#~ msgid "Stock Settings"
#~ msgstr "Stock Settings"

#: src/pages/Index/Profile/Profile.tsx:284
#~ msgid "Build Order Settings"
#~ msgstr "Build Order Settings"

#: src/pages/Index/Profile/Profile.tsx:289
#~ msgid "Purchase Order Settings"
#~ msgstr "Purchase Order Settings"

#: src/pages/Index/Profile/Profile.tsx:300
#~ msgid "Sales Order Settings"
#~ msgstr "Sales Order Settings"

#: src/pages/Index/Profile/Profile.tsx:330
#~ msgid "Plugin Settings for this instance"
#~ msgstr "Plugin Settings for this instance"

#: src/pages/Index/Profile/SettingsPanel.tsx:27
#~ msgid "Data is current beeing loaded"
#~ msgstr "Data is current beeing loaded"

#: src/pages/Index/Profile/SettingsPanel.tsx:69
#: src/pages/Index/Profile/SettingsPanel.tsx:76
#~ msgid "Failed to load"
#~ msgstr "Failed to load"

#: src/pages/Index/Profile/SettingsPanel.tsx:100
#~ msgid "Show internal names"
#~ msgstr "Show internal names"

#: src/pages/Index/Profile/SettingsPanel.tsx:148
#~ msgid "Input {0} is not known"
#~ msgstr "Input {0} is not known"

#: src/pages/Index/Profile/SettingsPanel.tsx:161
#~ msgid "Saved changes {0}"
#~ msgstr "Saved changes {0}"

#: src/pages/Index/Profile/SettingsPanel.tsx:162
#~ msgid "Changed to {0}"
#~ msgstr "Changed to {0}"

#: src/pages/Index/Profile/SettingsPanel.tsx:176
#~ msgid "Error while saving {0}"
#~ msgstr "Error while saving {0}"

#: src/pages/Index/Profile/SettingsPanel.tsx:177
#~ msgid "Error was {err}"
#~ msgstr "Error was {err}"

#: src/pages/Index/Profile/SettingsPanel.tsx:257
#~ msgid "Plugin: {0}"
#~ msgstr "Plugin: {0}"

#: src/pages/Index/Profile/SettingsPanel.tsx:262
#~ msgid "Method: {0}"
#~ msgstr "Method: {0}"

#: src/pages/Index/Profile/UserPanel.tsx:85
#~ msgid "Userinfo"
#~ msgstr "Userinfo"

#: src/pages/Index/Profile/UserPanel.tsx:122
#~ msgid "Username: {0}"
#~ msgstr "Username: {0}"

#: src/pages/Index/Profile/UserTheme.tsx:83
#~ msgid "Design <0/>"
#~ msgstr "Design <0/>"

#: src/pages/Index/Scan.tsx:65
msgid "Item already scanned"
msgstr ""

#: src/pages/Index/Scan.tsx:82
#: src/tables/InvenTreeTable.tsx:217
msgid "API Error"
msgstr "Error de API"

#: src/pages/Index/Scan.tsx:83
msgid "Failed to fetch instance data"
msgstr ""

#: src/pages/Index/Scan.tsx:130
msgid "Scan Error"
msgstr ""

#: src/pages/Index/Scan.tsx:162
msgid "Selected elements are not known"
msgstr "Se desconocen los elementos seleccionados"

#: src/pages/Index/Scan.tsx:169
msgid "Multiple object types selected"
msgstr "Varios tipos de objetos seleccionados"

#: src/pages/Index/Scan.tsx:175
#~ msgid "Actions ..."
#~ msgstr "Actions ..."

#: src/pages/Index/Scan.tsx:177
msgid "Actions ... "
msgstr ""

#: src/pages/Index/Scan.tsx:194
#: src/pages/Index/Scan.tsx:198
msgid "Barcode Scanning"
msgstr "Escaneo de código de barras"

#: src/pages/Index/Scan.tsx:207
msgid "Barcode Input"
msgstr ""

#: src/pages/Index/Scan.tsx:214
msgid "Action"
msgstr "Acción"

#: src/pages/Index/Scan.tsx:217
msgid "No Items Selected"
msgstr ""

#: src/pages/Index/Scan.tsx:217
#~ msgid "Manual input"
#~ msgstr "Manual input"

#: src/pages/Index/Scan.tsx:218
msgid "Scan and select items to perform actions"
msgstr ""

#: src/pages/Index/Scan.tsx:218
#~ msgid "Image Barcode"
#~ msgstr "Image Barcode"

#. placeholder {0}: selection.length
#: src/pages/Index/Scan.tsx:223
msgid "{0} items selected"
msgstr "{0} artículos seleccionados"

#: src/pages/Index/Scan.tsx:235
msgid "Scanned Items"
msgstr ""

#: src/pages/Index/Scan.tsx:276
#~ msgid "Actions for {0}"
#~ msgstr "Actions for {0}"

#: src/pages/Index/Scan.tsx:298
#~ msgid "Scan Page"
#~ msgstr "Scan Page"

#: src/pages/Index/Scan.tsx:301
#~ msgid "This page can be used for continuously scanning items and taking actions on them."
#~ msgstr "This page can be used for continuously scanning items and taking actions on them."

#: src/pages/Index/Scan.tsx:308
#~ msgid "Toggle Fullscreen"
#~ msgstr "Toggle Fullscreen"

#: src/pages/Index/Scan.tsx:321
#~ msgid "Select the input method you want to use to scan items."
#~ msgstr "Select the input method you want to use to scan items."

#: src/pages/Index/Scan.tsx:323
#~ msgid "Input"
#~ msgstr "Input"

#: src/pages/Index/Scan.tsx:330
#~ msgid "Select input method"
#~ msgstr "Select input method"

#: src/pages/Index/Scan.tsx:331
#~ msgid "Nothing found"
#~ msgstr "Nothing found"

#: src/pages/Index/Scan.tsx:339
#~ msgid "Depending on the selected parts actions will be shown here. Not all barcode types are supported currently."
#~ msgstr "Depending on the selected parts actions will be shown here. Not all barcode types are supported currently."

#: src/pages/Index/Scan.tsx:353
#~ msgid "General Actions"
#~ msgstr "General Actions"

#: src/pages/Index/Scan.tsx:367
#~ msgid "Lookup part"
#~ msgstr "Lookup part"

#: src/pages/Index/Scan.tsx:375
#~ msgid "Open Link"
#~ msgstr "Open Link"

#: src/pages/Index/Scan.tsx:391
#~ msgid "History is locally kept in this browser."
#~ msgstr "History is locally kept in this browser."

#: src/pages/Index/Scan.tsx:392
#~ msgid "The history is kept in this browser's local storage. So it won't be shared with other users or other devices but is persistent through reloads. You can select items in the history to perform actions on them. To add items, scan/enter them in the Input area."
#~ msgstr "The history is kept in this browser's local storage. So it won't be shared with other users or other devices but is persistent through reloads. You can select items in the history to perform actions on them. To add items, scan/enter them in the Input area."

#: src/pages/Index/Scan.tsx:400
#~ msgid "Delete History"
#~ msgstr "Delete History"

#: src/pages/Index/Scan.tsx:465
#~ msgid "No history"
#~ msgstr "No history"

#: src/pages/Index/Scan.tsx:489
#~ msgid "Source"
#~ msgstr "Source"

#: src/pages/Index/Scan.tsx:492
#~ msgid "Scanned at"
#~ msgstr "Scanned at"

#: src/pages/Index/Scan.tsx:549
#~ msgid "Enter item serial or data"
#~ msgstr "Enter item serial or data"

#: src/pages/Index/Scan.tsx:561
#~ msgid "Add dummy item"
#~ msgstr "Add dummy item"

#: src/pages/Index/Scan.tsx:652
#~ msgid "Error while getting camera"
#~ msgstr "Error while getting camera"

#: src/pages/Index/Scan.tsx:765
#~ msgid "Scanning"
#~ msgstr "Scanning"

#: src/pages/Index/Scan.tsx:765
#~ msgid "Not scanning"
#~ msgstr "Not scanning"

#: src/pages/Index/Scan.tsx:777
#~ msgid "Select Camera"
#~ msgstr "Select Camera"

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:30
#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:52
#~ msgid "Edit User Information"
#~ msgstr "Edit User Information"

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:33
#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:113
msgid "Edit Account Information"
msgstr ""

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:34
#~ msgid "User details updated"
#~ msgstr "User details updated"

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:37
msgid "Account details updated"
msgstr ""

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:46
#~ msgid "User Actions"
#~ msgstr "User Actions"

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:50
#~ msgid "First name"
#~ msgstr "First name"

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:54
#~ msgid "Set Password"
#~ msgstr "Set Password"

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:55
#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:136
msgid "Edit Profile Information"
msgstr ""

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:55
#~ msgid "Last name"
#~ msgstr "Last name"

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:56
#~ msgid "Set User Password"
#~ msgstr "Set User Password"

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:58
#~ msgid "First name: {0}"
#~ msgstr "First name: {0}"

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:59
msgid "Profile details updated"
msgstr ""

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:61
#~ msgid "Last name: {0}"
#~ msgstr "Last name: {0}"

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:65
#: src/pages/core/UserDetail.tsx:55
msgid "First Name"
msgstr "Nombre"

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:66
#: src/pages/core/UserDetail.tsx:63
msgid "Last Name"
msgstr "Apellido"

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:67
#~ msgid "First name:"
#~ msgstr "First name:"

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:71
#~ msgid "Last name:"
#~ msgstr "Last name:"

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:72
msgid "Staff Access"
msgstr "Acceso de personal"

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:85
#: src/pages/core/UserDetail.tsx:119
#: src/tables/settings/CustomStateTable.tsx:101
msgid "Display Name"
msgstr ""

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:86
#: src/pages/core/UserDetail.tsx:127
msgid "Position"
msgstr ""

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:90
#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:444
msgid "Type"
msgstr ""

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:91
#: src/pages/core/UserDetail.tsx:143
msgid "Organisation"
msgstr ""

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:92
msgid "Primary Group"
msgstr ""

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:104
msgid "Account Details"
msgstr ""

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:107
msgid "Account Actions"
msgstr ""

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:111
msgid "Edit Account"
msgstr ""

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:117
msgid "Change Password"
msgstr "Cambiar Contraseña"

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:119
msgid "Change User Password"
msgstr "Cambiar Contraseña de Usuario"

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:131
msgid "Profile Details"
msgstr ""

#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:134
msgid "Edit Profile"
msgstr ""

#. placeholder {0}: item.label
#: src/pages/Index/Settings/AccountSettings/AccountDetailPanel.tsx:153
msgid "{0}"
msgstr ""

#: src/pages/Index/Settings/AccountSettings/QrRegistrationForm.tsx:24
msgid "Secret"
msgstr ""

#: src/pages/Index/Settings/AccountSettings/QrRegistrationForm.tsx:30
msgid "One-Time Password"
msgstr ""

#: src/pages/Index/Settings/AccountSettings/QrRegistrationForm.tsx:31
msgid "Enter the TOTP code to ensure it registered correctly"
msgstr ""

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:55
#~ msgid "Single Sign On Accounts"
#~ msgstr "Single Sign On Accounts"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:56
msgid "Email Addresses"
msgstr ""

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:64
msgid "Single Sign On"
msgstr ""

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:69
#~ msgid "Multifactor"
#~ msgstr "Multifactor"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:71
#~ msgid "Single Sign On is not enabled for this server"
#~ msgstr "Single Sign On is not enabled for this server"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:72
msgid "Not enabled"
msgstr "No habilitado"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:75
msgid "Single Sign On is not enabled for this server "
msgstr ""

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:83
#~ msgid "Multifactor authentication is not configured for your account"
#~ msgstr "Multifactor authentication is not configured for your account"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:90
msgid "Access Tokens"
msgstr ""

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:128
msgid "Error while updating email"
msgstr ""

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:141
#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:297
#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:433
msgid "Not Configured"
msgstr ""

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:144
msgid "Currently no email addresses are registered."
msgstr ""

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:151
msgid "The following email addresses are associated with your account:"
msgstr "Las siguientes direcciones de correo electrónico están asociadas con tu cuenta:"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:164
msgid "Primary"
msgstr "Primario"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:169
msgid "Verified"
msgstr "Verificado"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:173
msgid "Unverified"
msgstr "Sin verificar"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:191
msgid "Make Primary"
msgstr "Convertir en principal"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:197
msgid "Re-send Verification"
msgstr "Reenviar verificación"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:211
msgid "Add Email Address"
msgstr "Añadir dirección de correo electrónico"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:213
msgid "E-Mail"
msgstr "Correo electrónico"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:214
msgid "E-Mail address"
msgstr "Dirección de correo electrónico"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:226
msgid "Error while adding email"
msgstr ""

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:237
msgid "Add Email"
msgstr "Añadir correo electrónico"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:270
#~ msgid "Provider has not been configured"
#~ msgstr "Provider has not been configured"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:280
#~ msgid "Not configured"
#~ msgstr "Not configured"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:283
#~ msgid "There are no social network accounts connected to this account."
#~ msgstr "There are no social network accounts connected to this account."

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:293
#~ msgid "You can sign in to your account using any of the following third party accounts"
#~ msgstr "You can sign in to your account using any of the following third party accounts"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:300
msgid "There are no providers connected to this account."
msgstr ""

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:308
msgid "You can sign in to your account using any of the following providers"
msgstr ""

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:321
msgid "Remove Provider Link"
msgstr ""

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:437
msgid "No multi-factor tokens configured for this account"
msgstr ""

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:447
msgid "Last used at"
msgstr ""

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:450
msgid "Created at"
msgstr ""

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:471
#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:574
msgid "Recovery Codes"
msgstr ""

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:475
msgid "Unused Codes"
msgstr "Códigos sin usar"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:480
msgid "Used Codes"
msgstr "Códigos usados"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:533
msgid "Error while registering recovery codes"
msgstr "Error al registrar los códigos de recuperación"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:567
msgid "TOTP"
msgstr ""

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:568
msgid "Time-based One-Time Password"
msgstr "Contraseña única temporal"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:575
msgid "One-Time pre-generated recovery codes"
msgstr "Códigos de recuperación auto-generados de un sólo uso"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:589
msgid "Add Token"
msgstr "Añadir Token"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:604
msgid "Register TOTP Token"
msgstr ""

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:629
msgid "Error registering TOTP token"
msgstr ""

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:705
msgid "Enter your password"
msgstr "Ingrese su contraseña"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:741
#~ msgid "Token is used - no actions"
#~ msgstr "Token is used - no actions"

#: src/pages/Index/Settings/AccountSettings/SecurityContent.tsx:761
#~ msgid "No tokens configured"
#~ msgstr "No tokens configured"

#: src/pages/Index/Settings/AccountSettings/UserThemePanel.tsx:62
msgid "Display Settings"
msgstr "Ajustes de Visualización"

#: src/pages/Index/Settings/AccountSettings/UserThemePanel.tsx:65
#~ msgid "bars"
#~ msgstr "bars"

#: src/pages/Index/Settings/AccountSettings/UserThemePanel.tsx:66
#~ msgid "oval"
#~ msgstr "oval"

#: src/pages/Index/Settings/AccountSettings/UserThemePanel.tsx:67
#~ msgid "dots"
#~ msgstr "dots"

#: src/pages/Index/Settings/AccountSettings/UserThemePanel.tsx:68
msgid "Language"
msgstr "Idioma"

#: src/pages/Index/Settings/AccountSettings/UserThemePanel.tsx:79
msgid "Use pseudo language"
msgstr "Usar pseudoidioma"

#: src/pages/Index/Settings/AccountSettings/UserThemePanel.tsx:81
#~ msgid "Theme"
#~ msgstr "Theme"

#: src/pages/Index/Settings/AccountSettings/UserThemePanel.tsx:86
msgid "Color Mode"
msgstr "Modo de color"

#: src/pages/Index/Settings/AccountSettings/UserThemePanel.tsx:87
#~ msgid "Primary color"
#~ msgstr "Primary color"

#: src/pages/Index/Settings/AccountSettings/UserThemePanel.tsx:97
msgid "Highlight color"
msgstr "Color de resaltado"

#: src/pages/Index/Settings/AccountSettings/UserThemePanel.tsx:111
msgid "Example"
msgstr "Ejemplo"

#: src/pages/Index/Settings/AccountSettings/UserThemePanel.tsx:117
msgid "White color"
msgstr "Color blanco"

#: src/pages/Index/Settings/AccountSettings/UserThemePanel.tsx:140
msgid "Black color"
msgstr "Color negro"

#: src/pages/Index/Settings/AccountSettings/UserThemePanel.tsx:163
msgid "Border Radius"
msgstr "Radio del borde"

#: src/pages/Index/Settings/AccountSettings/UserThemePanel.tsx:179
msgid "Loader"
msgstr "Cargador"

#: src/pages/Index/Settings/AccountSettings/UserThemePanel.tsx:186
msgid "Bars"
msgstr "Barras"

#: src/pages/Index/Settings/AccountSettings/UserThemePanel.tsx:187
msgid "Oval"
msgstr "Ovalado"

#: src/pages/Index/Settings/AccountSettings/UserThemePanel.tsx:188
msgid "Dots"
msgstr "Puntos"

#: src/pages/Index/Settings/AccountSettings/useConfirm.tsx:93
msgid "Reauthentication"
msgstr "Reautentificación"

#: src/pages/Index/Settings/AccountSettings/useConfirm.tsx:109
msgid "OK"
msgstr "OK"

#: src/pages/Index/Settings/AdminCenter.tsx:91
#~ msgid "Advanced Amininistrative Options for InvenTree"
#~ msgstr "Advanced Amininistrative Options for InvenTree"

#: src/pages/Index/Settings/AdminCenter/CurrencyManagementPanel.tsx:28
#: src/tables/ColumnRenderers.tsx:304
msgid "Currency"
msgstr "Moneda"

#: src/pages/Index/Settings/AdminCenter/CurrencyManagementPanel.tsx:33
msgid "Rate"
msgstr "Tarifa"

#: src/pages/Index/Settings/AdminCenter/CurrencyManagementPanel.tsx:46
msgid "Exchange rates updated"
msgstr "Tipos de cambio actualizados"

#: src/pages/Index/Settings/AdminCenter/CurrencyManagementPanel.tsx:53
msgid "Exchange rate update error"
msgstr "Error de actualización del tipo de cambio"

#: src/pages/Index/Settings/AdminCenter/CurrencyManagementPanel.tsx:63
msgid "Refresh currency exchange rates"
msgstr "Actualizar los tipos de cambio"

#: src/pages/Index/Settings/AdminCenter/CurrencyManagementPanel.tsx:99
msgid "Last fetched"
msgstr "Última búsqueda"

#: src/pages/Index/Settings/AdminCenter/CurrencyManagementPanel.tsx:100
msgid "Base currency"
msgstr "Divisa principal"

#: src/pages/Index/Settings/AdminCenter/Index.tsx:107
msgid "User Management"
msgstr "Administración de usuarios"

#: src/pages/Index/Settings/AdminCenter/Index.tsx:114
msgid "Data Import"
msgstr "Importación de datos"

#: src/pages/Index/Settings/AdminCenter/Index.tsx:120
msgid "Data Export"
msgstr ""

#: src/pages/Index/Settings/AdminCenter/Index.tsx:126
msgid "Barcode Scans"
msgstr "Escaneo de códigos de barras"

#: src/pages/Index/Settings/AdminCenter/Index.tsx:127
#~ msgid "Templates"
#~ msgstr "Templates"

#: src/pages/Index/Settings/AdminCenter/Index.tsx:132
msgid "Background Tasks"
msgstr "Tareas de fondo"

#: src/pages/Index/Settings/AdminCenter/Index.tsx:138
msgid "Error Reports"
msgstr "Informes de Errores"

#: src/pages/Index/Settings/AdminCenter/Index.tsx:144
msgid "Currencies"
msgstr "Divisas"

#: src/pages/Index/Settings/AdminCenter/Index.tsx:161
msgid "Custom States"
msgstr "Estados personalizados"

#: src/pages/Index/Settings/AdminCenter/Index.tsx:167
#: src/pages/Index/Settings/AdminCenter/UnitManagementPanel.tsx:57
msgid "Custom Units"
msgstr "Unidades personalizadas"

#: src/pages/Index/Settings/AdminCenter/Index.tsx:170
#~ msgid "Location types"
#~ msgstr "Location types"

#: src/pages/Index/Settings/AdminCenter/Index.tsx:173
#: src/pages/part/CategoryDetail.tsx:303
msgid "Part Parameters"
msgstr "Parámetros de Pieza"

#: src/pages/Index/Settings/AdminCenter/Index.tsx:180
msgid "Category Parameters"
msgstr "Parámetros de categoría"

#: src/pages/Index/Settings/AdminCenter/Index.tsx:206
msgid "Location Types"
msgstr "Tipos de Ubicación"

#: src/pages/Index/Settings/AdminCenter/Index.tsx:220
#: src/pages/Index/Settings/AdminCenter/MachineManagementPanel.tsx:48
#: src/tables/machine/MachineTypeTable.tsx:309
msgid "Machines"
msgstr "Máquinas"

#: src/pages/Index/Settings/AdminCenter/Index.tsx:221
#~ msgid "Quick Actions"
#~ msgstr "Quick Actions"

#: src/pages/Index/Settings/AdminCenter/Index.tsx:226
#~ msgid "Add a new user"
#~ msgstr "Add a new user"

#: src/pages/Index/Settings/AdminCenter/Index.tsx:236
msgid "Advanced Options"
msgstr "Opciones Avanzadas"

#: src/pages/Index/Settings/AdminCenter/LabelTemplatePanel.tsx:40
#~ msgid "Generated Labels"
#~ msgstr "Generated Labels"

#: src/pages/Index/Settings/AdminCenter/MachineManagementPanel.tsx:43
#~ msgid "Machine types"
#~ msgstr "Machine types"

#: src/pages/Index/Settings/AdminCenter/MachineManagementPanel.tsx:53
#~ msgid "Machine Error Stack"
#~ msgstr "Machine Error Stack"

#: src/pages/Index/Settings/AdminCenter/MachineManagementPanel.tsx:56
msgid "Machine Types"
msgstr "Tipos de máquinas"

#: src/pages/Index/Settings/AdminCenter/MachineManagementPanel.tsx:62
#~ msgid "There are no machine registry errors."
#~ msgstr "There are no machine registry errors."

#: src/pages/Index/Settings/AdminCenter/MachineManagementPanel.tsx:64
msgid "Machine Errors"
msgstr "Errores de máquina"

#: src/pages/Index/Settings/AdminCenter/MachineManagementPanel.tsx:77
msgid "Registry Registry Errors"
msgstr "Error de registro del registro"

#: src/pages/Index/Settings/AdminCenter/MachineManagementPanel.tsx:80
msgid "There are machine registry errors"
msgstr "Hay errores en el registro de máquinas"

#: src/pages/Index/Settings/AdminCenter/MachineManagementPanel.tsx:86
msgid "Machine Registry Errors"
msgstr "Errores de registro de máquinas"

#: src/pages/Index/Settings/AdminCenter/MachineManagementPanel.tsx:89
msgid "There are no machine registry errors"
msgstr "No hay errores en el registro de máquina"

#: src/pages/Index/Settings/AdminCenter/PluginManagementPanel.tsx:32
#: src/tables/settings/UserTable.tsx:184
msgid "Info"
msgstr "Información"

#: src/pages/Index/Settings/AdminCenter/PluginManagementPanel.tsx:33
#~ msgid "Plugin Error Stack"
#~ msgstr "Plugin Error Stack"

#: src/pages/Index/Settings/AdminCenter/PluginManagementPanel.tsx:36
msgid "External plugins are not enabled for this InvenTree installation."
msgstr "Los complementos externos no están habilitados para esta instalación de InvenTree."

#: src/pages/Index/Settings/AdminCenter/PluginManagementPanel.tsx:45
#~ msgid "Warning"
#~ msgstr "Warning"

#: src/pages/Index/Settings/AdminCenter/PluginManagementPanel.tsx:47
#~ msgid "Changing the settings below require you to immediately restart the server. Do not change this while under active usage."
#~ msgstr "Changing the settings below require you to immediately restart the server. Do not change this while under active usage."

#: src/pages/Index/Settings/AdminCenter/PluginManagementPanel.tsx:74
msgid "Plugin Errors"
msgstr "Errores de complementos"

#: src/pages/Index/Settings/AdminCenter/ReportTemplatePanel.tsx:16
msgid "Page Size"
msgstr "Tamaño de página"

#: src/pages/Index/Settings/AdminCenter/ReportTemplatePanel.tsx:19
msgid "Landscape"
msgstr "Orientación Horizontal"

#: src/pages/Index/Settings/AdminCenter/ReportTemplatePanel.tsx:25
msgid "Attach to Model"
msgstr "Adjuntar al modelo"

#: src/pages/Index/Settings/AdminCenter/ReportTemplatePanel.tsx:55
#~ msgid "Generated Reports"
#~ msgstr "Generated Reports"

#: src/pages/Index/Settings/AdminCenter/StocktakePanel.tsx:25
msgid "Stocktake Reports"
msgstr "Informes de inventario"

#: src/pages/Index/Settings/AdminCenter/TaskManagementPanel.tsx:30
msgid "Background worker not running"
msgstr "Trabajador en segundo plano no está ejecutándose"

#: src/pages/Index/Settings/AdminCenter/TaskManagementPanel.tsx:31
msgid "The background task manager service is not running. Contact your system administrator."
msgstr "El servicio de administración de tareas en segundo plano no se está ejecutando. Póngase en contacto con el administrador del sistema."

#: src/pages/Index/Settings/AdminCenter/TaskManagementPanel.tsx:35
#~ msgid "Background Worker Not Running"
#~ msgstr "Background Worker Not Running"

#: src/pages/Index/Settings/AdminCenter/TaskManagementPanel.tsx:38
#: src/pages/Index/Settings/AdminCenter/TaskManagementPanel.tsx:47
msgid "Pending Tasks"
msgstr "Tareas pendientes"

#: src/pages/Index/Settings/AdminCenter/TaskManagementPanel.tsx:39
#: src/pages/Index/Settings/AdminCenter/TaskManagementPanel.tsx:55
msgid "Scheduled Tasks"
msgstr "Tareas Programadas"

#: src/pages/Index/Settings/AdminCenter/TaskManagementPanel.tsx:40
#: src/pages/Index/Settings/AdminCenter/TaskManagementPanel.tsx:63
msgid "Failed Tasks"
msgstr "Tareas fallidas"

#: src/pages/Index/Settings/AdminCenter/TemplateManagementPanel.tsx:67
#~ msgid "Stock item"
#~ msgstr "Stock item"

#: src/pages/Index/Settings/AdminCenter/TemplateManagementPanel.tsx:76
#~ msgid "Build line"
#~ msgstr "Build line"

#: src/pages/Index/Settings/AdminCenter/TemplateManagementPanel.tsx:88
#~ msgid "Reports"
#~ msgstr "Reports"

#: src/pages/Index/Settings/AdminCenter/TemplateManagementPanel.tsx:99
#~ msgid "Purchase order"
#~ msgstr "Purchase order"

#: src/pages/Index/Settings/AdminCenter/TemplateManagementPanel.tsx:108
#~ msgid "Sales order"
#~ msgstr "Sales order"

#: src/pages/Index/Settings/AdminCenter/TemplateManagementPanel.tsx:117
#~ msgid "Return order"
#~ msgstr "Return order"

#: src/pages/Index/Settings/AdminCenter/TemplateManagementPanel.tsx:145
#~ msgid "Tests"
#~ msgstr "Tests"

#: src/pages/Index/Settings/AdminCenter/TemplateManagementPanel.tsx:154
#~ msgid "Stock location"
#~ msgstr "Stock location"

#: src/pages/Index/Settings/AdminCenter/UnitManagementPanel.tsx:21
msgid "Alias"
msgstr "Alias"

#: src/pages/Index/Settings/AdminCenter/UnitManagementPanel.tsx:22
msgid "Dimensionless"
msgstr "Sin dimensión"

#: src/pages/Index/Settings/AdminCenter/UnitManagementPanel.tsx:65
msgid "All units"
msgstr "Todas las unidades"

#: src/pages/Index/Settings/AdminCenter/UserManagementPanel.tsx:31
msgid "Tokens"
msgstr ""

#: src/pages/Index/Settings/AdminCenter/UserManagementPanel.tsx:32
#~ msgid "Select settings relevant for user lifecycle. More available in"
#~ msgstr "Select settings relevant for user lifecycle. More available in"

#: src/pages/Index/Settings/AdminCenter/UserManagementPanel.tsx:37
#~ msgid "System settings"
#~ msgstr "System settings"

#: src/pages/Index/Settings/SystemSettings.tsx:68
msgid "Authentication"
msgstr ""

#: src/pages/Index/Settings/SystemSettings.tsx:94
msgid "Barcodes"
msgstr "Códigos de barras"

#: src/pages/Index/Settings/SystemSettings.tsx:118
msgid "This panel is a placeholder."
msgstr "Este panel es un marcador de posición."

#: src/pages/Index/Settings/SystemSettings.tsx:118
#~ msgid "Physical Units"
#~ msgstr "Physical Units"

#: src/pages/Index/Settings/SystemSettings.tsx:128
msgid "Pricing"
msgstr "Precios"

#: src/pages/Index/Settings/SystemSettings.tsx:135
#~ msgid "Exchange Rates"
#~ msgstr "Exchange Rates"

#: src/pages/Index/Settings/SystemSettings.tsx:163
msgid "Labels"
msgstr "Etiquetas"

#: src/pages/Index/Settings/SystemSettings.tsx:169
#: src/pages/Index/Settings/UserSettings.tsx:105
msgid "Reporting"
msgstr "Informes"

#: src/pages/Index/Settings/SystemSettings.tsx:317
#~ msgid "Switch to User Setting"
#~ msgstr "Switch to User Setting"

#: src/pages/Index/Settings/UserSettings.tsx:35
msgid "Account"
msgstr "Cuenta"

#: src/pages/Index/Settings/UserSettings.tsx:41
msgid "Security"
msgstr "Seguridad"

#: src/pages/Index/Settings/UserSettings.tsx:47
msgid "Display Options"
msgstr "Opciones de visualización"

#: src/pages/Index/Settings/UserSettings.tsx:159
#~ msgid "Switch to System Setting"
#~ msgstr "Switch to System Setting"

#: src/pages/Logged-In.tsx:24
#~ msgid "Found an exsisting login - using it to log you in."
#~ msgstr "Found an exsisting login - using it to log you in."

#: src/pages/NotFound.tsx:20
#~ msgid "Sorry, this page is not known or was moved."
#~ msgstr "Sorry, this page is not known or was moved."

#: src/pages/NotFound.tsx:27
#~ msgid "Go to the start page"
#~ msgstr "Go to the start page"

#: src/pages/Notifications.tsx:44
#~ msgid "Delete Notifications"
#~ msgstr "Delete Notifications"

#: src/pages/Notifications.tsx:83
msgid "History"
msgstr "Historial"

#: src/pages/Notifications.tsx:91
msgid "Mark as unread"
msgstr "Marcar como no leído"

#: src/pages/Notifications.tsx:146
#~ msgid "Delete notifications"
#~ msgstr "Delete notifications"

#: src/pages/build/BuildDetail.tsx:80
#~ msgid "Build Status"
#~ msgstr "Build Status"

#: src/pages/build/BuildDetail.tsx:100
#: src/pages/company/ManufacturerPartDetail.tsx:84
#: src/pages/company/SupplierPartDetail.tsx:95
#: src/pages/part/PartDetail.tsx:172
#: src/pages/stock/StockDetail.tsx:131
#: src/tables/bom/BomTable.tsx:120
#: src/tables/bom/UsedInTable.tsx:39
#: src/tables/build/BuildAllocatedStockTable.tsx:104
#: src/tables/build/BuildLineTable.tsx:324
#: src/tables/build/BuildOrderTable.tsx:77
#: src/tables/part/RelatedPartTable.tsx:69
#: src/tables/sales/SalesOrderAllocationTable.tsx:135
#: src/tables/sales/SalesOrderLineItemTable.tsx:87
#: src/tables/stock/StockItemTable.tsx:60
msgid "IPN"
msgstr "IPN"

#: src/pages/build/BuildDetail.tsx:108
#: src/pages/part/PartDetail.tsx:199
#: src/tables/stock/StockItemTable.tsx:65
msgid "Revision"
msgstr "Revisión"

#: src/pages/build/BuildDetail.tsx:121
#: src/pages/purchasing/PurchaseOrderDetail.tsx:157
#: src/pages/sales/ReturnOrderDetail.tsx:122
#: src/pages/sales/SalesOrderDetail.tsx:131
#: src/pages/stock/StockDetail.tsx:146
msgid "Custom Status"
msgstr "Estado Personalizado"

#: src/pages/build/BuildDetail.tsx:130
#: src/pages/purchasing/PurchaseOrderDetail.tsx:124
#: src/pages/sales/ReturnOrderDetail.tsx:89
#: src/pages/sales/SalesOrderDetail.tsx:98
#: src/tables/ColumnRenderers.tsx:136
#: src/tables/build/BuildAllocatedStockTable.tsx:111
#: src/tables/build/BuildLineTable.tsx:335
msgid "Reference"
msgstr "Referencia"

#: src/pages/build/BuildDetail.tsx:144
msgid "Parent Build"
msgstr "Construir padre"

#: src/pages/build/BuildDetail.tsx:155
msgid "Build Quantity"
msgstr "Cantidad de construcción"

#: src/pages/build/BuildDetail.tsx:163
#: src/pages/build/BuildDetail.tsx:309
msgid "Completed Outputs"
msgstr "Salidas completadas"

#: src/pages/build/BuildDetail.tsx:180
#: src/tables/Filter.tsx:334
msgid "Issued By"
msgstr "Emitido por"

#: src/pages/build/BuildDetail.tsx:185
#: src/pages/part/PartDetail.tsx:269
#: src/pages/stock/StockDetail.tsx:150
#~ msgid "View part barcode"
#~ msgstr "View part barcode"

#: src/pages/build/BuildDetail.tsx:188
#: src/pages/part/PartDetail.tsx:407
#: src/pages/purchasing/PurchaseOrderDetail.tsx:244
#: src/pages/sales/ReturnOrderDetail.tsx:208
#: src/pages/sales/SalesOrderDetail.tsx:220
#: src/tables/Filter.tsx:298
msgid "Responsible"
msgstr "Responsable"

#: src/pages/build/BuildDetail.tsx:190
#: src/pages/part/PartDetail.tsx:274
#~ msgid "Link custom barcode to part"
#~ msgstr "Link custom barcode to part"

#: src/pages/build/BuildDetail.tsx:196
#: src/pages/part/PartDetail.tsx:280
#~ msgid "Unlink custom barcode from part"
#~ msgstr "Unlink custom barcode from part"

#: src/pages/build/BuildDetail.tsx:202
#~ msgid "Build Order updated"
#~ msgstr "Build Order updated"

#: src/pages/build/BuildDetail.tsx:206
msgid "Any location"
msgstr "Cualquier ubicación"

#: src/pages/build/BuildDetail.tsx:213
msgid "Destination Location"
msgstr "Ubicación de destino"

#: src/pages/build/BuildDetail.tsx:221
#~ msgid "Edit build order"
#~ msgstr "Edit build order"

#: src/pages/build/BuildDetail.tsx:226
#~ msgid "Duplicate build order"
#~ msgstr "Duplicate build order"

#: src/pages/build/BuildDetail.tsx:229
#: src/tables/settings/ApiTokenTable.tsx:96
#: src/tables/settings/PendingTasksTable.tsx:36
msgid "Created"
msgstr "Creado"

#: src/pages/build/BuildDetail.tsx:231
#~ msgid "Delete build order"
#~ msgstr "Delete build order"

#: src/pages/build/BuildDetail.tsx:237
#: src/pages/purchasing/PurchaseOrderDetail.tsx:269
#: src/pages/sales/ReturnOrderDetail.tsx:234
#: src/pages/sales/SalesOrderDetail.tsx:245
#: src/tables/ColumnRenderers.tsx:252
msgid "Start Date"
msgstr "Fecha de inicio"

#: src/pages/build/BuildDetail.tsx:245
#: src/pages/purchasing/PurchaseOrderDetail.tsx:277
#: src/pages/sales/ReturnOrderDetail.tsx:242
#: src/pages/sales/SalesOrderDetail.tsx:253
#: src/tables/ColumnRenderers.tsx:260
#: src/tables/part/PartPurchaseOrdersTable.tsx:100
#: src/tables/sales/ReturnOrderLineItemTable.tsx:143
#: src/tables/sales/SalesOrderLineItemTable.tsx:124
msgid "Target Date"
msgstr "Fecha objetivo"

#: src/pages/build/BuildDetail.tsx:253
#: src/tables/sales/SalesOrderLineItemTable.tsx:311
msgid "Completed"
msgstr "Completado"

#: src/pages/build/BuildDetail.tsx:284
msgid "Build Details"
msgstr "Detalles de construcción"

#: src/pages/build/BuildDetail.tsx:290
#: src/pages/purchasing/PurchaseOrderDetail.tsx:322
#: src/pages/purchasing/PurchaseOrderDetail.tsx:331
#: src/pages/sales/ReturnOrderDetail.tsx:134
#: src/pages/sales/ReturnOrderDetail.tsx:286
#: src/pages/sales/ReturnOrderDetail.tsx:295
#: src/pages/sales/SalesOrderDetail.tsx:332
#: src/pages/sales/SalesOrderDetail.tsx:341
msgid "Line Items"
msgstr "Partidas"

#: src/pages/build/BuildDetail.tsx:296
msgid "Incomplete Outputs"
msgstr "Salidas incompletas"

#: src/pages/build/BuildDetail.tsx:324
#: src/pages/sales/SalesOrderDetail.tsx:379
#: src/pages/sales/SalesOrderShipmentDetail.tsx:212
msgid "Allocated Stock"
msgstr "Existencias asignadas"

#: src/pages/build/BuildDetail.tsx:337
msgid "Consumed Stock"
msgstr "Existencias consumidas"

#: src/pages/build/BuildDetail.tsx:347
#: src/pages/part/PartDetail.tsx:727
#~ msgid "Test Statistics"
#~ msgstr "Test Statistics"

#: src/pages/build/BuildDetail.tsx:352
msgid "Child Build Orders"
msgstr "Órdenes de Trabajo herederas"

#: src/pages/build/BuildDetail.tsx:362
#: src/tables/build/BuildOutputTable.tsx:539
#: src/tables/stock/StockItemTestResultTable.tsx:157
msgid "Test Results"
msgstr "Resultados de la prueba"

#: src/pages/build/BuildDetail.tsx:368
#~ msgid "Reporting Actions"
#~ msgstr "Reporting Actions"

#: src/pages/build/BuildDetail.tsx:374
#~ msgid "Print build report"
#~ msgstr "Print build report"

#: src/pages/build/BuildDetail.tsx:387
msgid "Edit Build Order"
msgstr "Editar orden de construcción"

#: src/pages/build/BuildDetail.tsx:403
#: src/tables/build/BuildOrderTable.tsx:180
#: src/tables/build/BuildOrderTable.tsx:195
msgid "Add Build Order"
msgstr "Añadir orden de construcción"

#: src/pages/build/BuildDetail.tsx:412
msgid "Cancel Build Order"
msgstr "Cancelar orden de construcción"

#: src/pages/build/BuildDetail.tsx:414
#: src/pages/purchasing/PurchaseOrderDetail.tsx:397
#: src/pages/sales/ReturnOrderDetail.tsx:392
#: src/pages/sales/SalesOrderDetail.tsx:426
msgid "Order cancelled"
msgstr "Pedido cancelado"

#: src/pages/build/BuildDetail.tsx:415
#: src/pages/purchasing/PurchaseOrderDetail.tsx:396
#: src/pages/sales/ReturnOrderDetail.tsx:391
#: src/pages/sales/SalesOrderDetail.tsx:425
msgid "Cancel this order"
msgstr "Cancelar esta orden"

#: src/pages/build/BuildDetail.tsx:424
msgid "Hold Build Order"
msgstr "Poner en espera orden de construcción"

#: src/pages/build/BuildDetail.tsx:426
#: src/pages/purchasing/PurchaseOrderDetail.tsx:404
#: src/pages/sales/ReturnOrderDetail.tsx:399
#: src/pages/sales/SalesOrderDetail.tsx:433
msgid "Place this order on hold"
msgstr "Poner este pedido en espera"

#: src/pages/build/BuildDetail.tsx:427
#: src/pages/purchasing/PurchaseOrderDetail.tsx:405
#: src/pages/sales/ReturnOrderDetail.tsx:400
#: src/pages/sales/SalesOrderDetail.tsx:434
msgid "Order placed on hold"
msgstr "Pedido puesto en espera"

#: src/pages/build/BuildDetail.tsx:432
msgid "Issue Build Order"
msgstr "Emitir Orden de Construcción"

#: src/pages/build/BuildDetail.tsx:434
#: src/pages/purchasing/PurchaseOrderDetail.tsx:388
#: src/pages/sales/ReturnOrderDetail.tsx:383
#: src/pages/sales/SalesOrderDetail.tsx:417
msgid "Issue this order"
msgstr "Emitir este pedido"

#: src/pages/build/BuildDetail.tsx:435
#: src/pages/purchasing/PurchaseOrderDetail.tsx:389
#: src/pages/sales/ReturnOrderDetail.tsx:384
#: src/pages/sales/SalesOrderDetail.tsx:418
msgid "Order issued"
msgstr "Orden emitida"

#: src/pages/build/BuildDetail.tsx:440
msgid "Complete Build Order"
msgstr "Completar Orden de Construcción"

#: src/pages/build/BuildDetail.tsx:442
#: src/pages/purchasing/PurchaseOrderDetail.tsx:417
#: src/pages/sales/ReturnOrderDetail.tsx:407
#: src/pages/sales/SalesOrderDetail.tsx:452
msgid "Mark this order as complete"
msgstr "Marcar pedido como completado"

#: src/pages/build/BuildDetail.tsx:443
#: src/pages/purchasing/PurchaseOrderDetail.tsx:411
#: src/pages/sales/ReturnOrderDetail.tsx:408
#: src/pages/sales/SalesOrderDetail.tsx:453
msgid "Order completed"
msgstr "Pedido completado"

#: src/pages/build/BuildDetail.tsx:474
#: src/pages/purchasing/PurchaseOrderDetail.tsx:440
#: src/pages/sales/ReturnOrderDetail.tsx:437
#: src/pages/sales/SalesOrderDetail.tsx:482
msgid "Issue Order"
msgstr "Emitir pedido"

#: src/pages/build/BuildDetail.tsx:481
#: src/pages/purchasing/PurchaseOrderDetail.tsx:447
#: src/pages/sales/ReturnOrderDetail.tsx:444
#: src/pages/sales/SalesOrderDetail.tsx:496
msgid "Complete Order"
msgstr "Completar Pedido"

#: src/pages/build/BuildDetail.tsx:499
msgid "Build Order Actions"
msgstr ""

#: src/pages/build/BuildDetail.tsx:504
#: src/pages/purchasing/PurchaseOrderDetail.tsx:469
#: src/pages/sales/ReturnOrderDetail.tsx:466
#: src/pages/sales/SalesOrderDetail.tsx:519
msgid "Edit order"
msgstr "Editar pedido"

#: src/pages/build/BuildDetail.tsx:508
#: src/pages/purchasing/PurchaseOrderDetail.tsx:477
#: src/pages/sales/ReturnOrderDetail.tsx:472
#: src/pages/sales/SalesOrderDetail.tsx:524
msgid "Duplicate order"
msgstr "Duplicar pedido"

#: src/pages/build/BuildDetail.tsx:512
#: src/pages/purchasing/PurchaseOrderDetail.tsx:480
#: src/pages/sales/ReturnOrderDetail.tsx:477
#: src/pages/sales/SalesOrderDetail.tsx:527
msgid "Hold order"
msgstr "Retener pedido"

#: src/pages/build/BuildDetail.tsx:517
#: src/pages/purchasing/PurchaseOrderDetail.tsx:485
#: src/pages/sales/ReturnOrderDetail.tsx:482
#: src/pages/sales/SalesOrderDetail.tsx:532
msgid "Cancel order"
msgstr "Cancelar pedido"

#: src/pages/build/BuildDetail.tsx:553
#: src/pages/stock/StockDetail.tsx:268
#: src/tables/build/BuildAllocatedStockTable.tsx:77
#: src/tables/part/PartBuildAllocationsTable.tsx:41
#: src/tables/stock/StockTrackingTable.tsx:107
msgid "Build Order"
msgstr ""

#: src/pages/build/BuildIndex.tsx:23
#~ msgid "Build order created"
#~ msgstr "Build order created"

#: src/pages/build/BuildIndex.tsx:39
#~ msgid "New Build Order"
#~ msgstr "New Build Order"

#: src/pages/build/BuildIndex.tsx:71
#: src/pages/purchasing/PurchasingIndex.tsx:69
#: src/pages/sales/SalesIndex.tsx:90
#: src/pages/sales/SalesIndex.tsx:111
msgid "Table View"
msgstr ""

#: src/pages/build/BuildIndex.tsx:74
#: src/pages/purchasing/PurchasingIndex.tsx:72
#: src/pages/sales/SalesIndex.tsx:93
#: src/pages/sales/SalesIndex.tsx:114
msgid "Calendar View"
msgstr ""

#: src/pages/company/CompanyDetail.tsx:99
msgid "Website"
msgstr "Sitio web"

#: src/pages/company/CompanyDetail.tsx:107
msgid "Phone Number"
msgstr "Número de teléfono"

#: src/pages/company/CompanyDetail.tsx:114
msgid "Email Address"
msgstr "Dirección de correo electrónico"

#: src/pages/company/CompanyDetail.tsx:124
msgid "Default Currency"
msgstr "Divisa predeterminada"

#: src/pages/company/CompanyDetail.tsx:129
#: src/pages/company/SupplierDetail.tsx:8
#: src/pages/company/SupplierPartDetail.tsx:129
#: src/pages/company/SupplierPartDetail.tsx:235
#: src/pages/company/SupplierPartDetail.tsx:357
#: src/pages/purchasing/PurchaseOrderDetail.tsx:139
#: src/tables/company/CompanyTable.tsx:101
#: src/tables/part/PartPurchaseOrdersTable.tsx:42
#: src/tables/purchasing/PurchaseOrderTable.tsx:105
#: src/tables/purchasing/SupplierPriceBreakTable.tsx:36
msgid "Supplier"
msgstr "Proveedor"

#: src/pages/company/CompanyDetail.tsx:135
#: src/pages/company/ManufacturerDetail.tsx:8
#: src/pages/company/ManufacturerPartDetail.tsx:103
#: src/pages/company/ManufacturerPartDetail.tsx:265
#: src/pages/company/SupplierPartDetail.tsx:151
#: src/tables/company/CompanyTable.tsx:106
#: src/tables/purchasing/SupplierPartTable.tsx:77
msgid "Manufacturer"
msgstr "Fabricante"

#: src/pages/company/CompanyDetail.tsx:141
#: src/pages/company/CustomerDetail.tsx:8
#: src/pages/part/pricing/SaleHistoryPanel.tsx:31
#: src/pages/sales/ReturnOrderDetail.tsx:104
#: src/pages/sales/SalesOrderDetail.tsx:113
#: src/pages/sales/SalesOrderShipmentDetail.tsx:104
#: src/pages/stock/StockDetail.tsx:294
#: src/tables/company/CompanyTable.tsx:111
#: src/tables/sales/ReturnOrderTable.tsx:112
#: src/tables/sales/SalesOrderTable.tsx:137
#: src/tables/stock/StockTrackingTable.tsx:151
msgid "Customer"
msgstr "Cliente"

#: src/pages/company/CompanyDetail.tsx:174
msgid "Company Details"
msgstr "Datos de la empresa"

#: src/pages/company/CompanyDetail.tsx:175
#~ msgid "Edit company"
#~ msgstr "Edit company"

#: src/pages/company/CompanyDetail.tsx:180
msgid "Supplied Parts"
msgstr "Piezas suministradas"

#: src/pages/company/CompanyDetail.tsx:189
msgid "Manufactured Parts"
msgstr "Piezas fabricadas"

#: src/pages/company/CompanyDetail.tsx:189
#~ msgid "Delete company"
#~ msgstr "Delete company"

#: src/pages/company/CompanyDetail.tsx:236
msgid "Assigned Stock"
msgstr "Existencias asignadas"

#: src/pages/company/CompanyDetail.tsx:276
#: src/tables/company/CompanyTable.tsx:87
msgid "Edit Company"
msgstr "Editar empresa"

#: src/pages/company/CompanyDetail.tsx:284
msgid "Delete Company"
msgstr "Eliminar Empresa"

#: src/pages/company/CompanyDetail.tsx:294
msgid "Company Actions"
msgstr "Acciones de empresa"

#: src/pages/company/ManufacturerPartDetail.tsx:77
#: src/pages/company/SupplierPartDetail.tsx:88
msgid "Internal Part"
msgstr "Pieza Interna"

#: src/pages/company/ManufacturerPartDetail.tsx:111
#: src/pages/company/SupplierPartDetail.tsx:160
#: src/tables/purchasing/ManufacturerPartTable.tsx:58
msgid "Manufacturer Part Number"
msgstr "Referencia de pieza del fabricante"

#: src/pages/company/ManufacturerPartDetail.tsx:128
#: src/pages/company/SupplierPartDetail.tsx:112
msgid "External Link"
msgstr "Enlace externo"

#: src/pages/company/ManufacturerPartDetail.tsx:147
#: src/pages/company/SupplierPartDetail.tsx:232
#: src/pages/part/PartDetail.tsx:481
msgid "Part Details"
msgstr "Detalles de la Pieza"

#: src/pages/company/ManufacturerPartDetail.tsx:150
msgid "Manufacturer Details"
msgstr "Detalles del fabricante"

#: src/pages/company/ManufacturerPartDetail.tsx:159
msgid "Manufacturer Part Details"
msgstr "Detalles de pieza del fabricante"

#: src/pages/company/ManufacturerPartDetail.tsx:165
#: src/pages/part/PartDetail.tsx:487
msgid "Parameters"
msgstr "Parámetros"

#: src/pages/company/ManufacturerPartDetail.tsx:205
#: src/tables/purchasing/ManufacturerPartTable.tsx:86
msgid "Edit Manufacturer Part"
msgstr "Editar pieza de fabricante"

#: src/pages/company/ManufacturerPartDetail.tsx:212
#: src/tables/purchasing/ManufacturerPartTable.tsx:74
#: src/tables/purchasing/ManufacturerPartTable.tsx:106
msgid "Add Manufacturer Part"
msgstr "Añadir pieza de fabricante"

#: src/pages/company/ManufacturerPartDetail.tsx:224
#: src/tables/purchasing/ManufacturerPartTable.tsx:94
msgid "Delete Manufacturer Part"
msgstr "Eliminar pieza de fabricante"

#: src/pages/company/ManufacturerPartDetail.tsx:239
msgid "Manufacturer Part Actions"
msgstr "Acciones de pieza de fabricante"

#: src/pages/company/ManufacturerPartDetail.tsx:279
msgid "ManufacturerPart"
msgstr "Pieza de fabricante"

#: src/pages/company/SupplierPartDetail.tsx:103
#: src/tables/part/RelatedPartTable.tsx:78
msgid "Part Description"
msgstr "Descripción de la Pieza"

#: src/pages/company/SupplierPartDetail.tsx:179
#: src/tables/part/PartPurchaseOrdersTable.tsx:72
#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:163
#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:204
#: src/tables/purchasing/SupplierPartTable.tsx:134
msgid "Pack Quantity"
msgstr "Cantidad del paquete"

#: src/pages/company/SupplierPartDetail.tsx:197
#: src/pages/company/SupplierPartDetail.tsx:387
#: src/pages/part/PartDetail.tsx:773
#: src/tables/bom/BomTable.tsx:330
#: src/tables/part/PartTable.tsx:93
msgid "On Order"
msgstr "En pedido"

#: src/pages/company/SupplierPartDetail.tsx:204
msgid "Supplier Availability"
msgstr "Disponibilidad del proveedor"

#: src/pages/company/SupplierPartDetail.tsx:212
msgid "Availability Updated"
msgstr "Disponibilidad actualizada"

#: src/pages/company/SupplierPartDetail.tsx:237
msgid "Availability"
msgstr "Disponibilidad"

#: src/pages/company/SupplierPartDetail.tsx:246
msgid "Supplier Part Details"
msgstr "Detalles de pieza de proveedor"

#: src/pages/company/SupplierPartDetail.tsx:252
#: src/pages/purchasing/PurchaseOrderDetail.tsx:360
msgid "Received Stock"
msgstr "Existencias recibidas"

#: src/pages/company/SupplierPartDetail.tsx:276
#: src/pages/part/PartPricingPanel.tsx:113
#: src/pages/part/pricing/PricingOverviewPanel.tsx:232
msgid "Supplier Pricing"
msgstr "Precios del Proveedor"

#: src/pages/company/SupplierPartDetail.tsx:301
msgid "Supplier Part Actions"
msgstr "Acciones de piezas de proveedor"

#: src/pages/company/SupplierPartDetail.tsx:325
#: src/tables/purchasing/SupplierPartTable.tsx:221
msgid "Edit Supplier Part"
msgstr "Editar pieza de proveedor"

#: src/pages/company/SupplierPartDetail.tsx:333
#: src/tables/purchasing/SupplierPartTable.tsx:229
msgid "Delete Supplier Part"
msgstr "Eliminar pieza de proveedor"

#: src/pages/company/SupplierPartDetail.tsx:341
#: src/tables/purchasing/SupplierPartTable.tsx:168
msgid "Add Supplier Part"
msgstr "Añadir pieza de proveedor"

#: src/pages/company/SupplierPartDetail.tsx:381
#: src/pages/part/PartDetail.tsx:761
msgid "No Stock"
msgstr "Sin existencias"

#: src/pages/core/CoreIndex.tsx:46
#: src/pages/core/GroupDetail.tsx:81
#: src/pages/core/UserDetail.tsx:224
msgid "System Overview"
msgstr ""

#: src/pages/core/GroupDetail.tsx:45
msgid "Group Name"
msgstr ""

#: src/pages/core/GroupDetail.tsx:52
#: src/pages/core/GroupDetail.tsx:67
#: src/tables/settings/GroupTable.tsx:80
msgid "Group Details"
msgstr ""

#: src/pages/core/GroupDetail.tsx:55
#: src/tables/settings/GroupTable.tsx:107
msgid "Group Roles"
msgstr ""

#: src/pages/core/UserDetail.tsx:175
msgid "User Information"
msgstr ""

#: src/pages/core/UserDetail.tsx:176
msgid "User Permissions"
msgstr ""

#: src/pages/core/UserDetail.tsx:178
msgid "User Profile"
msgstr ""

#: src/pages/core/UserDetail.tsx:188
#: src/tables/settings/UserTable.tsx:153
msgid "User Details"
msgstr "Detalles de Usuario"

#: src/pages/core/UserDetail.tsx:206
msgid "Basic user"
msgstr ""

#: src/pages/part/CategoryDetail.tsx:99
#: src/pages/stock/LocationDetail.tsx:97
#: src/tables/settings/ErrorTable.tsx:63
#: src/tables/settings/ErrorTable.tsx:108
msgid "Path"
msgstr "Ruta"

#: src/pages/part/CategoryDetail.tsx:115
msgid "Parent Category"
msgstr "Categoría superior"

#: src/pages/part/CategoryDetail.tsx:138
#: src/pages/part/CategoryDetail.tsx:268
msgid "Subcategories"
msgstr "Subcategorías"

#: src/pages/part/CategoryDetail.tsx:145
#: src/pages/stock/LocationDetail.tsx:137
#: src/tables/part/PartCategoryTable.tsx:88
#: src/tables/stock/StockLocationTable.tsx:43
msgid "Structural"
msgstr "Estructural"

#: src/pages/part/CategoryDetail.tsx:151
msgid "Parent default location"
msgstr "Ubicación padre por defecto"

#: src/pages/part/CategoryDetail.tsx:158
msgid "Default location"
msgstr "Ubicación predeterminada"

#: src/pages/part/CategoryDetail.tsx:169
msgid "Top level part category"
msgstr "Categoría de piezas de nivel superior"

#: src/pages/part/CategoryDetail.tsx:179
#: src/pages/part/CategoryDetail.tsx:245
#: src/tables/part/PartCategoryTable.tsx:121
msgid "Edit Part Category"
msgstr "Editar categoría de pieza"

#: src/pages/part/CategoryDetail.tsx:188
msgid "Move items to parent category"
msgstr "Mover artículos a la categoría padre"

#: src/pages/part/CategoryDetail.tsx:192
#: src/pages/stock/LocationDetail.tsx:229
msgid "Delete items"
msgstr "Eliminar elementos"

#: src/pages/part/CategoryDetail.tsx:200
#: src/pages/part/CategoryDetail.tsx:250
msgid "Delete Part Category"
msgstr "Eliminar categoría de pieza"

#: src/pages/part/CategoryDetail.tsx:203
msgid "Parts Action"
msgstr "Acciones de piezas"

#: src/pages/part/CategoryDetail.tsx:204
msgid "Action for parts in this category"
msgstr "Acciones de piezas en esta categoría"

#: src/pages/part/CategoryDetail.tsx:209
msgid "Child Categories Action"
msgstr "Acción en subcategorías"

#: src/pages/part/CategoryDetail.tsx:210
msgid "Action for child categories in this category"
msgstr "Acción para subcategorías en esta categoría"

#: src/pages/part/CategoryDetail.tsx:241
#: src/tables/part/PartCategoryTable.tsx:142
msgid "Category Actions"
msgstr "Acciones de categoría"

#: src/pages/part/CategoryDetail.tsx:262
msgid "Category Details"
msgstr "Detalles de categoría"

#: src/pages/part/PartAllocationPanel.tsx:21
#: src/pages/stock/StockDetail.tsx:463
#: src/tables/part/PartTable.tsx:106
msgid "Build Order Allocations"
msgstr ""

#: src/pages/part/PartAllocationPanel.tsx:31
#: src/pages/stock/StockDetail.tsx:478
#: src/tables/part/PartTable.tsx:114
msgid "Sales Order Allocations"
msgstr ""

#: src/pages/part/PartDetail.tsx:185
msgid "Variant of"
msgstr "Variante de"

#: src/pages/part/PartDetail.tsx:192
msgid "Revision of"
msgstr "Revisión de"

#: src/pages/part/PartDetail.tsx:206
#: src/tables/Filter.tsx:342
#: src/tables/notifications/NotificationTable.tsx:32
#: src/tables/part/PartCategoryTemplateTable.tsx:67
msgid "Category"
msgstr "Categoría"

#: src/pages/part/PartDetail.tsx:212
msgid "Default Location"
msgstr "Ubicación Predeterminada"

#: src/pages/part/PartDetail.tsx:219
msgid "Category Default Location"
msgstr "Ubicación de Categoría Predeterminada"

#: src/pages/part/PartDetail.tsx:226
#: src/tables/purchasing/ManufacturerPartParameterTable.tsx:44
msgid "Units"
msgstr "Unidades"

#: src/pages/part/PartDetail.tsx:233
#: src/tables/settings/PendingTasksTable.tsx:46
msgid "Keywords"
msgstr "Palabras claves"

#: src/pages/part/PartDetail.tsx:258
#: src/tables/bom/BomTable.tsx:325
#: src/tables/build/BuildLineTable.tsx:293
#: src/tables/part/PartTable.tsx:297
#: src/tables/sales/SalesOrderLineItemTable.tsx:128
msgid "Available Stock"
msgstr "Existencias disponibles"

#: src/pages/part/PartDetail.tsx:265
msgid "Variant Stock"
msgstr "Existencias Variables"

#: src/pages/part/PartDetail.tsx:273
msgid "Minimum Stock"
msgstr "Existencias Mínimas"

#: src/pages/part/PartDetail.tsx:279
#: src/tables/bom/BomTable.tsx:239
#: src/tables/build/BuildLineTable.tsx:255
#: src/tables/sales/SalesOrderLineItemTable.tsx:166
msgid "On order"
msgstr "En pedido"

#: src/pages/part/PartDetail.tsx:286
msgid "Required for Orders"
msgstr "Requerido para Pedidos"

#: src/pages/part/PartDetail.tsx:295
msgid "Allocated to Build Orders"
msgstr "Asignado para Construir Pedidos"

#: src/pages/part/PartDetail.tsx:306
msgid "Allocated to Sales Orders"
msgstr ""

#: src/pages/part/PartDetail.tsx:310
#~ msgid "Edit part"
#~ msgstr "Edit part"

#: src/pages/part/PartDetail.tsx:316
#: src/tables/bom/BomTable.tsx:263
#: src/tables/bom/BomTable.tsx:298
msgid "Can Build"
msgstr ""

#: src/pages/part/PartDetail.tsx:322
#~ msgid "Duplicate part"
#~ msgstr "Duplicate part"

#: src/pages/part/PartDetail.tsx:323
#: src/pages/part/PartDetail.tsx:779
#: src/pages/stock/StockDetail.tsx:832
#: src/tables/build/BuildOrderTestTable.tsx:227
#: src/tables/stock/StockItemTable.tsx:347
msgid "In Production"
msgstr "En producción"

#: src/pages/part/PartDetail.tsx:327
#~ msgid "Delete part"
#~ msgstr "Delete part"

#: src/pages/part/PartDetail.tsx:337
#: src/tables/part/ParametricPartTable.tsx:229
#: src/tables/part/PartTable.tsx:187
msgid "Locked"
msgstr "Bloqueado"

#: src/pages/part/PartDetail.tsx:343
msgid "Template Part"
msgstr ""

#: src/pages/part/PartDetail.tsx:348
#: src/tables/bom/BomTable.tsx:320
msgid "Assembled Part"
msgstr ""

#: src/pages/part/PartDetail.tsx:353
msgid "Component Part"
msgstr ""

#: src/pages/part/PartDetail.tsx:358
#: src/tables/bom/BomTable.tsx:310
msgid "Testable Part"
msgstr ""

#: src/pages/part/PartDetail.tsx:364
#: src/tables/bom/BomTable.tsx:315
msgid "Trackable Part"
msgstr ""

#: src/pages/part/PartDetail.tsx:369
msgid "Purchaseable Part"
msgstr ""

#: src/pages/part/PartDetail.tsx:375
msgid "Saleable Part"
msgstr ""

#: src/pages/part/PartDetail.tsx:380
msgid "Virtual Part"
msgstr ""

#: src/pages/part/PartDetail.tsx:394
#: src/pages/purchasing/PurchaseOrderDetail.tsx:254
#: src/pages/sales/ReturnOrderDetail.tsx:218
#: src/pages/sales/SalesOrderDetail.tsx:230
#: src/tables/ColumnRenderers.tsx:268
msgid "Creation Date"
msgstr "Fecha de Creación"

#: src/pages/part/PartDetail.tsx:399
#: src/tables/ColumnRenderers.tsx:216
#: src/tables/Filter.tsx:326
msgid "Created By"
msgstr "Creado Por"

#: src/pages/part/PartDetail.tsx:414
msgid "Default Supplier"
msgstr "Proveedor Predeterminado"

#: src/pages/part/PartDetail.tsx:425
#: src/pages/part/pricing/BomPricingPanel.tsx:113
#: src/pages/part/pricing/VariantPricingPanel.tsx:97
#: src/tables/part/PartTable.tsx:164
msgid "Price Range"
msgstr "Rango de Precios"

#: src/pages/part/PartDetail.tsx:435
msgid "Latest Serial Number"
msgstr "Último número de serie"

#: src/pages/part/PartDetail.tsx:510
#~ msgid "Stocktake By"
#~ msgstr "Stocktake By"

#: src/pages/part/PartDetail.tsx:516
msgid "Variants"
msgstr "Variantes"

#: src/pages/part/PartDetail.tsx:523
#: src/pages/stock/StockDetail.tsx:450
msgid "Allocations"
msgstr "Asignaciones"

#: src/pages/part/PartDetail.tsx:530
msgid "Bill of Materials"
msgstr "Lista de Materiales"

#: src/pages/part/PartDetail.tsx:548
msgid "Used In"
msgstr ""

#: src/pages/part/PartDetail.tsx:555
msgid "Part Pricing"
msgstr ""

#: src/pages/part/PartDetail.tsx:613
msgid "Scheduling"
msgstr ""

#: src/pages/part/PartDetail.tsx:620
msgid "Test Templates"
msgstr ""

#: src/pages/part/PartDetail.tsx:631
msgid "Related Parts"
msgstr ""

#: src/pages/part/PartDetail.tsx:767
#: src/tables/part/PartTestTemplateTable.tsx:112
#: src/tables/stock/StockItemTestResultTable.tsx:387
msgid "Required"
msgstr "Requerido"

#: src/pages/part/PartDetail.tsx:798
msgid "Edit Part"
msgstr "Editar Pieza"

#: src/pages/part/PartDetail.tsx:833
#: src/tables/part/PartTable.tsx:340
#: src/tables/part/PartTable.tsx:390
msgid "Add Part"
msgstr "Añadir pieza"

#: src/pages/part/PartDetail.tsx:847
msgid "Delete Part"
msgstr "Eliminar pieza"

#: src/pages/part/PartDetail.tsx:856
msgid "Deleting this part cannot be reversed"
msgstr "La eliminación de esta parte no puede ser revertida"

#: src/pages/part/PartDetail.tsx:907
#: src/pages/stock/LocationDetail.tsx:309
#: src/tables/stock/StockItemTable.tsx:568
msgid "Stock Actions"
msgstr ""

#: src/pages/part/PartDetail.tsx:915
msgid "Count part stock"
msgstr ""

#: src/pages/part/PartDetail.tsx:926
msgid "Transfer part stock"
msgstr ""

#: src/pages/part/PartDetail.tsx:933
#: src/pages/part/PartSchedulingDetail.tsx:71
#: src/pages/stock/StockDetail.tsx:765
msgid "Order"
msgstr "Pedido"

#: src/pages/part/PartDetail.tsx:934
#: src/pages/stock/StockDetail.tsx:766
#: src/tables/build/BuildLineTable.tsx:594
msgid "Order Stock"
msgstr ""

#: src/pages/part/PartDetail.tsx:947
#: src/tables/part/PartTable.tsx:362
msgid "Part Actions"
msgstr ""

#: src/pages/part/PartDetail.tsx:1028
msgid "Select Part Revision"
msgstr ""

#: src/pages/part/PartIndex.tsx:29
#~ msgid "Categories"
#~ msgstr "Categories"

#: src/pages/part/PartPricingPanel.tsx:72
msgid "No pricing data found for this part."
msgstr ""

#: src/pages/part/PartPricingPanel.tsx:87
#: src/pages/part/pricing/PricingOverviewPanel.tsx:325
msgid "Pricing Overview"
msgstr ""

#: src/pages/part/PartPricingPanel.tsx:93
msgid "Purchase History"
msgstr ""

#: src/pages/part/PartPricingPanel.tsx:107
#: src/pages/part/pricing/PricingOverviewPanel.tsx:204
msgid "Internal Pricing"
msgstr ""

#: src/pages/part/PartPricingPanel.tsx:122
#: src/pages/part/pricing/PricingOverviewPanel.tsx:214
msgid "BOM Pricing"
msgstr ""

#: src/pages/part/PartPricingPanel.tsx:129
#: src/pages/part/pricing/PricingOverviewPanel.tsx:242
msgid "Variant Pricing"
msgstr ""

#: src/pages/part/PartPricingPanel.tsx:141
#: src/pages/part/pricing/PricingOverviewPanel.tsx:251
msgid "Sale Pricing"
msgstr ""

#: src/pages/part/PartPricingPanel.tsx:147
#: src/pages/part/pricing/PricingOverviewPanel.tsx:260
msgid "Sale History"
msgstr ""

#: src/pages/part/PartSchedulingDetail.tsx:48
#: src/pages/part/PartSchedulingDetail.tsx:301
#: src/pages/part/pricing/PricingOverviewPanel.tsx:170
msgid "Maximum"
msgstr "Máximo"

#: src/pages/part/PartSchedulingDetail.tsx:51
#: src/pages/part/PartSchedulingDetail.tsx:291
msgid "Scheduled"
msgstr "Programado"

#: src/pages/part/PartSchedulingDetail.tsx:54
#: src/pages/part/PartSchedulingDetail.tsx:296
#: src/pages/part/pricing/PricingOverviewPanel.tsx:158
msgid "Minimum"
msgstr "Mínimo"

#: src/pages/part/PartSchedulingDetail.tsx:95
msgid "Quantity is speculative"
msgstr "La cantidad es especulativa"

#: src/pages/part/PartSchedulingDetail.tsx:104
msgid "No date available for provided quantity"
msgstr "No hay fecha disponible para la cantidad proporcionada"

#: src/pages/part/PartSchedulingDetail.tsx:108
msgid "Date is in the past"
msgstr "La fecha es en el pasado"

#: src/pages/part/PartSchedulingDetail.tsx:115
msgid "Scheduled Quantity"
msgstr "Cantidad programada"

#: src/pages/part/PartSchedulingDetail.tsx:242
msgid "No information available"
msgstr "No hay información disponible"

#: src/pages/part/PartSchedulingDetail.tsx:243
msgid "There is no scheduling information available for the selected part"
msgstr "No hay información de programación disponible para la pieza seleccionada"

#: src/pages/part/PartSchedulingDetail.tsx:278
msgid "Expected Quantity"
msgstr "Cantidad esperada"

#: src/pages/part/PartStocktakeDetail.tsx:85
msgid "Edit Stocktake Entry"
msgstr "Editar entrada de inventario"

#: src/pages/part/PartStocktakeDetail.tsx:93
msgid "Delete Stocktake Entry"
msgstr "Eliminar entrada de inventario"

#: src/pages/part/PartStocktakeDetail.tsx:99
#: src/tables/settings/StocktakeReportTable.tsx:70
msgid "Generate Stocktake Report"
msgstr "Generar el informe de inventario"

#: src/pages/part/PartStocktakeDetail.tsx:104
#: src/tables/settings/StocktakeReportTable.tsx:72
msgid "Stocktake report scheduled"
msgstr "Informe de inventario programado"

#: src/pages/part/PartStocktakeDetail.tsx:122
#: src/pages/part/PartStocktakeDetail.tsx:240
#: src/pages/stock/StockDetail.tsx:326
#: src/tables/stock/StockItemTable.tsx:257
msgid "Stock Value"
msgstr ""

#: src/pages/part/PartStocktakeDetail.tsx:145
#: src/tables/settings/StocktakeReportTable.tsx:78
msgid "New Stocktake Report"
msgstr "Nuevo informe de inventario"

#: src/pages/part/PartStocktakeDetail.tsx:269
#: src/pages/part/pricing/PricingOverviewPanel.tsx:327
msgid "Minimum Value"
msgstr "Valor Mínimo"

#: src/pages/part/PartStocktakeDetail.tsx:275
#: src/pages/part/pricing/PricingOverviewPanel.tsx:328
msgid "Maximum Value"
msgstr "Valor Máximo"

#: src/pages/part/pricing/BomPricingPanel.tsx:87
#: src/pages/part/pricing/BomPricingPanel.tsx:177
#: src/tables/ColumnRenderers.tsx:318
#: src/tables/bom/BomTable.tsx:187
#: src/tables/general/ExtraLineItemTable.tsx:64
#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:231
#: src/tables/purchasing/PurchaseOrderTable.tsx:134
#: src/tables/sales/ReturnOrderTable.tsx:143
#: src/tables/sales/SalesOrderLineItemTable.tsx:114
#: src/tables/sales/SalesOrderTable.tsx:178
msgid "Total Price"
msgstr "Precio total"

#: src/pages/part/pricing/BomPricingPanel.tsx:112
#: src/pages/part/pricing/BomPricingPanel.tsx:141
#: src/tables/bom/UsedInTable.tsx:49
#: src/tables/build/BuildLineTable.tsx:303
#: src/tables/part/PartTable.tsx:205
msgid "Component"
msgstr "Componente"

#: src/pages/part/pricing/BomPricingPanel.tsx:112
#~ msgid "Minimum Total Price"
#~ msgstr "Minimum Total Price"

#: src/pages/part/pricing/BomPricingPanel.tsx:115
#: src/pages/part/pricing/VariantPricingPanel.tsx:37
#: src/pages/part/pricing/VariantPricingPanel.tsx:99
msgid "Minimum Price"
msgstr "Precio mínimo"

#: src/pages/part/pricing/BomPricingPanel.tsx:116
#: src/pages/part/pricing/VariantPricingPanel.tsx:45
#: src/pages/part/pricing/VariantPricingPanel.tsx:100
msgid "Maximum Price"
msgstr "Precio Máximo"

#: src/pages/part/pricing/BomPricingPanel.tsx:117
#~ msgid "Maximum Total Price"
#~ msgstr "Maximum Total Price"

#: src/pages/part/pricing/BomPricingPanel.tsx:168
#: src/pages/part/pricing/PriceBreakPanel.tsx:173
#: src/pages/part/pricing/PurchaseHistoryPanel.tsx:71
#: src/pages/part/pricing/PurchaseHistoryPanel.tsx:126
#: src/pages/part/pricing/SupplierPricingPanel.tsx:66
#: src/pages/stock/StockDetail.tsx:314
#: src/tables/bom/BomTable.tsx:178
#: src/tables/general/ExtraLineItemTable.tsx:56
#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:226
#: src/tables/purchasing/SupplierPriceBreakTable.tsx:92
#: src/tables/stock/StockItemTable.tsx:246
msgid "Unit Price"
msgstr "Precio Unitario"

#: src/pages/part/pricing/BomPricingPanel.tsx:258
msgid "Pie Chart"
msgstr ""

#: src/pages/part/pricing/BomPricingPanel.tsx:259
msgid "Bar Chart"
msgstr ""

#: src/pages/part/pricing/PriceBreakPanel.tsx:58
#: src/pages/part/pricing/PriceBreakPanel.tsx:111
#: src/tables/purchasing/SupplierPriceBreakTable.tsx:142
#: src/tables/purchasing/SupplierPriceBreakTable.tsx:170
msgid "Add Price Break"
msgstr ""

#: src/pages/part/pricing/PriceBreakPanel.tsx:71
#: src/tables/purchasing/SupplierPriceBreakTable.tsx:154
msgid "Edit Price Break"
msgstr ""

#: src/pages/part/pricing/PriceBreakPanel.tsx:81
#: src/tables/purchasing/SupplierPriceBreakTable.tsx:162
msgid "Delete Price Break"
msgstr ""

#: src/pages/part/pricing/PriceBreakPanel.tsx:95
msgid "Price Break"
msgstr ""

#: src/pages/part/pricing/PriceBreakPanel.tsx:171
msgid "Price"
msgstr "Precio"

#: src/pages/part/pricing/PricingOverviewPanel.tsx:72
msgid "Refreshing pricing data"
msgstr "Refrescando datos de precios"

#: src/pages/part/pricing/PricingOverviewPanel.tsx:92
msgid "Pricing data updated"
msgstr "Datos de precios actualizados"

#: src/pages/part/pricing/PricingOverviewPanel.tsx:99
msgid "Failed to update pricing data"
msgstr "Error al actualizar los datos de precios"

#: src/pages/part/pricing/PricingOverviewPanel.tsx:127
msgid "Edit Pricing"
msgstr "Editar precios"

#: src/pages/part/pricing/PricingOverviewPanel.tsx:139
msgid "Pricing Category"
msgstr "Categoría de precios"

#: src/pages/part/pricing/PricingOverviewPanel.tsx:188
msgid "Override Pricing"
msgstr ""

#: src/pages/part/pricing/PricingOverviewPanel.tsx:196
msgid "Overall Pricing"
msgstr ""

#: src/pages/part/pricing/PricingOverviewPanel.tsx:222
msgid "Purchase Pricing"
msgstr "Precio de Compra"

#: src/pages/part/pricing/PricingOverviewPanel.tsx:281
#: src/pages/stock/StockDetail.tsx:164
#: src/tables/stock/StockItemTable.tsx:284
msgid "Last Updated"
msgstr "Última Actualización"

#: src/pages/part/pricing/PricingOverviewPanel.tsx:285
msgid "Pricing Not Set"
msgstr "Precios no establecidos"

#: src/pages/part/pricing/PricingOverviewPanel.tsx:286
msgid "Pricing data has not been calculated for this part"
msgstr "Los datos de precios no se han calculado para esta pieza"

#: src/pages/part/pricing/PricingOverviewPanel.tsx:290
msgid "Pricing Actions"
msgstr "Acciones de precios"

#: src/pages/part/pricing/PricingOverviewPanel.tsx:293
msgid "Refresh"
msgstr "Refrescar"

#: src/pages/part/pricing/PricingOverviewPanel.tsx:294
msgid "Refresh pricing data"
msgstr "Refrescar datos de precios"

#: src/pages/part/pricing/PricingOverviewPanel.tsx:309
msgid "Edit pricing data"
msgstr "Editar datos de precios"

#: src/pages/part/pricing/PricingPanel.tsx:24
msgid "No data available"
msgstr "No hay información disponible"

#: src/pages/part/pricing/PricingPanel.tsx:65
msgid "No Data"
msgstr "Sin datos"

#: src/pages/part/pricing/PricingPanel.tsx:66
msgid "No pricing data available"
msgstr "No hay datos de precios disponibles"

#: src/pages/part/pricing/PricingPanel.tsx:77
msgid "Loading pricing data"
msgstr "Cargando datos de precios"

#: src/pages/part/pricing/PurchaseHistoryPanel.tsx:48
msgid "Purchase Price"
msgstr "Precio de Compra"

#: src/pages/part/pricing/SaleHistoryPanel.tsx:24
#~ msgid "Sale Order"
#~ msgstr "Sale Order"

#: src/pages/part/pricing/SaleHistoryPanel.tsx:44
#: src/pages/part/pricing/SaleHistoryPanel.tsx:87
msgid "Sale Price"
msgstr ""

#: src/pages/part/pricing/SupplierPricingPanel.tsx:69
#: src/tables/purchasing/SupplierPriceBreakTable.tsx:83
msgid "Supplier Price"
msgstr ""

#: src/pages/part/pricing/VariantPricingPanel.tsx:30
#: src/pages/part/pricing/VariantPricingPanel.tsx:96
msgid "Variant Part"
msgstr ""

#: src/pages/purchasing/PurchaseOrderDetail.tsx:90
msgid "Edit Purchase Order"
msgstr ""

#: src/pages/purchasing/PurchaseOrderDetail.tsx:108
#: src/tables/purchasing/PurchaseOrderTable.tsx:150
#: src/tables/purchasing/PurchaseOrderTable.tsx:163
msgid "Add Purchase Order"
msgstr ""

#: src/pages/purchasing/PurchaseOrderDetail.tsx:130
msgid "Supplier Reference"
msgstr ""

#: src/pages/purchasing/PurchaseOrderDetail.tsx:159
#: src/pages/sales/ReturnOrderDetail.tsx:126
#: src/pages/sales/SalesOrderDetail.tsx:130
#~ msgid "Order Currency,"
#~ msgstr "Order Currency,"

#: src/pages/purchasing/PurchaseOrderDetail.tsx:170
#: src/pages/sales/ReturnOrderDetail.tsx:141
#: src/pages/sales/SalesOrderDetail.tsx:144
msgid "Completed Line Items"
msgstr ""

#: src/pages/purchasing/PurchaseOrderDetail.tsx:179
#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:236
msgid "Destination"
msgstr ""

#: src/pages/purchasing/PurchaseOrderDetail.tsx:185
#: src/pages/sales/ReturnOrderDetail.tsx:148
#: src/pages/sales/SalesOrderDetail.tsx:161
msgid "Order Currency"
msgstr "Divisa de Pedido"

#: src/pages/purchasing/PurchaseOrderDetail.tsx:191
#: src/pages/sales/ReturnOrderDetail.tsx:155
#: src/pages/sales/SalesOrderDetail.tsx:167
msgid "Total Cost"
msgstr "Costo Total"

#: src/pages/purchasing/PurchaseOrderDetail.tsx:207
#: src/pages/sales/ReturnOrderDetail.tsx:183
#: src/pages/sales/SalesOrderDetail.tsx:191
#~ msgid "Created On"
#~ msgstr "Created On"

#: src/pages/purchasing/PurchaseOrderDetail.tsx:220
#: src/pages/sales/ReturnOrderDetail.tsx:184
#: src/pages/sales/SalesOrderDetail.tsx:196
msgid "Contact Email"
msgstr ""

#: src/pages/purchasing/PurchaseOrderDetail.tsx:228
#: src/pages/sales/ReturnOrderDetail.tsx:192
#: src/pages/sales/SalesOrderDetail.tsx:204
msgid "Contact Phone"
msgstr ""

#: src/pages/purchasing/PurchaseOrderDetail.tsx:261
#: src/pages/sales/ReturnOrderDetail.tsx:226
#: src/pages/sales/SalesOrderDetail.tsx:237
msgid "Issue Date"
msgstr "Fecha de emisión"

#: src/pages/purchasing/PurchaseOrderDetail.tsx:286
#: src/pages/sales/ReturnOrderDetail.tsx:250
#: src/pages/sales/SalesOrderDetail.tsx:260
#: src/tables/ColumnRenderers.tsx:276
#: src/tables/build/BuildOrderTable.tsx:112
#: src/tables/part/PartPurchaseOrdersTable.tsx:105
msgid "Completion Date"
msgstr "Fecha de Finalización"

#: src/pages/purchasing/PurchaseOrderDetail.tsx:316
#: src/pages/sales/ReturnOrderDetail.tsx:280
#: src/pages/sales/SalesOrderDetail.tsx:326
msgid "Order Details"
msgstr "Detalles del pedido"

#: src/pages/purchasing/PurchaseOrderDetail.tsx:344
#: src/pages/sales/ReturnOrderDetail.tsx:308
#: src/pages/sales/SalesOrderDetail.tsx:357
msgid "Extra Line Items"
msgstr ""

#: src/pages/purchasing/PurchaseOrderDetail.tsx:386
msgid "Issue Purchase Order"
msgstr ""

#: src/pages/purchasing/PurchaseOrderDetail.tsx:394
msgid "Cancel Purchase Order"
msgstr ""

#: src/pages/purchasing/PurchaseOrderDetail.tsx:402
msgid "Hold Purchase Order"
msgstr ""

#: src/pages/purchasing/PurchaseOrderDetail.tsx:410
msgid "Complete Purchase Order"
msgstr ""

#: src/pages/purchasing/PurchaseOrderDetail.tsx:465
#: src/pages/sales/ReturnOrderDetail.tsx:462
#: src/pages/sales/SalesOrderDetail.tsx:514
msgid "Order Actions"
msgstr ""

#: src/pages/sales/ReturnOrderDetail.tsx:95
#: src/pages/sales/SalesOrderDetail.tsx:104
#: src/pages/sales/SalesOrderShipmentDetail.tsx:113
#: src/tables/sales/SalesOrderTable.tsx:153
msgid "Customer Reference"
msgstr ""

#: src/pages/sales/ReturnOrderDetail.tsx:349
#~ msgid "Order canceled"
#~ msgstr "Order canceled"

#: src/pages/sales/ReturnOrderDetail.tsx:354
msgid "Edit Return Order"
msgstr ""

#: src/pages/sales/ReturnOrderDetail.tsx:372
#: src/tables/sales/ReturnOrderTable.tsx:158
#: src/tables/sales/ReturnOrderTable.tsx:171
msgid "Add Return Order"
msgstr ""

#: src/pages/sales/ReturnOrderDetail.tsx:381
msgid "Issue Return Order"
msgstr ""

#: src/pages/sales/ReturnOrderDetail.tsx:389
msgid "Cancel Return Order"
msgstr ""

#: src/pages/sales/ReturnOrderDetail.tsx:397
msgid "Hold Return Order"
msgstr ""

#: src/pages/sales/ReturnOrderDetail.tsx:405
msgid "Complete Return Order"
msgstr ""

#: src/pages/sales/SalesOrderDetail.tsx:153
msgid "Completed Shipments"
msgstr ""

#: src/pages/sales/SalesOrderDetail.tsx:256
#~ msgid "Pending Shipments"
#~ msgstr "Pending Shipments"

#: src/pages/sales/SalesOrderDetail.tsx:293
msgid "Edit Sales Order"
msgstr ""

#: src/pages/sales/SalesOrderDetail.tsx:315
#: src/tables/sales/SalesOrderTable.tsx:112
#: src/tables/sales/SalesOrderTable.tsx:125
msgid "Add Sales Order"
msgstr ""

#: src/pages/sales/SalesOrderDetail.tsx:373
#: src/tables/sales/SalesOrderTable.tsx:159
msgid "Shipments"
msgstr "Envíos"

#: src/pages/sales/SalesOrderDetail.tsx:415
msgid "Issue Sales Order"
msgstr ""

#: src/pages/sales/SalesOrderDetail.tsx:423
msgid "Cancel Sales Order"
msgstr ""

#: src/pages/sales/SalesOrderDetail.tsx:431
msgid "Hold Sales Order"
msgstr ""

#: src/pages/sales/SalesOrderDetail.tsx:439
msgid "Ship Sales Order"
msgstr "Orden de Venta de Envío"

#: src/pages/sales/SalesOrderDetail.tsx:441
msgid "Ship this order?"
msgstr "¿Enviar este pedido?"

#: src/pages/sales/SalesOrderDetail.tsx:442
msgid "Order shipped"
msgstr "Pedido enviado"

#: src/pages/sales/SalesOrderDetail.tsx:450
msgid "Complete Sales Order"
msgstr ""

#: src/pages/sales/SalesOrderDetail.tsx:489
msgid "Ship Order"
msgstr ""

#: src/pages/sales/SalesOrderShipmentDetail.tsx:121
#: src/tables/sales/SalesOrderShipmentTable.tsx:94
msgid "Shipment Reference"
msgstr ""

#: src/pages/sales/SalesOrderShipmentDetail.tsx:128
msgid "Allocated Items"
msgstr "Artículos asignados"

#: src/pages/sales/SalesOrderShipmentDetail.tsx:137
msgid "Tracking Number"
msgstr "Número de Seguimiento"

#: src/pages/sales/SalesOrderShipmentDetail.tsx:145
msgid "Invoice Number"
msgstr "Número de factura"

#: src/pages/sales/SalesOrderShipmentDetail.tsx:153
#: src/tables/ColumnRenderers.tsx:284
#: src/tables/sales/SalesOrderAllocationTable.tsx:181
#: src/tables/sales/SalesOrderShipmentTable.tsx:113
msgid "Shipment Date"
msgstr ""

#: src/pages/sales/SalesOrderShipmentDetail.tsx:161
#: src/tables/sales/SalesOrderShipmentTable.tsx:117
msgid "Delivery Date"
msgstr ""

#: src/pages/sales/SalesOrderShipmentDetail.tsx:206
msgid "Shipment Details"
msgstr "Detalles del envío"

#: src/pages/sales/SalesOrderShipmentDetail.tsx:211
#~ msgid "Assigned Items"
#~ msgstr "Assigned Items"

#: src/pages/sales/SalesOrderShipmentDetail.tsx:244
#: src/pages/sales/SalesOrderShipmentDetail.tsx:335
#: src/tables/sales/SalesOrderShipmentTable.tsx:73
msgid "Edit Shipment"
msgstr ""

#: src/pages/sales/SalesOrderShipmentDetail.tsx:251
#: src/pages/sales/SalesOrderShipmentDetail.tsx:340
#: src/tables/sales/SalesOrderShipmentTable.tsx:65
msgid "Cancel Shipment"
msgstr "Cancelar envío"

#: src/pages/sales/SalesOrderShipmentDetail.tsx:264
#: src/tables/sales/SalesOrderShipmentTable.tsx:81
#: src/tables/sales/SalesOrderShipmentTable.tsx:144
msgid "Complete Shipment"
msgstr ""

#: src/pages/sales/SalesOrderShipmentDetail.tsx:281
#: src/tables/part/PartPurchaseOrdersTable.tsx:121
msgid "Pending"
msgstr ""

#: src/pages/sales/SalesOrderShipmentDetail.tsx:287
#: src/tables/sales/SalesOrderShipmentTable.tsx:106
#: src/tables/sales/SalesOrderShipmentTable.tsx:190
msgid "Shipped"
msgstr ""

#: src/pages/sales/SalesOrderShipmentDetail.tsx:293
#: src/tables/sales/SalesOrderShipmentTable.tsx:195
msgid "Delivered"
msgstr ""

#: src/pages/sales/SalesOrderShipmentDetail.tsx:308
msgid "Send Shipment"
msgstr "Enviar envío"

#: src/pages/sales/SalesOrderShipmentDetail.tsx:330
msgid "Shipment Actions"
msgstr "Acciones de envío"

#: src/pages/stock/LocationDetail.tsx:113
msgid "Parent Location"
msgstr ""

#: src/pages/stock/LocationDetail.tsx:131
msgid "Sublocations"
msgstr ""

#: src/pages/stock/LocationDetail.tsx:143
#: src/tables/stock/StockLocationTable.tsx:48
msgid "External"
msgstr ""

#: src/pages/stock/LocationDetail.tsx:149
#: src/tables/stock/StockLocationTable.tsx:57
msgid "Location Type"
msgstr ""

#: src/pages/stock/LocationDetail.tsx:160
msgid "Top level stock location"
msgstr ""

#: src/pages/stock/LocationDetail.tsx:171
msgid "Location Details"
msgstr ""

#: src/pages/stock/LocationDetail.tsx:197
msgid "Default Parts"
msgstr ""

#: src/pages/stock/LocationDetail.tsx:216
#: src/pages/stock/LocationDetail.tsx:335
#: src/tables/stock/StockLocationTable.tsx:119
msgid "Edit Stock Location"
msgstr ""

#: src/pages/stock/LocationDetail.tsx:225
msgid "Move items to parent location"
msgstr "Mover elementos a la categoría padre"

#: src/pages/stock/LocationDetail.tsx:237
#: src/pages/stock/LocationDetail.tsx:340
msgid "Delete Stock Location"
msgstr ""

#: src/pages/stock/LocationDetail.tsx:240
msgid "Items Action"
msgstr ""

#: src/pages/stock/LocationDetail.tsx:241
msgid "Action for stock items in this location"
msgstr ""

#: src/pages/stock/LocationDetail.tsx:246
msgid "Child Locations Action"
msgstr ""

#: src/pages/stock/LocationDetail.tsx:247
msgid "Action for child locations in this location"
msgstr ""

#: src/pages/stock/LocationDetail.tsx:331
#: src/tables/stock/StockLocationTable.tsx:140
msgid "Location Actions"
msgstr ""

#: src/pages/stock/StockDetail.tsx:125
msgid "Base Part"
msgstr ""

#: src/pages/stock/StockDetail.tsx:155
#~ msgid "Link custom barcode to stock item"
#~ msgstr "Link custom barcode to stock item"

#: src/pages/stock/StockDetail.tsx:156
msgid "Completed Tests"
msgstr ""

#: src/pages/stock/StockDetail.tsx:161
#~ msgid "Unlink custom barcode from stock item"
#~ msgstr "Unlink custom barcode from stock item"

#: src/pages/stock/StockDetail.tsx:170
msgid "Last Stocktake"
msgstr ""

#: src/pages/stock/StockDetail.tsx:197
msgid "Allocated to Orders"
msgstr "Asignado a Pedidos"

#: src/pages/stock/StockDetail.tsx:205
#~ msgid "Edit stock item"
#~ msgstr "Edit stock item"

#: src/pages/stock/StockDetail.tsx:217
#~ msgid "Delete stock item"
#~ msgstr "Delete stock item"

#: src/pages/stock/StockDetail.tsx:229
msgid "Installed In"
msgstr ""

#: src/pages/stock/StockDetail.tsx:249
msgid "Parent Item"
msgstr "Artículo padre"

#: src/pages/stock/StockDetail.tsx:253
msgid "Parent stock item"
msgstr "Artículo de existencias padre"

#: src/pages/stock/StockDetail.tsx:259
msgid "Consumed By"
msgstr ""

#: src/pages/stock/StockDetail.tsx:433
#~ msgid "Duplicate stock item"
#~ msgstr "Duplicate stock item"

#: src/pages/stock/StockDetail.tsx:434
msgid "Stock Details"
msgstr ""

#: src/pages/stock/StockDetail.tsx:440
msgid "Stock Tracking"
msgstr ""

#: src/pages/stock/StockDetail.tsx:495
msgid "Test Data"
msgstr ""

#: src/pages/stock/StockDetail.tsx:509
msgid "Installed Items"
msgstr ""

#: src/pages/stock/StockDetail.tsx:516
msgid "Child Items"
msgstr ""

#: src/pages/stock/StockDetail.tsx:566
msgid "Edit Stock Item"
msgstr ""

#: src/pages/stock/StockDetail.tsx:593
msgid "Delete Stock Item"
msgstr ""

#: src/pages/stock/StockDetail.tsx:626
msgid "Serialize Stock Item"
msgstr "Serializar artículo de existencias"

#: src/pages/stock/StockDetail.tsx:639
msgid "Stock item serialized"
msgstr "Serializado artículo de existencias"

#: src/pages/stock/StockDetail.tsx:645
msgid "Return Stock Item"
msgstr "Devolver artículo de existencias"

#: src/pages/stock/StockDetail.tsx:648
msgid "Return this item into stock. This will remove the customer assignment."
msgstr "Devuelve este artículo a las existencias. Esto eliminará la asignación del cliente."

#: src/pages/stock/StockDetail.tsx:660
msgid "Item returned to stock"
msgstr "Artículo devuelto a existencias"

#: src/pages/stock/StockDetail.tsx:671
#: src/tables/stock/StockItemTable.tsx:452
#~ msgid "Add stock"
#~ msgstr "Add stock"

#: src/pages/stock/StockDetail.tsx:680
#: src/tables/stock/StockItemTable.tsx:461
#~ msgid "Remove stock"
#~ msgstr "Remove stock"

#: src/pages/stock/StockDetail.tsx:698
#: src/tables/stock/StockItemTable.tsx:481
#~ msgid "Transfer stock"
#~ msgstr "Transfer stock"

#: src/pages/stock/StockDetail.tsx:704
msgid "Stock Operations"
msgstr ""

#: src/pages/stock/StockDetail.tsx:709
msgid "Count stock"
msgstr "Contar stock"

#: src/pages/stock/StockDetail.tsx:741
msgid "Transfer"
msgstr "Transferir"

#: src/pages/stock/StockDetail.tsx:752
msgid "Serialize"
msgstr "Serializar"

#: src/pages/stock/StockDetail.tsx:753
msgid "Serialize stock"
msgstr "Serializar stock"

#: src/pages/stock/StockDetail.tsx:777
msgid "Return"
msgstr "Devolver"

#: src/pages/stock/StockDetail.tsx:778
msgid "Return from customer"
msgstr "Devolución del cliente"

#: src/pages/stock/StockDetail.tsx:791
msgid "Assign to Customer"
msgstr "Asignar al Cliente"

#: src/pages/stock/StockDetail.tsx:792
msgid "Assign to a customer"
msgstr "Asignar a cliente"

#: src/pages/stock/StockDetail.tsx:804
msgid "Stock Item Actions"
msgstr ""

#: src/pages/stock/StockDetail.tsx:873
#: src/tables/stock/StockItemTable.tsx:422
msgid "Stale"
msgstr "Obsoleto"

#: src/pages/stock/StockDetail.tsx:879
#: src/tables/stock/StockItemTable.tsx:416
msgid "Expired"
msgstr "Expirado"

#: src/pages/stock/StockDetail.tsx:885
msgid "Unavailable"
msgstr "No disponible"

#: src/states/IconState.tsx:47
#: src/states/IconState.tsx:77
msgid "Error loading icon package from server"
msgstr "Error al cargar el paquete de iconos del servidor"

#: src/tables/ColumnRenderers.tsx:37
msgid "Part is not active"
msgstr "La pieza no está activa"

#: src/tables/ColumnRenderers.tsx:41
#~ msgid "Part is locked"
#~ msgstr "Part is locked"

#: src/tables/ColumnRenderers.tsx:42
#: src/tables/bom/BomTable.tsx:549
#: src/tables/part/PartParameterTable.tsx:193
#: src/tables/part/PartTestTemplateTable.tsx:258
msgid "Part is Locked"
msgstr ""

#: src/tables/ColumnRenderers.tsx:47
msgid "You are subscribed to notifications for this part"
msgstr "Estás suscrito a las notificaciones de esta pieza"

#: src/tables/ColumnRenderers.tsx:72
msgid "No location set"
msgstr ""

#: src/tables/ColumnSelect.tsx:16
#: src/tables/ColumnSelect.tsx:23
msgid "Select Columns"
msgstr ""

#: src/tables/DownloadAction.tsx:13
#~ msgid "Excel"
#~ msgstr "Excel"

#: src/tables/DownloadAction.tsx:21
#~ msgid "CSV"
#~ msgstr "CSV"

#: src/tables/DownloadAction.tsx:21
#~ msgid "Download selected data"
#~ msgstr "Download selected data"

#: src/tables/DownloadAction.tsx:22
#~ msgid "TSV"
#~ msgstr "TSV"

#: src/tables/DownloadAction.tsx:23
#~ msgid "Excel (.xlsx)"
#~ msgstr "Excel (.xlsx)"

#: src/tables/DownloadAction.tsx:24
#~ msgid "Excel (.xls)"
#~ msgstr "Excel (.xls)"

#: src/tables/DownloadAction.tsx:36
#~ msgid "Download Data"
#~ msgstr "Download Data"

#: src/tables/Filter.tsx:106
#~ msgid "Show overdue orders"
#~ msgstr "Show overdue orders"

#: src/tables/Filter.tsx:124
msgid "Assigned to me"
msgstr ""

#: src/tables/Filter.tsx:125
msgid "Show orders assigned to me"
msgstr ""

#: src/tables/Filter.tsx:132
#: src/tables/sales/SalesOrderAllocationTable.tsx:77
msgid "Outstanding"
msgstr ""

#: src/tables/Filter.tsx:133
msgid "Show outstanding items"
msgstr "Mostrar artículos destacados"

#: src/tables/Filter.tsx:141
msgid "Show overdue items"
msgstr "Mostrar artículos vencidos"

#: src/tables/Filter.tsx:148
msgid "Minimum Date"
msgstr "Fecha Mínima"

#: src/tables/Filter.tsx:149
msgid "Show items after this date"
msgstr "Mostrar elementos después de esta fecha"

#: src/tables/Filter.tsx:157
msgid "Maximum Date"
msgstr "Fecha máxima"

#: src/tables/Filter.tsx:158
msgid "Show items before this date"
msgstr "Mostrar artículos antes de esta fecha"

#: src/tables/Filter.tsx:166
msgid "Created Before"
msgstr "Creado antes de"

#: src/tables/Filter.tsx:167
msgid "Show items created before this date"
msgstr "Mostrar elementos creados antes de esta fecha"

#: src/tables/Filter.tsx:175
msgid "Created After"
msgstr "Creado después de"

#: src/tables/Filter.tsx:176
msgid "Show items created after this date"
msgstr "Mostrar elementos creados después de esta fecha"

#: src/tables/Filter.tsx:184
msgid "Start Date Before"
msgstr ""

#: src/tables/Filter.tsx:185
msgid "Show items with a start date before this date"
msgstr ""

#: src/tables/Filter.tsx:193
msgid "Start Date After"
msgstr ""

#: src/tables/Filter.tsx:194
msgid "Show items with a start date after this date"
msgstr ""

#: src/tables/Filter.tsx:202
msgid "Target Date Before"
msgstr "Fecha objetivo antes de"

#: src/tables/Filter.tsx:203
msgid "Show items with a target date before this date"
msgstr "Mostrar elementos con una fecha objetivo anterior a esta fecha"

#: src/tables/Filter.tsx:211
msgid "Target Date After"
msgstr "Fecha objetivo después de"

#: src/tables/Filter.tsx:212
msgid "Show items with a target date after this date"
msgstr "Mostrar elementos con una fecha de objetivo posterior a esta fecha"

#: src/tables/Filter.tsx:220
msgid "Completed Before"
msgstr "Completado antes de"

#: src/tables/Filter.tsx:221
msgid "Show items completed before this date"
msgstr "Mostrar elementos completados antes de esta fecha"

#: src/tables/Filter.tsx:229
msgid "Completed After"
msgstr "Completado después de"

#: src/tables/Filter.tsx:230
msgid "Show items completed after this date"
msgstr "Mostrar elementos completados después de esta fecha"

#: src/tables/Filter.tsx:242
msgid "Has Project Code"
msgstr ""

#: src/tables/Filter.tsx:243
msgid "Show orders with an assigned project code"
msgstr "Mostrar pedidos con un código de proyecto asignado"

#: src/tables/Filter.tsx:254
#: src/tables/part/PartPurchaseOrdersTable.tsx:132
msgid "Filter by order status"
msgstr "Filtrar por estado de la orden"

#: src/tables/Filter.tsx:266
msgid "Filter by project code"
msgstr ""

#: src/tables/Filter.tsx:299
msgid "Filter by responsible owner"
msgstr ""

#: src/tables/Filter.tsx:315
#: src/tables/settings/ApiTokenTable.tsx:120
#: src/tables/stock/StockTrackingTable.tsx:189
msgid "Filter by user"
msgstr ""

#: src/tables/Filter.tsx:327
msgid "Filter by user who created the order"
msgstr ""

#: src/tables/Filter.tsx:335
msgid "Filter by user who issued the order"
msgstr ""

#: src/tables/Filter.tsx:343
msgid "Filter by part category"
msgstr ""

#: src/tables/FilterSelectDrawer.tsx:58
msgid "Remove filter"
msgstr "Eliminar filtro"

#: src/tables/FilterSelectDrawer.tsx:100
#: src/tables/FilterSelectDrawer.tsx:102
#: src/tables/FilterSelectDrawer.tsx:148
msgid "Select filter value"
msgstr "Seleccionar valor del filtro"

#: src/tables/FilterSelectDrawer.tsx:114
msgid "Enter filter value"
msgstr "Introducir valor de filtro"

#: src/tables/FilterSelectDrawer.tsx:136
msgid "Select date value"
msgstr "Seleccionar fecha"

#: src/tables/FilterSelectDrawer.tsx:258
msgid "Select filter"
msgstr "Seleccionar filtro"

#: src/tables/FilterSelectDrawer.tsx:259
msgid "Filter"
msgstr "Filtro"

#: src/tables/FilterSelectDrawer.tsx:311
#: src/tables/InvenTreeTableHeader.tsx:238
msgid "Table Filters"
msgstr "Filtros de tabla"

#: src/tables/FilterSelectDrawer.tsx:343
msgid "Add Filter"
msgstr "Añadir filtro"

#: src/tables/FilterSelectDrawer.tsx:352
msgid "Clear Filters"
msgstr "Borrar Filtros"

#: src/tables/InvenTreeTable.tsx:105
#: src/tables/InvenTreeTable.tsx:444
#: src/tables/InvenTreeTable.tsx:472
msgid "No records found"
msgstr "Ningún registro encontrado"

#: src/tables/InvenTreeTable.tsx:218
msgid "Failed to load table options"
msgstr "Error al cargar las opciones de tabla"

#: src/tables/InvenTreeTable.tsx:483
msgid "Server returned incorrect data type"
msgstr "El servidor devolvió un tipo de datos incorrecto"

#: src/tables/InvenTreeTable.tsx:510
#~ msgid "Are you sure you want to delete the selected records?"
#~ msgstr "Are you sure you want to delete the selected records?"

#: src/tables/InvenTreeTable.tsx:535
#~ msgid "Deleted records"
#~ msgstr "Deleted records"

#: src/tables/InvenTreeTable.tsx:536
#~ msgid "Records were deleted successfully"
#~ msgstr "Records were deleted successfully"

#: src/tables/InvenTreeTable.tsx:545
#~ msgid "Failed to delete records"
#~ msgstr "Failed to delete records"

#: src/tables/InvenTreeTable.tsx:552
#~ msgid "This action cannot be undone!"
#~ msgstr "This action cannot be undone!"

#: src/tables/InvenTreeTable.tsx:594
#: src/tables/InvenTreeTable.tsx:595
#~ msgid "Print actions"
#~ msgstr "Print actions"

#: src/tables/InvenTreeTable.tsx:647
msgid "View details"
msgstr ""

#: src/tables/InvenTreeTable.tsx:655
#: src/tables/InvenTreeTable.tsx:656
#~ msgid "Barcode actions"
#~ msgstr "Barcode actions"

#: src/tables/InvenTreeTable.tsx:712
#~ msgid "Table filters"
#~ msgstr "Table filters"

#: src/tables/InvenTreeTable.tsx:725
#~ msgid "Clear custom query filters"
#~ msgstr "Clear custom query filters"

#: src/tables/InvenTreeTableHeader.tsx:97
msgid "Delete Selected Items"
msgstr "Eliminar Elementos Seleccionados"

#: src/tables/InvenTreeTableHeader.tsx:101
msgid "Are you sure you want to delete the selected items?"
msgstr ""

#: src/tables/InvenTreeTableHeader.tsx:103
#: src/tables/plugin/PluginListTable.tsx:315
msgid "This action cannot be undone"
msgstr "Esta acción no se puede deshacer"

#: src/tables/InvenTreeTableHeader.tsx:114
msgid "Items deleted"
msgstr ""

#: src/tables/InvenTreeTableHeader.tsx:119
msgid "Failed to delete items"
msgstr ""

#: src/tables/InvenTreeTableHeader.tsx:165
msgid "Custom table filters are active"
msgstr "Los filtros personalizados de tabla están activos"

#: src/tables/InvenTreeTableHeader.tsx:191
#: src/tables/general/BarcodeScanTable.tsx:93
msgid "Delete selected records"
msgstr ""

#: src/tables/InvenTreeTableHeader.tsx:211
msgid "Refresh data"
msgstr ""

#: src/tables/TableHoverCard.tsx:35
#~ msgid "item-{idx}"
#~ msgstr "item-{idx}"

#: src/tables/UploadAction.tsx:7
#~ msgid "Upload Data"
#~ msgstr "Upload Data"

#: src/tables/bom/BomTable.tsx:97
msgid "This BOM item is defined for a different parent"
msgstr ""

#: src/tables/bom/BomTable.tsx:112
msgid "Part Information"
msgstr ""

#: src/tables/bom/BomTable.tsx:214
#: src/tables/build/BuildLineTable.tsx:264
#: src/tables/part/PartTable.tsx:130
msgid "External stock"
msgstr ""

#: src/tables/bom/BomTable.tsx:222
#: src/tables/build/BuildLineTable.tsx:227
msgid "Includes substitute stock"
msgstr ""

#: src/tables/bom/BomTable.tsx:231
#: src/tables/build/BuildLineTable.tsx:237
#: src/tables/sales/SalesOrderLineItemTable.tsx:152
msgid "Includes variant stock"
msgstr ""

#: src/tables/bom/BomTable.tsx:247
#: src/tables/part/PartTable.tsx:99
msgid "Building"
msgstr ""

#: src/tables/bom/BomTable.tsx:256
#: src/tables/part/PartTable.tsx:156
#: src/tables/sales/SalesOrderLineItemTable.tsx:175
#: src/tables/stock/StockItemTable.tsx:212
msgid "Stock Information"
msgstr ""

#: src/tables/bom/BomTable.tsx:290
#: src/tables/build/BuildLineTable.tsx:409
msgid "Consumable item"
msgstr ""

#: src/tables/bom/BomTable.tsx:293
msgid "No available stock"
msgstr ""

#: src/tables/bom/BomTable.tsx:301
#~ msgid "Create BOM Item"
#~ msgstr "Create BOM Item"

#: src/tables/bom/BomTable.tsx:310
#~ msgid "Show asssmbled items"
#~ msgstr "Show asssmbled items"

#: src/tables/bom/BomTable.tsx:311
#: src/tables/build/BuildLineTable.tsx:207
msgid "Show testable items"
msgstr ""

#: src/tables/bom/BomTable.tsx:316
msgid "Show trackable items"
msgstr ""

#: src/tables/bom/BomTable.tsx:321
#: src/tables/build/BuildLineTable.tsx:202
msgid "Show assembled items"
msgstr ""

#: src/tables/bom/BomTable.tsx:326
#: src/tables/build/BuildLineTable.tsx:187
msgid "Show items with available stock"
msgstr ""

#: src/tables/bom/BomTable.tsx:331
msgid "Show items on order"
msgstr ""

#: src/tables/bom/BomTable.tsx:331
#~ msgid "Edit Bom Item"
#~ msgstr "Edit Bom Item"

#: src/tables/bom/BomTable.tsx:333
#~ msgid "Bom item updated"
#~ msgstr "Bom item updated"

#: src/tables/bom/BomTable.tsx:335
msgid "Validated"
msgstr "Validado"

#: src/tables/bom/BomTable.tsx:336
msgid "Show validated items"
msgstr "Mostrar artículos validados"

#: src/tables/bom/BomTable.tsx:340
#: src/tables/bom/UsedInTable.tsx:75
msgid "Inherited"
msgstr ""

#: src/tables/bom/BomTable.tsx:341
#: src/tables/bom/UsedInTable.tsx:76
msgid "Show inherited items"
msgstr ""

#: src/tables/bom/BomTable.tsx:345
msgid "Allow Variants"
msgstr ""

#: src/tables/bom/BomTable.tsx:346
msgid "Show items which allow variant substitution"
msgstr ""

#: src/tables/bom/BomTable.tsx:348
#~ msgid "Delete Bom Item"
#~ msgstr "Delete Bom Item"

#: src/tables/bom/BomTable.tsx:349
#~ msgid "Bom item deleted"
#~ msgstr "Bom item deleted"

#: src/tables/bom/BomTable.tsx:350
#: src/tables/bom/UsedInTable.tsx:80
#: src/tables/build/BuildLineTable.tsx:196
msgid "Optional"
msgstr "Opcional"

#: src/tables/bom/BomTable.tsx:351
#: src/tables/bom/UsedInTable.tsx:81
msgid "Show optional items"
msgstr ""

#: src/tables/bom/BomTable.tsx:351
#~ msgid "Are you sure you want to remove this BOM item?"
#~ msgstr "Are you sure you want to remove this BOM item?"

#: src/tables/bom/BomTable.tsx:354
#~ msgid "Validate BOM line"
#~ msgstr "Validate BOM line"

#: src/tables/bom/BomTable.tsx:355
#: src/tables/build/BuildLineTable.tsx:191
msgid "Consumable"
msgstr "Consumible"

#: src/tables/bom/BomTable.tsx:356
msgid "Show consumable items"
msgstr "Mostrar artículos consumibles"

#: src/tables/bom/BomTable.tsx:360
#: src/tables/part/PartTable.tsx:291
msgid "Has Pricing"
msgstr "Tiene Precio"

#: src/tables/bom/BomTable.tsx:361
msgid "Show items with pricing"
msgstr ""

#: src/tables/bom/BomTable.tsx:383
#: src/tables/bom/BomTable.tsx:519
msgid "Import BOM Data"
msgstr ""

#: src/tables/bom/BomTable.tsx:393
#: src/tables/bom/BomTable.tsx:533
msgid "Add BOM Item"
msgstr ""

#: src/tables/bom/BomTable.tsx:398
msgid "BOM item created"
msgstr ""

#: src/tables/bom/BomTable.tsx:405
msgid "Edit BOM Item"
msgstr ""

#: src/tables/bom/BomTable.tsx:407
msgid "BOM item updated"
msgstr ""

#: src/tables/bom/BomTable.tsx:414
msgid "Delete BOM Item"
msgstr ""

#: src/tables/bom/BomTable.tsx:415
msgid "BOM item deleted"
msgstr ""

#: src/tables/bom/BomTable.tsx:428
#: src/tables/bom/BomTable.tsx:431
#: src/tables/bom/BomTable.tsx:526
msgid "Validate BOM"
msgstr "Validar BOM"

#: src/tables/bom/BomTable.tsx:432
msgid "Do you want to validate the bill of materials for this assembly?"
msgstr ""

#: src/tables/bom/BomTable.tsx:435
msgid "BOM validated"
msgstr ""

#: src/tables/bom/BomTable.tsx:447
msgid "BOM item validated"
msgstr ""

#: src/tables/bom/BomTable.tsx:456
msgid "Failed to validate BOM item"
msgstr ""

#: src/tables/bom/BomTable.tsx:468
msgid "View BOM"
msgstr ""

#: src/tables/bom/BomTable.tsx:479
msgid "Validate BOM Line"
msgstr ""

#: src/tables/bom/BomTable.tsx:496
msgid "Edit Substitutes"
msgstr ""

#: src/tables/bom/BomTable.tsx:554
msgid "Bill of materials cannot be edited, as the part is locked"
msgstr ""

#: src/tables/bom/UsedInTable.tsx:33
#: src/tables/build/BuildLineTable.tsx:201
#: src/tables/part/ParametricPartTable.tsx:234
#: src/tables/part/PartTable.tsx:193
#: src/tables/stock/StockItemTable.tsx:317
msgid "Assembly"
msgstr ""

#: src/tables/bom/UsedInTable.tsx:86
msgid "Show active assemblies"
msgstr ""

#: src/tables/bom/UsedInTable.tsx:90
#: src/tables/part/PartTable.tsx:217
#: src/tables/part/PartVariantTable.tsx:30
msgid "Trackable"
msgstr "Rastreable"

#: src/tables/bom/UsedInTable.tsx:91
msgid "Show trackable assemblies"
msgstr "Mostrar ensamblajes rastreables"

#: src/tables/build/BuildAllocatedStockTable.tsx:56
msgid "Allocated to Output"
msgstr ""

#: src/tables/build/BuildAllocatedStockTable.tsx:57
msgid "Show items allocated to a build output"
msgstr ""

#: src/tables/build/BuildAllocatedStockTable.tsx:65
#: src/tables/build/BuildOrderTable.tsx:166
#: src/tables/part/PartParameterTable.tsx:212
#: src/tables/part/PartPurchaseOrdersTable.tsx:138
#: src/tables/sales/ReturnOrderTable.tsx:99
#: src/tables/sales/SalesOrderAllocationTable.tsx:91
#: src/tables/sales/SalesOrderTable.tsx:100
#: src/tables/stock/StockItemTable.tsx:352
msgid "Include Variants"
msgstr ""

#: src/tables/build/BuildAllocatedStockTable.tsx:66
#: src/tables/build/BuildOrderTable.tsx:167
#: src/tables/part/PartPurchaseOrdersTable.tsx:139
#: src/tables/sales/ReturnOrderTable.tsx:100
#: src/tables/sales/SalesOrderAllocationTable.tsx:92
#: src/tables/sales/SalesOrderTable.tsx:101
msgid "Include orders for part variants"
msgstr "Incluye pedidos para variantes de piezas"

#: src/tables/build/BuildAllocatedStockTable.tsx:89
#: src/tables/part/PartBuildAllocationsTable.tsx:62
#: src/tables/part/PartPurchaseOrdersTable.tsx:131
#: src/tables/part/PartSalesAllocationsTable.tsx:58
#: src/tables/sales/SalesOrderAllocationTable.tsx:116
msgid "Order Status"
msgstr ""

#: src/tables/build/BuildAllocatedStockTable.tsx:117
#: src/tables/sales/SalesOrderAllocationTable.tsx:162
msgid "Allocated Quantity"
msgstr ""

#: src/tables/build/BuildAllocatedStockTable.tsx:137
#: src/tables/sales/SalesOrderAllocationTable.tsx:155
msgid "Available Quantity"
msgstr ""

#: src/tables/build/BuildAllocatedStockTable.tsx:164
#: src/tables/build/BuildLineTable.tsx:513
msgid "Edit Stock Allocation"
msgstr "Editar asignación de existencias"

#: src/tables/build/BuildAllocatedStockTable.tsx:164
#~ msgid "Edit Build Item"
#~ msgstr "Edit Build Item"

#: src/tables/build/BuildAllocatedStockTable.tsx:174
#~ msgid "Delete Build Item"
#~ msgstr "Delete Build Item"

#: src/tables/build/BuildAllocatedStockTable.tsx:177
#: src/tables/build/BuildLineTable.tsx:526
msgid "Delete Stock Allocation"
msgstr "Eliminar asignación de existencias"

#: src/tables/build/BuildLineTable.tsx:59
#~ msgid "Show lines with available stock"
#~ msgstr "Show lines with available stock"

#: src/tables/build/BuildLineTable.tsx:104
msgid "View Stock Item"
msgstr "Ver artículo de existencias"

#: src/tables/build/BuildLineTable.tsx:182
msgid "Show allocated lines"
msgstr ""

#: src/tables/build/BuildLineTable.tsx:192
msgid "Show consumable lines"
msgstr ""

#: src/tables/build/BuildLineTable.tsx:197
msgid "Show optional lines"
msgstr ""

#: src/tables/build/BuildLineTable.tsx:206
#: src/tables/part/PartTable.tsx:211
msgid "Testable"
msgstr ""

#: src/tables/build/BuildLineTable.tsx:211
#: src/tables/stock/StockItemTable.tsx:406
msgid "Tracked"
msgstr ""

#: src/tables/build/BuildLineTable.tsx:212
msgid "Show tracked lines"
msgstr ""

#: src/tables/build/BuildLineTable.tsx:246
#: src/tables/sales/SalesOrderLineItemTable.tsx:158
msgid "In production"
msgstr ""

#: src/tables/build/BuildLineTable.tsx:274
msgid "Insufficient stock"
msgstr ""

#: src/tables/build/BuildLineTable.tsx:290
#: src/tables/sales/SalesOrderLineItemTable.tsx:146
#: src/tables/stock/StockItemTable.tsx:181
msgid "No stock available"
msgstr ""

#: src/tables/build/BuildLineTable.tsx:355
msgid "Gets Inherited"
msgstr ""

#: src/tables/build/BuildLineTable.tsx:366
msgid "Unit Quantity"
msgstr ""

#: src/tables/build/BuildLineTable.tsx:381
msgid "Required Quantity"
msgstr "Cantidad requerida"

#: src/tables/build/BuildLineTable.tsx:432
#: src/tables/sales/SalesOrderLineItemTable.tsx:278
msgid "Create Build Order"
msgstr ""

#: src/tables/build/BuildLineTable.tsx:460
msgid "Auto allocation in progress"
msgstr "Auto asignación en progreso"

#: src/tables/build/BuildLineTable.tsx:463
#: src/tables/build/BuildLineTable.tsx:635
msgid "Auto Allocate Stock"
msgstr "Autoasignar stock"

#: src/tables/build/BuildLineTable.tsx:464
msgid "Automatically allocate stock to this build according to the selected options"
msgstr "Asignar stock automáticamente a esta construcción de acuerdo a las opciones seleccionadas"

#: src/tables/build/BuildLineTable.tsx:482
#: src/tables/build/BuildLineTable.tsx:496
#: src/tables/build/BuildLineTable.tsx:584
#: src/tables/build/BuildLineTable.tsx:685
#: src/tables/build/BuildOutputTable.tsx:315
#: src/tables/build/BuildOutputTable.tsx:320
msgid "Deallocate Stock"
msgstr "Desasignar existencias"

#: src/tables/build/BuildLineTable.tsx:498
msgid "Deallocate all untracked stock for this build order"
msgstr "Desasignar todo el stock sin seguimiento para este pedido"

#: src/tables/build/BuildLineTable.tsx:500
msgid "Deallocate stock from the selected line item"
msgstr "Desasignar stock de la partida seleccionada"

#: src/tables/build/BuildLineTable.tsx:504
msgid "Stock has been deallocated"
msgstr "Stock ha sido desasignado"

#: src/tables/build/BuildLineTable.tsx:604
msgid "Build Stock"
msgstr ""

#: src/tables/build/BuildLineTable.tsx:617
#: src/tables/sales/SalesOrderLineItemTable.tsx:364
msgid "View Part"
msgstr "Ver pieza"

#: src/tables/build/BuildOrderTable.tsx:116
#~ msgid "Cascade"
#~ msgstr "Cascade"

#: src/tables/build/BuildOrderTable.tsx:117
#~ msgid "Display recursive child orders"
#~ msgstr "Display recursive child orders"

#: src/tables/build/BuildOrderTable.tsx:121
#~ msgid "Show active orders"
#~ msgstr "Show active orders"

#: src/tables/build/BuildOrderTable.tsx:122
#~ msgid "Show overdue status"
#~ msgstr "Show overdue status"

#: src/tables/build/BuildOrderTable.tsx:127
#~ msgid "Show outstanding orders"
#~ msgstr "Show outstanding orders"

#: src/tables/build/BuildOrderTable.tsx:139
#: src/tables/purchasing/PurchaseOrderTable.tsx:71
#: src/tables/sales/ReturnOrderTable.tsx:62
#: src/tables/sales/SalesOrderTable.tsx:69
#~ msgid "Filter by whether the purchase order has a project code"
#~ msgstr "Filter by whether the purchase order has a project code"

#: src/tables/build/BuildOrderTable.tsx:143
#: src/tables/purchasing/PurchaseOrderTable.tsx:81
#: src/tables/sales/ReturnOrderTable.tsx:78
#: src/tables/sales/SalesOrderTable.tsx:79
msgid "Has Target Date"
msgstr ""

#: src/tables/build/BuildOrderTable.tsx:144
#: src/tables/purchasing/PurchaseOrderTable.tsx:82
#: src/tables/sales/ReturnOrderTable.tsx:79
#: src/tables/sales/SalesOrderTable.tsx:80
msgid "Show orders with a target date"
msgstr ""

#: src/tables/build/BuildOrderTable.tsx:149
#: src/tables/purchasing/PurchaseOrderTable.tsx:87
#: src/tables/sales/ReturnOrderTable.tsx:84
#: src/tables/sales/SalesOrderTable.tsx:85
msgid "Has Start Date"
msgstr ""

#: src/tables/build/BuildOrderTable.tsx:150
#: src/tables/purchasing/PurchaseOrderTable.tsx:88
#: src/tables/sales/ReturnOrderTable.tsx:85
#: src/tables/sales/SalesOrderTable.tsx:86
msgid "Show orders with a start date"
msgstr ""

#: src/tables/build/BuildOrderTable.tsx:179
#~ msgid "Filter by user who issued this order"
#~ msgstr "Filter by user who issued this order"

#: src/tables/build/BuildOrderTestTable.tsx:83
#: src/tables/build/BuildOrderTestTable.tsx:119
#: src/tables/stock/StockItemTestResultTable.tsx:279
#: src/tables/stock/StockItemTestResultTable.tsx:351
#: src/tables/stock/StockItemTestResultTable.tsx:412
msgid "Add Test Result"
msgstr ""

#: src/tables/build/BuildOrderTestTable.tsx:90
#: src/tables/stock/StockItemTestResultTable.tsx:281
msgid "Test result added"
msgstr ""

#: src/tables/build/BuildOrderTestTable.tsx:118
#: src/tables/stock/StockItemTestResultTable.tsx:181
msgid "No Result"
msgstr ""

#: src/tables/build/BuildOrderTestTable.tsx:228
msgid "Show build outputs currently in production"
msgstr ""

#: src/tables/build/BuildOutputTable.tsx:76
msgid "Build Output Stock Allocation"
msgstr "Asignación de existencias de salida de construcción"

#: src/tables/build/BuildOutputTable.tsx:161
#~ msgid "Delete build output"
#~ msgstr "Delete build output"

#: src/tables/build/BuildOutputTable.tsx:259
#: src/tables/build/BuildOutputTable.tsx:375
msgid "Add Build Output"
msgstr ""

#: src/tables/build/BuildOutputTable.tsx:304
#~ msgid "Edit build output"
#~ msgstr "Edit build output"

#: src/tables/build/BuildOutputTable.tsx:307
#: src/tables/build/BuildOutputTable.tsx:424
msgid "Edit Build Output"
msgstr "Editar salida de construcción"

#: src/tables/build/BuildOutputTable.tsx:322
msgid "This action will deallocate all stock from the selected build output"
msgstr "Esta acción desasignará todas las existencias de la salida de construcción seleccionada"

#: src/tables/build/BuildOutputTable.tsx:342
msgid "Complete selected outputs"
msgstr ""

#: src/tables/build/BuildOutputTable.tsx:353
msgid "Scrap selected outputs"
msgstr ""

#: src/tables/build/BuildOutputTable.tsx:364
msgid "Cancel selected outputs"
msgstr ""

#: src/tables/build/BuildOutputTable.tsx:386
msgid "View Build Output"
msgstr "Ver salida de construcción"

#: src/tables/build/BuildOutputTable.tsx:392
msgid "Allocate"
msgstr ""

#: src/tables/build/BuildOutputTable.tsx:393
msgid "Allocate stock to build output"
msgstr ""

#: src/tables/build/BuildOutputTable.tsx:403
msgid "Deallocate"
msgstr ""

#: src/tables/build/BuildOutputTable.tsx:404
msgid "Deallocate stock from build output"
msgstr ""

#: src/tables/build/BuildOutputTable.tsx:415
msgid "Complete build output"
msgstr ""

#: src/tables/build/BuildOutputTable.tsx:431
msgid "Scrap"
msgstr ""

#: src/tables/build/BuildOutputTable.tsx:432
msgid "Scrap build output"
msgstr ""

#: src/tables/build/BuildOutputTable.tsx:442
msgid "Cancel build output"
msgstr ""

#: src/tables/build/BuildOutputTable.tsx:495
msgid "Allocated Lines"
msgstr ""

#: src/tables/build/BuildOutputTable.tsx:510
msgid "Required Tests"
msgstr ""

#: src/tables/company/AddressTable.tsx:118
#: src/tables/company/AddressTable.tsx:183
msgid "Add Address"
msgstr "Añadir Dirección"

#: src/tables/company/AddressTable.tsx:123
msgid "Address created"
msgstr "Dirección creada"

#: src/tables/company/AddressTable.tsx:132
msgid "Edit Address"
msgstr "Editar Dirección"

#: src/tables/company/AddressTable.tsx:140
msgid "Delete Address"
msgstr "Eliminar Dirección"

#: src/tables/company/AddressTable.tsx:141
msgid "Are you sure you want to delete this address?"
msgstr "¿Estás seguro de que deseas eliminar esta dirección?"

#: src/tables/company/CompanyTable.tsx:71
#~ msgid "New Company"
#~ msgstr "New Company"

#: src/tables/company/CompanyTable.tsx:75
#: src/tables/company/CompanyTable.tsx:125
msgid "Add Company"
msgstr ""

#: src/tables/company/CompanyTable.tsx:97
msgid "Show active companies"
msgstr ""

#: src/tables/company/CompanyTable.tsx:102
msgid "Show companies which are suppliers"
msgstr ""

#: src/tables/company/CompanyTable.tsx:107
msgid "Show companies which are manufacturers"
msgstr ""

#: src/tables/company/CompanyTable.tsx:112
msgid "Show companies which are customers"
msgstr ""

#: src/tables/company/ContactTable.tsx:95
msgid "Edit Contact"
msgstr "Editar Contacto"

#: src/tables/company/ContactTable.tsx:102
msgid "Add Contact"
msgstr ""

#: src/tables/company/ContactTable.tsx:113
msgid "Delete Contact"
msgstr "Eliminar Contacto"

#: src/tables/company/ContactTable.tsx:154
msgid "Add contact"
msgstr "Agregar contacto"

#: src/tables/general/AttachmentTable.tsx:104
msgid "Uploading file {filename}"
msgstr ""

#: src/tables/general/AttachmentTable.tsx:139
#~ msgid "File uploaded"
#~ msgstr "File uploaded"

#: src/tables/general/AttachmentTable.tsx:140
#~ msgid "File {0} uploaded successfully"
#~ msgstr "File {0} uploaded successfully"

#: src/tables/general/AttachmentTable.tsx:156
#: src/tables/general/AttachmentTable.tsx:170
msgid "Uploading File"
msgstr ""

#: src/tables/general/AttachmentTable.tsx:181
msgid "File Uploaded"
msgstr ""

#: src/tables/general/AttachmentTable.tsx:182
msgid "File {name} uploaded successfully"
msgstr ""

#: src/tables/general/AttachmentTable.tsx:198
msgid "File could not be uploaded"
msgstr ""

#: src/tables/general/AttachmentTable.tsx:249
msgid "Upload Attachment"
msgstr ""

#: src/tables/general/AttachmentTable.tsx:254
#~ msgid "Upload attachment"
#~ msgstr "Upload attachment"

#: src/tables/general/AttachmentTable.tsx:259
msgid "Edit Attachment"
msgstr ""

#: src/tables/general/AttachmentTable.tsx:273
msgid "Delete Attachment"
msgstr ""

#: src/tables/general/AttachmentTable.tsx:283
msgid "Is Link"
msgstr ""

#: src/tables/general/AttachmentTable.tsx:284
msgid "Show link attachments"
msgstr ""

#: src/tables/general/AttachmentTable.tsx:288
msgid "Is File"
msgstr ""

#: src/tables/general/AttachmentTable.tsx:289
msgid "Show file attachments"
msgstr ""

#: src/tables/general/AttachmentTable.tsx:298
msgid "Add attachment"
msgstr ""

#: src/tables/general/AttachmentTable.tsx:309
msgid "Add external link"
msgstr ""

#: src/tables/general/AttachmentTable.tsx:357
msgid "No attachments found"
msgstr ""

#: src/tables/general/AttachmentTable.tsx:396
msgid "Drag attachment file here to upload"
msgstr ""

#: src/tables/general/BarcodeScanTable.tsx:35
msgid "Item"
msgstr "Artículo"

#: src/tables/general/BarcodeScanTable.tsx:50
msgid "Model"
msgstr ""

#: src/tables/general/BarcodeScanTable.tsx:60
#: src/tables/settings/BarcodeScanHistoryTable.tsx:74
#: src/tables/settings/ErrorTable.tsx:59
msgid "Timestamp"
msgstr "Fecha y hora"

#: src/tables/general/BarcodeScanTable.tsx:75
msgid "View Item"
msgstr ""

#: src/tables/general/ExtraLineItemTable.tsx:86
#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:268
#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:367
#: src/tables/sales/ReturnOrderLineItemTable.tsx:72
#: src/tables/sales/ReturnOrderLineItemTable.tsx:176
#: src/tables/sales/SalesOrderLineItemTable.tsx:225
#: src/tables/sales/SalesOrderLineItemTable.tsx:321
msgid "Add Line Item"
msgstr ""

#: src/tables/general/ExtraLineItemTable.tsx:98
#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:288
#: src/tables/sales/ReturnOrderLineItemTable.tsx:84
#: src/tables/sales/SalesOrderLineItemTable.tsx:243
msgid "Edit Line Item"
msgstr ""

#: src/tables/general/ExtraLineItemTable.tsx:106
#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:296
#: src/tables/sales/ReturnOrderLineItemTable.tsx:92
#: src/tables/sales/SalesOrderLineItemTable.tsx:251
msgid "Delete Line Item"
msgstr ""

#: src/tables/general/ExtraLineItemTable.tsx:143
msgid "Add Extra Line Item"
msgstr ""

#: src/tables/machine/MachineListTable.tsx:205
msgid "Machine restarted"
msgstr ""

#: src/tables/machine/MachineListTable.tsx:215
#: src/tables/machine/MachineListTable.tsx:263
msgid "Edit machine"
msgstr ""

#: src/tables/machine/MachineListTable.tsx:229
#: src/tables/machine/MachineListTable.tsx:267
msgid "Delete machine"
msgstr ""

#: src/tables/machine/MachineListTable.tsx:230
msgid "Machine successfully deleted."
msgstr ""

#. placeholder {0}: machine?.name ?? 'unknown'
#: src/tables/machine/MachineListTable.tsx:234
msgid "Are you sure you want to remove the machine \"{0}\"?"
msgstr ""

#: src/tables/machine/MachineListTable.tsx:251
msgid "Machine"
msgstr ""

#: src/tables/machine/MachineListTable.tsx:256
#: src/tables/machine/MachineListTable.tsx:443
msgid "Restart required"
msgstr ""

#: src/tables/machine/MachineListTable.tsx:260
msgid "Machine Actions"
msgstr ""

#: src/tables/machine/MachineListTable.tsx:272
msgid "Restart"
msgstr ""

#: src/tables/machine/MachineListTable.tsx:274
msgid "Restart machine"
msgstr ""

#: src/tables/machine/MachineListTable.tsx:276
msgid "manual restart required"
msgstr ""

#: src/tables/machine/MachineListTable.tsx:291
#~ msgid "Machine information"
#~ msgstr "Machine information"

#: src/tables/machine/MachineListTable.tsx:294
msgid "Machine Information"
msgstr "Información de la máquina"

#: src/tables/machine/MachineListTable.tsx:304
#: src/tables/machine/MachineListTable.tsx:610
msgid "Machine Type"
msgstr ""

#: src/tables/machine/MachineListTable.tsx:317
msgid "Machine Driver"
msgstr ""

#: src/tables/machine/MachineListTable.tsx:332
msgid "Initialized"
msgstr ""

#: src/tables/machine/MachineListTable.tsx:361
#: src/tables/machine/MachineTypeTable.tsx:291
msgid "No errors reported"
msgstr ""

#: src/tables/machine/MachineListTable.tsx:380
msgid "Machine Settings"
msgstr ""

#: src/tables/machine/MachineListTable.tsx:396
msgid "Driver Settings"
msgstr ""

#: src/tables/machine/MachineListTable.tsx:494
#~ msgid "Create machine"
#~ msgstr "Create machine"

#: src/tables/machine/MachineListTable.tsx:516
msgid "Add Machine"
msgstr ""

#: src/tables/machine/MachineListTable.tsx:558
msgid "Add machine"
msgstr ""

#: src/tables/machine/MachineListTable.tsx:561
#~ msgid "Machine detail"
#~ msgstr "Machine detail"

#: src/tables/machine/MachineListTable.tsx:572
msgid "Machine Detail"
msgstr "Detalle de máquina"

#: src/tables/machine/MachineListTable.tsx:619
msgid "Driver"
msgstr ""

#: src/tables/machine/MachineTypeTable.tsx:79
msgid "Builtin driver"
msgstr ""

#: src/tables/machine/MachineTypeTable.tsx:97
msgid "Not Found"
msgstr "No Encontrado"

#: src/tables/machine/MachineTypeTable.tsx:99
#~ msgid "Machine type information"
#~ msgstr "Machine type information"

#: src/tables/machine/MachineTypeTable.tsx:100
msgid "Machine type not found."
msgstr ""

#: src/tables/machine/MachineTypeTable.tsx:110
msgid "Machine Type Information"
msgstr "Información del tipo de máquina"

#: src/tables/machine/MachineTypeTable.tsx:125
#: src/tables/machine/MachineTypeTable.tsx:239
msgid "Slug"
msgstr ""

#: src/tables/machine/MachineTypeTable.tsx:136
#: src/tables/machine/MachineTypeTable.tsx:260
msgid "Provider plugin"
msgstr ""

#: src/tables/machine/MachineTypeTable.tsx:148
#: src/tables/machine/MachineTypeTable.tsx:272
msgid "Provider file"
msgstr ""

#: src/tables/machine/MachineTypeTable.tsx:148
#~ msgid "Available drivers"
#~ msgstr "Available drivers"

#: src/tables/machine/MachineTypeTable.tsx:163
msgid "Available Drivers"
msgstr "Controladores Disponibles"

#: src/tables/machine/MachineTypeTable.tsx:218
msgid "Machine driver not found."
msgstr ""

#: src/tables/machine/MachineTypeTable.tsx:226
msgid "Machine driver information"
msgstr ""

#: src/tables/machine/MachineTypeTable.tsx:246
msgid "Machine type"
msgstr ""

#: src/tables/machine/MachineTypeTable.tsx:338
#~ msgid "Machine type detail"
#~ msgstr "Machine type detail"

#: src/tables/machine/MachineTypeTable.tsx:348
#~ msgid "Machine driver detail"
#~ msgstr "Machine driver detail"

#: src/tables/machine/MachineTypeTable.tsx:349
msgid "Builtin type"
msgstr ""

#: src/tables/machine/MachineTypeTable.tsx:358
msgid "Machine Type Detail"
msgstr "Detalle del tipo de máquina"

#: src/tables/machine/MachineTypeTable.tsx:368
msgid "Machine Driver Detail"
msgstr "Detalle del controlador de la máquina"

#: src/tables/notifications/NotificationTable.tsx:26
msgid "Age"
msgstr ""

#: src/tables/notifications/NotificationTable.tsx:37
msgid "Notification"
msgstr "Notificación"

#: src/tables/notifications/NotificationTable.tsx:41
#: src/tables/plugin/PluginErrorTable.tsx:37
#: src/tables/settings/ErrorTable.tsx:50
msgid "Message"
msgstr ""

#: src/tables/part/ParametricPartTable.tsx:74
msgid "Click to edit"
msgstr ""

#: src/tables/part/ParametricPartTable.tsx:82
#~ msgid "Edit parameter"
#~ msgstr "Edit parameter"

#: src/tables/part/ParametricPartTable.tsx:128
msgid "Add Part Parameter"
msgstr ""

#: src/tables/part/ParametricPartTable.tsx:142
#: src/tables/part/PartParameterTable.tsx:130
#: src/tables/part/PartParameterTable.tsx:153
msgid "Edit Part Parameter"
msgstr ""

#: src/tables/part/ParametricPartTable.tsx:225
msgid "Show active parts"
msgstr ""

#: src/tables/part/ParametricPartTable.tsx:230
msgid "Show locked parts"
msgstr ""

#: src/tables/part/ParametricPartTable.tsx:235
msgid "Show assembly parts"
msgstr ""

#: src/tables/part/PartBuildAllocationsTable.tsx:67
#: src/tables/part/PartSalesAllocationsTable.tsx:62
msgid "Required Stock"
msgstr "Stock requerido"

#: src/tables/part/PartBuildAllocationsTable.tsx:83
msgid "View Build Order"
msgstr "Ver orden de construcción"

#: src/tables/part/PartCategoryTable.tsx:51
msgid "You are subscribed to notifications for this category"
msgstr "Estás suscrito a las notificaciones de esta categoría"

#: src/tables/part/PartCategoryTable.tsx:83
#: src/tables/part/PartTable.tsx:199
msgid "Include Subcategories"
msgstr ""

#: src/tables/part/PartCategoryTable.tsx:84
msgid "Include subcategories in results"
msgstr ""

#: src/tables/part/PartCategoryTable.tsx:89
msgid "Show structural categories"
msgstr ""

#: src/tables/part/PartCategoryTable.tsx:94
msgid "Show categories to which the user is subscribed"
msgstr ""

#: src/tables/part/PartCategoryTable.tsx:103
msgid "New Part Category"
msgstr ""

#: src/tables/part/PartCategoryTable.tsx:129
msgid "Set Parent Category"
msgstr ""

#: src/tables/part/PartCategoryTable.tsx:147
#: src/tables/stock/StockLocationTable.tsx:145
msgid "Set Parent"
msgstr ""

#: src/tables/part/PartCategoryTable.tsx:149
msgid "Set parent category for the selected items"
msgstr ""

#: src/tables/part/PartCategoryTable.tsx:160
msgid "Add Part Category"
msgstr ""

#: src/tables/part/PartCategoryTemplateTable.tsx:38
#: src/tables/part/PartCategoryTemplateTable.tsx:132
msgid "Add Category Parameter"
msgstr ""

#: src/tables/part/PartCategoryTemplateTable.tsx:46
msgid "Edit Category Parameter"
msgstr ""

#: src/tables/part/PartCategoryTemplateTable.tsx:54
msgid "Delete Category Parameter"
msgstr ""

#: src/tables/part/PartCategoryTemplateTable.tsx:76
msgid "Parameter Template"
msgstr ""

#: src/tables/part/PartCategoryTemplateTable.tsx:93
#~ msgid "[{0}]"
#~ msgstr "[{0}]"

#: src/tables/part/PartParameterTable.tsx:97
msgid "Internal Units"
msgstr ""

#: src/tables/part/PartParameterTable.tsx:114
msgid "New Part Parameter"
msgstr ""

#: src/tables/part/PartParameterTable.tsx:139
#: src/tables/part/PartParameterTable.tsx:161
msgid "Delete Part Parameter"
msgstr ""

#: src/tables/part/PartParameterTable.tsx:179
msgid "Add parameter"
msgstr ""

#: src/tables/part/PartParameterTable.tsx:198
msgid "Part parameters cannot be edited, as the part is locked"
msgstr ""

#: src/tables/part/PartParameterTemplateTable.tsx:31
msgid "Checkbox"
msgstr ""

#: src/tables/part/PartParameterTemplateTable.tsx:32
msgid "Show checkbox templates"
msgstr ""

#: src/tables/part/PartParameterTemplateTable.tsx:36
msgid "Has choices"
msgstr "Tiene opciones"

#: src/tables/part/PartParameterTemplateTable.tsx:37
msgid "Show templates with choices"
msgstr "Mostrar plantillas con opciones"

#: src/tables/part/PartParameterTemplateTable.tsx:41
#: src/tables/part/PartTable.tsx:223
msgid "Has Units"
msgstr "Tiene Unidades"

#: src/tables/part/PartParameterTemplateTable.tsx:42
msgid "Show templates with units"
msgstr "Mostrar plantillas con unidades"

#: src/tables/part/PartParameterTemplateTable.tsx:86
#: src/tables/part/PartParameterTemplateTable.tsx:143
msgid "Add Parameter Template"
msgstr ""

#: src/tables/part/PartParameterTemplateTable.tsx:101
msgid "Edit Parameter Template"
msgstr ""

#: src/tables/part/PartParameterTemplateTable.tsx:112
msgid "Delete Parameter Template"
msgstr ""

#: src/tables/part/PartParameterTemplateTable.tsx:141
#~ msgid "Add parameter template"
#~ msgstr "Add parameter template"

#: src/tables/part/PartPurchaseOrdersTable.tsx:78
#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:169
msgid "Total Quantity"
msgstr ""

#: src/tables/part/PartPurchaseOrdersTable.tsx:122
msgid "Show pending orders"
msgstr ""

#: src/tables/part/PartPurchaseOrdersTable.tsx:127
msgid "Show received items"
msgstr ""

#: src/tables/part/PartSalesAllocationsTable.tsx:78
msgid "View Sales Order"
msgstr "Ver orden de venta"

#: src/tables/part/PartTable.tsx:84
msgid "Minimum stock"
msgstr "Stock mínimo"

#: src/tables/part/PartTable.tsx:182
msgid "Filter by part active status"
msgstr ""

#: src/tables/part/PartTable.tsx:188
msgid "Filter by part locked status"
msgstr ""

#: src/tables/part/PartTable.tsx:194
msgid "Filter by assembly attribute"
msgstr ""

#: src/tables/part/PartTable.tsx:200
msgid "Include parts in subcategories"
msgstr ""

#: src/tables/part/PartTable.tsx:206
msgid "Filter by component attribute"
msgstr ""

#: src/tables/part/PartTable.tsx:212
msgid "Filter by testable attribute"
msgstr ""

#: src/tables/part/PartTable.tsx:218
msgid "Filter by trackable attribute"
msgstr ""

#: src/tables/part/PartTable.tsx:224
msgid "Filter by parts which have units"
msgstr ""

#: src/tables/part/PartTable.tsx:229
msgid "Has IPN"
msgstr "Tiene IPN"

#: src/tables/part/PartTable.tsx:230
msgid "Filter by parts which have an internal part number"
msgstr ""

#: src/tables/part/PartTable.tsx:235
msgid "Has Stock"
msgstr ""

#: src/tables/part/PartTable.tsx:236
msgid "Filter by parts which have stock"
msgstr ""

#: src/tables/part/PartTable.tsx:242
msgid "Filter by parts which have low stock"
msgstr ""

#: src/tables/part/PartTable.tsx:247
msgid "Purchaseable"
msgstr ""

#: src/tables/part/PartTable.tsx:248
msgid "Filter by parts which are purchaseable"
msgstr ""

#: src/tables/part/PartTable.tsx:253
msgid "Salable"
msgstr ""

#: src/tables/part/PartTable.tsx:254
msgid "Filter by parts which are salable"
msgstr ""

#: src/tables/part/PartTable.tsx:259
#: src/tables/part/PartTable.tsx:263
#: src/tables/part/PartVariantTable.tsx:25
msgid "Virtual"
msgstr "Virtual"

#: src/tables/part/PartTable.tsx:260
msgid "Filter by parts which are virtual"
msgstr ""

#: src/tables/part/PartTable.tsx:264
msgid "Not Virtual"
msgstr ""

#: src/tables/part/PartTable.tsx:269
msgid "Is Template"
msgstr ""

#: src/tables/part/PartTable.tsx:270
msgid "Filter by parts which are templates"
msgstr ""

#: src/tables/part/PartTable.tsx:275
msgid "Is Variant"
msgstr ""

#: src/tables/part/PartTable.tsx:276
msgid "Filter by parts which are variants"
msgstr ""

#: src/tables/part/PartTable.tsx:281
msgid "Is Revision"
msgstr ""

#: src/tables/part/PartTable.tsx:282
msgid "Filter by parts which are revisions"
msgstr ""

#: src/tables/part/PartTable.tsx:286
msgid "Has Revisions"
msgstr ""

#: src/tables/part/PartTable.tsx:287
msgid "Filter by parts which have revisions"
msgstr ""

#: src/tables/part/PartTable.tsx:292
msgid "Filter by parts which have pricing information"
msgstr ""

#: src/tables/part/PartTable.tsx:298
msgid "Filter by parts which have available stock"
msgstr ""

#: src/tables/part/PartTable.tsx:304
msgid "Filter by parts to which the user is subscribed"
msgstr ""

#: src/tables/part/PartTable.tsx:309
msgid "Has Stocktake"
msgstr ""

#: src/tables/part/PartTable.tsx:310
msgid "Filter by parts which have stocktake information"
msgstr ""

#: src/tables/part/PartTable.tsx:350
#: src/tables/part/PartTable.tsx:367
msgid "Set Category"
msgstr ""

#: src/tables/part/PartTable.tsx:369
msgid "Set category for selected parts"
msgstr ""

#: src/tables/part/PartTable.tsx:379
msgid "Order selected parts"
msgstr "Ordenar partes seleccionadas"

#: src/tables/part/PartTestTemplateTable.tsx:56
msgid "Test is defined for a parent template part"
msgstr ""

#: src/tables/part/PartTestTemplateTable.tsx:70
msgid "Template Details"
msgstr ""

#: src/tables/part/PartTestTemplateTable.tsx:80
msgid "Results"
msgstr "Resultados"

#: src/tables/part/PartTestTemplateTable.tsx:113
msgid "Show required tests"
msgstr "Mostrar pruebas requeridas"

#: src/tables/part/PartTestTemplateTable.tsx:118
msgid "Show enabled tests"
msgstr "Mostrar pruebas habilitadas"

#: src/tables/part/PartTestTemplateTable.tsx:122
msgid "Requires Value"
msgstr ""

#: src/tables/part/PartTestTemplateTable.tsx:123
msgid "Show tests that require a value"
msgstr ""

#: src/tables/part/PartTestTemplateTable.tsx:127
msgid "Requires Attachment"
msgstr ""

#: src/tables/part/PartTestTemplateTable.tsx:128
msgid "Show tests that require an attachment"
msgstr ""

#: src/tables/part/PartTestTemplateTable.tsx:132
msgid "Include Inherited"
msgstr ""

#: src/tables/part/PartTestTemplateTable.tsx:133
msgid "Show tests from inherited templates"
msgstr ""

#: src/tables/part/PartTestTemplateTable.tsx:137
msgid "Has Results"
msgstr ""

#: src/tables/part/PartTestTemplateTable.tsx:138
msgid "Show tests which have recorded results"
msgstr ""

#: src/tables/part/PartTestTemplateTable.tsx:160
#: src/tables/part/PartTestTemplateTable.tsx:243
msgid "Add Test Template"
msgstr ""

#: src/tables/part/PartTestTemplateTable.tsx:176
msgid "Edit Test Template"
msgstr ""

#: src/tables/part/PartTestTemplateTable.tsx:187
msgid "Delete Test Template"
msgstr ""

#: src/tables/part/PartTestTemplateTable.tsx:189
msgid "This action cannot be reversed"
msgstr "Esta acción no puede ser revertida"

#: src/tables/part/PartTestTemplateTable.tsx:191
msgid "Any tests results associated with this template will be deleted"
msgstr "Cualquier resultado de prueba asociado a esta plantilla será eliminado"

#: src/tables/part/PartTestTemplateTable.tsx:209
msgid "View Parent Part"
msgstr ""

#: src/tables/part/PartTestTemplateTable.tsx:263
msgid "Part templates cannot be edited, as the part is locked"
msgstr ""

#: src/tables/part/PartThumbTable.tsx:224
msgid "Select"
msgstr ""

#: src/tables/part/PartVariantTable.tsx:16
msgid "Show active variants"
msgstr ""

#: src/tables/part/PartVariantTable.tsx:20
msgid "Template"
msgstr ""

#: src/tables/part/PartVariantTable.tsx:21
msgid "Show template variants"
msgstr ""

#: src/tables/part/PartVariantTable.tsx:26
msgid "Show virtual variants"
msgstr ""

#: src/tables/part/PartVariantTable.tsx:31
msgid "Show trackable variants"
msgstr ""

#: src/tables/part/RelatedPartTable.tsx:100
#: src/tables/part/RelatedPartTable.tsx:133
msgid "Add Related Part"
msgstr ""

#: src/tables/part/RelatedPartTable.tsx:109
#~ msgid "Add related part"
#~ msgstr "Add related part"

#: src/tables/part/RelatedPartTable.tsx:115
msgid "Delete Related Part"
msgstr ""

#: src/tables/part/RelatedPartTable.tsx:122
msgid "Edit Related Part"
msgstr "Editar pieza relacionada"

#: src/tables/part/SelectionListTable.tsx:61
#: src/tables/part/SelectionListTable.tsx:112
msgid "Add Selection List"
msgstr "Añadir lista de selección"

#: src/tables/part/SelectionListTable.tsx:73
msgid "Edit Selection List"
msgstr "Editar lista de selección"

#: src/tables/part/SelectionListTable.tsx:81
msgid "Delete Selection List"
msgstr "Eliminar lista de selección"

#: src/tables/plugin/PluginErrorTable.tsx:29
msgid "Stage"
msgstr "Etapa"

#: src/tables/plugin/PluginListTable.tsx:42
msgid "Plugin is active"
msgstr ""

#: src/tables/plugin/PluginListTable.tsx:48
msgid "Plugin is inactive"
msgstr ""

#: src/tables/plugin/PluginListTable.tsx:55
msgid "Plugin is not installed"
msgstr ""

#: src/tables/plugin/PluginListTable.tsx:77
#: src/tables/settings/ExportSessionTable.tsx:29
msgid "Plugin"
msgstr ""

#: src/tables/plugin/PluginListTable.tsx:95
#~ msgid "Plugin with key {pluginKey} not found"
#~ msgstr "Plugin with key {pluginKey} not found"

#: src/tables/plugin/PluginListTable.tsx:97
#~ msgid "An error occurred while fetching plugin details"
#~ msgstr "An error occurred while fetching plugin details"

#: src/tables/plugin/PluginListTable.tsx:105
#: src/tables/plugin/PluginListTable.tsx:421
msgid "Mandatory"
msgstr ""

#: src/tables/plugin/PluginListTable.tsx:113
#~ msgid "Plugin with id {id} not found"
#~ msgstr "Plugin with id {id} not found"

#: src/tables/plugin/PluginListTable.tsx:119
msgid "Description not available"
msgstr ""

#: src/tables/plugin/PluginListTable.tsx:122
#~ msgid "Plugin information"
#~ msgstr "Plugin information"

#: src/tables/plugin/PluginListTable.tsx:134
#~ msgid "Plugin Actions"
#~ msgstr "Plugin Actions"

#: src/tables/plugin/PluginListTable.tsx:138
#: src/tables/plugin/PluginListTable.tsx:141
#~ msgid "Edit plugin"
#~ msgstr "Edit plugin"

#: src/tables/plugin/PluginListTable.tsx:152
msgid "Confirm plugin activation"
msgstr ""

#: src/tables/plugin/PluginListTable.tsx:152
#: src/tables/plugin/PluginListTable.tsx:153
#~ msgid "Reload"
#~ msgstr "Reload"

#: src/tables/plugin/PluginListTable.tsx:153
msgid "Confirm plugin deactivation"
msgstr ""

#: src/tables/plugin/PluginListTable.tsx:158
msgid "The selected plugin will be activated"
msgstr ""

#: src/tables/plugin/PluginListTable.tsx:159
msgid "The selected plugin will be deactivated"
msgstr ""

#: src/tables/plugin/PluginListTable.tsx:163
#~ msgid "Package information"
#~ msgstr "Package information"

#: src/tables/plugin/PluginListTable.tsx:177
msgid "Deactivate"
msgstr ""

#: src/tables/plugin/PluginListTable.tsx:191
msgid "Activate"
msgstr ""

#: src/tables/plugin/PluginListTable.tsx:192
msgid "Activate selected plugin"
msgstr "Activar el complemento seleccionado"

#: src/tables/plugin/PluginListTable.tsx:197
#~ msgid "Plugin settings"
#~ msgstr "Plugin settings"

#: src/tables/plugin/PluginListTable.tsx:204
msgid "Update selected plugin"
msgstr "Actualizar complemento seleccionado"

#: src/tables/plugin/PluginListTable.tsx:223
#: src/tables/stock/InstalledItemsTable.tsx:108
msgid "Uninstall"
msgstr ""

#: src/tables/plugin/PluginListTable.tsx:224
msgid "Uninstall selected plugin"
msgstr "Desinstalar el complemento seleccionado"

#: src/tables/plugin/PluginListTable.tsx:243
msgid "Delete selected plugin configuration"
msgstr "Eliminar la configuración del complemento seleccionado"

#: src/tables/plugin/PluginListTable.tsx:259
msgid "Activate Plugin"
msgstr ""

#: src/tables/plugin/PluginListTable.tsx:266
msgid "The plugin was activated"
msgstr "El complemento fue activado"

#: src/tables/plugin/PluginListTable.tsx:267
msgid "The plugin was deactivated"
msgstr "El complemento fue desactivado"

#: src/tables/plugin/PluginListTable.tsx:280
msgid "Install plugin"
msgstr ""

#: src/tables/plugin/PluginListTable.tsx:293
msgid "Install"
msgstr ""

#: src/tables/plugin/PluginListTable.tsx:294
msgid "Plugin installed successfully"
msgstr ""

#: src/tables/plugin/PluginListTable.tsx:299
msgid "Uninstall Plugin"
msgstr ""

#: src/tables/plugin/PluginListTable.tsx:308
#~ msgid "This action cannot be undone."
#~ msgstr "This action cannot be undone."

#: src/tables/plugin/PluginListTable.tsx:311
msgid "Confirm plugin uninstall"
msgstr ""

#: src/tables/plugin/PluginListTable.tsx:314
msgid "The selected plugin will be uninstalled."
msgstr ""

#: src/tables/plugin/PluginListTable.tsx:319
msgid "Plugin uninstalled successfully"
msgstr ""

#: src/tables/plugin/PluginListTable.tsx:327
msgid "Delete Plugin"
msgstr ""

#: src/tables/plugin/PluginListTable.tsx:328
msgid "Deleting this plugin configuration will remove all associated settings and data. Are you sure you want to delete this plugin?"
msgstr ""

#: src/tables/plugin/PluginListTable.tsx:338
#~ msgid "Deactivate Plugin"
#~ msgstr "Deactivate Plugin"

#: src/tables/plugin/PluginListTable.tsx:341
msgid "Plugins reloaded"
msgstr ""

#: src/tables/plugin/PluginListTable.tsx:342
msgid "Plugins were reloaded successfully"
msgstr ""

#: src/tables/plugin/PluginListTable.tsx:354
#~ msgid "The following plugin will be activated"
#~ msgstr "The following plugin will be activated"

#: src/tables/plugin/PluginListTable.tsx:355
#~ msgid "The following plugin will be deactivated"
#~ msgstr "The following plugin will be deactivated"

#: src/tables/plugin/PluginListTable.tsx:360
msgid "Reload Plugins"
msgstr ""

#: src/tables/plugin/PluginListTable.tsx:367
msgid "Install Plugin"
msgstr ""

#: src/tables/plugin/PluginListTable.tsx:376
#~ msgid "Activating plugin"
#~ msgstr "Activating plugin"

#: src/tables/plugin/PluginListTable.tsx:376
#~ msgid "Deactivating plugin"
#~ msgstr "Deactivating plugin"

#: src/tables/plugin/PluginListTable.tsx:384
msgid "Plugin Detail"
msgstr ""

#: src/tables/plugin/PluginListTable.tsx:392
#~ msgid "Plugin updated"
#~ msgstr "Plugin updated"

#: src/tables/plugin/PluginListTable.tsx:403
#~ msgid "Error updating plugin"
#~ msgstr "Error updating plugin"

#: src/tables/plugin/PluginListTable.tsx:426
msgid "Sample"
msgstr ""

#: src/tables/plugin/PluginListTable.tsx:431
#: src/tables/stock/StockItemTable.tsx:362
msgid "Installed"
msgstr ""

#: src/tables/plugin/PluginListTable.tsx:615
#~ msgid "Plugin detail"
#~ msgstr "Plugin detail"

#: src/tables/purchasing/ManufacturerPartParameterTable.tsx:59
#: src/tables/purchasing/ManufacturerPartParameterTable.tsx:108
msgid "Add Parameter"
msgstr ""

#: src/tables/purchasing/ManufacturerPartParameterTable.tsx:60
#~ msgid "Parameter updated"
#~ msgstr "Parameter updated"

#: src/tables/purchasing/ManufacturerPartParameterTable.tsx:70
msgid "Edit Parameter"
msgstr ""

#: src/tables/purchasing/ManufacturerPartParameterTable.tsx:73
#~ msgid "Parameter deleted"
#~ msgstr "Parameter deleted"

#: src/tables/purchasing/ManufacturerPartParameterTable.tsx:74
#~ msgid "Are you sure you want to delete this parameter?"
#~ msgstr "Are you sure you want to delete this parameter?"

#: src/tables/purchasing/ManufacturerPartParameterTable.tsx:78
msgid "Delete Parameter"
msgstr ""

#: src/tables/purchasing/ManufacturerPartTable.tsx:63
#~ msgid "Create Manufacturer Part"
#~ msgstr "Create Manufacturer Part"

#: src/tables/purchasing/ManufacturerPartTable.tsx:100
#~ msgid "Manufacturer part updated"
#~ msgstr "Manufacturer part updated"

#: src/tables/purchasing/ManufacturerPartTable.tsx:112
#~ msgid "Manufacturer part deleted"
#~ msgstr "Manufacturer part deleted"

#: src/tables/purchasing/ManufacturerPartTable.tsx:114
#~ msgid "Are you sure you want to remove this manufacturer part?"
#~ msgstr "Are you sure you want to remove this manufacturer part?"

#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:102
#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:361
msgid "Import Line Items"
msgstr ""

#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:208
msgid "Supplier Code"
msgstr ""

#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:215
msgid "Supplier Link"
msgstr ""

#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:221
msgid "Manufacturer Code"
msgstr ""

#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:253
msgid "Show line items which have been received"
msgstr "Mostrar partidas que han sido recibidas"

#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:321
msgid "Receive line item"
msgstr ""

#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:344
#: src/tables/sales/ReturnOrderLineItemTable.tsx:160
#: src/tables/sales/SalesOrderLineItemTable.tsx:258
#~ msgid "Add line item"
#~ msgstr "Add line item"

#: src/tables/purchasing/PurchaseOrderLineItemTable.tsx:378
msgid "Receive items"
msgstr "Recibir artículos"

#: src/tables/purchasing/SupplierPartTable.tsx:96
msgid "MPN"
msgstr ""

#: src/tables/purchasing/SupplierPartTable.tsx:125
msgid "Base units"
msgstr "Unidades base"

#: src/tables/purchasing/SupplierPartTable.tsx:182
msgid "Add supplier part"
msgstr ""

#: src/tables/purchasing/SupplierPartTable.tsx:193
#~ msgid "Supplier part updated"
#~ msgstr "Supplier part updated"

#: src/tables/purchasing/SupplierPartTable.tsx:194
msgid "Show active supplier parts"
msgstr ""

#: src/tables/purchasing/SupplierPartTable.tsx:198
msgid "Active Part"
msgstr ""

#: src/tables/purchasing/SupplierPartTable.tsx:199
msgid "Show active internal parts"
msgstr ""

#: src/tables/purchasing/SupplierPartTable.tsx:203
msgid "Active Supplier"
msgstr ""

#: src/tables/purchasing/SupplierPartTable.tsx:204
msgid "Show active suppliers"
msgstr ""

#: src/tables/purchasing/SupplierPartTable.tsx:205
#~ msgid "Supplier part deleted"
#~ msgstr "Supplier part deleted"

#: src/tables/purchasing/SupplierPartTable.tsx:207
#~ msgid "Are you sure you want to remove this supplier part?"
#~ msgstr "Are you sure you want to remove this supplier part?"

#: src/tables/purchasing/SupplierPartTable.tsx:209
msgid "Show supplier parts with stock"
msgstr "Mostrar piezas de proveedor con stock"

#: src/tables/sales/ReturnOrderLineItemTable.tsx:147
msgid "Received Date"
msgstr ""

#: src/tables/sales/ReturnOrderLineItemTable.tsx:161
msgid "Show items which have been received"
msgstr ""

#: src/tables/sales/ReturnOrderLineItemTable.tsx:166
msgid "Filter by line item status"
msgstr ""

#: src/tables/sales/ReturnOrderLineItemTable.tsx:184
msgid "Receive selected items"
msgstr "Recibir elementos seleccionados"

#: src/tables/sales/ReturnOrderLineItemTable.tsx:216
msgid "Receive Item"
msgstr ""

#: src/tables/sales/SalesOrderAllocationTable.tsx:78
msgid "Show outstanding allocations"
msgstr "Mostrar asignaciones destacadas"

#: src/tables/sales/SalesOrderAllocationTable.tsx:82
msgid "Assigned to Shipment"
msgstr "Asignado al envío"

#: src/tables/sales/SalesOrderAllocationTable.tsx:83
msgid "Show allocations assigned to a shipment"
msgstr "Mostrar asignaciones asignadas a un envío"

#: src/tables/sales/SalesOrderAllocationTable.tsx:176
#: src/tables/sales/SalesOrderAllocationTable.tsx:190
msgid "No shipment"
msgstr "Sin envío"

#: src/tables/sales/SalesOrderAllocationTable.tsx:188
msgid "Not shipped"
msgstr "No enviado"

#: src/tables/sales/SalesOrderAllocationTable.tsx:210
#: src/tables/sales/SalesOrderAllocationTable.tsx:232
msgid "Edit Allocation"
msgstr "Editar Asignación"

#: src/tables/sales/SalesOrderAllocationTable.tsx:217
#: src/tables/sales/SalesOrderAllocationTable.tsx:240
msgid "Delete Allocation"
msgstr "Eliminar asignación"

#: src/tables/sales/SalesOrderAllocationTable.tsx:264
msgid "Assign to Shipment"
msgstr ""

#: src/tables/sales/SalesOrderAllocationTable.tsx:279
msgid "Assign to shipment"
msgstr ""

#: src/tables/sales/SalesOrderLineItemTable.tsx:263
msgid "Allocate Serial Numbers"
msgstr "Asignar Números de Serie"

#: src/tables/sales/SalesOrderLineItemTable.tsx:280
#~ msgid "Allocate stock"
#~ msgstr "Allocate stock"

#: src/tables/sales/SalesOrderLineItemTable.tsx:291
#~ msgid "Allocate Serials"
#~ msgstr "Allocate Serials"

#: src/tables/sales/SalesOrderLineItemTable.tsx:307
msgid "Show lines which are fully allocated"
msgstr "Mostrar líneas completamente asignadas"

#: src/tables/sales/SalesOrderLineItemTable.tsx:312
msgid "Show lines which are completed"
msgstr "Mostrar líneas que están completadas"

#: src/tables/sales/SalesOrderLineItemTable.tsx:389
msgid "Allocate serials"
msgstr "Asignar seriales"

#: src/tables/sales/SalesOrderLineItemTable.tsx:406
msgid "Build stock"
msgstr ""

#: src/tables/sales/SalesOrderLineItemTable.tsx:423
#: src/tables/stock/StockItemTable.tsx:631
msgid "Order stock"
msgstr ""

#: src/tables/sales/SalesOrderShipmentTable.tsx:51
#~ msgid "Delete Shipment"
#~ msgstr "Delete Shipment"

#: src/tables/sales/SalesOrderShipmentTable.tsx:55
msgid "Create Shipment"
msgstr ""

#: src/tables/sales/SalesOrderShipmentTable.tsx:102
msgid "Items"
msgstr ""

#: src/tables/sales/SalesOrderShipmentTable.tsx:137
msgid "View Shipment"
msgstr "Ver envío"

#: src/tables/sales/SalesOrderShipmentTable.tsx:154
msgid "Edit shipment"
msgstr "Editar envío"

#: src/tables/sales/SalesOrderShipmentTable.tsx:162
msgid "Cancel shipment"
msgstr "Cancelar envío"

#: src/tables/sales/SalesOrderShipmentTable.tsx:177
msgid "Add shipment"
msgstr ""

#: src/tables/sales/SalesOrderShipmentTable.tsx:191
msgid "Show shipments which have been shipped"
msgstr ""

#: src/tables/sales/SalesOrderShipmentTable.tsx:196
msgid "Show shipments which have been delivered"
msgstr ""

#: src/tables/settings/ApiTokenTable.tsx:29
#: src/tables/settings/ApiTokenTable.tsx:43
msgid "Generate Token"
msgstr ""

#: src/tables/settings/ApiTokenTable.tsx:31
msgid "Token generated"
msgstr ""

#: src/tables/settings/ApiTokenTable.tsx:66
#: src/tables/settings/ApiTokenTable.tsx:110
msgid "Revoked"
msgstr ""

#: src/tables/settings/ApiTokenTable.tsx:70
#: src/tables/settings/ApiTokenTable.tsx:172
msgid "Token"
msgstr ""

#: src/tables/settings/ApiTokenTable.tsx:77
msgid "In Use"
msgstr ""

#: src/tables/settings/ApiTokenTable.tsx:86
msgid "Last Seen"
msgstr ""

#: src/tables/settings/ApiTokenTable.tsx:91
msgid "Expiry"
msgstr ""

#: src/tables/settings/ApiTokenTable.tsx:111
msgid "Show revoked tokens"
msgstr ""

#: src/tables/settings/ApiTokenTable.tsx:130
msgid "Revoke"
msgstr "Revocar"

#: src/tables/settings/ApiTokenTable.tsx:154
msgid "Error revoking token"
msgstr "Error al revocar token"

#: src/tables/settings/ApiTokenTable.tsx:176
msgid "Tokens are only shown once - make sure to note it down."
msgstr ""

#: src/tables/settings/BarcodeScanHistoryTable.tsx:59
msgid "Barcode Information"
msgstr "Información de código de barras"

#: src/tables/settings/BarcodeScanHistoryTable.tsx:84
msgid "Endpoint"
msgstr "Extremo"

#: src/tables/settings/BarcodeScanHistoryTable.tsx:88
#: src/tables/settings/BarcodeScanHistoryTable.tsx:207
#: src/tables/stock/StockItemTestResultTable.tsx:175
msgid "Result"
msgstr ""

#: src/tables/settings/BarcodeScanHistoryTable.tsx:96
msgid "Context"
msgstr "Contexto"

#: src/tables/settings/BarcodeScanHistoryTable.tsx:117
msgid "Response"
msgstr "Respuesta"

#: src/tables/settings/BarcodeScanHistoryTable.tsx:208
msgid "Filter by result"
msgstr "Filtrar por resultado"

#: src/tables/settings/BarcodeScanHistoryTable.tsx:222
msgid "Delete Barcode Scan Record"
msgstr "Eliminar registro de escaneo de código de barras"

#: src/tables/settings/BarcodeScanHistoryTable.tsx:248
msgid "Barcode Scan Details"
msgstr "Detalles de escaneo de código de barras"

#: src/tables/settings/BarcodeScanHistoryTable.tsx:258
msgid "Logging Disabled"
msgstr "Registro desactivado"

#: src/tables/settings/BarcodeScanHistoryTable.tsx:260
msgid "Barcode logging is not enabled"
msgstr "El registro de código de barras no está habilitado"

#: src/tables/settings/CustomStateTable.tsx:63
msgid "Status Group"
msgstr ""

#: src/tables/settings/CustomStateTable.tsx:84
msgid "Logical State"
msgstr ""

#: src/tables/settings/CustomStateTable.tsx:96
msgid "Identifier"
msgstr ""

#: src/tables/settings/CustomStateTable.tsx:115
#~ msgid "Add state"
#~ msgstr "Add state"

#: src/tables/settings/CustomStateTable.tsx:133
#: src/tables/settings/CustomStateTable.tsx:140
#: src/tables/settings/CustomStateTable.tsx:202
msgid "Add State"
msgstr ""

#: src/tables/settings/CustomStateTable.tsx:153
msgid "Edit State"
msgstr ""

#: src/tables/settings/CustomStateTable.tsx:161
msgid "Delete State"
msgstr ""

#: src/tables/settings/CustomUnitsTable.tsx:50
msgid "Add Custom Unit"
msgstr ""

#: src/tables/settings/CustomUnitsTable.tsx:60
msgid "Edit Custom Unit"
msgstr ""

#: src/tables/settings/CustomUnitsTable.tsx:68
msgid "Delete Custom Unit"
msgstr ""

#: src/tables/settings/CustomUnitsTable.tsx:99
msgid "Add custom unit"
msgstr ""

#: src/tables/settings/ErrorTable.tsx:51
#~ msgid "Delete error report"
#~ msgstr "Delete error report"

#: src/tables/settings/ErrorTable.tsx:67
msgid "Traceback"
msgstr "Rastreo"

#: src/tables/settings/ErrorTable.tsx:103
msgid "When"
msgstr ""

#: src/tables/settings/ErrorTable.tsx:113
msgid "Error Information"
msgstr ""

#: src/tables/settings/ErrorTable.tsx:123
msgid "Delete Error Report"
msgstr ""

#: src/tables/settings/ErrorTable.tsx:125
msgid "Are you sure you want to delete this error report?"
msgstr ""

#: src/tables/settings/ErrorTable.tsx:127
msgid "Error report deleted"
msgstr ""

#: src/tables/settings/ErrorTable.tsx:146
#: src/tables/settings/FailedTasksTable.tsx:65
msgid "Error Details"
msgstr ""

#: src/tables/settings/ExportSessionTable.tsx:24
msgid "Output Type"
msgstr ""

#: src/tables/settings/ExportSessionTable.tsx:34
msgid "Exported On"
msgstr ""

#: src/tables/settings/FailedTasksTable.tsx:32
#: src/tables/settings/PendingTasksTable.tsx:23
#: src/tables/settings/ScheduledTasksTable.tsx:19
msgid "Task"
msgstr ""

#: src/tables/settings/FailedTasksTable.tsx:38
#: src/tables/settings/PendingTasksTable.tsx:28
msgid "Task ID"
msgstr ""

#: src/tables/settings/FailedTasksTable.tsx:42
#: src/tables/stock/StockItemTestResultTable.tsx:222
msgid "Started"
msgstr ""

#: src/tables/settings/FailedTasksTable.tsx:54
msgid "Attempts"
msgstr ""

#: src/tables/settings/FailedTasksTable.tsx:92
msgid "No Information"
msgstr "Sin información"

#: src/tables/settings/FailedTasksTable.tsx:93
msgid "No error details are available for this task"
msgstr "No hay detalles de error disponibles para esta tarea"

#: src/tables/settings/GroupTable.tsx:66
msgid "Group with id {id} not found"
msgstr ""

#: src/tables/settings/GroupTable.tsx:68
msgid "An error occurred while fetching group details"
msgstr ""

#: src/tables/settings/GroupTable.tsx:91
#: src/tables/settings/GroupTable.tsx:185
msgid "Name of the user group"
msgstr ""

#: src/tables/settings/GroupTable.tsx:117
#~ msgid "Permission set"
#~ msgstr "Permission set"

#: src/tables/settings/GroupTable.tsx:173
msgid "Delete group"
msgstr "Eliminar grupo"

#: src/tables/settings/GroupTable.tsx:174
msgid "Group deleted"
msgstr ""

#: src/tables/settings/GroupTable.tsx:176
msgid "Are you sure you want to delete this group?"
msgstr ""

#: src/tables/settings/GroupTable.tsx:181
msgid "Add Group"
msgstr ""

#: src/tables/settings/GroupTable.tsx:198
msgid "Add group"
msgstr "Agregar grupo"

#: src/tables/settings/GroupTable.tsx:213
#~ msgid "Edit group"
#~ msgstr "Edit group"

#: src/tables/settings/GroupTable.tsx:219
msgid "Edit Group"
msgstr ""

#: src/tables/settings/ImportSessionTable.tsx:37
msgid "Delete Import Session"
msgstr ""

#: src/tables/settings/ImportSessionTable.tsx:43
#: src/tables/settings/ImportSessionTable.tsx:126
msgid "Create Import Session"
msgstr ""

#: src/tables/settings/ImportSessionTable.tsx:69
msgid "Uploaded"
msgstr ""

#: src/tables/settings/ImportSessionTable.tsx:108
#: src/tables/settings/TemplateTable.tsx:371
msgid "Model Type"
msgstr ""

#: src/tables/settings/ImportSessionTable.tsx:109
#: src/tables/settings/TemplateTable.tsx:372
msgid "Filter by target model type"
msgstr ""

#: src/tables/settings/ImportSessionTable.tsx:115
msgid "Filter by import session status"
msgstr ""

#: src/tables/settings/PendingTasksTable.tsx:42
msgid "Arguments"
msgstr ""

#: src/tables/settings/ProjectCodeTable.tsx:42
msgid "Add Project Code"
msgstr ""

#: src/tables/settings/ProjectCodeTable.tsx:54
msgid "Edit Project Code"
msgstr ""

#: src/tables/settings/ProjectCodeTable.tsx:62
msgid "Delete Project Code"
msgstr ""

#: src/tables/settings/ProjectCodeTable.tsx:93
msgid "Add project code"
msgstr "Agregar código de proyecto"

#: src/tables/settings/ScheduledTasksTable.tsx:25
msgid "Last Run"
msgstr ""

#: src/tables/settings/ScheduledTasksTable.tsx:47
msgid "Next Run"
msgstr ""

#: src/tables/settings/StocktakeReportTable.tsx:28
msgid "Report"
msgstr "Informe"

#: src/tables/settings/StocktakeReportTable.tsx:36
msgid "Part Count"
msgstr "Número de partes"

#: src/tables/settings/StocktakeReportTable.tsx:59
msgid "Delete Report"
msgstr "Eliminar informe"

#: src/tables/settings/TemplateTable.tsx:120
#~ msgid "{templateTypeTranslation} with id {id} not found"
#~ msgstr "{templateTypeTranslation} with id {id} not found"

#: src/tables/settings/TemplateTable.tsx:124
#~ msgid "An error occurred while fetching {templateTypeTranslation} details"
#~ msgstr "An error occurred while fetching {templateTypeTranslation} details"

#: src/tables/settings/TemplateTable.tsx:146
#~ msgid "actions"
#~ msgstr "actions"

#: src/tables/settings/TemplateTable.tsx:163
msgid "Template not found"
msgstr ""

#: src/tables/settings/TemplateTable.tsx:165
msgid "An error occurred while fetching template details"
msgstr ""

#: src/tables/settings/TemplateTable.tsx:243
#~ msgid "Add new"
#~ msgstr "Add new"

#: src/tables/settings/TemplateTable.tsx:243
#~ msgid "Create new"
#~ msgstr "Create new"

#: src/tables/settings/TemplateTable.tsx:259
msgid "Modify"
msgstr ""

#: src/tables/settings/TemplateTable.tsx:260
msgid "Modify template file"
msgstr ""

#: src/tables/settings/TemplateTable.tsx:316
#: src/tables/settings/TemplateTable.tsx:384
msgid "Edit Template"
msgstr ""

#: src/tables/settings/TemplateTable.tsx:324
msgid "Delete template"
msgstr ""

#: src/tables/settings/TemplateTable.tsx:330
msgid "Add Template"
msgstr ""

#: src/tables/settings/TemplateTable.tsx:343
msgid "Add template"
msgstr ""

#: src/tables/settings/TemplateTable.tsx:366
msgid "Filter by enabled status"
msgstr ""

#: src/tables/settings/TemplateTable.tsx:420
#~ msgid "Report Output"
#~ msgstr "Report Output"

#: src/tables/settings/UserTable.tsx:112
msgid "Groups updated"
msgstr ""

#: src/tables/settings/UserTable.tsx:113
msgid "User groups updated successfully"
msgstr ""

#: src/tables/settings/UserTable.tsx:120
msgid "Error updating user groups"
msgstr ""

#: src/tables/settings/UserTable.tsx:139
msgid "User with id {id} not found"
msgstr ""

#: src/tables/settings/UserTable.tsx:141
msgid "An error occurred while fetching user details"
msgstr ""

#: src/tables/settings/UserTable.tsx:154
#~ msgid "No groups"
#~ msgstr "No groups"

#: src/tables/settings/UserTable.tsx:167
msgid "Is Active"
msgstr ""

#: src/tables/settings/UserTable.tsx:168
msgid "Designates whether this user should be treated as active. Unselect this instead of deleting accounts."
msgstr ""

#: src/tables/settings/UserTable.tsx:172
msgid "Is Staff"
msgstr ""

#: src/tables/settings/UserTable.tsx:173
msgid "Designates whether the user can log into the django admin site."
msgstr ""

#: src/tables/settings/UserTable.tsx:177
msgid "Is Superuser"
msgstr ""

#: src/tables/settings/UserTable.tsx:178
msgid "Designates that this user has all permissions without explicitly assigning them."
msgstr ""

#: src/tables/settings/UserTable.tsx:188
msgid "You cannot edit the rights for the currently logged-in user."
msgstr ""

#: src/tables/settings/UserTable.tsx:207
msgid "User Groups"
msgstr ""

#: src/tables/settings/UserTable.tsx:305
#~ msgid "Edit user"
#~ msgstr "Edit user"

#: src/tables/settings/UserTable.tsx:310
msgid "Delete user"
msgstr ""

#: src/tables/settings/UserTable.tsx:311
msgid "User deleted"
msgstr ""

#: src/tables/settings/UserTable.tsx:313
msgid "Are you sure you want to delete this user?"
msgstr ""

#: src/tables/settings/UserTable.tsx:319
msgid "Add User"
msgstr ""

#: src/tables/settings/UserTable.tsx:327
msgid "Added user"
msgstr "Usuario agregado"

#: src/tables/settings/UserTable.tsx:338
msgid "Add user"
msgstr ""

#: src/tables/settings/UserTable.tsx:351
msgid "Show active users"
msgstr "Mostrar usuarios activos"

#: src/tables/settings/UserTable.tsx:356
msgid "Show staff users"
msgstr "Mostrar usuarios del personal"

#: src/tables/settings/UserTable.tsx:361
msgid "Show superusers"
msgstr "Mostrar superusuarios"

#: src/tables/settings/UserTable.tsx:379
msgid "Edit User"
msgstr ""

#: src/tables/stock/InstalledItemsTable.tsx:37
#: src/tables/stock/InstalledItemsTable.tsx:91
msgid "Install Item"
msgstr "Instalar artículo"

#: src/tables/stock/InstalledItemsTable.tsx:39
msgid "Item installed"
msgstr "Artículo instalado"

#: src/tables/stock/InstalledItemsTable.tsx:50
msgid "Uninstall Item"
msgstr "Desinstalar artículo"

#: src/tables/stock/InstalledItemsTable.tsx:52
msgid "Item uninstalled"
msgstr "Artículo desinstalado"

#: src/tables/stock/InstalledItemsTable.tsx:109
msgid "Uninstall stock item"
msgstr "Desinstalar artículo de existencias"

#: src/tables/stock/LocationTypesTable.tsx:39
#: src/tables/stock/LocationTypesTable.tsx:109
msgid "Add Location Type"
msgstr "Añadir Tipo de Ubicación"

#: src/tables/stock/LocationTypesTable.tsx:47
msgid "Edit Location Type"
msgstr "Editar Tipo de Ubicación"

#: src/tables/stock/LocationTypesTable.tsx:55
msgid "Delete Location Type"
msgstr "Eliminar Tipo de Ubicación"

#: src/tables/stock/LocationTypesTable.tsx:63
msgid "Icon"
msgstr "Icono"

#: src/tables/stock/StockItemTable.tsx:96
msgid "This stock item is in production"
msgstr ""

#: src/tables/stock/StockItemTable.tsx:103
msgid "This stock item has been assigned to a sales order"
msgstr ""

#: src/tables/stock/StockItemTable.tsx:110
msgid "This stock item has been assigned to a customer"
msgstr ""

#: src/tables/stock/StockItemTable.tsx:117
msgid "This stock item is installed in another stock item"
msgstr ""

#: src/tables/stock/StockItemTable.tsx:124
msgid "This stock item has been consumed by a build order"
msgstr ""

#: src/tables/stock/StockItemTable.tsx:131
msgid "This stock item is unavailable"
msgstr "Este artículo de existencias no está disponible"

#: src/tables/stock/StockItemTable.tsx:140
msgid "This stock item has expired"
msgstr ""

#: src/tables/stock/StockItemTable.tsx:144
msgid "This stock item is stale"
msgstr ""

#: src/tables/stock/StockItemTable.tsx:156
msgid "This stock item is fully allocated"
msgstr ""

#: src/tables/stock/StockItemTable.tsx:163
msgid "This stock item is partially allocated"
msgstr ""

#: src/tables/stock/StockItemTable.tsx:191
msgid "This stock item has been depleted"
msgstr ""

#: src/tables/stock/StockItemTable.tsx:289
msgid "Stocktake Date"
msgstr ""

#: src/tables/stock/StockItemTable.tsx:301
#~ msgid "Show stock for assmebled parts"
#~ msgstr "Show stock for assmebled parts"

#: src/tables/stock/StockItemTable.tsx:307
msgid "Show stock for active parts"
msgstr ""

#: src/tables/stock/StockItemTable.tsx:312
msgid "Filter by stock status"
msgstr ""

#: src/tables/stock/StockItemTable.tsx:318
msgid "Show stock for assembled parts"
msgstr "Mostrar stock existencias para piezas ensambladas"

#: src/tables/stock/StockItemTable.tsx:323
msgid "Show items which have been allocated"
msgstr ""

#: src/tables/stock/StockItemTable.tsx:328
msgid "Show items which are available"
msgstr ""

#: src/tables/stock/StockItemTable.tsx:332
#: src/tables/stock/StockLocationTable.tsx:38
msgid "Include Sublocations"
msgstr ""

#: src/tables/stock/StockItemTable.tsx:333
msgid "Include stock in sublocations"
msgstr ""

#: src/tables/stock/StockItemTable.tsx:337
msgid "Depleted"
msgstr ""

#: src/tables/stock/StockItemTable.tsx:338
msgid "Show depleted stock items"
msgstr ""

#: src/tables/stock/StockItemTable.tsx:343
msgid "Show items which are in stock"
msgstr ""

#: src/tables/stock/StockItemTable.tsx:348
msgid "Show items which are in production"
msgstr ""

#: src/tables/stock/StockItemTable.tsx:353
msgid "Include stock items for variant parts"
msgstr ""

#: src/tables/stock/StockItemTable.tsx:357
msgid "Consumed"
msgstr "Consumido"

#: src/tables/stock/StockItemTable.tsx:358
msgid "Show items which have been consumed by a build order"
msgstr "Mostrar artículos que han sido consumidos por una orden de construcción"

#: src/tables/stock/StockItemTable.tsx:363
msgid "Show stock items which are installed in other items"
msgstr ""

#: src/tables/stock/StockItemTable.tsx:367
msgid "Sent to Customer"
msgstr ""

#: src/tables/stock/StockItemTable.tsx:368
msgid "Show items which have been sent to a customer"
msgstr ""

#: src/tables/stock/StockItemTable.tsx:372
msgid "Is Serialized"
msgstr ""

#: src/tables/stock/StockItemTable.tsx:373
msgid "Show items which have a serial number"
msgstr ""

#: src/tables/stock/StockItemTable.tsx:378
msgid "Filter items by batch code"
msgstr "Filtrar elementos por código de lote"

#: src/tables/stock/StockItemTable.tsx:384
msgid "Filter items by serial number"
msgstr "Filtrar artículos por número de serie"

#: src/tables/stock/StockItemTable.tsx:389
msgid "Serial Number LTE"
msgstr "Número de serie LTE"

#: src/tables/stock/StockItemTable.tsx:390
msgid "Show items with serial numbers less than or equal to a given value"
msgstr "Mostrar elementos con números de serie menores o iguales a un valor determinado"

#: src/tables/stock/StockItemTable.tsx:395
msgid "Serial Number GTE"
msgstr "Número de serie GTE"

#: src/tables/stock/StockItemTable.tsx:396
msgid "Show items with serial numbers greater than or equal to a given value"
msgstr "Mostrar artículos con números de serie mayores o iguales a un valor dado"

#: src/tables/stock/StockItemTable.tsx:401
msgid "Has Batch Code"
msgstr ""

#: src/tables/stock/StockItemTable.tsx:402
msgid "Show items which have a batch code"
msgstr ""

#: src/tables/stock/StockItemTable.tsx:407
msgid "Show tracked items"
msgstr ""

#: src/tables/stock/StockItemTable.tsx:411
msgid "Has Purchase Price"
msgstr ""

#: src/tables/stock/StockItemTable.tsx:412
msgid "Show items which have a purchase price"
msgstr ""

#: src/tables/stock/StockItemTable.tsx:417
msgid "Show items which have expired"
msgstr "Mostrar artículos que han caducado"

#: src/tables/stock/StockItemTable.tsx:423
msgid "Show items which are stale"
msgstr "Mostrar artículos que son obsoletos"

#: src/tables/stock/StockItemTable.tsx:428
msgid "Expired Before"
msgstr "Expiró antes"

#: src/tables/stock/StockItemTable.tsx:429
msgid "Show items which expired before this date"
msgstr "Mostrar elementos que caducaron antes de esta fecha"

#: src/tables/stock/StockItemTable.tsx:435
msgid "Expired After"
msgstr "Expiró después"

#: src/tables/stock/StockItemTable.tsx:436
msgid "Show items which expired after this date"
msgstr "Mostrar elementos que expiraron después de esta fecha"

#: src/tables/stock/StockItemTable.tsx:442
msgid "Updated Before"
msgstr "Actualizado antes"

#: src/tables/stock/StockItemTable.tsx:443
msgid "Show items updated before this date"
msgstr "Mostrar elementos actualizados antes de esta fecha"

#: src/tables/stock/StockItemTable.tsx:448
msgid "Updated After"
msgstr "Actualizado después de"

#: src/tables/stock/StockItemTable.tsx:449
msgid "Show items updated after this date"
msgstr "Mostrar elementos actualizados después de esta fecha"

#: src/tables/stock/StockItemTable.tsx:454
msgid "Stocktake Before"
msgstr "Inventario antes de"

#: src/tables/stock/StockItemTable.tsx:455
msgid "Show items counted before this date"
msgstr "Mostrar elementos contados antes de esta fecha"

#: src/tables/stock/StockItemTable.tsx:460
msgid "Stocktake After"
msgstr "Inventario después de"

#: src/tables/stock/StockItemTable.tsx:461
msgid "Show items counted after this date"
msgstr "Mostrar elementos contados después de esta fecha"

#: src/tables/stock/StockItemTable.tsx:466
msgid "External Location"
msgstr ""

#: src/tables/stock/StockItemTable.tsx:467
msgid "Show items in an external location"
msgstr ""

#: src/tables/stock/StockItemTable.tsx:528
#~ msgid "Delete stock items"
#~ msgstr "Delete stock items"

#: src/tables/stock/StockItemTable.tsx:586
msgid "Add a new stock item"
msgstr ""

#: src/tables/stock/StockItemTable.tsx:595
msgid "Remove some quantity from a stock item"
msgstr ""

#: src/tables/stock/StockItemTable.tsx:606
msgid "Move Stock items to new locations"
msgstr ""

#: src/tables/stock/StockItemTable.tsx:613
msgid "Change stock status"
msgstr ""

#: src/tables/stock/StockItemTable.tsx:615
msgid "Change the status of stock items"
msgstr ""

#: src/tables/stock/StockItemTable.tsx:622
msgid "Merge stock"
msgstr ""

#: src/tables/stock/StockItemTable.tsx:624
msgid "Merge stock items"
msgstr ""

#: src/tables/stock/StockItemTable.tsx:633
msgid "Order new stock"
msgstr ""

#: src/tables/stock/StockItemTable.tsx:644
msgid "Assign to customer"
msgstr ""

#: src/tables/stock/StockItemTable.tsx:646
msgid "Assign items to a customer"
msgstr "Asignar artículos a un cliente"

#: src/tables/stock/StockItemTable.tsx:653
msgid "Delete stock"
msgstr ""

#: src/tables/stock/StockItemTestResultTable.tsx:138
msgid "Test"
msgstr ""

#: src/tables/stock/StockItemTestResultTable.tsx:164
msgid "Test result for installed stock item"
msgstr ""

#: src/tables/stock/StockItemTestResultTable.tsx:197
msgid "Attachment"
msgstr ""

#: src/tables/stock/StockItemTestResultTable.tsx:216
msgid "Test station"
msgstr ""

#: src/tables/stock/StockItemTestResultTable.tsx:238
msgid "Finished"
msgstr ""

#: src/tables/stock/StockItemTestResultTable.tsx:290
#: src/tables/stock/StockItemTestResultTable.tsx:361
msgid "Edit Test Result"
msgstr ""

#: src/tables/stock/StockItemTestResultTable.tsx:292
msgid "Test result updated"
msgstr ""

#: src/tables/stock/StockItemTestResultTable.tsx:298
#: src/tables/stock/StockItemTestResultTable.tsx:370
msgid "Delete Test Result"
msgstr ""

#: src/tables/stock/StockItemTestResultTable.tsx:300
msgid "Test result deleted"
msgstr ""

#: src/tables/stock/StockItemTestResultTable.tsx:314
msgid "Test Passed"
msgstr ""

#: src/tables/stock/StockItemTestResultTable.tsx:315
msgid "Test result has been recorded"
msgstr ""

#: src/tables/stock/StockItemTestResultTable.tsx:322
msgid "Failed to record test result"
msgstr ""

#: src/tables/stock/StockItemTestResultTable.tsx:339
msgid "Pass Test"
msgstr ""

#: src/tables/stock/StockItemTestResultTable.tsx:388
msgid "Show results for required tests"
msgstr ""

#: src/tables/stock/StockItemTestResultTable.tsx:392
msgid "Include Installed"
msgstr ""

#: src/tables/stock/StockItemTestResultTable.tsx:393
msgid "Show results for installed stock items"
msgstr ""

#: src/tables/stock/StockItemTestResultTable.tsx:397
msgid "Passed"
msgstr ""

#: src/tables/stock/StockItemTestResultTable.tsx:398
msgid "Show only passed tests"
msgstr ""

#: src/tables/stock/StockItemTestResultTable.tsx:403
msgid "Show results for enabled tests"
msgstr "Mostrar resultados para las pruebas habilitadas"

#: src/tables/stock/StockLocationTable.tsx:38
#~ msgid "structural"
#~ msgstr "structural"

#: src/tables/stock/StockLocationTable.tsx:39
msgid "Include sublocations in results"
msgstr ""

#: src/tables/stock/StockLocationTable.tsx:43
#~ msgid "external"
#~ msgstr "external"

#: src/tables/stock/StockLocationTable.tsx:44
msgid "Show structural locations"
msgstr ""

#: src/tables/stock/StockLocationTable.tsx:49
msgid "Show external locations"
msgstr ""

#: src/tables/stock/StockLocationTable.tsx:53
msgid "Has location type"
msgstr ""

#: src/tables/stock/StockLocationTable.tsx:58
msgid "Filter by location type"
msgstr ""

#: src/tables/stock/StockLocationTable.tsx:103
#: src/tables/stock/StockLocationTable.tsx:158
msgid "Add Stock Location"
msgstr ""

#: src/tables/stock/StockLocationTable.tsx:127
msgid "Set Parent Location"
msgstr ""

#: src/tables/stock/StockLocationTable.tsx:147
msgid "Set parent location for the selected items"
msgstr ""

#: src/tables/stock/StockTrackingTable.tsx:75
msgid "Added"
msgstr ""

#: src/tables/stock/StockTrackingTable.tsx:80
msgid "Removed"
msgstr ""

#: src/tables/stock/StockTrackingTable.tsx:204
msgid "Details"
msgstr "Detalles"

#: src/tables/stock/StockTrackingTable.tsx:219
msgid "No user information"
msgstr ""

#: src/tables/stock/TestStatisticsTable.tsx:34
#: src/tables/stock/TestStatisticsTable.tsx:64
#~ msgid "Total"
#~ msgstr "Total"

#: src/tables/stock/TestStatisticsTable.tsx:63
#~ msgid "Failed"
#~ msgstr "Failed"

#: src/views/MobileAppView.tsx:22
msgid "Mobile viewport detected"
msgstr ""

#: src/views/MobileAppView.tsx:25
msgid "InvenTree UI is optimized for Tablets and Desktops, you can use the official app for a mobile experience."
msgstr ""

#: src/views/MobileAppView.tsx:25
#~ msgid "Platform UI is optimized for Tablets and Desktops, you can use the official app for a mobile experience."
#~ msgstr "Platform UI is optimized for Tablets and Desktops, you can use the official app for a mobile experience."

#: src/views/MobileAppView.tsx:31
msgid "Read the docs"
msgstr "Leer la documentación"

#: src/views/MobileAppView.tsx:35
msgid "Ignore and continue to Desktop view"
msgstr ""

