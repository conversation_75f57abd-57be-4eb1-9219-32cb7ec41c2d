{% load spa_helper %}
{% load inventree_extras %}
{% spa_bundle as bundle %}

<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>{% inventree_instance_name %}</title>
  {% include "favicon.html" %}
</head>

<body>
  <div id="root"></div>
  <div id="spa_settings">{% spa_settings %}</div>
  {% if bundle == "NOT_FOUND" %}
  <div id="spa_bundle_error">
    <div>
      <h1>INVE-E1 - No frontend included</h1>
      <p>The frontend bundle could not be found. Please check that your deployment method includes the bundle or check the <a href="https://docs.inventree.org/en/stable/faq/">FAQ</a>.<br/>
        <span>Install method: <code>{% inventree_installer %}</code></span></p>
  </div>
  {% else %}
  <div id="spa_bundle">{{ bundle }}</div>
  {% endif %}
</body>

</html>
