# Generated by Django 3.2.16 on 2023-02-11 00:29

import InvenTree.fields
from django.db import migrations
import djmoney.models.fields
import djmoney.models.validators


class Migration(migrations.Migration):

    dependencies = [
        ('part', '0095_alter_part_responsible'),
    ]

    operations = [
        migrations.AddField(
            model_name='partstocktake',
            name='cost_max',
            field=InvenTree.fields.InvenTreeModelMoneyField(blank=True, currency_choices=[], decimal_places=6, default_currency='', help_text='Estimated maximum cost of stock on hand', max_digits=19, null=True, validators=[djmoney.models.validators.MinMoneyValidator(0)], verbose_name='Maximum Stock Cost'),
        ),
        migrations.AddField(
            model_name='partstocktake',
            name='cost_max_currency',
            field=djmoney.models.fields.CurrencyField(choices=[], default='', editable=False, max_length=3),
        ),
        migrations.AddField(
            model_name='partstocktake',
            name='cost_min',
            field=InvenTree.fields.InvenTreeModelMoneyField(blank=True, currency_choices=[], decimal_places=6, default_currency='', help_text='Estimated minimum cost of stock on hand', max_digits=19, null=True, validators=[djmoney.models.validators.MinMoneyValidator(0)], verbose_name='Minimum Stock Cost'),
        ),
        migrations.AddField(
            model_name='partstocktake',
            name='cost_min_currency',
            field=djmoney.models.fields.CurrencyField(choices=[], default='', editable=False, max_length=3),
        ),
    ]
