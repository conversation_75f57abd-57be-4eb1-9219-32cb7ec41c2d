# Generated by Django 3.0.7 on 2021-01-03 11:15

import InvenTree.fields
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('part', '0060_merge_20201112_1722'),
        ('company', '0030_auto_20201112_1112'),
    ]

    operations = [
        migrations.AlterField(
            model_name='supplierpart',
            name='MPN',
            field=models.CharField(blank=True, help_text='Manufacturer part number', max_length=100, null=True, verbose_name='MPN'),
        ),
        migrations.AlterField(
            model_name='supplierpart',
            name='SKU',
            field=models.CharField(help_text='Supplier stock keeping unit', max_length=100, verbose_name='SKU'),
        ),
        migrations.AlterField(
            model_name='supplierpart',
            name='description',
            field=models.CharField(blank=True, help_text='Supplier part description', max_length=250, null=True, verbose_name='Description'),
        ),
        migrations.AlterField(
            model_name='supplierpart',
            name='link',
            field=InvenTree.fields.InvenTreeURLField(blank=True, help_text='URL for external supplier part link', null=True, verbose_name='Link'),
        ),
        migrations.AlterField(
            model_name='supplierpart',
            name='manufacturer',
            field=models.ForeignKey(blank=True, help_text='Select manufacturer', limit_choices_to={'is_manufacturer': True}, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='manufactured_parts', to='company.Company', verbose_name='Manufacturer'),
        ),
        migrations.AlterField(
            model_name='supplierpart',
            name='note',
            field=models.CharField(blank=True, help_text='Notes', max_length=100, null=True, verbose_name='Note'),
        ),
        migrations.AlterField(
            model_name='supplierpart',
            name='packaging',
            field=models.CharField(blank=True, help_text='Part packaging', max_length=50, null=True),
        ),
        migrations.AlterField(
            model_name='supplierpart',
            name='part',
            field=models.ForeignKey(help_text='Select part', limit_choices_to={'purchaseable': True}, on_delete=django.db.models.deletion.CASCADE, related_name='supplier_parts', to='part.Part', verbose_name='Base Part'),
        ),
        migrations.AlterField(
            model_name='supplierpart',
            name='supplier',
            field=models.ForeignKey(help_text='Select supplier', limit_choices_to={'is_supplier': True}, on_delete=django.db.models.deletion.CASCADE, related_name='supplied_parts', to='company.Company', verbose_name='Supplier'),
        ),
    ]
