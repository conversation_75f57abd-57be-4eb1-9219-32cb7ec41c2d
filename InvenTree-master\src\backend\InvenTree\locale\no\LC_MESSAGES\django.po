msgid ""
msgstr ""
"Project-Id-Version: inventree\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-09 19:12+0000\n"
"PO-Revision-Date: 2025-05-09 19:14\n"
"Last-Translator: \n"
"Language-Team: Norwegian\n"
"Language: no_NO\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Crowdin-Project: inventree\n"
"X-Crowdin-Project-ID: 452300\n"
"X-Crowdin-Language: no\n"
"X-Crowdin-File: /src/backend/InvenTree/locale/en/LC_MESSAGES/django.po\n"
"X-Crowdin-File-ID: 250\n"

#: InvenTree/AllUserRequire2FAMiddleware.py:42
msgid "You must enable two-factor authentication before doing anything else."
msgstr ""

#: InvenTree/api.py:355
msgid "API endpoint not found"
msgstr "API-endepunkt ikke funnet"

#: InvenTree/api.py:432
msgid "List of items or filters must be provided for bulk operation"
msgstr ""

#: InvenTree/api.py:439
msgid "Items must be provided as a list"
msgstr ""

#: InvenTree/api.py:447
msgid "Invalid items list provided"
msgstr ""

#: InvenTree/api.py:453
msgid "Filters must be provided as a dict"
msgstr ""

#: InvenTree/api.py:460
msgid "Invalid filters provided"
msgstr ""

#: InvenTree/api.py:465
msgid "All filter must only be used with true"
msgstr ""

#: InvenTree/api.py:470
msgid "No items match the provided criteria"
msgstr ""

#: InvenTree/api.py:713
msgid "User does not have permission to view this model"
msgstr "Brukeren har ikke rettigheter til å se denne modellen"

#: InvenTree/auth_overrides.py:57
msgid "Email (again)"
msgstr "E-post (gjenta)"

#: InvenTree/auth_overrides.py:61
msgid "Email address confirmation"
msgstr "Bekreft e-postaddresse"

#: InvenTree/auth_overrides.py:84
msgid "You must type the same email each time."
msgstr "Du må angi samme e-post hver gang."

#: InvenTree/auth_overrides.py:126 InvenTree/auth_overrides.py:134
msgid "The provided primary email address is not valid."
msgstr "Den oppgitte primære e-postadressen er ikke gyldig."

#: InvenTree/auth_overrides.py:140
msgid "The provided email domain is not approved."
msgstr "Det oppgitte e-postdomenet er ikke godkjent."

#: InvenTree/conversion.py:162
#, python-brace-format
msgid "Invalid unit provided ({unit})"
msgstr "Ugyldig enhet angitt ({unit})"

#: InvenTree/conversion.py:179
msgid "No value provided"
msgstr "Ingen verdi angitt"

#: InvenTree/conversion.py:206
#, python-brace-format
msgid "Could not convert {original} to {unit}"
msgstr "Kunne ikke konvertere {original} til {unit}"

#: InvenTree/conversion.py:208 InvenTree/conversion.py:222
#: InvenTree/helpers.py:548 order/models.py:707 order/models.py:975
msgid "Invalid quantity provided"
msgstr "Ugyldig mengde oppgitt"

#: InvenTree/exceptions.py:109
msgid "Error details can be found in the admin panel"
msgstr "Feildetaljer kan finnes i admin-panelet"

#: InvenTree/fields.py:138
msgid "Enter date"
msgstr "Oppgi dato"

#: InvenTree/fields.py:161
msgid "Invalid decimal value"
msgstr ""

#: InvenTree/fields.py:210 InvenTree/models.py:887 build/serializers.py:526
#: build/serializers.py:597 company/models.py:834 order/models.py:1579
#: part/models.py:3366
#: report/templates/report/inventree_build_order_report.html:172
#: stock/models.py:2708 stock/models.py:2832 stock/serializers.py:756
#: stock/serializers.py:918 stock/serializers.py:1048 stock/serializers.py:1100
#: stock/serializers.py:1419 stock/serializers.py:1508
#: stock/serializers.py:1684
msgid "Notes"
msgstr "Notater"

#: InvenTree/format.py:166
#, python-brace-format
msgid "Value '{name}' does not appear in pattern format"
msgstr "Verdi '{name}' vises ikke i mønsterformat"

#: InvenTree/format.py:177
msgid "Provided value does not match required pattern: "
msgstr "Angitt verdi samsvarer ikke med påkrevd mønster: "

#: InvenTree/helpers.py:552
msgid "Cannot serialize more than 1000 items at once"
msgstr ""

#: InvenTree/helpers.py:558
msgid "Empty serial number string"
msgstr "Tom serienummerstreng"

#: InvenTree/helpers.py:587
msgid "Duplicate serial"
msgstr "Duplisert serienummer"

#: InvenTree/helpers.py:619 InvenTree/helpers.py:662 InvenTree/helpers.py:680
#: InvenTree/helpers.py:687 InvenTree/helpers.py:706
#, python-brace-format
msgid "Invalid group: {group}"
msgstr ""

#: InvenTree/helpers.py:650
#, python-brace-format
msgid "Group range {group} exceeds allowed quantity ({expected_quantity})"
msgstr "Gruppesekvens {group} overskrider tillatt antall ({expected_quantity})"

#: InvenTree/helpers.py:716
msgid "No serial numbers found"
msgstr "Ingen serienummer funnet"

#: InvenTree/helpers.py:721
msgid "Number of unique serial numbers ({len(serials)}) must match quantity ({expected_quantity})"
msgstr "Antall unike serienumre ({len(serials)}) må samsvare med antallet ({expected_quantity})"

#: InvenTree/helpers.py:840
msgid "Remove HTML tags from this value"
msgstr "Fjern HTML-tagger fra denne verdien"

#: InvenTree/helpers.py:919
msgid "Data contains prohibited markdown content"
msgstr ""

#: InvenTree/helpers_model.py:131
msgid "Connection error"
msgstr "Tilkoblingsfeil"

#: InvenTree/helpers_model.py:136 InvenTree/helpers_model.py:143
msgid "Server responded with invalid status code"
msgstr "Serveren svarte med ugyldig statuskode"

#: InvenTree/helpers_model.py:139
msgid "Exception occurred"
msgstr "Det har oppstått et unntak"

#: InvenTree/helpers_model.py:149
msgid "Server responded with invalid Content-Length value"
msgstr "Serveren svarte med ugyldig \"Content-Length\"-verdi"

#: InvenTree/helpers_model.py:152
msgid "Image size is too large"
msgstr "Bildestørrelsen er for stor"

#: InvenTree/helpers_model.py:164
msgid "Image download exceeded maximum size"
msgstr "Bildenedlasting overskred maksimal størrelse"

#: InvenTree/helpers_model.py:169
msgid "Remote server returned empty response"
msgstr "Ekstern server returnerte tomt svar"

#: InvenTree/helpers_model.py:177
msgid "Supplied URL is not a valid image file"
msgstr "Angitt URL er ikke en gyldig bildefil"

#: InvenTree/locales.py:20
msgid "Arabic"
msgstr "Arabisk"

#: InvenTree/locales.py:21
msgid "Bulgarian"
msgstr "Bulgarsk"

#: InvenTree/locales.py:22
msgid "Czech"
msgstr "Tsjekkisk"

#: InvenTree/locales.py:23
msgid "Danish"
msgstr "Dansk"

#: InvenTree/locales.py:24
msgid "German"
msgstr "Tysk"

#: InvenTree/locales.py:25
msgid "Greek"
msgstr "Gresk"

#: InvenTree/locales.py:26
msgid "English"
msgstr "Engelsk"

#: InvenTree/locales.py:27
msgid "Spanish"
msgstr "Spansk"

#: InvenTree/locales.py:28
msgid "Spanish (Mexican)"
msgstr "Spansk (Meksikansk)"

#: InvenTree/locales.py:29
msgid "Estonian"
msgstr "Estisk"

#: InvenTree/locales.py:30
msgid "Farsi / Persian"
msgstr "Farsi / Persisk"

#: InvenTree/locales.py:31
msgid "Finnish"
msgstr "Finsk"

#: InvenTree/locales.py:32
msgid "French"
msgstr "Fransk"

#: InvenTree/locales.py:33
msgid "Hebrew"
msgstr "Hebraisk"

#: InvenTree/locales.py:34
msgid "Hindi"
msgstr "Hindi"

#: InvenTree/locales.py:35
msgid "Hungarian"
msgstr "Ungarsk"

#: InvenTree/locales.py:36
msgid "Italian"
msgstr "Italiensk"

#: InvenTree/locales.py:37
msgid "Japanese"
msgstr "Japansk"

#: InvenTree/locales.py:38
msgid "Korean"
msgstr "Koreansk"

#: InvenTree/locales.py:39
msgid "Lithuanian"
msgstr ""

#: InvenTree/locales.py:40
msgid "Latvian"
msgstr "Latvisk"

#: InvenTree/locales.py:41
msgid "Dutch"
msgstr "Nederlandsk"

#: InvenTree/locales.py:42
msgid "Norwegian"
msgstr "Norsk"

#: InvenTree/locales.py:43
msgid "Polish"
msgstr "Polsk"

#: InvenTree/locales.py:44
msgid "Portuguese"
msgstr "Portugisisk"

#: InvenTree/locales.py:45
msgid "Portuguese (Brazilian)"
msgstr "Portugisisk (Brasil)"

#: InvenTree/locales.py:46
msgid "Romanian"
msgstr "Rumensk"

#: InvenTree/locales.py:47
msgid "Russian"
msgstr "Russisk"

#: InvenTree/locales.py:48
msgid "Slovak"
msgstr "Slovakisk"

#: InvenTree/locales.py:49
msgid "Slovenian"
msgstr "Slovensk"

#: InvenTree/locales.py:50
msgid "Serbian"
msgstr "Serbisk"

#: InvenTree/locales.py:51
msgid "Swedish"
msgstr "Svensk"

#: InvenTree/locales.py:52
msgid "Thai"
msgstr "Thailandsk"

#: InvenTree/locales.py:53
msgid "Turkish"
msgstr "Tyrkisk"

#: InvenTree/locales.py:54
msgid "Ukrainian"
msgstr "Ukrainsk"

#: InvenTree/locales.py:55
msgid "Vietnamese"
msgstr "Vietnamesisk"

#: InvenTree/locales.py:56
msgid "Chinese (Simplified)"
msgstr "Kinesisk (forenklet)"

#: InvenTree/locales.py:57
msgid "Chinese (Traditional)"
msgstr "Kinesisk (tradisjonell)"

#: InvenTree/magic_login.py:31
msgid "Log in to the app"
msgstr ""

#: InvenTree/magic_login.py:41 company/models.py:136 users/serializers.py:247
msgid "Email"
msgstr "E-post"

#: InvenTree/models.py:107
msgid "Error running plugin validation"
msgstr "Feil under validering av utvidelse"

#: InvenTree/models.py:184
msgid "Metadata must be a python dict object"
msgstr "Metadata må være et python dict-objekt"

#: InvenTree/models.py:190
msgid "Plugin Metadata"
msgstr "Utvidelse-metadata"

#: InvenTree/models.py:191
msgid "JSON metadata field, for use by external plugins"
msgstr "JSON-metadatafelt, for bruk av eksterne utvidelser"

#: InvenTree/models.py:374
msgid "Improperly formatted pattern"
msgstr "Uriktig formatert mønster"

#: InvenTree/models.py:381
msgid "Unknown format key specified"
msgstr "Ukjent formatnøkkel spesifisert"

#: InvenTree/models.py:387
msgid "Missing required format key"
msgstr "Mangler nødvendig formatnøkkel"

#: InvenTree/models.py:398
msgid "Reference field cannot be empty"
msgstr "Referansefeltet kan ikke være tomt"

#: InvenTree/models.py:406
msgid "Reference must match required pattern"
msgstr "Referansen må samsvare påkrevd mønster"

#: InvenTree/models.py:437
msgid "Reference number is too large"
msgstr "Referansenummeret er for stort"

#: InvenTree/models.py:688
msgid "Duplicate names cannot exist under the same parent"
msgstr "Duplikatnavn kan ikke eksistere under samme overordnede"

#: InvenTree/models.py:705
msgid "Invalid choice"
msgstr "Ugyldig valg"

#: InvenTree/models.py:734 common/models.py:1325 common/models.py:1752
#: common/models.py:2011 common/models.py:2136 common/serializers.py:488
#: company/models.py:593 generic/states/serializers.py:20 machine/models.py:24
#: part/models.py:1039 part/models.py:3837 plugin/models.py:53
#: report/models.py:203 stock/models.py:83
msgid "Name"
msgstr "Navn"

#: InvenTree/models.py:740 build/models.py:242 common/models.py:123
#: common/models.py:2143 common/models.py:2256 company/models.py:521
#: company/models.py:825 order/models.py:429 order/models.py:1615
#: part/models.py:1062 part/models.py:3852 report/models.py:209
#: report/models.py:714 report/models.py:740
#: report/templates/report/inventree_build_order_report.html:117
#: stock/models.py:89
msgid "Description"
msgstr "Beskrivelse"

#: InvenTree/models.py:741 stock/models.py:90
msgid "Description (optional)"
msgstr "Beskrivelse (valgfritt)"

#: InvenTree/models.py:756 common/models.py:2309
msgid "Path"
msgstr "Sti"

#: InvenTree/models.py:887
msgid "Markdown notes (optional)"
msgstr "Markdown-notater (valgfritt)"

#: InvenTree/models.py:918
msgid "Barcode Data"
msgstr "Strekkodedata"

#: InvenTree/models.py:919
msgid "Third party barcode data"
msgstr "Tredjeparts strekkodedata"

#: InvenTree/models.py:925
msgid "Barcode Hash"
msgstr "Strekkode-hash"

#: InvenTree/models.py:926
msgid "Unique hash of barcode data"
msgstr "Unik hash av strekkodedata"

#: InvenTree/models.py:1003
msgid "Existing barcode found"
msgstr "Eksisterende strekkode funnet"

#: InvenTree/models.py:1084
msgid "Task Failure"
msgstr ""

#: InvenTree/models.py:1085
#, python-brace-format
msgid "Background worker task '{f}' failed after {n} attempts"
msgstr ""

#: InvenTree/models.py:1112
msgid "Server Error"
msgstr "Serverfeil"

#: InvenTree/models.py:1113
msgid "An error has been logged by the server."
msgstr "En feil har blitt logget av serveren."

#: InvenTree/serializers.py:69 part/models.py:4455
msgid "Must be a valid number"
msgstr "Må være et gyldig tall"

#: InvenTree/serializers.py:111 company/models.py:187 part/models.py:3184
msgid "Currency"
msgstr "Valuta"

#: InvenTree/serializers.py:114 part/serializers.py:1335
msgid "Select currency from available options"
msgstr "Velg valuta ut fra tilgjengelige alternativer"

#: InvenTree/serializers.py:466
msgid "Invalid value"
msgstr "Ugyldig verdi"

#: InvenTree/serializers.py:503
msgid "Remote Image"
msgstr "Eksternt bilde"

#: InvenTree/serializers.py:504
msgid "URL of remote image file"
msgstr "URLtil ekstern bildefil"

#: InvenTree/serializers.py:522
msgid "Downloading images from remote URL is not enabled"
msgstr "Nedlasting av bilder fra ekstern URL er ikke aktivert"

#: InvenTree/serializers.py:529
msgid "Failed to download image from remote URL"
msgstr ""

#: InvenTree/validators.py:30
msgid "Invalid physical unit"
msgstr "Ugyldig fysisk enhet"

#: InvenTree/validators.py:36
msgid "Not a valid currency code"
msgstr "Ikke en gyldig valutakode"

#: InvenTree/validators.py:116 InvenTree/validators.py:132
msgid "Overage value must not be negative"
msgstr "Svinn-verdien kan ikke være negativ"

#: InvenTree/validators.py:134
msgid "Overage must not exceed 100%"
msgstr "Svinn kan ikke overstige 100%"

#: InvenTree/validators.py:140
msgid "Invalid value for overage"
msgstr "Ugyldig verdi for svinn"

#: build/api.py:39 order/api.py:104 order/api.py:263 order/serializers.py:128
msgid "Order Status"
msgstr "Ordrestatus"

#: build/api.py:65 build/models.py:254
msgid "Parent Build"
msgstr "Overordnet produksjon"

#: build/api.py:69 build/api.py:768 order/api.py:512 order/api.py:732
#: order/api.py:1106 order/api.py:1334 stock/api.py:522
msgid "Include Variants"
msgstr ""

#: build/api.py:85 build/api.py:470 build/api.py:782 build/models.py:260
#: build/serializers.py:1254 build/serializers.py:1376
#: build/serializers.py:1427 company/models.py:1044 company/serializers.py:432
#: order/api.py:291 order/api.py:295 order/api.py:880 order/api.py:1119
#: order/api.py:1122 order/models.py:1704 order/models.py:1863
#: order/models.py:1864 part/api.py:1448 part/api.py:1451 part/api.py:1517
#: part/api.py:1827 part/models.py:457 part/models.py:3195 part/models.py:3339
#: part/models.py:3487 part/models.py:3508 part/models.py:3530
#: part/models.py:3666 part/models.py:4027 part/models.py:4299
#: part/models.py:4665 part/serializers.py:1255 part/serializers.py:1924
#: report/templates/report/inventree_bill_of_materials_report.html:110
#: report/templates/report/inventree_bill_of_materials_report.html:137
#: report/templates/report/inventree_build_order_report.html:109
#: report/templates/report/inventree_purchase_order_report.html:27
#: report/templates/report/inventree_return_order_report.html:24
#: report/templates/report/inventree_sales_order_report.html:27
#: report/templates/report/inventree_sales_order_shipment_report.html:28
#: report/templates/report/inventree_stock_location_report.html:102
#: stock/api.py:535 stock/serializers.py:119 stock/serializers.py:171
#: stock/serializers.py:481 stock/serializers.py:643 stock/serializers.py:948
#: templates/email/build_order_completed.html:17
#: templates/email/build_order_required_stock.html:17
#: templates/email/low_stock_notification.html:15
#: templates/email/overdue_build_order.html:16
#: templates/email/part_event_notification.html:15
msgid "Part"
msgstr "Del"

#: build/api.py:105 build/api.py:108 part/api.py:1531 part/models.py:1080
#: part/models.py:3558 part/models.py:4136 part/serializers.py:1265
#: stock/api.py:818
msgid "Category"
msgstr "Kategori"

#: build/api.py:116 build/api.py:120
msgid "Ancestor Build"
msgstr ""

#: build/api.py:137 order/api.py:122
msgid "Assigned to me"
msgstr ""

#: build/api.py:152
#: report/templates/report/inventree_build_order_report.html:152
msgid "Issued By"
msgstr "Utstedt av"

#: build/api.py:167
msgid "Assigned To"
msgstr ""

#: build/api.py:202
msgid "Created before"
msgstr ""

#: build/api.py:206
msgid "Created after"
msgstr ""

#: build/api.py:210
msgid "Has start date"
msgstr ""

#: build/api.py:218
msgid "Start date before"
msgstr ""

#: build/api.py:222
msgid "Start date after"
msgstr ""

#: build/api.py:226
msgid "Has target date"
msgstr ""

#: build/api.py:234
msgid "Target date before"
msgstr ""

#: build/api.py:238
msgid "Target date after"
msgstr ""

#: build/api.py:242
msgid "Completed before"
msgstr ""

#: build/api.py:246
msgid "Completed after"
msgstr ""

#: build/api.py:249 order/api.py:219
msgid "Min Date"
msgstr ""

#: build/api.py:272 order/api.py:238
msgid "Max Date"
msgstr ""

#: build/api.py:297 build/api.py:300 part/api.py:224
msgid "Exclude Tree"
msgstr ""

#: build/api.py:409
msgid "Build must be cancelled before it can be deleted"
msgstr "Produksjonen må avbrytes før den kan slettes"

#: build/api.py:453 build/serializers.py:1392 part/models.py:4333
msgid "Consumable"
msgstr "Forbruksvare"

#: build/api.py:456 build/serializers.py:1395 part/models.py:4327
msgid "Optional"
msgstr "Valgfritt"

#: build/api.py:459 common/setting/system.py:429 part/models.py:1211
#: part/serializers.py:1663 part/serializers.py:1672 stock/api.py:588
msgid "Assembly"
msgstr "Sammenstilling"

#: build/api.py:462
msgid "Tracked"
msgstr "Spores"

#: build/api.py:465 build/serializers.py:1398 part/models.py:1229
msgid "Testable"
msgstr ""

#: build/api.py:475 order/api.py:925
msgid "Order Outstanding"
msgstr ""

#: build/api.py:485 order/api.py:884
msgid "Allocated"
msgstr "Tildelt"

#: build/api.py:495 company/models.py:889 company/serializers.py:427
#: templates/email/build_order_required_stock.html:19
#: templates/email/low_stock_notification.html:17
#: templates/email/part_event_notification.html:18
msgid "Available"
msgstr "Tilgjengelig"

#: build/api.py:805 build/models.py:116
#: report/templates/report/inventree_build_order_report.html:105
#: stock/serializers.py:92 templates/email/build_order_completed.html:16
#: templates/email/overdue_build_order.html:15
msgid "Build Order"
msgstr "Produksjonsordre"

#: build/models.py:117 users/ruleset.py:37
msgid "Build Orders"
msgstr "Produksjonsordrer"

#: build/models.py:162
msgid "Assembly BOM has not been validated"
msgstr "Sammenstillings-BOMen er ikke godkjent"

#: build/models.py:169
msgid "Build order cannot be created for an inactive part"
msgstr "Produksjonsordre kan ikke opprettes for en inaktiv del"

#: build/models.py:176
msgid "Build order cannot be created for an unlocked part"
msgstr "Produksjonsordre kan ikke opprettes for en ulåst del"

#: build/models.py:188
msgid "Invalid choice for parent build"
msgstr "Ugyldig valg for overordnet produksjon"

#: build/models.py:197 order/models.py:363
msgid "Responsible user or group must be specified"
msgstr "Ansvarlig bruker eller gruppe må spesifiseres"

#: build/models.py:202
msgid "Build order part cannot be changed"
msgstr "Produksjonsordrens del kan ikke endres"

#: build/models.py:207 order/models.py:376
msgid "Target date must be after start date"
msgstr ""

#: build/models.py:235
msgid "Build Order Reference"
msgstr "Produksjonsordre-referanse"

#: build/models.py:236 build/serializers.py:1389 order/models.py:601
#: order/models.py:1140 order/models.py:1572 order/models.py:2403
#: part/models.py:4348
#: report/templates/report/inventree_bill_of_materials_report.html:139
#: report/templates/report/inventree_purchase_order_report.html:28
#: report/templates/report/inventree_return_order_report.html:26
#: report/templates/report/inventree_sales_order_report.html:28
msgid "Reference"
msgstr "Referanse"

#: build/models.py:245
msgid "Brief description of the build (optional)"
msgstr "Kort beskrivelse av produksjonen (valgfritt)"

#: build/models.py:255
msgid "BuildOrder to which this build is allocated"
msgstr "Produksjonsordre som denne produksjonen er tildelt"

#: build/models.py:264
msgid "Select part to build"
msgstr "Velg del å produsere"

#: build/models.py:269
msgid "Sales Order Reference"
msgstr "Salgsordrereferanse"

#: build/models.py:274
msgid "SalesOrder to which this build is allocated"
msgstr "Salgsordren denne produksjonen er tildelt til"

#: build/models.py:279 build/serializers.py:1104
msgid "Source Location"
msgstr "Kildeplassering"

#: build/models.py:285
msgid "Select location to take stock from for this build (leave blank to take from any stock location)"
msgstr "Velg plassering å ta lagerbeholdning fra for denne produksjonen (la stå tomt for a ta fra alle lagerplasseringer)"

#: build/models.py:291
msgid "Destination Location"
msgstr "Fullført plassering"

#: build/models.py:296
msgid "Select location where the completed items will be stored"
msgstr "Velg plassering der fullførte artikler vil bli lagret"

#: build/models.py:300
msgid "Build Quantity"
msgstr "Produksjonsmengde"

#: build/models.py:303
msgid "Number of stock items to build"
msgstr "Antall lagervarer å produsere"

#: build/models.py:307
msgid "Completed items"
msgstr "Fullførte artikler"

#: build/models.py:309
msgid "Number of stock items which have been completed"
msgstr "Antall lagervarer som er fullført"

#: build/models.py:313
msgid "Build Status"
msgstr "Produksjonsstatus"

#: build/models.py:318
msgid "Build status code"
msgstr "Produksjonsstatuskode"

#: build/models.py:327 build/serializers.py:376 order/serializers.py:785
#: stock/models.py:1023 stock/serializers.py:84 stock/serializers.py:1651
msgid "Batch Code"
msgstr "Batchkode"

#: build/models.py:331 build/serializers.py:377
msgid "Batch code for this build output"
msgstr "Batchkode for denne produksjonsartikkelen"

#: build/models.py:335 order/models.py:466 order/serializers.py:166
#: part/models.py:1286
msgid "Creation Date"
msgstr "Opprettelsesdato"

#: build/models.py:341
msgid "Build start date"
msgstr ""

#: build/models.py:342
msgid "Scheduled start date for this build order"
msgstr ""

#: build/models.py:348
msgid "Target completion date"
msgstr "Forventet sluttdato"

#: build/models.py:350
msgid "Target date for build completion. Build will be overdue after this date."
msgstr "Måldato for ferdigstillelse. Produksjonen vil være forfalt etter denne datoen."

#: build/models.py:355 order/models.py:654 order/models.py:2442
msgid "Completion Date"
msgstr "Fullført dato"

#: build/models.py:363
msgid "completed by"
msgstr "fullført av"

#: build/models.py:372
msgid "Issued by"
msgstr "Utstedt av"

#: build/models.py:373
msgid "User who issued this build order"
msgstr "Brukeren som utstedte denne produksjonsordren"

#: build/models.py:382 common/models.py:132 order/api.py:172
#: order/models.py:491 part/models.py:1303
#: report/templates/report/inventree_build_order_report.html:158
msgid "Responsible"
msgstr "Ansvarlig"

#: build/models.py:383
msgid "User or group responsible for this build order"
msgstr "Bruker eller gruppe ansvarlig for produksjonsordren"

#: build/models.py:388 stock/models.py:1016
msgid "External Link"
msgstr "Ekstern lenke"

#: build/models.py:390 common/models.py:1899 part/models.py:1114
#: stock/models.py:1018
msgid "Link to external URL"
msgstr "Lenke til ekstern URL"

#: build/models.py:395
msgid "Build Priority"
msgstr "Produksjonsprioritet"

#: build/models.py:398
msgid "Priority of this build order"
msgstr "Produksjonsordrens prioritet"

#: build/models.py:406 common/models.py:102 common/models.py:116
#: order/api.py:158 order/models.py:438
msgid "Project Code"
msgstr "Prosjektkode"

#: build/models.py:407
msgid "Project code for this build order"
msgstr "Prosjektkode for denne produksjonsordren"

#: build/models.py:680 build/models.py:803
msgid "Failed to offload task to complete build allocations"
msgstr "Kunne ikke delegere bort oppgaven for å fullføre tildelinger"

#: build/models.py:703
#, python-brace-format
msgid "Build order {build} has been completed"
msgstr "Produksjonsordre {build} er fullført"

#: build/models.py:709
msgid "A build order has been completed"
msgstr "En produksjonsordre er fullført"

#: build/models.py:881 build/serializers.py:424
msgid "Serial numbers must be provided for trackable parts"
msgstr "Serienumre må angis for sporbare deler"

#: build/models.py:991 build/models.py:1076
msgid "No build output specified"
msgstr "Ingen produksjonsartikkel spesifisert"

#: build/models.py:994
msgid "Build output is already completed"
msgstr "Produksjonsartikkelen er allerede fullført"

#: build/models.py:997
msgid "Build output does not match Build Order"
msgstr "Produksjonsartikkelen samsvarer ikke med produksjonsordren"

#: build/models.py:1079 build/serializers.py:303 build/serializers.py:352
#: build/serializers.py:972 order/models.py:704 order/serializers.py:601
#: order/serializers.py:780 part/serializers.py:1657 stock/models.py:863
#: stock/models.py:1730 stock/serializers.py:727
msgid "Quantity must be greater than zero"
msgstr "Mengden må være større enn null"

#: build/models.py:1083 build/serializers.py:307
msgid "Quantity cannot be greater than the output quantity"
msgstr "Kvantitet kan ikke være større enn utgangsantallet"

#: build/models.py:1148 build/serializers.py:615
#, python-brace-format
msgid "Build output {serial} has not passed all required tests"
msgstr "Produksjonsartikkel {serial} har ikke bestått alle påkrevde tester"

#: build/models.py:1534
msgid "Build Order Line Item"
msgstr "Produksjonsartikkel"

#: build/models.py:1558
msgid "Build object"
msgstr "Produksjonsobjekt"

#: build/models.py:1570 build/models.py:1829 build/serializers.py:291
#: build/serializers.py:337 build/serializers.py:1410 common/models.py:1255
#: order/models.py:1555 order/models.py:2288 order/serializers.py:1670
#: order/serializers.py:2131 part/models.py:3353 part/models.py:4321
#: part/serializers.py:275
#: report/templates/report/inventree_bill_of_materials_report.html:138
#: report/templates/report/inventree_build_order_report.html:113
#: report/templates/report/inventree_purchase_order_report.html:29
#: report/templates/report/inventree_sales_order_report.html:29
#: report/templates/report/inventree_sales_order_shipment_report.html:47
#: report/templates/report/inventree_sales_order_shipment_report.html:49
#: report/templates/report/inventree_stock_location_report.html:104
#: report/templates/report/inventree_test_report.html:90
#: report/templates/report/inventree_test_report.html:169
#: stock/serializers.py:135 stock/serializers.py:179 stock/serializers.py:718
#: templates/email/build_order_completed.html:18
msgid "Quantity"
msgstr "Antall"

#: build/models.py:1571
msgid "Required quantity for build order"
msgstr "Påkrevd antall for produksjonsordre"

#: build/models.py:1655
msgid "Build item must specify a build output, as master part is marked as trackable"
msgstr "Produksjonselement må spesifisere en produksjonsartikkel, da master-del er merket som sporbar"

#: build/models.py:1666
#, python-brace-format
msgid "Allocated quantity ({q}) must not exceed available stock quantity ({a})"
msgstr "Tildelt antall ({q}) kan ikke overstige tilgjengelig lagerbeholdning ({a})"

#: build/models.py:1687 order/models.py:2237
msgid "Stock item is over-allocated"
msgstr "Lagervaren er overtildelt"

#: build/models.py:1692 order/models.py:2240
msgid "Allocation quantity must be greater than zero"
msgstr "Tildelingsantall må være større enn null"

#: build/models.py:1698
msgid "Quantity must be 1 for serialized stock"
msgstr "Mengden må være 1 for serialisert lagervare"

#: build/models.py:1758
msgid "Selected stock item does not match BOM line"
msgstr "Valgt lagervare samsvarer ikke med BOM-linjen"

#: build/models.py:1819 build/serializers.py:955 build/serializers.py:1263
#: order/serializers.py:1507 order/serializers.py:1528
#: report/templates/report/inventree_sales_order_shipment_report.html:29
#: stock/models.py:452 stock/serializers.py:101 stock/serializers.py:825
#: stock/serializers.py:1357 stock/serializers.py:1469
msgid "Stock Item"
msgstr "Lagervare"

#: build/models.py:1820
msgid "Source stock item"
msgstr "Kildelagervare"

#: build/models.py:1830
msgid "Stock quantity to allocate to build"
msgstr "Lagerantall å tildele til produksjonen"

#: build/models.py:1839
msgid "Install into"
msgstr "Monteres i"

#: build/models.py:1840
msgid "Destination stock item"
msgstr "Lagervare for montering"

#: build/serializers.py:116
msgid "Build Level"
msgstr ""

#: build/serializers.py:125 part/stocktake.py:219
msgid "Part Name"
msgstr "Delnavn"

#: build/serializers.py:143
msgid "Project Code Label"
msgstr "Etikett for prosjektkode"

#: build/serializers.py:155
msgid "Create Child Builds"
msgstr ""

#: build/serializers.py:156
msgid "Automatically generate child build orders"
msgstr ""

#: build/serializers.py:239 build/serializers.py:981
msgid "Build Output"
msgstr "Produksjonsartikkel"

#: build/serializers.py:251
msgid "Build output does not match the parent build"
msgstr "Produksjonsartikkel samsvarer ikke med overordnet produksjon"

#: build/serializers.py:255
msgid "Output part does not match BuildOrder part"
msgstr "Resultatdel samsvarer ikke med produksjonsordredel"

#: build/serializers.py:259
msgid "This build output has already been completed"
msgstr "Denne produksjonsartikkelen er allerede fullført"

#: build/serializers.py:273
msgid "This build output is not fully allocated"
msgstr "Denne produksjonsartikkelen er ikke fullt tildelt"

#: build/serializers.py:292 build/serializers.py:338
msgid "Enter quantity for build output"
msgstr "Angi antall for produksjonsartikkel"

#: build/serializers.py:360
msgid "Integer quantity required for trackable parts"
msgstr "Heltallsverdi kreves for sporbare deler"

#: build/serializers.py:366
msgid "Integer quantity required, as the bill of materials contains trackable parts"
msgstr "Heltallsverdi kreves, da stykklisten inneholder sporbare deler"

#: build/serializers.py:383 order/serializers.py:801 order/serializers.py:1674
#: stock/serializers.py:738
msgid "Serial Numbers"
msgstr "Serienummer"

#: build/serializers.py:384
msgid "Enter serial numbers for build outputs"
msgstr "Angi serienummer for produksjonsartikler"

#: build/serializers.py:389 build/serializers.py:514 build/serializers.py:584
#: build/serializers.py:1267 build/serializers.py:1271 order/serializers.py:769
#: order/serializers.py:916 order/serializers.py:2022 part/serializers.py:1275
#: stock/serializers.py:110 stock/serializers.py:647 stock/serializers.py:749
#: stock/serializers.py:913 stock/serializers.py:1041 stock/serializers.py:1501
#: stock/serializers.py:1789 users/models.py:551
msgid "Location"
msgstr "Plassering"

#: build/serializers.py:390
msgid "Stock location for build output"
msgstr "Lagerplassering for produksjonsartikkel"

#: build/serializers.py:405
msgid "Auto Allocate Serial Numbers"
msgstr "Automatisk tildeling av serienummer"

#: build/serializers.py:407
msgid "Automatically allocate required items with matching serial numbers"
msgstr "Automatisk tildeling av nødvendige artikler med tilsvarende serienummer"

#: build/serializers.py:440 order/serializers.py:894 stock/api.py:1077
#: stock/models.py:1753
msgid "The following serial numbers already exist or are invalid"
msgstr "Følgende serienummer finnes allerede eller er ugyldige"

#: build/serializers.py:482 build/serializers.py:538 build/serializers.py:622
msgid "A list of build outputs must be provided"
msgstr "En liste over produksjonsartikler må oppgis"

#: build/serializers.py:515
msgid "Stock location for scrapped outputs"
msgstr "Lagerplassering for skrotede produksjonsartikler"

#: build/serializers.py:521
msgid "Discard Allocations"
msgstr "Forkast tildelinger"

#: build/serializers.py:522
msgid "Discard any stock allocations for scrapped outputs"
msgstr "Forkast tildelinger fra skrotede produksjonsartikler"

#: build/serializers.py:527
msgid "Reason for scrapping build output(s)"
msgstr "Grunn for skroting av produksjonsartikler"

#: build/serializers.py:585
msgid "Location for completed build outputs"
msgstr "Plassering for ferdige produksjonsartikler"

#: build/serializers.py:593
msgid "Accept Incomplete Allocation"
msgstr "Godta ufullstendig tildeling"

#: build/serializers.py:594
msgid "Complete outputs if stock has not been fully allocated"
msgstr "Fullfør artikler dersom lagerbeholdning ikke er fullt tildelt"

#: build/serializers.py:709
msgid "Consume Allocated Stock"
msgstr "Bruk tildelt lagerbeholdning"

#: build/serializers.py:710
msgid "Consume any stock which has already been allocated to this build"
msgstr "Bruk all lagerbeholdning som allerede er tildelt denne produksjonen"

#: build/serializers.py:716
msgid "Remove Incomplete Outputs"
msgstr "Fjern ufullstendige artikler"

#: build/serializers.py:717
msgid "Delete any build outputs which have not been completed"
msgstr "Slett alle produksjonsartikler som ikke er fullført"

#: build/serializers.py:744
msgid "Not permitted"
msgstr "Ikke tillatt"

#: build/serializers.py:745
msgid "Accept as consumed by this build order"
msgstr "Godta som brukt av denne produksjonsordren"

#: build/serializers.py:746
msgid "Deallocate before completing this build order"
msgstr "Fjern tildeling før produksjonsordren fullføres"

#: build/serializers.py:773
msgid "Overallocated Stock"
msgstr "Overtildelt lagerbeholdning"

#: build/serializers.py:776
msgid "How do you want to handle extra stock items assigned to the build order"
msgstr "Hvordan vil du håndtere ekstra lagervarer tildelt produksjonsordren"

#: build/serializers.py:787
msgid "Some stock items have been overallocated"
msgstr "Noen lagervarer har blitt overtildelt"

#: build/serializers.py:792
msgid "Accept Unallocated"
msgstr "Godta ikke tildelt"

#: build/serializers.py:794
msgid "Accept that stock items have not been fully allocated to this build order"
msgstr "Godta at lagervarer ikke er fullt tildelt til denne produksjonsordren"

#: build/serializers.py:805
msgid "Required stock has not been fully allocated"
msgstr "Nøvendig lagerbeholdning er ikke fullt tildelt"

#: build/serializers.py:810 order/serializers.py:444 order/serializers.py:1575
msgid "Accept Incomplete"
msgstr "Godta uferdig"

#: build/serializers.py:812
msgid "Accept that the required number of build outputs have not been completed"
msgstr "Godta at nødvendig antall fullførte produksjonsartikler ikke er nådd"

#: build/serializers.py:823
msgid "Required build quantity has not been completed"
msgstr "Nødvendig produksjonsmengde er ikke nådd"

#: build/serializers.py:835
msgid "Build order has open child build orders"
msgstr ""

#: build/serializers.py:838
msgid "Build order must be in production state"
msgstr ""

#: build/serializers.py:841
msgid "Build order has incomplete outputs"
msgstr "Produksjonsordren har uferdige artikler"

#: build/serializers.py:880
msgid "Build Line"
msgstr "Produksjonslinje"

#: build/serializers.py:888
msgid "Build output"
msgstr "Produksjonsartikkel"

#: build/serializers.py:896
msgid "Build output must point to the same build"
msgstr "Produksjonsartikkel må peke til samme produksjon"

#: build/serializers.py:927
msgid "Build Line Item"
msgstr "Produksjonsartikkel"

#: build/serializers.py:945
msgid "bom_item.part must point to the same part as the build order"
msgstr "bom_item.part må peke på den samme delen som produksjonsordren"

#: build/serializers.py:961 stock/serializers.py:1370
msgid "Item must be in stock"
msgstr "Artikkelen må være på lager"

#: build/serializers.py:1004 order/serializers.py:1561
#, python-brace-format
msgid "Available quantity ({q}) exceeded"
msgstr "Tilgjengelig antall ({q}) overskredet"

#: build/serializers.py:1010
msgid "Build output must be specified for allocation of tracked parts"
msgstr "Produksjonsartikkel må spesifiseres for tildeling av sporede deler"

#: build/serializers.py:1018
msgid "Build output cannot be specified for allocation of untracked parts"
msgstr "Produksjonsartikkel kan ikke spesifiseres for tildeling av usporede deler"

#: build/serializers.py:1042 order/serializers.py:1834
msgid "Allocation items must be provided"
msgstr "Tildelingsartikler må oppgis"

#: build/serializers.py:1106
msgid "Stock location where parts are to be sourced (leave blank to take from any location)"
msgstr "Lagerplassering hvor deler skal hentes (la stå tomt for å ta fra alle plasseringer)"

#: build/serializers.py:1115
msgid "Exclude Location"
msgstr "Eksluderer plassering"

#: build/serializers.py:1116
msgid "Exclude stock items from this selected location"
msgstr "Ekskluder lagervarer fra denne valgte plasseringen"

#: build/serializers.py:1121
msgid "Interchangeable Stock"
msgstr "Utskiftbar lagerbeholdning"

#: build/serializers.py:1122
msgid "Stock items in multiple locations can be used interchangeably"
msgstr "Lagervarer ved flere plasseringer kan brukes om hverandre"

#: build/serializers.py:1127
msgid "Substitute Stock"
msgstr "Erstatning-lagerbeholdning"

#: build/serializers.py:1128
msgid "Allow allocation of substitute parts"
msgstr "Tilatt tildelling av erstatningsdeler"

#: build/serializers.py:1133
msgid "Optional Items"
msgstr "Valgfrie artikler"

#: build/serializers.py:1134
msgid "Allocate optional BOM items to build order"
msgstr "Tildel valgfrie BOM-artikler til produksjonsordre"

#: build/serializers.py:1156
msgid "Failed to start auto-allocation task"
msgstr "Kunne ikke starte auto-tideling"

#: build/serializers.py:1230
msgid "BOM Reference"
msgstr "BOM-referanse"

#: build/serializers.py:1236
msgid "BOM Part ID"
msgstr ""

#: build/serializers.py:1243
msgid "BOM Part Name"
msgstr ""

#: build/serializers.py:1278 build/serializers.py:1434
msgid "Build"
msgstr ""

#: build/serializers.py:1286 company/models.py:662 order/api.py:304
#: order/api.py:309 order/api.py:508 order/serializers.py:593
#: stock/models.py:959 stock/serializers.py:631
msgid "Supplier Part"
msgstr "Leverandørdel"

#: build/serializers.py:1294 stock/serializers.py:662
msgid "Allocated Quantity"
msgstr "Tildelt antall"

#: build/serializers.py:1371
msgid "Build Reference"
msgstr "Produksjonsreferanse"

#: build/serializers.py:1381
msgid "Part Category Name"
msgstr "Delkategorinavn"

#: build/serializers.py:1401 common/setting/system.py:453 part/models.py:1223
msgid "Trackable"
msgstr "Sporbar"

#: build/serializers.py:1404
msgid "Inherited"
msgstr "Nedarvet"

#: build/serializers.py:1407 part/models.py:4381
msgid "Allow Variants"
msgstr "Tillat Varianter"

#: build/serializers.py:1412 build/serializers.py:1416 part/models.py:4179
#: part/models.py:4657 stock/api.py:831
msgid "BOM Item"
msgstr "BOM-artikkel"

#: build/serializers.py:1445
msgid "Allocated Stock"
msgstr "Tildelt lagerbeholdning"

#: build/serializers.py:1447 company/serializers.py:424
#: order/serializers.py:1271 part/serializers.py:943 part/serializers.py:1690
msgid "On Order"
msgstr "I bestilling"

#: build/serializers.py:1449 order/serializers.py:1272 part/serializers.py:1694
msgid "In Production"
msgstr "I produksjon"

#: build/serializers.py:1451 part/serializers.py:964
msgid "External Stock"
msgstr "Ekstern lagerbeholdning"

#: build/serializers.py:1452 part/serializers.py:1723
msgid "Available Stock"
msgstr "Tilgjengelig lagerbeholdning"

#: build/serializers.py:1454
msgid "Available Substitute Stock"
msgstr "Tilgjengelige erstatningsvarer"

#: build/serializers.py:1457
msgid "Available Variant Stock"
msgstr "Tilgjengelige variantvarer"

#: build/status_codes.py:11 generic/states/tests.py:21
#: generic/states/tests.py:131 order/status_codes.py:12
#: order/status_codes.py:44 order/status_codes.py:76 order/status_codes.py:102
msgid "Pending"
msgstr "Ventende"

#: build/status_codes.py:12
msgid "Production"
msgstr "Produksjon"

#: build/status_codes.py:13 order/status_codes.py:14 order/status_codes.py:51
#: order/status_codes.py:81
msgid "On Hold"
msgstr ""

#: build/status_codes.py:14 order/status_codes.py:16 order/status_codes.py:53
#: order/status_codes.py:84
msgid "Cancelled"
msgstr "Kansellert"

#: build/status_codes.py:15 generic/states/tests.py:23 importer/models.py:529
#: importer/status_codes.py:27 order/status_codes.py:15
#: order/status_codes.py:52 order/status_codes.py:83
msgid "Complete"
msgstr "Fullført"

#: build/tasks.py:193
msgid "Stock required for build order"
msgstr "Lagerbeholdning kreves for produksjonsordre"

#: build/tasks.py:267
msgid "Overdue Build Order"
msgstr "Forfalt produksjonsordre"

#: build/tasks.py:272
#, python-brace-format
msgid "Build order {bo} is now overdue"
msgstr "Produksjonsordre {bo} er nå forfalt"

#: common/api.py:710
msgid "Is Link"
msgstr "Er lenke"

#: common/api.py:718
msgid "Is File"
msgstr "Er fil"

#: common/api.py:761
msgid "User does not have permission to delete these attachments"
msgstr ""

#: common/api.py:778
msgid "User does not have permission to delete this attachment"
msgstr "Brukeren har ikke tillatelse til å slette dette vedlegget"

#: common/currency.py:120
msgid "Invalid currency code"
msgstr "Ugyldig valutakode"

#: common/currency.py:122
msgid "Duplicate currency code"
msgstr "Valutakode eksisterer allerede"

#: common/currency.py:127
msgid "No valid currency codes provided"
msgstr "Ingen gyldige valutakoder angitt"

#: common/currency.py:144
msgid "No plugin"
msgstr "Ingen programtillegg"

#: common/models.py:89
msgid "Updated"
msgstr "Oppdatert"

#: common/models.py:90
msgid "Timestamp of last update"
msgstr "Tidsstempel for forrige oppdatering"

#: common/models.py:117
msgid "Unique project code"
msgstr "Unik prosjektkode"

#: common/models.py:124
msgid "Project description"
msgstr "Prosjektbeskrivelse"

#: common/models.py:133
msgid "User or group responsible for this project"
msgstr "Bruker eller gruppe ansvarlig for dette prosjektet"

#: common/models.py:721 common/models.py:1187 common/models.py:1225
msgid "Settings key"
msgstr ""

#: common/models.py:725
msgid "Settings value"
msgstr "Innstillings verdi"

#: common/models.py:780
msgid "Chosen value is not a valid option"
msgstr "Valgt verdi er ikke et gyldig alternativ"

#: common/models.py:796
msgid "Value must be a boolean value"
msgstr "Verdien må være en boolsk verdi"

#: common/models.py:804
msgid "Value must be an integer value"
msgstr "Verdien må være et heltall"

#: common/models.py:812
msgid "Value must be a valid number"
msgstr ""

#: common/models.py:837
msgid "Value does not pass validation checks"
msgstr ""

#: common/models.py:859
msgid "Key string must be unique"
msgstr "Nøkkelstreng må være unik"

#: common/models.py:1233 common/models.py:1234 common/models.py:1338
#: common/models.py:1339 common/models.py:1584 common/models.py:1585
#: common/models.py:1915 common/models.py:1916 common/models.py:2297
#: importer/models.py:97 part/models.py:3376 part/models.py:3463
#: part/models.py:3537 part/models.py:3565 plugin/models.py:322
#: plugin/models.py:323 report/templates/report/inventree_test_report.html:105
#: users/models.py:130 users/models.py:503
msgid "User"
msgstr "Bruker"

#: common/models.py:1256
msgid "Price break quantity"
msgstr "Antall for prisbrudd"

#: common/models.py:1263 company/serializers.py:567 order/models.py:1632
#: order/models.py:2734
msgid "Price"
msgstr "Pris"

#: common/models.py:1264
msgid "Unit price at specified quantity"
msgstr "Enhetspris på spesifisert antall"

#: common/models.py:1315 common/models.py:1500
msgid "Endpoint"
msgstr "Endepunkt"

#: common/models.py:1316
msgid "Endpoint at which this webhook is received"
msgstr "Endepunktet hvor denne webhooken er mottatt"

#: common/models.py:1326
msgid "Name for this webhook"
msgstr "Navn for webhooken"

#: common/models.py:1330 common/models.py:2156 common/models.py:2263
#: company/models.py:164 company/models.py:799 machine/models.py:39
#: part/models.py:1246 plugin/models.py:68 stock/api.py:591 users/models.py:201
#: users/models.py:556 users/serializers.py:333
msgid "Active"
msgstr "Aktiv"

#: common/models.py:1330
msgid "Is this webhook active"
msgstr "Er webhooken aktiv"

#: common/models.py:1346 users/models.py:178
msgid "Token"
msgstr "Sjetong"

#: common/models.py:1347
msgid "Token for access"
msgstr "Nøkkel for tilgang"

#: common/models.py:1355
msgid "Secret"
msgstr "Hemmelig"

#: common/models.py:1356
msgid "Shared secret for HMAC"
msgstr "Delt hemmlighet for HMAC"

#: common/models.py:1464
msgid "Message ID"
msgstr "Melding ID"

#: common/models.py:1465
msgid "Unique identifier for this message"
msgstr "Unik Id for denne meldingen"

#: common/models.py:1473
msgid "Host"
msgstr "Vert"

#: common/models.py:1474
msgid "Host from which this message was received"
msgstr "Verten denne meldingen ble mottatt fra"

#: common/models.py:1482
msgid "Header"
msgstr "Tittel"

#: common/models.py:1483
msgid "Header of this message"
msgstr "Overskrift for denne meldingen"

#: common/models.py:1490
msgid "Body"
msgstr "Brødtekst"

#: common/models.py:1491
msgid "Body of this message"
msgstr "Innholdet i meldingen"

#: common/models.py:1501
msgid "Endpoint on which this message was received"
msgstr "Endepunktet meldingen ble mottatt fra"

#: common/models.py:1506
msgid "Worked on"
msgstr "Arbeidet med"

#: common/models.py:1507
msgid "Was the work on this message finished?"
msgstr "Var arbeidet med denne meldingen ferdig?"

#: common/models.py:1633
msgid "Id"
msgstr ""

#: common/models.py:1635 part/serializers.py:281
msgid "Title"
msgstr "Tittel"

#: common/models.py:1637 common/models.py:1898 company/models.py:149
#: company/models.py:445 company/models.py:512 company/models.py:816
#: order/models.py:444 order/models.py:1585 order/models.py:2056
#: part/models.py:1113
#: report/templates/report/inventree_build_order_report.html:164
msgid "Link"
msgstr "Lenke"

#: common/models.py:1639
msgid "Published"
msgstr "Publisert"

#: common/models.py:1641
msgid "Author"
msgstr "Forfatter"

#: common/models.py:1643
msgid "Summary"
msgstr "Sammendrag"

#: common/models.py:1646
msgid "Read"
msgstr "Les"

#: common/models.py:1646
msgid "Was this news item read?"
msgstr "Er dette nyhetselementet lest?"

#: common/models.py:1663 company/models.py:160 part/models.py:1124
#: report/templates/report/inventree_bill_of_materials_report.html:126
#: report/templates/report/inventree_bill_of_materials_report.html:148
#: report/templates/report/inventree_return_order_report.html:35
msgid "Image"
msgstr "Bilde"

#: common/models.py:1663
msgid "Image file"
msgstr "Bildefil"

#: common/models.py:1675
msgid "Target model type for this image"
msgstr ""

#: common/models.py:1679
msgid "Target model ID for this image"
msgstr ""

#: common/models.py:1701
msgid "Custom Unit"
msgstr ""

#: common/models.py:1719
msgid "Unit symbol must be unique"
msgstr "Enhetssymbolet må være unikt"

#: common/models.py:1734
msgid "Unit name must be a valid identifier"
msgstr "Enhetsnavn må være en gyldig identifikator"

#: common/models.py:1753
msgid "Unit name"
msgstr "Enhetsnavn"

#: common/models.py:1760
msgid "Symbol"
msgstr "Symbol"

#: common/models.py:1761
msgid "Optional unit symbol"
msgstr "Valgfritt enhetssymbol"

#: common/models.py:1767
msgid "Definition"
msgstr "Definisjon"

#: common/models.py:1768
msgid "Unit definition"
msgstr "Enhetsdefinisjon"

#: common/models.py:1826 common/models.py:1889 stock/models.py:2827
#: stock/serializers.py:258
msgid "Attachment"
msgstr "Vedlegg"

#: common/models.py:1843
msgid "Missing file"
msgstr "Fil mangler"

#: common/models.py:1844
msgid "Missing external link"
msgstr "Mangler eksternlenke"

#: common/models.py:1881
msgid "Model type"
msgstr ""

#: common/models.py:1882
msgid "Target model type for image"
msgstr ""

#: common/models.py:1890
msgid "Select file to attach"
msgstr "Velg fil å legge ved"

#: common/models.py:1906
msgid "Comment"
msgstr "Kommentar"

#: common/models.py:1907
msgid "Attachment comment"
msgstr "Vedleggskommentar"

#: common/models.py:1923
msgid "Upload date"
msgstr "Opplastet dato"

#: common/models.py:1924
msgid "Date the file was uploaded"
msgstr "Datoen som filen ble lastet opp"

#: common/models.py:1928
msgid "File size"
msgstr "Filstørrelse"

#: common/models.py:1928
msgid "File size in bytes"
msgstr "Filstørrelse i byte"

#: common/models.py:1966 common/serializers.py:637
msgid "Invalid model type specified for attachment"
msgstr "Ugyldig modelltype spesifisert for vedlegg"

#: common/models.py:1987
msgid "Custom State"
msgstr ""

#: common/models.py:1988
msgid "Custom States"
msgstr ""

#: common/models.py:1993
msgid "Reference Status Set"
msgstr ""

#: common/models.py:1994
msgid "Status set that is extended with this custom state"
msgstr ""

#: common/models.py:1998 generic/states/serializers.py:18
msgid "Logical Key"
msgstr ""

#: common/models.py:2000
msgid "State logical key that is equal to this custom state in business logic"
msgstr ""

#: common/models.py:2005 common/models.py:2244 company/models.py:600
#: report/templates/report/inventree_test_report.html:104 stock/models.py:2819
msgid "Value"
msgstr "Verdi"

#: common/models.py:2006
msgid "Numerical value that will be saved in the models database"
msgstr ""

#: common/models.py:2012
msgid "Name of the state"
msgstr ""

#: common/models.py:2021 common/models.py:2250 generic/states/serializers.py:22
#: part/serializers.py:283
msgid "Label"
msgstr ""

#: common/models.py:2022
msgid "Label that will be displayed in the frontend"
msgstr ""

#: common/models.py:2029 generic/states/serializers.py:24
msgid "Color"
msgstr ""

#: common/models.py:2030
msgid "Color that will be displayed in the frontend"
msgstr ""

#: common/models.py:2038 part/serializers.py:285
msgid "Model"
msgstr ""

#: common/models.py:2039
msgid "Model this state is associated with"
msgstr ""

#: common/models.py:2054
msgid "Model must be selected"
msgstr ""

#: common/models.py:2057
msgid "Key must be selected"
msgstr ""

#: common/models.py:2060
msgid "Logical key must be selected"
msgstr ""

#: common/models.py:2064
msgid "Key must be different from logical key"
msgstr ""

#: common/models.py:2071
msgid "Valid reference status class must be provided"
msgstr ""

#: common/models.py:2077
msgid "Key must be different from the logical keys of the reference status"
msgstr ""

#: common/models.py:2084
msgid "Logical key must be in the logical keys of the reference status"
msgstr ""

#: common/models.py:2091
msgid "Name must be different from the names of the reference status"
msgstr ""

#: common/models.py:2131 common/models.py:2238 part/models.py:3876
msgid "Selection List"
msgstr ""

#: common/models.py:2132
msgid "Selection Lists"
msgstr ""

#: common/models.py:2137
msgid "Name of the selection list"
msgstr ""

#: common/models.py:2144
msgid "Description of the selection list"
msgstr ""

#: common/models.py:2150 part/models.py:1251
msgid "Locked"
msgstr ""

#: common/models.py:2151
msgid "Is this selection list locked?"
msgstr ""

#: common/models.py:2157
msgid "Can this selection list be used?"
msgstr ""

#: common/models.py:2165
msgid "Source Plugin"
msgstr ""

#: common/models.py:2166
msgid "Plugin which provides the selection list"
msgstr ""

#: common/models.py:2171
msgid "Source String"
msgstr ""

#: common/models.py:2172
msgid "Optional string identifying the source used for this list"
msgstr ""

#: common/models.py:2181
msgid "Default Entry"
msgstr ""

#: common/models.py:2182
msgid "Default entry for this selection list"
msgstr ""

#: common/models.py:2187
msgid "Created"
msgstr "Opprettet"

#: common/models.py:2188
msgid "Date and time that the selection list was created"
msgstr ""

#: common/models.py:2193
msgid "Last Updated"
msgstr "Sist oppdatert"

#: common/models.py:2194
msgid "Date and time that the selection list was last updated"
msgstr ""

#: common/models.py:2228
msgid "Selection List Entry"
msgstr ""

#: common/models.py:2229
msgid "Selection List Entries"
msgstr ""

#: common/models.py:2239
msgid "Selection list to which this entry belongs"
msgstr ""

#: common/models.py:2245
msgid "Value of the selection list entry"
msgstr ""

#: common/models.py:2251
msgid "Label for the selection list entry"
msgstr ""

#: common/models.py:2257
msgid "Description of the selection list entry"
msgstr ""

#: common/models.py:2264
msgid "Is this selection list entry active?"
msgstr ""

#: common/models.py:2282
msgid "Barcode Scan"
msgstr ""

#: common/models.py:2286 importer/models.py:523 part/models.py:4041
msgid "Data"
msgstr ""

#: common/models.py:2287
msgid "Barcode data"
msgstr ""

#: common/models.py:2298
msgid "User who scanned the barcode"
msgstr ""

#: common/models.py:2303 importer/models.py:66
msgid "Timestamp"
msgstr ""

#: common/models.py:2304
msgid "Date and time of the barcode scan"
msgstr ""

#: common/models.py:2310
msgid "URL endpoint which processed the barcode"
msgstr ""

#: common/models.py:2317 order/models.py:1622 plugin/serializers.py:93
msgid "Context"
msgstr "Kontekst"

#: common/models.py:2318
msgid "Context data for the barcode scan"
msgstr ""

#: common/models.py:2325
msgid "Response"
msgstr ""

#: common/models.py:2326
msgid "Response data from the barcode scan"
msgstr ""

#: common/models.py:2332 report/templates/report/inventree_test_report.html:103
#: stock/models.py:2813
msgid "Result"
msgstr "Resultat"

#: common/models.py:2333
msgid "Was the barcode scan successful?"
msgstr ""

#: common/notifications.py:332
#, python-brace-format
msgid "New {verbose_name}"
msgstr "Ny {verbose_name}"

#: common/notifications.py:334
msgid "A new order has been created and assigned to you"
msgstr "En ny ordre har blitt opprettet og tilordnet til deg"

#: common/notifications.py:340
#, python-brace-format
msgid "{verbose_name} canceled"
msgstr "{verbose_name} kansellert"

#: common/notifications.py:342
msgid "A order that is assigned to you was canceled"
msgstr "En ordre som er tildelt til deg ble kansellert"

#: common/notifications.py:348 common/notifications.py:355 order/api.py:559
msgid "Items Received"
msgstr "Artikler mottatt"

#: common/notifications.py:350
msgid "Items have been received against a purchase order"
msgstr "Artikler har blitt mottatt mot en innkjøpsordre"

#: common/notifications.py:357
msgid "Items have been received against a return order"
msgstr "Artikler har blitt mottatt mot en returordre"

#: common/notifications.py:505
msgid "Error raised by plugin"
msgstr "Feil oppstått i utvidelse"

#: common/serializers.py:451
msgid "Is Running"
msgstr "Kjører"

#: common/serializers.py:457
msgid "Pending Tasks"
msgstr "Ventende oppgaver"

#: common/serializers.py:463
msgid "Scheduled Tasks"
msgstr "Planlagte oppgaver"

#: common/serializers.py:469
msgid "Failed Tasks"
msgstr "Mislykkede oppgaver"

#: common/serializers.py:484
msgid "Task ID"
msgstr "Oppgave-ID"

#: common/serializers.py:484
msgid "Unique task ID"
msgstr "Unik oppgave-ID"

#: common/serializers.py:486
msgid "Lock"
msgstr "Lås"

#: common/serializers.py:486
msgid "Lock time"
msgstr "Låsetidspunkt"

#: common/serializers.py:488
msgid "Task name"
msgstr "Oppgavenavn"

#: common/serializers.py:490
msgid "Function"
msgstr "Funksjon"

#: common/serializers.py:490
msgid "Function name"
msgstr "Funksjonsnavn"

#: common/serializers.py:492
msgid "Arguments"
msgstr "Argumenter"

#: common/serializers.py:492
msgid "Task arguments"
msgstr "Oppgaveargumenter"

#: common/serializers.py:495
msgid "Keyword Arguments"
msgstr "Nøkkelordargumenter"

#: common/serializers.py:495
msgid "Task keyword arguments"
msgstr "Nøkkelordargumenter for oppgave"

#: common/serializers.py:605
msgid "Filename"
msgstr "Filnavn"

#: common/serializers.py:612 importer/models.py:86 report/api.py:41
#: report/models.py:277 report/serializers.py:53
msgid "Model Type"
msgstr "Modelltype"

#: common/serializers.py:640
msgid "User does not have permission to create or edit attachments for this model"
msgstr "Brukeren har ikke tillatelse tillatelse å opprette eller endre vedlegg for denne modellen"

#: common/serializers.py:684 common/serializers.py:787
msgid "Selection list is locked"
msgstr ""

#: common/setting/system.py:97
msgid "No group"
msgstr "Ingen gruppe"

#: common/setting/system.py:156
msgid "Site URL is locked by configuration"
msgstr "Nettstedets URL er låst av konfigurasjon"

#: common/setting/system.py:167
msgid "Restart required"
msgstr "Omstart kreves"

#: common/setting/system.py:168
msgid "A setting has been changed which requires a server restart"
msgstr "En innstilling har blitt endret som krever en omstart av serveren"

#: common/setting/system.py:174
msgid "Pending migrations"
msgstr "Ventende migrasjoner"

#: common/setting/system.py:175
msgid "Number of pending database migrations"
msgstr "Antall ventende databasemigreringer"

#: common/setting/system.py:180
msgid "Instance ID"
msgstr ""

#: common/setting/system.py:181
msgid "Unique identifier for this InvenTree instance"
msgstr ""

#: common/setting/system.py:186
msgid "Announce ID"
msgstr ""

#: common/setting/system.py:188
msgid "Announce the instance ID of the server in the server status info (unauthenticated)"
msgstr ""

#: common/setting/system.py:194
msgid "Server Instance Name"
msgstr "Navn på serverinstans"

#: common/setting/system.py:196
msgid "String descriptor for the server instance"
msgstr "Strengbeskrivelse for serverinstansen"

#: common/setting/system.py:200
msgid "Use instance name"
msgstr "Bruk instansnavn"

#: common/setting/system.py:201
msgid "Use the instance name in the title-bar"
msgstr "Bruk instansnavnet på tittellinjen"

#: common/setting/system.py:206
msgid "Restrict showing `about`"
msgstr "Begrens visning av 'om'"

#: common/setting/system.py:207
msgid "Show the `about` modal only to superusers"
msgstr "Vis `about`-modal kun til superbrukere"

#: common/setting/system.py:212 company/models.py:108 company/models.py:109
msgid "Company name"
msgstr "Firmanavn"

#: common/setting/system.py:213
msgid "Internal company name"
msgstr "Internt firmanavn"

#: common/setting/system.py:217
msgid "Base URL"
msgstr "Base-URL"

#: common/setting/system.py:218
msgid "Base URL for server instance"
msgstr "Base-URL for serverinstans"

#: common/setting/system.py:224
msgid "Default Currency"
msgstr "Standardvaluta"

#: common/setting/system.py:225
msgid "Select base currency for pricing calculations"
msgstr "Velg grunnvalutaen for prisberegninger"

#: common/setting/system.py:231
msgid "Supported Currencies"
msgstr "Støttede valutaer"

#: common/setting/system.py:232
msgid "List of supported currency codes"
msgstr "Liste over støttede valutakoder"

#: common/setting/system.py:238
msgid "Currency Update Interval"
msgstr "Oppdateringsintervall for valuta"

#: common/setting/system.py:239
msgid "How often to update exchange rates (set to zero to disable)"
msgstr "Hvor ofte valutakurser skal oppdateres (sett til null for å deaktiverere)"

#: common/setting/system.py:241 common/setting/system.py:281
#: common/setting/system.py:294 common/setting/system.py:302
#: common/setting/system.py:309 common/setting/system.py:318
#: common/setting/system.py:567 common/setting/system.py:587
#: common/setting/system.py:684 common/setting/system.py:1068
msgid "days"
msgstr "dager"

#: common/setting/system.py:245
msgid "Currency Update Plugin"
msgstr "Valutaoppdaterings-plugin"

#: common/setting/system.py:246
msgid "Currency update plugin to use"
msgstr "Valgt valutaoppdaterings-plugin"

#: common/setting/system.py:251
msgid "Download from URL"
msgstr "Last ned fra URL"

#: common/setting/system.py:252
msgid "Allow download of remote images and files from external URL"
msgstr "Tillat nedlastning av eksterne bilder og filer fra ekstern URL"

#: common/setting/system.py:257
msgid "Download Size Limit"
msgstr "Nedlastingsgrense"

#: common/setting/system.py:258
msgid "Maximum allowable download size for remote image"
msgstr "Maksimal tillatt nedlastingsstørrelse for eksternt bilde"

#: common/setting/system.py:264
msgid "User-agent used to download from URL"
msgstr "User-Agent brukt for å laste ned fra URL"

#: common/setting/system.py:266
msgid "Allow to override the user-agent used to download images and files from external URL (leave blank for the default)"
msgstr "Tillat overstyring av User-Agent brukt for å laste ned bilder og filer fra eksterne URLer (lå stå blank for standard)"

#: common/setting/system.py:271
msgid "Strict URL Validation"
msgstr "Streng URL-validering"

#: common/setting/system.py:272
msgid "Require schema specification when validating URLs"
msgstr "Krev skjemaspesifikasjon ved validering av URLer"

#: common/setting/system.py:277
msgid "Update Check Interval"
msgstr "Intervall for oppdateringssjekk"

#: common/setting/system.py:278
msgid "How often to check for updates (set to zero to disable)"
msgstr "Tidsintervall for å se etter oppdateringer(sett til null for å skru av)"

#: common/setting/system.py:284
msgid "Automatic Backup"
msgstr "Automatisk sikkerhetskopiering"

#: common/setting/system.py:285
msgid "Enable automatic backup of database and media files"
msgstr "Aktiver automatisk sikkerhetskopiering av database og mediafiler"

#: common/setting/system.py:290
msgid "Auto Backup Interval"
msgstr "Automatisk sikkerhetskopieringsintervall"

#: common/setting/system.py:291
msgid "Specify number of days between automated backup events"
msgstr "Angi antall dager mellom automatiske sikkerhetskopieringshendelser"

#: common/setting/system.py:297
msgid "Task Deletion Interval"
msgstr "Slettingsintervall for oppgaver"

#: common/setting/system.py:299
msgid "Background task results will be deleted after specified number of days"
msgstr "Bakgrunnsoppgaveresultater vil bli slettet etter antall angitte dager"

#: common/setting/system.py:306
msgid "Error Log Deletion Interval"
msgstr "Slettingsintervall for feillogg"

#: common/setting/system.py:307
msgid "Error logs will be deleted after specified number of days"
msgstr "Feilloggene vil bli slettet etter et angitt antall dager"

#: common/setting/system.py:313
msgid "Notification Deletion Interval"
msgstr "Slettingsintervall for varsler"

#: common/setting/system.py:315
msgid "User notifications will be deleted after specified number of days"
msgstr "Brukervarsler slettes etter angitt antall dager"

#: common/setting/system.py:322
msgid "Barcode Support"
msgstr "Strekkodestøtte"

#: common/setting/system.py:323
msgid "Enable barcode scanner support in the web interface"
msgstr "Aktiver støtte for strekkodeleser i webgrensesnittet"

#: common/setting/system.py:328
msgid "Store Barcode Results"
msgstr ""

#: common/setting/system.py:329
msgid "Store barcode scan results in the database"
msgstr ""

#: common/setting/system.py:334
msgid "Barcode Scans Maximum Count"
msgstr ""

#: common/setting/system.py:335
msgid "Maximum number of barcode scan results to store"
msgstr ""

#: common/setting/system.py:340
msgid "Barcode Input Delay"
msgstr "Innlesingsforsinkelse for strekkode"

#: common/setting/system.py:341
msgid "Barcode input processing delay time"
msgstr "Tidsforsinkelse for behandling av strekkode"

#: common/setting/system.py:347
msgid "Barcode Webcam Support"
msgstr "Støtte for strekkodewebkamera"

#: common/setting/system.py:348
msgid "Allow barcode scanning via webcam in browser"
msgstr "Tillat strekkodelesning via webkamera i nettleseren"

#: common/setting/system.py:353
msgid "Barcode Show Data"
msgstr "Vis Strekkodedata"

#: common/setting/system.py:354
msgid "Display barcode data in browser as text"
msgstr "Vis strekkodedata som tekst"

#: common/setting/system.py:359
msgid "Barcode Generation Plugin"
msgstr ""

#: common/setting/system.py:360
msgid "Plugin to use for internal barcode data generation"
msgstr ""

#: common/setting/system.py:365
msgid "Part Revisions"
msgstr "Delrevisjoner"

#: common/setting/system.py:366
msgid "Enable revision field for Part"
msgstr "Aktiver revisjonsfeltet for Del"

#: common/setting/system.py:371
msgid "Assembly Revision Only"
msgstr ""

#: common/setting/system.py:372
msgid "Only allow revisions for assembly parts"
msgstr ""

#: common/setting/system.py:377
msgid "Allow Deletion from Assembly"
msgstr ""

#: common/setting/system.py:378
msgid "Allow deletion of parts which are used in an assembly"
msgstr ""

#: common/setting/system.py:383
msgid "IPN Regex"
msgstr "IPN regex"

#: common/setting/system.py:384
msgid "Regular expression pattern for matching Part IPN"
msgstr "Regulært uttrykksmønster for matching av internt delnummer"

#: common/setting/system.py:387
msgid "Allow Duplicate IPN"
msgstr "Tilat duplikat av internt delnummer"

#: common/setting/system.py:388
msgid "Allow multiple parts to share the same IPN"
msgstr "Tillat flere deler å dele samme interne delnummer"

#: common/setting/system.py:393
msgid "Allow Editing IPN"
msgstr "Tillat redigering av internt delnummer"

#: common/setting/system.py:394
msgid "Allow changing the IPN value while editing a part"
msgstr "Tillat endring av IPN-verdien mens du redigerer en del"

#: common/setting/system.py:399
msgid "Copy Part BOM Data"
msgstr "Kopier BOM-data fra del"

#: common/setting/system.py:400
msgid "Copy BOM data by default when duplicating a part"
msgstr "Kopier BOM-data som standard når du dupliserer en del"

#: common/setting/system.py:405
msgid "Copy Part Parameter Data"
msgstr "Kopier parameterdata fra del"

#: common/setting/system.py:406
msgid "Copy parameter data by default when duplicating a part"
msgstr "Kopier parameterdata som standard ved duplisering av en del"

#: common/setting/system.py:411
msgid "Copy Part Test Data"
msgstr "Kopier testdata fra del"

#: common/setting/system.py:412
msgid "Copy test data by default when duplicating a part"
msgstr "Kopier testdata som standard ved duplisering av en del"

#: common/setting/system.py:417
msgid "Copy Category Parameter Templates"
msgstr "Kopier designmaler for kategoriparametere"

#: common/setting/system.py:418
msgid "Copy category parameter templates when creating a part"
msgstr "Kopier parametermaler for kategori ved oppretting av en del"

#: common/setting/system.py:423 part/models.py:4035 report/models.py:357
#: report/models.py:563 report/serializers.py:90 report/serializers.py:131
#: stock/serializers.py:247
msgid "Template"
msgstr "Mal"

#: common/setting/system.py:424
msgid "Parts are templates by default"
msgstr "Deler er maler som standard"

#: common/setting/system.py:430
msgid "Parts can be assembled from other components by default"
msgstr "Deler kan settes sammen fra andre komponenter som standard"

#: common/setting/system.py:435 part/models.py:1217 part/serializers.py:1677
#: part/serializers.py:1683
msgid "Component"
msgstr "Komponent"

#: common/setting/system.py:436
msgid "Parts can be used as sub-components by default"
msgstr "Deler kan bli brukt som underkomponenter som standard"

#: common/setting/system.py:441 part/models.py:1235
msgid "Purchaseable"
msgstr "Kjøpbar"

#: common/setting/system.py:442
msgid "Parts are purchaseable by default"
msgstr "Deler er kjøpbare som standard"

#: common/setting/system.py:447 part/models.py:1241 stock/api.py:592
msgid "Salable"
msgstr "Salgbar"

#: common/setting/system.py:448
msgid "Parts are salable by default"
msgstr "Deler er salgbare som standard"

#: common/setting/system.py:454
msgid "Parts are trackable by default"
msgstr "Deler er sporbare som standard"

#: common/setting/system.py:459 part/models.py:1257
msgid "Virtual"
msgstr "Virtuelle"

#: common/setting/system.py:460
msgid "Parts are virtual by default"
msgstr "Deler er virtuelle som standard"

#: common/setting/system.py:465
msgid "Show Import in Views"
msgstr "Vis import i visninger"

#: common/setting/system.py:466
msgid "Display the import wizard in some part views"
msgstr "Vis importveiviseren i noen deler visninger"

#: common/setting/system.py:471
msgid "Show related parts"
msgstr "Vis relaterte deler"

#: common/setting/system.py:472
msgid "Display related parts for a part"
msgstr "Vis relaterte deler i en del"

#: common/setting/system.py:477
msgid "Initial Stock Data"
msgstr "Innledende lagerbeholdningsdata"

#: common/setting/system.py:478
msgid "Allow creation of initial stock when adding a new part"
msgstr "Tillat oppretting av innledende lagerbeholdning når en ny del opprettes"

#: common/setting/system.py:483
msgid "Initial Supplier Data"
msgstr "Innledende leverandørdata"

#: common/setting/system.py:485
msgid "Allow creation of initial supplier data when adding a new part"
msgstr "Tillat oppretting av innledende leverandørdata når en ny del opprettes"

#: common/setting/system.py:491
msgid "Part Name Display Format"
msgstr "Visningsformat for delnavn"

#: common/setting/system.py:492
msgid "Format to display the part name"
msgstr "Format for å vise delnavnet"

#: common/setting/system.py:498
msgid "Part Category Default Icon"
msgstr "Standardikon for delkategorier"

#: common/setting/system.py:499
msgid "Part category default icon (empty means no icon)"
msgstr "Standardikon for delkategorier (tomt betyr ingen ikon)"

#: common/setting/system.py:504
msgid "Enforce Parameter Units"
msgstr "Tving parameterenheter"

#: common/setting/system.py:506
msgid "If units are provided, parameter values must match the specified units"
msgstr "Hvis det er angitt en enhet, skal parameterverdiene samsvare med de angitte enhetene"

#: common/setting/system.py:512
msgid "Minimum Pricing Decimal Places"
msgstr "Minimum antall desimalplasser for priser"

#: common/setting/system.py:514
msgid "Minimum number of decimal places to display when rendering pricing data"
msgstr "Minimum antall desimalplasser som skal vises når man gjengir prisdata"

#: common/setting/system.py:525
msgid "Maximum Pricing Decimal Places"
msgstr "Maksimalt antall desimalplasser for priser"

#: common/setting/system.py:527
msgid "Maximum number of decimal places to display when rendering pricing data"
msgstr "Maksimalt antall desimalplasser som skal vises når man gjengir prisdata"

#: common/setting/system.py:538
msgid "Use Supplier Pricing"
msgstr "Bruk leverandørpriser"

#: common/setting/system.py:540
msgid "Include supplier price breaks in overall pricing calculations"
msgstr "Inkluder leverandørprisbrudd i beregninger av totalpriser"

#: common/setting/system.py:546
msgid "Purchase History Override"
msgstr "Innkjøpshistorikkoverstyring"

#: common/setting/system.py:548
msgid "Historical purchase order pricing overrides supplier price breaks"
msgstr "Historiske innkjøpspriser overstyrer leverandørprisnivåer"

#: common/setting/system.py:554
msgid "Use Stock Item Pricing"
msgstr "Bruk lagervarepriser"

#: common/setting/system.py:556
msgid "Use pricing from manually entered stock data for pricing calculations"
msgstr "Bruk priser fra manuelt innlagte lagervarer for prisberegninger"

#: common/setting/system.py:562
msgid "Stock Item Pricing Age"
msgstr "Lagervare prisalder"

#: common/setting/system.py:564
msgid "Exclude stock items older than this number of days from pricing calculations"
msgstr "Unnta lagervarer som er eldre enn dette antall dager fra prisberegninger"

#: common/setting/system.py:571
msgid "Use Variant Pricing"
msgstr "Bruk Variantpriser"

#: common/setting/system.py:572
msgid "Include variant pricing in overall pricing calculations"
msgstr "Inkluder variantpriser i beregninger av totale priser"

#: common/setting/system.py:577
msgid "Active Variants Only"
msgstr "Kun aktive varianter"

#: common/setting/system.py:579
msgid "Only use active variant parts for calculating variant pricing"
msgstr "Bruk kun aktive variantdeler til beregning av variantprising"

#: common/setting/system.py:585
msgid "Pricing Rebuild Interval"
msgstr "Intervall for rekalkulering av priser"

#: common/setting/system.py:586
msgid "Number of days before part pricing is automatically updated"
msgstr "Antall dager før delpriser blir automatisk oppdatert"

#: common/setting/system.py:592
msgid "Internal Prices"
msgstr "Interne Priser"

#: common/setting/system.py:593
msgid "Enable internal prices for parts"
msgstr "Aktiver interne priser for deler"

#: common/setting/system.py:598
msgid "Internal Price Override"
msgstr "Intern prisoverstyring"

#: common/setting/system.py:600
msgid "If available, internal prices override price range calculations"
msgstr "Hvis tilgjengelig, overstyrer interne priser kalkulering av prisområde"

#: common/setting/system.py:606
msgid "Enable label printing"
msgstr "Aktiver etikettutskrift"

#: common/setting/system.py:607
msgid "Enable label printing from the web interface"
msgstr "Aktiver utskrift av etiketter fra nettleseren"

#: common/setting/system.py:612
msgid "Label Image DPI"
msgstr "Etikettbilde-DPI"

#: common/setting/system.py:614
msgid "DPI resolution when generating image files to supply to label printing plugins"
msgstr "DPI-oppløsning når når det genereres bildefiler for sending til utvidelser for etikettutskrift"

#: common/setting/system.py:620
msgid "Enable Reports"
msgstr "Aktiver Rapporter"

#: common/setting/system.py:621
msgid "Enable generation of reports"
msgstr "Aktiver generering av rapporter"

#: common/setting/system.py:626
msgid "Debug Mode"
msgstr "Feilsøkingsmodus"

#: common/setting/system.py:627
msgid "Generate reports in debug mode (HTML output)"
msgstr "Generer rapporter i feilsøkingsmodus (HTML-output)"

#: common/setting/system.py:632
msgid "Log Report Errors"
msgstr ""

#: common/setting/system.py:633
msgid "Log errors which occur when generating reports"
msgstr ""

#: common/setting/system.py:638 plugin/builtin/labels/label_sheet.py:29
#: report/models.py:365
msgid "Page Size"
msgstr "Sidestørrelse"

#: common/setting/system.py:639
msgid "Default page size for PDF reports"
msgstr "Standard sidestørrelse for PDF-rapporter"

#: common/setting/system.py:644
msgid "Globally Unique Serials"
msgstr "Globalt Unike Serienummer"

#: common/setting/system.py:645
msgid "Serial numbers for stock items must be globally unique"
msgstr "Serienummer for lagervarer må være globalt unike"

#: common/setting/system.py:650
msgid "Autofill Serial Numbers"
msgstr "Automatisk tildeling av Serienummer"

#: common/setting/system.py:651
msgid "Autofill serial numbers in forms"
msgstr "Aumatisk fyll ut serienummer i skjemaer"

#: common/setting/system.py:656
msgid "Delete Depleted Stock"
msgstr "Slett oppbrukt lagerbeholdning"

#: common/setting/system.py:657
msgid "Determines default behavior when a stock item is depleted"
msgstr ""

#: common/setting/system.py:662
msgid "Batch Code Template"
msgstr "Batchkodemal"

#: common/setting/system.py:663
msgid "Template for generating default batch codes for stock items"
msgstr "Mal for generering av standard batchkoder for lagervarer"

#: common/setting/system.py:667
msgid "Stock Expiry"
msgstr "Lagerbeholdning utløper"

#: common/setting/system.py:668
msgid "Enable stock expiry functionality"
msgstr "Aktiver funksjonalitet for utløp av lagerbeholdning"

#: common/setting/system.py:673
msgid "Sell Expired Stock"
msgstr "Selg utløpt lagerbeholdning"

#: common/setting/system.py:674
msgid "Allow sale of expired stock"
msgstr "Tillat salg av utgått lagerbeholdning"

#: common/setting/system.py:679
msgid "Stock Stale Time"
msgstr "Foreldet lagerbeholdning tidsintervall"

#: common/setting/system.py:681
msgid "Number of days stock items are considered stale before expiring"
msgstr "Antall dager før lagervarer er ansett som foreldet før utløp"

#: common/setting/system.py:688
msgid "Build Expired Stock"
msgstr "Produsér Utløpt Lagerbeholdning"

#: common/setting/system.py:689
msgid "Allow building with expired stock"
msgstr "Tillat produksjon med utløpt lagerbeholdning"

#: common/setting/system.py:694
msgid "Stock Ownership Control"
msgstr "Kontroll over eierskap av lagerbeholdning"

#: common/setting/system.py:695
msgid "Enable ownership control over stock locations and items"
msgstr "Aktiver eierskap over lagerplasseringer og -varer"

#: common/setting/system.py:700
msgid "Stock Location Default Icon"
msgstr "Lagerplassering standard ikon"

#: common/setting/system.py:701
msgid "Stock location default icon (empty means no icon)"
msgstr "Lagerplassering standard ikon (tomt betyr ingen ikon)"

#: common/setting/system.py:706
msgid "Show Installed Stock Items"
msgstr "Vis installerte lagervarer"

#: common/setting/system.py:707
msgid "Display installed stock items in stock tables"
msgstr "Vis installerte lagervarer i lagertabeller"

#: common/setting/system.py:712
msgid "Check BOM when installing items"
msgstr ""

#: common/setting/system.py:714
msgid "Installed stock items must exist in the BOM for the parent part"
msgstr ""

#: common/setting/system.py:720
msgid "Allow Out of Stock Transfer"
msgstr ""

#: common/setting/system.py:722
msgid "Allow stock items which are not in stock to be transferred between stock locations"
msgstr ""

#: common/setting/system.py:728
msgid "Build Order Reference Pattern"
msgstr "Produksjonsordre-referansemønster"

#: common/setting/system.py:729
msgid "Required pattern for generating Build Order reference field"
msgstr "Nødvendig mønster for å generere Produksjonsordre-referansefeltet"

#: common/setting/system.py:734 common/setting/system.py:788
#: common/setting/system.py:808 common/setting/system.py:844
msgid "Require Responsible Owner"
msgstr ""

#: common/setting/system.py:735 common/setting/system.py:789
#: common/setting/system.py:809 common/setting/system.py:845
msgid "A responsible owner must be assigned to each order"
msgstr ""

#: common/setting/system.py:740
msgid "Require Active Part"
msgstr ""

#: common/setting/system.py:741
msgid "Prevent build order creation for inactive parts"
msgstr ""

#: common/setting/system.py:746
msgid "Require Locked Part"
msgstr ""

#: common/setting/system.py:747
msgid "Prevent build order creation for unlocked parts"
msgstr ""

#: common/setting/system.py:752
msgid "Require Valid BOM"
msgstr ""

#: common/setting/system.py:753
msgid "Prevent build order creation unless BOM has been validated"
msgstr ""

#: common/setting/system.py:758
msgid "Require Closed Child Orders"
msgstr ""

#: common/setting/system.py:760
msgid "Prevent build order completion until all child orders are closed"
msgstr ""

#: common/setting/system.py:766
msgid "Block Until Tests Pass"
msgstr ""

#: common/setting/system.py:768
msgid "Prevent build outputs from being completed until all required tests pass"
msgstr ""

#: common/setting/system.py:774
msgid "Enable Return Orders"
msgstr "Aktiver returordrer"

#: common/setting/system.py:775
msgid "Enable return order functionality in the user interface"
msgstr "Aktiver returordrefunksjonalitet i brukergrensesnittet"

#: common/setting/system.py:780
msgid "Return Order Reference Pattern"
msgstr "Returordre-referansemønster"

#: common/setting/system.py:782
msgid "Required pattern for generating Return Order reference field"
msgstr ""

#: common/setting/system.py:794
msgid "Edit Completed Return Orders"
msgstr "Rediger fullførte returordrer"

#: common/setting/system.py:796
msgid "Allow editing of return orders after they have been completed"
msgstr "Tillat redigering av returordrer etter de er fullført"

#: common/setting/system.py:802
msgid "Sales Order Reference Pattern"
msgstr "Salgsordre-referansemønster"

#: common/setting/system.py:803
msgid "Required pattern for generating Sales Order reference field"
msgstr "Påkrevd mønster for å generere salgsordrereferansefelt"

#: common/setting/system.py:814
msgid "Sales Order Default Shipment"
msgstr "Salgsordre standard fraktmetode"

#: common/setting/system.py:815
msgid "Enable creation of default shipment with sales orders"
msgstr "Aktiver opprettelse av standard forsendelse med salgsordrer"

#: common/setting/system.py:820
msgid "Edit Completed Sales Orders"
msgstr "Rediger fullførte salgsordrer"

#: common/setting/system.py:822
msgid "Allow editing of sales orders after they have been shipped or completed"
msgstr "Tillat redigering av salgsordrer etter de har blitt sendt eller fullført"

#: common/setting/system.py:828
msgid "Mark Shipped Orders as Complete"
msgstr ""

#: common/setting/system.py:830
msgid "Sales orders marked as shipped will automatically be completed, bypassing the \"shipped\" status"
msgstr ""

#: common/setting/system.py:836
msgid "Purchase Order Reference Pattern"
msgstr "Referansemønster for innkjøpsordre"

#: common/setting/system.py:838
msgid "Required pattern for generating Purchase Order reference field"
msgstr "Obligatorisk mønster for generering av referansefelt for innkjøpsordre"

#: common/setting/system.py:850
msgid "Edit Completed Purchase Orders"
msgstr "Rediger fullførte innkjøpsordre"

#: common/setting/system.py:852
msgid "Allow editing of purchase orders after they have been shipped or completed"
msgstr "Tillat redigering av innkjøpsordre etter at de har blitt sendt eller fullført"

#: common/setting/system.py:858
msgid "Convert Currency"
msgstr ""

#: common/setting/system.py:859
msgid "Convert item value to base currency when receiving stock"
msgstr ""

#: common/setting/system.py:864
msgid "Auto Complete Purchase Orders"
msgstr "Autofullfør innkjøpsordrer"

#: common/setting/system.py:866
msgid "Automatically mark purchase orders as complete when all line items are received"
msgstr "Automatisk merk innkjøpsordre som fullført når alle ordrelinjer er mottatt"

#: common/setting/system.py:873
msgid "Enable password forgot"
msgstr "Aktiver passord glemt"

#: common/setting/system.py:874
msgid "Enable password forgot function on the login pages"
msgstr "Ativer funskjon for glemt passord på innloggingssidene"

#: common/setting/system.py:879
msgid "Enable registration"
msgstr "Aktiver registrering"

#: common/setting/system.py:880
msgid "Enable self-registration for users on the login pages"
msgstr "Aktiver egenregistrerting for brukerer på påloggingssidene"

#: common/setting/system.py:885
msgid "Enable SSO"
msgstr "Aktiver SSO"

#: common/setting/system.py:886
msgid "Enable SSO on the login pages"
msgstr "Aktiver SSO på innloggingssidene"

#: common/setting/system.py:891
msgid "Enable SSO registration"
msgstr "Aktiver SSO-registrering"

#: common/setting/system.py:893
msgid "Enable self-registration via SSO for users on the login pages"
msgstr "Aktiver selvregistrering via SSO for brukere på innloggingssiden"

#: common/setting/system.py:899
msgid "Enable SSO group sync"
msgstr ""

#: common/setting/system.py:901
msgid "Enable synchronizing InvenTree groups with groups provided by the IdP"
msgstr ""

#: common/setting/system.py:907
msgid "SSO group key"
msgstr ""

#: common/setting/system.py:908
msgid "The name of the groups claim attribute provided by the IdP"
msgstr ""

#: common/setting/system.py:913
msgid "SSO group map"
msgstr ""

#: common/setting/system.py:915
msgid "A mapping from SSO groups to local InvenTree groups. If the local group does not exist, it will be created."
msgstr ""

#: common/setting/system.py:921
msgid "Remove groups outside of SSO"
msgstr ""

#: common/setting/system.py:923
msgid "Whether groups assigned to the user should be removed if they are not backend by the IdP. Disabling this setting might cause security issues"
msgstr ""

#: common/setting/system.py:929
msgid "Email required"
msgstr "E-postadresse kreves"

#: common/setting/system.py:930
msgid "Require user to supply mail on signup"
msgstr "Krevt at brukere angir e-post ved registrering"

#: common/setting/system.py:935
msgid "Auto-fill SSO users"
msgstr "Auto-utfyll SSO-brukere"

#: common/setting/system.py:936
msgid "Automatically fill out user-details from SSO account-data"
msgstr "Fyll automatisk ut brukeropplysninger fra SSO-kontodata"

#: common/setting/system.py:941
msgid "Mail twice"
msgstr "E-post to ganger"

#: common/setting/system.py:942
msgid "On signup ask users twice for their mail"
msgstr "Spør brukeren om e-post to ganger ved registrering"

#: common/setting/system.py:947
msgid "Password twice"
msgstr "Passord to ganger"

#: common/setting/system.py:948
msgid "On signup ask users twice for their password"
msgstr "Spør brukeren om passord to ganger ved registrering"

#: common/setting/system.py:953
msgid "Allowed domains"
msgstr "Tillatte domener"

#: common/setting/system.py:955
msgid "Restrict signup to certain domains (comma-separated, starting with @)"
msgstr "Begrens registrering til bestemte domener (kommaseparert, begynner med @)"

#: common/setting/system.py:961
msgid "Group on signup"
msgstr "Gruppe ved registrering"

#: common/setting/system.py:963
msgid "Group to which new users are assigned on registration. If SSO group sync is enabled, this group is only set if no group can be assigned from the IdP."
msgstr ""

#: common/setting/system.py:969
msgid "Enforce MFA"
msgstr "Krev MFA"

#: common/setting/system.py:970
msgid "Users must use multifactor security."
msgstr "Brukere må bruke flerfaktorsikkerhet."

#: common/setting/system.py:975
msgid "Check plugins on startup"
msgstr "Sjekk utvidelser ved oppstart"

#: common/setting/system.py:977
msgid "Check that all plugins are installed on startup - enable in container environments"
msgstr "Sjekk at alle utvidelser er installert ved oppstart - aktiver i containermiljøer"

#: common/setting/system.py:984
msgid "Check for plugin updates"
msgstr ""

#: common/setting/system.py:985
msgid "Enable periodic checks for updates to installed plugins"
msgstr ""

#: common/setting/system.py:991
msgid "Enable URL integration"
msgstr "Aktiver URL-integrasjon"

#: common/setting/system.py:992
msgid "Enable plugins to add URL routes"
msgstr "Tillat utvidelser å legge til URL-ruter"

#: common/setting/system.py:998
msgid "Enable navigation integration"
msgstr "Aktiver navigasjonsintegrasjon"

#: common/setting/system.py:999
msgid "Enable plugins to integrate into navigation"
msgstr "Tillat utvidelser å integrere mot navigasjon"

#: common/setting/system.py:1005
msgid "Enable app integration"
msgstr "Aktiver app-integrasjon"

#: common/setting/system.py:1006
msgid "Enable plugins to add apps"
msgstr "Tillat utvidelser å legge til apper"

#: common/setting/system.py:1012
msgid "Enable schedule integration"
msgstr "Aktiver tidsplanintegrasjon"

#: common/setting/system.py:1013
msgid "Enable plugins to run scheduled tasks"
msgstr "Tillat utvidelser å kjøre planlagte oppgaver"

#: common/setting/system.py:1019
msgid "Enable event integration"
msgstr "Aktiver hendelsesintegrasjon"

#: common/setting/system.py:1020
msgid "Enable plugins to respond to internal events"
msgstr "Tillat utvidelser å reagere på interne hendelser"

#: common/setting/system.py:1026
msgid "Enable interface integration"
msgstr ""

#: common/setting/system.py:1027
msgid "Enable plugins to integrate into the user interface"
msgstr ""

#: common/setting/system.py:1033
msgid "Enable project codes"
msgstr ""

#: common/setting/system.py:1034
msgid "Enable project codes for tracking projects"
msgstr ""

#: common/setting/system.py:1039
msgid "Stocktake Functionality"
msgstr "Varetellingsfunksjonalitet"

#: common/setting/system.py:1041
msgid "Enable stocktake functionality for recording stock levels and calculating stock value"
msgstr "Aktiver varetellingsfunksjonalitet for å registrere lagernivåer og regne ut lagerverdi"

#: common/setting/system.py:1047
msgid "Exclude External Locations"
msgstr "Ekskluder eksterne plasseringer"

#: common/setting/system.py:1049
msgid "Exclude stock items in external locations from stocktake calculations"
msgstr "Eksluder lagervarer i eksterne plasseringer fra varetellinger"

#: common/setting/system.py:1055
msgid "Automatic Stocktake Period"
msgstr "Automatisk varetellingsperiode"

#: common/setting/system.py:1057
msgid "Number of days between automatic stocktake recording (set to zero to disable)"
msgstr "Antall dager mellom automatisk varetellingsregistrering (sett til null for å deaktivere)"

#: common/setting/system.py:1063
msgid "Report Deletion Interval"
msgstr "Rapportslettingsintervall"

#: common/setting/system.py:1065
msgid "Stocktake reports will be deleted after specified number of days"
msgstr "Varetellingsrapporter vil slettes etter angitt antall dager"

#: common/setting/system.py:1072
msgid "Display Users full names"
msgstr "Vis brukernes fulle navn"

#: common/setting/system.py:1073
msgid "Display Users full names instead of usernames"
msgstr "Vis brukernes fulle navn istedet for brukernavn"

#: common/setting/system.py:1078
msgid "Display User Profiles"
msgstr ""

#: common/setting/system.py:1079
msgid "Display Users Profiles on their profile page"
msgstr ""

#: common/setting/system.py:1084
msgid "Enable Test Station Data"
msgstr ""

#: common/setting/system.py:1085
msgid "Enable test station data collection for test results"
msgstr ""

#: common/setting/system.py:1090
msgid "Create Template on Upload"
msgstr ""

#: common/setting/system.py:1092
msgid "Create a new test template when uploading test data which does not match an existing template"
msgstr ""

#: common/setting/user.py:23
msgid "Inline label display"
msgstr "Innebygd etikettvisning"

#: common/setting/user.py:25
msgid "Display PDF labels in the browser, instead of downloading as a file"
msgstr "Vis PDF-etiketter i nettleseren fremfor å lastes ned som en fil"

#: common/setting/user.py:31
msgid "Default label printer"
msgstr "Standard etikettskriver"

#: common/setting/user.py:32
msgid "Configure which label printer should be selected by default"
msgstr "Konfigurer hvilken etikettskriver som skal være valgt som standard"

#: common/setting/user.py:37
msgid "Inline report display"
msgstr "Innebygd rapportvisning"

#: common/setting/user.py:39
msgid "Display PDF reports in the browser, instead of downloading as a file"
msgstr "Vis PDF-rapporter i nettleseren fremfor å lastes ned som en fil"

#: common/setting/user.py:45
msgid "Search Parts"
msgstr "Søk i Deler"

#: common/setting/user.py:46
msgid "Display parts in search preview window"
msgstr "Vis deler i forhåndsvsningsvinduet for søk"

#: common/setting/user.py:51
msgid "Search Supplier Parts"
msgstr "Søk i Leverandørdeler"

#: common/setting/user.py:52
msgid "Display supplier parts in search preview window"
msgstr "Vis leverandørdeler i forhåndsvisningsvinduet for søk"

#: common/setting/user.py:57
msgid "Search Manufacturer Parts"
msgstr "Søk i Produsentdeler"

#: common/setting/user.py:58
msgid "Display manufacturer parts in search preview window"
msgstr "Vis produsentdeler i forhåndsvisningsvinduet for søk"

#: common/setting/user.py:63
msgid "Hide Inactive Parts"
msgstr "Skjul Inaktive Deler"

#: common/setting/user.py:64
msgid "Excluded inactive parts from search preview window"
msgstr "Ekskluder inaktive deler fra forhåndsvisningsvinduet for søk"

#: common/setting/user.py:69
msgid "Search Categories"
msgstr "Søk i kategorier"

#: common/setting/user.py:70
msgid "Display part categories in search preview window"
msgstr "Vis delkategorier i forhåndsvisningsvinduet for søk"

#: common/setting/user.py:75
msgid "Search Stock"
msgstr "Søk i lagerbeholdning"

#: common/setting/user.py:76
msgid "Display stock items in search preview window"
msgstr "Vis lagervarer i forhåndsvisningsvinduet for søk"

#: common/setting/user.py:81
msgid "Hide Unavailable Stock Items"
msgstr "Skjul utilgjengelige Lagervarer"

#: common/setting/user.py:83
msgid "Exclude stock items which are not available from the search preview window"
msgstr "Ekskluder lagervarer som ikke er tilgjengelige fra forhåndsvisningsvinduet for søk"

#: common/setting/user.py:89
msgid "Search Locations"
msgstr "Søk i Plasseringer"

#: common/setting/user.py:90
msgid "Display stock locations in search preview window"
msgstr "Vis lagerplasseringer i forhåndsvisningsvinduet for søk"

#: common/setting/user.py:95
msgid "Search Companies"
msgstr "Søk i Firma"

#: common/setting/user.py:96
msgid "Display companies in search preview window"
msgstr "Vis firma i forhåndsvsningsvinduet for søk"

#: common/setting/user.py:101
msgid "Search Build Orders"
msgstr "Søk i Produksjonsordrer"

#: common/setting/user.py:102
msgid "Display build orders in search preview window"
msgstr "Vis produksjonsordrer i forhåndsvisningsvinduet for søk"

#: common/setting/user.py:107
msgid "Search Purchase Orders"
msgstr "Søk i Innkjøpsordrer"

#: common/setting/user.py:108
msgid "Display purchase orders in search preview window"
msgstr "Vis innkjøpsordrer i forhåndsvisningsvinduet for søk"

#: common/setting/user.py:113
msgid "Exclude Inactive Purchase Orders"
msgstr "Ekskluder inaktive Innkjøpsordrer"

#: common/setting/user.py:114
msgid "Exclude inactive purchase orders from search preview window"
msgstr "Ekskluder inaktive innkjøpsordrer fra forhåndsvisningsvinduet for søk"

#: common/setting/user.py:119
msgid "Search Sales Orders"
msgstr "Søk i Salgsordrer"

#: common/setting/user.py:120
msgid "Display sales orders in search preview window"
msgstr "Vis salgsordrer i forhåndsvisningsvinduet for søk"

#: common/setting/user.py:125
msgid "Exclude Inactive Sales Orders"
msgstr "Ekskluder Inaktive Salgsordrer"

#: common/setting/user.py:126
msgid "Exclude inactive sales orders from search preview window"
msgstr "Ekskluder inaktive salgsordrer fra forhåndsvisningsvinduet for søk"

#: common/setting/user.py:131
msgid "Search Sales Order Shipments"
msgstr ""

#: common/setting/user.py:132
msgid "Display sales order shipments in search preview window"
msgstr ""

#: common/setting/user.py:137
msgid "Search Return Orders"
msgstr "Søk i Returordrer"

#: common/setting/user.py:138
msgid "Display return orders in search preview window"
msgstr "Vis returordrer i forhåndsvisningsvinduet for søk"

#: common/setting/user.py:143
msgid "Exclude Inactive Return Orders"
msgstr "Ekskluder Inaktive Returordrer"

#: common/setting/user.py:144
msgid "Exclude inactive return orders from search preview window"
msgstr "Ekskluder inaktive returordrer fra forhåndsvisningsvinduet for søk"

#: common/setting/user.py:149
msgid "Search Preview Results"
msgstr "Forhåndsvisning av søkeresultater"

#: common/setting/user.py:151
msgid "Number of results to show in each section of the search preview window"
msgstr "Antall resultater å vise i hver seksjon av søkeresultatsforhåndsvisningen"

#: common/setting/user.py:157
msgid "Regex Search"
msgstr "Regex-søk"

#: common/setting/user.py:158
msgid "Enable regular expressions in search queries"
msgstr "Aktiver regulære uttrykk i søkeord"

#: common/setting/user.py:163
msgid "Whole Word Search"
msgstr "Helordsøk"

#: common/setting/user.py:164
msgid "Search queries return results for whole word matches"
msgstr "Søk returnerer resultater for treff med hele ord"

#: common/setting/user.py:169
msgid "Search Notes"
msgstr ""

#: common/setting/user.py:171
msgid "Search queries return results for matches from the item's notes"
msgstr ""

#: common/setting/user.py:177
msgid "Show Quantity in Forms"
msgstr "Vis antall i skjemaer"

#: common/setting/user.py:178
msgid "Display available part quantity in some forms"
msgstr "Vis antall tilgjengelige deler i noen skjemaer"

#: common/setting/user.py:183
msgid "Escape Key Closes Forms"
msgstr "Escape-knappen lukker skjemaer"

#: common/setting/user.py:184
msgid "Use the escape key to close modal forms"
msgstr "Bruk Escape-knappen for å lukke modal-skjemaer"

#: common/setting/user.py:189
msgid "Fixed Navbar"
msgstr "Fast navigasjonsbar"

#: common/setting/user.py:190
msgid "The navbar position is fixed to the top of the screen"
msgstr "Navigasjonsbarens posisjon er fast på toppen av skjermen"

#: common/setting/user.py:195
msgid "Navigation Icons"
msgstr ""

#: common/setting/user.py:196
msgid "Display icons in the navigation bar"
msgstr ""

#: common/setting/user.py:201
msgid "Date Format"
msgstr "Datoformat"

#: common/setting/user.py:202
msgid "Preferred format for displaying dates"
msgstr "Foretrukket format for å vise datoer"

#: common/setting/user.py:215
msgid "Part Scheduling"
msgstr "Delplanlegging"

#: common/setting/user.py:216
msgid "Display part scheduling information"
msgstr "Vis delplanleggingsinformasjon"

#: common/setting/user.py:221
msgid "Part Stocktake"
msgstr "Lagertelling for Del"

#: common/setting/user.py:223
msgid "Display part stocktake information (if stocktake functionality is enabled)"
msgstr "Vis lagertellingsinformasjon for del (om lagertellingsfunksjonalitet er aktivert)"

#: common/setting/user.py:229
msgid "Table String Length"
msgstr "Tabellstrenglengde"

#: common/setting/user.py:230
msgid "Maximum length limit for strings displayed in table views"
msgstr "Maksimal lengdegrense for tekst vist i tabeller"

#: common/setting/user.py:235
msgid "Show Last Breadcrumb"
msgstr ""

#: common/setting/user.py:236
msgid "Show the current page in breadcrumbs"
msgstr ""

#: common/setting/user.py:241
msgid "Receive error reports"
msgstr "Motta feilrapporter"

#: common/setting/user.py:242
msgid "Receive notifications for system errors"
msgstr "Motta varsler om systemfeil"

#: common/setting/user.py:247
msgid "Last used printing machines"
msgstr ""

#: common/setting/user.py:248
msgid "Save the last used printing machines for a user"
msgstr ""

#: common/validators.py:35
msgid "No attachment model type provided"
msgstr "Ingen modelltype angitt for vedlegg"

#: common/validators.py:41
msgid "Invalid attachment model type"
msgstr "Ugyldig modelltype for vedlegg"

#: common/validators.py:82
msgid "Minimum places cannot be greater than maximum places"
msgstr "Minste antall plasser kan ikke være mer enn største antall plasser"

#: common/validators.py:94
msgid "Maximum places cannot be less than minimum places"
msgstr "Største antall plasser kan ikke være mindre enn minste antall plasser"

#: common/validators.py:105
msgid "An empty domain is not allowed."
msgstr "Et tomt domene er ikke tillatt."

#: common/validators.py:107
#, python-brace-format
msgid "Invalid domain name: {domain}"
msgstr "Ugyldig domenenavn: {domain}"

#: common/validators.py:123
msgid "Value must be uppercase"
msgstr ""

#: common/validators.py:129
msgid "Value must be a valid variable identifier"
msgstr ""

#: company/api.py:141
msgid "Part is Active"
msgstr "Delen er aktiv"

#: company/api.py:145
msgid "Manufacturer is Active"
msgstr "Leverandør er aktiv"

#: company/api.py:278
msgid "Supplier Part is Active"
msgstr "Leverandørdel er aktiv"

#: company/api.py:282
msgid "Internal Part is Active"
msgstr "Intern del er aktiv"

#: company/api.py:287
msgid "Supplier is Active"
msgstr "Leverandør er aktiv"

#: company/api.py:299 company/models.py:498 company/serializers.py:444
#: part/serializers.py:577
msgid "Manufacturer"
msgstr "Produsent"

#: company/api.py:306 company/models.py:97 company/models.py:370
#: stock/api.py:849
msgid "Company"
msgstr "Firma"

#: company/api.py:316
msgid "Has Stock"
msgstr ""

#: company/models.py:98
msgid "Companies"
msgstr "Firmaer"

#: company/models.py:114
msgid "Company description"
msgstr "Beskrivelse av firma"

#: company/models.py:115
msgid "Description of the company"
msgstr "Beskrivelse av firmaet"

#: company/models.py:121
msgid "Website"
msgstr "Nettside"

#: company/models.py:122
msgid "Company website URL"
msgstr "Bedriftens nettside URL"

#: company/models.py:128
msgid "Phone number"
msgstr "Telefonnummer"

#: company/models.py:130
msgid "Contact phone number"
msgstr "Kontakt-telefonnummer"

#: company/models.py:137
msgid "Contact email address"
msgstr "Kontakt e-post"

#: company/models.py:142 company/models.py:274 order/models.py:500
#: users/models.py:563
msgid "Contact"
msgstr "Kontakt"

#: company/models.py:144
msgid "Point of contact"
msgstr "Kontaktpunkt"

#: company/models.py:150
msgid "Link to external company information"
msgstr "Link til ekstern bedriftsinformasjon"

#: company/models.py:164
msgid "Is this company active?"
msgstr "Er firmaet aktivt?"

#: company/models.py:169
msgid "Is customer"
msgstr "Er kunde"

#: company/models.py:170
msgid "Do you sell items to this company?"
msgstr "Selger du varer til dette firmaet?"

#: company/models.py:175
msgid "Is supplier"
msgstr "Er leverandør"

#: company/models.py:176
msgid "Do you purchase items from this company?"
msgstr "Kjøper du varer fra dette firmaet?"

#: company/models.py:181
msgid "Is manufacturer"
msgstr "Er produsent"

#: company/models.py:182
msgid "Does this company manufacture parts?"
msgstr "Produserer dette firmaet deler?"

#: company/models.py:190
msgid "Default currency used for this company"
msgstr "Standardvaluta brukt for dette firmaet"

#: company/models.py:313 order/models.py:510
msgid "Address"
msgstr "Adresse"

#: company/models.py:314
msgid "Addresses"
msgstr "Adresser"

#: company/models.py:371
msgid "Select company"
msgstr "Velg selskap"

#: company/models.py:376
msgid "Address title"
msgstr "Adressetittel"

#: company/models.py:377
msgid "Title describing the address entry"
msgstr "Tittel som beskriver addressen"

#: company/models.py:383
msgid "Primary address"
msgstr "Hovedadresse"

#: company/models.py:384
msgid "Set as primary address"
msgstr "Sett som hovedadresse"

#: company/models.py:389
msgid "Line 1"
msgstr "Linje 1"

#: company/models.py:390
msgid "Address line 1"
msgstr "Adresselinje 1"

#: company/models.py:396
msgid "Line 2"
msgstr "Linje 2"

#: company/models.py:397
msgid "Address line 2"
msgstr "Adresselinje 2"

#: company/models.py:403 company/models.py:404
msgid "Postal code"
msgstr "Postnummer"

#: company/models.py:410
msgid "City/Region"
msgstr "Poststed/område"

#: company/models.py:411
msgid "Postal code city/region"
msgstr "Postnummerets by/område"

#: company/models.py:417
msgid "State/Province"
msgstr "Delstat/provins"

#: company/models.py:418
msgid "State or province"
msgstr "Delstat eller provins"

#: company/models.py:424
msgid "Country"
msgstr "Land"

#: company/models.py:425
msgid "Address country"
msgstr "Adressens land"

#: company/models.py:431
msgid "Courier shipping notes"
msgstr "Notater til transportør"

#: company/models.py:432
msgid "Notes for shipping courier"
msgstr "Notater for transportør"

#: company/models.py:438
msgid "Internal shipping notes"
msgstr "Interne fraktnotater"

#: company/models.py:439
msgid "Shipping notes for internal use"
msgstr "Fraktnotater for internt bruk"

#: company/models.py:446
msgid "Link to address information (external)"
msgstr "Lenke til adresseinformasjon (ekstern)"

#: company/models.py:470 company/models.py:587 company/models.py:809
#: company/serializers.py:458
msgid "Manufacturer Part"
msgstr "Produsentdeler"

#: company/models.py:487 company/models.py:777 stock/models.py:948
#: stock/serializers.py:480
msgid "Base Part"
msgstr "Basisdel"

#: company/models.py:489 company/models.py:779
msgid "Select part"
msgstr "Velg del"

#: company/models.py:499
msgid "Select manufacturer"
msgstr "Velg produsent"

#: company/models.py:505 company/serializers.py:466 order/serializers.py:665
#: part/serializers.py:587
msgid "MPN"
msgstr "MPN"

#: company/models.py:506 stock/serializers.py:625
msgid "Manufacturer Part Number"
msgstr "Produsentens varenummer"

#: company/models.py:513
msgid "URL for external manufacturer part link"
msgstr "URL for ekstern produsentdel-lenke"

#: company/models.py:522
msgid "Manufacturer part description"
msgstr "Produsentens delbeskrivelse"

#: company/models.py:575
msgid "Manufacturer Part Parameter"
msgstr "Produsentdel parameter"

#: company/models.py:594
msgid "Parameter name"
msgstr "Parameternavn"

#: company/models.py:601
msgid "Parameter value"
msgstr "Parameterverdi"

#: company/models.py:608 part/models.py:1204 part/models.py:3844
msgid "Units"
msgstr "Enheter"

#: company/models.py:609
msgid "Parameter units"
msgstr "Parameterenheter"

#: company/models.py:717
msgid "Pack units must be compatible with the base part units"
msgstr "Pakkeenhetene må være komptible med delens basisenhet"

#: company/models.py:724
msgid "Pack units must be greater than zero"
msgstr "Pakkeenhet må være mer enn null"

#: company/models.py:738
msgid "Linked manufacturer part must reference the same base part"
msgstr "Den sammenkoblede produsentdelen må referere til samme basisdel"

#: company/models.py:787 company/serializers.py:436 company/serializers.py:454
#: order/models.py:626 part/serializers.py:561
#: plugin/builtin/suppliers/digikey.py:25 plugin/builtin/suppliers/lcsc.py:26
#: plugin/builtin/suppliers/mouser.py:24 plugin/builtin/suppliers/tme.py:26
#: stock/api.py:516 templates/email/overdue_purchase_order.html:16
msgid "Supplier"
msgstr "Leverandør"

#: company/models.py:788
msgid "Select supplier"
msgstr "Velg leverandør"

#: company/models.py:794 part/serializers.py:572
msgid "Supplier stock keeping unit"
msgstr "Leverandørens lagerbeholdningsenhet"

#: company/models.py:800
msgid "Is this supplier part active?"
msgstr "Er denne leverandørdelen aktiv?"

#: company/models.py:810
msgid "Select manufacturer part"
msgstr "Velg produsentdel"

#: company/models.py:817
msgid "URL for external supplier part link"
msgstr "URL for ekstern leverandørdel-lenke"

#: company/models.py:826
msgid "Supplier part description"
msgstr "Leverandørens delbeskrivelse"

#: company/models.py:833 order/serializers.py:819 order/serializers.py:2027
#: part/models.py:4356 part/models.py:4697
#: report/templates/report/inventree_bill_of_materials_report.html:140
#: report/templates/report/inventree_purchase_order_report.html:32
#: report/templates/report/inventree_return_order_report.html:27
#: report/templates/report/inventree_sales_order_report.html:32
#: report/templates/report/inventree_stock_location_report.html:105
#: stock/serializers.py:838
msgid "Note"
msgstr "Notat"

#: company/models.py:842 part/models.py:2182
msgid "base cost"
msgstr "grunnkostnad"

#: company/models.py:843 part/models.py:2183
msgid "Minimum charge (e.g. stocking fee)"
msgstr "Minimum betaling (f.eks. lageravgift)"

#: company/models.py:850 order/serializers.py:811 stock/models.py:979
#: stock/serializers.py:1666
msgid "Packaging"
msgstr "Emballasje"

#: company/models.py:851
msgid "Part packaging"
msgstr "Delemballasje"

#: company/models.py:856
msgid "Pack Quantity"
msgstr "Pakkeantall"

#: company/models.py:858
msgid "Total quantity supplied in a single pack. Leave empty for single items."
msgstr "Totalt antall i en enkelt pakke. La være tom for enkeltenheter."

#: company/models.py:877 part/models.py:2189
msgid "multiple"
msgstr "flere"

#: company/models.py:878
msgid "Order multiple"
msgstr "Bestill flere"

#: company/models.py:890
msgid "Quantity available from supplier"
msgstr "Antall tilgjengelig fra leverandør"

#: company/models.py:896
msgid "Availability Updated"
msgstr "Tilgjengelighet oppdatert"

#: company/models.py:897
msgid "Date of last update of availability data"
msgstr "Dato for siste oppdatering av tilgjengelighetsdata"

#: company/models.py:1025
msgid "Supplier Price Break"
msgstr "Leverandørens prisbrudd"

#: company/serializers.py:166
msgid "Return the string representation for the primary address. This property exists for backwards compatibility."
msgstr ""

#: company/serializers.py:185
msgid "Default currency used for this supplier"
msgstr "Standardvaluta brukt for denne leverandøren"

#: company/serializers.py:221
msgid "Company Name"
msgstr "Bedriftsnavn"

#: company/serializers.py:420 part/serializers.py:940 stock/serializers.py:498
msgid "In Stock"
msgstr "På lager"

#: data_exporter/mixins.py:324 data_exporter/mixins.py:393
msgid "Error occurred during data export"
msgstr ""

#: data_exporter/mixins.py:375
msgid "Data export plugin returned incorrect data format"
msgstr ""

#: data_exporter/serializers.py:74
msgid "Export Format"
msgstr ""

#: data_exporter/serializers.py:75
msgid "Select export file format"
msgstr ""

#: data_exporter/serializers.py:82
msgid "Export Plugin"
msgstr ""

#: data_exporter/serializers.py:83
msgid "Select export plugin"
msgstr ""

#: generic/states/fields.py:146
msgid "Additional status information for this item"
msgstr ""

#: generic/states/fields.py:160
msgid "Custom status key"
msgstr ""

#: generic/states/serializers.py:16 plugin/models.py:45 users/models.py:119
msgid "Key"
msgstr "Nøkkel"

#: generic/states/serializers.py:26
msgid "Custom"
msgstr ""

#: generic/states/serializers.py:37
msgid "Class"
msgstr ""

#: generic/states/serializers.py:40
msgid "Values"
msgstr ""

#: generic/states/tests.py:22 order/status_codes.py:13
msgid "Placed"
msgstr "Plassert"

#: generic/states/validators.py:21
msgid "Invalid status code"
msgstr ""

#: importer/models.py:70
msgid "Data File"
msgstr "Datafil"

#: importer/models.py:71
msgid "Data file to import"
msgstr ""

#: importer/models.py:80
msgid "Columns"
msgstr ""

#: importer/models.py:87
msgid "Target model type for this import session"
msgstr ""

#: importer/models.py:93
msgid "Import status"
msgstr ""

#: importer/models.py:103
msgid "Field Defaults"
msgstr ""

#: importer/models.py:110
msgid "Field Overrides"
msgstr ""

#: importer/models.py:117
msgid "Field Filters"
msgstr ""

#: importer/models.py:250
msgid "Some required fields have not been mapped"
msgstr ""

#: importer/models.py:407
msgid "Column is already mapped to a database field"
msgstr ""

#: importer/models.py:412
msgid "Field is already mapped to a data column"
msgstr ""

#: importer/models.py:421
msgid "Column mapping must be linked to a valid import session"
msgstr ""

#: importer/models.py:426
msgid "Column does not exist in the data file"
msgstr ""

#: importer/models.py:433
msgid "Field does not exist in the target model"
msgstr ""

#: importer/models.py:437
msgid "Selected field is read-only"
msgstr ""

#: importer/models.py:442 importer/models.py:513
msgid "Import Session"
msgstr ""

#: importer/models.py:446
msgid "Field"
msgstr ""

#: importer/models.py:448
msgid "Column"
msgstr ""

#: importer/models.py:517
msgid "Row Index"
msgstr ""

#: importer/models.py:520
msgid "Original row data"
msgstr ""

#: importer/models.py:525 machine/models.py:110
msgid "Errors"
msgstr ""

#: importer/models.py:527 part/api.py:864
msgid "Valid"
msgstr "Gyldig"

#: importer/operations.py:28 importer/operations.py:49
msgid "Unsupported data file format"
msgstr ""

#: importer/operations.py:40
msgid "Failed to open data file"
msgstr ""

#: importer/operations.py:51
msgid "Invalid data file dimensions"
msgstr ""

#: importer/serializers.py:91
msgid "Invalid field defaults"
msgstr ""

#: importer/serializers.py:104
msgid "Invalid field overrides"
msgstr ""

#: importer/serializers.py:117
msgid "Invalid field filters"
msgstr ""

#: importer/serializers.py:178
msgid "Rows"
msgstr ""

#: importer/serializers.py:179
msgid "List of row IDs to accept"
msgstr ""

#: importer/serializers.py:192
msgid "No rows provided"
msgstr ""

#: importer/serializers.py:196
msgid "Row does not belong to this session"
msgstr ""

#: importer/serializers.py:199
msgid "Row contains invalid data"
msgstr ""

#: importer/serializers.py:202
msgid "Row has already been completed"
msgstr ""

#: importer/status_codes.py:13
msgid "Initializing"
msgstr ""

#: importer/status_codes.py:18
msgid "Mapping Columns"
msgstr ""

#: importer/status_codes.py:21
msgid "Importing Data"
msgstr ""

#: importer/status_codes.py:24
msgid "Processing Data"
msgstr ""

#: importer/validators.py:21
msgid "Data file exceeds maximum size limit"
msgstr ""

#: importer/validators.py:26
msgid "Data file contains no headers"
msgstr ""

#: importer/validators.py:29
msgid "Data file contains too many columns"
msgstr ""

#: importer/validators.py:32
msgid "Data file contains too many rows"
msgstr ""

#: importer/validators.py:53
msgid "Value must be a valid dictionary object"
msgstr ""

#: machine/machine_types/label_printer.py:212
msgid "Copies"
msgstr ""

#: machine/machine_types/label_printer.py:213
msgid "Number of copies to print for each label"
msgstr ""

#: machine/machine_types/label_printer.py:228
msgid "Connected"
msgstr ""

#: machine/machine_types/label_printer.py:229 order/api.py:1679
msgid "Unknown"
msgstr "Ukjent"

#: machine/machine_types/label_printer.py:230
msgid "Printing"
msgstr ""

#: machine/machine_types/label_printer.py:231
msgid "No media"
msgstr ""

#: machine/machine_types/label_printer.py:232
msgid "Paper jam"
msgstr ""

#: machine/machine_types/label_printer.py:233
msgid "Disconnected"
msgstr ""

#: machine/machine_types/label_printer.py:240
msgid "Label Printer"
msgstr ""

#: machine/machine_types/label_printer.py:241
msgid "Directly print labels for various items."
msgstr ""

#: machine/machine_types/label_printer.py:247
msgid "Printer Location"
msgstr ""

#: machine/machine_types/label_printer.py:248
msgid "Scope the printer to a specific location"
msgstr ""

#: machine/models.py:25
msgid "Name of machine"
msgstr ""

#: machine/models.py:29
msgid "Machine Type"
msgstr ""

#: machine/models.py:29
msgid "Type of machine"
msgstr ""

#: machine/models.py:34 machine/models.py:146
msgid "Driver"
msgstr ""

#: machine/models.py:35
msgid "Driver used for the machine"
msgstr ""

#: machine/models.py:39
msgid "Machines can be disabled"
msgstr ""

#: machine/models.py:95
msgid "Driver available"
msgstr ""

#: machine/models.py:100
msgid "No errors"
msgstr ""

#: machine/models.py:105
msgid "Initialized"
msgstr ""

#: machine/models.py:117
msgid "Machine status"
msgstr ""

#: machine/models.py:145
msgid "Machine"
msgstr ""

#: machine/models.py:151
msgid "Machine Config"
msgstr ""

#: machine/models.py:156
msgid "Config type"
msgstr ""

#: order/api.py:118
msgid "Order Reference"
msgstr "Ordrereferanse"

#: order/api.py:146 order/api.py:1139
msgid "Outstanding"
msgstr ""

#: order/api.py:162
msgid "Has Project Code"
msgstr ""

#: order/api.py:176 order/models.py:475
msgid "Created By"
msgstr "Opprettet av"

#: order/api.py:180
msgid "Created Before"
msgstr ""

#: order/api.py:184
msgid "Created After"
msgstr ""

#: order/api.py:188
msgid "Has Start Date"
msgstr ""

#: order/api.py:196
msgid "Start Date Before"
msgstr ""

#: order/api.py:200
msgid "Start Date After"
msgstr ""

#: order/api.py:204
msgid "Has Target Date"
msgstr ""

#: order/api.py:212
msgid "Target Date Before"
msgstr ""

#: order/api.py:216
msgid "Target Date After"
msgstr ""

#: order/api.py:267
msgid "Has Pricing"
msgstr ""

#: order/api.py:320 order/api.py:774 order/api.py:1375
msgid "Completed Before"
msgstr ""

#: order/api.py:324 order/api.py:778 order/api.py:1379
msgid "Completed After"
msgstr ""

#: order/api.py:491 order/api.py:876 order/api.py:1102 order/models.py:1685
#: order/models.py:1803 order/models.py:1854 order/models.py:2002
#: order/models.py:2168 order/models.py:2690 order/models.py:2756
msgid "Order"
msgstr "Ordre"

#: order/api.py:495 order/api.py:914
msgid "Order Complete"
msgstr ""

#: order/api.py:527 order/api.py:531 order/serializers.py:676
msgid "Internal Part"
msgstr "Intern del"

#: order/api.py:549
msgid "Order Pending"
msgstr ""

#: order/api.py:899
msgid "Completed"
msgstr "Fullført"

#: order/api.py:1155
msgid "Has Shipment"
msgstr ""

#: order/api.py:1673 order/models.py:539 order/models.py:1686
#: order/models.py:1804
#: report/templates/report/inventree_purchase_order_report.html:14
#: stock/serializers.py:128 templates/email/overdue_purchase_order.html:15
msgid "Purchase Order"
msgstr "Innkjøpsordre"

#: order/api.py:1675 order/models.py:1080 order/models.py:1855
#: order/models.py:2003 order/models.py:2169
#: report/templates/report/inventree_build_order_report.html:135
#: report/templates/report/inventree_sales_order_report.html:14
#: report/templates/report/inventree_sales_order_shipment_report.html:15
#: templates/email/overdue_sales_order.html:15
msgid "Sales Order"
msgstr "Salgsordre"

#: order/api.py:1677 order/models.py:2340 order/models.py:2691
#: order/models.py:2757
#: report/templates/report/inventree_return_order_report.html:13
#: templates/email/overdue_return_order.html:15
msgid "Return Order"
msgstr "Returordre"

#: order/models.py:89
#: report/templates/report/inventree_purchase_order_report.html:31
#: report/templates/report/inventree_sales_order_report.html:31
msgid "Total Price"
msgstr "Total pris"

#: order/models.py:90
msgid "Total price for this order"
msgstr "Total pris for denne ordren"

#: order/models.py:95 order/serializers.py:75
msgid "Order Currency"
msgstr "Ordrevaluta"

#: order/models.py:98 order/serializers.py:76
msgid "Currency for this order (leave blank to use company default)"
msgstr "Valuta for denne ordren (la stå tom for å bruke firmastandard)"

#: order/models.py:323
msgid "This order is locked and cannot be modified"
msgstr ""

#: order/models.py:370
msgid "Contact does not match selected company"
msgstr "Kontakten samsvarer ikke med valgt firma"

#: order/models.py:377
msgid "Start date must be before target date"
msgstr ""

#: order/models.py:430
msgid "Order description (optional)"
msgstr "Ordrebeskrivelse (valgfritt)"

#: order/models.py:439
msgid "Select project code for this order"
msgstr "Velg prosjektkode for denne ordren"

#: order/models.py:445 order/models.py:1586 order/models.py:2057
msgid "Link to external page"
msgstr "Lenke til ekstern side"

#: order/models.py:452
msgid "Start date"
msgstr ""

#: order/models.py:453
msgid "Scheduled start date for this order"
msgstr ""

#: order/models.py:459 order/models.py:1593 order/serializers.py:269
#: report/templates/report/inventree_build_order_report.html:125
msgid "Target Date"
msgstr "Måldato"

#: order/models.py:461
msgid "Expected date for order delivery. Order will be overdue after this date."
msgstr "Forventet dato for levering av ordre. Bestillingen vil være forfalt etter denne datoen."

#: order/models.py:481
msgid "Issue Date"
msgstr "Sendt dato"

#: order/models.py:482
msgid "Date order was issued"
msgstr "Dato bestillingen ble sendt"

#: order/models.py:490
msgid "User or group responsible for this order"
msgstr "Bruker eller gruppe ansvarlig for ordren"

#: order/models.py:501
msgid "Point of contact for this order"
msgstr "Kontaktpunkt for denne ordren"

#: order/models.py:511
msgid "Company address for this order"
msgstr "Selskapsadresse for denne ordren"

#: order/models.py:602 order/models.py:1141
msgid "Order reference"
msgstr "Ordrereferanse"

#: order/models.py:611 order/models.py:1165 order/models.py:2428
#: stock/serializers.py:612 stock/serializers.py:1010 users/models.py:544
msgid "Status"
msgstr "Status"

#: order/models.py:612
msgid "Purchase order status"
msgstr "Status for innkjøpsordre"

#: order/models.py:627
msgid "Company from which the items are being ordered"
msgstr "Firma som varene blir bestilt fra"

#: order/models.py:638
msgid "Supplier Reference"
msgstr "Leverandørreferanse"

#: order/models.py:639
msgid "Supplier order reference code"
msgstr "Leverandørens ordrereferanse"

#: order/models.py:648
msgid "received by"
msgstr "mottatt av"

#: order/models.py:655 order/models.py:2443
msgid "Date order was completed"
msgstr "Dato ordre ble fullført"

#: order/models.py:664 order/models.py:1733
msgid "Destination"
msgstr "Destinasjon"

#: order/models.py:665 order/models.py:1737
msgid "Destination for received items"
msgstr ""

#: order/models.py:711
msgid "Part supplier must match PO supplier"
msgstr "Delleverandør må matche PO-leverandør"

#: order/models.py:971
msgid "Quantity must be a positive number"
msgstr "Mengde må være positiv"

#: order/models.py:1152 order/models.py:2415 stock/models.py:1001
#: stock/models.py:1002 stock/serializers.py:1405
#: templates/email/overdue_return_order.html:16
#: templates/email/overdue_sales_order.html:16
msgid "Customer"
msgstr "Kunde"

#: order/models.py:1153
msgid "Company to which the items are being sold"
msgstr "Firma som varene selges til"

#: order/models.py:1166
msgid "Sales order status"
msgstr ""

#: order/models.py:1177 order/models.py:2435
msgid "Customer Reference "
msgstr "Kundereferanse "

#: order/models.py:1178 order/models.py:2436
msgid "Customer order reference code"
msgstr "Kundens ordrereferanse"

#: order/models.py:1182 order/models.py:2009
msgid "Shipment Date"
msgstr "Forsendelsesdato"

#: order/models.py:1191
msgid "shipped by"
msgstr "sendt av"

#: order/models.py:1230
msgid "Order is already complete"
msgstr ""

#: order/models.py:1233
msgid "Order is already cancelled"
msgstr ""

#: order/models.py:1237
msgid "Only an open order can be marked as complete"
msgstr "Kun en åpen ordre kan merkes som fullført"

#: order/models.py:1241
msgid "Order cannot be completed as there are incomplete shipments"
msgstr "Bestillingen kan ikke fullføres da det finnes ufullstendige forsendelser"

#: order/models.py:1246
msgid "Order cannot be completed as there are incomplete allocations"
msgstr ""

#: order/models.py:1251
msgid "Order cannot be completed as there are incomplete line items"
msgstr "Denne ordren kan ikke fullføres da det fortsatt er ufullstendige artikler"

#: order/models.py:1535 order/models.py:1548
msgid "The order is locked and cannot be modified"
msgstr ""

#: order/models.py:1556
msgid "Item quantity"
msgstr "Antall"

#: order/models.py:1573
msgid "Line item reference"
msgstr "Linjereferanse"

#: order/models.py:1580
msgid "Line item notes"
msgstr "Linjenotater"

#: order/models.py:1595
msgid "Target date for this line item (leave blank to use the target date from the order)"
msgstr "Måldato for denne linjen (la stå tomt for å bruke måldatoen fra ordren)"

#: order/models.py:1616
msgid "Line item description (optional)"
msgstr "Linjeelementbeskrivelse (valgfritt)"

#: order/models.py:1623
msgid "Additional context for this line"
msgstr "Ytterligere kontekst for denne linjen"

#: order/models.py:1633
msgid "Unit price"
msgstr "Enhetspris"

#: order/models.py:1647
msgid "Purchase Order Line Item"
msgstr ""

#: order/models.py:1671
msgid "Supplier part must match supplier"
msgstr "Delens leverandør må samsvare med leverandør"

#: order/models.py:1705
msgid "Supplier part"
msgstr "Leverandørdel"

#: order/models.py:1712
msgid "Received"
msgstr "Mottatt"

#: order/models.py:1713
msgid "Number of items received"
msgstr "Antall enheter mottatt"

#: order/models.py:1721 stock/models.py:1124 stock/serializers.py:679
msgid "Purchase Price"
msgstr "Innkjøpspris"

#: order/models.py:1722
msgid "Unit purchase price"
msgstr "Enhet-innkjøpspris"

#: order/models.py:1792
msgid "Purchase Order Extra Line"
msgstr ""

#: order/models.py:1821
msgid "Sales Order Line Item"
msgstr ""

#: order/models.py:1842
msgid "Virtual part cannot be assigned to a sales order"
msgstr "Virtuell del kan ikke tildeles salgsordre"

#: order/models.py:1847
msgid "Only salable parts can be assigned to a sales order"
msgstr "Kun salgbare deler kan tildeles en salgsordre"

#: order/models.py:1873
msgid "Sale Price"
msgstr "Salgspris"

#: order/models.py:1874
msgid "Unit sale price"
msgstr "Enhets-salgspris"

#: order/models.py:1883 order/status_codes.py:50
msgid "Shipped"
msgstr "Sendt"

#: order/models.py:1884
msgid "Shipped quantity"
msgstr "Sendt antall"

#: order/models.py:1978
msgid "Sales Order Shipment"
msgstr ""

#: order/models.py:2010
msgid "Date of shipment"
msgstr "Dato for forsendelse"

#: order/models.py:2016
msgid "Delivery Date"
msgstr "Leveringsdato"

#: order/models.py:2017
msgid "Date of delivery of shipment"
msgstr "Dato for levering av forsendelse"

#: order/models.py:2025
msgid "Checked By"
msgstr "Sjekket Av"

#: order/models.py:2026
msgid "User who checked this shipment"
msgstr "Brukeren som sjekket forsendelsen"

#: order/models.py:2033 order/models.py:2265 order/serializers.py:1685
#: order/serializers.py:1809
#: report/templates/report/inventree_sales_order_shipment_report.html:14
msgid "Shipment"
msgstr "Forsendelse"

#: order/models.py:2034
msgid "Shipment number"
msgstr "Forsendelsesnummer"

#: order/models.py:2042
msgid "Tracking Number"
msgstr "Sporingsnummer"

#: order/models.py:2043
msgid "Shipment tracking information"
msgstr "Sporingsinformasjon for forsendelse"

#: order/models.py:2050
msgid "Invoice Number"
msgstr "Fakturanummer"

#: order/models.py:2051
msgid "Reference number for associated invoice"
msgstr "Referansenummer for tilknyttet faktura"

#: order/models.py:2074
msgid "Shipment has already been sent"
msgstr "Forsendelsen er allerede sendt"

#: order/models.py:2077
msgid "Shipment has no allocated stock items"
msgstr "Forsendelsen har ingen tildelte lagervarer"

#: order/models.py:2157
msgid "Sales Order Extra Line"
msgstr ""

#: order/models.py:2186
msgid "Sales Order Allocation"
msgstr ""

#: order/models.py:2209 order/models.py:2211
msgid "Stock item has not been assigned"
msgstr "Lagervarer er ikke blitt tildelt"

#: order/models.py:2218
msgid "Cannot allocate stock item to a line with a different part"
msgstr "Kan ikke tildele lagervare til en linje med annen del"

#: order/models.py:2221
msgid "Cannot allocate stock to a line without a part"
msgstr "Kan ikke tildele lagerbeholdning til en linje uten en del"

#: order/models.py:2224
msgid "Allocation quantity cannot exceed stock quantity"
msgstr "Tildelingsantall kan ikke overstige tilgjengelig lagerbeholdning"

#: order/models.py:2243 order/serializers.py:1555
msgid "Quantity must be 1 for serialized stock item"
msgstr "Antall må være 1 for serialisert lagervare"

#: order/models.py:2246
msgid "Sales order does not match shipment"
msgstr "Salgsordre samsvarer ikke med forsendelse"

#: order/models.py:2247 plugin/base/barcodes/api.py:642
msgid "Shipment does not match sales order"
msgstr "Forsendelsen samsvarer ikke med salgsordre"

#: order/models.py:2255
msgid "Line"
msgstr "Linje"

#: order/models.py:2266
msgid "Sales order shipment reference"
msgstr "Forsendelsesreferanse for salgsordre"

#: order/models.py:2279 order/models.py:2698
msgid "Item"
msgstr "Artikkel"

#: order/models.py:2280
msgid "Select stock item to allocate"
msgstr "Velg lagervare å tildele"

#: order/models.py:2289
msgid "Enter stock allocation quantity"
msgstr "Angi lagertildelingsmengde"

#: order/models.py:2404
msgid "Return Order reference"
msgstr "Returordre-referanse"

#: order/models.py:2416
msgid "Company from which items are being returned"
msgstr "Firmaet delen skal returneres fra"

#: order/models.py:2429
msgid "Return order status"
msgstr "Returordrestatus"

#: order/models.py:2656
msgid "Return Order Line Item"
msgstr ""

#: order/models.py:2669
msgid "Stock item must be specified"
msgstr ""

#: order/models.py:2673
msgid "Return quantity exceeds stock quantity"
msgstr ""

#: order/models.py:2678
msgid "Return quantity must be greater than zero"
msgstr ""

#: order/models.py:2683
msgid "Invalid quantity for serialized stock item"
msgstr ""

#: order/models.py:2699
msgid "Select item to return from customer"
msgstr "Velg artikkel som skal returneres fra kunde"

#: order/models.py:2714
msgid "Received Date"
msgstr "Mottatt Dato"

#: order/models.py:2715
msgid "The date this this return item was received"
msgstr "Datoen denne returartikkelen ble mottatt"

#: order/models.py:2727
msgid "Outcome"
msgstr "Utfall"

#: order/models.py:2728
msgid "Outcome for this line item"
msgstr "Utfall for dette linjeelementet"

#: order/models.py:2735
msgid "Cost associated with return or repair for this line item"
msgstr "Kostnad forbundet med retur eller reparasjon for dette linjeelementet"

#: order/models.py:2745
msgid "Return Order Extra Line"
msgstr ""

#: order/serializers.py:89
msgid "Order ID"
msgstr ""

#: order/serializers.py:89
msgid "ID of the order to duplicate"
msgstr ""

#: order/serializers.py:95
msgid "Copy Lines"
msgstr ""

#: order/serializers.py:96
msgid "Copy line items from the original order"
msgstr ""

#: order/serializers.py:102
msgid "Copy Extra Lines"
msgstr ""

#: order/serializers.py:103
msgid "Copy extra line items from the original order"
msgstr ""

#: order/serializers.py:116
#: report/templates/report/inventree_purchase_order_report.html:22
#: report/templates/report/inventree_return_order_report.html:19
#: report/templates/report/inventree_sales_order_report.html:22
msgid "Line Items"
msgstr "Linjeelementer"

#: order/serializers.py:121
msgid "Completed Lines"
msgstr ""

#: order/serializers.py:172
msgid "Duplicate Order"
msgstr ""

#: order/serializers.py:173
msgid "Specify options for duplicating this order"
msgstr ""

#: order/serializers.py:249
msgid "Invalid order ID"
msgstr ""

#: order/serializers.py:388
msgid "Supplier Name"
msgstr "Leverandørnavn"

#: order/serializers.py:430
msgid "Order cannot be cancelled"
msgstr "Ordren kan ikke kanselleres"

#: order/serializers.py:445 order/serializers.py:1576
msgid "Allow order to be closed with incomplete line items"
msgstr "Tillat ordre å lukkes med ufullstendige linjeelementer"

#: order/serializers.py:455 order/serializers.py:1586
msgid "Order has incomplete line items"
msgstr "Ordren har ufullstendige linjeelementer"

#: order/serializers.py:608
msgid "Order is not open"
msgstr "Ordren er ikke åpen"

#: order/serializers.py:629
msgid "Auto Pricing"
msgstr ""

#: order/serializers.py:631
msgid "Automatically calculate purchase price based on supplier part data"
msgstr ""

#: order/serializers.py:641
msgid "Purchase price currency"
msgstr "Innkjøpsvaluta"

#: order/serializers.py:649
msgid "Merge Items"
msgstr ""

#: order/serializers.py:651
msgid "Merge items with the same part, destination and target date into one line item"
msgstr ""

#: order/serializers.py:658 part/serializers.py:571
msgid "SKU"
msgstr "SKU-kode"

#: order/serializers.py:672 part/models.py:1089 part/serializers.py:412
msgid "Internal Part Number"
msgstr "Internt delnummer"

#: order/serializers.py:680
msgid "Internal Part Name"
msgstr ""

#: order/serializers.py:696
msgid "Supplier part must be specified"
msgstr "Leverandørdel må angis"

#: order/serializers.py:699
msgid "Purchase order must be specified"
msgstr "Innkjøpsordre må angis"

#: order/serializers.py:707
msgid "Supplier must match purchase order"
msgstr "Leverandør må samsvare med innkjøpsordre"

#: order/serializers.py:708
msgid "Purchase order must match supplier"
msgstr "Innkjøpsordre må samsvare med leverandør"

#: order/serializers.py:754 order/serializers.py:1656
msgid "Line Item"
msgstr "Ordrelinje"

#: order/serializers.py:760
msgid "Line item does not match purchase order"
msgstr "Linjeelementet samsvarer ikke med innkjøpsordre"

#: order/serializers.py:770 order/serializers.py:917 order/serializers.py:2023
msgid "Select destination location for received items"
msgstr "Velg lagerplassering for mottatte enheter"

#: order/serializers.py:786
msgid "Enter batch code for incoming stock items"
msgstr "Angi batchkode for innkommende lagervarer"

#: order/serializers.py:793 stock/models.py:1083 users/models.py:143
msgid "Expiry Date"
msgstr "Utløpsdato"

#: order/serializers.py:794
msgid "Enter expiry date for incoming stock items"
msgstr ""

#: order/serializers.py:802
msgid "Enter serial numbers for incoming stock items"
msgstr "Angi serienummer for innkommende lagervarer"

#: order/serializers.py:812
msgid "Override packaging information for incoming stock items"
msgstr ""

#: order/serializers.py:820 order/serializers.py:2028
msgid "Additional note for incoming stock items"
msgstr ""

#: order/serializers.py:827
msgid "Barcode"
msgstr "Strekkode"

#: order/serializers.py:828
msgid "Scanned barcode"
msgstr "Skannet strekkode"

#: order/serializers.py:844
msgid "Barcode is already in use"
msgstr "Strekkode allerede i bruk"

#: order/serializers.py:867
msgid "An integer quantity must be provided for trackable parts"
msgstr "Heltallsverdi må angis for sporbare deler"

#: order/serializers.py:934 order/serializers.py:2047
msgid "Line items must be provided"
msgstr "Linjeelementer må være oppgitt"

#: order/serializers.py:950
msgid "Destination location must be specified"
msgstr "Målplassering må angis"

#: order/serializers.py:961
msgid "Supplied barcode values must be unique"
msgstr "Angitte strekkodeverdier må være unike"

#: order/serializers.py:1092
msgid "Shipments"
msgstr ""

#: order/serializers.py:1096
msgid "Completed Shipments"
msgstr "Fullførte forsendelser"

#: order/serializers.py:1283
msgid "Sale price currency"
msgstr "Valuta for salgspris"

#: order/serializers.py:1331
msgid "Allocated Items"
msgstr ""

#: order/serializers.py:1458
msgid "No shipment details provided"
msgstr "Ingen forsendelsesopplysninger oppgitt"

#: order/serializers.py:1519 order/serializers.py:1665
msgid "Line item is not associated with this order"
msgstr "Linjeelement er ikke knyttet til denne ordren"

#: order/serializers.py:1538
msgid "Quantity must be positive"
msgstr "Mengden må være positiv"

#: order/serializers.py:1675
msgid "Enter serial numbers to allocate"
msgstr "Skriv inn serienummer for å tildele"

#: order/serializers.py:1697 order/serializers.py:1817
msgid "Shipment has already been shipped"
msgstr "Forsendelsen er allerede sendt"

#: order/serializers.py:1700 order/serializers.py:1820
msgid "Shipment is not associated with this order"
msgstr "Forsendelsen er ikke knyttet til denne ordren"

#: order/serializers.py:1755
msgid "No match found for the following serial numbers"
msgstr "Ingen treff funnet for følgende serienummer"

#: order/serializers.py:1762
msgid "The following serial numbers are unavailable"
msgstr ""

#: order/serializers.py:1989
msgid "Return order line item"
msgstr "Returordrelinje"

#: order/serializers.py:1999
msgid "Line item does not match return order"
msgstr "Linjeelementet samsvarer ikke med returordre"

#: order/serializers.py:2002
msgid "Line item has already been received"
msgstr "Linjeelementet er allerede mottatt"

#: order/serializers.py:2039
msgid "Items can only be received against orders which are in progress"
msgstr "Artikler kan bare mottas mot ordrer som pågår"

#: order/serializers.py:2131
msgid "Quantity to return"
msgstr ""

#: order/serializers.py:2143
msgid "Line price currency"
msgstr "Valuta for linje"

#: order/status_codes.py:17 order/status_codes.py:54 stock/status_codes.py:16
msgid "Lost"
msgstr "Tapt"

#: order/status_codes.py:18 order/status_codes.py:55 stock/status_codes.py:24
msgid "Returned"
msgstr "Returnert"

#: order/status_codes.py:47 order/status_codes.py:79
msgid "In Progress"
msgstr "Pågående"

#: order/status_codes.py:105
msgid "Return"
msgstr "Retur"

#: order/status_codes.py:108
msgid "Repair"
msgstr "Reparasjon"

#: order/status_codes.py:111
msgid "Replace"
msgstr "Erstatt"

#: order/status_codes.py:114
msgid "Refund"
msgstr "Refusjon"

#: order/status_codes.py:117
msgid "Reject"
msgstr "Avvis"

#: order/tasks.py:44
msgid "Overdue Purchase Order"
msgstr "Forfalt Innkjøpsordre"

#: order/tasks.py:49
#, python-brace-format
msgid "Purchase order {po} is now overdue"
msgstr "Innkjøpsordre {po} er nå forfalt"

#: order/tasks.py:112
msgid "Overdue Sales Order"
msgstr "Forfalt Salgsordre"

#: order/tasks.py:117
#, python-brace-format
msgid "Sales order {so} is now overdue"
msgstr "Salgsordre {so} er nå forfalt"

#: order/tasks.py:177
msgid "Overdue Return Order"
msgstr ""

#: order/tasks.py:182
#, python-brace-format
msgid "Return order {ro} is now overdue"
msgstr ""

#: part/api.py:115
msgid "Starred"
msgstr ""

#: part/api.py:117
msgid "Filter by starred categories"
msgstr ""

#: part/api.py:134 stock/api.py:255
msgid "Depth"
msgstr ""

#: part/api.py:134
msgid "Filter by category depth"
msgstr ""

#: part/api.py:152 stock/api.py:273
msgid "Top Level"
msgstr ""

#: part/api.py:154
msgid "Filter by top-level categories"
msgstr ""

#: part/api.py:167 stock/api.py:288
msgid "Cascade"
msgstr ""

#: part/api.py:169
msgid "Include sub-categories in filtered results"
msgstr ""

#: part/api.py:189
msgid "Parent"
msgstr ""

#: part/api.py:191
msgid "Filter by parent category"
msgstr ""

#: part/api.py:226
msgid "Exclude sub-categories under the specified category"
msgstr ""

#: part/api.py:438
msgid "Has Results"
msgstr ""

#: part/api.py:611
msgid "Incoming Purchase Order"
msgstr "Innkommende innkjøpsordre"

#: part/api.py:625
msgid "Outgoing Sales Order"
msgstr "Utgående salgsordre"

#: part/api.py:637
msgid "Stock produced by Build Order"
msgstr "Lagervarer produsert av en produksjonsordre"

#: part/api.py:719
msgid "Stock required for Build Order"
msgstr "Lagervarer påkrevd for produksjonsordre"

#: part/api.py:865
msgid "Validate entire Bill of Materials"
msgstr "Godkjenn hele Stykklisten"

#: part/api.py:871
msgid "This option must be selected"
msgstr "Dette alternativet må være valgt"

#: part/api.py:907
msgid "Is Variant"
msgstr ""

#: part/api.py:915
msgid "Is Revision"
msgstr ""

#: part/api.py:925
msgid "Has Revisions"
msgstr ""

#: part/api.py:1116
msgid "BOM Valid"
msgstr ""

#: part/api.py:1775
msgid "Assembly part is testable"
msgstr ""

#: part/api.py:1784
msgid "Component part is testable"
msgstr ""

#: part/api.py:1835
msgid "Uses"
msgstr ""

#: part/models.py:90 part/models.py:4137
#: templates/email/part_event_notification.html:16
msgid "Part Category"
msgstr "Delkategori"

#: part/models.py:91 users/ruleset.py:32
msgid "Part Categories"
msgstr "Delkategorier"

#: part/models.py:109 part/models.py:1134
msgid "Default Location"
msgstr "Standard plassering"

#: part/models.py:110
msgid "Default location for parts in this category"
msgstr "Standardplassering for deler i denne kategorien"

#: part/models.py:115 stock/models.py:212
msgid "Structural"
msgstr "Strukturell"

#: part/models.py:117
msgid "Parts may not be directly assigned to a structural category, but may be assigned to child categories."
msgstr "Deler kan ikke tilordnes direkte til en strukturell kategori, men kan tilordnes til underkategorier."

#: part/models.py:126
msgid "Default keywords"
msgstr "Standard nøkkelord"

#: part/models.py:127
msgid "Default keywords for parts in this category"
msgstr "Standard nøkkelord for deler i denne kategorien"

#: part/models.py:134 stock/models.py:96 stock/models.py:194
msgid "Icon"
msgstr "Ikon"

#: part/models.py:135 part/serializers.py:152 part/serializers.py:171
#: stock/models.py:195
msgid "Icon (optional)"
msgstr "Ikon (valgfritt)"

#: part/models.py:179
msgid "You cannot make this part category structural because some parts are already assigned to it!"
msgstr "Du kan ikke gjøre denne delkategorien strukturell fordi noen deler allerede er tilordnet den!"

#: part/models.py:458 part/serializers.py:130 part/serializers.py:343
#: users/ruleset.py:33
msgid "Parts"
msgstr "Deler"

#: part/models.py:510
msgid "Cannot delete this part as it is locked"
msgstr ""

#: part/models.py:513
msgid "Cannot delete this part as it is still active"
msgstr ""

#: part/models.py:518
msgid "Cannot delete this part as it is used in an assembly"
msgstr ""

#: part/models.py:556
msgid "Invalid choice for parent part"
msgstr "Ugyldig valg for overordnet del"

#: part/models.py:604 part/models.py:611
#, python-brace-format
msgid "Part '{self}' cannot be used in BOM for '{parent}' (recursive)"
msgstr "Delen '{self}' kan ikke brukes i BOM for '{parent}' (rekursiv)"

#: part/models.py:623
#, python-brace-format
msgid "Part '{parent}' is  used in BOM for '{self}' (recursive)"
msgstr "Delen '{parent}' er brukt i BOM for '{self}' (rekursiv)"

#: part/models.py:690
#, python-brace-format
msgid "IPN must match regex pattern {pattern}"
msgstr "IPN må samsvare med regex-mønsteret {pattern}"

#: part/models.py:698
msgid "Part cannot be a revision of itself"
msgstr ""

#: part/models.py:705
msgid "Cannot make a revision of a part which is already a revision"
msgstr ""

#: part/models.py:712
msgid "Revision code must be specified"
msgstr ""

#: part/models.py:719
msgid "Revisions are only allowed for assembly parts"
msgstr ""

#: part/models.py:726
msgid "Cannot make a revision of a template part"
msgstr ""

#: part/models.py:732
msgid "Parent part must point to the same template"
msgstr ""

#: part/models.py:829
msgid "Stock item with this serial number already exists"
msgstr "Lagervare med dette serienummeret eksisterer allerede"

#: part/models.py:971
msgid "Duplicate IPN not allowed in part settings"
msgstr "Duplikat av internt delnummer er ikke tillatt i delinnstillinger"

#: part/models.py:983
msgid "Duplicate part revision already exists."
msgstr ""

#: part/models.py:992
msgid "Part with this Name, IPN and Revision already exists."
msgstr "Del med dette Navnet, internt delnummer og Revisjon eksisterer allerede."

#: part/models.py:1007
msgid "Parts cannot be assigned to structural part categories!"
msgstr "Deler kan ikke tilordnes strukturelle delkategorier!"

#: part/models.py:1039
msgid "Part name"
msgstr "Delnavn"

#: part/models.py:1044
msgid "Is Template"
msgstr "Er Mal"

#: part/models.py:1045
msgid "Is this part a template part?"
msgstr "Er delen en maldel?"

#: part/models.py:1055
msgid "Is this part a variant of another part?"
msgstr "Er delen en variant av en annen del?"

#: part/models.py:1056
msgid "Variant Of"
msgstr "Variant av"

#: part/models.py:1063
msgid "Part description (optional)"
msgstr "Delbeskrivelse (valgfritt)"

#: part/models.py:1070
msgid "Keywords"
msgstr "Nøkkelord"

#: part/models.py:1071
msgid "Part keywords to improve visibility in search results"
msgstr "Del-nøkkelord for å øke synligheten i søkeresultater"

#: part/models.py:1081
msgid "Part category"
msgstr "Delkategori"

#: part/models.py:1088 part/serializers.py:926
#: report/templates/report/inventree_stock_location_report.html:103
msgid "IPN"
msgstr ""

#: part/models.py:1096
msgid "Part revision or version number"
msgstr "Delrevisjon eller versjonsnummer"

#: part/models.py:1097 report/models.py:215
msgid "Revision"
msgstr "Revisjon"

#: part/models.py:1106
msgid "Is this part a revision of another part?"
msgstr ""

#: part/models.py:1107
msgid "Revision Of"
msgstr ""

#: part/models.py:1132
msgid "Where is this item normally stored?"
msgstr "Hvor er denne artikkelen vanligvis lagret?"

#: part/models.py:1178
msgid "Default Supplier"
msgstr "Standard leverandør"

#: part/models.py:1179
msgid "Default supplier part"
msgstr "Standard leverandørdel"

#: part/models.py:1186
msgid "Default Expiry"
msgstr "Standard utløp"

#: part/models.py:1187
msgid "Expiry time (in days) for stock items of this part"
msgstr "Utløpstid (i dager) for lagervarer av denne delen"

#: part/models.py:1195 part/serializers.py:977
msgid "Minimum Stock"
msgstr "Minimal lagerbeholdning"

#: part/models.py:1196
msgid "Minimum allowed stock level"
msgstr "Minimum tillatt lagernivå"

#: part/models.py:1205
msgid "Units of measure for this part"
msgstr "Måleenheter for denne delen"

#: part/models.py:1212
msgid "Can this part be built from other parts?"
msgstr "Kan denne delen bygges fra andre deler?"

#: part/models.py:1218
msgid "Can this part be used to build other parts?"
msgstr "Kan denne delen brukes til å bygge andre deler?"

#: part/models.py:1224
msgid "Does this part have tracking for unique items?"
msgstr "Har denne delen sporing av unike artikler?"

#: part/models.py:1230
msgid "Can this part have test results recorded against it?"
msgstr ""

#: part/models.py:1236
msgid "Can this part be purchased from external suppliers?"
msgstr "Kan denne delen kjøpes inn fra eksterne leverandører?"

#: part/models.py:1242
msgid "Can this part be sold to customers?"
msgstr "Kan denne delen selges til kunder?"

#: part/models.py:1246
msgid "Is this part active?"
msgstr "Er denne delen aktiv?"

#: part/models.py:1252
msgid "Locked parts cannot be edited"
msgstr ""

#: part/models.py:1258
msgid "Is this a virtual part, such as a software product or license?"
msgstr "Er dette en virtuell del, som et softwareprodukt eller en lisens?"

#: part/models.py:1264
msgid "BOM checksum"
msgstr "Kontrollsum for BOM"

#: part/models.py:1265
msgid "Stored BOM checksum"
msgstr "Lagret BOM-kontrollsum"

#: part/models.py:1273
msgid "BOM checked by"
msgstr "Stykkliste sjekket av"

#: part/models.py:1278
msgid "BOM checked date"
msgstr "Stykkliste sjekket dato"

#: part/models.py:1294
msgid "Creation User"
msgstr "Opprettingsbruker"

#: part/models.py:1304
msgid "Owner responsible for this part"
msgstr "Eier ansvarlig for denne delen"

#: part/models.py:1309
msgid "Last Stocktake"
msgstr "Siste lagertelling"

#: part/models.py:2190
msgid "Sell multiple"
msgstr "Selg flere"

#: part/models.py:3185
msgid "Currency used to cache pricing calculations"
msgstr "Valuta som brukes til å bufre prisberegninger"

#: part/models.py:3201
msgid "Minimum BOM Cost"
msgstr "Minimal BOM-kostnad"

#: part/models.py:3202
msgid "Minimum cost of component parts"
msgstr "Minste kostnad for komponentdeler"

#: part/models.py:3208
msgid "Maximum BOM Cost"
msgstr "Maksimal BOM-kostnad"

#: part/models.py:3209
msgid "Maximum cost of component parts"
msgstr "Maksimal kostnad for komponentdeler"

#: part/models.py:3215
msgid "Minimum Purchase Cost"
msgstr "Minimal innkjøpskostnad"

#: part/models.py:3216
msgid "Minimum historical purchase cost"
msgstr "Minimal historisk innkjøpskostnad"

#: part/models.py:3222
msgid "Maximum Purchase Cost"
msgstr "Maksimal innkjøpskostnad"

#: part/models.py:3223
msgid "Maximum historical purchase cost"
msgstr "Maksimal historisk innkjøpskostnad"

#: part/models.py:3229
msgid "Minimum Internal Price"
msgstr "Minimal intern pris"

#: part/models.py:3230
msgid "Minimum cost based on internal price breaks"
msgstr "Minimal kostnad basert på interne prisbrudd"

#: part/models.py:3236
msgid "Maximum Internal Price"
msgstr "Maksimal intern pris"

#: part/models.py:3237
msgid "Maximum cost based on internal price breaks"
msgstr "Maksimal kostnad basert på interne prisbrudd"

#: part/models.py:3243
msgid "Minimum Supplier Price"
msgstr "Minimal leverandørpris"

#: part/models.py:3244
msgid "Minimum price of part from external suppliers"
msgstr "Minimumspris for del fra eksterne leverandører"

#: part/models.py:3250
msgid "Maximum Supplier Price"
msgstr "Maksimal leverandørpris"

#: part/models.py:3251
msgid "Maximum price of part from external suppliers"
msgstr "Maksimalpris for del fra eksterne leverandører"

#: part/models.py:3257
msgid "Minimum Variant Cost"
msgstr "Minimal Variantkostnad"

#: part/models.py:3258
msgid "Calculated minimum cost of variant parts"
msgstr "Beregnet minimal kostnad for variantdeler"

#: part/models.py:3264
msgid "Maximum Variant Cost"
msgstr "Maksimal Variantkostnad"

#: part/models.py:3265
msgid "Calculated maximum cost of variant parts"
msgstr "Beregnet maksimal kostnad for variantdeler"

#: part/models.py:3271 part/models.py:3285
msgid "Minimum Cost"
msgstr "Minimal kostnad"

#: part/models.py:3272
msgid "Override minimum cost"
msgstr "Overstyr minstekostnad"

#: part/models.py:3278 part/models.py:3292
msgid "Maximum Cost"
msgstr "Maksimal kostnad"

#: part/models.py:3279
msgid "Override maximum cost"
msgstr "Overstyr maksimal kostnad"

#: part/models.py:3286
msgid "Calculated overall minimum cost"
msgstr "Beregnet samlet minimal kostnad"

#: part/models.py:3293
msgid "Calculated overall maximum cost"
msgstr "Beregnet samlet maksimal kostnad"

#: part/models.py:3299
msgid "Minimum Sale Price"
msgstr "Minimal salgspris"

#: part/models.py:3300
msgid "Minimum sale price based on price breaks"
msgstr "Minimal salgspris basert på prisbrudd"

#: part/models.py:3306
msgid "Maximum Sale Price"
msgstr "Maksimal Salgspris"

#: part/models.py:3307
msgid "Maximum sale price based on price breaks"
msgstr "Maksimal salgspris basert på prisbrudd"

#: part/models.py:3313
msgid "Minimum Sale Cost"
msgstr "Minimal Salgskostnad"

#: part/models.py:3314
msgid "Minimum historical sale price"
msgstr "Minimal historisk salgspris"

#: part/models.py:3320
msgid "Maximum Sale Cost"
msgstr "Maksimal Salgskostnad"

#: part/models.py:3321
msgid "Maximum historical sale price"
msgstr "Maksimal historisk salgspris"

#: part/models.py:3340
msgid "Part for stocktake"
msgstr "Del for varetelling"

#: part/models.py:3345
msgid "Item Count"
msgstr "Antall"

#: part/models.py:3346
msgid "Number of individual stock entries at time of stocktake"
msgstr "Antall individuelle lagerenheter på tidspunkt for varetelling"

#: part/models.py:3354
msgid "Total available stock at time of stocktake"
msgstr "Total tilgjengelig lagerbeholdning på tidspunkt for varetelling"

#: part/models.py:3358 part/models.py:3441 part/serializers.py:273
#: report/templates/report/inventree_test_report.html:106
msgid "Date"
msgstr "Dato"

#: part/models.py:3359
msgid "Date stocktake was performed"
msgstr "Dato for utført lagertelling"

#: part/models.py:3367
msgid "Additional notes"
msgstr "Flere notater"

#: part/models.py:3377
msgid "User who performed this stocktake"
msgstr "Bruker som utførte denne lagertellingen"

#: part/models.py:3383
msgid "Minimum Stock Cost"
msgstr "Minimal lagerkostnad"

#: part/models.py:3384
msgid "Estimated minimum cost of stock on hand"
msgstr "Estimert minimal kostnad for lagerbeholdning"

#: part/models.py:3390
msgid "Maximum Stock Cost"
msgstr "Maksimal lagerkostnad"

#: part/models.py:3391
msgid "Estimated maximum cost of stock on hand"
msgstr "Estimert maksimal kostnad for lagerbeholdning"

#: part/models.py:3447
msgid "Report"
msgstr "Rapport"

#: part/models.py:3448
msgid "Stocktake report file (generated internally)"
msgstr "Lagertellingsrapportfil (generert internt)"

#: part/models.py:3453
msgid "Part Count"
msgstr "Antall deler"

#: part/models.py:3454
msgid "Number of parts covered by stocktake"
msgstr "Antall deler dekket av varetellingen"

#: part/models.py:3464
msgid "User who requested this stocktake report"
msgstr "Bruker som forespurte varetellingsrapporten"

#: part/models.py:3474
msgid "Part Sale Price Break"
msgstr ""

#: part/models.py:3586
msgid "Part Test Template"
msgstr ""

#: part/models.py:3612
msgid "Invalid template name - must include at least one alphanumeric character"
msgstr ""

#: part/models.py:3633 part/models.py:3803
msgid "Choices must be unique"
msgstr "Valg må være unike"

#: part/models.py:3644
msgid "Test templates can only be created for testable parts"
msgstr ""

#: part/models.py:3655
msgid "Test template with the same key already exists for part"
msgstr ""

#: part/models.py:3672
msgid "Test Name"
msgstr "Testnavn"

#: part/models.py:3673
msgid "Enter a name for the test"
msgstr "Angi et navn for testen"

#: part/models.py:3679
msgid "Test Key"
msgstr ""

#: part/models.py:3680
msgid "Simplified key for the test"
msgstr ""

#: part/models.py:3687
msgid "Test Description"
msgstr "Testbeskrivelse"

#: part/models.py:3688
msgid "Enter description for this test"
msgstr "Legg inn beskrivelse for denne testen"

#: part/models.py:3692 report/models.py:271
msgid "Enabled"
msgstr "Aktivert"

#: part/models.py:3692
msgid "Is this test enabled?"
msgstr ""

#: part/models.py:3697
msgid "Required"
msgstr "Påkrevd"

#: part/models.py:3698
msgid "Is this test required to pass?"
msgstr "Er det påkrevd at denne testen bestås?"

#: part/models.py:3703
msgid "Requires Value"
msgstr "Krever verdi"

#: part/models.py:3704
msgid "Does this test require a value when adding a test result?"
msgstr "Krever denne testen en verdi når det legges til et testresultat?"

#: part/models.py:3709
msgid "Requires Attachment"
msgstr "Krever vedlegg"

#: part/models.py:3711
msgid "Does this test require a file attachment when adding a test result?"
msgstr "Krever denne testen et filvedlegg når du legger inn et testresultat?"

#: part/models.py:3717 part/models.py:3865
msgid "Choices"
msgstr "Valg"

#: part/models.py:3718
msgid "Valid choices for this test (comma-separated)"
msgstr ""

#: part/models.py:3751
msgid "Part Parameter Template"
msgstr ""

#: part/models.py:3778
msgid "Checkbox parameters cannot have units"
msgstr "Sjekkboksparameter kan ikke ha enheter"

#: part/models.py:3783
msgid "Checkbox parameters cannot have choices"
msgstr "Sjekkboksparameter kan ikke ha valg"

#: part/models.py:3820
msgid "Parameter template name must be unique"
msgstr "Navn på parametermal må være unikt"

#: part/models.py:3838
msgid "Parameter Name"
msgstr "Parameternavn"

#: part/models.py:3845
msgid "Physical units for this parameter"
msgstr "Fysisk enheter for denne parameteren"

#: part/models.py:3853
msgid "Parameter description"
msgstr "Parameterbeskrivelse"

#: part/models.py:3859
msgid "Checkbox"
msgstr "Sjekkboks"

#: part/models.py:3860
msgid "Is this parameter a checkbox?"
msgstr "Er dette parameteret en sjekkboks?"

#: part/models.py:3866
msgid "Valid choices for this parameter (comma-separated)"
msgstr "Gyldige valg for denne parameteren (kommaseparert)"

#: part/models.py:3877
msgid "Selection list for this parameter"
msgstr ""

#: part/models.py:3913
msgid "Part Parameter"
msgstr ""

#: part/models.py:3939
msgid "Parameter cannot be modified - part is locked"
msgstr ""

#: part/models.py:3977
msgid "Invalid choice for parameter value"
msgstr "Ugyldig valg for parameterverdi"

#: part/models.py:4028
msgid "Parent Part"
msgstr "Overordnet del"

#: part/models.py:4036 part/models.py:4144 part/models.py:4145
msgid "Parameter Template"
msgstr "Parametermal"

#: part/models.py:4042
msgid "Parameter Value"
msgstr "Parameterverdi"

#: part/models.py:4092
msgid "Part Category Parameter Template"
msgstr ""

#: part/models.py:4151
msgid "Default Value"
msgstr "Standardverdi"

#: part/models.py:4152
msgid "Default Parameter Value"
msgstr "Standard Parameterverdi"

#: part/models.py:4283
msgid "BOM item cannot be modified - assembly is locked"
msgstr ""

#: part/models.py:4290
msgid "BOM item cannot be modified - variant assembly is locked"
msgstr ""

#: part/models.py:4300
msgid "Select parent part"
msgstr "Velg overordnet del"

#: part/models.py:4310
msgid "Sub part"
msgstr "Underordnet del"

#: part/models.py:4311
msgid "Select part to be used in BOM"
msgstr "Velg del som skal brukes i BOM"

#: part/models.py:4322
msgid "BOM quantity for this BOM item"
msgstr "BOM-antall for denne BOM-artikkelen"

#: part/models.py:4328
msgid "This BOM item is optional"
msgstr "Denne BOM-artikkelen er valgfri"

#: part/models.py:4334
msgid "This BOM item is consumable (it is not tracked in build orders)"
msgstr "Denne BOM-artikkelen er forbruksvare (den spores ikke i produksjonsordrer)"

#: part/models.py:4341
msgid "Overage"
msgstr "Svinn"

#: part/models.py:4342
msgid "Estimated build wastage quantity (absolute or percentage)"
msgstr "Forventet produksjonssvinn (absolutt eller prosent)"

#: part/models.py:4349
msgid "BOM item reference"
msgstr "BOM-artikkelreferanse"

#: part/models.py:4357
msgid "BOM item notes"
msgstr "BOM-artikkelnotater"

#: part/models.py:4363
msgid "Checksum"
msgstr "Kontrollsum"

#: part/models.py:4364
msgid "BOM line checksum"
msgstr "BOM-linje kontrollsum"

#: part/models.py:4369
msgid "Validated"
msgstr "Godkjent"

#: part/models.py:4370
msgid "This BOM item has been validated"
msgstr "Denne BOM-artikkelen er godkjent"

#: part/models.py:4375
msgid "Gets inherited"
msgstr "Arves"

#: part/models.py:4376
msgid "This BOM item is inherited by BOMs for variant parts"
msgstr "Denne BOM-artikkelen er arvet fra stykkliste for variantdeler"

#: part/models.py:4382
msgid "Stock items for variant parts can be used for this BOM item"
msgstr "Lagervarer for variantdeler kan brukes for denne BOM-artikkelen"

#: part/models.py:4467 stock/models.py:848
msgid "Quantity must be integer value for trackable parts"
msgstr "Antall må være heltallsverdi for sporbare deler"

#: part/models.py:4477 part/models.py:4479
msgid "Sub part must be specified"
msgstr "Underordnet del må angis"

#: part/models.py:4624
msgid "BOM Item Substitute"
msgstr "BOM-artikkel erstatning"

#: part/models.py:4645
msgid "Substitute part cannot be the same as the master part"
msgstr "Erstatningsdel kan ikke være samme som hoveddelen"

#: part/models.py:4658
msgid "Parent BOM item"
msgstr "Overordnet BOM-artikkel"

#: part/models.py:4666
msgid "Substitute part"
msgstr "Erstatningsdel"

#: part/models.py:4682
msgid "Part 1"
msgstr "Del 1"

#: part/models.py:4690
msgid "Part 2"
msgstr "Del 2"

#: part/models.py:4691
msgid "Select Related Part"
msgstr "Velg relatert del"

#: part/models.py:4698
msgid "Note for this relationship"
msgstr ""

#: part/models.py:4717
msgid "Part relationship cannot be created between a part and itself"
msgstr "Del-forhold kan ikke opprettes mellom en del og seg selv"

#: part/models.py:4722
msgid "Duplicate relationship already exists"
msgstr "Duplikatforhold eksisterer allerede"

#: part/serializers.py:125
msgid "Parent Category"
msgstr ""

#: part/serializers.py:126
msgid "Parent part category"
msgstr ""

#: part/serializers.py:134 part/serializers.py:168
msgid "Subcategories"
msgstr "Underkategorier"

#: part/serializers.py:207
msgid "Results"
msgstr ""

#: part/serializers.py:208
msgid "Number of results recorded against this template"
msgstr ""

#: part/serializers.py:235 part/serializers.py:253 stock/serializers.py:685
msgid "Purchase currency of this stock item"
msgstr "Innkjøpsvaluta for lagervaren"

#: part/serializers.py:278
msgid "Speculative Quantity"
msgstr ""

#: part/serializers.py:287
msgid "Model ID"
msgstr ""

#: part/serializers.py:313
msgid "File is not an image"
msgstr ""

#: part/serializers.py:344
msgid "Number of parts using this template"
msgstr ""

#: part/serializers.py:489
msgid "Original Part"
msgstr "Original Del"

#: part/serializers.py:490
msgid "Select original part to duplicate"
msgstr "Velg original del å duplisere"

#: part/serializers.py:495
msgid "Copy Image"
msgstr "Kopier Bilde"

#: part/serializers.py:496
msgid "Copy image from original part"
msgstr "Kopier bilde fra originaldel"

#: part/serializers.py:502
msgid "Copy BOM"
msgstr "Kopier Stykkliste"

#: part/serializers.py:503
msgid "Copy bill of materials from original part"
msgstr "Kopier stykkliste fra original del"

#: part/serializers.py:509
msgid "Copy Parameters"
msgstr "Kopier parametere"

#: part/serializers.py:510
msgid "Copy parameter data from original part"
msgstr "Kopier parameterdata fra originaldel"

#: part/serializers.py:516
msgid "Copy Notes"
msgstr "Kopier notater"

#: part/serializers.py:517
msgid "Copy notes from original part"
msgstr "Kopier notater fra originaldel"

#: part/serializers.py:535
msgid "Initial Stock Quantity"
msgstr "Innledende lagerbeholdning"

#: part/serializers.py:537
msgid "Specify initial stock quantity for this Part. If quantity is zero, no stock is added."
msgstr "Angi initiell lagermengde for denne delen. Hvis antall er null, er ingen lagerbeholdning lagt til."

#: part/serializers.py:544
msgid "Initial Stock Location"
msgstr "Innledende lagerplassering"

#: part/serializers.py:545
msgid "Specify initial stock location for this Part"
msgstr "Angi initiell lagerplasering for denne delen"

#: part/serializers.py:562
msgid "Select supplier (or leave blank to skip)"
msgstr "Velg leverandør (eller la stå tom for å hoppe over)"

#: part/serializers.py:578
msgid "Select manufacturer (or leave blank to skip)"
msgstr "Velg produsent (eller la stå tom for å hoppe over)"

#: part/serializers.py:588
msgid "Manufacturer part number"
msgstr "Produsentens delenummer"

#: part/serializers.py:595
msgid "Selected company is not a valid supplier"
msgstr "Valgt firma er ikke en gyldig leverandør"

#: part/serializers.py:604
msgid "Selected company is not a valid manufacturer"
msgstr "Valgt firma er ikke en gyldig produsent"

#: part/serializers.py:615
msgid "Manufacturer part matching this MPN already exists"
msgstr "Produsentdel som matcher dette MPN-et, finnes allerede"

#: part/serializers.py:622
msgid "Supplier part matching this SKU already exists"
msgstr "Leverandørdel som matcher denne SKU-en, finnes allerede"

#: part/serializers.py:911 part/stocktake.py:222
msgid "Category Name"
msgstr "Kategorinavn"

#: part/serializers.py:937
msgid "Building"
msgstr "Produseres"

#: part/serializers.py:952 part/stocktake.py:223 stock/serializers.py:1084
#: stock/serializers.py:1260 users/ruleset.py:36
msgid "Stock Items"
msgstr "Lagervarer"

#: part/serializers.py:955
msgid "Revisions"
msgstr ""

#: part/serializers.py:958
msgid "Suppliers"
msgstr "Leverandører"

#: part/serializers.py:961 templates/email/low_stock_notification.html:16
#: templates/email/part_event_notification.html:17
msgid "Total Stock"
msgstr "Total lagerbeholdning"

#: part/serializers.py:967
msgid "Unallocated Stock"
msgstr ""

#: part/serializers.py:973
msgid "Variant Stock"
msgstr ""

#: part/serializers.py:1006
msgid "Duplicate Part"
msgstr "Dupliser del"

#: part/serializers.py:1007
msgid "Copy initial data from another Part"
msgstr "Kopier innledende data fra en annen del"

#: part/serializers.py:1013
msgid "Initial Stock"
msgstr "Innledende lagerbeholdning"

#: part/serializers.py:1014
msgid "Create Part with initial stock quantity"
msgstr "Lag en del med innledende lagermengde"

#: part/serializers.py:1020
msgid "Supplier Information"
msgstr "Leverandøropplysninger"

#: part/serializers.py:1021
msgid "Add initial supplier information for this part"
msgstr "Legg til innledende leverandørinformasjon for denne delen"

#: part/serializers.py:1029
msgid "Copy Category Parameters"
msgstr "Kopier kategoriparametre"

#: part/serializers.py:1030
msgid "Copy parameter templates from selected part category"
msgstr "Kopier parametermaler fra valgt delkategori"

#: part/serializers.py:1035
msgid "Existing Image"
msgstr "Eksisterende bilde"

#: part/serializers.py:1036
msgid "Filename of an existing part image"
msgstr "Filnavn for et eksisterende del-bilde"

#: part/serializers.py:1053
msgid "Image file does not exist"
msgstr "Bildefilen finnes ikke"

#: part/serializers.py:1257
msgid "Limit stocktake report to a particular part, and any variant parts"
msgstr "Begrens lagerbeholdningsrapport til en bestemt del og enhver variant av delen"

#: part/serializers.py:1267
msgid "Limit stocktake report to a particular part category, and any child categories"
msgstr "Begrens lagerbeholdningsrapport til en bestemt delkategori og alle underkategorier"

#: part/serializers.py:1277
msgid "Limit stocktake report to a particular stock location, and any child locations"
msgstr "Begrens lagerbeholdningsrapport til en bestemt plasering og eventuelle underplasseringer"

#: part/serializers.py:1283
msgid "Exclude External Stock"
msgstr "Ekskluder ekstern lagerbeholdning"

#: part/serializers.py:1284
msgid "Exclude stock items in external locations"
msgstr "Ekskluder lagervarer i eksterne lokasjoner"

#: part/serializers.py:1289
msgid "Generate Report"
msgstr "Generer rapport"

#: part/serializers.py:1290
msgid "Generate report file containing calculated stocktake data"
msgstr "Genererer rapport som inneholder beregnede lagerdata"

#: part/serializers.py:1295
msgid "Update Parts"
msgstr "Oppdater deler"

#: part/serializers.py:1296
msgid "Update specified parts with calculated stocktake data"
msgstr "Oppdater spesifiserte deler med beregnede lagerbeholdningsdata"

#: part/serializers.py:1304
msgid "Stocktake functionality is not enabled"
msgstr "Lagerbeholdningsfunksjonalitet er ikke aktivert"

#: part/serializers.py:1309
msgid "Background worker check failed"
msgstr "Sjekk av bakgrunnsarbeider mislyktes"

#: part/serializers.py:1425
msgid "Minimum Price"
msgstr "Minstepris"

#: part/serializers.py:1426
msgid "Override calculated value for minimum price"
msgstr "Overstyr beregnet verdi for minimumspris"

#: part/serializers.py:1433
msgid "Minimum price currency"
msgstr "Valuta for minstepris"

#: part/serializers.py:1440
msgid "Maximum Price"
msgstr "Makspris"

#: part/serializers.py:1441
msgid "Override calculated value for maximum price"
msgstr "Overstyr beregnet verdi for maksimal pris"

#: part/serializers.py:1448
msgid "Maximum price currency"
msgstr "Valuta for maksimal pris"

#: part/serializers.py:1477
msgid "Update"
msgstr "Oppdater"

#: part/serializers.py:1478
msgid "Update pricing for this part"
msgstr "Oppdater priser for denne delen"

#: part/serializers.py:1501
#, python-brace-format
msgid "Could not convert from provided currencies to {default_currency}"
msgstr "Kan ikke konvertere fra gitte valutaer til {default_currency}"

#: part/serializers.py:1508
msgid "Minimum price must not be greater than maximum price"
msgstr "Minsteprisen kan ikke være større enn maksimal pris"

#: part/serializers.py:1511
msgid "Maximum price must not be less than minimum price"
msgstr "Maksimal pris kan ikke være mindre enn minstepris"

#: part/serializers.py:1664
msgid "Select the parent assembly"
msgstr ""

#: part/serializers.py:1678
msgid "Select the component part"
msgstr ""

#: part/serializers.py:1698
msgid "Can Build"
msgstr "Kan Produsere"

#: part/serializers.py:1925
msgid "Select part to copy BOM from"
msgstr "Velg del å kopiere BOM fra"

#: part/serializers.py:1933
msgid "Remove Existing Data"
msgstr "Fjern eksisterende data"

#: part/serializers.py:1934
msgid "Remove existing BOM items before copying"
msgstr "Fjern eksisterende BOM-artikler før kopiering"

#: part/serializers.py:1939
msgid "Include Inherited"
msgstr "Inkluder arvede"

#: part/serializers.py:1940
msgid "Include BOM items which are inherited from templated parts"
msgstr "Inkluder BOM-artikler som er arvet fra maldeler"

#: part/serializers.py:1945
msgid "Skip Invalid Rows"
msgstr "Hopp over ugyldige rader"

#: part/serializers.py:1946
msgid "Enable this option to skip invalid rows"
msgstr "Aktiver dette alternativet for å hoppe over ugyldige rader"

#: part/serializers.py:1951
msgid "Copy Substitute Parts"
msgstr "Kopier erstatningsdeler"

#: part/serializers.py:1952
msgid "Copy substitute parts when duplicate BOM items"
msgstr "Kopier erstatningsdeler når BOM-elementer dupliseres"

#: part/stocktake.py:218
msgid "Part ID"
msgstr "Del-ID"

#: part/stocktake.py:220
msgid "Part Description"
msgstr "Delbeskrivelse"

#: part/stocktake.py:221
msgid "Category ID"
msgstr "Kategori-ID"

#: part/stocktake.py:224
msgid "Total Quantity"
msgstr "Totalt Antall"

#: part/stocktake.py:225
msgid "Total Cost Min"
msgstr "Total Kostnad Min"

#: part/stocktake.py:226
msgid "Total Cost Max"
msgstr "Total Kostnad Max"

#: part/stocktake.py:284
msgid "Stocktake Report Available"
msgstr "Lagertellingsrapport tilgjengelig"

#: part/stocktake.py:285
msgid "A new stocktake report is available for download"
msgstr "En ny lagertellingsrapport er tilgjengelig for nedlasting"

#: part/tasks.py:38
msgid "Low stock notification"
msgstr "Varsel om lav lagerbeholdning"

#: part/tasks.py:40
#, python-brace-format
msgid "The available stock for {part.name} has fallen below the configured minimum level"
msgstr "Tilgjengelig lagerbeholdning for {part.name} har falt under det konfigurerte minimumsnivået"

#: plugin/api.py:78
msgid "Builtin"
msgstr ""

#: plugin/api.py:92
msgid "Mandatory"
msgstr ""

#: plugin/api.py:103
msgid "Sample"
msgstr ""

#: plugin/api.py:117 plugin/models.py:159
msgid "Installed"
msgstr "Installert"

#: plugin/api.py:184
msgid "Plugin cannot be deleted as it is currently active"
msgstr ""

#: plugin/base/action/api.py:34
msgid "No action specified"
msgstr "Ingen handling spesifisert"

#: plugin/base/action/api.py:46
msgid "No matching action found"
msgstr "Ingen samsvarende handling funnet"

#: plugin/base/barcodes/api.py:211
msgid "No match found for barcode data"
msgstr "Ingen treff funnet for strekkodedata"

#: plugin/base/barcodes/api.py:215
msgid "Match found for barcode data"
msgstr "Treff funnet for strekkodedata"

#: plugin/base/barcodes/api.py:253 plugin/base/barcodes/serializers.py:77
msgid "Model is not supported"
msgstr ""

#: plugin/base/barcodes/api.py:258
msgid "Model instance not found"
msgstr ""

#: plugin/base/barcodes/api.py:287
msgid "Barcode matches existing item"
msgstr "Strekkode samsvarer med ekisterende element"

#: plugin/base/barcodes/api.py:418
msgid "No matching part data found"
msgstr "Ingen samsvarende del-data funnet"

#: plugin/base/barcodes/api.py:434
msgid "No matching supplier parts found"
msgstr "Finner ingen matchende leverandørdeler"

#: plugin/base/barcodes/api.py:437
msgid "Multiple matching supplier parts found"
msgstr "Flere samsvarende leverandørdeler funnet"

#: plugin/base/barcodes/api.py:450 plugin/base/barcodes/api.py:677
msgid "No matching plugin found for barcode data"
msgstr ""

#: plugin/base/barcodes/api.py:460
msgid "Matched supplier part"
msgstr "Fant leverandørdel"

#: plugin/base/barcodes/api.py:528
msgid "Item has already been received"
msgstr "Artikkelen er allerede mottatt"

#: plugin/base/barcodes/api.py:576
msgid "No plugin match for supplier barcode"
msgstr ""

#: plugin/base/barcodes/api.py:625
msgid "Multiple matching line items found"
msgstr "Flere samsvarende elementer funnet"

#: plugin/base/barcodes/api.py:628
msgid "No matching line item found"
msgstr "Ingen samsvarende element funnet"

#: plugin/base/barcodes/api.py:674
msgid "No sales order provided"
msgstr ""

#: plugin/base/barcodes/api.py:683
msgid "Barcode does not match an existing stock item"
msgstr "Strekkoden samsvarer ikke med eksisterende lagervare"

#: plugin/base/barcodes/api.py:699
msgid "Stock item does not match line item"
msgstr "Lagervare samsvarer ikke med linjeelement"

#: plugin/base/barcodes/api.py:729
msgid "Insufficient stock available"
msgstr "Utilstrekkelig lagerbeholdning"

#: plugin/base/barcodes/api.py:742
msgid "Stock item allocated to sales order"
msgstr "Lagervaren er tildelt en salgsordre"

#: plugin/base/barcodes/api.py:745
msgid "Not enough information"
msgstr "Ikke nok informasjon"

#: plugin/base/barcodes/mixins.py:308
#: plugin/builtin/barcodes/inventree_barcode.py:101
msgid "Found matching item"
msgstr ""

#: plugin/base/barcodes/mixins.py:374
msgid "Supplier part does not match line item"
msgstr ""

#: plugin/base/barcodes/mixins.py:377
msgid "Line item is already completed"
msgstr ""

#: plugin/base/barcodes/mixins.py:414
msgid "Further information required to receive line item"
msgstr "Mer informasjon nødvendig for å motta artikkelen"

#: plugin/base/barcodes/mixins.py:422
msgid "Received purchase order line item"
msgstr "Mottok ordreartikkelen"

#: plugin/base/barcodes/mixins.py:429
msgid "Failed to receive line item"
msgstr ""

#: plugin/base/barcodes/serializers.py:53
msgid "Scanned barcode data"
msgstr "Skannet strekkodedata"

#: plugin/base/barcodes/serializers.py:62
msgid "Model name to generate barcode for"
msgstr ""

#: plugin/base/barcodes/serializers.py:67
msgid "Primary key of model object to generate barcode for"
msgstr ""

#: plugin/base/barcodes/serializers.py:137
msgid "Purchase Order to allocate items against"
msgstr "Innkjøpsordre å tildele artikler mot"

#: plugin/base/barcodes/serializers.py:143
msgid "Purchase order is not open"
msgstr ""

#: plugin/base/barcodes/serializers.py:161
msgid "Supplier to receive items from"
msgstr ""

#: plugin/base/barcodes/serializers.py:168
msgid "PurchaseOrder to receive items against"
msgstr "Innkjøpsordre å motta artikler mot"

#: plugin/base/barcodes/serializers.py:174
msgid "Purchase order has not been placed"
msgstr "Innkjøpsordren har ikke blitt sendt"

#: plugin/base/barcodes/serializers.py:182
msgid "Location to receive items into"
msgstr "Plassering å motta deler til"

#: plugin/base/barcodes/serializers.py:188
msgid "Cannot select a structural location"
msgstr "Kan ikke velge en strukturell plassering"

#: plugin/base/barcodes/serializers.py:196
msgid "Purchase order line item to receive items against"
msgstr ""

#: plugin/base/barcodes/serializers.py:202
msgid "Automatically allocate stock items to the purchase order"
msgstr ""

#: plugin/base/barcodes/serializers.py:215
msgid "Sales Order to allocate items against"
msgstr "Salgsordre å tildele artikler mot"

#: plugin/base/barcodes/serializers.py:221
msgid "Sales order is not open"
msgstr ""

#: plugin/base/barcodes/serializers.py:229
msgid "Sales order line item to allocate items against"
msgstr "Salgsordrelinje å tildele artikler mot"

#: plugin/base/barcodes/serializers.py:236
msgid "Sales order shipment to allocate items against"
msgstr "Salgsordre-forsendelse å tildele artikler mot"

#: plugin/base/barcodes/serializers.py:242
msgid "Shipment has already been delivered"
msgstr "Forsendelsen er allerede levert"

#: plugin/base/barcodes/serializers.py:247
msgid "Quantity to allocate"
msgstr "Antall å tildele"

#: plugin/base/label/label.py:39
msgid "Label printing failed"
msgstr "Utskrift av etikett mislyktes"

#: plugin/base/label/mixins.py:55
msgid "Error rendering label to PDF"
msgstr ""

#: plugin/base/label/mixins.py:69
msgid "Error rendering label to HTML"
msgstr ""

#: plugin/base/label/mixins.py:146
msgid "No items provided to print"
msgstr ""

#: plugin/base/ui/serializers.py:30
msgid "Plugin Name"
msgstr ""

#: plugin/base/ui/serializers.py:34
msgid "Feature Type"
msgstr ""

#: plugin/base/ui/serializers.py:39
msgid "Feature Label"
msgstr ""

#: plugin/base/ui/serializers.py:44
msgid "Feature Title"
msgstr ""

#: plugin/base/ui/serializers.py:49
msgid "Feature Description"
msgstr ""

#: plugin/base/ui/serializers.py:54
msgid "Feature Icon"
msgstr ""

#: plugin/base/ui/serializers.py:58
msgid "Feature Options"
msgstr ""

#: plugin/base/ui/serializers.py:61
msgid "Feature Context"
msgstr ""

#: plugin/base/ui/serializers.py:64
msgid "Feature Source (javascript)"
msgstr ""

#: plugin/builtin/barcodes/inventree_barcode.py:27
msgid "InvenTree Barcodes"
msgstr "InvenTree-strekkoder"

#: plugin/builtin/barcodes/inventree_barcode.py:28
msgid "Provides native support for barcodes"
msgstr "Gir innebygd støtte for strekkoder"

#: plugin/builtin/barcodes/inventree_barcode.py:30
#: plugin/builtin/exporter/bom_exporter.py:61
#: plugin/builtin/exporter/inventree_exporter.py:17
#: plugin/builtin/exporter/part_parameter_exporter.py:36
#: plugin/builtin/integration/core_notifications.py:33
#: plugin/builtin/integration/currency_exchange.py:21
#: plugin/builtin/integration/part_notifications.py:25
#: plugin/builtin/labels/inventree_label.py:26
#: plugin/builtin/labels/inventree_machine.py:64
#: plugin/builtin/labels/label_sheet.py:71
#: plugin/builtin/suppliers/digikey.py:19 plugin/builtin/suppliers/lcsc.py:21
#: plugin/builtin/suppliers/mouser.py:19 plugin/builtin/suppliers/tme.py:21
msgid "InvenTree contributors"
msgstr "InvenTree-bidragsytere"

#: plugin/builtin/barcodes/inventree_barcode.py:34
msgid "Internal Barcode Format"
msgstr ""

#: plugin/builtin/barcodes/inventree_barcode.py:35
msgid "Select an internal barcode format"
msgstr ""

#: plugin/builtin/barcodes/inventree_barcode.py:37
msgid "JSON barcodes (human readable)"
msgstr ""

#: plugin/builtin/barcodes/inventree_barcode.py:38
msgid "Short barcodes (space optimized)"
msgstr ""

#: plugin/builtin/barcodes/inventree_barcode.py:43
msgid "Short Barcode Prefix"
msgstr ""

#: plugin/builtin/barcodes/inventree_barcode.py:45
msgid "Customize the prefix used for short barcodes, may be useful for environments with multiple InvenTree instances"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:17
msgid "Levels"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:18
msgid "Number of levels to export"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:23
#: plugin/builtin/exporter/part_parameter_exporter.py:17
msgid "Stock Data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:23
#: plugin/builtin/exporter/part_parameter_exporter.py:17
msgid "Include part stock data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:27
#: plugin/builtin/exporter/part_parameter_exporter.py:21
msgid "Pricing Data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:27
#: plugin/builtin/exporter/part_parameter_exporter.py:21
msgid "Include part pricing data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:31
msgid "Supplier Data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:31
msgid "Include supplier data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:36
msgid "Manufacturer Data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:37
msgid "Include manufacturer data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:42
msgid "Substitute Data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:43
msgid "Include substitute part data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:48
msgid "Parameter Data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:49
msgid "Include part parameter data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:58
msgid "Multi-Level BOM Exporter"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:59
msgid "Provides support for exporting multi-level BOMs"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:96
msgid "BOM Level"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:102
#, python-brace-format
msgid "Substitute {n}"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:108
#, python-brace-format
msgid "Supplier {n}"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:109
#, python-brace-format
msgid "Supplier {n} SKU"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:110
#, python-brace-format
msgid "Supplier {n} MPN"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:116
#, python-brace-format
msgid "Manufacturer {n}"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:117
#, python-brace-format
msgid "Manufacturer {n} MPN"
msgstr ""

#: plugin/builtin/exporter/inventree_exporter.py:14
msgid "InvenTree Generic Exporter"
msgstr ""

#: plugin/builtin/exporter/inventree_exporter.py:15
msgid "Provides support for exporting data from InvenTree"
msgstr ""

#: plugin/builtin/exporter/part_parameter_exporter.py:33
msgid "Part Parameter Exporter"
msgstr ""

#: plugin/builtin/exporter/part_parameter_exporter.py:34
msgid "Exporter for part parameter data"
msgstr ""

#: plugin/builtin/integration/core_notifications.py:32
msgid "InvenTree Notifications"
msgstr "InvenTree-varsler"

#: plugin/builtin/integration/core_notifications.py:34
msgid "Integrated outgoing notification methods"
msgstr "Integrerte utgående varslingsmetoder"

#: plugin/builtin/integration/core_notifications.py:39
#: plugin/builtin/integration/core_notifications.py:78
msgid "Enable email notifications"
msgstr "Aktiver epostvarsler"

#: plugin/builtin/integration/core_notifications.py:40
#: plugin/builtin/integration/core_notifications.py:79
msgid "Allow sending of emails for event notifications"
msgstr "Tillat sending av e-post for hendelsesvarsler"

#: plugin/builtin/integration/core_notifications.py:45
msgid "Enable slack notifications"
msgstr "Aktiver Slack-varsler"

#: plugin/builtin/integration/core_notifications.py:47
msgid "Allow sending of slack channel messages for event notifications"
msgstr "Tillat sending av Slack-kanalmeldinger for hendelsesvarsler"

#: plugin/builtin/integration/core_notifications.py:53
msgid "Slack incoming webhook url"
msgstr "Slack innkommende webhook"

#: plugin/builtin/integration/core_notifications.py:54
msgid "URL that is used to send messages to a slack channel"
msgstr "URL brukt til å sende meldinger til en Slack-kanal"

#: plugin/builtin/integration/core_notifications.py:164
msgid "Open link"
msgstr "Åpne lenke"

#: plugin/builtin/integration/currency_exchange.py:22
msgid "InvenTree Currency Exchange"
msgstr "InvenTree valutautveksling"

#: plugin/builtin/integration/currency_exchange.py:23
msgid "Default currency exchange integration"
msgstr "Standard valutaintegrasjon"

#: plugin/builtin/integration/part_notifications.py:24
msgid "Part Notifications"
msgstr ""

#: plugin/builtin/integration/part_notifications.py:26
msgid "Notify users about part changes"
msgstr ""

#: plugin/builtin/integration/part_notifications.py:31
msgid "Send notifications"
msgstr ""

#: plugin/builtin/integration/part_notifications.py:32
msgid "Send notifications for part changes to subscribed users"
msgstr ""

#: plugin/builtin/integration/part_notifications.py:49
msgid "Changed part notification"
msgstr ""

#: plugin/builtin/integration/part_notifications.py:59
#, python-brace-format
msgid "The part `{part.name}` has been triggered with a `{part_action}` event"
msgstr ""

#: plugin/builtin/labels/inventree_label.py:23
msgid "InvenTree PDF label printer"
msgstr "InvenTree PDF etikettskriver"

#: plugin/builtin/labels/inventree_label.py:24
msgid "Provides native support for printing PDF labels"
msgstr "Gir innebygd støtte for å skrive ut PDF-etiketter"

#: plugin/builtin/labels/inventree_label.py:32
#: plugin/builtin/labels/label_sheet.py:77
msgid "Debug mode"
msgstr "Feilsøkingsmodus"

#: plugin/builtin/labels/inventree_label.py:33
#: plugin/builtin/labels/label_sheet.py:78
msgid "Enable debug mode - returns raw HTML instead of PDF"
msgstr "Aktiver feilsøkingsmodus - returnerer rå HTML i stedet for PDF"

#: plugin/builtin/labels/inventree_machine.py:61
msgid "InvenTree machine label printer"
msgstr ""

#: plugin/builtin/labels/inventree_machine.py:62
msgid "Provides support for printing using a machine"
msgstr ""

#: plugin/builtin/labels/inventree_machine.py:164
msgid "last used"
msgstr ""

#: plugin/builtin/labels/inventree_machine.py:181
msgid "Options"
msgstr ""

#: plugin/builtin/labels/label_sheet.py:30
msgid "Page size for the label sheet"
msgstr "Sidestørrelse på etikett-arket"

#: plugin/builtin/labels/label_sheet.py:35
msgid "Skip Labels"
msgstr "Hopp over etiketter"

#: plugin/builtin/labels/label_sheet.py:36
msgid "Skip this number of labels when printing label sheets"
msgstr "Hopp over dette antallet etiketter når det skrives ut etiketterark"

#: plugin/builtin/labels/label_sheet.py:42
msgid "Border"
msgstr "Kantlinjer"

#: plugin/builtin/labels/label_sheet.py:43
msgid "Print a border around each label"
msgstr "Skriv ut en kant rundt hver etikett"

#: plugin/builtin/labels/label_sheet.py:48 report/models.py:371
msgid "Landscape"
msgstr "Liggende"

#: plugin/builtin/labels/label_sheet.py:49
msgid "Print the label sheet in landscape mode"
msgstr "Skriv ut etikett-arket i liggende modus"

#: plugin/builtin/labels/label_sheet.py:54
msgid "Page Margin"
msgstr ""

#: plugin/builtin/labels/label_sheet.py:55
msgid "Margin around the page in mm"
msgstr ""

#: plugin/builtin/labels/label_sheet.py:68
msgid "InvenTree Label Sheet Printer"
msgstr "InvenTree etikett-ark skriver"

#: plugin/builtin/labels/label_sheet.py:69
msgid "Arrays multiple labels onto a single sheet"
msgstr "Sprer ut flere etiketter på ett enkelt ark"

#: plugin/builtin/labels/label_sheet.py:121
msgid "Label is too large for page size"
msgstr "Etiketten er for stor for sidestørrelse"

#: plugin/builtin/labels/label_sheet.py:160
msgid "No labels were generated"
msgstr "Ingen etiketter ble generert"

#: plugin/builtin/suppliers/digikey.py:16
msgid "Supplier Integration - DigiKey"
msgstr "Leverandørintegrasjon - DigiKey"

#: plugin/builtin/suppliers/digikey.py:17
msgid "Provides support for scanning DigiKey barcodes"
msgstr "Gir støtte for å skanne DigiKey-strekkoder"

#: plugin/builtin/suppliers/digikey.py:26
msgid "The Supplier which acts as 'DigiKey'"
msgstr "Leverandøren som fungerer som 'DigiKey'"

#: plugin/builtin/suppliers/lcsc.py:18
msgid "Supplier Integration - LCSC"
msgstr "Leverandørintegrasjon - LCSC"

#: plugin/builtin/suppliers/lcsc.py:19
msgid "Provides support for scanning LCSC barcodes"
msgstr "Gir støtte for å skanne LCSC-strekkoder"

#: plugin/builtin/suppliers/lcsc.py:27
msgid "The Supplier which acts as 'LCSC'"
msgstr "Leverandøren som fungerer som \"LCSC\""

#: plugin/builtin/suppliers/mouser.py:16
msgid "Supplier Integration - Mouser"
msgstr "Leverandørintegrasjon - Mouser"

#: plugin/builtin/suppliers/mouser.py:17
msgid "Provides support for scanning Mouser barcodes"
msgstr "Gir støtte for å skanne Mouser-strekkoder"

#: plugin/builtin/suppliers/mouser.py:25
msgid "The Supplier which acts as 'Mouser'"
msgstr "Leverandøren som fungerer som 'Mouser'"

#: plugin/builtin/suppliers/tme.py:18
msgid "Supplier Integration - TME"
msgstr "Leverandørintegrasjon - TME"

#: plugin/builtin/suppliers/tme.py:19
msgid "Provides support for scanning TME barcodes"
msgstr "Gir støtte for å skanne TME-strekkoder"

#: plugin/builtin/suppliers/tme.py:27
msgid "The Supplier which acts as 'TME'"
msgstr "Leverandøren som fungerer som \"TME\""

#: plugin/installer.py:229 plugin/installer.py:307
msgid "Only staff users can administer plugins"
msgstr ""

#: plugin/installer.py:232
msgid "Plugin installation is disabled"
msgstr ""

#: plugin/installer.py:269
msgid "Installed plugin successfully"
msgstr "Installasjon av utvidelse vellykket"

#: plugin/installer.py:274
#, python-brace-format
msgid "Installed plugin into {path}"
msgstr "Installerte utvidelsen til {path}"

#: plugin/installer.py:298
msgid "Plugin was not found in registry"
msgstr ""

#: plugin/installer.py:301
msgid "Plugin is not a packaged plugin"
msgstr ""

#: plugin/installer.py:304
msgid "Plugin package name not found"
msgstr ""

#: plugin/installer.py:324
msgid "Plugin uninstalling is disabled"
msgstr ""

#: plugin/installer.py:328
msgid "Plugin cannot be uninstalled as it is currently active"
msgstr ""

#: plugin/installer.py:332
msgid "Plugin is not installed"
msgstr ""

#: plugin/installer.py:348
msgid "Plugin installation not found"
msgstr ""

#: plugin/installer.py:363
msgid "Uninstalled plugin successfully"
msgstr ""

#: plugin/models.py:38
msgid "Plugin Configuration"
msgstr "Konfigurasjon av utvidelse"

#: plugin/models.py:39
msgid "Plugin Configurations"
msgstr "Konfigurasjon av utvidelser"

#: plugin/models.py:46
msgid "Key of plugin"
msgstr "Utvidelsens \"Key\""

#: plugin/models.py:54
msgid "PluginName of the plugin"
msgstr "Navn på utvidelsen"

#: plugin/models.py:61 plugin/serializers.py:119
msgid "Package Name"
msgstr "Pakkenavn"

#: plugin/models.py:63
msgid "Name of the installed package, if the plugin was installed via PIP"
msgstr ""

#: plugin/models.py:68
msgid "Is the plugin active"
msgstr "Er utvidelsen aktiv"

#: plugin/models.py:168
msgid "Sample plugin"
msgstr "Eksempel-utvidelse"

#: plugin/models.py:176
msgid "Builtin Plugin"
msgstr "Innebygd utvidelse"

#: plugin/models.py:184
msgid "Mandatory Plugin"
msgstr ""

#: plugin/models.py:189
msgid "Package Plugin"
msgstr ""

#: plugin/models.py:268
msgid "Plugin"
msgstr "Utvidelse"

#: plugin/models.py:315
msgid "Method"
msgstr "Metode"

#: plugin/plugin.py:312
msgid "No author found"
msgstr "Ingen forfatter funnet"

#: plugin/registry.py:611
#, python-brace-format
msgid "Plugin '{p}' is not compatible with the current InvenTree version {v}"
msgstr "Utvidensen '{p}' er ikke kompatibel med nåværende InvenTree-versjon {v}"

#: plugin/registry.py:614
#, python-brace-format
msgid "Plugin requires at least version {v}"
msgstr "Utvidelsen krever minst versjon {v}"

#: plugin/registry.py:616
#, python-brace-format
msgid "Plugin requires at most version {v}"
msgstr "Utvidelsen krever maks versjon {v}"

#: plugin/samples/integration/sample.py:52
msgid "Enable PO"
msgstr "Aktiver PO"

#: plugin/samples/integration/sample.py:53
msgid "Enable PO functionality in InvenTree interface"
msgstr "Aktiver Innkjøpsordrefunksjonalitet i InvenTree-grensesnittet"

#: plugin/samples/integration/sample.py:58
msgid "API Key"
msgstr "API-nøkkel"

#: plugin/samples/integration/sample.py:59
msgid "Key required for accessing external API"
msgstr "Nøkkel kreves for tilgang til eksternt API"

#: plugin/samples/integration/sample.py:63
msgid "Numerical"
msgstr "Numerisk"

#: plugin/samples/integration/sample.py:64
msgid "A numerical setting"
msgstr "En numerisk innstilling"

#: plugin/samples/integration/sample.py:69
msgid "Choice Setting"
msgstr "Valginnstilling"

#: plugin/samples/integration/sample.py:70
msgid "A setting with multiple choices"
msgstr "En innstilling med flere valg"

#: plugin/samples/integration/sample_currency_exchange.py:15
msgid "Sample currency exchange plugin"
msgstr "Eksempel valutakonverterings-utvidelse"

#: plugin/samples/integration/sample_currency_exchange.py:18
msgid "InvenTree Contributors"
msgstr "InvenTree-bidragsytere"

#: plugin/samples/integration/user_interface_sample.py:27
msgid "Enable Part Panels"
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:28
msgid "Enable custom panels for Part views"
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:33
msgid "Enable Purchase Order Panels"
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:34
msgid "Enable custom panels for Purchase Order views"
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:39
msgid "Enable Broken Panels"
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:40
msgid "Enable broken panels for testing"
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:45
msgid "Enable Dynamic Panel"
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:46
msgid "Enable dynamic panels for testing"
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:98
msgid "Part Panel"
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:131
msgid "Broken Dashboard Item"
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:133
msgid "This is a broken dashboard item - it will not render!"
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:139
msgid "Sample Dashboard Item"
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:141
msgid "This is a sample dashboard item. It renders a simple string of HTML content."
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:147
msgid "Context Dashboard Item"
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:161
msgid "Admin Dashboard Item"
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:162
msgid "This is an admin-only dashboard item."
msgstr ""

#: plugin/serializers.py:86
msgid "Source File"
msgstr ""

#: plugin/serializers.py:87
msgid "Path to the source file for admin integration"
msgstr ""

#: plugin/serializers.py:94
msgid "Optional context data for the admin integration"
msgstr ""

#: plugin/serializers.py:110
msgid "Source URL"
msgstr "Kilde-URL"

#: plugin/serializers.py:112
msgid "Source for the package - this can be a custom registry or a VCS path"
msgstr "Kilde for pakken - dette kan være et egendefinert register eller en VCS-sti"

#: plugin/serializers.py:121
msgid "Name for the Plugin Package - can also contain a version indicator"
msgstr "Navn på utvidelsespakke – kan også inneholde en versjonsindikator"

#: plugin/serializers.py:128
msgid "Version"
msgstr "Versjon"

#: plugin/serializers.py:130
msgid "Version specifier for the plugin. Leave blank for latest version."
msgstr ""

#: plugin/serializers.py:135
msgid "Confirm plugin installation"
msgstr "Bekreft installasjon av utvidelse"

#: plugin/serializers.py:137
msgid "This will install this plugin now into the current instance. The instance will go into maintenance."
msgstr "Dette vil installere denne utvidelsen nå i gjeldende instans. Instansen vil gå i vedlikehold."

#: plugin/serializers.py:150
msgid "Installation not confirmed"
msgstr "Installasjonen ble ikke bekreftet"

#: plugin/serializers.py:152
msgid "Either packagename of URL must be provided"
msgstr "Enten pakkenavn eller URL må angis"

#: plugin/serializers.py:188
msgid "Full reload"
msgstr "Full omlasting"

#: plugin/serializers.py:189
msgid "Perform a full reload of the plugin registry"
msgstr "Utfør en full omlasting av utvidelsesregisteret"

#: plugin/serializers.py:195
msgid "Force reload"
msgstr "Tvangsomlasting"

#: plugin/serializers.py:197
msgid "Force a reload of the plugin registry, even if it is already loaded"
msgstr "Tving en omlasting av utvidelsesregisteret, selv om det allerede er lastet"

#: plugin/serializers.py:204
msgid "Collect plugins"
msgstr "Hent inn utvidelser"

#: plugin/serializers.py:205
msgid "Collect plugins and add them to the registry"
msgstr "Hent inn utvidelser og legg dem til i registeret"

#: plugin/serializers.py:232
msgid "Activate Plugin"
msgstr "Aktivér utvidelse"

#: plugin/serializers.py:233
msgid "Activate this plugin"
msgstr "Aktivér denne utvidelsen"

#: plugin/serializers.py:253
msgid "Delete configuration"
msgstr ""

#: plugin/serializers.py:254
msgid "Delete the plugin configuration from the database"
msgstr ""

#: report/api.py:44 report/serializers.py:98 report/serializers.py:148
msgid "Items"
msgstr ""

#: report/api.py:121
msgid "Plugin not found"
msgstr ""

#: report/api.py:123
msgid "Plugin is not active"
msgstr ""

#: report/api.py:125
msgid "Plugin does not support label printing"
msgstr ""

#: report/api.py:173
msgid "Invalid label dimensions"
msgstr ""

#: report/api.py:188 report/api.py:267
msgid "No valid items provided to template"
msgstr ""

#: report/helpers.py:43
msgid "A4"
msgstr ""

#: report/helpers.py:44
msgid "A3"
msgstr ""

#: report/helpers.py:45
msgid "Legal"
msgstr ""

#: report/helpers.py:46
msgid "Letter"
msgstr ""

#: report/models.py:117
msgid "Template file with this name already exists"
msgstr ""

#: report/models.py:204
msgid "Template name"
msgstr "Malnavn"

#: report/models.py:210
msgid "Template description"
msgstr ""

#: report/models.py:216
msgid "Revision number (auto-increments)"
msgstr ""

#: report/models.py:222
msgid "Attach to Model on Print"
msgstr ""

#: report/models.py:224
msgid "Save report output as an attachment against linked model instance when printing"
msgstr ""

#: report/models.py:265
msgid "Filename Pattern"
msgstr "Filnavnmønster"

#: report/models.py:266
msgid "Pattern for generating filenames"
msgstr ""

#: report/models.py:271
msgid "Template is enabled"
msgstr ""

#: report/models.py:278
msgid "Target model type for template"
msgstr ""

#: report/models.py:298
msgid "Filters"
msgstr "Filtre"

#: report/models.py:299
msgid "Template query filters (comma-separated list of key=value pairs)"
msgstr ""

#: report/models.py:358 report/models.py:564
msgid "Template file"
msgstr ""

#: report/models.py:366
msgid "Page size for PDF reports"
msgstr "Sidestørrelse for PDF-rapporter"

#: report/models.py:372
msgid "Render report in landscape orientation"
msgstr "Generer rapport i landskapsorientering"

#: report/models.py:486
#, python-brace-format
msgid "Report generated from template {self.name}"
msgstr ""

#: report/models.py:511
msgid "Error generating report"
msgstr ""

#: report/models.py:570
msgid "Width [mm]"
msgstr "Bredde [mm]"

#: report/models.py:571
msgid "Label width, specified in mm"
msgstr "Etikettbredde, spesifisert i mm"

#: report/models.py:577
msgid "Height [mm]"
msgstr "Høyde [mm]"

#: report/models.py:578
msgid "Label height, specified in mm"
msgstr "Etiketthøyde, spesifisert i mm"

#: report/models.py:688
msgid "Error printing labels"
msgstr ""

#: report/models.py:707
msgid "Snippet"
msgstr "Snutt"

#: report/models.py:708
msgid "Report snippet file"
msgstr "Rapportsnuttfil"

#: report/models.py:715
msgid "Snippet file description"
msgstr "Filbeskrivelse for snutt"

#: report/models.py:733
msgid "Asset"
msgstr "Ressurs"

#: report/models.py:734
msgid "Report asset file"
msgstr "Rapportressursfil"

#: report/models.py:741
msgid "Asset file description"
msgstr "Ressursfilbeskrivelse"

#: report/serializers.py:91
msgid "Select report template"
msgstr ""

#: report/serializers.py:99 report/serializers.py:149
msgid "List of item primary keys to include in the report"
msgstr ""

#: report/serializers.py:132
msgid "Select label template"
msgstr ""

#: report/serializers.py:140
msgid "Printing Plugin"
msgstr ""

#: report/serializers.py:141
msgid "Select plugin to use for label printing"
msgstr ""

#: report/templates/label/part_label.html:31
#: report/templates/label/stockitem_qr.html:21
#: report/templates/label/stocklocation_qr.html:20
msgid "QR Code"
msgstr "QR-kode"

#: report/templates/label/part_label_code128.html:31
#: report/templates/label/stocklocation_qr_and_text.html:31
msgid "QR code"
msgstr "QR-kode"

#: report/templates/report/inventree_bill_of_materials_report.html:100
msgid "Bill of Materials"
msgstr "Stykkliste (BOM)"

#: report/templates/report/inventree_bill_of_materials_report.html:133
msgid "Materials needed"
msgstr "Nødvendige materialer"

#: report/templates/report/inventree_build_order_report.html:98
#: report/templates/report/inventree_purchase_order_report.html:40
#: report/templates/report/inventree_sales_order_report.html:40
#: report/templates/report/inventree_sales_order_shipment_report.html:37
#: report/templates/report/inventree_test_report.html:84
#: report/templates/report/inventree_test_report.html:162
msgid "Part image"
msgstr "Bilde av del"

#: report/templates/report/inventree_build_order_report.html:121
msgid "Issued"
msgstr "Utstedt"

#: report/templates/report/inventree_build_order_report.html:146
msgid "Required For"
msgstr "Kreves for"

#: report/templates/report/inventree_purchase_order_report.html:15
msgid "Supplier was deleted"
msgstr "Leverandør ble slettet"

#: report/templates/report/inventree_purchase_order_report.html:30
#: report/templates/report/inventree_sales_order_report.html:30
msgid "Unit Price"
msgstr "Enhetspris"

#: report/templates/report/inventree_purchase_order_report.html:55
#: report/templates/report/inventree_return_order_report.html:48
#: report/templates/report/inventree_sales_order_report.html:55
msgid "Extra Line Items"
msgstr "Ekstra linjeelementer"

#: report/templates/report/inventree_purchase_order_report.html:72
#: report/templates/report/inventree_sales_order_report.html:72
msgid "Total"
msgstr ""

#: report/templates/report/inventree_return_order_report.html:25
#: report/templates/report/inventree_sales_order_shipment_report.html:45
#: report/templates/report/inventree_test_report.html:88 stock/models.py:1006
#: stock/serializers.py:163
msgid "Serial Number"
msgstr "Serienummer"

#: report/templates/report/inventree_sales_order_shipment_report.html:23
msgid "Allocations"
msgstr "Tildelinger"

#: report/templates/report/inventree_sales_order_shipment_report.html:47
msgid "Batch"
msgstr "Parti"

#: report/templates/report/inventree_stock_location_report.html:97
msgid "Stock location items"
msgstr "Artikler ved lagerplassering"

#: report/templates/report/inventree_test_report.html:21
msgid "Stock Item Test Report"
msgstr "Testrapport for lagervare"

#: report/templates/report/inventree_test_report.html:97
msgid "Test Results"
msgstr "Testresultater"

#: report/templates/report/inventree_test_report.html:102
msgid "Test"
msgstr ""

#: report/templates/report/inventree_test_report.html:129
msgid "Pass"
msgstr "Bestått"

#: report/templates/report/inventree_test_report.html:131
msgid "Fail"
msgstr "Mislykket"

#: report/templates/report/inventree_test_report.html:138
msgid "No result (required)"
msgstr "Ingen resultat (obligatorisk)"

#: report/templates/report/inventree_test_report.html:140
msgid "No result"
msgstr "Ingen resultat"

#: report/templates/report/inventree_test_report.html:153
#: stock/serializers.py:668
msgid "Installed Items"
msgstr "Installerte artikler"

#: report/templates/report/inventree_test_report.html:167
msgid "Serial"
msgstr "Serienummer"

#: report/templatetags/report.py:144
msgid "Asset file does not exist"
msgstr "Asset-filen eksisterer ikke"

#: report/templatetags/report.py:201 report/templatetags/report.py:277
msgid "Image file not found"
msgstr "Bildefil ikke funnet"

#: report/templatetags/report.py:302
msgid "part_image tag requires a Part instance"
msgstr "part_image-taggen krever en Part-instans"

#: report/templatetags/report.py:349
msgid "company_image tag requires a Company instance"
msgstr "company_image-taggen krever en Company-instans"

#: stock/api.py:255
msgid "Filter by location depth"
msgstr ""

#: stock/api.py:275
msgid "Filter by top-level locations"
msgstr ""

#: stock/api.py:290
msgid "Include sub-locations in filtered results"
msgstr ""

#: stock/api.py:311 stock/serializers.py:1256
msgid "Parent Location"
msgstr ""

#: stock/api.py:312
msgid "Filter by parent location"
msgstr ""

#: stock/api.py:554
msgid "Part name (case insensitive)"
msgstr ""

#: stock/api.py:560
msgid "Part name contains (case insensitive)"
msgstr ""

#: stock/api.py:566
msgid "Part name (regex)"
msgstr ""

#: stock/api.py:571
msgid "Part IPN (case insensitive)"
msgstr ""

#: stock/api.py:577
msgid "Part IPN contains (case insensitive)"
msgstr ""

#: stock/api.py:583
msgid "Part IPN (regex)"
msgstr ""

#: stock/api.py:595
msgid "Minimum stock"
msgstr ""

#: stock/api.py:599
msgid "Maximum stock"
msgstr ""

#: stock/api.py:602
msgid "Status Code"
msgstr "Statuskode"

#: stock/api.py:642
msgid "External Location"
msgstr "Ekstern plassering"

#: stock/api.py:741
msgid "Consumed by Build Order"
msgstr ""

#: stock/api.py:751
msgid "Installed in other stock item"
msgstr ""

#: stock/api.py:840
msgid "Part Tree"
msgstr "Del-tre"

#: stock/api.py:862
msgid "Updated before"
msgstr ""

#: stock/api.py:866
msgid "Updated after"
msgstr ""

#: stock/api.py:870
msgid "Stocktake Before"
msgstr ""

#: stock/api.py:874
msgid "Stocktake After"
msgstr ""

#: stock/api.py:879
msgid "Expiry date before"
msgstr "Utløpsdato før"

#: stock/api.py:883
msgid "Expiry date after"
msgstr "Utløpsdato etter"

#: stock/api.py:886 stock/serializers.py:673
msgid "Stale"
msgstr "Foreldet"

#: stock/api.py:987
msgid "Quantity is required"
msgstr "Antall kreves"

#: stock/api.py:992
msgid "Valid part must be supplied"
msgstr "Gyldig del må oppgis"

#: stock/api.py:1019
msgid "The given supplier part does not exist"
msgstr "Oppgitt leverandørdel eksisterer ikke"

#: stock/api.py:1029
msgid "The supplier part has a pack size defined, but flag use_pack_size not set"
msgstr "Leverandørdelen har en pakkestørrelse definert, men flagget \"use_pack_size\" er ikke satt"

#: stock/api.py:1056
msgid "Serial numbers cannot be supplied for a non-trackable part"
msgstr "Serienumre kan ikke angis for en ikke-sporbar del"

#: stock/models.py:70
msgid "Stock Location type"
msgstr "Lagerplasseringstype"

#: stock/models.py:71
msgid "Stock Location types"
msgstr "Lagerplasseringstyper"

#: stock/models.py:97
msgid "Default icon for all locations that have no icon set (optional)"
msgstr "Standard ikom for alle plasseringer som ikke har satt et ikon (valgfritt)"

#: stock/models.py:155 stock/models.py:968
msgid "Stock Location"
msgstr "Lagerplassering"

#: stock/models.py:156 users/ruleset.py:35
msgid "Stock Locations"
msgstr "Lagerplasseringer"

#: stock/models.py:205 stock/models.py:1133
msgid "Owner"
msgstr "Eier"

#: stock/models.py:206 stock/models.py:1134
msgid "Select Owner"
msgstr "Velg eier"

#: stock/models.py:214
msgid "Stock items may not be directly located into a structural stock locations, but may be located to child locations."
msgstr "Lagervarer kan ikke knyttes direkte mot en strukturell lagerplassering, men kan knyttes mot underplasseringer."

#: stock/models.py:221 users/models.py:499
msgid "External"
msgstr "Ekstern"

#: stock/models.py:222
msgid "This is an external stock location"
msgstr "Dette er en ekstern lagerplassering"

#: stock/models.py:228
msgid "Location type"
msgstr "Plasseringstype"

#: stock/models.py:232
msgid "Stock location type of this location"
msgstr "Lagerplasseringstype for denne plasseringen"

#: stock/models.py:304
msgid "You cannot make this stock location structural because some stock items are already located into it!"
msgstr "De kan ikke gjøre denne plasseringen strukturell, da noen lagervarer allerede er plassert i den!"

#: stock/models.py:562
msgid "Part must be specified"
msgstr ""

#: stock/models.py:827
msgid "Stock items cannot be located into structural stock locations!"
msgstr "Lagervarer kan ikke plasseres i strukturelle plasseringer!"

#: stock/models.py:854 stock/serializers.py:516
msgid "Stock item cannot be created for virtual parts"
msgstr "Lagervare kan ikke opprettes for virtuelle deler"

#: stock/models.py:871
#, python-brace-format
msgid "Part type ('{self.supplier_part.part}') must be {self.part}"
msgstr "Deltype ('{self.supplier_part.part}') må være {self.part}"

#: stock/models.py:881 stock/models.py:894
msgid "Quantity must be 1 for item with a serial number"
msgstr "Antall må være 1 for produkt med et serienummer"

#: stock/models.py:884
msgid "Serial number cannot be set if quantity greater than 1"
msgstr "Serienummeret kan ikke angis hvis antall er større enn 1"

#: stock/models.py:906
msgid "Item cannot belong to itself"
msgstr "Elementet kan ikke tilhøre seg selv"

#: stock/models.py:911
msgid "Item must have a build reference if is_building=True"
msgstr "Elementet må ha en produksjonsrefereanse om is_building=True"

#: stock/models.py:924
msgid "Build reference does not point to the same part object"
msgstr "Produksjonsreferanse peker ikke til samme del-objekt"

#: stock/models.py:938
msgid "Parent Stock Item"
msgstr "Overordnet lagervare"

#: stock/models.py:950
msgid "Base part"
msgstr "Basisdel"

#: stock/models.py:960
msgid "Select a matching supplier part for this stock item"
msgstr "Velg en tilsvarende leverandørdel for denne lagervaren"

#: stock/models.py:972
msgid "Where is this stock item located?"
msgstr "Hvor er denne lagervaren plassert?"

#: stock/models.py:980 stock/serializers.py:1667
msgid "Packaging this stock item is stored in"
msgstr "Inpakningen denne lagervaren er lagret i"

#: stock/models.py:986
msgid "Installed In"
msgstr "Installert i"

#: stock/models.py:991
msgid "Is this item installed in another item?"
msgstr "Er denne artikkelen montert i en annen artikkel?"

#: stock/models.py:1010
msgid "Serial number for this item"
msgstr "Serienummer for denne artikkelen"

#: stock/models.py:1027 stock/serializers.py:1652
msgid "Batch code for this stock item"
msgstr "Batchkode for denne lagervaren"

#: stock/models.py:1032
msgid "Stock Quantity"
msgstr "Lagerantall"

#: stock/models.py:1042
msgid "Source Build"
msgstr "Kildeproduksjon"

#: stock/models.py:1045
msgid "Build for this stock item"
msgstr "Produksjon for denne lagervaren"

#: stock/models.py:1052
msgid "Consumed By"
msgstr "Brukt av"

#: stock/models.py:1055
msgid "Build order which consumed this stock item"
msgstr "Produksjonsordren som brukte denne lagervaren"

#: stock/models.py:1064
msgid "Source Purchase Order"
msgstr "Kildeinnkjøpsordre"

#: stock/models.py:1068
msgid "Purchase order for this stock item"
msgstr "Innkjøpsordre for denne lagervaren"

#: stock/models.py:1074
msgid "Destination Sales Order"
msgstr "Tildelt Salgsordre"

#: stock/models.py:1085
msgid "Expiry date for stock item. Stock will be considered expired after this date"
msgstr "Utløpsdato for lagervare. Lagerbeholdning vil bli ansett som utløpt etter denne datoen"

#: stock/models.py:1103
msgid "Delete on deplete"
msgstr "Slett når oppbrukt"

#: stock/models.py:1104
msgid "Delete this Stock Item when stock is depleted"
msgstr "Slett lagervaren når beholdningen er oppbrukt"

#: stock/models.py:1125
msgid "Single unit purchase price at time of purchase"
msgstr "Innkjøpspris per enhet på kjøpstidspunktet"

#: stock/models.py:1156
msgid "Converted to part"
msgstr "Konvertert til del"

#: stock/models.py:1721
msgid "Part is not set as trackable"
msgstr "Delen er ikke angitt som sporbar"

#: stock/models.py:1727
msgid "Quantity must be integer"
msgstr "Antall må være heltall"

#: stock/models.py:1735
#, python-brace-format
msgid "Quantity must not exceed available stock quantity ({self.quantity})"
msgstr "Antall kan ikke overstige tilgjengelig lagerbeholdning ({self.quantity})"

#: stock/models.py:1741
msgid "Serial numbers must be provided as a list"
msgstr ""

#: stock/models.py:1746
msgid "Quantity does not match serial numbers"
msgstr "Antallet stemmer ikke overens med serienumrene"

#: stock/models.py:1868 stock/models.py:2777
msgid "Test template does not exist"
msgstr ""

#: stock/models.py:1886
msgid "Stock item has been assigned to a sales order"
msgstr "Lagervare har blitt tildelt en salgsordre"

#: stock/models.py:1890
msgid "Stock item is installed in another item"
msgstr "Lagervare er montert i en annen artikkel"

#: stock/models.py:1893
msgid "Stock item contains other items"
msgstr "Lagervare inneholder andre artikler"

#: stock/models.py:1896
msgid "Stock item has been assigned to a customer"
msgstr "Lagervare har blitt tildelt til en kunde"

#: stock/models.py:1899 stock/models.py:2073
msgid "Stock item is currently in production"
msgstr "Lagervare er for tiden i produksjon"

#: stock/models.py:1902
msgid "Serialized stock cannot be merged"
msgstr "Serialisert lagerbeholdning kan ikke slås sammen"

#: stock/models.py:1909 stock/serializers.py:1545
msgid "Duplicate stock items"
msgstr "Duplisert lagervare"

#: stock/models.py:1913
msgid "Stock items must refer to the same part"
msgstr "Lagervarer må referere til samme del"

#: stock/models.py:1921
msgid "Stock items must refer to the same supplier part"
msgstr "Lagervarer må referere til samme leverandørdel"

#: stock/models.py:1926
msgid "Stock status codes must match"
msgstr "Lagerstatuskoder må være like"

#: stock/models.py:2196
msgid "StockItem cannot be moved as it is not in stock"
msgstr "Lagervare kan ikke flyttes fordi den ikke er på lager"

#: stock/models.py:2678
msgid "Stock Item Tracking"
msgstr ""

#: stock/models.py:2709
msgid "Entry notes"
msgstr "Oppføringsnotater"

#: stock/models.py:2749
msgid "Stock Item Test Result"
msgstr ""

#: stock/models.py:2780
msgid "Value must be provided for this test"
msgstr "Verdi må angis for denne testen"

#: stock/models.py:2784
msgid "Attachment must be uploaded for this test"
msgstr "Vedlegg må lastes opp for denne testen"

#: stock/models.py:2789
msgid "Invalid value for this test"
msgstr ""

#: stock/models.py:2813
msgid "Test result"
msgstr "Testresultat"

#: stock/models.py:2820
msgid "Test output value"
msgstr "Testens verdi"

#: stock/models.py:2828 stock/serializers.py:259
msgid "Test result attachment"
msgstr "Vedlegg til testresultat"

#: stock/models.py:2832
msgid "Test notes"
msgstr "Testnotater"

#: stock/models.py:2840
msgid "Test station"
msgstr ""

#: stock/models.py:2841
msgid "The identifier of the test station where the test was performed"
msgstr ""

#: stock/models.py:2847
msgid "Started"
msgstr ""

#: stock/models.py:2848
msgid "The timestamp of the test start"
msgstr ""

#: stock/models.py:2854
msgid "Finished"
msgstr ""

#: stock/models.py:2855
msgid "The timestamp of the test finish"
msgstr ""

#: stock/serializers.py:84
msgid "Generated batch code"
msgstr ""

#: stock/serializers.py:93
msgid "Select build order"
msgstr ""

#: stock/serializers.py:102
msgid "Select stock item to generate batch code for"
msgstr ""

#: stock/serializers.py:111
msgid "Select location to generate batch code for"
msgstr ""

#: stock/serializers.py:120
msgid "Select part to generate batch code for"
msgstr ""

#: stock/serializers.py:129
msgid "Select purchase order"
msgstr ""

#: stock/serializers.py:136
msgid "Enter quantity for batch code"
msgstr ""

#: stock/serializers.py:162
msgid "Generated serial number"
msgstr ""

#: stock/serializers.py:172
msgid "Select part to generate serial number for"
msgstr ""

#: stock/serializers.py:180
msgid "Quantity of serial numbers to generate"
msgstr ""

#: stock/serializers.py:248
msgid "Test template for this result"
msgstr ""

#: stock/serializers.py:272
msgid "Template ID or test name must be provided"
msgstr ""

#: stock/serializers.py:304
msgid "The test finished time cannot be earlier than the test started time"
msgstr ""

#: stock/serializers.py:341
msgid "Serial number is too large"
msgstr "Serienummeret er for høyt"

#: stock/serializers.py:487
msgid "Parent Item"
msgstr "Overodnet element"

#: stock/serializers.py:488
msgid "Parent stock item"
msgstr ""

#: stock/serializers.py:508
msgid "Use pack size when adding: the quantity defined is the number of packs"
msgstr "Bruk pakningsstørrelse når du legger til: antall definert er antall pakker"

#: stock/serializers.py:618
msgid "Supplier Part Number"
msgstr "Leverandørens delnummer"

#: stock/serializers.py:665 users/models.py:193
msgid "Expired"
msgstr "Utløpt"

#: stock/serializers.py:671
msgid "Child Items"
msgstr "Underordnede artikler"

#: stock/serializers.py:675
msgid "Tracking Items"
msgstr ""

#: stock/serializers.py:681
msgid "Purchase price of this stock item, per unit or pack"
msgstr "Innkjøpspris for denne lagervaren, per enhet eller forpakning"

#: stock/serializers.py:719
msgid "Enter number of stock items to serialize"
msgstr "Angi antall lagervarer som skal serialiseres"

#: stock/serializers.py:732
#, python-brace-format
msgid "Quantity must not exceed available stock quantity ({q})"
msgstr "Antall kan ikke overstige tilgjengelig lagerbeholdning ({q})"

#: stock/serializers.py:739
msgid "Enter serial numbers for new items"
msgstr "Angi serienummer for nye artikler"

#: stock/serializers.py:750 stock/serializers.py:1502 stock/serializers.py:1790
msgid "Destination stock location"
msgstr "Til Lagerplassering"

#: stock/serializers.py:757
msgid "Optional note field"
msgstr "Valgfritt notatfelt"

#: stock/serializers.py:767
msgid "Serial numbers cannot be assigned to this part"
msgstr "Serienummer kan ikke tilordnes denne delen"

#: stock/serializers.py:787
msgid "Serial numbers already exist"
msgstr "Seriernummer eksisterer allerede"

#: stock/serializers.py:826
msgid "Select stock item to install"
msgstr "Velg lagervare å montere"

#: stock/serializers.py:833
msgid "Quantity to Install"
msgstr "Antall å installere"

#: stock/serializers.py:834
msgid "Enter the quantity of items to install"
msgstr "Angi antallet elementer som skal installeres"

#: stock/serializers.py:839 stock/serializers.py:919 stock/serializers.py:1049
#: stock/serializers.py:1101
msgid "Add transaction note (optional)"
msgstr "Legg til transaksjonsnotat (valgfritt)"

#: stock/serializers.py:847
msgid "Quantity to install must be at least 1"
msgstr "Antall å installere må være minst 1"

#: stock/serializers.py:855
msgid "Stock item is unavailable"
msgstr "Lagervaren er utilgjengelig"

#: stock/serializers.py:866
msgid "Selected part is not in the Bill of Materials"
msgstr "Valgt del er ikke i stykklisten"

#: stock/serializers.py:879
msgid "Quantity to install must not exceed available quantity"
msgstr "Antall å installere må ikke overskride tilgjengelig antall"

#: stock/serializers.py:914
msgid "Destination location for uninstalled item"
msgstr "Lagerplassering for den avinstallerte artikkelen"

#: stock/serializers.py:949
msgid "Select part to convert stock item into"
msgstr "Velg del å konvertere lagervare til"

#: stock/serializers.py:962
msgid "Selected part is not a valid option for conversion"
msgstr "Valgt del er ikke et gyldig alternativ for konvertering"

#: stock/serializers.py:979
msgid "Cannot convert stock item with assigned SupplierPart"
msgstr "Kan ikke konvertere lagerprodukt med tildelt leverandørdel"

#: stock/serializers.py:1013
msgid "Stock item status code"
msgstr "Lagervare statuskode"

#: stock/serializers.py:1042
msgid "Destination location for returned item"
msgstr "Lagerplassering for returnert artikkel"

#: stock/serializers.py:1085
msgid "Select stock items to change status"
msgstr "Velg lagervarer for å endre status"

#: stock/serializers.py:1091
msgid "No stock items selected"
msgstr "Ingen lagervarer valgt"

#: stock/serializers.py:1185 stock/serializers.py:1262
msgid "Sublocations"
msgstr "Underplasseringer"

#: stock/serializers.py:1257
msgid "Parent stock location"
msgstr ""

#: stock/serializers.py:1374
msgid "Part must be salable"
msgstr "Delen må være salgbar"

#: stock/serializers.py:1378
msgid "Item is allocated to a sales order"
msgstr "Artikkelen er tildelt en salgsordre"

#: stock/serializers.py:1382
msgid "Item is allocated to a build order"
msgstr "Artikkelen er tildelt en produksjonsordre"

#: stock/serializers.py:1406
msgid "Customer to assign stock items"
msgstr "Kunde å tilordne lagervarer"

#: stock/serializers.py:1412
msgid "Selected company is not a customer"
msgstr "Valgt firma er ikke en kunde"

#: stock/serializers.py:1420
msgid "Stock assignment notes"
msgstr "Lagervare-tildelignsnotater"

#: stock/serializers.py:1430 stock/serializers.py:1695
msgid "A list of stock items must be provided"
msgstr "En liste av lagervarer må oppgis"

#: stock/serializers.py:1509
msgid "Stock merging notes"
msgstr "Notater om lagersammenslåing"

#: stock/serializers.py:1514
msgid "Allow mismatched suppliers"
msgstr "Tillat forskjellige leverandører"

#: stock/serializers.py:1515
msgid "Allow stock items with different supplier parts to be merged"
msgstr "Tillat lagervarer med forskjellige leverandørdeler å slås sammen"

#: stock/serializers.py:1520
msgid "Allow mismatched status"
msgstr "Tillat forskjellig status"

#: stock/serializers.py:1521
msgid "Allow stock items with different status codes to be merged"
msgstr "Tillat lagervarer med forskjellige statuskoder å slås sammen"

#: stock/serializers.py:1531
msgid "At least two stock items must be provided"
msgstr "Minst to lagervarer må oppgis"

#: stock/serializers.py:1598
msgid "No Change"
msgstr ""

#: stock/serializers.py:1627
msgid "StockItem primary key value"
msgstr "Lagervare primærnøkkel verdi"

#: stock/serializers.py:1639
msgid "Stock item is not in stock"
msgstr ""

#: stock/serializers.py:1685
msgid "Stock transaction notes"
msgstr "Lager transaksjonsnotater"

#: stock/serializers.py:1831
msgid "Next Serial Number"
msgstr ""

#: stock/serializers.py:1837
msgid "Previous Serial Number"
msgstr ""

#: stock/status_codes.py:11
msgid "OK"
msgstr ""

#: stock/status_codes.py:12
msgid "Attention needed"
msgstr "Trenger oppmerksomhet"

#: stock/status_codes.py:13
msgid "Damaged"
msgstr "Skadet"

#: stock/status_codes.py:14
msgid "Destroyed"
msgstr "Ødelagt"

#: stock/status_codes.py:15
msgid "Rejected"
msgstr "Avvist"

#: stock/status_codes.py:19
msgid "Quarantined"
msgstr "I Karantene"

#: stock/status_codes.py:44
msgid "Legacy stock tracking entry"
msgstr "Gammel lagervare sporingsoppføring"

#: stock/status_codes.py:46
msgid "Stock item created"
msgstr "Lagevare opprettet"

#: stock/status_codes.py:49
msgid "Edited stock item"
msgstr "Redigerte lagervare"

#: stock/status_codes.py:50
msgid "Assigned serial number"
msgstr "Tildelte serienummer"

#: stock/status_codes.py:53
msgid "Stock counted"
msgstr "Lager opptelt"

#: stock/status_codes.py:54
msgid "Stock manually added"
msgstr "Lagerbeholdning manuelt lagt til"

#: stock/status_codes.py:55
msgid "Stock manually removed"
msgstr "Lagerbeholdning manuelt fjernet"

#: stock/status_codes.py:58
msgid "Location changed"
msgstr "Posisjon endret"

#: stock/status_codes.py:59
msgid "Stock updated"
msgstr "Lagerbeholdning oppdatert"

#: stock/status_codes.py:62
msgid "Installed into assembly"
msgstr "Montert i sammenstilling"

#: stock/status_codes.py:63
msgid "Removed from assembly"
msgstr "Fjernet fra sammenstilling"

#: stock/status_codes.py:65
msgid "Installed component item"
msgstr "Montert komponentartikkel"

#: stock/status_codes.py:66
msgid "Removed component item"
msgstr "Fjernet komponentartikkel"

#: stock/status_codes.py:69
msgid "Split from parent item"
msgstr "Skill ut fra overordnet artikkel"

#: stock/status_codes.py:70
msgid "Split child item"
msgstr "Skill ut fra underartikkel"

#: stock/status_codes.py:73
msgid "Merged stock items"
msgstr "Sammenslåtte lagervarer"

#: stock/status_codes.py:76
msgid "Converted to variant"
msgstr "Konvertert til variant"

#: stock/status_codes.py:79
msgid "Build order output created"
msgstr "Produksjonsartikkel opprettet"

#: stock/status_codes.py:80
msgid "Build order output completed"
msgstr "Produksjonsartikkel fullført"

#: stock/status_codes.py:81
msgid "Build order output rejected"
msgstr "Produksjonsartikkel avvist"

#: stock/status_codes.py:82
msgid "Consumed by build order"
msgstr "Brukt av produksjonsordre"

#: stock/status_codes.py:85
msgid "Shipped against Sales Order"
msgstr "Sendt mot salgsordre"

#: stock/status_codes.py:88
msgid "Received against Purchase Order"
msgstr "Mottatt mot innkjøpsordre"

#: stock/status_codes.py:91
msgid "Returned against Return Order"
msgstr "Returnert mot returordre"

#: stock/status_codes.py:94
msgid "Sent to customer"
msgstr "Sendt til kunde"

#: stock/status_codes.py:95
msgid "Returned from customer"
msgstr "Returnert av kunde"

#: templates/403.html:6 templates/403.html:10 templates/403_csrf.html:7
msgid "Permission Denied"
msgstr "Tilgang nektet"

#: templates/403.html:11
msgid "You do not have permission to view this page."
msgstr "Du har ikke tillatelse til å se denne siden."

#: templates/403_csrf.html:11
msgid "Authentication Failure"
msgstr "Autentiseringsfeil"

#: templates/403_csrf.html:12
msgid "You have been logged out from InvenTree."
msgstr "Du har blitt logget ut av InvenTree."

#: templates/404.html:6 templates/404.html:10
msgid "Page Not Found"
msgstr "Side ikke funnet"

#: templates/404.html:11
msgid "The requested page does not exist"
msgstr "Forespurt side eksisterer ikke"

#: templates/500.html:6 templates/500.html:10
msgid "Internal Server Error"
msgstr "Intern serverfeil"

#: templates/500.html:11
#, python-format
msgid "The %(inventree_title)s server raised an internal error"
msgstr "%(inventree_title)s serveren reiste en intern feil"

#: templates/500.html:12
msgid "Refer to the error log in the admin interface for further details"
msgstr "Se feilloggen i admingrensesnittet for flere detaljer"

#: templates/503.html:11 templates/503.html:15
msgid "Site is in Maintenance"
msgstr "Nettstedet er i vedlikehold"

#: templates/503.html:17
msgid "The site is currently in maintenance and should be up again soon!"
msgstr "Siden er for øyeblikket i vedlikehold og bør være oppe igjen snart!"

#: templates/base.html:51
msgid "Server Restart Required"
msgstr "Omstart av server kreves"

#: templates/base.html:54
msgid "A configuration option has been changed which requires a server restart"
msgstr "En konfigurasjonsinnstilling har blitt endret som krever en omstart av serveren"

#: templates/base.html:54 templates/base.html:64
msgid "Contact your system administrator for further information"
msgstr "Kontakt systemadministratoren for mer informasjon"

#: templates/base.html:61
msgid "Pending Database Migrations"
msgstr "Ventende database-migrasjoner"

#: templates/base.html:64
msgid "There are pending database migrations which require attention"
msgstr "Det er ventende database-migrasjoner som krever oppmerksomhet"

#: templates/email/build_order_completed.html:9
#: templates/email/canceled_order_assigned.html:9
#: templates/email/new_order_assigned.html:9
#: templates/email/overdue_build_order.html:9
#: templates/email/overdue_purchase_order.html:9
#: templates/email/overdue_return_order.html:9
#: templates/email/overdue_sales_order.html:9
#: templates/email/purchase_order_received.html:9
#: templates/email/return_order_received.html:9
msgid "Click on the following link to view this order"
msgstr "Klikk på følgende lenke for å se denne ordren"

#: templates/email/build_order_required_stock.html:7
msgid "Stock is required for the following build order"
msgstr "Lagerbeholdning kreves for følgende produksjonsordre"

#: templates/email/build_order_required_stock.html:8
#, python-format
msgid "Build order %(build)s - building %(quantity)s x %(part)s"
msgstr "Produksjonsordre %(build)s - produserer %(quantity)s x %(part)s"

#: templates/email/build_order_required_stock.html:10
msgid "Click on the following link to view this build order"
msgstr "Klikk på følgende lenke for å se denne produksjonsordren"

#: templates/email/build_order_required_stock.html:14
msgid "The following parts are low on required stock"
msgstr "Følgende deler har for lav lagerbeholdning"

#: templates/email/build_order_required_stock.html:18
msgid "Required Quantity"
msgstr "Antall som kreves"

#: templates/email/build_order_required_stock.html:38
#: templates/email/low_stock_notification.html:30
msgid "You are receiving this email because you are subscribed to notifications for this part "
msgstr "Du mottar denne e-posten fordi du abonnerer på varsler for denne delen "

#: templates/email/low_stock_notification.html:9
#: templates/email/part_event_notification.html:9
msgid "Click on the following link to view this part"
msgstr "Klikk på følgende lenke for å se denne delen"

#: templates/email/low_stock_notification.html:18
#: templates/email/part_event_notification.html:19
msgid "Minimum Quantity"
msgstr "Minimum antall"

#: templates/email/part_event_notification.html:32
msgid "You are receiving this email because you are subscribed to notifications for this part or a category that it is part of "
msgstr ""

#: users/admin.py:101
msgid "Users"
msgstr "Brukere"

#: users/admin.py:102
msgid "Select which users are assigned to this group"
msgstr "Velg hvilke brukere som er tilordnet denne gruppen"

#: users/admin.py:137
msgid "Personal info"
msgstr "Personlig informasjon"

#: users/admin.py:139
msgid "Permissions"
msgstr "Tillatelser"

#: users/admin.py:142
msgid "Important dates"
msgstr "Viktige datoer"

#: users/authentication.py:30 users/models.py:157
msgid "Token has been revoked"
msgstr "Token er tilbakekalt"

#: users/authentication.py:33
msgid "Token has expired"
msgstr "Token har utløpt"

#: users/models.py:100
msgid "API Token"
msgstr "API-Token"

#: users/models.py:101
msgid "API Tokens"
msgstr "API-Tokener"

#: users/models.py:137
msgid "Token Name"
msgstr "Tokennavn"

#: users/models.py:138
msgid "Custom token name"
msgstr "Egendefinert tokennavn"

#: users/models.py:144
msgid "Token expiry date"
msgstr "Token utløpsdato"

#: users/models.py:152
msgid "Last Seen"
msgstr "Sist sett"

#: users/models.py:153
msgid "Last time the token was used"
msgstr "Sist gang tokenet ble brukt"

#: users/models.py:157
msgid "Revoked"
msgstr "Tilbakekalt"

#: users/models.py:235
msgid "Permission set"
msgstr "Tillatelse satt"

#: users/models.py:244
msgid "Group"
msgstr "Gruppe"

#: users/models.py:248
msgid "View"
msgstr "Visning"

#: users/models.py:248
msgid "Permission to view items"
msgstr "Tillatelse til å se elementer"

#: users/models.py:252
msgid "Add"
msgstr "Legg til"

#: users/models.py:252
msgid "Permission to add items"
msgstr "Tillatelse til å legge til elementer"

#: users/models.py:256
msgid "Change"
msgstr "Endre"

#: users/models.py:258
msgid "Permissions to edit items"
msgstr "Tillatelse til å endre elementer"

#: users/models.py:262
msgid "Delete"
msgstr "Slett"

#: users/models.py:264
msgid "Permission to delete items"
msgstr "Tillatelse til å slette elementer"

#: users/models.py:497
msgid "Bot"
msgstr ""

#: users/models.py:498
msgid "Internal"
msgstr ""

#: users/models.py:500
msgid "Guest"
msgstr ""

#: users/models.py:509
msgid "Language"
msgstr ""

#: users/models.py:510
msgid "Preferred language for the user"
msgstr ""

#: users/models.py:515
msgid "Theme"
msgstr ""

#: users/models.py:516
msgid "Settings for the web UI as JSON - do not edit manually!"
msgstr ""

#: users/models.py:521
msgid "Widgets"
msgstr ""

#: users/models.py:523
msgid "Settings for the dashboard widgets as JSON - do not edit manually!"
msgstr ""

#: users/models.py:530
msgid "Display Name"
msgstr ""

#: users/models.py:531
msgid "Chosen display name for the user"
msgstr ""

#: users/models.py:537
msgid "Position"
msgstr ""

#: users/models.py:538
msgid "Main job title or position"
msgstr ""

#: users/models.py:545
msgid "User status message"
msgstr ""

#: users/models.py:552
msgid "User location information"
msgstr ""

#: users/models.py:557
msgid "User is actively using the system"
msgstr ""

#: users/models.py:564
msgid "Preferred contact information for the user"
msgstr ""

#: users/models.py:570
msgid "User Type"
msgstr ""

#: users/models.py:571
msgid "Which type of user is this?"
msgstr ""

#: users/models.py:577
msgid "Organisation"
msgstr ""

#: users/models.py:578
msgid "Users primary organisation/affiliation"
msgstr ""

#: users/models.py:586
msgid "Primary Group"
msgstr ""

#: users/models.py:587
msgid "Primary group for the user"
msgstr ""

#: users/ruleset.py:31
msgid "Admin"
msgstr "Administrator"

#: users/ruleset.py:34
msgid "Stocktake"
msgstr "Lagertelling"

#: users/ruleset.py:38
msgid "Purchase Orders"
msgstr "Innkjøpsordrer"

#: users/ruleset.py:39
msgid "Sales Orders"
msgstr "Salgsordre"

#: users/ruleset.py:40
msgid "Return Orders"
msgstr "Returordrer"

#: users/serializers.py:236
msgid "Username"
msgstr "Brukernavn"

#: users/serializers.py:239
msgid "First Name"
msgstr "Fornavn"

#: users/serializers.py:239
msgid "First name of the user"
msgstr "Fornavn på brukeren"

#: users/serializers.py:243
msgid "Last Name"
msgstr "Etternavn"

#: users/serializers.py:243
msgid "Last name of the user"
msgstr "Etternavn på brukeren"

#: users/serializers.py:247
msgid "Email address of the user"
msgstr "E-postadressen til brukeren"

#: users/serializers.py:323
msgid "Staff"
msgstr "Personale"

#: users/serializers.py:324
msgid "Does this user have staff permissions"
msgstr "Har denne brukeren personelltillatelser"

#: users/serializers.py:329
msgid "Superuser"
msgstr "Superbruker"

#: users/serializers.py:329
msgid "Is this user a superuser"
msgstr "Er denne brukeren en superbruker"

#: users/serializers.py:333
msgid "Is this user account active"
msgstr "Er denne brukerkontoen aktiv"

#: users/serializers.py:345
msgid "Only a superuser can adjust this field"
msgstr ""

#: users/serializers.py:399
msgid "Only staff users can create new users"
msgstr ""

#: users/serializers.py:404
msgid "You do not have permission to create users"
msgstr ""

#: users/serializers.py:425
msgid "Your account has been created."
msgstr "Din konto er opprettet."

#: users/serializers.py:427
msgid "Please use the password reset function to login"
msgstr "Vennligst bruk funksjonen for å tilbakestille passord for å logge inn"

#: users/serializers.py:433
msgid "Welcome to InvenTree"
msgstr "Velkommen til InvenTree"

