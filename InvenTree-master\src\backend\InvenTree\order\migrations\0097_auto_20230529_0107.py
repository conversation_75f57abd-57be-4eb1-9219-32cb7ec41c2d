# Generated by Django 3.2.19 on 2023-05-29 01:07

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('company', '0065_remove_company_address'),
        ('order', '0096_alter_returnorderlineitem_outcome'),
    ]

    operations = [
        migrations.AddField(
            model_name='purchaseorder',
            name='address',
            field=models.ForeignKey(blank=True, help_text='Company address for this order', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to='company.address', verbose_name='Address'),
        ),
        migrations.AddField(
            model_name='returnorder',
            name='address',
            field=models.ForeignKey(blank=True, help_text='Company address for this order', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to='company.address', verbose_name='Address'),
        ),
        migrations.AddField(
            model_name='salesorder',
            name='address',
            field=models.ForeignKey(blank=True, help_text='Company address for this order', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to='company.address', verbose_name='Address'),
        ),
    ]
