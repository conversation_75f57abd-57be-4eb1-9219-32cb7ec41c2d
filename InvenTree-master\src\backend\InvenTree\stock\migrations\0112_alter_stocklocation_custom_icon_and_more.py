# Generated by Django 4.2.11 on 2024-07-20 22:30

import common.icons
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('stock', '0111_delete_stockitemattachment'),
    ]

    operations = [
        migrations.AlterField(
            model_name='stocklocation',
            name='custom_icon',
            field=models.CharField(blank=True, db_column='icon', help_text='Icon (optional)', max_length=100, validators=[common.icons.validate_icon], verbose_name='Icon'),
        ),
        migrations.AlterField(
            model_name='stocklocationtype',
            name='icon',
            field=models.CharField(blank=True, help_text='Default icon for all locations that have no icon set (optional)', max_length=100, validators=[common.icons.validate_icon], verbose_name='Icon'),
        ),
    ]
