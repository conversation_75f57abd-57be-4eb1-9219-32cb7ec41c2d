# Generated by Django 4.2.16 on 2024-10-31 02:52

from django.db import migrations
import django.db.models.deletion
import mptt.fields


class Migration(migrations.Migration):

    dependencies = [
        ('stock', '0113_stockitem_status_custom_key_and_more'),
        ('order', '0101_purchaseorder_status_custom_key_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='purchaseorder',
            name='destination',
            field=mptt.fields.TreeForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='purchase_orders', to='stock.stocklocation', verbose_name='Destination', help_text='Destination for received items'),
        ),
        migrations.AlterField(
            model_name='purchaseorderlineitem',
            name='destination',
            field=mptt.fields.TreeForeignKey(blank=True, help_text='Destination for received items', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='po_lines', to='stock.stocklocation', verbose_name='Destination'),
        ),
    ]
