# Generated by Django 4.2.20 on 2025-04-07 20:53

from django.db import migrations, models
import report.validators


class Migration(migrations.Migration):

    dependencies = [
        ("report", "0029_remove_reportoutput_template_and_more"),
    ]

    operations = [
        migrations.AlterField(
            model_name="labeltemplate",
            name="model_type",
            field=models.Char<PERSON>ield(
                help_text="Target model type for template",
                max_length=100,
                validators=[report.validators.validate_report_model_type],
                verbose_name="Model Type",
            ),
        ),
        migrations.AlterField(
            model_name="reporttemplate",
            name="model_type",
            field=models.CharField(
                help_text="Target model type for template",
                max_length=100,
                validators=[report.validators.validate_report_model_type],
                verbose_name="Model Type",
            ),
        ),
    ]
