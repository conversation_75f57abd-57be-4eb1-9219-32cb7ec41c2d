# Generated by Django 3.2.5 on 2021-11-11 23:37

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='PluginConfig',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('key', models.<PERSON><PERSON><PERSON><PERSON>(help_text='Key of plugin', max_length=255, unique=True, verbose_name='Key')),
                ('name', models.Char<PERSON>ield(blank=True, help_text='PluginName of the plugin', max_length=255, null=True, verbose_name='Name')),
                ('active', models.Boolean<PERSON>ield(default=False, help_text='Is the plugin active', verbose_name='Active')),
            ],
        ),
    ]
