{% extends "report/inventree_report_base.html" %}

{% load i18n %}
{% load report %}
{% load barcode %}
{% load inventree_extras %}
{% load markdownify %}

{% block page_margin %}
margin: 2cm;
margin-top: 4cm;
{% endblock page_margin %}

{% block style %}

.header-right {
    text-align: right;
    float: right;
}

.logo {
    height: 20mm;
    vertical-align: middle;
}

.part-image {
    border: 1px solid;
    border-radius: 2px;
    vertical-align: middle;
    height: 40mm;
    width: 100%;
    display: inline-block;
    z-index: 100;
}

.details-image {
    float: right;
    width: 30%;
}

.details {
    width: 100%;
    border: 1px solid;
    border-radius: 3px;
    padding: 5px;
    min-height: 42mm;
}

.details table {
    overflow-wrap: break-word;
    word-wrap: break-word;
    width: 65%;
    table-layout: fixed;
    font-size: 75%;
}

.details table td:not(:last-child){
    white-space: nowrap;
}

.details table td:last-child{
    width: 50%;
    padding-left: 1cm;
    padding-right: 1cm;
}

.details-table td {
    padding-left: 10px;
    padding-top: 5px;
    padding-bottom: 5px;
    border-bottom: 1px solid #555;
}

{% endblock style %}

{% block bottom_left %}
content: "v{{ report_revision }} - {% format_date date %}";
{% endblock bottom_left %}

{% block header_content %}
    <img class='logo' src="{% logo_image %}" alt="logo" width="150">

    <div class='header-right'>
        <h3>
            Build Order {{ build }}
        </h3>
        <small>{{ quantity }} x {{ part.full_name }}</small>
        <br>
    </div>

    <hr>
{% endblock header_content %}

{% block page_content %}

<div class='details'>
    <div class='details-image'>
        <img class='part-image' alt="{% trans 'Part image' %}" src="{% part_image part height=480 %}">
    </div>

    <div class='details-container'>

        <table class='details-table'>
            <tr>
                <th>{% trans "Build Order" %}</th>
                <td>{% internal_link build.get_absolute_url build %}</td>
            </tr>
            <tr>
                <th>{% trans "Part" %}</th>
                <td>{% internal_link part.get_absolute_url part.full_name %}</td>
            </tr>
            <tr>
                <th>{% trans "Quantity" %}</th>
                <td>{{ build.quantity }}</td>
            </tr>
            <tr>
                <th>{% trans "Description" %}</th>
                <td>{{ build.title }}</td>
            </tr>
            <tr>
                <th>{% trans "Issued" %}</th>
                <td>{% format_date build.creation_date %}</td>
            </tr>
            <tr>
                <th>{% trans "Target Date" %}</th>
                <td>
                    {% if build.target_date %}
                    {% format_date build.target_date %}
                    {% else %}
                    <em>Not specified</em>
                    {% endif %}
                </td>
            </tr>
            <tr>
                <th>{% trans "Sales Order" %}</th>
                <td>
                    {% if build.sales_order %}
                    {% internal_link build.sales_order.get_absolute_url build.sales_order %}
                    {% else %}
                    <em>Not specified</em>
                    {% endif %}
                </td>
            </tr>
            {% if build.parent %}
            <tr>
                <th>{% trans "Required For" %}</th>
                <td>{% internal_link build.parent.get_absolute_url build.parent %}</td>
            </tr>
            {% endif %}
            {% if build.issued_by %}
            <tr>
                <th>{% trans "Issued By" %}</th>
                <td>{{ build.issued_by }}</td>
            </tr>
            {% endif %}
            {% if build.responsible %}
            <tr>
                <th>{% trans "Responsible" %}</th>
                <td>{{ build.responsible }}</td>
            </tr>
            {% endif %}
            {% if build.link %}
            <tr>
                <th>{% trans "Link" %}</th>
                <td><a href="{{ build.link }}">{{ build.link }}</a></td>
            </tr>
            {% endif %}
        </table>
    </div>
</div>

<h3>{% trans "Notes" %}</h3>

{% if build.notes %}
{{ build.notes|markdownify }}
{% endif %}

{% endblock page_content %}
