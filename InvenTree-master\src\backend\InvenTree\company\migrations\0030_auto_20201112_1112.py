# Generated by Django 3.0.7 on 2020-11-12 00:12

import InvenTree.fields
import django.core.validators
from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('company', '0029_company_currency'),
    ]

    operations = [
        migrations.AlterField(
            model_name='supplierpricebreak',
            name='quantity',
            field=InvenTree.fields.RoundingDecimalField(decimal_places=5, default=1, help_text='Price break quantity', max_digits=15, validators=[django.core.validators.MinValueValidator(1)], verbose_name='Quantity'),
        ),
    ]
