# Generated by Django 3.0.7 on 2021-04-04 20:16

import InvenTree.fields
import InvenTree.models
from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('company', '0032_auto_20210403_1837'),
        ('part', '0063_bomitem_inherited'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('stock', '0058_stockitem_packaging'),
        ('order', '0043_auto_20210330_0013'),
    ]

    operations = [
        migrations.AlterField(
            model_name='purchaseorder',
            name='created_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL, verbose_name='Created By'),
        ),
        migrations.AlterField(
            model_name='purchaseorder',
            name='creation_date',
            field=models.DateField(blank=True, null=True, verbose_name='Creation Date'),
        ),
        migrations.AlterField(
            model_name='purchaseorder',
            name='description',
            field=models.CharField(help_text='Order description', max_length=250, verbose_name='Description'),
        ),
        migrations.AlterField(
            model_name='purchaseorder',
            name='link',
            field=models.URLField(blank=True, help_text='Link to external page', verbose_name='Link'),
        ),
        migrations.AlterField(
            model_name='purchaseorder',
            name='notes',
            field=models.TextField(blank=True, help_text='Order notes', verbose_name='Notes'),
        ),
        migrations.AlterField(
            model_name='purchaseorder',
            name='received_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL, verbose_name='received by'),
        ),
        migrations.AlterField(
            model_name='purchaseorder',
            name='reference',
            field=models.CharField(help_text='Order reference', max_length=64, unique=True, verbose_name='Reference'),
        ),
        migrations.AlterField(
            model_name='purchaseorder',
            name='supplier',
            field=models.ForeignKey(help_text='Company from which the items are being ordered', limit_choices_to={'is_supplier': True}, on_delete=django.db.models.deletion.CASCADE, related_name='purchase_orders', to='company.Company', verbose_name='Supplier'),
        ),
        migrations.AlterField(
            model_name='purchaseorder',
            name='supplier_reference',
            field=models.CharField(blank=True, help_text='Supplier order reference code', max_length=64, verbose_name='Supplier Reference'),
        ),
        migrations.AlterField(
            model_name='purchaseorderattachment',
            name='attachment',
            field=models.FileField(help_text='Select file to attach', upload_to='attachments', verbose_name='Attachment'),
        ),
        migrations.AlterField(
            model_name='purchaseorderattachment',
            name='comment',
            field=models.CharField(blank=True, help_text='File comment', max_length=100, verbose_name='Comment'),
        ),
        migrations.AlterField(
            model_name='purchaseorderattachment',
            name='upload_date',
            field=models.DateField(auto_now_add=True, null=True, verbose_name='upload date'),
        ),
        migrations.AlterField(
            model_name='purchaseorderattachment',
            name='user',
            field=models.ForeignKey(blank=True, help_text='User', null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='User'),
        ),
        migrations.AlterField(
            model_name='purchaseorderlineitem',
            name='notes',
            field=models.CharField(blank=True, help_text='Line item notes', max_length=500, verbose_name='Notes'),
        ),
        migrations.AlterField(
            model_name='purchaseorderlineitem',
            name='order',
            field=models.ForeignKey(help_text='Purchase Order', on_delete=django.db.models.deletion.CASCADE, related_name='lines', to='order.PurchaseOrder', verbose_name='Order'),
        ),
        migrations.AlterField(
            model_name='purchaseorderlineitem',
            name='part',
            field=models.ForeignKey(blank=True, help_text='Supplier part', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='purchase_order_line_items', to='company.SupplierPart', verbose_name='Part'),
        ),
        migrations.AlterField(
            model_name='purchaseorderlineitem',
            name='quantity',
            field=InvenTree.fields.RoundingDecimalField(decimal_places=5, default=1, help_text='Item quantity', max_digits=15, validators=[django.core.validators.MinValueValidator(0)], verbose_name='Quantity'),
        ),
        migrations.AlterField(
            model_name='purchaseorderlineitem',
            name='received',
            field=models.DecimalField(decimal_places=5, default=0, help_text='Number of items received', max_digits=15, verbose_name='Received'),
        ),
        migrations.AlterField(
            model_name='purchaseorderlineitem',
            name='reference',
            field=models.CharField(blank=True, help_text='Line item reference', max_length=100, verbose_name='Reference'),
        ),
        migrations.AlterField(
            model_name='salesorder',
            name='created_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL, verbose_name='Created By'),
        ),
        migrations.AlterField(
            model_name='salesorder',
            name='creation_date',
            field=models.DateField(blank=True, null=True, verbose_name='Creation Date'),
        ),
        migrations.AlterField(
            model_name='salesorder',
            name='customer',
            field=models.ForeignKey(help_text='Company to which the items are being sold', limit_choices_to={'is_customer': True}, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='sales_orders', to='company.Company', verbose_name='Customer'),
        ),
        migrations.AlterField(
            model_name='salesorder',
            name='customer_reference',
            field=models.CharField(blank=True, help_text='Customer order reference code', max_length=64, verbose_name='Customer Reference '),
        ),
        migrations.AlterField(
            model_name='salesorder',
            name='description',
            field=models.CharField(help_text='Order description', max_length=250, verbose_name='Description'),
        ),
        migrations.AlterField(
            model_name='salesorder',
            name='link',
            field=models.URLField(blank=True, help_text='Link to external page', verbose_name='Link'),
        ),
        migrations.AlterField(
            model_name='salesorder',
            name='notes',
            field=models.TextField(blank=True, help_text='Order notes', verbose_name='Notes'),
        ),
        migrations.AlterField(
            model_name='salesorder',
            name='reference',
            field=models.CharField(help_text='Order reference', max_length=64, unique=True, verbose_name='Reference'),
        ),
        migrations.AlterField(
            model_name='salesorder',
            name='shipment_date',
            field=models.DateField(blank=True, null=True, verbose_name='Shipment Date'),
        ),
        migrations.AlterField(
            model_name='salesorder',
            name='shipped_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL, verbose_name='shipped by'),
        ),
        migrations.AlterField(
            model_name='salesorder',
            name='status',
            field=models.PositiveIntegerField(choices=[(10, 'Pending'), (20, 'Shipped'), (40, 'Cancelled'), (50, 'Lost'), (60, 'Returned')], default=10, help_text='Purchase order status', verbose_name='Status'),
        ),
        migrations.AlterField(
            model_name='salesorderallocation',
            name='item',
            field=models.ForeignKey(help_text='Select stock item to allocate', limit_choices_to={'belongs_to': None, 'part__salable': True, 'sales_order': None}, on_delete=django.db.models.deletion.CASCADE, related_name='sales_order_allocations', to='stock.StockItem', verbose_name='Item'),
        ),
        migrations.AlterField(
            model_name='salesorderallocation',
            name='line',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='allocations', to='order.SalesOrderLineItem', verbose_name='Line'),
        ),
        migrations.AlterField(
            model_name='salesorderallocation',
            name='quantity',
            field=InvenTree.fields.RoundingDecimalField(decimal_places=5, default=1, help_text='Enter stock allocation quantity', max_digits=15, validators=[django.core.validators.MinValueValidator(0)], verbose_name='Quantity'),
        ),
        migrations.AlterField(
            model_name='salesorderattachment',
            name='attachment',
            field=models.FileField(help_text='Select file to attach', upload_to='attachments', verbose_name='Attachment'),
        ),
        migrations.AlterField(
            model_name='salesorderattachment',
            name='comment',
            field=models.CharField(blank=True, help_text='File comment', max_length=100, verbose_name='Comment'),
        ),
        migrations.AlterField(
            model_name='salesorderattachment',
            name='upload_date',
            field=models.DateField(auto_now_add=True, null=True, verbose_name='upload date'),
        ),
        migrations.AlterField(
            model_name='salesorderattachment',
            name='user',
            field=models.ForeignKey(blank=True, help_text='User', null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='User'),
        ),
        migrations.AlterField(
            model_name='salesorderlineitem',
            name='notes',
            field=models.CharField(blank=True, help_text='Line item notes', max_length=500, verbose_name='Notes'),
        ),
        migrations.AlterField(
            model_name='salesorderlineitem',
            name='order',
            field=models.ForeignKey(help_text='Sales Order', on_delete=django.db.models.deletion.CASCADE, related_name='lines', to='order.SalesOrder', verbose_name='Order'),
        ),
        migrations.AlterField(
            model_name='salesorderlineitem',
            name='part',
            field=models.ForeignKey(help_text='Part', limit_choices_to={'salable': True}, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='sales_order_line_items', to='part.Part', verbose_name='Part'),
        ),
        migrations.AlterField(
            model_name='salesorderlineitem',
            name='quantity',
            field=InvenTree.fields.RoundingDecimalField(decimal_places=5, default=1, help_text='Item quantity', max_digits=15, validators=[django.core.validators.MinValueValidator(0)], verbose_name='Quantity'),
        ),
        migrations.AlterField(
            model_name='salesorderlineitem',
            name='reference',
            field=models.CharField(blank=True, help_text='Line item reference', max_length=100, verbose_name='Reference'),
        ),
    ]
