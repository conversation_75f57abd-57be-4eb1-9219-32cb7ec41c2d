# Generated by Django 3.0.7 on 2021-02-15 21:08

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('report', '0011_auto_20210212_2024'),
    ]

    operations = [
        migrations.CreateModel(
            name='BuildReport',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.Char<PERSON>ield(help_text='Template name', max_length=100, verbose_name='Name')),
                ('template', models.FileField(help_text='Report template file', upload_to='report', validators=[django.core.validators.FileExtensionValidator(allowed_extensions=['html', 'htm'])], verbose_name='Template')),
                ('description', models.CharField(help_text='Report template description', max_length=250, verbose_name='Description')),
                ('revision', models.PositiveIntegerField(default=1, editable=False, help_text='Report revision number (auto-increments)', verbose_name='Revision')),
                ('enabled', models.BooleanField(default=True, help_text='Report template is enabled', verbose_name='Enabled')),
                ('filters', models.CharField(blank=True, help_text='Build query filters (comma-separated list of key=value pairs', max_length=250, verbose_name='Build Filters')),
            ],
            options={
                'abstract': False,
            },
        ),
    ]
