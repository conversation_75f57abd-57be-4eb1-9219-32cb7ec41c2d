"""
Pesapal API Configuration
Based on the existing Pesapal API files in src/PesapalAPI-Files/
"""
import os
from django.conf import settings
from decouple import config


class PesapalConfig:
    """Pesapal API configuration management"""
    
    # Environment settings
    SANDBOX_MODE = config('PESAPAL_SANDBOX_MODE', default=True, cast=bool)
    
    # API Endpoints
    SANDBOX_BASE_URL = 'http://demo.pesapal.com'
    LIVE_BASE_URL = 'https://www.pesapal.com'
    
    # API Endpoints
    POST_ORDER_ENDPOINT = '/api/PostPesapalDirectOrderV4'
    QUERY_PAYMENT_STATUS_ENDPOINT = '/api/querypaymentstatus'
    
    # Credentials
    CONSUMER_KEY = config('PESAPAL_CONSUMER_KEY', default='')
    CONSUMER_SECRET = config('PESAPAL_CONSUMER_SECRET', default='')
    
    # Callback URLs
    CALLBACK_URL = config('PESAPAL_CALLBACK_URL', default='http://localhost:8000/api/payments/callback/')
    IPN_URL = config('PESAPAL_IPN_URL', default='http://localhost:8000/api/payments/ipn/')
    
    @classmethod
    def get_base_url(cls):
        """Get the appropriate base URL based on environment"""
        return cls.SANDBOX_BASE_URL if cls.SANDBOX_MODE else cls.LIVE_BASE_URL
    
    @classmethod
    def get_post_order_url(cls):
        """Get the complete URL for posting orders"""
        return cls.get_base_url() + cls.POST_ORDER_ENDPOINT
    
    @classmethod
    def get_query_status_url(cls):
        """Get the complete URL for querying payment status"""
        return cls.get_base_url() + cls.QUERY_PAYMENT_STATUS_ENDPOINT
    
    @classmethod
    def validate_config(cls):
        """Validate that all required configuration is present"""
        if not cls.CONSUMER_KEY:
            raise ValueError("PESAPAL_CONSUMER_KEY is required")
        if not cls.CONSUMER_SECRET:
            raise ValueError("PESAPAL_CONSUMER_SECRET is required")
        return True


# Payment method choices
PAYMENT_METHOD_CHOICES = [
    ('mpesa', 'M-Pesa'),
    ('visa', 'Visa Card'),
    ('mastercard', 'Mastercard'),
    ('airtel', 'Airtel Money'),
    ('equitel', 'Equitel'),
    ('bank', 'Bank Transfer'),
]

# Payment status choices
PAYMENT_STATUS_CHOICES = [
    ('pending', 'Pending'),
    ('completed', 'Completed'),
    ('failed', 'Failed'),
    ('cancelled', 'Cancelled'),
    ('invalid', 'Invalid'),
]

# Order status choices
ORDER_STATUS_CHOICES = [
    ('pending', 'Pending Payment'),
    ('paid', 'Paid'),
    ('processing', 'Processing'),
    ('shipped', 'Shipped'),
    ('delivered', 'Delivered'),
    ('cancelled', 'Cancelled'),
    ('refunded', 'Refunded'),
]
