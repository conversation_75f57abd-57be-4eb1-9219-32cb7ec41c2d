# Generated by Django 4.2.19 on 2025-02-21 14:57

import InvenTree.fields
from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ("order", "0107_auto_20250221_1241"),
    ]

    operations = [
        migrations.AlterField(
            model_name="purchaseorder",
            name="link",
            field=InvenTree.fields.InvenTreeURLField(
                blank=True,
                help_text="Link to external page",
                max_length=2000,
                verbose_name="Link",
            ),
        ),
        migrations.AlterField(
            model_name="purchaseorderlineitem",
            name="link",
            field=InvenTree.fields.InvenTreeURLField(
                blank=True,
                help_text="Link to external page",
                max_length=2000,
                verbose_name="Link",
            ),
        ),
        migrations.AlterField(
            model_name="returnorder",
            name="link",
            field=InvenTree.fields.InvenTreeURLField(
                blank=True,
                help_text="Link to external page",
                max_length=2000,
                verbose_name="Link",
            ),
        ),
        migrations.AlterField(
            model_name="returnorderlineitem",
            name="link",
            field=InvenTree.fields.InvenTreeURLField(
                blank=True,
                help_text="Link to external page",
                max_length=2000,
                verbose_name="Link",
            ),
        ),
        migrations.AlterField(
            model_name="salesorder",
            name="link",
            field=InvenTree.fields.InvenTreeURLField(
                blank=True,
                help_text="Link to external page",
                max_length=2000,
                verbose_name="Link",
            ),
        ),
        migrations.AlterField(
            model_name="salesorderlineitem",
            name="link",
            field=InvenTree.fields.InvenTreeURLField(
                blank=True,
                help_text="Link to external page",
                max_length=2000,
                verbose_name="Link",
            ),
        ),
        migrations.AlterField(
            model_name="salesordershipment",
            name="link",
            field=InvenTree.fields.InvenTreeURLField(
                blank=True,
                help_text="Link to external page",
                max_length=2000,
                verbose_name="Link",
            ),
        ),
        migrations.AlterField(
            model_name="purchaseorderextraline",
            name="link",
            field=InvenTree.fields.InvenTreeURLField(
                blank=True,
                help_text="Link to external page",
                max_length=2000,
                verbose_name="Link",
            ),
        ),
        migrations.AlterField(
            model_name="returnorderextraline",
            name="link",
            field=InvenTree.fields.InvenTreeURLField(
                blank=True,
                help_text="Link to external page",
                max_length=2000,
                verbose_name="Link",
            ),
        ),
        migrations.AlterField(
            model_name="salesorderextraline",
            name="link",
            field=InvenTree.fields.InvenTreeURLField(
                blank=True,
                help_text="Link to external page",
                max_length=2000,
                verbose_name="Link",
            ),
        ),
    ]
