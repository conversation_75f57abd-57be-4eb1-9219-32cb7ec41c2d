# Generated by Django 3.0.7 on 2020-08-31 09:12

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('company', '0023_auto_20200808_0715'),
        ('order', '0035_auto_20200513_0016'),
    ]

    operations = [
        migrations.AlterField(
            model_name='purchaseorder',
            name='complete_date',
            field=models.DateField(blank=True, help_text='Date order was completed', null=True),
        ),
        migrations.AlterField(
            model_name='purchaseorder',
            name='issue_date',
            field=models.DateField(blank=True, help_text='Date order was issued', null=True),
        ),
        migrations.AlterField(
            model_name='purchaseorder',
            name='supplier',
            field=models.ForeignKey(help_text='Company from which the items are being ordered', limit_choices_to={'is_supplier': True}, on_delete=django.db.models.deletion.CASCADE, related_name='purchase_orders', to='company.Company'),
        ),
        migrations.Alter<PERSON>ield(
            model_name='salesorder',
            name='customer',
            field=models.ForeignKey(help_text='Company to which the items are being sold', limit_choices_to={'is_customer': True}, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='sales_orders', to='company.Company'),
        ),
    ]
