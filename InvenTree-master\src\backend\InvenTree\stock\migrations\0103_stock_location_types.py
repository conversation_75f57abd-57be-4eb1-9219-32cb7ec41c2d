# Generated by Django 3.2.20 on 2023-09-21 11:50

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('stock', '0102_alter_stockitem_status'),
    ]

    operations = [
        migrations.CreateModel(
            name='StockLocationType',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('metadata', models.JSONField(blank=True, help_text='JSON metadata field, for use by external plugins', null=True, verbose_name='Plugin Metadata')),
                ('name', models.CharField(help_text='Name', max_length=100, verbose_name='Name')),
                ('description', models.CharField(blank=True, help_text='Description (optional)', max_length=250, verbose_name='Description')),
                ('icon', models.Char<PERSON>ield(blank=True, help_text='Default icon for all locations that have no icon set (optional)', max_length=100, verbose_name='Icon')),
            ],
            options={
                'verbose_name': 'Stock Location type',
                'verbose_name_plural': 'Stock Location types',
            },
        ),
        migrations.RenameField(
            model_name='stocklocation',
            old_name='icon',
            new_name='custom_icon',
        ),
        migrations.AlterField(
            model_name='stocklocation',
            name='custom_icon',
            field=models.CharField(blank=True, db_column='icon', help_text='Icon (optional)', max_length=100, verbose_name='Icon'),
        ),
        migrations.AddField(
            model_name='stocklocation',
            name='location_type',
            field=models.ForeignKey(blank=True, help_text='Stock location type of this location', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='stock_locations', to='stock.stocklocationtype', verbose_name='Location type'),
        ),
    ]
