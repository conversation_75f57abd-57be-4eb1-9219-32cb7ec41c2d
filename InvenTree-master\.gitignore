# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# Distribution / packaging
.Python
env/
inventree-env/
.venv/
./build/
.cache/
develop-eggs/
dist/
bin/
lib64
pyvenv.cfg
share/
downloads/
eggs/
.eggs/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg
*.DS_Store

# Django stuff:
*.log
local_settings.py
*.sqlite
*.sqlite3
*.sqlite3-journal
*.backup
*.old

# Files used for testing
inventree-demo-dataset/
inventree-data/

# Local static and media file storage (only when running in development mode)
inventree_media
inventree_static
static_i18n

# Local config file
config.yaml
plugins.txt

# Default data file
data.json
*.json.tmp
*.tmp.json

# Key file
secret_key.txt
oidc.pem

# IDE / development files
.idea/
*.code-workspace
.bash_history
.DS_Store

# https://github.com/github/gitignore/blob/main/Global/VisualStudioCode.gitignore
.vscode/*
#!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
#!.vscode/extensions.json
#!.vscode/*.code-snippets

# Coverage reports
.coverage
htmlcov/

# Temporary javascript files (used for testing)
js_tmp/

# Development files
dev/
dev-db/
data/
env/

# Locale stats file
src/backend/InvenTree/InvenTree/locale_stats.json
src/backend/InvenTree/InvenTree/licenses.txt

# Logs
src/backend/InvenTree/logs.json
src/backend/InvenTree/logs.log

# node.js
node_modules/

# maintenance locker
maintenance_mode_state.txt

# plugin dev directory
src/backend/InvenTree/plugins/

# Compiled translation files
*.mo
messages.ts

# Generated API schema file
api.yaml

# web frontend (static files)
src/backend/InvenTree/web/static
InvenTree/web/static
