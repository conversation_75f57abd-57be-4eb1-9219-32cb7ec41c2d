msgid ""
msgstr ""
"Project-Id-Version: inventree\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-09 19:12+0000\n"
"PO-Revision-Date: 2025-05-09 19:15\n"
"Last-Translator: \n"
"Language-Team: Vietnamese\n"
"Language: vi_VN\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"X-Crowdin-Project: inventree\n"
"X-Crowdin-Project-ID: 452300\n"
"X-Crowdin-Language: vi\n"
"X-Crowdin-File: /src/backend/InvenTree/locale/en/LC_MESSAGES/django.po\n"
"X-Crowdin-File-ID: 250\n"

#: InvenTree/AllUserRequire2FAMiddleware.py:42
msgid "You must enable two-factor authentication before doing anything else."
msgstr ""

#: InvenTree/api.py:355
msgid "API endpoint not found"
msgstr "API endpoint không tồn tại"

#: InvenTree/api.py:432
msgid "List of items or filters must be provided for bulk operation"
msgstr ""

#: InvenTree/api.py:439
msgid "Items must be provided as a list"
msgstr ""

#: InvenTree/api.py:447
msgid "Invalid items list provided"
msgstr ""

#: InvenTree/api.py:453
msgid "Filters must be provided as a dict"
msgstr ""

#: InvenTree/api.py:460
msgid "Invalid filters provided"
msgstr ""

#: InvenTree/api.py:465
msgid "All filter must only be used with true"
msgstr ""

#: InvenTree/api.py:470
msgid "No items match the provided criteria"
msgstr ""

#: InvenTree/api.py:713
msgid "User does not have permission to view this model"
msgstr "Người dùng không được phân quyền xem mẫu này"

#: InvenTree/auth_overrides.py:57
msgid "Email (again)"
msgstr "Email (nhắc lại)"

#: InvenTree/auth_overrides.py:61
msgid "Email address confirmation"
msgstr "Xác nhận địa chỉ email"

#: InvenTree/auth_overrides.py:84
msgid "You must type the same email each time."
msgstr "Bạn phải nhập cùng một email mỗi lần."

#: InvenTree/auth_overrides.py:126 InvenTree/auth_overrides.py:134
msgid "The provided primary email address is not valid."
msgstr "Địa chỉ email chính đã cung cấp không hợp lệ."

#: InvenTree/auth_overrides.py:140
msgid "The provided email domain is not approved."
msgstr "Miền email được cung cấp không được phê duyệt."

#: InvenTree/conversion.py:162
#, python-brace-format
msgid "Invalid unit provided ({unit})"
msgstr "Đơn vị không hợp lệ ({unit})"

#: InvenTree/conversion.py:179
msgid "No value provided"
msgstr "Chưa cung cấp giá trị"

#: InvenTree/conversion.py:206
#, python-brace-format
msgid "Could not convert {original} to {unit}"
msgstr "Không thể chuyển đổi {original} sang {unit}"

#: InvenTree/conversion.py:208 InvenTree/conversion.py:222
#: InvenTree/helpers.py:548 order/models.py:707 order/models.py:975
msgid "Invalid quantity provided"
msgstr "Số lượng cung cấp không hợp lệ"

#: InvenTree/exceptions.py:109
msgid "Error details can be found in the admin panel"
msgstr "Chi tiết lỗi có thể được tìm thấy trong bảng quản trị"

#: InvenTree/fields.py:138
msgid "Enter date"
msgstr "Nhập ngày"

#: InvenTree/fields.py:161
msgid "Invalid decimal value"
msgstr ""

#: InvenTree/fields.py:210 InvenTree/models.py:887 build/serializers.py:526
#: build/serializers.py:597 company/models.py:834 order/models.py:1579
#: part/models.py:3366
#: report/templates/report/inventree_build_order_report.html:172
#: stock/models.py:2708 stock/models.py:2832 stock/serializers.py:756
#: stock/serializers.py:918 stock/serializers.py:1048 stock/serializers.py:1100
#: stock/serializers.py:1419 stock/serializers.py:1508
#: stock/serializers.py:1684
msgid "Notes"
msgstr "Ghi chú"

#: InvenTree/format.py:166
#, python-brace-format
msgid "Value '{name}' does not appear in pattern format"
msgstr "Giá trị '{name}' không xuất hiện ở định dạng mẫu"

#: InvenTree/format.py:177
msgid "Provided value does not match required pattern: "
msgstr "Giá trị được cung cấp không khớp với mẫu bắt buộc: "

#: InvenTree/helpers.py:552
msgid "Cannot serialize more than 1000 items at once"
msgstr ""

#: InvenTree/helpers.py:558
msgid "Empty serial number string"
msgstr "Chuỗi số sê-ri trống"

#: InvenTree/helpers.py:587
msgid "Duplicate serial"
msgstr "Trùng lặp sê-ri"

#: InvenTree/helpers.py:619 InvenTree/helpers.py:662 InvenTree/helpers.py:680
#: InvenTree/helpers.py:687 InvenTree/helpers.py:706
#, python-brace-format
msgid "Invalid group: {group}"
msgstr ""

#: InvenTree/helpers.py:650
#, python-brace-format
msgid "Group range {group} exceeds allowed quantity ({expected_quantity})"
msgstr "Khoảng nhóm {group} vượt cho phép số lượng ({expected_quantity})"

#: InvenTree/helpers.py:716
msgid "No serial numbers found"
msgstr "Không tìm thấy số sê-ri"

#: InvenTree/helpers.py:721
msgid "Number of unique serial numbers ({len(serials)}) must match quantity ({expected_quantity})"
msgstr "Số sê ri duy nhất ({len(serials)}) phải phù hợp số lượng ({expected_quantity})"

#: InvenTree/helpers.py:840
msgid "Remove HTML tags from this value"
msgstr "Xóa thẻ HTML từ giá trị này"

#: InvenTree/helpers.py:919
msgid "Data contains prohibited markdown content"
msgstr ""

#: InvenTree/helpers_model.py:131
msgid "Connection error"
msgstr "Lỗi kết nối"

#: InvenTree/helpers_model.py:136 InvenTree/helpers_model.py:143
msgid "Server responded with invalid status code"
msgstr "Máy chủ phản hồi với mã trạng thái không hợp lệ"

#: InvenTree/helpers_model.py:139
msgid "Exception occurred"
msgstr "Xảy ra Exception"

#: InvenTree/helpers_model.py:149
msgid "Server responded with invalid Content-Length value"
msgstr "Máy chủ đã phản hồi với giá trị Content-Length không hợp lệ"

#: InvenTree/helpers_model.py:152
msgid "Image size is too large"
msgstr "Hình ảnh quá lớn"

#: InvenTree/helpers_model.py:164
msgid "Image download exceeded maximum size"
msgstr "Tải xuống hình ảnh vượt quá kích thước tối đa"

#: InvenTree/helpers_model.py:169
msgid "Remote server returned empty response"
msgstr "Máy chủ trả về phản hồi trống"

#: InvenTree/helpers_model.py:177
msgid "Supplied URL is not a valid image file"
msgstr "URL được cung cấp không phải là tệp hình ảnh hợp lệ"

#: InvenTree/locales.py:20
msgid "Arabic"
msgstr "Arabic"

#: InvenTree/locales.py:21
msgid "Bulgarian"
msgstr "Tiếng Bulgaria"

#: InvenTree/locales.py:22
msgid "Czech"
msgstr "Tiếng Séc"

#: InvenTree/locales.py:23
msgid "Danish"
msgstr "Tiếng Đan Mạch"

#: InvenTree/locales.py:24
msgid "German"
msgstr "Tiếng Đức"

#: InvenTree/locales.py:25
msgid "Greek"
msgstr "Tiếng Hy Lạp"

#: InvenTree/locales.py:26
msgid "English"
msgstr "Tiếng Anh"

#: InvenTree/locales.py:27
msgid "Spanish"
msgstr "Tiếng Tây Ban Nha"

#: InvenTree/locales.py:28
msgid "Spanish (Mexican)"
msgstr "Tiếng Tây Ban Nha (Mê-hi-cô)"

#: InvenTree/locales.py:29
msgid "Estonian"
msgstr "Estonian"

#: InvenTree/locales.py:30
msgid "Farsi / Persian"
msgstr "Tiếng Ba Tư"

#: InvenTree/locales.py:31
msgid "Finnish"
msgstr "Tiếng Phần Lan"

#: InvenTree/locales.py:32
msgid "French"
msgstr "Tiếng Pháp"

#: InvenTree/locales.py:33
msgid "Hebrew"
msgstr "Tiếng Do Thái"

#: InvenTree/locales.py:34
msgid "Hindi"
msgstr "Tiếng Ấn Độ"

#: InvenTree/locales.py:35
msgid "Hungarian"
msgstr "Tiếng Hung-ga-ri"

#: InvenTree/locales.py:36
msgid "Italian"
msgstr "Tiếng Ý"

#: InvenTree/locales.py:37
msgid "Japanese"
msgstr "Tiếng Nhật"

#: InvenTree/locales.py:38
msgid "Korean"
msgstr "Tiếng Hàn"

#: InvenTree/locales.py:39
msgid "Lithuanian"
msgstr ""

#: InvenTree/locales.py:40
msgid "Latvian"
msgstr "Latvian"

#: InvenTree/locales.py:41
msgid "Dutch"
msgstr "Tiếng Hà Lan"

#: InvenTree/locales.py:42
msgid "Norwegian"
msgstr "Tiếng Na Uy"

#: InvenTree/locales.py:43
msgid "Polish"
msgstr "Tiếng Ba Lan"

#: InvenTree/locales.py:44
msgid "Portuguese"
msgstr "Tiếng Bồ Đào Nha"

#: InvenTree/locales.py:45
msgid "Portuguese (Brazilian)"
msgstr "Tiếng Bồ Đào Nha (Brazil)"

#: InvenTree/locales.py:46
msgid "Romanian"
msgstr "Romanian"

#: InvenTree/locales.py:47
msgid "Russian"
msgstr "Tiếng Nga"

#: InvenTree/locales.py:48
msgid "Slovak"
msgstr "Tiếng Slo-va-ki-a"

#: InvenTree/locales.py:49
msgid "Slovenian"
msgstr "Tiếng Slô-ven-ni-a"

#: InvenTree/locales.py:50
msgid "Serbian"
msgstr "Tiếng Serbia"

#: InvenTree/locales.py:51
msgid "Swedish"
msgstr "Tiếng Thụy Điển"

#: InvenTree/locales.py:52
msgid "Thai"
msgstr "Tiếng Thái"

#: InvenTree/locales.py:53
msgid "Turkish"
msgstr "Tiếng Thổ Nhĩ Kỳ"

#: InvenTree/locales.py:54
msgid "Ukrainian"
msgstr "Ukrainian"

#: InvenTree/locales.py:55
msgid "Vietnamese"
msgstr "Tiếng Việt"

#: InvenTree/locales.py:56
msgid "Chinese (Simplified)"
msgstr "Tiếng Trung (Giản thể)"

#: InvenTree/locales.py:57
msgid "Chinese (Traditional)"
msgstr "Tiếng Trung (Phồn thể)"

#: InvenTree/magic_login.py:31
msgid "Log in to the app"
msgstr ""

#: InvenTree/magic_login.py:41 company/models.py:136 users/serializers.py:247
msgid "Email"
msgstr "Email"

#: InvenTree/models.py:107
msgid "Error running plugin validation"
msgstr "Lỗi xác thực plugin"

#: InvenTree/models.py:184
msgid "Metadata must be a python dict object"
msgstr "Siêu dữ liệu phải là đối tượng từ điển của python"

#: InvenTree/models.py:190
msgid "Plugin Metadata"
msgstr "Phụ trợ siêu dữ liệu"

#: InvenTree/models.py:191
msgid "JSON metadata field, for use by external plugins"
msgstr "Trường siêu dữ liệu JSON, được sử dụng bởi phụ trợ bên ngoài"

#: InvenTree/models.py:374
msgid "Improperly formatted pattern"
msgstr "Mẫu được định dạng không thích hợp"

#: InvenTree/models.py:381
msgid "Unknown format key specified"
msgstr "Khóa định dạng không rõ ràng đã được chỉ định"

#: InvenTree/models.py:387
msgid "Missing required format key"
msgstr "Thiếu khóa định dạng cần thiết"

#: InvenTree/models.py:398
msgid "Reference field cannot be empty"
msgstr "Trường tham chiếu không thể rỗng"

#: InvenTree/models.py:406
msgid "Reference must match required pattern"
msgstr "Tham chiếu phải phù hợp với mẫu yêu cầu"

#: InvenTree/models.py:437
msgid "Reference number is too large"
msgstr "Số tham chiếu quá lớn"

#: InvenTree/models.py:688
msgid "Duplicate names cannot exist under the same parent"
msgstr "Tên trùng lặp không thể tồn tại trong cùng cấp thư mục"

#: InvenTree/models.py:705
msgid "Invalid choice"
msgstr "Lựa chọn sai"

#: InvenTree/models.py:734 common/models.py:1325 common/models.py:1752
#: common/models.py:2011 common/models.py:2136 common/serializers.py:488
#: company/models.py:593 generic/states/serializers.py:20 machine/models.py:24
#: part/models.py:1039 part/models.py:3837 plugin/models.py:53
#: report/models.py:203 stock/models.py:83
msgid "Name"
msgstr "Tên"

#: InvenTree/models.py:740 build/models.py:242 common/models.py:123
#: common/models.py:2143 common/models.py:2256 company/models.py:521
#: company/models.py:825 order/models.py:429 order/models.py:1615
#: part/models.py:1062 part/models.py:3852 report/models.py:209
#: report/models.py:714 report/models.py:740
#: report/templates/report/inventree_build_order_report.html:117
#: stock/models.py:89
msgid "Description"
msgstr "Mô tả"

#: InvenTree/models.py:741 stock/models.py:90
msgid "Description (optional)"
msgstr "Mô tả (tùy chọn)"

#: InvenTree/models.py:756 common/models.py:2309
msgid "Path"
msgstr "Đường dẫn"

#: InvenTree/models.py:887
msgid "Markdown notes (optional)"
msgstr "Ghi chú markdown (không bắt buộc)"

#: InvenTree/models.py:918
msgid "Barcode Data"
msgstr "Dữ liệu mã vạch"

#: InvenTree/models.py:919
msgid "Third party barcode data"
msgstr "Dữ liệu mã vạch của bên thứ ba"

#: InvenTree/models.py:925
msgid "Barcode Hash"
msgstr "Dữ liệu băm mã vạch"

#: InvenTree/models.py:926
msgid "Unique hash of barcode data"
msgstr "Chuỗi băm duy nhất của dữ liệu mã vạch"

#: InvenTree/models.py:1003
msgid "Existing barcode found"
msgstr "Mã vạch đã tồn tại"

#: InvenTree/models.py:1084
msgid "Task Failure"
msgstr ""

#: InvenTree/models.py:1085
#, python-brace-format
msgid "Background worker task '{f}' failed after {n} attempts"
msgstr ""

#: InvenTree/models.py:1112
msgid "Server Error"
msgstr "Lỗi máy chủ"

#: InvenTree/models.py:1113
msgid "An error has been logged by the server."
msgstr "Lỗi đã được ghi lại bởi máy chủ."

#: InvenTree/serializers.py:69 part/models.py:4455
msgid "Must be a valid number"
msgstr "Phải là một số hợp lệ"

#: InvenTree/serializers.py:111 company/models.py:187 part/models.py:3184
msgid "Currency"
msgstr "Tiền tệ"

#: InvenTree/serializers.py:114 part/serializers.py:1335
msgid "Select currency from available options"
msgstr "Chọn tiền tệ trong các tùy chọn đang có"

#: InvenTree/serializers.py:466
msgid "Invalid value"
msgstr "Giá trị không hợp lệ"

#: InvenTree/serializers.py:503
msgid "Remote Image"
msgstr "Hình ảnh từ xa"

#: InvenTree/serializers.py:504
msgid "URL of remote image file"
msgstr "URL của tệp hình ảnh bên ngoài"

#: InvenTree/serializers.py:522
msgid "Downloading images from remote URL is not enabled"
msgstr "Chức năng tải hình ảnh từ URL bên ngoài không được bật"

#: InvenTree/serializers.py:529
msgid "Failed to download image from remote URL"
msgstr ""

#: InvenTree/validators.py:30
msgid "Invalid physical unit"
msgstr "Đơn vị vật lý không hợp lệ"

#: InvenTree/validators.py:36
msgid "Not a valid currency code"
msgstr "Mã tiền tệ không hợp lệ"

#: InvenTree/validators.py:116 InvenTree/validators.py:132
msgid "Overage value must not be negative"
msgstr "Giá trị hàng hóa dư không thể là số âm"

#: InvenTree/validators.py:134
msgid "Overage must not exceed 100%"
msgstr "Hàng hóa dư thừa không thể vượt quá 100%"

#: InvenTree/validators.py:140
msgid "Invalid value for overage"
msgstr "Giá trị không hợp lệ cho hàng hóa dư thừa"

#: build/api.py:39 order/api.py:104 order/api.py:263 order/serializers.py:128
msgid "Order Status"
msgstr "Trạng thái đặt hàng"

#: build/api.py:65 build/models.py:254
msgid "Parent Build"
msgstr "Phiên bản cha"

#: build/api.py:69 build/api.py:768 order/api.py:512 order/api.py:732
#: order/api.py:1106 order/api.py:1334 stock/api.py:522
msgid "Include Variants"
msgstr ""

#: build/api.py:85 build/api.py:470 build/api.py:782 build/models.py:260
#: build/serializers.py:1254 build/serializers.py:1376
#: build/serializers.py:1427 company/models.py:1044 company/serializers.py:432
#: order/api.py:291 order/api.py:295 order/api.py:880 order/api.py:1119
#: order/api.py:1122 order/models.py:1704 order/models.py:1863
#: order/models.py:1864 part/api.py:1448 part/api.py:1451 part/api.py:1517
#: part/api.py:1827 part/models.py:457 part/models.py:3195 part/models.py:3339
#: part/models.py:3487 part/models.py:3508 part/models.py:3530
#: part/models.py:3666 part/models.py:4027 part/models.py:4299
#: part/models.py:4665 part/serializers.py:1255 part/serializers.py:1924
#: report/templates/report/inventree_bill_of_materials_report.html:110
#: report/templates/report/inventree_bill_of_materials_report.html:137
#: report/templates/report/inventree_build_order_report.html:109
#: report/templates/report/inventree_purchase_order_report.html:27
#: report/templates/report/inventree_return_order_report.html:24
#: report/templates/report/inventree_sales_order_report.html:27
#: report/templates/report/inventree_sales_order_shipment_report.html:28
#: report/templates/report/inventree_stock_location_report.html:102
#: stock/api.py:535 stock/serializers.py:119 stock/serializers.py:171
#: stock/serializers.py:481 stock/serializers.py:643 stock/serializers.py:948
#: templates/email/build_order_completed.html:17
#: templates/email/build_order_required_stock.html:17
#: templates/email/low_stock_notification.html:15
#: templates/email/overdue_build_order.html:16
#: templates/email/part_event_notification.html:15
msgid "Part"
msgstr "Nguyên liệu"

#: build/api.py:105 build/api.py:108 part/api.py:1531 part/models.py:1080
#: part/models.py:3558 part/models.py:4136 part/serializers.py:1265
#: stock/api.py:818
msgid "Category"
msgstr "Danh mục"

#: build/api.py:116 build/api.py:120
msgid "Ancestor Build"
msgstr "Xây dựng nguồn gốc"

#: build/api.py:137 order/api.py:122
msgid "Assigned to me"
msgstr "Đã gán cho tôi"

#: build/api.py:152
#: report/templates/report/inventree_build_order_report.html:152
msgid "Issued By"
msgstr "Phát hành bởi"

#: build/api.py:167
msgid "Assigned To"
msgstr "Đã gán cho"

#: build/api.py:202
msgid "Created before"
msgstr ""

#: build/api.py:206
msgid "Created after"
msgstr ""

#: build/api.py:210
msgid "Has start date"
msgstr ""

#: build/api.py:218
msgid "Start date before"
msgstr ""

#: build/api.py:222
msgid "Start date after"
msgstr ""

#: build/api.py:226
msgid "Has target date"
msgstr ""

#: build/api.py:234
msgid "Target date before"
msgstr ""

#: build/api.py:238
msgid "Target date after"
msgstr ""

#: build/api.py:242
msgid "Completed before"
msgstr ""

#: build/api.py:246
msgid "Completed after"
msgstr ""

#: build/api.py:249 order/api.py:219
msgid "Min Date"
msgstr ""

#: build/api.py:272 order/api.py:238
msgid "Max Date"
msgstr ""

#: build/api.py:297 build/api.py:300 part/api.py:224
msgid "Exclude Tree"
msgstr ""

#: build/api.py:409
msgid "Build must be cancelled before it can be deleted"
msgstr "Bạn dựng phải được hủy bỏ trước khi có thể xóa được"

#: build/api.py:453 build/serializers.py:1392 part/models.py:4333
msgid "Consumable"
msgstr "Vật tư tiêu hao"

#: build/api.py:456 build/serializers.py:1395 part/models.py:4327
msgid "Optional"
msgstr "Tuỳ chọn"

#: build/api.py:459 common/setting/system.py:429 part/models.py:1211
#: part/serializers.py:1663 part/serializers.py:1672 stock/api.py:588
msgid "Assembly"
msgstr "Lắp ráp"

#: build/api.py:462
msgid "Tracked"
msgstr "Đã theo dõi"

#: build/api.py:465 build/serializers.py:1398 part/models.py:1229
msgid "Testable"
msgstr "Có thể kiểm tra"

#: build/api.py:475 order/api.py:925
msgid "Order Outstanding"
msgstr ""

#: build/api.py:485 order/api.py:884
msgid "Allocated"
msgstr "Đã cấp phát"

#: build/api.py:495 company/models.py:889 company/serializers.py:427
#: templates/email/build_order_required_stock.html:19
#: templates/email/low_stock_notification.html:17
#: templates/email/part_event_notification.html:18
msgid "Available"
msgstr "Có sẵn"

#: build/api.py:805 build/models.py:116
#: report/templates/report/inventree_build_order_report.html:105
#: stock/serializers.py:92 templates/email/build_order_completed.html:16
#: templates/email/overdue_build_order.html:15
msgid "Build Order"
msgstr "Tạo đơn hàng"

#: build/models.py:117 users/ruleset.py:37
msgid "Build Orders"
msgstr "Tạo đơn hàng"

#: build/models.py:162
msgid "Assembly BOM has not been validated"
msgstr "Dây chuyền BOM chưa được xác thực"

#: build/models.py:169
msgid "Build order cannot be created for an inactive part"
msgstr "Không thể tạo đơn hàng cho hàng hoá đang không hoạt động"

#: build/models.py:176
msgid "Build order cannot be created for an unlocked part"
msgstr "Không thể tạo đơn hàng cho hàng hoá đang mở khoá"

#: build/models.py:188
msgid "Invalid choice for parent build"
msgstr "Lựa chọn sai cho bản dựng cha"

#: build/models.py:197 order/models.py:363
msgid "Responsible user or group must be specified"
msgstr "Phải chọn người dùng hoặc nhóm"

#: build/models.py:202
msgid "Build order part cannot be changed"
msgstr "Sản phẩm đơn đặt bản dựng không thể thay đổi được"

#: build/models.py:207 order/models.py:376
msgid "Target date must be after start date"
msgstr ""

#: build/models.py:235
msgid "Build Order Reference"
msgstr "Tham chiếu đơn đặt bản dựng"

#: build/models.py:236 build/serializers.py:1389 order/models.py:601
#: order/models.py:1140 order/models.py:1572 order/models.py:2403
#: part/models.py:4348
#: report/templates/report/inventree_bill_of_materials_report.html:139
#: report/templates/report/inventree_purchase_order_report.html:28
#: report/templates/report/inventree_return_order_report.html:26
#: report/templates/report/inventree_sales_order_report.html:28
msgid "Reference"
msgstr "Tham chiếu"

#: build/models.py:245
msgid "Brief description of the build (optional)"
msgstr "Mô tả ngắn về phiên bạn (Tùy chọn)"

#: build/models.py:255
msgid "BuildOrder to which this build is allocated"
msgstr "Đơn đặt bản dựng với bản dựng này đã được phân bổ"

#: build/models.py:264
msgid "Select part to build"
msgstr "Chọn sản phẩm để xây dựng"

#: build/models.py:269
msgid "Sales Order Reference"
msgstr "Tham chiếu đơn đặt bản dựng"

#: build/models.py:274
msgid "SalesOrder to which this build is allocated"
msgstr "Đơn đặt bán hàng với bản dựng này đã được phân bổ"

#: build/models.py:279 build/serializers.py:1104
msgid "Source Location"
msgstr "Địa điểm nguồn"

#: build/models.py:285
msgid "Select location to take stock from for this build (leave blank to take from any stock location)"
msgstr "Chọn địa điểm để lấy trong kho cho bản dựng này (để trống để lấy từ bất kỳ vị trí kho nào)"

#: build/models.py:291
msgid "Destination Location"
msgstr "Địa điểm đích"

#: build/models.py:296
msgid "Select location where the completed items will be stored"
msgstr "Chọn địa điểm nơi hàng hóa hoàn thiện sẽ được lưu kho"

#: build/models.py:300
msgid "Build Quantity"
msgstr "Xây dựng số lượng"

#: build/models.py:303
msgid "Number of stock items to build"
msgstr "Số kho hàng để dựng"

#: build/models.py:307
msgid "Completed items"
msgstr "Những mục hoàn thành"

#: build/models.py:309
msgid "Number of stock items which have been completed"
msgstr "Số sản phẩm trong kho đã được hoàn thiện"

#: build/models.py:313
msgid "Build Status"
msgstr "Trnạg thái bản dựng"

#: build/models.py:318
msgid "Build status code"
msgstr "Mã trạng thái bản dựng"

#: build/models.py:327 build/serializers.py:376 order/serializers.py:785
#: stock/models.py:1023 stock/serializers.py:84 stock/serializers.py:1651
msgid "Batch Code"
msgstr "Mã lô hàng"

#: build/models.py:331 build/serializers.py:377
msgid "Batch code for this build output"
msgstr "Mã lô cho đầu ra bản dựng này"

#: build/models.py:335 order/models.py:466 order/serializers.py:166
#: part/models.py:1286
msgid "Creation Date"
msgstr "Ngày tạo"

#: build/models.py:341
msgid "Build start date"
msgstr ""

#: build/models.py:342
msgid "Scheduled start date for this build order"
msgstr ""

#: build/models.py:348
msgid "Target completion date"
msgstr "Ngày hoàn thành mục tiêu"

#: build/models.py:350
msgid "Target date for build completion. Build will be overdue after this date."
msgstr "Ngày mục tiêu để hoàn thành bản dựng. Bản dựng sẽ bị quá hạn sau ngày này."

#: build/models.py:355 order/models.py:654 order/models.py:2442
msgid "Completion Date"
msgstr "Ngày hoàn thành"

#: build/models.py:363
msgid "completed by"
msgstr "hoàn thành bởi"

#: build/models.py:372
msgid "Issued by"
msgstr "Cấp bởi"

#: build/models.py:373
msgid "User who issued this build order"
msgstr "Người dùng người đã được phân công cho đơn đặt bản dựng này"

#: build/models.py:382 common/models.py:132 order/api.py:172
#: order/models.py:491 part/models.py:1303
#: report/templates/report/inventree_build_order_report.html:158
msgid "Responsible"
msgstr "Chịu trách nhiệm"

#: build/models.py:383
msgid "User or group responsible for this build order"
msgstr "Người dùng hoặc nhóm có trách nhiệm với đơn đặt bản dựng này"

#: build/models.py:388 stock/models.py:1016
msgid "External Link"
msgstr "Liên kết bên ngoài"

#: build/models.py:390 common/models.py:1899 part/models.py:1114
#: stock/models.py:1018
msgid "Link to external URL"
msgstr "Liên kết đến URL bên ngoài"

#: build/models.py:395
msgid "Build Priority"
msgstr "Độ ưu tiên"

#: build/models.py:398
msgid "Priority of this build order"
msgstr "Độ quan trọng của đơn đặt bản dựng"

#: build/models.py:406 common/models.py:102 common/models.py:116
#: order/api.py:158 order/models.py:438
msgid "Project Code"
msgstr "Mã dự án"

#: build/models.py:407
msgid "Project code for this build order"
msgstr "Mã dự án cho đơn đặt bản dựng này"

#: build/models.py:680 build/models.py:803
msgid "Failed to offload task to complete build allocations"
msgstr "Không thể dỡ bỏ tác vụ để hoàn tất phân bổ"

#: build/models.py:703
#, python-brace-format
msgid "Build order {build} has been completed"
msgstr "Đơn đặt bản dựng {build} đã được hoàn thành"

#: build/models.py:709
msgid "A build order has been completed"
msgstr "Một đơn đặt bản dựng đã được hoàn thành"

#: build/models.py:881 build/serializers.py:424
msgid "Serial numbers must be provided for trackable parts"
msgstr "Số sê-ri phải được cung cấp cho hàng hoá có thể theo dõi"

#: build/models.py:991 build/models.py:1076
msgid "No build output specified"
msgstr "Không có đầu ra bản dựng đã được chỉ ra"

#: build/models.py:994
msgid "Build output is already completed"
msgstr "Đầu ra bản dựng đã được hoàn thiện"

#: build/models.py:997
msgid "Build output does not match Build Order"
msgstr "Đầu ra bản dựng không phù hợp với đơn đặt bản dựng"

#: build/models.py:1079 build/serializers.py:303 build/serializers.py:352
#: build/serializers.py:972 order/models.py:704 order/serializers.py:601
#: order/serializers.py:780 part/serializers.py:1657 stock/models.py:863
#: stock/models.py:1730 stock/serializers.py:727
msgid "Quantity must be greater than zero"
msgstr "Số lượng phải lớn hơn 0"

#: build/models.py:1083 build/serializers.py:307
msgid "Quantity cannot be greater than the output quantity"
msgstr "Số lượng không thể lớn hơn số lượng đầu ra"

#: build/models.py:1148 build/serializers.py:615
#, python-brace-format
msgid "Build output {serial} has not passed all required tests"
msgstr "Tạo đầu ra {serial} chưa vượt qua tất cả các bài kiểm tra"

#: build/models.py:1534
msgid "Build Order Line Item"
msgstr "Tạo mục đơn hàng"

#: build/models.py:1558
msgid "Build object"
msgstr "Dựng đối tượng"

#: build/models.py:1570 build/models.py:1829 build/serializers.py:291
#: build/serializers.py:337 build/serializers.py:1410 common/models.py:1255
#: order/models.py:1555 order/models.py:2288 order/serializers.py:1670
#: order/serializers.py:2131 part/models.py:3353 part/models.py:4321
#: part/serializers.py:275
#: report/templates/report/inventree_bill_of_materials_report.html:138
#: report/templates/report/inventree_build_order_report.html:113
#: report/templates/report/inventree_purchase_order_report.html:29
#: report/templates/report/inventree_sales_order_report.html:29
#: report/templates/report/inventree_sales_order_shipment_report.html:47
#: report/templates/report/inventree_sales_order_shipment_report.html:49
#: report/templates/report/inventree_stock_location_report.html:104
#: report/templates/report/inventree_test_report.html:90
#: report/templates/report/inventree_test_report.html:169
#: stock/serializers.py:135 stock/serializers.py:179 stock/serializers.py:718
#: templates/email/build_order_completed.html:18
msgid "Quantity"
msgstr "Số lượng"

#: build/models.py:1571
msgid "Required quantity for build order"
msgstr "Yêu cầu số lượng để dựng đơn đặt"

#: build/models.py:1655
msgid "Build item must specify a build output, as master part is marked as trackable"
msgstr "Xây dựng mục phải xác định đầu ra, bởi vì sản phẩm chủ được đánh dấu là có thể theo dõi"

#: build/models.py:1666
#, python-brace-format
msgid "Allocated quantity ({q}) must not exceed available stock quantity ({a})"
msgstr "Số lượng được phân bổ ({q}) không thể vượt quá số lượng có trong kho ({a})"

#: build/models.py:1687 order/models.py:2237
msgid "Stock item is over-allocated"
msgstr "Kho hàng đã bị phân bổ quá đà"

#: build/models.py:1692 order/models.py:2240
msgid "Allocation quantity must be greater than zero"
msgstr "Số lượng phân bổ phải lớn hơn 0"

#: build/models.py:1698
msgid "Quantity must be 1 for serialized stock"
msgstr "Số lượng phải là 1 cho kho sê ri"

#: build/models.py:1758
msgid "Selected stock item does not match BOM line"
msgstr "Hàng trong kho đã chọn không phù hợp với đường BOM"

#: build/models.py:1819 build/serializers.py:955 build/serializers.py:1263
#: order/serializers.py:1507 order/serializers.py:1528
#: report/templates/report/inventree_sales_order_shipment_report.html:29
#: stock/models.py:452 stock/serializers.py:101 stock/serializers.py:825
#: stock/serializers.py:1357 stock/serializers.py:1469
msgid "Stock Item"
msgstr "Kho hàng"

#: build/models.py:1820
msgid "Source stock item"
msgstr "Kho hàng gốc"

#: build/models.py:1830
msgid "Stock quantity to allocate to build"
msgstr "Số lượng kho hàng cần chỉ định để xây dựng"

#: build/models.py:1839
msgid "Install into"
msgstr "Cài đặt vào"

#: build/models.py:1840
msgid "Destination stock item"
msgstr "Kho hàng đích"

#: build/serializers.py:116
msgid "Build Level"
msgstr "Tạo cấp"

#: build/serializers.py:125 part/stocktake.py:219
msgid "Part Name"
msgstr "Tên sản phẩm"

#: build/serializers.py:143
msgid "Project Code Label"
msgstr "Nhãn mã dự án"

#: build/serializers.py:155
msgid "Create Child Builds"
msgstr "Tạo mới bản dựng con"

#: build/serializers.py:156
msgid "Automatically generate child build orders"
msgstr "Tự động tạo đơn hàng con"

#: build/serializers.py:239 build/serializers.py:981
msgid "Build Output"
msgstr "Đầu ra bản dựng"

#: build/serializers.py:251
msgid "Build output does not match the parent build"
msgstr "Đầu ra xây dựng không hợp với bản dựng cha"

#: build/serializers.py:255
msgid "Output part does not match BuildOrder part"
msgstr "Đầu ra sản phẩm không phù hợp với bản dựng đơn đặt hàng"

#: build/serializers.py:259
msgid "This build output has already been completed"
msgstr "Đầu ra bản dựng này đã được hoàn thành"

#: build/serializers.py:273
msgid "This build output is not fully allocated"
msgstr "Đầu ra bản dựng này chưa được phân bổ đầy đủ"

#: build/serializers.py:292 build/serializers.py:338
msgid "Enter quantity for build output"
msgstr "Điền số lượng cho đầu ra bản dựng"

#: build/serializers.py:360
msgid "Integer quantity required for trackable parts"
msgstr "Số lượng nguyên dương cần phải điền cho sản phẩm có thể theo dõi"

#: build/serializers.py:366
msgid "Integer quantity required, as the bill of materials contains trackable parts"
msgstr "Cần nhập số lượng nguyên dương, bởi vì hóa đơn vật liệu chứa sản phẩm có thể theo dõi"

#: build/serializers.py:383 order/serializers.py:801 order/serializers.py:1674
#: stock/serializers.py:738
msgid "Serial Numbers"
msgstr "Số sê-ri"

#: build/serializers.py:384
msgid "Enter serial numbers for build outputs"
msgstr "Nhập vào số sêri cho đầu ra bản dựng"

#: build/serializers.py:389 build/serializers.py:514 build/serializers.py:584
#: build/serializers.py:1267 build/serializers.py:1271 order/serializers.py:769
#: order/serializers.py:916 order/serializers.py:2022 part/serializers.py:1275
#: stock/serializers.py:110 stock/serializers.py:647 stock/serializers.py:749
#: stock/serializers.py:913 stock/serializers.py:1041 stock/serializers.py:1501
#: stock/serializers.py:1789 users/models.py:551
msgid "Location"
msgstr "Địa điểm"

#: build/serializers.py:390
msgid "Stock location for build output"
msgstr "Vị trí tồn kho cho sản phẩm"

#: build/serializers.py:405
msgid "Auto Allocate Serial Numbers"
msgstr "Số sêri tự cấp"

#: build/serializers.py:407
msgid "Automatically allocate required items with matching serial numbers"
msgstr "Tự động cấp số seri phù hợp cho hàng hóa được yêu cầu"

#: build/serializers.py:440 order/serializers.py:894 stock/api.py:1077
#: stock/models.py:1753
msgid "The following serial numbers already exist or are invalid"
msgstr "Số sêri sau đây đã tồn tại hoặc không hợp lệ"

#: build/serializers.py:482 build/serializers.py:538 build/serializers.py:622
msgid "A list of build outputs must be provided"
msgstr "Danh sách đầu ra bản dựng phải được cung cấp"

#: build/serializers.py:515
msgid "Stock location for scrapped outputs"
msgstr "Vị trí kho cho đầu ra phế phẩm"

#: build/serializers.py:521
msgid "Discard Allocations"
msgstr "Hủy phân bổ"

#: build/serializers.py:522
msgid "Discard any stock allocations for scrapped outputs"
msgstr "Hủy bất kỳ phân kho nào cho đầu ra phế phẩm"

#: build/serializers.py:527
msgid "Reason for scrapping build output(s)"
msgstr "Lý do loại bỏ đầu ra bản dựng"

#: build/serializers.py:585
msgid "Location for completed build outputs"
msgstr "Vị trí cho đầu ra bản dựng hoàn thiện"

#: build/serializers.py:593
msgid "Accept Incomplete Allocation"
msgstr "Chấp nhận phân kho dang dở"

#: build/serializers.py:594
msgid "Complete outputs if stock has not been fully allocated"
msgstr "Hoàn hiện đầu ra nếu kho chưa được phân bổ hết chỗ trống"

#: build/serializers.py:709
msgid "Consume Allocated Stock"
msgstr "Xử lý phân bổ kho hàng"

#: build/serializers.py:710
msgid "Consume any stock which has already been allocated to this build"
msgstr "Tiêu thụ bất kỳ hàng tồn kho nào đã được phân bổ cho dự án này."

#: build/serializers.py:716
msgid "Remove Incomplete Outputs"
msgstr "Xóa toàn bộ đầu ra chưa hoàn thành"

#: build/serializers.py:717
msgid "Delete any build outputs which have not been completed"
msgstr "Xóa bất kỳ đầu ra bản dựng nào chưa được hoàn thành"

#: build/serializers.py:744
msgid "Not permitted"
msgstr "Chưa được cấp phép"

#: build/serializers.py:745
msgid "Accept as consumed by this build order"
msgstr "Chấp nhận trạng thái tiêu hao bởi đơn đặt bản dựng này"

#: build/serializers.py:746
msgid "Deallocate before completing this build order"
msgstr "Phân bổ trước khi hoàn thiện đơn đặt bản dựng này"

#: build/serializers.py:773
msgid "Overallocated Stock"
msgstr "Kho quá tải"

#: build/serializers.py:776
msgid "How do you want to handle extra stock items assigned to the build order"
msgstr "Bạn muốn thế nào để xử lý hàng trong kho được gán thừa cho đơn đặt bản dựng"

#: build/serializers.py:787
msgid "Some stock items have been overallocated"
msgstr "Một vài hàng hóa đã được phân bổ quá thừa"

#: build/serializers.py:792
msgid "Accept Unallocated"
msgstr "Chấp nhận chưa phân bổ được"

#: build/serializers.py:794
msgid "Accept that stock items have not been fully allocated to this build order"
msgstr "Chấp nhận hàng hóa không được phân bổ đầy đủ vào đơn đặt bản dựng này"

#: build/serializers.py:805
msgid "Required stock has not been fully allocated"
msgstr "Kho được yêu cầu chưa được phân bổ hết không gian"

#: build/serializers.py:810 order/serializers.py:444 order/serializers.py:1575
msgid "Accept Incomplete"
msgstr "Chấp nhận không hoàn thành"

#: build/serializers.py:812
msgid "Accept that the required number of build outputs have not been completed"
msgstr "Chấp nhận số yêu cầu của đầu ra bản dựng chưa được hoàn thành"

#: build/serializers.py:823
msgid "Required build quantity has not been completed"
msgstr "Số lượng bản dựng được yêu cầu chưa được hoàn thành"

#: build/serializers.py:835
msgid "Build order has open child build orders"
msgstr "Tạo đơn hàng có các đơn hàng đang mở"

#: build/serializers.py:838
msgid "Build order must be in production state"
msgstr "Tạo đơn hàng phải ở trạng thái sản xuất."

#: build/serializers.py:841
msgid "Build order has incomplete outputs"
msgstr "Đơn đặt bản dựng có đầu ra chưa hoàn thiện"

#: build/serializers.py:880
msgid "Build Line"
msgstr "Lộ giới"

#: build/serializers.py:888
msgid "Build output"
msgstr "Đầu ra bản dựng"

#: build/serializers.py:896
msgid "Build output must point to the same build"
msgstr "Đầu ra bản dựng phải chỉ đến bản dựng tương ứng"

#: build/serializers.py:927
msgid "Build Line Item"
msgstr "Mục chi tiết bản dựng"

#: build/serializers.py:945
msgid "bom_item.part must point to the same part as the build order"
msgstr "bom_item.part phải trỏ đến phần tương tự của đơn đặt bản dựng"

#: build/serializers.py:961 stock/serializers.py:1370
msgid "Item must be in stock"
msgstr "Hàng hóa phải trong kho"

#: build/serializers.py:1004 order/serializers.py:1561
#, python-brace-format
msgid "Available quantity ({q}) exceeded"
msgstr "Số lượng có sẵn ({q}) đã bị vượt quá"

#: build/serializers.py:1010
msgid "Build output must be specified for allocation of tracked parts"
msgstr "Đầu ra bản dựng phải được xác định cho việc phân sản phẩm được theo dõi"

#: build/serializers.py:1018
msgid "Build output cannot be specified for allocation of untracked parts"
msgstr "Đầu ra bản dựng không thể chỉ định cho việc phân sản phẩm chưa được theo dõi"

#: build/serializers.py:1042 order/serializers.py:1834
msgid "Allocation items must be provided"
msgstr "Hàng hóa phân bổ phải được cung cấp"

#: build/serializers.py:1106
msgid "Stock location where parts are to be sourced (leave blank to take from any location)"
msgstr "Vị trí kho nơi sản phẩm được lấy ra (để trống để lấy từ bất kỳ vị trí nào)"

#: build/serializers.py:1115
msgid "Exclude Location"
msgstr "Ngoại trừ vị trí"

#: build/serializers.py:1116
msgid "Exclude stock items from this selected location"
msgstr "Không bao gồm hàng trong kho từ vị trí đã chọn này"

#: build/serializers.py:1121
msgid "Interchangeable Stock"
msgstr "Kho trao đổi"

#: build/serializers.py:1122
msgid "Stock items in multiple locations can be used interchangeably"
msgstr "Hàng trong kho thuộc nhiều vị trí có thể dùng thay thế được cho nhau"

#: build/serializers.py:1127
msgid "Substitute Stock"
msgstr "Kho thay thế"

#: build/serializers.py:1128
msgid "Allow allocation of substitute parts"
msgstr "Cho phép phân kho sản phẩm thay thế"

#: build/serializers.py:1133
msgid "Optional Items"
msgstr "Mục tùy chọn"

#: build/serializers.py:1134
msgid "Allocate optional BOM items to build order"
msgstr "Phân bổ các mục hóa đơn vật liệu tùy chọn đến đơn đặt bản dựng"

#: build/serializers.py:1156
msgid "Failed to start auto-allocation task"
msgstr "Không thể khởi động tác vụ phân bổ tự động."

#: build/serializers.py:1230
msgid "BOM Reference"
msgstr "BOM liên quan"

#: build/serializers.py:1236
msgid "BOM Part ID"
msgstr "ID hàng hoá BOM"

#: build/serializers.py:1243
msgid "BOM Part Name"
msgstr "Tên hàng hoá BOM"

#: build/serializers.py:1278 build/serializers.py:1434
msgid "Build"
msgstr ""

#: build/serializers.py:1286 company/models.py:662 order/api.py:304
#: order/api.py:309 order/api.py:508 order/serializers.py:593
#: stock/models.py:959 stock/serializers.py:631
msgid "Supplier Part"
msgstr "Sản phẩm nhà cung cấp"

#: build/serializers.py:1294 stock/serializers.py:662
msgid "Allocated Quantity"
msgstr "Số lượng đã phân bổ"

#: build/serializers.py:1371
msgid "Build Reference"
msgstr "Tạo liên quan"

#: build/serializers.py:1381
msgid "Part Category Name"
msgstr "Tên danh mục hàng hoá"

#: build/serializers.py:1401 common/setting/system.py:453 part/models.py:1223
msgid "Trackable"
msgstr "Có thể theo dõi"

#: build/serializers.py:1404
msgid "Inherited"
msgstr "Được kế thừa"

#: build/serializers.py:1407 part/models.py:4381
msgid "Allow Variants"
msgstr "Cho phép biến thể"

#: build/serializers.py:1412 build/serializers.py:1416 part/models.py:4179
#: part/models.py:4657 stock/api.py:831
msgid "BOM Item"
msgstr "Mục BOM"

#: build/serializers.py:1445
msgid "Allocated Stock"
msgstr "Phân kho"

#: build/serializers.py:1447 company/serializers.py:424
#: order/serializers.py:1271 part/serializers.py:943 part/serializers.py:1690
msgid "On Order"
msgstr "Bật đơn hàng"

#: build/serializers.py:1449 order/serializers.py:1272 part/serializers.py:1694
msgid "In Production"
msgstr "Đang sản xuất"

#: build/serializers.py:1451 part/serializers.py:964
msgid "External Stock"
msgstr "Kho ngoài"

#: build/serializers.py:1452 part/serializers.py:1723
msgid "Available Stock"
msgstr "Số hàng tồn"

#: build/serializers.py:1454
msgid "Available Substitute Stock"
msgstr "Kho hàng thay thế"

#: build/serializers.py:1457
msgid "Available Variant Stock"
msgstr "Hàng tồn kho có sẵn"

#: build/status_codes.py:11 generic/states/tests.py:21
#: generic/states/tests.py:131 order/status_codes.py:12
#: order/status_codes.py:44 order/status_codes.py:76 order/status_codes.py:102
msgid "Pending"
msgstr "Đợi duyệt"

#: build/status_codes.py:12
msgid "Production"
msgstr "Sản xuất"

#: build/status_codes.py:13 order/status_codes.py:14 order/status_codes.py:51
#: order/status_codes.py:81
msgid "On Hold"
msgstr "Chờ"

#: build/status_codes.py:14 order/status_codes.py:16 order/status_codes.py:53
#: order/status_codes.py:84
msgid "Cancelled"
msgstr "Đã hủy"

#: build/status_codes.py:15 generic/states/tests.py:23 importer/models.py:529
#: importer/status_codes.py:27 order/status_codes.py:15
#: order/status_codes.py:52 order/status_codes.py:83
msgid "Complete"
msgstr "Hoàn thành"

#: build/tasks.py:193
msgid "Stock required for build order"
msgstr "Kho được yêu cầu cho đặt hàng bản dựng"

#: build/tasks.py:267
msgid "Overdue Build Order"
msgstr "Đơn đặt bản dựng quá hạn"

#: build/tasks.py:272
#, python-brace-format
msgid "Build order {bo} is now overdue"
msgstr "Đặt hàng bản dựng {bo} đang quá hạn"

#: common/api.py:710
msgid "Is Link"
msgstr "Đường dẫn"

#: common/api.py:718
msgid "Is File"
msgstr "File"

#: common/api.py:761
msgid "User does not have permission to delete these attachments"
msgstr "Không có quyền xoá file đính kèm"

#: common/api.py:778
msgid "User does not have permission to delete this attachment"
msgstr "Không có quyền xoá file đính kèm"

#: common/currency.py:120
msgid "Invalid currency code"
msgstr "Sai mã tiền tệ"

#: common/currency.py:122
msgid "Duplicate currency code"
msgstr "Trùng mã tiền tệ"

#: common/currency.py:127
msgid "No valid currency codes provided"
msgstr "Mã tiền tệ không đúng"

#: common/currency.py:144
msgid "No plugin"
msgstr "Không phần mở rộng"

#: common/models.py:89
msgid "Updated"
msgstr "Đã cập nhật"

#: common/models.py:90
msgid "Timestamp of last update"
msgstr "Nhãn thời gian của lần cập cuối cùng"

#: common/models.py:117
msgid "Unique project code"
msgstr "Mã dự án duy nhất"

#: common/models.py:124
msgid "Project description"
msgstr "Mô tả dự án"

#: common/models.py:133
msgid "User or group responsible for this project"
msgstr "Người dùng hoặc nhóm có trách nhiệm với dự án này"

#: common/models.py:721 common/models.py:1187 common/models.py:1225
msgid "Settings key"
msgstr ""

#: common/models.py:725
msgid "Settings value"
msgstr "Giá trị cài đặt"

#: common/models.py:780
msgid "Chosen value is not a valid option"
msgstr "Giá trị đã chọn không hợp lệ"

#: common/models.py:796
msgid "Value must be a boolean value"
msgstr "Giá trị phải là kiểu boolean"

#: common/models.py:804
msgid "Value must be an integer value"
msgstr "Giá trị phải là một số nguyên dương"

#: common/models.py:812
msgid "Value must be a valid number"
msgstr ""

#: common/models.py:837
msgid "Value does not pass validation checks"
msgstr ""

#: common/models.py:859
msgid "Key string must be unique"
msgstr "Chuỗi khóa phải duy nhất"

#: common/models.py:1233 common/models.py:1234 common/models.py:1338
#: common/models.py:1339 common/models.py:1584 common/models.py:1585
#: common/models.py:1915 common/models.py:1916 common/models.py:2297
#: importer/models.py:97 part/models.py:3376 part/models.py:3463
#: part/models.py:3537 part/models.py:3565 plugin/models.py:322
#: plugin/models.py:323 report/templates/report/inventree_test_report.html:105
#: users/models.py:130 users/models.py:503
msgid "User"
msgstr "Người dùng"

#: common/models.py:1256
msgid "Price break quantity"
msgstr "Số lượng giá phá vỡ"

#: common/models.py:1263 company/serializers.py:567 order/models.py:1632
#: order/models.py:2734
msgid "Price"
msgstr "Giá"

#: common/models.py:1264
msgid "Unit price at specified quantity"
msgstr "Đơn vị giá theo số lượng cụ thể"

#: common/models.py:1315 common/models.py:1500
msgid "Endpoint"
msgstr "Đầu mối"

#: common/models.py:1316
msgid "Endpoint at which this webhook is received"
msgstr "Đầu mối tại điểm webhook được nhận"

#: common/models.py:1326
msgid "Name for this webhook"
msgstr "Tên của webhook này"

#: common/models.py:1330 common/models.py:2156 common/models.py:2263
#: company/models.py:164 company/models.py:799 machine/models.py:39
#: part/models.py:1246 plugin/models.py:68 stock/api.py:591 users/models.py:201
#: users/models.py:556 users/serializers.py:333
msgid "Active"
msgstr "Hoạt động"

#: common/models.py:1330
msgid "Is this webhook active"
msgstr "Webhook có hoạt động không"

#: common/models.py:1346 users/models.py:178
msgid "Token"
msgstr "Chữ ký số"

#: common/models.py:1347
msgid "Token for access"
msgstr "Chữ ký số để truy cập"

#: common/models.py:1355
msgid "Secret"
msgstr "Bí mật"

#: common/models.py:1356
msgid "Shared secret for HMAC"
msgstr "Mã bí mật dùng chung cho HMAC"

#: common/models.py:1464
msgid "Message ID"
msgstr "Mã Tin nhắn"

#: common/models.py:1465
msgid "Unique identifier for this message"
msgstr "Định danh duy nhất cho tin nhắn này"

#: common/models.py:1473
msgid "Host"
msgstr "Máy chủ"

#: common/models.py:1474
msgid "Host from which this message was received"
msgstr "Mãy chủ từ tin nhắn này đã được nhận"

#: common/models.py:1482
msgid "Header"
msgstr "Đầu mục"

#: common/models.py:1483
msgid "Header of this message"
msgstr "Đầu mục tin nhắn"

#: common/models.py:1490
msgid "Body"
msgstr "Thân"

#: common/models.py:1491
msgid "Body of this message"
msgstr "Thân tin nhắn này"

#: common/models.py:1501
msgid "Endpoint on which this message was received"
msgstr "Đầu mối của tin nhắn này đã nhận được"

#: common/models.py:1506
msgid "Worked on"
msgstr "Làm việc vào"

#: common/models.py:1507
msgid "Was the work on this message finished?"
msgstr "Công việc trong tin nhắn này đã kết thúc?"

#: common/models.py:1633
msgid "Id"
msgstr "Mã"

#: common/models.py:1635 part/serializers.py:281
msgid "Title"
msgstr "Tiêu đề"

#: common/models.py:1637 common/models.py:1898 company/models.py:149
#: company/models.py:445 company/models.py:512 company/models.py:816
#: order/models.py:444 order/models.py:1585 order/models.py:2056
#: part/models.py:1113
#: report/templates/report/inventree_build_order_report.html:164
msgid "Link"
msgstr "Liên kết"

#: common/models.py:1639
msgid "Published"
msgstr "Đã công bố"

#: common/models.py:1641
msgid "Author"
msgstr "Tác giả"

#: common/models.py:1643
msgid "Summary"
msgstr "Tóm tắt"

#: common/models.py:1646
msgid "Read"
msgstr "Đọc"

#: common/models.py:1646
msgid "Was this news item read?"
msgstr "Tin này đã được đọc?"

#: common/models.py:1663 company/models.py:160 part/models.py:1124
#: report/templates/report/inventree_bill_of_materials_report.html:126
#: report/templates/report/inventree_bill_of_materials_report.html:148
#: report/templates/report/inventree_return_order_report.html:35
msgid "Image"
msgstr "Hình ảnh"

#: common/models.py:1663
msgid "Image file"
msgstr "Tệp ảnh"

#: common/models.py:1675
msgid "Target model type for this image"
msgstr ""

#: common/models.py:1679
msgid "Target model ID for this image"
msgstr ""

#: common/models.py:1701
msgid "Custom Unit"
msgstr ""

#: common/models.py:1719
msgid "Unit symbol must be unique"
msgstr ""

#: common/models.py:1734
msgid "Unit name must be a valid identifier"
msgstr "Tên đơn vị phải là một định danh hợp lệ"

#: common/models.py:1753
msgid "Unit name"
msgstr "Tên đơn vị"

#: common/models.py:1760
msgid "Symbol"
msgstr "Biểu tượng"

#: common/models.py:1761
msgid "Optional unit symbol"
msgstr "Biểu tượng đơn vị tùy chọn"

#: common/models.py:1767
msgid "Definition"
msgstr "Định nghĩa"

#: common/models.py:1768
msgid "Unit definition"
msgstr "Định nghĩa đơn vị"

#: common/models.py:1826 common/models.py:1889 stock/models.py:2827
#: stock/serializers.py:258
msgid "Attachment"
msgstr "Đính kèm"

#: common/models.py:1843
msgid "Missing file"
msgstr "Tập tin bị thiếu"

#: common/models.py:1844
msgid "Missing external link"
msgstr "Thiếu liên kết bên ngoài"

#: common/models.py:1881
msgid "Model type"
msgstr ""

#: common/models.py:1882
msgid "Target model type for image"
msgstr ""

#: common/models.py:1890
msgid "Select file to attach"
msgstr "Chọn file đính kèm"

#: common/models.py:1906
msgid "Comment"
msgstr "Bình luận"

#: common/models.py:1907
msgid "Attachment comment"
msgstr ""

#: common/models.py:1923
msgid "Upload date"
msgstr ""

#: common/models.py:1924
msgid "Date the file was uploaded"
msgstr ""

#: common/models.py:1928
msgid "File size"
msgstr ""

#: common/models.py:1928
msgid "File size in bytes"
msgstr ""

#: common/models.py:1966 common/serializers.py:637
msgid "Invalid model type specified for attachment"
msgstr ""

#: common/models.py:1987
msgid "Custom State"
msgstr ""

#: common/models.py:1988
msgid "Custom States"
msgstr ""

#: common/models.py:1993
msgid "Reference Status Set"
msgstr ""

#: common/models.py:1994
msgid "Status set that is extended with this custom state"
msgstr ""

#: common/models.py:1998 generic/states/serializers.py:18
msgid "Logical Key"
msgstr ""

#: common/models.py:2000
msgid "State logical key that is equal to this custom state in business logic"
msgstr ""

#: common/models.py:2005 common/models.py:2244 company/models.py:600
#: report/templates/report/inventree_test_report.html:104 stock/models.py:2819
msgid "Value"
msgstr "Giá trị"

#: common/models.py:2006
msgid "Numerical value that will be saved in the models database"
msgstr ""

#: common/models.py:2012
msgid "Name of the state"
msgstr ""

#: common/models.py:2021 common/models.py:2250 generic/states/serializers.py:22
#: part/serializers.py:283
msgid "Label"
msgstr ""

#: common/models.py:2022
msgid "Label that will be displayed in the frontend"
msgstr ""

#: common/models.py:2029 generic/states/serializers.py:24
msgid "Color"
msgstr ""

#: common/models.py:2030
msgid "Color that will be displayed in the frontend"
msgstr ""

#: common/models.py:2038 part/serializers.py:285
msgid "Model"
msgstr ""

#: common/models.py:2039
msgid "Model this state is associated with"
msgstr ""

#: common/models.py:2054
msgid "Model must be selected"
msgstr ""

#: common/models.py:2057
msgid "Key must be selected"
msgstr ""

#: common/models.py:2060
msgid "Logical key must be selected"
msgstr ""

#: common/models.py:2064
msgid "Key must be different from logical key"
msgstr ""

#: common/models.py:2071
msgid "Valid reference status class must be provided"
msgstr ""

#: common/models.py:2077
msgid "Key must be different from the logical keys of the reference status"
msgstr ""

#: common/models.py:2084
msgid "Logical key must be in the logical keys of the reference status"
msgstr ""

#: common/models.py:2091
msgid "Name must be different from the names of the reference status"
msgstr ""

#: common/models.py:2131 common/models.py:2238 part/models.py:3876
msgid "Selection List"
msgstr ""

#: common/models.py:2132
msgid "Selection Lists"
msgstr ""

#: common/models.py:2137
msgid "Name of the selection list"
msgstr ""

#: common/models.py:2144
msgid "Description of the selection list"
msgstr ""

#: common/models.py:2150 part/models.py:1251
msgid "Locked"
msgstr ""

#: common/models.py:2151
msgid "Is this selection list locked?"
msgstr ""

#: common/models.py:2157
msgid "Can this selection list be used?"
msgstr ""

#: common/models.py:2165
msgid "Source Plugin"
msgstr ""

#: common/models.py:2166
msgid "Plugin which provides the selection list"
msgstr ""

#: common/models.py:2171
msgid "Source String"
msgstr ""

#: common/models.py:2172
msgid "Optional string identifying the source used for this list"
msgstr ""

#: common/models.py:2181
msgid "Default Entry"
msgstr ""

#: common/models.py:2182
msgid "Default entry for this selection list"
msgstr ""

#: common/models.py:2187
msgid "Created"
msgstr "Đã tạo"

#: common/models.py:2188
msgid "Date and time that the selection list was created"
msgstr ""

#: common/models.py:2193
msgid "Last Updated"
msgstr "Cập nhật lần cuối"

#: common/models.py:2194
msgid "Date and time that the selection list was last updated"
msgstr ""

#: common/models.py:2228
msgid "Selection List Entry"
msgstr ""

#: common/models.py:2229
msgid "Selection List Entries"
msgstr ""

#: common/models.py:2239
msgid "Selection list to which this entry belongs"
msgstr ""

#: common/models.py:2245
msgid "Value of the selection list entry"
msgstr ""

#: common/models.py:2251
msgid "Label for the selection list entry"
msgstr ""

#: common/models.py:2257
msgid "Description of the selection list entry"
msgstr ""

#: common/models.py:2264
msgid "Is this selection list entry active?"
msgstr ""

#: common/models.py:2282
msgid "Barcode Scan"
msgstr ""

#: common/models.py:2286 importer/models.py:523 part/models.py:4041
msgid "Data"
msgstr "Dữ liệu"

#: common/models.py:2287
msgid "Barcode data"
msgstr ""

#: common/models.py:2298
msgid "User who scanned the barcode"
msgstr ""

#: common/models.py:2303 importer/models.py:66
msgid "Timestamp"
msgstr ""

#: common/models.py:2304
msgid "Date and time of the barcode scan"
msgstr ""

#: common/models.py:2310
msgid "URL endpoint which processed the barcode"
msgstr ""

#: common/models.py:2317 order/models.py:1622 plugin/serializers.py:93
msgid "Context"
msgstr "Ngữ cảnh"

#: common/models.py:2318
msgid "Context data for the barcode scan"
msgstr ""

#: common/models.py:2325
msgid "Response"
msgstr ""

#: common/models.py:2326
msgid "Response data from the barcode scan"
msgstr ""

#: common/models.py:2332 report/templates/report/inventree_test_report.html:103
#: stock/models.py:2813
msgid "Result"
msgstr "Kết quả"

#: common/models.py:2333
msgid "Was the barcode scan successful?"
msgstr ""

#: common/notifications.py:332
#, python-brace-format
msgid "New {verbose_name}"
msgstr "Mới {verbose_name}"

#: common/notifications.py:334
msgid "A new order has been created and assigned to you"
msgstr "Một đơn đặt hàng mới đã được tạo và phân công cho bạn"

#: common/notifications.py:340
#, python-brace-format
msgid "{verbose_name} canceled"
msgstr "{verbose_name} đã bị hủy"

#: common/notifications.py:342
msgid "A order that is assigned to you was canceled"
msgstr "Một đơn đặt từng được phân công cho bạn đã bị hủy bỏ"

#: common/notifications.py:348 common/notifications.py:355 order/api.py:559
msgid "Items Received"
msgstr "Mục đã nhận"

#: common/notifications.py:350
msgid "Items have been received against a purchase order"
msgstr "Hàng đã được nhận theo đơn đặt mua"

#: common/notifications.py:357
msgid "Items have been received against a return order"
msgstr "Hàng đã nhận theo đơn hàng trả lại"

#: common/notifications.py:505
msgid "Error raised by plugin"
msgstr "Lỗi được thông báo bởi phần mở rộng"

#: common/serializers.py:451
msgid "Is Running"
msgstr "Đang chạy"

#: common/serializers.py:457
msgid "Pending Tasks"
msgstr "Công việc chờ xử lý"

#: common/serializers.py:463
msgid "Scheduled Tasks"
msgstr "Tác vụ theo lịch"

#: common/serializers.py:469
msgid "Failed Tasks"
msgstr "Tác vụ thất bại"

#: common/serializers.py:484
msgid "Task ID"
msgstr "ID tác vụ"

#: common/serializers.py:484
msgid "Unique task ID"
msgstr "ID tác vụ duy nhất"

#: common/serializers.py:486
msgid "Lock"
msgstr "Khoá"

#: common/serializers.py:486
msgid "Lock time"
msgstr "Thời gian khóa"

#: common/serializers.py:488
msgid "Task name"
msgstr "Tên công việc"

#: common/serializers.py:490
msgid "Function"
msgstr "Chức năng"

#: common/serializers.py:490
msgid "Function name"
msgstr "Tên chức năng"

#: common/serializers.py:492
msgid "Arguments"
msgstr "Đối số"

#: common/serializers.py:492
msgid "Task arguments"
msgstr "Đối số công việc"

#: common/serializers.py:495
msgid "Keyword Arguments"
msgstr "Đối số từ khóa"

#: common/serializers.py:495
msgid "Task keyword arguments"
msgstr "Đối số từ khóa công việc"

#: common/serializers.py:605
msgid "Filename"
msgstr "Tên tập tin"

#: common/serializers.py:612 importer/models.py:86 report/api.py:41
#: report/models.py:277 report/serializers.py:53
msgid "Model Type"
msgstr ""

#: common/serializers.py:640
msgid "User does not have permission to create or edit attachments for this model"
msgstr ""

#: common/serializers.py:684 common/serializers.py:787
msgid "Selection list is locked"
msgstr ""

#: common/setting/system.py:97
msgid "No group"
msgstr "Không có nhóm"

#: common/setting/system.py:156
msgid "Site URL is locked by configuration"
msgstr "URL trang web đã bị khóa bởi cấu hình"

#: common/setting/system.py:167
msgid "Restart required"
msgstr "Cần khởi động lại"

#: common/setting/system.py:168
msgid "A setting has been changed which requires a server restart"
msgstr "Một thiết lập đã bị thay đổi yêu cầu khởi động lại máy chủ"

#: common/setting/system.py:174
msgid "Pending migrations"
msgstr "Chuyển dữ liệu chờ xử lý"

#: common/setting/system.py:175
msgid "Number of pending database migrations"
msgstr "Số đợt nâng cấp cơ sở dữ liệu chờ xử lý"

#: common/setting/system.py:180
msgid "Instance ID"
msgstr ""

#: common/setting/system.py:181
msgid "Unique identifier for this InvenTree instance"
msgstr ""

#: common/setting/system.py:186
msgid "Announce ID"
msgstr ""

#: common/setting/system.py:188
msgid "Announce the instance ID of the server in the server status info (unauthenticated)"
msgstr ""

#: common/setting/system.py:194
msgid "Server Instance Name"
msgstr "Tên thực thể máy chủ"

#: common/setting/system.py:196
msgid "String descriptor for the server instance"
msgstr "Mô tả chuỗi cho thực thể máy chủ"

#: common/setting/system.py:200
msgid "Use instance name"
msgstr "Sử dụng tên thực thể"

#: common/setting/system.py:201
msgid "Use the instance name in the title-bar"
msgstr "Sử dụng tên thực thể trên thanh tiêu đề"

#: common/setting/system.py:206
msgid "Restrict showing `about`"
msgstr "Cấm hiển thị `giới thiệu`"

#: common/setting/system.py:207
msgid "Show the `about` modal only to superusers"
msgstr "Chỉ hiển thị cửa sổ `giới thiệu` với siêu người dùng"

#: common/setting/system.py:212 company/models.py:108 company/models.py:109
msgid "Company name"
msgstr "Tên công ty"

#: common/setting/system.py:213
msgid "Internal company name"
msgstr "Tên công ty nội bộ"

#: common/setting/system.py:217
msgid "Base URL"
msgstr "URL cơ sở"

#: common/setting/system.py:218
msgid "Base URL for server instance"
msgstr "URL cơ sở cho thực thể máy chủ"

#: common/setting/system.py:224
msgid "Default Currency"
msgstr "Tiền tệ mặc định"

#: common/setting/system.py:225
msgid "Select base currency for pricing calculations"
msgstr "Chọn tiền tệ chính khi tính giá"

#: common/setting/system.py:231
msgid "Supported Currencies"
msgstr ""

#: common/setting/system.py:232
msgid "List of supported currency codes"
msgstr ""

#: common/setting/system.py:238
msgid "Currency Update Interval"
msgstr "Tần suất cập nhật tiền tệ"

#: common/setting/system.py:239
msgid "How often to update exchange rates (set to zero to disable)"
msgstr "Mức độ thường xuyên để cập nhật tỉ giá hối đoái (điền 0 để tắt)"

#: common/setting/system.py:241 common/setting/system.py:281
#: common/setting/system.py:294 common/setting/system.py:302
#: common/setting/system.py:309 common/setting/system.py:318
#: common/setting/system.py:567 common/setting/system.py:587
#: common/setting/system.py:684 common/setting/system.py:1068
msgid "days"
msgstr "ngày"

#: common/setting/system.py:245
msgid "Currency Update Plugin"
msgstr "Phần mở rộng cập nhật tiền tệ"

#: common/setting/system.py:246
msgid "Currency update plugin to use"
msgstr "Phần mở rộng cập nhật tiền tệ được sử dụng"

#: common/setting/system.py:251
msgid "Download from URL"
msgstr "Tải về từ URL"

#: common/setting/system.py:252
msgid "Allow download of remote images and files from external URL"
msgstr "Cho phép tải ảnh và tệp tin từ xa theo URL bên ngoài"

#: common/setting/system.py:257
msgid "Download Size Limit"
msgstr "Giới hạn kích thước tải xuống"

#: common/setting/system.py:258
msgid "Maximum allowable download size for remote image"
msgstr "Kích thước tải xuống tối đa với hình ảnh từ xa"

#: common/setting/system.py:264
msgid "User-agent used to download from URL"
msgstr "User-agent được dùng để tải xuống theo URL"

#: common/setting/system.py:266
msgid "Allow to override the user-agent used to download images and files from external URL (leave blank for the default)"
msgstr "Cho phép ghi đè user-agent được dùng để tải về hình ảnh và tệp tin từ xa theo URL bên ngoài (để trống nghĩa là dùng mặc định)"

#: common/setting/system.py:271
msgid "Strict URL Validation"
msgstr ""

#: common/setting/system.py:272
msgid "Require schema specification when validating URLs"
msgstr ""

#: common/setting/system.py:277
msgid "Update Check Interval"
msgstr "Thời gian kiểm tra bản cập nhật"

#: common/setting/system.py:278
msgid "How often to check for updates (set to zero to disable)"
msgstr "Mức độ thường xuyên để kiểm tra bản cập nhật (điền 0 để tắt)"

#: common/setting/system.py:284
msgid "Automatic Backup"
msgstr "Sao lưu tự động"

#: common/setting/system.py:285
msgid "Enable automatic backup of database and media files"
msgstr "Bật tính năng sao lưu tự động cơ sở dữ liệu và tệp tin đa phương tiện"

#: common/setting/system.py:290
msgid "Auto Backup Interval"
msgstr "Khoảng thời gian sao lưu tự động"

#: common/setting/system.py:291
msgid "Specify number of days between automated backup events"
msgstr "Xác định số ngày giữa các kỳ sao lưu tự động"

#: common/setting/system.py:297
msgid "Task Deletion Interval"
msgstr "Khoảng thời gian xóa tác vụ"

#: common/setting/system.py:299
msgid "Background task results will be deleted after specified number of days"
msgstr "Kết quả tác vụ chạy ngầm sẽ bị xóa sau số ngày được chỉ định"

#: common/setting/system.py:306
msgid "Error Log Deletion Interval"
msgstr "Khoảng thời gian xóa nhật ký lỗi"

#: common/setting/system.py:307
msgid "Error logs will be deleted after specified number of days"
msgstr "Nhật ký lỗi sẽ bị xóa sau số ngày được chỉ định"

#: common/setting/system.py:313
msgid "Notification Deletion Interval"
msgstr "Khoảng thời gian xóa thông báo"

#: common/setting/system.py:315
msgid "User notifications will be deleted after specified number of days"
msgstr "Thông báo sẽ bị xóa sau số ngày được chỉ định"

#: common/setting/system.py:322
msgid "Barcode Support"
msgstr "Hỗ trợ mã vạch"

#: common/setting/system.py:323
msgid "Enable barcode scanner support in the web interface"
msgstr "Bật hỗ trợ máy quét mã vạch trong giao diện web"

#: common/setting/system.py:328
msgid "Store Barcode Results"
msgstr ""

#: common/setting/system.py:329
msgid "Store barcode scan results in the database"
msgstr ""

#: common/setting/system.py:334
msgid "Barcode Scans Maximum Count"
msgstr ""

#: common/setting/system.py:335
msgid "Maximum number of barcode scan results to store"
msgstr ""

#: common/setting/system.py:340
msgid "Barcode Input Delay"
msgstr "Độ trễ quét mã vạch"

#: common/setting/system.py:341
msgid "Barcode input processing delay time"
msgstr "Thời gian trễ xử lý đầu đọc mã vạch"

#: common/setting/system.py:347
msgid "Barcode Webcam Support"
msgstr "Hỗ trợ mã vạch qua webcam"

#: common/setting/system.py:348
msgid "Allow barcode scanning via webcam in browser"
msgstr "Cho phép quét mã vạch qua webcam bên trong trình duyệt"

#: common/setting/system.py:353
msgid "Barcode Show Data"
msgstr ""

#: common/setting/system.py:354
msgid "Display barcode data in browser as text"
msgstr ""

#: common/setting/system.py:359
msgid "Barcode Generation Plugin"
msgstr ""

#: common/setting/system.py:360
msgid "Plugin to use for internal barcode data generation"
msgstr ""

#: common/setting/system.py:365
msgid "Part Revisions"
msgstr "Phiên bản Sản phẩm"

#: common/setting/system.py:366
msgid "Enable revision field for Part"
msgstr "Bật trường phiên bản cho sản phẩm"

#: common/setting/system.py:371
msgid "Assembly Revision Only"
msgstr ""

#: common/setting/system.py:372
msgid "Only allow revisions for assembly parts"
msgstr ""

#: common/setting/system.py:377
msgid "Allow Deletion from Assembly"
msgstr ""

#: common/setting/system.py:378
msgid "Allow deletion of parts which are used in an assembly"
msgstr ""

#: common/setting/system.py:383
msgid "IPN Regex"
msgstr "Mẫu IPN"

#: common/setting/system.py:384
msgid "Regular expression pattern for matching Part IPN"
msgstr "Mẫu dùng nhanh phổ biến dành cho tìm IPN sản phẩm"

#: common/setting/system.py:387
msgid "Allow Duplicate IPN"
msgstr "Cho phép trùng IPN"

#: common/setting/system.py:388
msgid "Allow multiple parts to share the same IPN"
msgstr "Cho phép nhiều sản phẩm dùng IPN giống nhau"

#: common/setting/system.py:393
msgid "Allow Editing IPN"
msgstr "Cho phép sửa IPN"

#: common/setting/system.py:394
msgid "Allow changing the IPN value while editing a part"
msgstr "Cho phép đổi giá trị IPN khi sửa một sản phẩm"

#: common/setting/system.py:399
msgid "Copy Part BOM Data"
msgstr "Sao chép dữ liệu BOM của sản phẩm"

#: common/setting/system.py:400
msgid "Copy BOM data by default when duplicating a part"
msgstr "Sao chép dữ liệu BOM mặc định khi nhân bản 1 sản phẩm"

#: common/setting/system.py:405
msgid "Copy Part Parameter Data"
msgstr "Sao chép dữ liệu tham số sản phẩm"

#: common/setting/system.py:406
msgid "Copy parameter data by default when duplicating a part"
msgstr "Sao chép dữ liệu tham số mặc định khi nhân bản 1 sản phẩm"

#: common/setting/system.py:411
msgid "Copy Part Test Data"
msgstr "Chép thông tin kiểm thử sản phẩm"

#: common/setting/system.py:412
msgid "Copy test data by default when duplicating a part"
msgstr "Sao chép dữ liệu kiểm thử mặc định khi nhân bản 1 sản phẩm"

#: common/setting/system.py:417
msgid "Copy Category Parameter Templates"
msgstr "Sao chéo mẫu tham số danh mục"

#: common/setting/system.py:418
msgid "Copy category parameter templates when creating a part"
msgstr "Sao chéo mẫu tham số danh mục khi tạo 1 sản phẩm"

#: common/setting/system.py:423 part/models.py:4035 report/models.py:357
#: report/models.py:563 report/serializers.py:90 report/serializers.py:131
#: stock/serializers.py:247
msgid "Template"
msgstr "Mẫu"

#: common/setting/system.py:424
msgid "Parts are templates by default"
msgstr "Sản phẩm là mẫu bởi mặc định"

#: common/setting/system.py:430
msgid "Parts can be assembled from other components by default"
msgstr "Sản phẩm có thể lắp giáp từ thành phần khác theo mặc định"

#: common/setting/system.py:435 part/models.py:1217 part/serializers.py:1677
#: part/serializers.py:1683
msgid "Component"
msgstr "Thành phần"

#: common/setting/system.py:436
msgid "Parts can be used as sub-components by default"
msgstr "Sản phẩm có thể được sử dụng mặc định như thành phần phụ"

#: common/setting/system.py:441 part/models.py:1235
msgid "Purchaseable"
msgstr "Có thể mua"

#: common/setting/system.py:442
msgid "Parts are purchaseable by default"
msgstr "Sản phẩm mặc định có thể mua được"

#: common/setting/system.py:447 part/models.py:1241 stock/api.py:592
msgid "Salable"
msgstr "Có thể bán"

#: common/setting/system.py:448
msgid "Parts are salable by default"
msgstr "Sản phẩm mặc định có thể bán được"

#: common/setting/system.py:454
msgid "Parts are trackable by default"
msgstr "Sản phẩm mặc định có thể theo dõi được"

#: common/setting/system.py:459 part/models.py:1257
msgid "Virtual"
msgstr "Ảo"

#: common/setting/system.py:460
msgid "Parts are virtual by default"
msgstr "Sản phẩm mặc định là số hóa"

#: common/setting/system.py:465
msgid "Show Import in Views"
msgstr "Hiển thị Nhập liệu trong khung xem"

#: common/setting/system.py:466
msgid "Display the import wizard in some part views"
msgstr "Hiển thị đồ thuật nhập dữ liệu trong một số khung nhìn sản phẩm"

#: common/setting/system.py:471
msgid "Show related parts"
msgstr "Hiển thị sản phẩm liên quan"

#: common/setting/system.py:472
msgid "Display related parts for a part"
msgstr "Hiện sản phẩm liên quan cho 1 sản phẩm"

#: common/setting/system.py:477
msgid "Initial Stock Data"
msgstr "Số liệu tồn kho ban đầu"

#: common/setting/system.py:478
msgid "Allow creation of initial stock when adding a new part"
msgstr "Cho phép tạo tồn kho ban đầu khi thêm 1 sản phẩm mới"

#: common/setting/system.py:483
msgid "Initial Supplier Data"
msgstr "Dữ liệu nhà cung cấp ban đầu"

#: common/setting/system.py:485
msgid "Allow creation of initial supplier data when adding a new part"
msgstr "Cho phép tạo dữ liệu nhà cung cấp ban đầu khi thêm 1 sản phẩm mới"

#: common/setting/system.py:491
msgid "Part Name Display Format"
msgstr "Định dạng tên sản phẩm hiển thị"

#: common/setting/system.py:492
msgid "Format to display the part name"
msgstr "Định dạng để hiển thị tên sản phẩm"

#: common/setting/system.py:498
msgid "Part Category Default Icon"
msgstr "Biểu tượng mặc định của danh mục sản phẩm"

#: common/setting/system.py:499
msgid "Part category default icon (empty means no icon)"
msgstr "Biểu tượng mặc định của danh mục sản phẩm (để trống nghĩa là không có biểu tượng)"

#: common/setting/system.py:504
msgid "Enforce Parameter Units"
msgstr "Bắt buộc đơn vị tham số"

#: common/setting/system.py:506
msgid "If units are provided, parameter values must match the specified units"
msgstr "Nếu đơn vị được cung cấp, giá trị tham số phải phù hợp với các đơn vị xác định"

#: common/setting/system.py:512
msgid "Minimum Pricing Decimal Places"
msgstr "Vị trí phần thập phân giá bán tối thiểu"

#: common/setting/system.py:514
msgid "Minimum number of decimal places to display when rendering pricing data"
msgstr "Số vị trí thập phân tối thiểu cần hiển thị khi tạo dữ liệu giá"

#: common/setting/system.py:525
msgid "Maximum Pricing Decimal Places"
msgstr "Vị trí phần thập phân giá bán tối đa"

#: common/setting/system.py:527
msgid "Maximum number of decimal places to display when rendering pricing data"
msgstr "Số vị trí thập phân tối đa cần hiển thị khi tạo dữ liệu giá"

#: common/setting/system.py:538
msgid "Use Supplier Pricing"
msgstr "Sử dụng giá bán nhà cung cấp"

#: common/setting/system.py:540
msgid "Include supplier price breaks in overall pricing calculations"
msgstr "Bao gồm giá phá vỡ cả nhà cung cấp trong tính toán giá tổng thể"

#: common/setting/system.py:546
msgid "Purchase History Override"
msgstr "Ghi đè lịch sử mua hàng"

#: common/setting/system.py:548
msgid "Historical purchase order pricing overrides supplier price breaks"
msgstr "Giá đơn hàng đặt mua trước đó ghi đè giá phá vỡ của nhà cung cấp"

#: common/setting/system.py:554
msgid "Use Stock Item Pricing"
msgstr "Sử dụng giá hàng hóa trong kho"

#: common/setting/system.py:556
msgid "Use pricing from manually entered stock data for pricing calculations"
msgstr "Dùng giá bán từ dữ liệu kho nhập vào thủ công đối với bộ tính toán giá bán"

#: common/setting/system.py:562
msgid "Stock Item Pricing Age"
msgstr "Tuổi giá kho hàng"

#: common/setting/system.py:564
msgid "Exclude stock items older than this number of days from pricing calculations"
msgstr "Loại trừ hàng hóa trong kho cũ hơn số ngày ngày từ bảng tính giá bán"

#: common/setting/system.py:571
msgid "Use Variant Pricing"
msgstr "Sử dụng giá biến thể"

#: common/setting/system.py:572
msgid "Include variant pricing in overall pricing calculations"
msgstr "Bao gồm giá biến thể trong bộ tính toán giá tổng thể"

#: common/setting/system.py:577
msgid "Active Variants Only"
msgstr "Chỉ các biến thể hoạt động"

#: common/setting/system.py:579
msgid "Only use active variant parts for calculating variant pricing"
msgstr "Chỉ sử dụng sản phẩm biến thể hoạt động để tính toán giá bán biến thể"

#: common/setting/system.py:585
msgid "Pricing Rebuild Interval"
msgstr "Tần suất tạo lại giá"

#: common/setting/system.py:586
msgid "Number of days before part pricing is automatically updated"
msgstr "Số ngày trước khi giá sản phẩm được tự động cập nhật"

#: common/setting/system.py:592
msgid "Internal Prices"
msgstr "Giá nội bộ"

#: common/setting/system.py:593
msgid "Enable internal prices for parts"
msgstr "Bật giá nội bộ cho sản phẩm"

#: common/setting/system.py:598
msgid "Internal Price Override"
msgstr "Ghi đè giá nội bộ"

#: common/setting/system.py:600
msgid "If available, internal prices override price range calculations"
msgstr "Nếu khả dụng, giá nội bộ ghi đè tính toán khoảng giá"

#: common/setting/system.py:606
msgid "Enable label printing"
msgstr "Bật in tem nhãn"

#: common/setting/system.py:607
msgid "Enable label printing from the web interface"
msgstr "Bật chức năng in tem nhãn từ giao diện web"

#: common/setting/system.py:612
msgid "Label Image DPI"
msgstr "DPI hỉnh ảnh tem nhãn"

#: common/setting/system.py:614
msgid "DPI resolution when generating image files to supply to label printing plugins"
msgstr "Độ phân giải DPI khi tạo tệp hình ảnh để cung cấp cho plugin in ấn tem nhãn"

#: common/setting/system.py:620
msgid "Enable Reports"
msgstr "Bật báo cáo"

#: common/setting/system.py:621
msgid "Enable generation of reports"
msgstr "Cho phép tạo báo cáo"

#: common/setting/system.py:626
msgid "Debug Mode"
msgstr "Chế độ gỡ lỗi"

#: common/setting/system.py:627
msgid "Generate reports in debug mode (HTML output)"
msgstr "Tạo báo cáo trong chế độ gỡ lỗi (đầu ra HTML)"

#: common/setting/system.py:632
msgid "Log Report Errors"
msgstr ""

#: common/setting/system.py:633
msgid "Log errors which occur when generating reports"
msgstr ""

#: common/setting/system.py:638 plugin/builtin/labels/label_sheet.py:29
#: report/models.py:365
msgid "Page Size"
msgstr "Khổ giấy"

#: common/setting/system.py:639
msgid "Default page size for PDF reports"
msgstr "Kích thước trang mặc định cho báo cáo PDF"

#: common/setting/system.py:644
msgid "Globally Unique Serials"
msgstr "Sê ri toàn cục duy nhất"

#: common/setting/system.py:645
msgid "Serial numbers for stock items must be globally unique"
msgstr "Số sê ri cho hàng trong kho phải là duy nhất trong toàn hệ thống"

#: common/setting/system.py:650
msgid "Autofill Serial Numbers"
msgstr "Tự động điền số sê ri"

#: common/setting/system.py:651
msgid "Autofill serial numbers in forms"
msgstr "Tự động điền số sê ri vào biểu mẫu"

#: common/setting/system.py:656
msgid "Delete Depleted Stock"
msgstr "Xóa kho đã hết hàng"

#: common/setting/system.py:657
msgid "Determines default behavior when a stock item is depleted"
msgstr ""

#: common/setting/system.py:662
msgid "Batch Code Template"
msgstr "Mẫu sinh mã theo lô"

#: common/setting/system.py:663
msgid "Template for generating default batch codes for stock items"
msgstr "Mẫu tạo mã theo lô mặc định cho hàng trong kho"

#: common/setting/system.py:667
msgid "Stock Expiry"
msgstr "Quá hạn trong kho"

#: common/setting/system.py:668
msgid "Enable stock expiry functionality"
msgstr "Bật chức năng quá hạn tồn kho"

#: common/setting/system.py:673
msgid "Sell Expired Stock"
msgstr "Bán kho quá hạn"

#: common/setting/system.py:674
msgid "Allow sale of expired stock"
msgstr "Cho phép bán hàng kho quá hạn"

#: common/setting/system.py:679
msgid "Stock Stale Time"
msgstr "Thời gian hàng cũ trong kho"

#: common/setting/system.py:681
msgid "Number of days stock items are considered stale before expiring"
msgstr "Số ngày hàng trong kho được xác định là cũ trước khi quá hạn"

#: common/setting/system.py:688
msgid "Build Expired Stock"
msgstr "Dựng kho quá hạn"

#: common/setting/system.py:689
msgid "Allow building with expired stock"
msgstr "Cho phép xây dựng với kho hàng quá hạn"

#: common/setting/system.py:694
msgid "Stock Ownership Control"
msgstr "Kiểm soát sở hữu kho"

#: common/setting/system.py:695
msgid "Enable ownership control over stock locations and items"
msgstr "Bật chức năng kiểm soát sở hữu kho với địa điểm và hàng trong kho"

#: common/setting/system.py:700
msgid "Stock Location Default Icon"
msgstr "Biểu tượng địa điểm kho mặc định"

#: common/setting/system.py:701
msgid "Stock location default icon (empty means no icon)"
msgstr "Biểu tượng địa điểm kho hàng mặc định (trống nghĩa là không có biểu tượng)"

#: common/setting/system.py:706
msgid "Show Installed Stock Items"
msgstr "Hiển thị hàng hóa đã lắp đặt"

#: common/setting/system.py:707
msgid "Display installed stock items in stock tables"
msgstr "Hiển thị hàng trong kho đã được lắp đặt trên bảng kho"

#: common/setting/system.py:712
msgid "Check BOM when installing items"
msgstr ""

#: common/setting/system.py:714
msgid "Installed stock items must exist in the BOM for the parent part"
msgstr ""

#: common/setting/system.py:720
msgid "Allow Out of Stock Transfer"
msgstr ""

#: common/setting/system.py:722
msgid "Allow stock items which are not in stock to be transferred between stock locations"
msgstr ""

#: common/setting/system.py:728
msgid "Build Order Reference Pattern"
msgstr "Mã tham chiếu đơn đặt bản dựng"

#: common/setting/system.py:729
msgid "Required pattern for generating Build Order reference field"
msgstr "Mẫu bắt buộc cho để trường tham chiếu đơn đặt bản dựng"

#: common/setting/system.py:734 common/setting/system.py:788
#: common/setting/system.py:808 common/setting/system.py:844
msgid "Require Responsible Owner"
msgstr ""

#: common/setting/system.py:735 common/setting/system.py:789
#: common/setting/system.py:809 common/setting/system.py:845
msgid "A responsible owner must be assigned to each order"
msgstr ""

#: common/setting/system.py:740
msgid "Require Active Part"
msgstr ""

#: common/setting/system.py:741
msgid "Prevent build order creation for inactive parts"
msgstr ""

#: common/setting/system.py:746
msgid "Require Locked Part"
msgstr ""

#: common/setting/system.py:747
msgid "Prevent build order creation for unlocked parts"
msgstr ""

#: common/setting/system.py:752
msgid "Require Valid BOM"
msgstr ""

#: common/setting/system.py:753
msgid "Prevent build order creation unless BOM has been validated"
msgstr ""

#: common/setting/system.py:758
msgid "Require Closed Child Orders"
msgstr ""

#: common/setting/system.py:760
msgid "Prevent build order completion until all child orders are closed"
msgstr ""

#: common/setting/system.py:766
msgid "Block Until Tests Pass"
msgstr ""

#: common/setting/system.py:768
msgid "Prevent build outputs from being completed until all required tests pass"
msgstr ""

#: common/setting/system.py:774
msgid "Enable Return Orders"
msgstr "Bật đơn hàng trả lại"

#: common/setting/system.py:775
msgid "Enable return order functionality in the user interface"
msgstr "Bật chức năng đơn hàng trả lại trong giao diện người dùng"

#: common/setting/system.py:780
msgid "Return Order Reference Pattern"
msgstr "Mẫu tham chiếu đơn hàng trả lại"

#: common/setting/system.py:782
msgid "Required pattern for generating Return Order reference field"
msgstr ""

#: common/setting/system.py:794
msgid "Edit Completed Return Orders"
msgstr "Sửa đơn hàng trả lại đã hoàn thành"

#: common/setting/system.py:796
msgid "Allow editing of return orders after they have been completed"
msgstr "Cho phép sửa đơn hàng trả lại sau khi đã hoàn thành rồi"

#: common/setting/system.py:802
msgid "Sales Order Reference Pattern"
msgstr "Mẫu tham chiếu đơn đặt hàng"

#: common/setting/system.py:803
msgid "Required pattern for generating Sales Order reference field"
msgstr "Mẫu bắt buộc để tạo trường tham chiếu đơn đặt hàng"

#: common/setting/system.py:814
msgid "Sales Order Default Shipment"
msgstr "Vận chuyển mặc định đơn đặt hàng"

#: common/setting/system.py:815
msgid "Enable creation of default shipment with sales orders"
msgstr "Cho phép tạo vận chuyển mặc định với đơn đặt hàng"

#: common/setting/system.py:820
msgid "Edit Completed Sales Orders"
msgstr "Sửa đơn đặt hàng đã hoàn thành"

#: common/setting/system.py:822
msgid "Allow editing of sales orders after they have been shipped or completed"
msgstr "Cho phép sửa đơn đặt hàng sau khi đã vận chuyển hoặc hoàn thành"

#: common/setting/system.py:828
msgid "Mark Shipped Orders as Complete"
msgstr ""

#: common/setting/system.py:830
msgid "Sales orders marked as shipped will automatically be completed, bypassing the \"shipped\" status"
msgstr ""

#: common/setting/system.py:836
msgid "Purchase Order Reference Pattern"
msgstr "Mẫu tham chiếu đơn đặt mua"

#: common/setting/system.py:838
msgid "Required pattern for generating Purchase Order reference field"
msgstr "Mẫu bắt buộc cho để trường tham chiếu đơn đặt mua"

#: common/setting/system.py:850
msgid "Edit Completed Purchase Orders"
msgstr "Sửa đơn đặt mua đã hoàn thành"

#: common/setting/system.py:852
msgid "Allow editing of purchase orders after they have been shipped or completed"
msgstr "Cho phép sửa đơn đặt mua sau khi đã vận chuyển hoặc hoàn thành"

#: common/setting/system.py:858
msgid "Convert Currency"
msgstr ""

#: common/setting/system.py:859
msgid "Convert item value to base currency when receiving stock"
msgstr ""

#: common/setting/system.py:864
msgid "Auto Complete Purchase Orders"
msgstr "Tự động hoàn thành đơn đặt mua"

#: common/setting/system.py:866
msgid "Automatically mark purchase orders as complete when all line items are received"
msgstr ""

#: common/setting/system.py:873
msgid "Enable password forgot"
msgstr "Bật quên mật khẩu"

#: common/setting/system.py:874
msgid "Enable password forgot function on the login pages"
msgstr "Bật chức năng quên mật khẩu trong trang đăng nhập"

#: common/setting/system.py:879
msgid "Enable registration"
msgstr "Bật đăng ký"

#: common/setting/system.py:880
msgid "Enable self-registration for users on the login pages"
msgstr "Cho phép người dùng tự đăng ký tại trang đăng nhập"

#: common/setting/system.py:885
msgid "Enable SSO"
msgstr "Bật SSO"

#: common/setting/system.py:886
msgid "Enable SSO on the login pages"
msgstr "Cho phép SSO tại trang đăng nhập"

#: common/setting/system.py:891
msgid "Enable SSO registration"
msgstr "Bật đăng ký SSO"

#: common/setting/system.py:893
msgid "Enable self-registration via SSO for users on the login pages"
msgstr "Cho phép người dùng tự đăng ký SSO tại trang đăng nhập"

#: common/setting/system.py:899
msgid "Enable SSO group sync"
msgstr ""

#: common/setting/system.py:901
msgid "Enable synchronizing InvenTree groups with groups provided by the IdP"
msgstr ""

#: common/setting/system.py:907
msgid "SSO group key"
msgstr ""

#: common/setting/system.py:908
msgid "The name of the groups claim attribute provided by the IdP"
msgstr ""

#: common/setting/system.py:913
msgid "SSO group map"
msgstr ""

#: common/setting/system.py:915
msgid "A mapping from SSO groups to local InvenTree groups. If the local group does not exist, it will be created."
msgstr ""

#: common/setting/system.py:921
msgid "Remove groups outside of SSO"
msgstr ""

#: common/setting/system.py:923
msgid "Whether groups assigned to the user should be removed if they are not backend by the IdP. Disabling this setting might cause security issues"
msgstr ""

#: common/setting/system.py:929
msgid "Email required"
msgstr "Yêu cầu email"

#: common/setting/system.py:930
msgid "Require user to supply mail on signup"
msgstr "Yêu cầu người dùng cung cấp email để đăng ký"

#: common/setting/system.py:935
msgid "Auto-fill SSO users"
msgstr "Người dùng tự động điền SSO"

#: common/setting/system.py:936
msgid "Automatically fill out user-details from SSO account-data"
msgstr "Tự động điền thông tin chi tiết từ dữ liệu tài khoản SSO"

#: common/setting/system.py:941
msgid "Mail twice"
msgstr "Thư 2 lần"

#: common/setting/system.py:942
msgid "On signup ask users twice for their mail"
msgstr "Khi đăng ký sẽ hỏi người dùng hai lần thư điện tử của họ"

#: common/setting/system.py:947
msgid "Password twice"
msgstr "Mật khẩu 2 lần"

#: common/setting/system.py:948
msgid "On signup ask users twice for their password"
msgstr "Khi đăng ký sẽ hỏi người dùng hai lần mật khẩu của họ"

#: common/setting/system.py:953
msgid "Allowed domains"
msgstr "Các tên miền được phép"

#: common/setting/system.py:955
msgid "Restrict signup to certain domains (comma-separated, starting with @)"
msgstr "Cấm đăng ký với 1 số tên miền cụ thể (dấu phẩy ngăn cách, bắt đầu với dấu @)"

#: common/setting/system.py:961
msgid "Group on signup"
msgstr "Nhóm khi đăng ký"

#: common/setting/system.py:963
msgid "Group to which new users are assigned on registration. If SSO group sync is enabled, this group is only set if no group can be assigned from the IdP."
msgstr ""

#: common/setting/system.py:969
msgid "Enforce MFA"
msgstr "Bắt buộc MFA"

#: common/setting/system.py:970
msgid "Users must use multifactor security."
msgstr "Người dùng phải sử dụng bảo mật đa nhân tố."

#: common/setting/system.py:975
msgid "Check plugins on startup"
msgstr "Kiểm tra phần mở rộng khi khởi động"

#: common/setting/system.py:977
msgid "Check that all plugins are installed on startup - enable in container environments"
msgstr "Kiểm tra toàn bộ phần mở rộng đã được cài đặt khi khởi dộng - bật trong môi trường ảo hóa"

#: common/setting/system.py:984
msgid "Check for plugin updates"
msgstr "Kiểm tra cập nhật plugin"

#: common/setting/system.py:985
msgid "Enable periodic checks for updates to installed plugins"
msgstr ""

#: common/setting/system.py:991
msgid "Enable URL integration"
msgstr "Bật tích hợp URL"

#: common/setting/system.py:992
msgid "Enable plugins to add URL routes"
msgstr "Bật phần mở rộng để thêm định tuyến URL"

#: common/setting/system.py:998
msgid "Enable navigation integration"
msgstr "Bật tích hợp điều hướng"

#: common/setting/system.py:999
msgid "Enable plugins to integrate into navigation"
msgstr "Bật phần mở rộng để tích hợp thanh định hướng"

#: common/setting/system.py:1005
msgid "Enable app integration"
msgstr "Bật tích hợp ứng dụng"

#: common/setting/system.py:1006
msgid "Enable plugins to add apps"
msgstr "Bật phần mở rộng để thêm ứng dụng"

#: common/setting/system.py:1012
msgid "Enable schedule integration"
msgstr "Cho phép tích hợp lập lịch"

#: common/setting/system.py:1013
msgid "Enable plugins to run scheduled tasks"
msgstr "Bật phẩn mở rộng để chạy các tác vụ theo lịch"

#: common/setting/system.py:1019
msgid "Enable event integration"
msgstr "Bật tích hợp nguồn cấp sự kiện"

#: common/setting/system.py:1020
msgid "Enable plugins to respond to internal events"
msgstr "Bật phần mở rộng để trả lời sự kiện bên trong"

#: common/setting/system.py:1026
msgid "Enable interface integration"
msgstr ""

#: common/setting/system.py:1027
msgid "Enable plugins to integrate into the user interface"
msgstr ""

#: common/setting/system.py:1033
msgid "Enable project codes"
msgstr ""

#: common/setting/system.py:1034
msgid "Enable project codes for tracking projects"
msgstr ""

#: common/setting/system.py:1039
msgid "Stocktake Functionality"
msgstr "Chức năng kiểm kê"

#: common/setting/system.py:1041
msgid "Enable stocktake functionality for recording stock levels and calculating stock value"
msgstr "Bật chức năng kiểm kê theo mức độ ghi nhận kho và tính toán giá trị kho"

#: common/setting/system.py:1047
msgid "Exclude External Locations"
msgstr "Ngoại trừ vị trí bên ngoài"

#: common/setting/system.py:1049
msgid "Exclude stock items in external locations from stocktake calculations"
msgstr "Loại trừ hàng trong kho thuộc địa điểm bên ngoài ra khỏi tính toán kiểm kê"

#: common/setting/system.py:1055
msgid "Automatic Stocktake Period"
msgstr "Giai đoạn kiểm kê tự động"

#: common/setting/system.py:1057
msgid "Number of days between automatic stocktake recording (set to zero to disable)"
msgstr "Số ngày giữa ghi chép kiểm kê tự động (đặt không để tắt)"

#: common/setting/system.py:1063
msgid "Report Deletion Interval"
msgstr "Khoảng thời gian xóa báo cáo"

#: common/setting/system.py:1065
msgid "Stocktake reports will be deleted after specified number of days"
msgstr "Báo cáo kiểm kê sẽ bị xóa sau số ngày xác định"

#: common/setting/system.py:1072
msgid "Display Users full names"
msgstr "Hiển thị tên đầy đủ của người dùng"

#: common/setting/system.py:1073
msgid "Display Users full names instead of usernames"
msgstr "Hiển thị tên đầy đủ thay vì tên đăng nhập"

#: common/setting/system.py:1078
msgid "Display User Profiles"
msgstr ""

#: common/setting/system.py:1079
msgid "Display Users Profiles on their profile page"
msgstr ""

#: common/setting/system.py:1084
msgid "Enable Test Station Data"
msgstr ""

#: common/setting/system.py:1085
msgid "Enable test station data collection for test results"
msgstr ""

#: common/setting/system.py:1090
msgid "Create Template on Upload"
msgstr ""

#: common/setting/system.py:1092
msgid "Create a new test template when uploading test data which does not match an existing template"
msgstr ""

#: common/setting/user.py:23
msgid "Inline label display"
msgstr "Hiển thị nhãn cùng dòng"

#: common/setting/user.py:25
msgid "Display PDF labels in the browser, instead of downloading as a file"
msgstr "Hiển thị nhãn PDF trong trình duyệt, thay vì tải về dạng tệp tin"

#: common/setting/user.py:31
msgid "Default label printer"
msgstr "Máy in tem nhãn mặc định"

#: common/setting/user.py:32
msgid "Configure which label printer should be selected by default"
msgstr "Cấu hình máy in tem nhãn nào được chọn mặc định"

#: common/setting/user.py:37
msgid "Inline report display"
msgstr "Hiển thị báo cáo cùng hàng"

#: common/setting/user.py:39
msgid "Display PDF reports in the browser, instead of downloading as a file"
msgstr "Hiện báo cáo PDF trong trình duyệt, thay vì tải về dạng tệp tin"

#: common/setting/user.py:45
msgid "Search Parts"
msgstr "Tìm sản phẩm"

#: common/setting/user.py:46
msgid "Display parts in search preview window"
msgstr "Hiện hàng hóa trong cửa sổ xem trước tìm kiếm"

#: common/setting/user.py:51
msgid "Search Supplier Parts"
msgstr "Tìm sản phẩm nhà cung cấp"

#: common/setting/user.py:52
msgid "Display supplier parts in search preview window"
msgstr "Hiện sản phẩm nhà cung cấp trong cửa sổ xem trước tìm kiếm"

#: common/setting/user.py:57
msgid "Search Manufacturer Parts"
msgstr "Tìm sản phẩm nhà sản xuất"

#: common/setting/user.py:58
msgid "Display manufacturer parts in search preview window"
msgstr "Hiện sản phẩm nhà sản xuất trong cửa sổ xem trước tìm kiếm"

#: common/setting/user.py:63
msgid "Hide Inactive Parts"
msgstr "Ẩn sản phẩm ngừng hoạt động"

#: common/setting/user.py:64
msgid "Excluded inactive parts from search preview window"
msgstr "Loại trừ sản phẩm ngưng hoạt động trong cửa sổ xem trước tìm kiếm"

#: common/setting/user.py:69
msgid "Search Categories"
msgstr "Tìm kiếm danh mục"

#: common/setting/user.py:70
msgid "Display part categories in search preview window"
msgstr "Hiện danh mục sản phẩm trong cửa sổ xem trước tìm kiếm"

#: common/setting/user.py:75
msgid "Search Stock"
msgstr "Tìm kiếm kho"

#: common/setting/user.py:76
msgid "Display stock items in search preview window"
msgstr "Hiện hàng hóa ở kho trong cửa sổ xem trước tìm kiếm"

#: common/setting/user.py:81
msgid "Hide Unavailable Stock Items"
msgstr "Ẩn hàng hóa trong kho không có sẵn"

#: common/setting/user.py:83
msgid "Exclude stock items which are not available from the search preview window"
msgstr "Không bao gồm hàng hóa trong kho mà không sẵn sàng từ màn hình xem trước tìm kiếm"

#: common/setting/user.py:89
msgid "Search Locations"
msgstr "Tìm kiếm vị trí"

#: common/setting/user.py:90
msgid "Display stock locations in search preview window"
msgstr "Hiện vị trí kho hàng trong cửa sổ xem trước tìm kiếm"

#: common/setting/user.py:95
msgid "Search Companies"
msgstr "Tìm kiếm công ty"

#: common/setting/user.py:96
msgid "Display companies in search preview window"
msgstr "Hiện công ty trong cửa sổ xem trước tìm kiếm"

#: common/setting/user.py:101
msgid "Search Build Orders"
msgstr "Tìm kiếm đặt hàng xây dựng"

#: common/setting/user.py:102
msgid "Display build orders in search preview window"
msgstr "Hiện đơn đặt xây dựng trong cửa sổ xem trước tìm kiếm"

#: common/setting/user.py:107
msgid "Search Purchase Orders"
msgstr "Tìm kiếm đơn đặt mua"

#: common/setting/user.py:108
msgid "Display purchase orders in search preview window"
msgstr "Hiện đơn đặt mua trong cửa sổ xem trước tìm kiếm"

#: common/setting/user.py:113
msgid "Exclude Inactive Purchase Orders"
msgstr "Loại trừ đơn đặt mua không hoạt động"

#: common/setting/user.py:114
msgid "Exclude inactive purchase orders from search preview window"
msgstr "Loại trừ đơn đặt mua không hoạt động ra khỏi cửa sổ xem trước tìm kiếm"

#: common/setting/user.py:119
msgid "Search Sales Orders"
msgstr "Tìm đơn đặt hàng người mua"

#: common/setting/user.py:120
msgid "Display sales orders in search preview window"
msgstr "Hiện đơn đặt hàng người mua trong cửa sổ xem trước tìm kiếm"

#: common/setting/user.py:125
msgid "Exclude Inactive Sales Orders"
msgstr "Loại trừ đơn đặt hàng người mua không hoạt động"

#: common/setting/user.py:126
msgid "Exclude inactive sales orders from search preview window"
msgstr "Không bao gồm đơn đặt hàng người mua không hoạt động trong cửa sổ xem trước tìm kiếm"

#: common/setting/user.py:131
msgid "Search Sales Order Shipments"
msgstr ""

#: common/setting/user.py:132
msgid "Display sales order shipments in search preview window"
msgstr ""

#: common/setting/user.py:137
msgid "Search Return Orders"
msgstr "Tìm kiếm đơn hàng trả lại"

#: common/setting/user.py:138
msgid "Display return orders in search preview window"
msgstr "Hiện đơn hàng trả lại trong cửa sổ xem trước tìm kiếm"

#: common/setting/user.py:143
msgid "Exclude Inactive Return Orders"
msgstr "Loại trừ đơn hàng trả lại không hoạt động"

#: common/setting/user.py:144
msgid "Exclude inactive return orders from search preview window"
msgstr "Không bao gồm đơn hàng trả lại không hoạt động trong cửa sổ xem trước tìm kiếm"

#: common/setting/user.py:149
msgid "Search Preview Results"
msgstr "Kết quả xem trước tìm kiếm"

#: common/setting/user.py:151
msgid "Number of results to show in each section of the search preview window"
msgstr "Số kết quả cần hiển thị trong từng phần của cửa sổ xem trước tìm kiếm"

#: common/setting/user.py:157
msgid "Regex Search"
msgstr "Tìm kiếm biểu thức"

#: common/setting/user.py:158
msgid "Enable regular expressions in search queries"
msgstr "Bật tìm kiếm biểu thức chính quy trong câu truy vấn tìm kiếm"

#: common/setting/user.py:163
msgid "Whole Word Search"
msgstr "Tìm phù hợp toàn bộ chữ"

#: common/setting/user.py:164
msgid "Search queries return results for whole word matches"
msgstr "Truy vấn tìm trả về kết quả phù hợp toàn bộ chữ"

#: common/setting/user.py:169
msgid "Search Notes"
msgstr ""

#: common/setting/user.py:171
msgid "Search queries return results for matches from the item's notes"
msgstr ""

#: common/setting/user.py:177
msgid "Show Quantity in Forms"
msgstr "Hiện số lượng trong biểu mẫu"

#: common/setting/user.py:178
msgid "Display available part quantity in some forms"
msgstr "Hiển thị số lượng sản phẩm có sẵn trong một số biểu mẫu"

#: common/setting/user.py:183
msgid "Escape Key Closes Forms"
msgstr "Phím escape để đóng mẫu biểu"

#: common/setting/user.py:184
msgid "Use the escape key to close modal forms"
msgstr "Sử dụng phím escape để đóng mẫu biểu hộp thoại"

#: common/setting/user.py:189
msgid "Fixed Navbar"
msgstr "Cố định điều hướng"

#: common/setting/user.py:190
msgid "The navbar position is fixed to the top of the screen"
msgstr "Vị trí thành điều hướng là cố định trên cùng màn hình"

#: common/setting/user.py:195
msgid "Navigation Icons"
msgstr ""

#: common/setting/user.py:196
msgid "Display icons in the navigation bar"
msgstr ""

#: common/setting/user.py:201
msgid "Date Format"
msgstr "Định dạng ngày"

#: common/setting/user.py:202
msgid "Preferred format for displaying dates"
msgstr "Định dạng ưa chuộng khi hiển thị ngày"

#: common/setting/user.py:215
msgid "Part Scheduling"
msgstr "Lập lịch sản phẩm"

#: common/setting/user.py:216
msgid "Display part scheduling information"
msgstr "Hiển thị thông tin lịch sản phẩm"

#: common/setting/user.py:221
msgid "Part Stocktake"
msgstr "Kiểm kê sản phẩm"

#: common/setting/user.py:223
msgid "Display part stocktake information (if stocktake functionality is enabled)"
msgstr "Hiển thị thông tin kiểm kê sản phẩm (nếu chức năng kiểm kê được bật)"

#: common/setting/user.py:229
msgid "Table String Length"
msgstr "Độ dài chuỗi trong bảng"

#: common/setting/user.py:230
msgid "Maximum length limit for strings displayed in table views"
msgstr "Giới hạn độ dài tối đa cho chuỗi hiển thị trong kiểu xem bảng biểu"

#: common/setting/user.py:235
msgid "Show Last Breadcrumb"
msgstr ""

#: common/setting/user.py:236
msgid "Show the current page in breadcrumbs"
msgstr ""

#: common/setting/user.py:241
msgid "Receive error reports"
msgstr "Nhận báo cáo lỗi"

#: common/setting/user.py:242
msgid "Receive notifications for system errors"
msgstr "Nhận thông báo khi có lỗi hệ thống"

#: common/setting/user.py:247
msgid "Last used printing machines"
msgstr ""

#: common/setting/user.py:248
msgid "Save the last used printing machines for a user"
msgstr ""

#: common/validators.py:35
msgid "No attachment model type provided"
msgstr ""

#: common/validators.py:41
msgid "Invalid attachment model type"
msgstr ""

#: common/validators.py:82
msgid "Minimum places cannot be greater than maximum places"
msgstr ""

#: common/validators.py:94
msgid "Maximum places cannot be less than minimum places"
msgstr ""

#: common/validators.py:105
msgid "An empty domain is not allowed."
msgstr "Tên miền rỗng là không được phép."

#: common/validators.py:107
#, python-brace-format
msgid "Invalid domain name: {domain}"
msgstr "Tên miền không hợp lệ: {domain}"

#: common/validators.py:123
msgid "Value must be uppercase"
msgstr ""

#: common/validators.py:129
msgid "Value must be a valid variable identifier"
msgstr ""

#: company/api.py:141
msgid "Part is Active"
msgstr ""

#: company/api.py:145
msgid "Manufacturer is Active"
msgstr ""

#: company/api.py:278
msgid "Supplier Part is Active"
msgstr ""

#: company/api.py:282
msgid "Internal Part is Active"
msgstr ""

#: company/api.py:287
msgid "Supplier is Active"
msgstr ""

#: company/api.py:299 company/models.py:498 company/serializers.py:444
#: part/serializers.py:577
msgid "Manufacturer"
msgstr "Nhà sản xuất"

#: company/api.py:306 company/models.py:97 company/models.py:370
#: stock/api.py:849
msgid "Company"
msgstr "Doanh nghiêp"

#: company/api.py:316
msgid "Has Stock"
msgstr ""

#: company/models.py:98
msgid "Companies"
msgstr "Doanh nghiệp"

#: company/models.py:114
msgid "Company description"
msgstr "Mô tả công ty"

#: company/models.py:115
msgid "Description of the company"
msgstr "Mô tả của công ty"

#: company/models.py:121
msgid "Website"
msgstr "Trang web"

#: company/models.py:122
msgid "Company website URL"
msgstr "URL trang web của công ty"

#: company/models.py:128
msgid "Phone number"
msgstr "Số điện thoại"

#: company/models.py:130
msgid "Contact phone number"
msgstr "Số điện thoại liên hệ"

#: company/models.py:137
msgid "Contact email address"
msgstr "Địa chỉ email liên hệ"

#: company/models.py:142 company/models.py:274 order/models.py:500
#: users/models.py:563
msgid "Contact"
msgstr "Liên hệ"

#: company/models.py:144
msgid "Point of contact"
msgstr "Đầu mối liên hệ"

#: company/models.py:150
msgid "Link to external company information"
msgstr "Liên kết đến thông tin công ty ngoài"

#: company/models.py:164
msgid "Is this company active?"
msgstr ""

#: company/models.py:169
msgid "Is customer"
msgstr ""

#: company/models.py:170
msgid "Do you sell items to this company?"
msgstr "Bạn có bán hàng cho công ty này?"

#: company/models.py:175
msgid "Is supplier"
msgstr ""

#: company/models.py:176
msgid "Do you purchase items from this company?"
msgstr "Bạn có mua hàng từ công ty này?"

#: company/models.py:181
msgid "Is manufacturer"
msgstr ""

#: company/models.py:182
msgid "Does this company manufacture parts?"
msgstr "Công ty này có sản xuất sản phẩm?"

#: company/models.py:190
msgid "Default currency used for this company"
msgstr "Tiền tệ mặc định dùng cho công ty này"

#: company/models.py:313 order/models.py:510
msgid "Address"
msgstr "Địa chỉ"

#: company/models.py:314
msgid "Addresses"
msgstr "Địa chỉ"

#: company/models.py:371
msgid "Select company"
msgstr "Chọn doanh nghiệp"

#: company/models.py:376
msgid "Address title"
msgstr "Tiêu đề địa chỉ"

#: company/models.py:377
msgid "Title describing the address entry"
msgstr "Tiêu đề mô tả mục địa chỉ"

#: company/models.py:383
msgid "Primary address"
msgstr "Địa chỉ chính"

#: company/models.py:384
msgid "Set as primary address"
msgstr "Đặt làm địa chỉ chính"

#: company/models.py:389
msgid "Line 1"
msgstr "Dòng 1"

#: company/models.py:390
msgid "Address line 1"
msgstr "Địa chỉ dòng 1"

#: company/models.py:396
msgid "Line 2"
msgstr "Dòng 2"

#: company/models.py:397
msgid "Address line 2"
msgstr "Địa chỉ dòng 2"

#: company/models.py:403 company/models.py:404
msgid "Postal code"
msgstr "Mã bưu chính"

#: company/models.py:410
msgid "City/Region"
msgstr "Thành phố/Vùng"

#: company/models.py:411
msgid "Postal code city/region"
msgstr "Mã bưu chính thành phố/vùng"

#: company/models.py:417
msgid "State/Province"
msgstr "Bang/Tỉnh"

#: company/models.py:418
msgid "State or province"
msgstr "Bang hay tỉnh"

#: company/models.py:424
msgid "Country"
msgstr "Quốc gia"

#: company/models.py:425
msgid "Address country"
msgstr "Địa chỉ quốc gia"

#: company/models.py:431
msgid "Courier shipping notes"
msgstr "Ghi chú vận chuyển"

#: company/models.py:432
msgid "Notes for shipping courier"
msgstr "Ghi chú dành cho chuyển phát nhanh"

#: company/models.py:438
msgid "Internal shipping notes"
msgstr "Ghi chú nội bọ chuyển phát nhanh"

#: company/models.py:439
msgid "Shipping notes for internal use"
msgstr "Ghi chú nội bộ sử dụng cho chuyển phát nhanh"

#: company/models.py:446
msgid "Link to address information (external)"
msgstr "Liên kết thông tin địa chỉ (bên ngoài)"

#: company/models.py:470 company/models.py:587 company/models.py:809
#: company/serializers.py:458
msgid "Manufacturer Part"
msgstr "Sản phẩm nhà sản xuất"

#: company/models.py:487 company/models.py:777 stock/models.py:948
#: stock/serializers.py:480
msgid "Base Part"
msgstr "Sản phẩm cơ bản"

#: company/models.py:489 company/models.py:779
msgid "Select part"
msgstr "Chọn sản phẩm"

#: company/models.py:499
msgid "Select manufacturer"
msgstr "Chọn nhà sản xuất"

#: company/models.py:505 company/serializers.py:466 order/serializers.py:665
#: part/serializers.py:587
msgid "MPN"
msgstr ""

#: company/models.py:506 stock/serializers.py:625
msgid "Manufacturer Part Number"
msgstr "Mã số nhà sản xuất"

#: company/models.py:513
msgid "URL for external manufacturer part link"
msgstr "URL cho liên kết sản phẩm của nhà sản xuất bên ngoài"

#: company/models.py:522
msgid "Manufacturer part description"
msgstr "Mô tả sản phẩm của nhà sản xuất"

#: company/models.py:575
msgid "Manufacturer Part Parameter"
msgstr ""

#: company/models.py:594
msgid "Parameter name"
msgstr "Tên tham số"

#: company/models.py:601
msgid "Parameter value"
msgstr "Giá trị tham số"

#: company/models.py:608 part/models.py:1204 part/models.py:3844
msgid "Units"
msgstr "Đơn vị"

#: company/models.py:609
msgid "Parameter units"
msgstr "Đơn vị tham số"

#: company/models.py:717
msgid "Pack units must be compatible with the base part units"
msgstr "Đơn vị đóng gói phải tương thích với đơn vị sản phẩm cơ bản"

#: company/models.py:724
msgid "Pack units must be greater than zero"
msgstr "Đơn vị đóng gói phải lớn hơn không"

#: company/models.py:738
msgid "Linked manufacturer part must reference the same base part"
msgstr "Sản phẩm nhà sản xuất đã liên kết phải tham chiếu với sản phẩm cơ bản tương tự"

#: company/models.py:787 company/serializers.py:436 company/serializers.py:454
#: order/models.py:626 part/serializers.py:561
#: plugin/builtin/suppliers/digikey.py:25 plugin/builtin/suppliers/lcsc.py:26
#: plugin/builtin/suppliers/mouser.py:24 plugin/builtin/suppliers/tme.py:26
#: stock/api.py:516 templates/email/overdue_purchase_order.html:16
msgid "Supplier"
msgstr "Nhà cung cấp"

#: company/models.py:788
msgid "Select supplier"
msgstr "Chọn nhà cung cấp"

#: company/models.py:794 part/serializers.py:572
msgid "Supplier stock keeping unit"
msgstr "Đơn vị quản lý kho nhà cung cấp"

#: company/models.py:800
msgid "Is this supplier part active?"
msgstr ""

#: company/models.py:810
msgid "Select manufacturer part"
msgstr "Chọn sản phẩm của nhà sản xuất"

#: company/models.py:817
msgid "URL for external supplier part link"
msgstr "URL cho liên kết sản phẩm của nhà cung cấp bên ngoài"

#: company/models.py:826
msgid "Supplier part description"
msgstr "Mô tả sản phẩm nhà cung cấp"

#: company/models.py:833 order/serializers.py:819 order/serializers.py:2027
#: part/models.py:4356 part/models.py:4697
#: report/templates/report/inventree_bill_of_materials_report.html:140
#: report/templates/report/inventree_purchase_order_report.html:32
#: report/templates/report/inventree_return_order_report.html:27
#: report/templates/report/inventree_sales_order_report.html:32
#: report/templates/report/inventree_stock_location_report.html:105
#: stock/serializers.py:838
msgid "Note"
msgstr "Ghi chú"

#: company/models.py:842 part/models.py:2182
msgid "base cost"
msgstr "chi phí cơ sở"

#: company/models.py:843 part/models.py:2183
msgid "Minimum charge (e.g. stocking fee)"
msgstr "Thu phí tối thiểu (vd: phí kho bãi)"

#: company/models.py:850 order/serializers.py:811 stock/models.py:979
#: stock/serializers.py:1666
msgid "Packaging"
msgstr "Đóng gói"

#: company/models.py:851
msgid "Part packaging"
msgstr "Đóng gói sản phẩm"

#: company/models.py:856
msgid "Pack Quantity"
msgstr "Số lượng gói"

#: company/models.py:858
msgid "Total quantity supplied in a single pack. Leave empty for single items."
msgstr "Tổng số lượng được cung cấp trong một gói đơn. Để trống cho các hàng hóa riêng lẻ."

#: company/models.py:877 part/models.py:2189
msgid "multiple"
msgstr "nhiều"

#: company/models.py:878
msgid "Order multiple"
msgstr "Đặt hàng nhiều"

#: company/models.py:890
msgid "Quantity available from supplier"
msgstr "Số lượng có sẵn từ nhà cung cấp"

#: company/models.py:896
msgid "Availability Updated"
msgstr "Sẵn hàng đã được cập nhật"

#: company/models.py:897
msgid "Date of last update of availability data"
msgstr "Ngày cập nhật cuối thông tin tồn kho"

#: company/models.py:1025
msgid "Supplier Price Break"
msgstr ""

#: company/serializers.py:166
msgid "Return the string representation for the primary address. This property exists for backwards compatibility."
msgstr ""

#: company/serializers.py:185
msgid "Default currency used for this supplier"
msgstr "Tiền tệ mặc định được sử dụng cho nhà cung cấp này"

#: company/serializers.py:221
msgid "Company Name"
msgstr ""

#: company/serializers.py:420 part/serializers.py:940 stock/serializers.py:498
msgid "In Stock"
msgstr "Còn hàng"

#: data_exporter/mixins.py:324 data_exporter/mixins.py:393
msgid "Error occurred during data export"
msgstr ""

#: data_exporter/mixins.py:375
msgid "Data export plugin returned incorrect data format"
msgstr ""

#: data_exporter/serializers.py:74
msgid "Export Format"
msgstr ""

#: data_exporter/serializers.py:75
msgid "Select export file format"
msgstr ""

#: data_exporter/serializers.py:82
msgid "Export Plugin"
msgstr ""

#: data_exporter/serializers.py:83
msgid "Select export plugin"
msgstr ""

#: generic/states/fields.py:146
msgid "Additional status information for this item"
msgstr ""

#: generic/states/fields.py:160
msgid "Custom status key"
msgstr ""

#: generic/states/serializers.py:16 plugin/models.py:45 users/models.py:119
msgid "Key"
msgstr "Khóa"

#: generic/states/serializers.py:26
msgid "Custom"
msgstr ""

#: generic/states/serializers.py:37
msgid "Class"
msgstr ""

#: generic/states/serializers.py:40
msgid "Values"
msgstr ""

#: generic/states/tests.py:22 order/status_codes.py:13
msgid "Placed"
msgstr "Đã đặt"

#: generic/states/validators.py:21
msgid "Invalid status code"
msgstr ""

#: importer/models.py:70
msgid "Data File"
msgstr "Tập tin dữ liệu"

#: importer/models.py:71
msgid "Data file to import"
msgstr ""

#: importer/models.py:80
msgid "Columns"
msgstr ""

#: importer/models.py:87
msgid "Target model type for this import session"
msgstr ""

#: importer/models.py:93
msgid "Import status"
msgstr ""

#: importer/models.py:103
msgid "Field Defaults"
msgstr ""

#: importer/models.py:110
msgid "Field Overrides"
msgstr ""

#: importer/models.py:117
msgid "Field Filters"
msgstr ""

#: importer/models.py:250
msgid "Some required fields have not been mapped"
msgstr ""

#: importer/models.py:407
msgid "Column is already mapped to a database field"
msgstr ""

#: importer/models.py:412
msgid "Field is already mapped to a data column"
msgstr ""

#: importer/models.py:421
msgid "Column mapping must be linked to a valid import session"
msgstr ""

#: importer/models.py:426
msgid "Column does not exist in the data file"
msgstr ""

#: importer/models.py:433
msgid "Field does not exist in the target model"
msgstr ""

#: importer/models.py:437
msgid "Selected field is read-only"
msgstr ""

#: importer/models.py:442 importer/models.py:513
msgid "Import Session"
msgstr ""

#: importer/models.py:446
msgid "Field"
msgstr ""

#: importer/models.py:448
msgid "Column"
msgstr ""

#: importer/models.py:517
msgid "Row Index"
msgstr ""

#: importer/models.py:520
msgid "Original row data"
msgstr ""

#: importer/models.py:525 machine/models.py:110
msgid "Errors"
msgstr ""

#: importer/models.py:527 part/api.py:864
msgid "Valid"
msgstr "Hợp lệ"

#: importer/operations.py:28 importer/operations.py:49
msgid "Unsupported data file format"
msgstr ""

#: importer/operations.py:40
msgid "Failed to open data file"
msgstr ""

#: importer/operations.py:51
msgid "Invalid data file dimensions"
msgstr ""

#: importer/serializers.py:91
msgid "Invalid field defaults"
msgstr ""

#: importer/serializers.py:104
msgid "Invalid field overrides"
msgstr ""

#: importer/serializers.py:117
msgid "Invalid field filters"
msgstr ""

#: importer/serializers.py:178
msgid "Rows"
msgstr ""

#: importer/serializers.py:179
msgid "List of row IDs to accept"
msgstr ""

#: importer/serializers.py:192
msgid "No rows provided"
msgstr ""

#: importer/serializers.py:196
msgid "Row does not belong to this session"
msgstr ""

#: importer/serializers.py:199
msgid "Row contains invalid data"
msgstr ""

#: importer/serializers.py:202
msgid "Row has already been completed"
msgstr ""

#: importer/status_codes.py:13
msgid "Initializing"
msgstr ""

#: importer/status_codes.py:18
msgid "Mapping Columns"
msgstr ""

#: importer/status_codes.py:21
msgid "Importing Data"
msgstr ""

#: importer/status_codes.py:24
msgid "Processing Data"
msgstr ""

#: importer/validators.py:21
msgid "Data file exceeds maximum size limit"
msgstr ""

#: importer/validators.py:26
msgid "Data file contains no headers"
msgstr ""

#: importer/validators.py:29
msgid "Data file contains too many columns"
msgstr ""

#: importer/validators.py:32
msgid "Data file contains too many rows"
msgstr ""

#: importer/validators.py:53
msgid "Value must be a valid dictionary object"
msgstr ""

#: machine/machine_types/label_printer.py:212
msgid "Copies"
msgstr ""

#: machine/machine_types/label_printer.py:213
msgid "Number of copies to print for each label"
msgstr ""

#: machine/machine_types/label_printer.py:228
msgid "Connected"
msgstr ""

#: machine/machine_types/label_printer.py:229 order/api.py:1679
msgid "Unknown"
msgstr "Không rõ"

#: machine/machine_types/label_printer.py:230
msgid "Printing"
msgstr ""

#: machine/machine_types/label_printer.py:231
msgid "No media"
msgstr ""

#: machine/machine_types/label_printer.py:232
msgid "Paper jam"
msgstr ""

#: machine/machine_types/label_printer.py:233
msgid "Disconnected"
msgstr ""

#: machine/machine_types/label_printer.py:240
msgid "Label Printer"
msgstr ""

#: machine/machine_types/label_printer.py:241
msgid "Directly print labels for various items."
msgstr ""

#: machine/machine_types/label_printer.py:247
msgid "Printer Location"
msgstr ""

#: machine/machine_types/label_printer.py:248
msgid "Scope the printer to a specific location"
msgstr ""

#: machine/models.py:25
msgid "Name of machine"
msgstr ""

#: machine/models.py:29
msgid "Machine Type"
msgstr ""

#: machine/models.py:29
msgid "Type of machine"
msgstr ""

#: machine/models.py:34 machine/models.py:146
msgid "Driver"
msgstr ""

#: machine/models.py:35
msgid "Driver used for the machine"
msgstr ""

#: machine/models.py:39
msgid "Machines can be disabled"
msgstr ""

#: machine/models.py:95
msgid "Driver available"
msgstr ""

#: machine/models.py:100
msgid "No errors"
msgstr ""

#: machine/models.py:105
msgid "Initialized"
msgstr ""

#: machine/models.py:117
msgid "Machine status"
msgstr ""

#: machine/models.py:145
msgid "Machine"
msgstr ""

#: machine/models.py:151
msgid "Machine Config"
msgstr ""

#: machine/models.py:156
msgid "Config type"
msgstr ""

#: order/api.py:118
msgid "Order Reference"
msgstr "Tham chiếu đơn đặt"

#: order/api.py:146 order/api.py:1139
msgid "Outstanding"
msgstr ""

#: order/api.py:162
msgid "Has Project Code"
msgstr ""

#: order/api.py:176 order/models.py:475
msgid "Created By"
msgstr "Tạo bởi"

#: order/api.py:180
msgid "Created Before"
msgstr ""

#: order/api.py:184
msgid "Created After"
msgstr ""

#: order/api.py:188
msgid "Has Start Date"
msgstr ""

#: order/api.py:196
msgid "Start Date Before"
msgstr ""

#: order/api.py:200
msgid "Start Date After"
msgstr ""

#: order/api.py:204
msgid "Has Target Date"
msgstr ""

#: order/api.py:212
msgid "Target Date Before"
msgstr ""

#: order/api.py:216
msgid "Target Date After"
msgstr ""

#: order/api.py:267
msgid "Has Pricing"
msgstr ""

#: order/api.py:320 order/api.py:774 order/api.py:1375
msgid "Completed Before"
msgstr ""

#: order/api.py:324 order/api.py:778 order/api.py:1379
msgid "Completed After"
msgstr ""

#: order/api.py:491 order/api.py:876 order/api.py:1102 order/models.py:1685
#: order/models.py:1803 order/models.py:1854 order/models.py:2002
#: order/models.py:2168 order/models.py:2690 order/models.py:2756
msgid "Order"
msgstr "Đặt hàng"

#: order/api.py:495 order/api.py:914
msgid "Order Complete"
msgstr ""

#: order/api.py:527 order/api.py:531 order/serializers.py:676
msgid "Internal Part"
msgstr "Sản phẩm nội bộ"

#: order/api.py:549
msgid "Order Pending"
msgstr ""

#: order/api.py:899
msgid "Completed"
msgstr "Đã hoàn thành"

#: order/api.py:1155
msgid "Has Shipment"
msgstr ""

#: order/api.py:1673 order/models.py:539 order/models.py:1686
#: order/models.py:1804
#: report/templates/report/inventree_purchase_order_report.html:14
#: stock/serializers.py:128 templates/email/overdue_purchase_order.html:15
msgid "Purchase Order"
msgstr "Đơn hàng"

#: order/api.py:1675 order/models.py:1080 order/models.py:1855
#: order/models.py:2003 order/models.py:2169
#: report/templates/report/inventree_build_order_report.html:135
#: report/templates/report/inventree_sales_order_report.html:14
#: report/templates/report/inventree_sales_order_shipment_report.html:15
#: templates/email/overdue_sales_order.html:15
msgid "Sales Order"
msgstr "Đơn đặt hàng"

#: order/api.py:1677 order/models.py:2340 order/models.py:2691
#: order/models.py:2757
#: report/templates/report/inventree_return_order_report.html:13
#: templates/email/overdue_return_order.html:15
msgid "Return Order"
msgstr "Đơn hàng trả lại"

#: order/models.py:89
#: report/templates/report/inventree_purchase_order_report.html:31
#: report/templates/report/inventree_sales_order_report.html:31
msgid "Total Price"
msgstr "Tổng tiền"

#: order/models.py:90
msgid "Total price for this order"
msgstr "Tổng tiền cho đơn hàng hàng"

#: order/models.py:95 order/serializers.py:75
msgid "Order Currency"
msgstr "Tiền tệ đơn đặt hàng"

#: order/models.py:98 order/serializers.py:76
msgid "Currency for this order (leave blank to use company default)"
msgstr "Tiền tệ cho đơn đặt này (để trống để sử dụng tiền mặc định)"

#: order/models.py:323
msgid "This order is locked and cannot be modified"
msgstr ""

#: order/models.py:370
msgid "Contact does not match selected company"
msgstr "Liên hệ không phù hợp với doanh nghiệp đã chọn"

#: order/models.py:377
msgid "Start date must be before target date"
msgstr ""

#: order/models.py:430
msgid "Order description (optional)"
msgstr "Mô tả đơn đặt (tùy chọn)"

#: order/models.py:439
msgid "Select project code for this order"
msgstr "Mã dự án đã chọn cho đơn đặt hàng này"

#: order/models.py:445 order/models.py:1586 order/models.py:2057
msgid "Link to external page"
msgstr "Liên kết đến trang bên ngoài"

#: order/models.py:452
msgid "Start date"
msgstr ""

#: order/models.py:453
msgid "Scheduled start date for this order"
msgstr ""

#: order/models.py:459 order/models.py:1593 order/serializers.py:269
#: report/templates/report/inventree_build_order_report.html:125
msgid "Target Date"
msgstr "Ngày mục tiêu"

#: order/models.py:461
msgid "Expected date for order delivery. Order will be overdue after this date."
msgstr "Ngày mong muốn giao được hàng. Đơn đặt sẽ quá hạn sau ngày này."

#: order/models.py:481
msgid "Issue Date"
msgstr "Ngày phát hành"

#: order/models.py:482
msgid "Date order was issued"
msgstr "Ngày đặt hàng đã phát hành"

#: order/models.py:490
msgid "User or group responsible for this order"
msgstr "Người dùng hoặc nhóm có trách nhiệm với đơn đặt này"

#: order/models.py:501
msgid "Point of contact for this order"
msgstr "Đầu mối liên hệ của đơn đặt này"

#: order/models.py:511
msgid "Company address for this order"
msgstr "Địa chỉ công ty cho đơn đặt này"

#: order/models.py:602 order/models.py:1141
msgid "Order reference"
msgstr "Mã đặt hàng"

#: order/models.py:611 order/models.py:1165 order/models.py:2428
#: stock/serializers.py:612 stock/serializers.py:1010 users/models.py:544
msgid "Status"
msgstr "Trạng thái"

#: order/models.py:612
msgid "Purchase order status"
msgstr "Trạng thái đơn đặt mua"

#: order/models.py:627
msgid "Company from which the items are being ordered"
msgstr "Doanh nghiệp từ những hàng hóa đang được đặt mua"

#: order/models.py:638
msgid "Supplier Reference"
msgstr "Tham chiếu nhà cung cấp"

#: order/models.py:639
msgid "Supplier order reference code"
msgstr "Mã tham chiếu đơn đặt nhà cung cấp"

#: order/models.py:648
msgid "received by"
msgstr "nhận bởi"

#: order/models.py:655 order/models.py:2443
msgid "Date order was completed"
msgstr "Ngày đặt hàng đã được hoàn thiện"

#: order/models.py:664 order/models.py:1733
msgid "Destination"
msgstr "Đích đến"

#: order/models.py:665 order/models.py:1737
msgid "Destination for received items"
msgstr ""

#: order/models.py:711
msgid "Part supplier must match PO supplier"
msgstr "Nhà cung cấp sản phẩm phải trùng với nhà cung cấp PO"

#: order/models.py:971
msgid "Quantity must be a positive number"
msgstr "Số lượng phải là số dương"

#: order/models.py:1152 order/models.py:2415 stock/models.py:1001
#: stock/models.py:1002 stock/serializers.py:1405
#: templates/email/overdue_return_order.html:16
#: templates/email/overdue_sales_order.html:16
msgid "Customer"
msgstr "Khách hàng"

#: order/models.py:1153
msgid "Company to which the items are being sold"
msgstr "Doanh nghiệp từ những hàng hóa đang được bán"

#: order/models.py:1166
msgid "Sales order status"
msgstr ""

#: order/models.py:1177 order/models.py:2435
msgid "Customer Reference "
msgstr "Tham chiếu khách hàng "

#: order/models.py:1178 order/models.py:2436
msgid "Customer order reference code"
msgstr "Mã tham chiếu đơn đặt của khách hàng"

#: order/models.py:1182 order/models.py:2009
msgid "Shipment Date"
msgstr "Ngày giao hàng"

#: order/models.py:1191
msgid "shipped by"
msgstr "vận chuyển bằng"

#: order/models.py:1230
msgid "Order is already complete"
msgstr ""

#: order/models.py:1233
msgid "Order is already cancelled"
msgstr ""

#: order/models.py:1237
msgid "Only an open order can be marked as complete"
msgstr "Những đơn hàng đang mở thì sẽ được đánh dấu là hoàn thành"

#: order/models.py:1241
msgid "Order cannot be completed as there are incomplete shipments"
msgstr "Đơn hàng không thể hoàn thành được vì vận chuyển chưa xong"

#: order/models.py:1246
msgid "Order cannot be completed as there are incomplete allocations"
msgstr ""

#: order/models.py:1251
msgid "Order cannot be completed as there are incomplete line items"
msgstr "Đơn hàng không thể hoàn thành được vì những khoản riêng chưa xong"

#: order/models.py:1535 order/models.py:1548
msgid "The order is locked and cannot be modified"
msgstr ""

#: order/models.py:1556
msgid "Item quantity"
msgstr "Số lượng mặt hàng"

#: order/models.py:1573
msgid "Line item reference"
msgstr "Tham chiếu khoản riêng"

#: order/models.py:1580
msgid "Line item notes"
msgstr "Ghi chú khoản riêng"

#: order/models.py:1595
msgid "Target date for this line item (leave blank to use the target date from the order)"
msgstr "Ngày mục tiêu cho khoản riêng này (để trống để sử dụng ngày mục tiêu từ đơn đặt)"

#: order/models.py:1616
msgid "Line item description (optional)"
msgstr "Mô tả khoản riêng (tùy chọn)"

#: order/models.py:1623
msgid "Additional context for this line"
msgstr "Ngữ cảnh bổ sung"

#: order/models.py:1633
msgid "Unit price"
msgstr "Đơn giá"

#: order/models.py:1647
msgid "Purchase Order Line Item"
msgstr ""

#: order/models.py:1671
msgid "Supplier part must match supplier"
msgstr "Sản phẩm nhà cung cấp phải phù hợp với nhà cung cung cấp"

#: order/models.py:1705
msgid "Supplier part"
msgstr "Sản phẩm nhà cung cấp"

#: order/models.py:1712
msgid "Received"
msgstr "Đã nhận"

#: order/models.py:1713
msgid "Number of items received"
msgstr "Số mục đã nhận"

#: order/models.py:1721 stock/models.py:1124 stock/serializers.py:679
msgid "Purchase Price"
msgstr "Giá mua"

#: order/models.py:1722
msgid "Unit purchase price"
msgstr "Giá đơn vị mua"

#: order/models.py:1792
msgid "Purchase Order Extra Line"
msgstr ""

#: order/models.py:1821
msgid "Sales Order Line Item"
msgstr ""

#: order/models.py:1842
msgid "Virtual part cannot be assigned to a sales order"
msgstr "Không thể gán sản phẩm ảo vào trong đơn đặt bán hàng"

#: order/models.py:1847
msgid "Only salable parts can be assigned to a sales order"
msgstr "Chỉ có thể gán sản phẩm có thể bán vào đơn đặt bán hàng"

#: order/models.py:1873
msgid "Sale Price"
msgstr "Giá bán"

#: order/models.py:1874
msgid "Unit sale price"
msgstr "Giá bán đơn vị"

#: order/models.py:1883 order/status_codes.py:50
msgid "Shipped"
msgstr "Đã chuyển"

#: order/models.py:1884
msgid "Shipped quantity"
msgstr "Số lượng đã vận chuyển"

#: order/models.py:1978
msgid "Sales Order Shipment"
msgstr ""

#: order/models.py:2010
msgid "Date of shipment"
msgstr "Ngày vận chuyển"

#: order/models.py:2016
msgid "Delivery Date"
msgstr "Ngày giao hàng"

#: order/models.py:2017
msgid "Date of delivery of shipment"
msgstr "Ngày giao hàng của vận chuyển"

#: order/models.py:2025
msgid "Checked By"
msgstr "Kiểm tra bởi"

#: order/models.py:2026
msgid "User who checked this shipment"
msgstr "Người dùng đã kiểm tra vận chuyển này"

#: order/models.py:2033 order/models.py:2265 order/serializers.py:1685
#: order/serializers.py:1809
#: report/templates/report/inventree_sales_order_shipment_report.html:14
msgid "Shipment"
msgstr "Vận chuyển"

#: order/models.py:2034
msgid "Shipment number"
msgstr "Mã vận chuyển"

#: order/models.py:2042
msgid "Tracking Number"
msgstr "Số theo dõi"

#: order/models.py:2043
msgid "Shipment tracking information"
msgstr "Thông tin theo dõi vận chuyển"

#: order/models.py:2050
msgid "Invoice Number"
msgstr "Mã hóa đơn"

#: order/models.py:2051
msgid "Reference number for associated invoice"
msgstr "Số tham chiếu liên kết với hóa đơn"

#: order/models.py:2074
msgid "Shipment has already been sent"
msgstr "Vận đơn đã được gửi đi"

#: order/models.py:2077
msgid "Shipment has no allocated stock items"
msgstr "Vận đơn chưa có hàng hóa được phân bổ"

#: order/models.py:2157
msgid "Sales Order Extra Line"
msgstr ""

#: order/models.py:2186
msgid "Sales Order Allocation"
msgstr ""

#: order/models.py:2209 order/models.py:2211
msgid "Stock item has not been assigned"
msgstr "Hàng trong kho chưa được giao"

#: order/models.py:2218
msgid "Cannot allocate stock item to a line with a different part"
msgstr "Không thể phân bổ hàng hóa vào cùng với dòng với sản phẩm khác"

#: order/models.py:2221
msgid "Cannot allocate stock to a line without a part"
msgstr "Không thể phân bổ hàng hóa vào một dòng mà không có sản phẩm nào"

#: order/models.py:2224
msgid "Allocation quantity cannot exceed stock quantity"
msgstr "Số lượng phân bổ không thể vượt quá số lượng của kho"

#: order/models.py:2243 order/serializers.py:1555
msgid "Quantity must be 1 for serialized stock item"
msgstr "Số lượng phải là 1 cho hàng hóa sêri"

#: order/models.py:2246
msgid "Sales order does not match shipment"
msgstr "Đơn bán hàng không phù hợp với vận đơn"

#: order/models.py:2247 plugin/base/barcodes/api.py:642
msgid "Shipment does not match sales order"
msgstr "Vận đơn không phù hợp với đơn bán hàng"

#: order/models.py:2255
msgid "Line"
msgstr "Dòng"

#: order/models.py:2266
msgid "Sales order shipment reference"
msgstr "Tham chiếu vận đơn của đơn hàng bán"

#: order/models.py:2279 order/models.py:2698
msgid "Item"
msgstr "Hàng hóa"

#: order/models.py:2280
msgid "Select stock item to allocate"
msgstr "Chọn hàng trong kho để phân bổ"

#: order/models.py:2289
msgid "Enter stock allocation quantity"
msgstr "Nhập số lượng phân kho"

#: order/models.py:2404
msgid "Return Order reference"
msgstr "Tham chiếu đơn hàng trả lại"

#: order/models.py:2416
msgid "Company from which items are being returned"
msgstr "Công ty có hàng hóa sẽ được trả lại"

#: order/models.py:2429
msgid "Return order status"
msgstr "Trạng thái đơn hàng trả lại"

#: order/models.py:2656
msgid "Return Order Line Item"
msgstr ""

#: order/models.py:2669
msgid "Stock item must be specified"
msgstr ""

#: order/models.py:2673
msgid "Return quantity exceeds stock quantity"
msgstr ""

#: order/models.py:2678
msgid "Return quantity must be greater than zero"
msgstr ""

#: order/models.py:2683
msgid "Invalid quantity for serialized stock item"
msgstr ""

#: order/models.py:2699
msgid "Select item to return from customer"
msgstr "Chọn hàng hóa để trả lại từ khách hàng"

#: order/models.py:2714
msgid "Received Date"
msgstr "Ngày nhận được"

#: order/models.py:2715
msgid "The date this this return item was received"
msgstr "Ngày mà hàng hóa trả lại đã được nhận"

#: order/models.py:2727
msgid "Outcome"
msgstr "Kết quả"

#: order/models.py:2728
msgid "Outcome for this line item"
msgstr "Kết quả cho hàng hóa dòng này"

#: order/models.py:2735
msgid "Cost associated with return or repair for this line item"
msgstr "Chi phí gắn với hàng trả lại hoặc sửa chữa cho dòng hàng hóa này"

#: order/models.py:2745
msgid "Return Order Extra Line"
msgstr ""

#: order/serializers.py:89
msgid "Order ID"
msgstr ""

#: order/serializers.py:89
msgid "ID of the order to duplicate"
msgstr ""

#: order/serializers.py:95
msgid "Copy Lines"
msgstr ""

#: order/serializers.py:96
msgid "Copy line items from the original order"
msgstr ""

#: order/serializers.py:102
msgid "Copy Extra Lines"
msgstr ""

#: order/serializers.py:103
msgid "Copy extra line items from the original order"
msgstr ""

#: order/serializers.py:116
#: report/templates/report/inventree_purchase_order_report.html:22
#: report/templates/report/inventree_return_order_report.html:19
#: report/templates/report/inventree_sales_order_report.html:22
msgid "Line Items"
msgstr "Mục dòng"

#: order/serializers.py:121
msgid "Completed Lines"
msgstr ""

#: order/serializers.py:172
msgid "Duplicate Order"
msgstr ""

#: order/serializers.py:173
msgid "Specify options for duplicating this order"
msgstr ""

#: order/serializers.py:249
msgid "Invalid order ID"
msgstr ""

#: order/serializers.py:388
msgid "Supplier Name"
msgstr "Tên nhà cung cấp"

#: order/serializers.py:430
msgid "Order cannot be cancelled"
msgstr "Đơn đặt không thể bị hủy"

#: order/serializers.py:445 order/serializers.py:1576
msgid "Allow order to be closed with incomplete line items"
msgstr "Cho phép đơn đặt phải đóng lại cùng với các mục dòng hàng hóa chưa hoàn thành"

#: order/serializers.py:455 order/serializers.py:1586
msgid "Order has incomplete line items"
msgstr "Đơn đặt có dòng hàng hóa chưa hoàn thành"

#: order/serializers.py:608
msgid "Order is not open"
msgstr "Đơn đặt là không được mở"

#: order/serializers.py:629
msgid "Auto Pricing"
msgstr ""

#: order/serializers.py:631
msgid "Automatically calculate purchase price based on supplier part data"
msgstr ""

#: order/serializers.py:641
msgid "Purchase price currency"
msgstr "Tiền tệ giá mua"

#: order/serializers.py:649
msgid "Merge Items"
msgstr ""

#: order/serializers.py:651
msgid "Merge items with the same part, destination and target date into one line item"
msgstr ""

#: order/serializers.py:658 part/serializers.py:571
msgid "SKU"
msgstr ""

#: order/serializers.py:672 part/models.py:1089 part/serializers.py:412
msgid "Internal Part Number"
msgstr "Mã sản phẩm nội bộ"

#: order/serializers.py:680
msgid "Internal Part Name"
msgstr ""

#: order/serializers.py:696
msgid "Supplier part must be specified"
msgstr "Sản phẩm nhà cung cấp phải được chỉ định"

#: order/serializers.py:699
msgid "Purchase order must be specified"
msgstr "Đơn đặt mua phải được chỉ định"

#: order/serializers.py:707
msgid "Supplier must match purchase order"
msgstr "Nhà cung cấp phải phù hợp với đơn đặt mua"

#: order/serializers.py:708
msgid "Purchase order must match supplier"
msgstr "Đơn đặt mua phải phù hợp với nhà cung cấp"

#: order/serializers.py:754 order/serializers.py:1656
msgid "Line Item"
msgstr "Mục dòng"

#: order/serializers.py:760
msgid "Line item does not match purchase order"
msgstr "Mục dòng không phù hợp với đơn đặt mua"

#: order/serializers.py:770 order/serializers.py:917 order/serializers.py:2023
msgid "Select destination location for received items"
msgstr "Chọn vị trí đích cho hàng hóa đã nhận"

#: order/serializers.py:786
msgid "Enter batch code for incoming stock items"
msgstr "Nhập mã lô cho hàng trong kho đang đến"

#: order/serializers.py:793 stock/models.py:1083 users/models.py:143
msgid "Expiry Date"
msgstr "Ngày hết hạn"

#: order/serializers.py:794
msgid "Enter expiry date for incoming stock items"
msgstr ""

#: order/serializers.py:802
msgid "Enter serial numbers for incoming stock items"
msgstr "Nhập số sê ri cho hàng trong kho đang đến"

#: order/serializers.py:812
msgid "Override packaging information for incoming stock items"
msgstr ""

#: order/serializers.py:820 order/serializers.py:2028
msgid "Additional note for incoming stock items"
msgstr ""

#: order/serializers.py:827
msgid "Barcode"
msgstr "Mã vạch"

#: order/serializers.py:828
msgid "Scanned barcode"
msgstr "Mã vạch đã quét"

#: order/serializers.py:844
msgid "Barcode is already in use"
msgstr "Mã vạch đã được dùng"

#: order/serializers.py:867
msgid "An integer quantity must be provided for trackable parts"
msgstr "Cần điền số nguyên cho sản phẩm có thể theo dõi"

#: order/serializers.py:934 order/serializers.py:2047
msgid "Line items must be provided"
msgstr "Dòng hàng hóa phải được cung cấp"

#: order/serializers.py:950
msgid "Destination location must be specified"
msgstr "Vị trí đích phải được chỉ ra"

#: order/serializers.py:961
msgid "Supplied barcode values must be unique"
msgstr "Giá trị mã vạch đã cung cấp phải duy nhất"

#: order/serializers.py:1092
msgid "Shipments"
msgstr ""

#: order/serializers.py:1096
msgid "Completed Shipments"
msgstr "Vận đơn đã hoàn thành"

#: order/serializers.py:1283
msgid "Sale price currency"
msgstr "Tiền tệ giá bán"

#: order/serializers.py:1331
msgid "Allocated Items"
msgstr ""

#: order/serializers.py:1458
msgid "No shipment details provided"
msgstr "Chưa cung cấp thông tin vận chuyển"

#: order/serializers.py:1519 order/serializers.py:1665
msgid "Line item is not associated with this order"
msgstr "Dòng hàng hóa chưa được gắn với đơn đặt này"

#: order/serializers.py:1538
msgid "Quantity must be positive"
msgstr "Số lượng phải là số dương"

#: order/serializers.py:1675
msgid "Enter serial numbers to allocate"
msgstr "Nhập số sê ri để phân bổ"

#: order/serializers.py:1697 order/serializers.py:1817
msgid "Shipment has already been shipped"
msgstr "Vận đơn đã được chuyển đi"

#: order/serializers.py:1700 order/serializers.py:1820
msgid "Shipment is not associated with this order"
msgstr "Vận đơn không được gắn với đơn đặt này"

#: order/serializers.py:1755
msgid "No match found for the following serial numbers"
msgstr "Không tìm thấy số sê ri sau đây"

#: order/serializers.py:1762
msgid "The following serial numbers are unavailable"
msgstr ""

#: order/serializers.py:1989
msgid "Return order line item"
msgstr "Dòng riêng biệt đơn hàng trả lại"

#: order/serializers.py:1999
msgid "Line item does not match return order"
msgstr "Line item không phù hợp với đơn hàng trả lại"

#: order/serializers.py:2002
msgid "Line item has already been received"
msgstr "Line item đã nhận được"

#: order/serializers.py:2039
msgid "Items can only be received against orders which are in progress"
msgstr "Hàng hóa chỉ có thể được nhận theo đơn hàng đang trong tiến trình"

#: order/serializers.py:2131
msgid "Quantity to return"
msgstr ""

#: order/serializers.py:2143
msgid "Line price currency"
msgstr "Tiền tệ giá đồng hạng"

#: order/status_codes.py:17 order/status_codes.py:54 stock/status_codes.py:16
msgid "Lost"
msgstr "Mất"

#: order/status_codes.py:18 order/status_codes.py:55 stock/status_codes.py:24
msgid "Returned"
msgstr "Đã trả lại"

#: order/status_codes.py:47 order/status_codes.py:79
msgid "In Progress"
msgstr "Đang tiến hành"

#: order/status_codes.py:105
msgid "Return"
msgstr "Trả lại"

#: order/status_codes.py:108
msgid "Repair"
msgstr "Sửa chữa"

#: order/status_codes.py:111
msgid "Replace"
msgstr "Thay thế"

#: order/status_codes.py:114
msgid "Refund"
msgstr "Hoàn tiền"

#: order/status_codes.py:117
msgid "Reject"
msgstr "Từ chối"

#: order/tasks.py:44
msgid "Overdue Purchase Order"
msgstr "Đơn đặt mua quá hạn"

#: order/tasks.py:49
#, python-brace-format
msgid "Purchase order {po} is now overdue"
msgstr "Đơn đặt mua {po} quá hạn"

#: order/tasks.py:112
msgid "Overdue Sales Order"
msgstr "Đơn bán hàng quá hạn"

#: order/tasks.py:117
#, python-brace-format
msgid "Sales order {so} is now overdue"
msgstr "Đơn bán hàng {so} đã quá hạn"

#: order/tasks.py:177
msgid "Overdue Return Order"
msgstr ""

#: order/tasks.py:182
#, python-brace-format
msgid "Return order {ro} is now overdue"
msgstr ""

#: part/api.py:115
msgid "Starred"
msgstr ""

#: part/api.py:117
msgid "Filter by starred categories"
msgstr ""

#: part/api.py:134 stock/api.py:255
msgid "Depth"
msgstr ""

#: part/api.py:134
msgid "Filter by category depth"
msgstr ""

#: part/api.py:152 stock/api.py:273
msgid "Top Level"
msgstr ""

#: part/api.py:154
msgid "Filter by top-level categories"
msgstr ""

#: part/api.py:167 stock/api.py:288
msgid "Cascade"
msgstr ""

#: part/api.py:169
msgid "Include sub-categories in filtered results"
msgstr ""

#: part/api.py:189
msgid "Parent"
msgstr ""

#: part/api.py:191
msgid "Filter by parent category"
msgstr ""

#: part/api.py:226
msgid "Exclude sub-categories under the specified category"
msgstr ""

#: part/api.py:438
msgid "Has Results"
msgstr ""

#: part/api.py:611
msgid "Incoming Purchase Order"
msgstr "Đơn đặt mua vào"

#: part/api.py:625
msgid "Outgoing Sales Order"
msgstr "Đơn hàng bán ra"

#: part/api.py:637
msgid "Stock produced by Build Order"
msgstr "Kho sản xuất bởi Đơn đặt bản dựng"

#: part/api.py:719
msgid "Stock required for Build Order"
msgstr "Kho được yêu cầu cho đơn đặt bản dựng"

#: part/api.py:865
msgid "Validate entire Bill of Materials"
msgstr "Xác minh toàn bộ hóa đơn vật liệu"

#: part/api.py:871
msgid "This option must be selected"
msgstr "Tùy chọn này phải được chọn"

#: part/api.py:907
msgid "Is Variant"
msgstr ""

#: part/api.py:915
msgid "Is Revision"
msgstr ""

#: part/api.py:925
msgid "Has Revisions"
msgstr ""

#: part/api.py:1116
msgid "BOM Valid"
msgstr ""

#: part/api.py:1775
msgid "Assembly part is testable"
msgstr ""

#: part/api.py:1784
msgid "Component part is testable"
msgstr ""

#: part/api.py:1835
msgid "Uses"
msgstr ""

#: part/models.py:90 part/models.py:4137
#: templates/email/part_event_notification.html:16
msgid "Part Category"
msgstr "Danh mục sản phẩm"

#: part/models.py:91 users/ruleset.py:32
msgid "Part Categories"
msgstr "Danh mục sản phẩm"

#: part/models.py:109 part/models.py:1134
msgid "Default Location"
msgstr "Điểm bán mặc định"

#: part/models.py:110
msgid "Default location for parts in this category"
msgstr "Vị trí mặc định cho sản phẩm trong danh mục này"

#: part/models.py:115 stock/models.py:212
msgid "Structural"
msgstr "Cấu trúc"

#: part/models.py:117
msgid "Parts may not be directly assigned to a structural category, but may be assigned to child categories."
msgstr "Hàng hóa không được gán trực tiếp vào danh mục có cấu trúc nhưng có thể được gán vào danh mục con."

#: part/models.py:126
msgid "Default keywords"
msgstr "Từ khóa mặc định"

#: part/models.py:127
msgid "Default keywords for parts in this category"
msgstr "Từ khóa mặc định cho sản phẩm trong danh mục này"

#: part/models.py:134 stock/models.py:96 stock/models.py:194
msgid "Icon"
msgstr "Biểu tượng"

#: part/models.py:135 part/serializers.py:152 part/serializers.py:171
#: stock/models.py:195
msgid "Icon (optional)"
msgstr "Biểu tượng (tùy chọn)"

#: part/models.py:179
msgid "You cannot make this part category structural because some parts are already assigned to it!"
msgstr "Bạn không thể thay đổi cấu trúc nhóm sản phẩm này vì một số sản phẩm đã được gắn với nó rồi!"

#: part/models.py:458 part/serializers.py:130 part/serializers.py:343
#: users/ruleset.py:33
msgid "Parts"
msgstr "Nguyên liệu"

#: part/models.py:510
msgid "Cannot delete this part as it is locked"
msgstr ""

#: part/models.py:513
msgid "Cannot delete this part as it is still active"
msgstr ""

#: part/models.py:518
msgid "Cannot delete this part as it is used in an assembly"
msgstr ""

#: part/models.py:556
msgid "Invalid choice for parent part"
msgstr "Lựa chọn sai cho sản phẩm cha"

#: part/models.py:604 part/models.py:611
#, python-brace-format
msgid "Part '{self}' cannot be used in BOM for '{parent}' (recursive)"
msgstr "Không thể dùng sản phẩm '{self}' trong BOM cho '{parent}' (đệ quy)"

#: part/models.py:623
#, python-brace-format
msgid "Part '{parent}' is  used in BOM for '{self}' (recursive)"
msgstr "Sản phẩm '{parent}' được dùng trong BOM cho '{self}' (đệ quy)"

#: part/models.py:690
#, python-brace-format
msgid "IPN must match regex pattern {pattern}"
msgstr "IPN phải phù hợp mẫu biểu thức chính quy {pattern}"

#: part/models.py:698
msgid "Part cannot be a revision of itself"
msgstr ""

#: part/models.py:705
msgid "Cannot make a revision of a part which is already a revision"
msgstr ""

#: part/models.py:712
msgid "Revision code must be specified"
msgstr ""

#: part/models.py:719
msgid "Revisions are only allowed for assembly parts"
msgstr ""

#: part/models.py:726
msgid "Cannot make a revision of a template part"
msgstr ""

#: part/models.py:732
msgid "Parent part must point to the same template"
msgstr ""

#: part/models.py:829
msgid "Stock item with this serial number already exists"
msgstr "Hàng trong kho với số sê ri này đã tồn tại"

#: part/models.py:971
msgid "Duplicate IPN not allowed in part settings"
msgstr "IPN trùng lặp không được cho phép trong thiết lập sản phẩm"

#: part/models.py:983
msgid "Duplicate part revision already exists."
msgstr ""

#: part/models.py:992
msgid "Part with this Name, IPN and Revision already exists."
msgstr "Sản phẩm với Tên, IPN và Duyệt lại đã tồn tại."

#: part/models.py:1007
msgid "Parts cannot be assigned to structural part categories!"
msgstr "Sản phẩm không thể được phân vào danh mục sản phẩm có cấu trúc!"

#: part/models.py:1039
msgid "Part name"
msgstr "Tên sản phẩm"

#: part/models.py:1044
msgid "Is Template"
msgstr "Là Mẫu"

#: part/models.py:1045
msgid "Is this part a template part?"
msgstr "Sản phẩm này có phải là sản phẩm mẫu?"

#: part/models.py:1055
msgid "Is this part a variant of another part?"
msgstr "Đây có phải là 1 biến thể của sản phẩm khác?"

#: part/models.py:1056
msgid "Variant Of"
msgstr "Biến thể của"

#: part/models.py:1063
msgid "Part description (optional)"
msgstr "Mô tả (không bắt buộc)"

#: part/models.py:1070
msgid "Keywords"
msgstr "Từ khóa"

#: part/models.py:1071
msgid "Part keywords to improve visibility in search results"
msgstr "Từ khóa sản phẩm để cải thiện sự hiện diện trong kết quả tìm kiếm"

#: part/models.py:1081
msgid "Part category"
msgstr "Danh mục sản phẩm"

#: part/models.py:1088 part/serializers.py:926
#: report/templates/report/inventree_stock_location_report.html:103
msgid "IPN"
msgstr ""

#: part/models.py:1096
msgid "Part revision or version number"
msgstr "Số phiên bản hoặc bản duyệt lại sản phẩm"

#: part/models.py:1097 report/models.py:215
msgid "Revision"
msgstr "Phiên bản"

#: part/models.py:1106
msgid "Is this part a revision of another part?"
msgstr ""

#: part/models.py:1107
msgid "Revision Of"
msgstr ""

#: part/models.py:1132
msgid "Where is this item normally stored?"
msgstr "Hàng hóa này sẽ được cất vào đâu?"

#: part/models.py:1178
msgid "Default Supplier"
msgstr "Nhà cung ứng mặc định"

#: part/models.py:1179
msgid "Default supplier part"
msgstr "Nhà cung ứng sản phẩm mặc định"

#: part/models.py:1186
msgid "Default Expiry"
msgstr "Hết hạn mặc định"

#: part/models.py:1187
msgid "Expiry time (in days) for stock items of this part"
msgstr "Thời gian hết hạn (theo ngày) để nhập kho hàng hóa cho sản phẩm này"

#: part/models.py:1195 part/serializers.py:977
msgid "Minimum Stock"
msgstr "Kho tối thiểu"

#: part/models.py:1196
msgid "Minimum allowed stock level"
msgstr "Cấp độ kho tối thiểu được phép"

#: part/models.py:1205
msgid "Units of measure for this part"
msgstr "Đơn vị đo cho sản phẩm này"

#: part/models.py:1212
msgid "Can this part be built from other parts?"
msgstr "Sản phẩm này có thể được dựng từ sản phẩm khác?"

#: part/models.py:1218
msgid "Can this part be used to build other parts?"
msgstr "Sản phẩm này có thể dùng để dựng các sản phẩm khác?"

#: part/models.py:1224
msgid "Does this part have tracking for unique items?"
msgstr "Sản phẩm này có đang theo dõi cho hàng hóa duy nhất?"

#: part/models.py:1230
msgid "Can this part have test results recorded against it?"
msgstr ""

#: part/models.py:1236
msgid "Can this part be purchased from external suppliers?"
msgstr "Sản phẩm này có thể mua được từ nhà cung ứng bên ngoài?"

#: part/models.py:1242
msgid "Can this part be sold to customers?"
msgstr "Sản phẩm này có thể được bán cho khách hàng?"

#: part/models.py:1246
msgid "Is this part active?"
msgstr "Sản phẩm này đang hoạt động?"

#: part/models.py:1252
msgid "Locked parts cannot be edited"
msgstr ""

#: part/models.py:1258
msgid "Is this a virtual part, such as a software product or license?"
msgstr "Đây là sản phẩm ảo, ví dụ như sản phẩm phần mềm hay bản quyền?"

#: part/models.py:1264
msgid "BOM checksum"
msgstr "Giá trị tổng kiểm BOM"

#: part/models.py:1265
msgid "Stored BOM checksum"
msgstr "Giá trị tổng kiểm BOM đã được lưu"

#: part/models.py:1273
msgid "BOM checked by"
msgstr "BOM kiểm tra bởi"

#: part/models.py:1278
msgid "BOM checked date"
msgstr "Ngày kiểm tra BOM"

#: part/models.py:1294
msgid "Creation User"
msgstr "Tạo người dùng"

#: part/models.py:1304
msgid "Owner responsible for this part"
msgstr "Trách nhiệm chủ sở hữu cho sản phẩm này"

#: part/models.py:1309
msgid "Last Stocktake"
msgstr "Kiểm kê cuối cùng"

#: part/models.py:2190
msgid "Sell multiple"
msgstr "Bán nhiều"

#: part/models.py:3185
msgid "Currency used to cache pricing calculations"
msgstr "Tiền được dùng để làm đệm tính toán giá bán"

#: part/models.py:3201
msgid "Minimum BOM Cost"
msgstr "Chi phí BOM tối thiểu"

#: part/models.py:3202
msgid "Minimum cost of component parts"
msgstr "Chi phí thành phần sản phẩm tối thiểu"

#: part/models.py:3208
msgid "Maximum BOM Cost"
msgstr "Chi phí BOM tối đa"

#: part/models.py:3209
msgid "Maximum cost of component parts"
msgstr "Chi phí thành phần sản phẩm tối đa"

#: part/models.py:3215
msgid "Minimum Purchase Cost"
msgstr "Chi phí mua vào tối thiểu"

#: part/models.py:3216
msgid "Minimum historical purchase cost"
msgstr "Chi phí mua vào tối thiểu trong lịch sử"

#: part/models.py:3222
msgid "Maximum Purchase Cost"
msgstr "Chi phí mua tối đa"

#: part/models.py:3223
msgid "Maximum historical purchase cost"
msgstr "Chi phí thành phần sản phẩm tối đa trong lịch sử"

#: part/models.py:3229
msgid "Minimum Internal Price"
msgstr "Giá nội bộ tối thiểu"

#: part/models.py:3230
msgid "Minimum cost based on internal price breaks"
msgstr "Chi phí tối thiểu dựa trên phá vỡ giá nội bộ"

#: part/models.py:3236
msgid "Maximum Internal Price"
msgstr "Giá nội bộ tối đa"

#: part/models.py:3237
msgid "Maximum cost based on internal price breaks"
msgstr "Chi phí tối đa dựa trên phá vỡ giá nội bộ"

#: part/models.py:3243
msgid "Minimum Supplier Price"
msgstr "Giá nhà cung ứng tối thiểu"

#: part/models.py:3244
msgid "Minimum price of part from external suppliers"
msgstr "Giá sản phẩm tối thiểu từ nhà cung ứng bên ngoài"

#: part/models.py:3250
msgid "Maximum Supplier Price"
msgstr "Giá nhà cung ứng tối đa"

#: part/models.py:3251
msgid "Maximum price of part from external suppliers"
msgstr "Giá sản phẩm tối đã từ nhà cung ứng bên ngoài"

#: part/models.py:3257
msgid "Minimum Variant Cost"
msgstr "Giá trị biến thể tối thiểu"

#: part/models.py:3258
msgid "Calculated minimum cost of variant parts"
msgstr "Chi phí tối thiểu của sản phẩm biến thể đã tính"

#: part/models.py:3264
msgid "Maximum Variant Cost"
msgstr "Chi phí biến thể tối đa"

#: part/models.py:3265
msgid "Calculated maximum cost of variant parts"
msgstr "Chi phí tối đa của sản phẩm biến thể đã tính"

#: part/models.py:3271 part/models.py:3285
msgid "Minimum Cost"
msgstr "Chi phí tối thiểu"

#: part/models.py:3272
msgid "Override minimum cost"
msgstr "Ghi đề chi phí tối thiểu"

#: part/models.py:3278 part/models.py:3292
msgid "Maximum Cost"
msgstr "Chi phí tối đa"

#: part/models.py:3279
msgid "Override maximum cost"
msgstr "Ghi đề chi phí tối đa"

#: part/models.py:3286
msgid "Calculated overall minimum cost"
msgstr "Chi phí tối thiểu tính toán tổng thể"

#: part/models.py:3293
msgid "Calculated overall maximum cost"
msgstr "Chi phí tối đa tính toán tổng thể"

#: part/models.py:3299
msgid "Minimum Sale Price"
msgstr "Giá bán thấp nhất"

#: part/models.py:3300
msgid "Minimum sale price based on price breaks"
msgstr "Giá bán tối thiểu dựa trên phá giá"

#: part/models.py:3306
msgid "Maximum Sale Price"
msgstr "Giá bán cao nhất"

#: part/models.py:3307
msgid "Maximum sale price based on price breaks"
msgstr "Giá bán cao nhất dựa trên phá giá"

#: part/models.py:3313
msgid "Minimum Sale Cost"
msgstr "Chi phí bán hàng tối thiểu"

#: part/models.py:3314
msgid "Minimum historical sale price"
msgstr "Giá bán hàng tối thiểu trong lịch sử"

#: part/models.py:3320
msgid "Maximum Sale Cost"
msgstr "Giá bán hàng tối đa"

#: part/models.py:3321
msgid "Maximum historical sale price"
msgstr "Giá bán hàng tối đa trong lịch sử"

#: part/models.py:3340
msgid "Part for stocktake"
msgstr "Sản phẩm dành cho kiểm kê"

#: part/models.py:3345
msgid "Item Count"
msgstr "Tổng số hàng"

#: part/models.py:3346
msgid "Number of individual stock entries at time of stocktake"
msgstr "Số mục kho độc lậo tại thời điểm kiểm kê"

#: part/models.py:3354
msgid "Total available stock at time of stocktake"
msgstr "Tống số kho tại thời điểm kiểm kê"

#: part/models.py:3358 part/models.py:3441 part/serializers.py:273
#: report/templates/report/inventree_test_report.html:106
msgid "Date"
msgstr "Ngày"

#: part/models.py:3359
msgid "Date stocktake was performed"
msgstr "Kiểm kê đã thực hiện"

#: part/models.py:3367
msgid "Additional notes"
msgstr "Ghi chú bổ sung"

#: part/models.py:3377
msgid "User who performed this stocktake"
msgstr "Người dùng đã thực hiện đợt kiểm kê này"

#: part/models.py:3383
msgid "Minimum Stock Cost"
msgstr "Chi phí kho tối thiểu"

#: part/models.py:3384
msgid "Estimated minimum cost of stock on hand"
msgstr "Chi phí kho tối thiểu ước tính của kho đang có"

#: part/models.py:3390
msgid "Maximum Stock Cost"
msgstr "Chi phí kho tối đa"

#: part/models.py:3391
msgid "Estimated maximum cost of stock on hand"
msgstr "Chi phí kho tối đa ước tính của kho đang có"

#: part/models.py:3447
msgid "Report"
msgstr "Báo cáo"

#: part/models.py:3448
msgid "Stocktake report file (generated internally)"
msgstr "Tệp báo cáo kiểm kê (được sinh nội bộ)"

#: part/models.py:3453
msgid "Part Count"
msgstr "Bộ đếm sản phẩm"

#: part/models.py:3454
msgid "Number of parts covered by stocktake"
msgstr "Số sản phẩm đã được bao quát bởi kiểm kê"

#: part/models.py:3464
msgid "User who requested this stocktake report"
msgstr "Người dùng đã yêu cầu báo cáo kiểm kê này"

#: part/models.py:3474
msgid "Part Sale Price Break"
msgstr ""

#: part/models.py:3586
msgid "Part Test Template"
msgstr ""

#: part/models.py:3612
msgid "Invalid template name - must include at least one alphanumeric character"
msgstr ""

#: part/models.py:3633 part/models.py:3803
msgid "Choices must be unique"
msgstr "Lựa chọn phải duy nhất"

#: part/models.py:3644
msgid "Test templates can only be created for testable parts"
msgstr ""

#: part/models.py:3655
msgid "Test template with the same key already exists for part"
msgstr ""

#: part/models.py:3672
msgid "Test Name"
msgstr "Tên kiểm thử"

#: part/models.py:3673
msgid "Enter a name for the test"
msgstr "Nhập tên cho kiểm thử"

#: part/models.py:3679
msgid "Test Key"
msgstr ""

#: part/models.py:3680
msgid "Simplified key for the test"
msgstr ""

#: part/models.py:3687
msgid "Test Description"
msgstr "Mô tả kiểm thử"

#: part/models.py:3688
msgid "Enter description for this test"
msgstr "Nhập mô tả cho kiểm thử này"

#: part/models.py:3692 report/models.py:271
msgid "Enabled"
msgstr "Đã bật"

#: part/models.py:3692
msgid "Is this test enabled?"
msgstr ""

#: part/models.py:3697
msgid "Required"
msgstr "Bắt buộc"

#: part/models.py:3698
msgid "Is this test required to pass?"
msgstr "Kiểm thử này bắt buộc phải đạt?"

#: part/models.py:3703
msgid "Requires Value"
msgstr "Giá trị bắt buộc"

#: part/models.py:3704
msgid "Does this test require a value when adding a test result?"
msgstr "Kiểm thử này yêu cầu 1 giá trị khi thêm một kết quả kiểm thử?"

#: part/models.py:3709
msgid "Requires Attachment"
msgstr "Yêu cầu đính kèm"

#: part/models.py:3711
msgid "Does this test require a file attachment when adding a test result?"
msgstr "Kiểm thử này yêu cầu tệp đính kèm khi thêm một kết quả kiểm thử?"

#: part/models.py:3717 part/models.py:3865
msgid "Choices"
msgstr "Lựa chọn"

#: part/models.py:3718
msgid "Valid choices for this test (comma-separated)"
msgstr ""

#: part/models.py:3751
msgid "Part Parameter Template"
msgstr ""

#: part/models.py:3778
msgid "Checkbox parameters cannot have units"
msgstr "Tham số hộp kiểm tra không thể có đơn vị"

#: part/models.py:3783
msgid "Checkbox parameters cannot have choices"
msgstr "Tham số hộp kiểm tra không thể có lựa chọn"

#: part/models.py:3820
msgid "Parameter template name must be unique"
msgstr "Tên tham số mẫu phải là duy nhất"

#: part/models.py:3838
msgid "Parameter Name"
msgstr "Tên tham số"

#: part/models.py:3845
msgid "Physical units for this parameter"
msgstr "Đơn vị vật lý cho tham số này"

#: part/models.py:3853
msgid "Parameter description"
msgstr "Mô tả tham số"

#: part/models.py:3859
msgid "Checkbox"
msgstr "Ô lựa chọn"

#: part/models.py:3860
msgid "Is this parameter a checkbox?"
msgstr "Tham số này có phải là hộp kiểm tra?"

#: part/models.py:3866
msgid "Valid choices for this parameter (comma-separated)"
msgstr "Lựa chọn hợp lệ từ tham số này (ngăn cách bằng dấu phẩy)"

#: part/models.py:3877
msgid "Selection list for this parameter"
msgstr ""

#: part/models.py:3913
msgid "Part Parameter"
msgstr ""

#: part/models.py:3939
msgid "Parameter cannot be modified - part is locked"
msgstr ""

#: part/models.py:3977
msgid "Invalid choice for parameter value"
msgstr "Lựa chọn sai cho giá trị tham số"

#: part/models.py:4028
msgid "Parent Part"
msgstr "Sản phẩm cha"

#: part/models.py:4036 part/models.py:4144 part/models.py:4145
msgid "Parameter Template"
msgstr "Mẫu tham số"

#: part/models.py:4042
msgid "Parameter Value"
msgstr "Giá trị tham số"

#: part/models.py:4092
msgid "Part Category Parameter Template"
msgstr ""

#: part/models.py:4151
msgid "Default Value"
msgstr "Giá trị mặc định"

#: part/models.py:4152
msgid "Default Parameter Value"
msgstr "Giá trị tham số mặc định"

#: part/models.py:4283
msgid "BOM item cannot be modified - assembly is locked"
msgstr ""

#: part/models.py:4290
msgid "BOM item cannot be modified - variant assembly is locked"
msgstr ""

#: part/models.py:4300
msgid "Select parent part"
msgstr "Chọn sản phẩm cha"

#: part/models.py:4310
msgid "Sub part"
msgstr "Sản phẩm phụ"

#: part/models.py:4311
msgid "Select part to be used in BOM"
msgstr "Chọn sản phẩm được dùng trong BOM"

#: part/models.py:4322
msgid "BOM quantity for this BOM item"
msgstr "Số lượng BOM cho mục BOM này"

#: part/models.py:4328
msgid "This BOM item is optional"
msgstr "Mục BOM này là tùy chọn"

#: part/models.py:4334
msgid "This BOM item is consumable (it is not tracked in build orders)"
msgstr "Mục BOM này bị tiêu hao (không được theo dõi trong đơn đặt bản dựng)"

#: part/models.py:4341
msgid "Overage"
msgstr "Dư thừa"

#: part/models.py:4342
msgid "Estimated build wastage quantity (absolute or percentage)"
msgstr "Số lượng bản dựng lãng phí ước tính (tuyệt đối hoặc phần trăm)"

#: part/models.py:4349
msgid "BOM item reference"
msgstr "Tham chiếu mục BOM"

#: part/models.py:4357
msgid "BOM item notes"
msgstr "Ghi chú mục BOM"

#: part/models.py:4363
msgid "Checksum"
msgstr "Giá trị tổng kiểm"

#: part/models.py:4364
msgid "BOM line checksum"
msgstr "Giá trị tổng kiểm dòng BOM"

#: part/models.py:4369
msgid "Validated"
msgstr "Đã xác minh"

#: part/models.py:4370
msgid "This BOM item has been validated"
msgstr "Mục BOM này là hợp lệ"

#: part/models.py:4375
msgid "Gets inherited"
msgstr "Nhận thừa hưởng"

#: part/models.py:4376
msgid "This BOM item is inherited by BOMs for variant parts"
msgstr "Mục BOM này được thừa kế bởi BOM cho sản phẩm biến thể"

#: part/models.py:4382
msgid "Stock items for variant parts can be used for this BOM item"
msgstr "Hàng trong kho cho sản phẩm biến thể có thể được dùng bởi mục BOM này"

#: part/models.py:4467 stock/models.py:848
msgid "Quantity must be integer value for trackable parts"
msgstr "Số lượng phải là giá trị nguyên dùng cho sản phẩm có thể theo dõi được"

#: part/models.py:4477 part/models.py:4479
msgid "Sub part must be specified"
msgstr "Sản phẩm phụ phải được chỉ định"

#: part/models.py:4624
msgid "BOM Item Substitute"
msgstr "Sảm phẩm thay thế mục BOM"

#: part/models.py:4645
msgid "Substitute part cannot be the same as the master part"
msgstr "Sản phẩm thay thế không thể giống sản phẩm chủ đạo"

#: part/models.py:4658
msgid "Parent BOM item"
msgstr "Hàng hóa BOM cha"

#: part/models.py:4666
msgid "Substitute part"
msgstr "Sản phẩm thay thế"

#: part/models.py:4682
msgid "Part 1"
msgstr "Sản phẩm 1"

#: part/models.py:4690
msgid "Part 2"
msgstr "Sản phẩm 2"

#: part/models.py:4691
msgid "Select Related Part"
msgstr "Chọn sản phẩm liên quan"

#: part/models.py:4698
msgid "Note for this relationship"
msgstr ""

#: part/models.py:4717
msgid "Part relationship cannot be created between a part and itself"
msgstr "Không thể tạo mối quan hệ giữa một sản phẩm và chính nó"

#: part/models.py:4722
msgid "Duplicate relationship already exists"
msgstr "Đã tồn tại mối quan hệ trùng lặp"

#: part/serializers.py:125
msgid "Parent Category"
msgstr ""

#: part/serializers.py:126
msgid "Parent part category"
msgstr ""

#: part/serializers.py:134 part/serializers.py:168
msgid "Subcategories"
msgstr "Phụ mục"

#: part/serializers.py:207
msgid "Results"
msgstr ""

#: part/serializers.py:208
msgid "Number of results recorded against this template"
msgstr ""

#: part/serializers.py:235 part/serializers.py:253 stock/serializers.py:685
msgid "Purchase currency of this stock item"
msgstr "Loại tiền mua hàng của hàng hóa này"

#: part/serializers.py:278
msgid "Speculative Quantity"
msgstr ""

#: part/serializers.py:287
msgid "Model ID"
msgstr ""

#: part/serializers.py:313
msgid "File is not an image"
msgstr ""

#: part/serializers.py:344
msgid "Number of parts using this template"
msgstr ""

#: part/serializers.py:489
msgid "Original Part"
msgstr "Sản phẩm gốc"

#: part/serializers.py:490
msgid "Select original part to duplicate"
msgstr "Chọn sản phẩm gốc để nhân bản"

#: part/serializers.py:495
msgid "Copy Image"
msgstr "Sao chép ảnh"

#: part/serializers.py:496
msgid "Copy image from original part"
msgstr "Sao chép hình ảnh từ sản phẩm gốc"

#: part/serializers.py:502
msgid "Copy BOM"
msgstr "Sao chép BOM"

#: part/serializers.py:503
msgid "Copy bill of materials from original part"
msgstr "Sao chép định mức nguyên vật liệu từ sản phẩm gốc"

#: part/serializers.py:509
msgid "Copy Parameters"
msgstr "Sao chép thông số"

#: part/serializers.py:510
msgid "Copy parameter data from original part"
msgstr "Sao chép thông tin tham số từ sản phẩm gốc"

#: part/serializers.py:516
msgid "Copy Notes"
msgstr "Sao chép ghi chú"

#: part/serializers.py:517
msgid "Copy notes from original part"
msgstr "Sao chép ghi chú từ sản phẩm gốc"

#: part/serializers.py:535
msgid "Initial Stock Quantity"
msgstr "Số liệu tồn kho ban đầu"

#: part/serializers.py:537
msgid "Specify initial stock quantity for this Part. If quantity is zero, no stock is added."
msgstr "Chỉ ra số lượng tồn kho ban đầu cho sản phẩm. Nếu điền là không, không thêm kho nào."

#: part/serializers.py:544
msgid "Initial Stock Location"
msgstr "Vị trí kho ban đầu"

#: part/serializers.py:545
msgid "Specify initial stock location for this Part"
msgstr "Chỉ định vị trí kho ban đầu cho sản phẩm này"

#: part/serializers.py:562
msgid "Select supplier (or leave blank to skip)"
msgstr "Chọn nhà cung cấp (hoặc để trống để bỏ qua)"

#: part/serializers.py:578
msgid "Select manufacturer (or leave blank to skip)"
msgstr "Chọn nhà sản xuất (hoặc để trống để bỏ qua)"

#: part/serializers.py:588
msgid "Manufacturer part number"
msgstr "Mã số nhà sản xuất"

#: part/serializers.py:595
msgid "Selected company is not a valid supplier"
msgstr "Công ty đã chọn không phải là nhà cung ứng hợp lệ"

#: part/serializers.py:604
msgid "Selected company is not a valid manufacturer"
msgstr "Công ty đã chọn không phải là nhà sản xuất hợp lệ"

#: part/serializers.py:615
msgid "Manufacturer part matching this MPN already exists"
msgstr "Mã số nhà sản xuất khớp với MPN này đã tồn tại"

#: part/serializers.py:622
msgid "Supplier part matching this SKU already exists"
msgstr "Mã số nhà cung cấp khớp với SKU này đã tồn tại"

#: part/serializers.py:911 part/stocktake.py:222
msgid "Category Name"
msgstr "Tên danh mục"

#: part/serializers.py:937
msgid "Building"
msgstr "Đang dựng"

#: part/serializers.py:952 part/stocktake.py:223 stock/serializers.py:1084
#: stock/serializers.py:1260 users/ruleset.py:36
msgid "Stock Items"
msgstr "Hàng trong kho"

#: part/serializers.py:955
msgid "Revisions"
msgstr ""

#: part/serializers.py:958
msgid "Suppliers"
msgstr "Nhà cung cấp"

#: part/serializers.py:961 templates/email/low_stock_notification.html:16
#: templates/email/part_event_notification.html:17
msgid "Total Stock"
msgstr "Tổng số lượng"

#: part/serializers.py:967
msgid "Unallocated Stock"
msgstr ""

#: part/serializers.py:973
msgid "Variant Stock"
msgstr ""

#: part/serializers.py:1006
msgid "Duplicate Part"
msgstr "Nhân bản sản phẩm"

#: part/serializers.py:1007
msgid "Copy initial data from another Part"
msgstr "Sao chép dữ liệu ban đầu từ sản phẩm khác"

#: part/serializers.py:1013
msgid "Initial Stock"
msgstr "Số liệu kho ban đầu"

#: part/serializers.py:1014
msgid "Create Part with initial stock quantity"
msgstr "Tạo sản phẩm với số lượng tồn kho ban đầu"

#: part/serializers.py:1020
msgid "Supplier Information"
msgstr "Thông tin nhà cung cấp"

#: part/serializers.py:1021
msgid "Add initial supplier information for this part"
msgstr "Thêm thông tin nhà cung cấp ban đầu cho sản phẩm này"

#: part/serializers.py:1029
msgid "Copy Category Parameters"
msgstr "Sao chép thông số nhóm hàng"

#: part/serializers.py:1030
msgid "Copy parameter templates from selected part category"
msgstr "Sao chép mẫu tham số từ nhóm sản phẩm được chọn"

#: part/serializers.py:1035
msgid "Existing Image"
msgstr "Ảnh hiện có"

#: part/serializers.py:1036
msgid "Filename of an existing part image"
msgstr "Tên tệp của ảnh sản phẩm hiện hữu"

#: part/serializers.py:1053
msgid "Image file does not exist"
msgstr "Tệp hình ảnh không tồn tại"

#: part/serializers.py:1257
msgid "Limit stocktake report to a particular part, and any variant parts"
msgstr "Hạn chế báo cáo kiểm kê với sản phẩm riêng biệt và sản phẩm biến thể bất kỳ"

#: part/serializers.py:1267
msgid "Limit stocktake report to a particular part category, and any child categories"
msgstr "Hạn chế báo cáo kiểm kê với danh mục sản phẩm riêng biệt và danh mục con bất kỳ"

#: part/serializers.py:1277
msgid "Limit stocktake report to a particular stock location, and any child locations"
msgstr "Hạn chế báo cáo kiểm kê với vị trí kho riêng biệt và vị trí con bất kỳ"

#: part/serializers.py:1283
msgid "Exclude External Stock"
msgstr "Ngoại trừ kho bên ngoài"

#: part/serializers.py:1284
msgid "Exclude stock items in external locations"
msgstr "Loại trừ hàng trong kho của vị trí bên ngoài"

#: part/serializers.py:1289
msgid "Generate Report"
msgstr "Tạo báo cáo"

#: part/serializers.py:1290
msgid "Generate report file containing calculated stocktake data"
msgstr "Tạo tệp báo cáo chứa dữ liệu kiểm kê đã tính toán"

#: part/serializers.py:1295
msgid "Update Parts"
msgstr "Cập nhật sản phẩm"

#: part/serializers.py:1296
msgid "Update specified parts with calculated stocktake data"
msgstr "Cập nhật sản phẩm cụ thể với dữ liệu kiểm kê đã tính"

#: part/serializers.py:1304
msgid "Stocktake functionality is not enabled"
msgstr "Chức năng kiểm kê chưa được bật"

#: part/serializers.py:1309
msgid "Background worker check failed"
msgstr "Nhân công chạy ngầm kiểm tra thất bại"

#: part/serializers.py:1425
msgid "Minimum Price"
msgstr "Giá thấp nhất"

#: part/serializers.py:1426
msgid "Override calculated value for minimum price"
msgstr "Giá trị tính toán ghi đè cho giá tối thiểu"

#: part/serializers.py:1433
msgid "Minimum price currency"
msgstr "Tiền tế giá tối thiểu"

#: part/serializers.py:1440
msgid "Maximum Price"
msgstr "Giá cao nhất"

#: part/serializers.py:1441
msgid "Override calculated value for maximum price"
msgstr "Giá trị tính toán ghi đè cho giá tối đa"

#: part/serializers.py:1448
msgid "Maximum price currency"
msgstr "Tiền tế giá tối đa"

#: part/serializers.py:1477
msgid "Update"
msgstr "Cập nhật"

#: part/serializers.py:1478
msgid "Update pricing for this part"
msgstr "Cập nhật giá cho sản phẩm này"

#: part/serializers.py:1501
#, python-brace-format
msgid "Could not convert from provided currencies to {default_currency}"
msgstr "Không thể chuyển đổi từ tiền tệ đã cung cấp cho {default_currency}"

#: part/serializers.py:1508
msgid "Minimum price must not be greater than maximum price"
msgstr "Giá tối thiểu không được lớn hơn giá tối đa"

#: part/serializers.py:1511
msgid "Maximum price must not be less than minimum price"
msgstr "Giá tối đa không được nhỏ hơn giá tối thiểu"

#: part/serializers.py:1664
msgid "Select the parent assembly"
msgstr ""

#: part/serializers.py:1678
msgid "Select the component part"
msgstr ""

#: part/serializers.py:1698
msgid "Can Build"
msgstr "Có thể dựng"

#: part/serializers.py:1925
msgid "Select part to copy BOM from"
msgstr "Chọn sản phẩm để sao chép định mức nguyên vật liệu"

#: part/serializers.py:1933
msgid "Remove Existing Data"
msgstr "Xóa dữ liệu đã tồn tại"

#: part/serializers.py:1934
msgid "Remove existing BOM items before copying"
msgstr "Xóa mục BOM đã tồn tại trước khi sao chép"

#: part/serializers.py:1939
msgid "Include Inherited"
msgstr "Bao gồm thừa hưởng"

#: part/serializers.py:1940
msgid "Include BOM items which are inherited from templated parts"
msgstr "Bao gồm mục BOM được thừa hưởng từ sản phẩm mẫu"

#: part/serializers.py:1945
msgid "Skip Invalid Rows"
msgstr "Bỏ qua dòng không hợp lệ"

#: part/serializers.py:1946
msgid "Enable this option to skip invalid rows"
msgstr "Bật tùy chọn này để bỏ qua dòng không hợp lệ"

#: part/serializers.py:1951
msgid "Copy Substitute Parts"
msgstr "Sao chép sản phẩm thay thế"

#: part/serializers.py:1952
msgid "Copy substitute parts when duplicate BOM items"
msgstr "Sao chép sản phẩm thay thế khi nhân bản hàng hóa BOM"

#: part/stocktake.py:218
msgid "Part ID"
msgstr "ID sản phẩm"

#: part/stocktake.py:220
msgid "Part Description"
msgstr "Mô tả sản phẩm"

#: part/stocktake.py:221
msgid "Category ID"
msgstr "ID danh mục"

#: part/stocktake.py:224
msgid "Total Quantity"
msgstr "Tổng số lượng"

#: part/stocktake.py:225
msgid "Total Cost Min"
msgstr "Tổng chi phí tối thiểu"

#: part/stocktake.py:226
msgid "Total Cost Max"
msgstr "Tổng chi phí tối đa"

#: part/stocktake.py:284
msgid "Stocktake Report Available"
msgstr "Báo cáo kiểm kê có sẵn"

#: part/stocktake.py:285
msgid "A new stocktake report is available for download"
msgstr "Có sẵn một báo cáo kiểm kê mới để tải về"

#: part/tasks.py:38
msgid "Low stock notification"
msgstr "Thông báo sắp hết hàng"

#: part/tasks.py:40
#, python-brace-format
msgid "The available stock for {part.name} has fallen below the configured minimum level"
msgstr "Kho có sẵn cho {part.name} đã mất dưới mức cấu hình tối thiểu"

#: plugin/api.py:78
msgid "Builtin"
msgstr ""

#: plugin/api.py:92
msgid "Mandatory"
msgstr ""

#: plugin/api.py:103
msgid "Sample"
msgstr ""

#: plugin/api.py:117 plugin/models.py:159
msgid "Installed"
msgstr "Đã cài đặt"

#: plugin/api.py:184
msgid "Plugin cannot be deleted as it is currently active"
msgstr ""

#: plugin/base/action/api.py:34
msgid "No action specified"
msgstr "Chưa chỉ ra hành động cụ thể"

#: plugin/base/action/api.py:46
msgid "No matching action found"
msgstr "Không tìm thấy chức năng phù hợp"

#: plugin/base/barcodes/api.py:211
msgid "No match found for barcode data"
msgstr "Không tìm thấy dữ liệu mã vạch phù hợp"

#: plugin/base/barcodes/api.py:215
msgid "Match found for barcode data"
msgstr "Đã tìm thấy dữ liệu mã vạch phù hợp"

#: plugin/base/barcodes/api.py:253 plugin/base/barcodes/serializers.py:77
msgid "Model is not supported"
msgstr ""

#: plugin/base/barcodes/api.py:258
msgid "Model instance not found"
msgstr ""

#: plugin/base/barcodes/api.py:287
msgid "Barcode matches existing item"
msgstr "Mã vạch phù hợp với hàng hóa hiện có"

#: plugin/base/barcodes/api.py:418
msgid "No matching part data found"
msgstr "Không tìm thấy thông tin sản phẩm phù hợp"

#: plugin/base/barcodes/api.py:434
msgid "No matching supplier parts found"
msgstr "Không tìm thấy sản phẩm nhà cung cấp phù hợp"

#: plugin/base/barcodes/api.py:437
msgid "Multiple matching supplier parts found"
msgstr "Tìm thấy nhiều sản phẩm nhà cung cấp phù hợp"

#: plugin/base/barcodes/api.py:450 plugin/base/barcodes/api.py:677
msgid "No matching plugin found for barcode data"
msgstr ""

#: plugin/base/barcodes/api.py:460
msgid "Matched supplier part"
msgstr "Sản phẩm nhà cung cấp phù hợp"

#: plugin/base/barcodes/api.py:528
msgid "Item has already been received"
msgstr "Hàng hóa này đã được nhận"

#: plugin/base/barcodes/api.py:576
msgid "No plugin match for supplier barcode"
msgstr ""

#: plugin/base/barcodes/api.py:625
msgid "Multiple matching line items found"
msgstr ""

#: plugin/base/barcodes/api.py:628
msgid "No matching line item found"
msgstr ""

#: plugin/base/barcodes/api.py:674
msgid "No sales order provided"
msgstr ""

#: plugin/base/barcodes/api.py:683
msgid "Barcode does not match an existing stock item"
msgstr ""

#: plugin/base/barcodes/api.py:699
msgid "Stock item does not match line item"
msgstr ""

#: plugin/base/barcodes/api.py:729
msgid "Insufficient stock available"
msgstr "Kho không đủ hạn mức khả dụng"

#: plugin/base/barcodes/api.py:742
msgid "Stock item allocated to sales order"
msgstr ""

#: plugin/base/barcodes/api.py:745
msgid "Not enough information"
msgstr "Không đủ thông tin"

#: plugin/base/barcodes/mixins.py:308
#: plugin/builtin/barcodes/inventree_barcode.py:101
msgid "Found matching item"
msgstr ""

#: plugin/base/barcodes/mixins.py:374
msgid "Supplier part does not match line item"
msgstr ""

#: plugin/base/barcodes/mixins.py:377
msgid "Line item is already completed"
msgstr ""

#: plugin/base/barcodes/mixins.py:414
msgid "Further information required to receive line item"
msgstr "Buộc phải nhập thông tin khác để nhận mục dòng này"

#: plugin/base/barcodes/mixins.py:422
msgid "Received purchase order line item"
msgstr "Mục dòng đơn đặt mua đã nhận"

#: plugin/base/barcodes/mixins.py:429
msgid "Failed to receive line item"
msgstr ""

#: plugin/base/barcodes/serializers.py:53
msgid "Scanned barcode data"
msgstr "Thông tin mã vạch đã quét"

#: plugin/base/barcodes/serializers.py:62
msgid "Model name to generate barcode for"
msgstr ""

#: plugin/base/barcodes/serializers.py:67
msgid "Primary key of model object to generate barcode for"
msgstr ""

#: plugin/base/barcodes/serializers.py:137
msgid "Purchase Order to allocate items against"
msgstr ""

#: plugin/base/barcodes/serializers.py:143
msgid "Purchase order is not open"
msgstr ""

#: plugin/base/barcodes/serializers.py:161
msgid "Supplier to receive items from"
msgstr ""

#: plugin/base/barcodes/serializers.py:168
msgid "PurchaseOrder to receive items against"
msgstr "Đơn đặt mua để nhận hàng hóa"

#: plugin/base/barcodes/serializers.py:174
msgid "Purchase order has not been placed"
msgstr "Đơn đặt mua vẫn chưa được thực hiện"

#: plugin/base/barcodes/serializers.py:182
msgid "Location to receive items into"
msgstr "Địa điểm để nhận hàng hóa vào bên trong"

#: plugin/base/barcodes/serializers.py:188
msgid "Cannot select a structural location"
msgstr "Không thể chọn một địa điểm có cấu trúc"

#: plugin/base/barcodes/serializers.py:196
msgid "Purchase order line item to receive items against"
msgstr ""

#: plugin/base/barcodes/serializers.py:202
msgid "Automatically allocate stock items to the purchase order"
msgstr ""

#: plugin/base/barcodes/serializers.py:215
msgid "Sales Order to allocate items against"
msgstr ""

#: plugin/base/barcodes/serializers.py:221
msgid "Sales order is not open"
msgstr ""

#: plugin/base/barcodes/serializers.py:229
msgid "Sales order line item to allocate items against"
msgstr ""

#: plugin/base/barcodes/serializers.py:236
msgid "Sales order shipment to allocate items against"
msgstr ""

#: plugin/base/barcodes/serializers.py:242
msgid "Shipment has already been delivered"
msgstr ""

#: plugin/base/barcodes/serializers.py:247
msgid "Quantity to allocate"
msgstr "Số lượng cần phân bổ"

#: plugin/base/label/label.py:39
msgid "Label printing failed"
msgstr "In nhãn thất bại"

#: plugin/base/label/mixins.py:55
msgid "Error rendering label to PDF"
msgstr ""

#: plugin/base/label/mixins.py:69
msgid "Error rendering label to HTML"
msgstr ""

#: plugin/base/label/mixins.py:146
msgid "No items provided to print"
msgstr ""

#: plugin/base/ui/serializers.py:30
msgid "Plugin Name"
msgstr ""

#: plugin/base/ui/serializers.py:34
msgid "Feature Type"
msgstr ""

#: plugin/base/ui/serializers.py:39
msgid "Feature Label"
msgstr ""

#: plugin/base/ui/serializers.py:44
msgid "Feature Title"
msgstr ""

#: plugin/base/ui/serializers.py:49
msgid "Feature Description"
msgstr ""

#: plugin/base/ui/serializers.py:54
msgid "Feature Icon"
msgstr ""

#: plugin/base/ui/serializers.py:58
msgid "Feature Options"
msgstr ""

#: plugin/base/ui/serializers.py:61
msgid "Feature Context"
msgstr ""

#: plugin/base/ui/serializers.py:64
msgid "Feature Source (javascript)"
msgstr ""

#: plugin/builtin/barcodes/inventree_barcode.py:27
msgid "InvenTree Barcodes"
msgstr "Mã vạch InvenTree"

#: plugin/builtin/barcodes/inventree_barcode.py:28
msgid "Provides native support for barcodes"
msgstr "Cung cấp hỗ trợ gốc cho mã vạch"

#: plugin/builtin/barcodes/inventree_barcode.py:30
#: plugin/builtin/exporter/bom_exporter.py:61
#: plugin/builtin/exporter/inventree_exporter.py:17
#: plugin/builtin/exporter/part_parameter_exporter.py:36
#: plugin/builtin/integration/core_notifications.py:33
#: plugin/builtin/integration/currency_exchange.py:21
#: plugin/builtin/integration/part_notifications.py:25
#: plugin/builtin/labels/inventree_label.py:26
#: plugin/builtin/labels/inventree_machine.py:64
#: plugin/builtin/labels/label_sheet.py:71
#: plugin/builtin/suppliers/digikey.py:19 plugin/builtin/suppliers/lcsc.py:21
#: plugin/builtin/suppliers/mouser.py:19 plugin/builtin/suppliers/tme.py:21
msgid "InvenTree contributors"
msgstr "Người đóng góp InvenTree"

#: plugin/builtin/barcodes/inventree_barcode.py:34
msgid "Internal Barcode Format"
msgstr ""

#: plugin/builtin/barcodes/inventree_barcode.py:35
msgid "Select an internal barcode format"
msgstr ""

#: plugin/builtin/barcodes/inventree_barcode.py:37
msgid "JSON barcodes (human readable)"
msgstr ""

#: plugin/builtin/barcodes/inventree_barcode.py:38
msgid "Short barcodes (space optimized)"
msgstr ""

#: plugin/builtin/barcodes/inventree_barcode.py:43
msgid "Short Barcode Prefix"
msgstr ""

#: plugin/builtin/barcodes/inventree_barcode.py:45
msgid "Customize the prefix used for short barcodes, may be useful for environments with multiple InvenTree instances"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:17
msgid "Levels"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:18
msgid "Number of levels to export"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:23
#: plugin/builtin/exporter/part_parameter_exporter.py:17
msgid "Stock Data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:23
#: plugin/builtin/exporter/part_parameter_exporter.py:17
msgid "Include part stock data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:27
#: plugin/builtin/exporter/part_parameter_exporter.py:21
msgid "Pricing Data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:27
#: plugin/builtin/exporter/part_parameter_exporter.py:21
msgid "Include part pricing data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:31
msgid "Supplier Data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:31
msgid "Include supplier data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:36
msgid "Manufacturer Data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:37
msgid "Include manufacturer data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:42
msgid "Substitute Data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:43
msgid "Include substitute part data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:48
msgid "Parameter Data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:49
msgid "Include part parameter data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:58
msgid "Multi-Level BOM Exporter"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:59
msgid "Provides support for exporting multi-level BOMs"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:96
msgid "BOM Level"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:102
#, python-brace-format
msgid "Substitute {n}"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:108
#, python-brace-format
msgid "Supplier {n}"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:109
#, python-brace-format
msgid "Supplier {n} SKU"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:110
#, python-brace-format
msgid "Supplier {n} MPN"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:116
#, python-brace-format
msgid "Manufacturer {n}"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:117
#, python-brace-format
msgid "Manufacturer {n} MPN"
msgstr ""

#: plugin/builtin/exporter/inventree_exporter.py:14
msgid "InvenTree Generic Exporter"
msgstr ""

#: plugin/builtin/exporter/inventree_exporter.py:15
msgid "Provides support for exporting data from InvenTree"
msgstr ""

#: plugin/builtin/exporter/part_parameter_exporter.py:33
msgid "Part Parameter Exporter"
msgstr ""

#: plugin/builtin/exporter/part_parameter_exporter.py:34
msgid "Exporter for part parameter data"
msgstr ""

#: plugin/builtin/integration/core_notifications.py:32
msgid "InvenTree Notifications"
msgstr "Thông báo InvenTree"

#: plugin/builtin/integration/core_notifications.py:34
msgid "Integrated outgoing notification methods"
msgstr "Phương thức thông báo ra ngoài đã tích hợp"

#: plugin/builtin/integration/core_notifications.py:39
#: plugin/builtin/integration/core_notifications.py:78
msgid "Enable email notifications"
msgstr "Bật thông báo qua email"

#: plugin/builtin/integration/core_notifications.py:40
#: plugin/builtin/integration/core_notifications.py:79
msgid "Allow sending of emails for event notifications"
msgstr "Cho phép gửi email cho thông báo sự kiện"

#: plugin/builtin/integration/core_notifications.py:45
msgid "Enable slack notifications"
msgstr "Bật thông báo qua Slack"

#: plugin/builtin/integration/core_notifications.py:47
msgid "Allow sending of slack channel messages for event notifications"
msgstr "Cho phép gửi tin nhắn qua kênh Slack cho thông báo sự kiện"

#: plugin/builtin/integration/core_notifications.py:53
msgid "Slack incoming webhook url"
msgstr "URL webhook đầu vào của Slack"

#: plugin/builtin/integration/core_notifications.py:54
msgid "URL that is used to send messages to a slack channel"
msgstr "URL dùng để gửi tin nhắn đến một kênh của Slack"

#: plugin/builtin/integration/core_notifications.py:164
msgid "Open link"
msgstr "Mở liên kết"

#: plugin/builtin/integration/currency_exchange.py:22
msgid "InvenTree Currency Exchange"
msgstr "Trao đổi tiền tệ InvenTree"

#: plugin/builtin/integration/currency_exchange.py:23
msgid "Default currency exchange integration"
msgstr "Tích hợp trao đổi tiền tệ mặc định"

#: plugin/builtin/integration/part_notifications.py:24
msgid "Part Notifications"
msgstr ""

#: plugin/builtin/integration/part_notifications.py:26
msgid "Notify users about part changes"
msgstr ""

#: plugin/builtin/integration/part_notifications.py:31
msgid "Send notifications"
msgstr ""

#: plugin/builtin/integration/part_notifications.py:32
msgid "Send notifications for part changes to subscribed users"
msgstr ""

#: plugin/builtin/integration/part_notifications.py:49
msgid "Changed part notification"
msgstr ""

#: plugin/builtin/integration/part_notifications.py:59
#, python-brace-format
msgid "The part `{part.name}` has been triggered with a `{part_action}` event"
msgstr ""

#: plugin/builtin/labels/inventree_label.py:23
msgid "InvenTree PDF label printer"
msgstr "Máy in nhãn InvenTree PDF"

#: plugin/builtin/labels/inventree_label.py:24
msgid "Provides native support for printing PDF labels"
msgstr "Cung cấp hỗ trợ gốc để in nhãn PDF"

#: plugin/builtin/labels/inventree_label.py:32
#: plugin/builtin/labels/label_sheet.py:77
msgid "Debug mode"
msgstr "Chế độ gỡ lỗi"

#: plugin/builtin/labels/inventree_label.py:33
#: plugin/builtin/labels/label_sheet.py:78
msgid "Enable debug mode - returns raw HTML instead of PDF"
msgstr "Bật chế độ gỡ lỗi - trả về mã HTML thuần thay vì PDF"

#: plugin/builtin/labels/inventree_machine.py:61
msgid "InvenTree machine label printer"
msgstr ""

#: plugin/builtin/labels/inventree_machine.py:62
msgid "Provides support for printing using a machine"
msgstr ""

#: plugin/builtin/labels/inventree_machine.py:164
msgid "last used"
msgstr ""

#: plugin/builtin/labels/inventree_machine.py:181
msgid "Options"
msgstr ""

#: plugin/builtin/labels/label_sheet.py:30
msgid "Page size for the label sheet"
msgstr "Khổ giấy cho tờ nhãn"

#: plugin/builtin/labels/label_sheet.py:35
msgid "Skip Labels"
msgstr "Bỏ qua nhãn"

#: plugin/builtin/labels/label_sheet.py:36
msgid "Skip this number of labels when printing label sheets"
msgstr "Bỏ qua số nhãn này khi in tờ nhãn"

#: plugin/builtin/labels/label_sheet.py:42
msgid "Border"
msgstr "Viền"

#: plugin/builtin/labels/label_sheet.py:43
msgid "Print a border around each label"
msgstr "In một viền xung quanh từng nhãn"

#: plugin/builtin/labels/label_sheet.py:48 report/models.py:371
msgid "Landscape"
msgstr "Ngang"

#: plugin/builtin/labels/label_sheet.py:49
msgid "Print the label sheet in landscape mode"
msgstr "In tờ viền theo khổ giấy nằm ngang"

#: plugin/builtin/labels/label_sheet.py:54
msgid "Page Margin"
msgstr ""

#: plugin/builtin/labels/label_sheet.py:55
msgid "Margin around the page in mm"
msgstr ""

#: plugin/builtin/labels/label_sheet.py:68
msgid "InvenTree Label Sheet Printer"
msgstr "Máy in tờ nhãn InvenTree"

#: plugin/builtin/labels/label_sheet.py:69
msgid "Arrays multiple labels onto a single sheet"
msgstr "Sắp xếp nhiều nhãn trong một tờ đơn"

#: plugin/builtin/labels/label_sheet.py:121
msgid "Label is too large for page size"
msgstr "Nhãn quá lớn so với khổ giấy"

#: plugin/builtin/labels/label_sheet.py:160
msgid "No labels were generated"
msgstr "Chưa tạo nhãn nào"

#: plugin/builtin/suppliers/digikey.py:16
msgid "Supplier Integration - DigiKey"
msgstr "Tích hợp nhà cung cấp - DigiKey"

#: plugin/builtin/suppliers/digikey.py:17
msgid "Provides support for scanning DigiKey barcodes"
msgstr "Hỗ trợ quét mã vạch DigiKey"

#: plugin/builtin/suppliers/digikey.py:26
msgid "The Supplier which acts as 'DigiKey'"
msgstr "Nhà cung cấp hành động như 'DigiKey'"

#: plugin/builtin/suppliers/lcsc.py:18
msgid "Supplier Integration - LCSC"
msgstr "Tích hợp nhà cung cấp - LCSC"

#: plugin/builtin/suppliers/lcsc.py:19
msgid "Provides support for scanning LCSC barcodes"
msgstr "Cung cấp khả năng quét mã vạch LCSC"

#: plugin/builtin/suppliers/lcsc.py:27
msgid "The Supplier which acts as 'LCSC'"
msgstr "Nhà cung cấp hoạt động như 'LCSC'"

#: plugin/builtin/suppliers/mouser.py:16
msgid "Supplier Integration - Mouser"
msgstr "Tích hợp nhà cung cấp - Mouser"

#: plugin/builtin/suppliers/mouser.py:17
msgid "Provides support for scanning Mouser barcodes"
msgstr "Cung cấp khả năng quét mã vạch Mouser"

#: plugin/builtin/suppliers/mouser.py:25
msgid "The Supplier which acts as 'Mouser'"
msgstr "Nhà cung cấp hành động như 'Mouser'"

#: plugin/builtin/suppliers/tme.py:18
msgid "Supplier Integration - TME"
msgstr "Tích hợp nhà cung cấp - TME"

#: plugin/builtin/suppliers/tme.py:19
msgid "Provides support for scanning TME barcodes"
msgstr "Cung cấp khả năng quét mã vạch TME"

#: plugin/builtin/suppliers/tme.py:27
msgid "The Supplier which acts as 'TME'"
msgstr "Nhà cung cấp hoạt động như 'TME'"

#: plugin/installer.py:229 plugin/installer.py:307
msgid "Only staff users can administer plugins"
msgstr ""

#: plugin/installer.py:232
msgid "Plugin installation is disabled"
msgstr ""

#: plugin/installer.py:269
msgid "Installed plugin successfully"
msgstr "Cài đặt phần mở rộng thành công"

#: plugin/installer.py:274
#, python-brace-format
msgid "Installed plugin into {path}"
msgstr "Cài đặt phần bổ sung đến {path}"

#: plugin/installer.py:298
msgid "Plugin was not found in registry"
msgstr ""

#: plugin/installer.py:301
msgid "Plugin is not a packaged plugin"
msgstr ""

#: plugin/installer.py:304
msgid "Plugin package name not found"
msgstr ""

#: plugin/installer.py:324
msgid "Plugin uninstalling is disabled"
msgstr ""

#: plugin/installer.py:328
msgid "Plugin cannot be uninstalled as it is currently active"
msgstr ""

#: plugin/installer.py:332
msgid "Plugin is not installed"
msgstr ""

#: plugin/installer.py:348
msgid "Plugin installation not found"
msgstr ""

#: plugin/installer.py:363
msgid "Uninstalled plugin successfully"
msgstr ""

#: plugin/models.py:38
msgid "Plugin Configuration"
msgstr "Cấu hình phần bổ sung"

#: plugin/models.py:39
msgid "Plugin Configurations"
msgstr "Cấu hình phần bổ sung"

#: plugin/models.py:46
msgid "Key of plugin"
msgstr "Khóa của phần bổ sung"

#: plugin/models.py:54
msgid "PluginName of the plugin"
msgstr "Tên của phần bổ sung"

#: plugin/models.py:61 plugin/serializers.py:119
msgid "Package Name"
msgstr "Tên gói"

#: plugin/models.py:63
msgid "Name of the installed package, if the plugin was installed via PIP"
msgstr ""

#: plugin/models.py:68
msgid "Is the plugin active"
msgstr "Là phần bổ sung hoạt động"

#: plugin/models.py:168
msgid "Sample plugin"
msgstr "Phần bổ sung mẫu"

#: plugin/models.py:176
msgid "Builtin Plugin"
msgstr "Plugin có sẵn"

#: plugin/models.py:184
msgid "Mandatory Plugin"
msgstr ""

#: plugin/models.py:189
msgid "Package Plugin"
msgstr ""

#: plugin/models.py:268
msgid "Plugin"
msgstr "Phần bổ sung"

#: plugin/models.py:315
msgid "Method"
msgstr "Phương thức"

#: plugin/plugin.py:312
msgid "No author found"
msgstr "Không tìm thấy tác giả"

#: plugin/registry.py:611
#, python-brace-format
msgid "Plugin '{p}' is not compatible with the current InvenTree version {v}"
msgstr "Phần bổ sung '{p}' không tương thích với phiên bản InvenTree hiện tại {v}"

#: plugin/registry.py:614
#, python-brace-format
msgid "Plugin requires at least version {v}"
msgstr "Phần bổ sung yêu cầu ít nhất phiên bản {v}"

#: plugin/registry.py:616
#, python-brace-format
msgid "Plugin requires at most version {v}"
msgstr "Phần bổ sung yêu cầu tối đa phiên bản {v}"

#: plugin/samples/integration/sample.py:52
msgid "Enable PO"
msgstr "Bật PO"

#: plugin/samples/integration/sample.py:53
msgid "Enable PO functionality in InvenTree interface"
msgstr "Bật chức năng PO trong giao diện InvenTree"

#: plugin/samples/integration/sample.py:58
msgid "API Key"
msgstr "Khóa API"

#: plugin/samples/integration/sample.py:59
msgid "Key required for accessing external API"
msgstr "Khóa bắt buộc đê truy cập API bên ngoài"

#: plugin/samples/integration/sample.py:63
msgid "Numerical"
msgstr "Kiểu số"

#: plugin/samples/integration/sample.py:64
msgid "A numerical setting"
msgstr "Thiết lập con số"

#: plugin/samples/integration/sample.py:69
msgid "Choice Setting"
msgstr "Thiết lập lựa chọn"

#: plugin/samples/integration/sample.py:70
msgid "A setting with multiple choices"
msgstr "Một thiết lập với nhiều lựa chọn"

#: plugin/samples/integration/sample_currency_exchange.py:15
msgid "Sample currency exchange plugin"
msgstr "Phần bổ sung trao đổi tiền tệ mẫu"

#: plugin/samples/integration/sample_currency_exchange.py:18
msgid "InvenTree Contributors"
msgstr "Người đóng góp InvenTree"

#: plugin/samples/integration/user_interface_sample.py:27
msgid "Enable Part Panels"
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:28
msgid "Enable custom panels for Part views"
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:33
msgid "Enable Purchase Order Panels"
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:34
msgid "Enable custom panels for Purchase Order views"
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:39
msgid "Enable Broken Panels"
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:40
msgid "Enable broken panels for testing"
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:45
msgid "Enable Dynamic Panel"
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:46
msgid "Enable dynamic panels for testing"
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:98
msgid "Part Panel"
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:131
msgid "Broken Dashboard Item"
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:133
msgid "This is a broken dashboard item - it will not render!"
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:139
msgid "Sample Dashboard Item"
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:141
msgid "This is a sample dashboard item. It renders a simple string of HTML content."
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:147
msgid "Context Dashboard Item"
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:161
msgid "Admin Dashboard Item"
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:162
msgid "This is an admin-only dashboard item."
msgstr ""

#: plugin/serializers.py:86
msgid "Source File"
msgstr ""

#: plugin/serializers.py:87
msgid "Path to the source file for admin integration"
msgstr ""

#: plugin/serializers.py:94
msgid "Optional context data for the admin integration"
msgstr ""

#: plugin/serializers.py:110
msgid "Source URL"
msgstr "URL nguồn"

#: plugin/serializers.py:112
msgid "Source for the package - this can be a custom registry or a VCS path"
msgstr "Nguồn cấp cho gói - có thể là sổ đăng ký tùy chỉnh hoặc đường dẫn VCS"

#: plugin/serializers.py:121
msgid "Name for the Plugin Package - can also contain a version indicator"
msgstr "Tên của gói bổ sung - có thể còn chứa chỉ dẫn phiên bản"

#: plugin/serializers.py:128
msgid "Version"
msgstr "Phiên bản"

#: plugin/serializers.py:130
msgid "Version specifier for the plugin. Leave blank for latest version."
msgstr ""

#: plugin/serializers.py:135
msgid "Confirm plugin installation"
msgstr "Xác nhận cài đặt phần bổ sung"

#: plugin/serializers.py:137
msgid "This will install this plugin now into the current instance. The instance will go into maintenance."
msgstr "Sẽ cài đặt phần bổ sung này vào thực thể hiện tại. Thực thể sẽ chuyển sang chế độ bảo trì."

#: plugin/serializers.py:150
msgid "Installation not confirmed"
msgstr "Cài đặt chưa được xác nhận"

#: plugin/serializers.py:152
msgid "Either packagename of URL must be provided"
msgstr "Hoặc là phải cung cấp tên gói của URL"

#: plugin/serializers.py:188
msgid "Full reload"
msgstr "Tải lại đầy đủ"

#: plugin/serializers.py:189
msgid "Perform a full reload of the plugin registry"
msgstr ""

#: plugin/serializers.py:195
msgid "Force reload"
msgstr "Buộc tải lại"

#: plugin/serializers.py:197
msgid "Force a reload of the plugin registry, even if it is already loaded"
msgstr ""

#: plugin/serializers.py:204
msgid "Collect plugins"
msgstr ""

#: plugin/serializers.py:205
msgid "Collect plugins and add them to the registry"
msgstr ""

#: plugin/serializers.py:232
msgid "Activate Plugin"
msgstr "Kích hoạt phần bổ sung"

#: plugin/serializers.py:233
msgid "Activate this plugin"
msgstr "Kích hoạt phần bổ sung này"

#: plugin/serializers.py:253
msgid "Delete configuration"
msgstr ""

#: plugin/serializers.py:254
msgid "Delete the plugin configuration from the database"
msgstr ""

#: report/api.py:44 report/serializers.py:98 report/serializers.py:148
msgid "Items"
msgstr ""

#: report/api.py:121
msgid "Plugin not found"
msgstr ""

#: report/api.py:123
msgid "Plugin is not active"
msgstr ""

#: report/api.py:125
msgid "Plugin does not support label printing"
msgstr ""

#: report/api.py:173
msgid "Invalid label dimensions"
msgstr ""

#: report/api.py:188 report/api.py:267
msgid "No valid items provided to template"
msgstr ""

#: report/helpers.py:43
msgid "A4"
msgstr ""

#: report/helpers.py:44
msgid "A3"
msgstr ""

#: report/helpers.py:45
msgid "Legal"
msgstr "Pháp lý"

#: report/helpers.py:46
msgid "Letter"
msgstr "Thư"

#: report/models.py:117
msgid "Template file with this name already exists"
msgstr ""

#: report/models.py:204
msgid "Template name"
msgstr "Tên mẫu"

#: report/models.py:210
msgid "Template description"
msgstr ""

#: report/models.py:216
msgid "Revision number (auto-increments)"
msgstr ""

#: report/models.py:222
msgid "Attach to Model on Print"
msgstr ""

#: report/models.py:224
msgid "Save report output as an attachment against linked model instance when printing"
msgstr ""

#: report/models.py:265
msgid "Filename Pattern"
msgstr "Mẫu tên tệp"

#: report/models.py:266
msgid "Pattern for generating filenames"
msgstr ""

#: report/models.py:271
msgid "Template is enabled"
msgstr ""

#: report/models.py:278
msgid "Target model type for template"
msgstr ""

#: report/models.py:298
msgid "Filters"
msgstr "Bộ lọc"

#: report/models.py:299
msgid "Template query filters (comma-separated list of key=value pairs)"
msgstr ""

#: report/models.py:358 report/models.py:564
msgid "Template file"
msgstr ""

#: report/models.py:366
msgid "Page size for PDF reports"
msgstr "Khổ giấy cho báo cáo PDF"

#: report/models.py:372
msgid "Render report in landscape orientation"
msgstr "Tạo báo cáo theo hướng ngang"

#: report/models.py:486
#, python-brace-format
msgid "Report generated from template {self.name}"
msgstr ""

#: report/models.py:511
msgid "Error generating report"
msgstr ""

#: report/models.py:570
msgid "Width [mm]"
msgstr "Chiều rộng [mm]"

#: report/models.py:571
msgid "Label width, specified in mm"
msgstr "Chiều rộng nhãn, tính theo mm"

#: report/models.py:577
msgid "Height [mm]"
msgstr "Chiều cao [mm]"

#: report/models.py:578
msgid "Label height, specified in mm"
msgstr "Chiều cao nhãn, tính theo mm"

#: report/models.py:688
msgid "Error printing labels"
msgstr ""

#: report/models.py:707
msgid "Snippet"
msgstr "Mẫu trích"

#: report/models.py:708
msgid "Report snippet file"
msgstr "Tệp báo cáo mẫu"

#: report/models.py:715
msgid "Snippet file description"
msgstr "Mô tả tệp báo cáo mẫu"

#: report/models.py:733
msgid "Asset"
msgstr "Tài sản"

#: report/models.py:734
msgid "Report asset file"
msgstr "Tệp báo cáo tài sản"

#: report/models.py:741
msgid "Asset file description"
msgstr "Mô tả tệp báo cáo tài sản"

#: report/serializers.py:91
msgid "Select report template"
msgstr ""

#: report/serializers.py:99 report/serializers.py:149
msgid "List of item primary keys to include in the report"
msgstr ""

#: report/serializers.py:132
msgid "Select label template"
msgstr ""

#: report/serializers.py:140
msgid "Printing Plugin"
msgstr ""

#: report/serializers.py:141
msgid "Select plugin to use for label printing"
msgstr ""

#: report/templates/label/part_label.html:31
#: report/templates/label/stockitem_qr.html:21
#: report/templates/label/stocklocation_qr.html:20
msgid "QR Code"
msgstr "Mã QR"

#: report/templates/label/part_label_code128.html:31
#: report/templates/label/stocklocation_qr_and_text.html:31
msgid "QR code"
msgstr "Mã QR"

#: report/templates/report/inventree_bill_of_materials_report.html:100
msgid "Bill of Materials"
msgstr "Hóa đơn nguyên vật liệu"

#: report/templates/report/inventree_bill_of_materials_report.html:133
msgid "Materials needed"
msgstr "Vật liệu cần có"

#: report/templates/report/inventree_build_order_report.html:98
#: report/templates/report/inventree_purchase_order_report.html:40
#: report/templates/report/inventree_sales_order_report.html:40
#: report/templates/report/inventree_sales_order_shipment_report.html:37
#: report/templates/report/inventree_test_report.html:84
#: report/templates/report/inventree_test_report.html:162
msgid "Part image"
msgstr "Ảnh sản phẩm"

#: report/templates/report/inventree_build_order_report.html:121
msgid "Issued"
msgstr "Đã cấp"

#: report/templates/report/inventree_build_order_report.html:146
msgid "Required For"
msgstr "Bắt buộc cho"

#: report/templates/report/inventree_purchase_order_report.html:15
msgid "Supplier was deleted"
msgstr "Nhà cung cấp đã bị xóa"

#: report/templates/report/inventree_purchase_order_report.html:30
#: report/templates/report/inventree_sales_order_report.html:30
msgid "Unit Price"
msgstr "Đơn giá"

#: report/templates/report/inventree_purchase_order_report.html:55
#: report/templates/report/inventree_return_order_report.html:48
#: report/templates/report/inventree_sales_order_report.html:55
msgid "Extra Line Items"
msgstr "Bảng liệt kê mở rộng"

#: report/templates/report/inventree_purchase_order_report.html:72
#: report/templates/report/inventree_sales_order_report.html:72
msgid "Total"
msgstr "Tổng cộng"

#: report/templates/report/inventree_return_order_report.html:25
#: report/templates/report/inventree_sales_order_shipment_report.html:45
#: report/templates/report/inventree_test_report.html:88 stock/models.py:1006
#: stock/serializers.py:163
msgid "Serial Number"
msgstr "Số sê-ri"

#: report/templates/report/inventree_sales_order_shipment_report.html:23
msgid "Allocations"
msgstr "Phân bổ"

#: report/templates/report/inventree_sales_order_shipment_report.html:47
msgid "Batch"
msgstr "Hàng loạt"

#: report/templates/report/inventree_stock_location_report.html:97
msgid "Stock location items"
msgstr "Mục vị trí kho hàng"

#: report/templates/report/inventree_test_report.html:21
msgid "Stock Item Test Report"
msgstr "Báo cáo kiểm thử mặt hàng"

#: report/templates/report/inventree_test_report.html:97
msgid "Test Results"
msgstr "Kết quả kiểm tra"

#: report/templates/report/inventree_test_report.html:102
msgid "Test"
msgstr "Thử nghiệm"

#: report/templates/report/inventree_test_report.html:129
msgid "Pass"
msgstr "Đạt"

#: report/templates/report/inventree_test_report.html:131
msgid "Fail"
msgstr "Không đạt"

#: report/templates/report/inventree_test_report.html:138
msgid "No result (required)"
msgstr "Không có kết quả (bắt buộc)"

#: report/templates/report/inventree_test_report.html:140
msgid "No result"
msgstr "Không có kết quả"

#: report/templates/report/inventree_test_report.html:153
#: stock/serializers.py:668
msgid "Installed Items"
msgstr "Mục đã cài đặt"

#: report/templates/report/inventree_test_report.html:167
msgid "Serial"
msgstr "Sê-ri"

#: report/templatetags/report.py:144
msgid "Asset file does not exist"
msgstr "Tệp tin tài sản không tồn tại"

#: report/templatetags/report.py:201 report/templatetags/report.py:277
msgid "Image file not found"
msgstr "Không tìm thấy tệp hình ảnh"

#: report/templatetags/report.py:302
msgid "part_image tag requires a Part instance"
msgstr "thẻ part_image yêu cầu 1 thực thể sản phẩm"

#: report/templatetags/report.py:349
msgid "company_image tag requires a Company instance"
msgstr "thẻ company_image yêu cầu một thực thể doanh nghiệp"

#: stock/api.py:255
msgid "Filter by location depth"
msgstr ""

#: stock/api.py:275
msgid "Filter by top-level locations"
msgstr ""

#: stock/api.py:290
msgid "Include sub-locations in filtered results"
msgstr ""

#: stock/api.py:311 stock/serializers.py:1256
msgid "Parent Location"
msgstr ""

#: stock/api.py:312
msgid "Filter by parent location"
msgstr ""

#: stock/api.py:554
msgid "Part name (case insensitive)"
msgstr ""

#: stock/api.py:560
msgid "Part name contains (case insensitive)"
msgstr ""

#: stock/api.py:566
msgid "Part name (regex)"
msgstr ""

#: stock/api.py:571
msgid "Part IPN (case insensitive)"
msgstr ""

#: stock/api.py:577
msgid "Part IPN contains (case insensitive)"
msgstr ""

#: stock/api.py:583
msgid "Part IPN (regex)"
msgstr ""

#: stock/api.py:595
msgid "Minimum stock"
msgstr ""

#: stock/api.py:599
msgid "Maximum stock"
msgstr ""

#: stock/api.py:602
msgid "Status Code"
msgstr "Mã trạng thái"

#: stock/api.py:642
msgid "External Location"
msgstr "Địa điểm bên ngoài"

#: stock/api.py:741
msgid "Consumed by Build Order"
msgstr ""

#: stock/api.py:751
msgid "Installed in other stock item"
msgstr ""

#: stock/api.py:840
msgid "Part Tree"
msgstr "Cây sản phẩm"

#: stock/api.py:862
msgid "Updated before"
msgstr ""

#: stock/api.py:866
msgid "Updated after"
msgstr ""

#: stock/api.py:870
msgid "Stocktake Before"
msgstr ""

#: stock/api.py:874
msgid "Stocktake After"
msgstr ""

#: stock/api.py:879
msgid "Expiry date before"
msgstr "Ngày hết hạn trước đó"

#: stock/api.py:883
msgid "Expiry date after"
msgstr "Ngày hết hạn sau đó"

#: stock/api.py:886 stock/serializers.py:673
msgid "Stale"
msgstr "Ế"

#: stock/api.py:987
msgid "Quantity is required"
msgstr "Bắt buộc nhập số lượng"

#: stock/api.py:992
msgid "Valid part must be supplied"
msgstr "Phải cung cấp sản phẩm hợp lệ"

#: stock/api.py:1019
msgid "The given supplier part does not exist"
msgstr "Sản phẩm nhà cung cấp đã đưa không tồn tại"

#: stock/api.py:1029
msgid "The supplier part has a pack size defined, but flag use_pack_size not set"
msgstr "Sản phẩm nhà cung cấp có kích thước đóng gói được định nghĩa nhưng cờ use_pack_size chưa được thiết lập"

#: stock/api.py:1056
msgid "Serial numbers cannot be supplied for a non-trackable part"
msgstr "Số sê-ri không thê được cung cấp cho sản phẩm không thể theo dõi"

#: stock/models.py:70
msgid "Stock Location type"
msgstr "Loại vị trí kho hàng"

#: stock/models.py:71
msgid "Stock Location types"
msgstr "Loại vị trí kho hàng"

#: stock/models.py:97
msgid "Default icon for all locations that have no icon set (optional)"
msgstr "Biểu tượng mặc định cho vị trí không được đặt biểu tượng (tùy chọn)"

#: stock/models.py:155 stock/models.py:968
msgid "Stock Location"
msgstr "Kho hàng"

#: stock/models.py:156 users/ruleset.py:35
msgid "Stock Locations"
msgstr "Vị trí kho hàng"

#: stock/models.py:205 stock/models.py:1133
msgid "Owner"
msgstr "Chủ sở hữu"

#: stock/models.py:206 stock/models.py:1134
msgid "Select Owner"
msgstr "Chọn chủ sở hữu"

#: stock/models.py:214
msgid "Stock items may not be directly located into a structural stock locations, but may be located to child locations."
msgstr "Không thể đưa trực tiếp hàng trong kho vào bên trong vị trí kho hàng có cấu trúc, nhưng có thể đặt vào kho con."

#: stock/models.py:221 users/models.py:499
msgid "External"
msgstr "Bên ngoài"

#: stock/models.py:222
msgid "This is an external stock location"
msgstr "Đây là vị trí kho bên ngoài"

#: stock/models.py:228
msgid "Location type"
msgstr "Loại vị trí"

#: stock/models.py:232
msgid "Stock location type of this location"
msgstr "Loại vị trí kho hàng của địa điểm này"

#: stock/models.py:304
msgid "You cannot make this stock location structural because some stock items are already located into it!"
msgstr "Bạn không thể chuyển đổi vị trí kho hàng này thành cấu trúc vì đã có hàng hóa trong kho được đặt vào bên trong nó!"

#: stock/models.py:562
msgid "Part must be specified"
msgstr ""

#: stock/models.py:827
msgid "Stock items cannot be located into structural stock locations!"
msgstr "Không thể đặt hàng trong kho vào trong địa điểm kho có cấu trúc!"

#: stock/models.py:854 stock/serializers.py:516
msgid "Stock item cannot be created for virtual parts"
msgstr "Không thể tạo hàng hóa trong kho cho sản phẩm ảo"

#: stock/models.py:871
#, python-brace-format
msgid "Part type ('{self.supplier_part.part}') must be {self.part}"
msgstr "Loại sản phẩm ('{self.supplier_part.part}') phải là {self.part}"

#: stock/models.py:881 stock/models.py:894
msgid "Quantity must be 1 for item with a serial number"
msgstr "Số lượng phải là 1 cho hàng hóa với số sê ri"

#: stock/models.py:884
msgid "Serial number cannot be set if quantity greater than 1"
msgstr "Số sê ri không thể đặt được nếu số lượng lớn hơn 1"

#: stock/models.py:906
msgid "Item cannot belong to itself"
msgstr "Hàng hóa không thể thuộc về chính nó"

#: stock/models.py:911
msgid "Item must have a build reference if is_building=True"
msgstr "Hàng hóa phải có 1 tham chiếu bản dựng nếu is_building=True"

#: stock/models.py:924
msgid "Build reference does not point to the same part object"
msgstr "Tham chiếu bản dựng không thể trỏ vào cùng một đối tượng sản phẩm"

#: stock/models.py:938
msgid "Parent Stock Item"
msgstr "Hàng trong kho cha"

#: stock/models.py:950
msgid "Base part"
msgstr "Sản phẩm cơ bản"

#: stock/models.py:960
msgid "Select a matching supplier part for this stock item"
msgstr "Chọn sản phẩm nhà cung cấp khớp với hàng hóa trong kho này"

#: stock/models.py:972
msgid "Where is this stock item located?"
msgstr "Hàng trong kho này được đặt ở đâu?"

#: stock/models.py:980 stock/serializers.py:1667
msgid "Packaging this stock item is stored in"
msgstr "Đóng gói hàng hóa này được lưu trữ lại"

#: stock/models.py:986
msgid "Installed In"
msgstr "Đã cài đặt trong"

#: stock/models.py:991
msgid "Is this item installed in another item?"
msgstr "Mục này đã được cài đặt trong mục khác?"

#: stock/models.py:1010
msgid "Serial number for this item"
msgstr "Số sê ri cho mục này"

#: stock/models.py:1027 stock/serializers.py:1652
msgid "Batch code for this stock item"
msgstr "Mã lô cho hàng trong kho này"

#: stock/models.py:1032
msgid "Stock Quantity"
msgstr "Số lượng tồn kho"

#: stock/models.py:1042
msgid "Source Build"
msgstr "Bản dựng nguồn"

#: stock/models.py:1045
msgid "Build for this stock item"
msgstr "Bản dựng cho hàng hóa này"

#: stock/models.py:1052
msgid "Consumed By"
msgstr "Tiêu thụ bởi"

#: stock/models.py:1055
msgid "Build order which consumed this stock item"
msgstr "Đơn đặt bản dựng đã dùng hàng hóa này"

#: stock/models.py:1064
msgid "Source Purchase Order"
msgstr "Đơn đặt mua nguồn"

#: stock/models.py:1068
msgid "Purchase order for this stock item"
msgstr "Đơn đặt mua cho hàng hóa này"

#: stock/models.py:1074
msgid "Destination Sales Order"
msgstr "Đơn hàng bán đích"

#: stock/models.py:1085
msgid "Expiry date for stock item. Stock will be considered expired after this date"
msgstr "Ngày hết hạn của hàng hóa này. Kho sẽ được nhắc tình trạng hết hạn sau ngày này"

#: stock/models.py:1103
msgid "Delete on deplete"
msgstr "Xóa khi thiếu hụt"

#: stock/models.py:1104
msgid "Delete this Stock Item when stock is depleted"
msgstr "Xóa hàng trong kho này khi kho hàng bị thiếu hụt"

#: stock/models.py:1125
msgid "Single unit purchase price at time of purchase"
msgstr "Giá mua riêng lẻ tại thời điểm mua"

#: stock/models.py:1156
msgid "Converted to part"
msgstr "Đã chuyển đổi sang sản phẩm"

#: stock/models.py:1721
msgid "Part is not set as trackable"
msgstr "Chưa đặt sản phẩm thành có thể theo dõi"

#: stock/models.py:1727
msgid "Quantity must be integer"
msgstr "Số lượng phải là số nguyên"

#: stock/models.py:1735
#, python-brace-format
msgid "Quantity must not exceed available stock quantity ({self.quantity})"
msgstr "Số lượng không thể vượt quá số lượng trong kho đang có ({self.quantity})"

#: stock/models.py:1741
msgid "Serial numbers must be provided as a list"
msgstr ""

#: stock/models.py:1746
msgid "Quantity does not match serial numbers"
msgstr "Số lượng không khớp với số sêri"

#: stock/models.py:1868 stock/models.py:2777
msgid "Test template does not exist"
msgstr ""

#: stock/models.py:1886
msgid "Stock item has been assigned to a sales order"
msgstr "Hàng trong kho đã được gán vào đơn hàng bán"

#: stock/models.py:1890
msgid "Stock item is installed in another item"
msgstr "Hàng trong kho đã được cài đặt vào hàng hóa khác"

#: stock/models.py:1893
msgid "Stock item contains other items"
msgstr "Hàng trong kho chứa hàng hóa khác"

#: stock/models.py:1896
msgid "Stock item has been assigned to a customer"
msgstr "Hàng trong kho đã được gắn với một khách hàng"

#: stock/models.py:1899 stock/models.py:2073
msgid "Stock item is currently in production"
msgstr "Hàng trong kho hiện đang sản xuất"

#: stock/models.py:1902
msgid "Serialized stock cannot be merged"
msgstr "Không thể hợp nhất kho nối tiếp"

#: stock/models.py:1909 stock/serializers.py:1545
msgid "Duplicate stock items"
msgstr "Mặt hàng trùng lặp"

#: stock/models.py:1913
msgid "Stock items must refer to the same part"
msgstr "Mặt hàng phải tham chiếu đến sản phẩm tương tự"

#: stock/models.py:1921
msgid "Stock items must refer to the same supplier part"
msgstr "Mặt hàng phải tham chiếu đến sản phẩm nhà cung cấp tương tự"

#: stock/models.py:1926
msgid "Stock status codes must match"
msgstr "Mã trạng thái kho phải phù hợp"

#: stock/models.py:2196
msgid "StockItem cannot be moved as it is not in stock"
msgstr "Không thể xóa mặt hàng không ở trong kho"

#: stock/models.py:2678
msgid "Stock Item Tracking"
msgstr ""

#: stock/models.py:2709
msgid "Entry notes"
msgstr "Ghi chú đầu vào"

#: stock/models.py:2749
msgid "Stock Item Test Result"
msgstr ""

#: stock/models.py:2780
msgid "Value must be provided for this test"
msgstr "Phải cung cấp giá trị cho kiểm thử này"

#: stock/models.py:2784
msgid "Attachment must be uploaded for this test"
msgstr "Phải tải liên đính kèm cho kiểm thử này"

#: stock/models.py:2789
msgid "Invalid value for this test"
msgstr ""

#: stock/models.py:2813
msgid "Test result"
msgstr "Kết quả kiểm thử"

#: stock/models.py:2820
msgid "Test output value"
msgstr "Giá trị đầu ra kiểm thử"

#: stock/models.py:2828 stock/serializers.py:259
msgid "Test result attachment"
msgstr "Đính kèm kết quả kiểm thử"

#: stock/models.py:2832
msgid "Test notes"
msgstr "Ghi chú kiểm thử"

#: stock/models.py:2840
msgid "Test station"
msgstr ""

#: stock/models.py:2841
msgid "The identifier of the test station where the test was performed"
msgstr ""

#: stock/models.py:2847
msgid "Started"
msgstr ""

#: stock/models.py:2848
msgid "The timestamp of the test start"
msgstr ""

#: stock/models.py:2854
msgid "Finished"
msgstr ""

#: stock/models.py:2855
msgid "The timestamp of the test finish"
msgstr ""

#: stock/serializers.py:84
msgid "Generated batch code"
msgstr ""

#: stock/serializers.py:93
msgid "Select build order"
msgstr ""

#: stock/serializers.py:102
msgid "Select stock item to generate batch code for"
msgstr ""

#: stock/serializers.py:111
msgid "Select location to generate batch code for"
msgstr ""

#: stock/serializers.py:120
msgid "Select part to generate batch code for"
msgstr ""

#: stock/serializers.py:129
msgid "Select purchase order"
msgstr ""

#: stock/serializers.py:136
msgid "Enter quantity for batch code"
msgstr ""

#: stock/serializers.py:162
msgid "Generated serial number"
msgstr ""

#: stock/serializers.py:172
msgid "Select part to generate serial number for"
msgstr ""

#: stock/serializers.py:180
msgid "Quantity of serial numbers to generate"
msgstr ""

#: stock/serializers.py:248
msgid "Test template for this result"
msgstr ""

#: stock/serializers.py:272
msgid "Template ID or test name must be provided"
msgstr ""

#: stock/serializers.py:304
msgid "The test finished time cannot be earlier than the test started time"
msgstr ""

#: stock/serializers.py:341
msgid "Serial number is too large"
msgstr "Số sêri quá lớn"

#: stock/serializers.py:487
msgid "Parent Item"
msgstr "Mục cha"

#: stock/serializers.py:488
msgid "Parent stock item"
msgstr ""

#: stock/serializers.py:508
msgid "Use pack size when adding: the quantity defined is the number of packs"
msgstr "Sử dụng kích thước đóng gói khi thêm: Số lượng được định nghĩa là số của gói"

#: stock/serializers.py:618
msgid "Supplier Part Number"
msgstr "Số hiệu hàng hoá nhà cung cấp"

#: stock/serializers.py:665 users/models.py:193
msgid "Expired"
msgstr "Đã hết hạn"

#: stock/serializers.py:671
msgid "Child Items"
msgstr "Mục con"

#: stock/serializers.py:675
msgid "Tracking Items"
msgstr ""

#: stock/serializers.py:681
msgid "Purchase price of this stock item, per unit or pack"
msgstr "Giá mua của mặt hàng, theo đơn vị hoặc gói"

#: stock/serializers.py:719
msgid "Enter number of stock items to serialize"
msgstr "Nhập số của mặt hàng cần tạo số nối tiếp"

#: stock/serializers.py:732
#, python-brace-format
msgid "Quantity must not exceed available stock quantity ({q})"
msgstr "Số lượng phải không vượt quá số lượng trong kho đang có ({q})"

#: stock/serializers.py:739
msgid "Enter serial numbers for new items"
msgstr "Điền số sêri cho hàng hóa mới"

#: stock/serializers.py:750 stock/serializers.py:1502 stock/serializers.py:1790
msgid "Destination stock location"
msgstr "Vị trí kho đích"

#: stock/serializers.py:757
msgid "Optional note field"
msgstr "Trường ghi chú tùy chọn"

#: stock/serializers.py:767
msgid "Serial numbers cannot be assigned to this part"
msgstr "Không thể gán số sêri cho sản phẩm này"

#: stock/serializers.py:787
msgid "Serial numbers already exist"
msgstr "Số sêri đã tồn tại"

#: stock/serializers.py:826
msgid "Select stock item to install"
msgstr "Chọn mặt hàng để lắp đặt"

#: stock/serializers.py:833
msgid "Quantity to Install"
msgstr "Số lượng để cài đặt"

#: stock/serializers.py:834
msgid "Enter the quantity of items to install"
msgstr "Nhập số lượng hàng hóa để cài đặt"

#: stock/serializers.py:839 stock/serializers.py:919 stock/serializers.py:1049
#: stock/serializers.py:1101
msgid "Add transaction note (optional)"
msgstr "Thêm ghi chú giao dịch (tùy chọn)"

#: stock/serializers.py:847
msgid "Quantity to install must be at least 1"
msgstr "Số lượng cần cài đặt phải ít nhất là 1"

#: stock/serializers.py:855
msgid "Stock item is unavailable"
msgstr "Mặt hàng không khả dụng"

#: stock/serializers.py:866
msgid "Selected part is not in the Bill of Materials"
msgstr "Sản phẩm đã chọn không có trong hóa đơn vật liệu"

#: stock/serializers.py:879
msgid "Quantity to install must not exceed available quantity"
msgstr "Số lượng cần lắp đặt phải không vượt quá số lượng đang có"

#: stock/serializers.py:914
msgid "Destination location for uninstalled item"
msgstr "Vị trí đích cho hàng hóa bị gỡ bỏ"

#: stock/serializers.py:949
msgid "Select part to convert stock item into"
msgstr "Chọn sản phẩm để chuyển đổi mặt hàng vào bên trong"

#: stock/serializers.py:962
msgid "Selected part is not a valid option for conversion"
msgstr "Sản phẩm đã chọn không phải là tùy chọn hợp lệ để chuyển đổi"

#: stock/serializers.py:979
msgid "Cannot convert stock item with assigned SupplierPart"
msgstr "Không thể chuyển đổi hàng hóa với sản phẩm nhà cung cấp đã gán"

#: stock/serializers.py:1013
msgid "Stock item status code"
msgstr "Mã trạng thái mặt hàng"

#: stock/serializers.py:1042
msgid "Destination location for returned item"
msgstr "Vị trí đích dành cho hàng hóa trả lại"

#: stock/serializers.py:1085
msgid "Select stock items to change status"
msgstr "Chọn mặt hàng để đổi trạng thái"

#: stock/serializers.py:1091
msgid "No stock items selected"
msgstr "Không có mặt hàng nào được chọn"

#: stock/serializers.py:1185 stock/serializers.py:1262
msgid "Sublocations"
msgstr "Kho phụ"

#: stock/serializers.py:1257
msgid "Parent stock location"
msgstr ""

#: stock/serializers.py:1374
msgid "Part must be salable"
msgstr "Sản phẩm phải có thể bán được"

#: stock/serializers.py:1378
msgid "Item is allocated to a sales order"
msgstr "Hàng hóa được phân bổ đến một đơn hàng bán"

#: stock/serializers.py:1382
msgid "Item is allocated to a build order"
msgstr "Hàng hóa được phân bổ đến một đơn đặt bản dựng"

#: stock/serializers.py:1406
msgid "Customer to assign stock items"
msgstr "Khách hàng được gán vào các mặt hàng"

#: stock/serializers.py:1412
msgid "Selected company is not a customer"
msgstr "Công ty đã chọn không phải là khách hàng"

#: stock/serializers.py:1420
msgid "Stock assignment notes"
msgstr "Ghi chú phân bổ kho"

#: stock/serializers.py:1430 stock/serializers.py:1695
msgid "A list of stock items must be provided"
msgstr "Phải cung cấp danh sách mặt hàng"

#: stock/serializers.py:1509
msgid "Stock merging notes"
msgstr "Ghi chú gộp kho"

#: stock/serializers.py:1514
msgid "Allow mismatched suppliers"
msgstr "Cho phép nhiều nhà cung không khớp"

#: stock/serializers.py:1515
msgid "Allow stock items with different supplier parts to be merged"
msgstr "Cho phép mặt hàng cùng sản phẩm nhà cung cấp khác phải được gộp"

#: stock/serializers.py:1520
msgid "Allow mismatched status"
msgstr "Cho phép trạng thái không khớp"

#: stock/serializers.py:1521
msgid "Allow stock items with different status codes to be merged"
msgstr "Cho phép mặt hàng với mã trạng thái khác nhau để gộp lại"

#: stock/serializers.py:1531
msgid "At least two stock items must be provided"
msgstr "Cần cung cấp ít nhất hai mặt hàng"

#: stock/serializers.py:1598
msgid "No Change"
msgstr ""

#: stock/serializers.py:1627
msgid "StockItem primary key value"
msgstr "Giá trị khóa chính mặt hàng"

#: stock/serializers.py:1639
msgid "Stock item is not in stock"
msgstr ""

#: stock/serializers.py:1685
msgid "Stock transaction notes"
msgstr "Ghi chú giao dịch kho"

#: stock/serializers.py:1831
msgid "Next Serial Number"
msgstr ""

#: stock/serializers.py:1837
msgid "Previous Serial Number"
msgstr ""

#: stock/status_codes.py:11
msgid "OK"
msgstr "Đồng ý"

#: stock/status_codes.py:12
msgid "Attention needed"
msgstr "Chú ý cần thiết"

#: stock/status_codes.py:13
msgid "Damaged"
msgstr "Bị hư hại"

#: stock/status_codes.py:14
msgid "Destroyed"
msgstr "Đã hủy"

#: stock/status_codes.py:15
msgid "Rejected"
msgstr "Đã từ chối"

#: stock/status_codes.py:19
msgid "Quarantined"
msgstr "Đã cách ly"

#: stock/status_codes.py:44
msgid "Legacy stock tracking entry"
msgstr "Mục theo dõi kho cổ điển"

#: stock/status_codes.py:46
msgid "Stock item created"
msgstr "Kho hàng đã được khởi tạo"

#: stock/status_codes.py:49
msgid "Edited stock item"
msgstr "Sửa kho hàng"

#: stock/status_codes.py:50
msgid "Assigned serial number"
msgstr "Số sê ri đã được gán"

#: stock/status_codes.py:53
msgid "Stock counted"
msgstr "Kho đã đếm"

#: stock/status_codes.py:54
msgid "Stock manually added"
msgstr "Kho được thêm thủ công"

#: stock/status_codes.py:55
msgid "Stock manually removed"
msgstr "Kho được xóa thủ công"

#: stock/status_codes.py:58
msgid "Location changed"
msgstr "Vị trí đã thay đổi"

#: stock/status_codes.py:59
msgid "Stock updated"
msgstr "Kho hàng đã được cập nhật"

#: stock/status_codes.py:62
msgid "Installed into assembly"
msgstr "Đã cài đặt vào bộ phận lắp ráp"

#: stock/status_codes.py:63
msgid "Removed from assembly"
msgstr "Di rời khỏi bộ phận lắp ráp"

#: stock/status_codes.py:65
msgid "Installed component item"
msgstr "Mục thành phần đã cài đặt"

#: stock/status_codes.py:66
msgid "Removed component item"
msgstr "Mục thành phần đã di rời"

#: stock/status_codes.py:69
msgid "Split from parent item"
msgstr "Tách từ mục cha"

#: stock/status_codes.py:70
msgid "Split child item"
msgstr "Tách mục con"

#: stock/status_codes.py:73
msgid "Merged stock items"
msgstr "Kho hàng đã được gộp"

#: stock/status_codes.py:76
msgid "Converted to variant"
msgstr "Đã chuyển đổi sang biến thể"

#: stock/status_codes.py:79
msgid "Build order output created"
msgstr "Đầu ra đơn đặt bản dựng đã được tạo"

#: stock/status_codes.py:80
msgid "Build order output completed"
msgstr "Đầu ra đơn đặt bản dựng đã hoàn thành"

#: stock/status_codes.py:81
msgid "Build order output rejected"
msgstr "Đầu ra đơn đặt bản dựng bị từ chối"

#: stock/status_codes.py:82
msgid "Consumed by build order"
msgstr "Tiêu hao bởi đơn đặt bản dựng"

#: stock/status_codes.py:85
msgid "Shipped against Sales Order"
msgstr "Vận chyển dựa vào đơn đặt bản dựng"

#: stock/status_codes.py:88
msgid "Received against Purchase Order"
msgstr "Đã nhận dựa vào đơn đặt hàng mua"

#: stock/status_codes.py:91
msgid "Returned against Return Order"
msgstr "Trả hành dựa vào đơn hàng trả lại"

#: stock/status_codes.py:94
msgid "Sent to customer"
msgstr "Gửi đến khách hàng"

#: stock/status_codes.py:95
msgid "Returned from customer"
msgstr "Bị trả lại từ khách hàng"

#: templates/403.html:6 templates/403.html:10 templates/403_csrf.html:7
msgid "Permission Denied"
msgstr "Quyền truy cập bị từ chối"

#: templates/403.html:11
msgid "You do not have permission to view this page."
msgstr "Bạn không có quyền xem trang này."

#: templates/403_csrf.html:11
msgid "Authentication Failure"
msgstr "Lỗi xác thực"

#: templates/403_csrf.html:12
msgid "You have been logged out from InvenTree."
msgstr "Bạn đã đăng xuất từ InvenTree."

#: templates/404.html:6 templates/404.html:10
msgid "Page Not Found"
msgstr "Trang không tồn tại"

#: templates/404.html:11
msgid "The requested page does not exist"
msgstr "Trang được yêu cầu không tồn tại"

#: templates/500.html:6 templates/500.html:10
msgid "Internal Server Error"
msgstr "Lỗi nội bộ máy chủ"

#: templates/500.html:11
#, python-format
msgid "The %(inventree_title)s server raised an internal error"
msgstr "Máy chủ %(inventree_title)s thông báo lỗi nội bộ"

#: templates/500.html:12
msgid "Refer to the error log in the admin interface for further details"
msgstr "Tham khảo nhật ký lỗi trong giao diện quản trị để biết thêm chi tiết"

#: templates/503.html:11 templates/503.html:15
msgid "Site is in Maintenance"
msgstr "Site đang được bảo trì"

#: templates/503.html:17
msgid "The site is currently in maintenance and should be up again soon!"
msgstr "Khu vực đang được bảo trì và sẽ mở lại sớm thôi!"

#: templates/base.html:51
msgid "Server Restart Required"
msgstr "Yêu cầu khởi động máy chủ"

#: templates/base.html:54
msgid "A configuration option has been changed which requires a server restart"
msgstr "Đã thay đổi tùy chọn cấu hình nên cần phải khởi động lại máy chủ"

#: templates/base.html:54 templates/base.html:64
msgid "Contact your system administrator for further information"
msgstr "Liên lạc với quản trị hệ thống của bạn để biết thêm thông tin"

#: templates/base.html:61
msgid "Pending Database Migrations"
msgstr "Di trú cơ sở dữ liệu đang chờ xử lý"

#: templates/base.html:64
msgid "There are pending database migrations which require attention"
msgstr "Có di trú cơ sở dữ liệu đang chờ xử lý cần bạn lưu ý"

#: templates/email/build_order_completed.html:9
#: templates/email/canceled_order_assigned.html:9
#: templates/email/new_order_assigned.html:9
#: templates/email/overdue_build_order.html:9
#: templates/email/overdue_purchase_order.html:9
#: templates/email/overdue_return_order.html:9
#: templates/email/overdue_sales_order.html:9
#: templates/email/purchase_order_received.html:9
#: templates/email/return_order_received.html:9
msgid "Click on the following link to view this order"
msgstr "Nhấp chuột vào liên kết dưới đây để xem đơn đặt này"

#: templates/email/build_order_required_stock.html:7
msgid "Stock is required for the following build order"
msgstr "Kho là bắt buộc cho đơn đặt bản dựng sau đây"

#: templates/email/build_order_required_stock.html:8
#, python-format
msgid "Build order %(build)s - building %(quantity)s x %(part)s"
msgstr "Đơn đặt bản dựng %(build)s - đang dựng %(quantity)s x %(part)s"

#: templates/email/build_order_required_stock.html:10
msgid "Click on the following link to view this build order"
msgstr "Nhấp vào liên kết dưới đây để xem đơn đặt bản dựng này"

#: templates/email/build_order_required_stock.html:14
msgid "The following parts are low on required stock"
msgstr "Sản phẩm sau còn ít hàng trong kho yêu cầu"

#: templates/email/build_order_required_stock.html:18
msgid "Required Quantity"
msgstr "Số lượng bắt buộc"

#: templates/email/build_order_required_stock.html:38
#: templates/email/low_stock_notification.html:30
msgid "You are receiving this email because you are subscribed to notifications for this part "
msgstr "Bạn nhận được email này vì bạn đã đăng ký nhận thông báo cho sản phẩm này "

#: templates/email/low_stock_notification.html:9
#: templates/email/part_event_notification.html:9
msgid "Click on the following link to view this part"
msgstr "Nhấp chuột vào liên kết dưới đây để xem sản phẩm này"

#: templates/email/low_stock_notification.html:18
#: templates/email/part_event_notification.html:19
msgid "Minimum Quantity"
msgstr "Số lượng tối thiểu"

#: templates/email/part_event_notification.html:32
msgid "You are receiving this email because you are subscribed to notifications for this part or a category that it is part of "
msgstr ""

#: users/admin.py:101
msgid "Users"
msgstr "Người dùng"

#: users/admin.py:102
msgid "Select which users are assigned to this group"
msgstr "Chọn người dùng được chỉ định cho nhóm này"

#: users/admin.py:137
msgid "Personal info"
msgstr "Thông tin cá nhân"

#: users/admin.py:139
msgid "Permissions"
msgstr "Quyền"

#: users/admin.py:142
msgid "Important dates"
msgstr "Ngày quan trọng"

#: users/authentication.py:30 users/models.py:157
msgid "Token has been revoked"
msgstr "Mã thông báo đã bị thu hồi"

#: users/authentication.py:33
msgid "Token has expired"
msgstr "Mã thông báo đã hết hạn"

#: users/models.py:100
msgid "API Token"
msgstr "Mã thông báo API"

#: users/models.py:101
msgid "API Tokens"
msgstr "Mã thông báo API"

#: users/models.py:137
msgid "Token Name"
msgstr "Tên mã thông báo"

#: users/models.py:138
msgid "Custom token name"
msgstr "Tên tùy chỉnh mã thông báo"

#: users/models.py:144
msgid "Token expiry date"
msgstr "Ngày hết hạn mã thông báo"

#: users/models.py:152
msgid "Last Seen"
msgstr "Xem lần cuối"

#: users/models.py:153
msgid "Last time the token was used"
msgstr "Lần cuối mã thông báo được sử dụng"

#: users/models.py:157
msgid "Revoked"
msgstr "Đã thu hồi"

#: users/models.py:235
msgid "Permission set"
msgstr "Quyền hạn đã đặt"

#: users/models.py:244
msgid "Group"
msgstr "Nhóm"

#: users/models.py:248
msgid "View"
msgstr "Xem"

#: users/models.py:248
msgid "Permission to view items"
msgstr "Quyền để xem mục"

#: users/models.py:252
msgid "Add"
msgstr "Thêm"

#: users/models.py:252
msgid "Permission to add items"
msgstr "Quyền để thêm mục"

#: users/models.py:256
msgid "Change"
msgstr "Đổi"

#: users/models.py:258
msgid "Permissions to edit items"
msgstr "Quyển để sửa mục"

#: users/models.py:262
msgid "Delete"
msgstr "Xóa"

#: users/models.py:264
msgid "Permission to delete items"
msgstr "Quyền để xóa mục"

#: users/models.py:497
msgid "Bot"
msgstr ""

#: users/models.py:498
msgid "Internal"
msgstr ""

#: users/models.py:500
msgid "Guest"
msgstr ""

#: users/models.py:509
msgid "Language"
msgstr ""

#: users/models.py:510
msgid "Preferred language for the user"
msgstr ""

#: users/models.py:515
msgid "Theme"
msgstr ""

#: users/models.py:516
msgid "Settings for the web UI as JSON - do not edit manually!"
msgstr ""

#: users/models.py:521
msgid "Widgets"
msgstr ""

#: users/models.py:523
msgid "Settings for the dashboard widgets as JSON - do not edit manually!"
msgstr ""

#: users/models.py:530
msgid "Display Name"
msgstr ""

#: users/models.py:531
msgid "Chosen display name for the user"
msgstr ""

#: users/models.py:537
msgid "Position"
msgstr ""

#: users/models.py:538
msgid "Main job title or position"
msgstr ""

#: users/models.py:545
msgid "User status message"
msgstr ""

#: users/models.py:552
msgid "User location information"
msgstr ""

#: users/models.py:557
msgid "User is actively using the system"
msgstr ""

#: users/models.py:564
msgid "Preferred contact information for the user"
msgstr ""

#: users/models.py:570
msgid "User Type"
msgstr ""

#: users/models.py:571
msgid "Which type of user is this?"
msgstr ""

#: users/models.py:577
msgid "Organisation"
msgstr ""

#: users/models.py:578
msgid "Users primary organisation/affiliation"
msgstr ""

#: users/models.py:586
msgid "Primary Group"
msgstr ""

#: users/models.py:587
msgid "Primary group for the user"
msgstr ""

#: users/ruleset.py:31
msgid "Admin"
msgstr "Quản trị"

#: users/ruleset.py:34
msgid "Stocktake"
msgstr "Kiểm kê"

#: users/ruleset.py:38
msgid "Purchase Orders"
msgstr "Đơn mua hàng"

#: users/ruleset.py:39
msgid "Sales Orders"
msgstr "Đơn hàng bán"

#: users/ruleset.py:40
msgid "Return Orders"
msgstr "Đơn hàng trả lại"

#: users/serializers.py:236
msgid "Username"
msgstr "Tên người dùng"

#: users/serializers.py:239
msgid "First Name"
msgstr "Tên"

#: users/serializers.py:239
msgid "First name of the user"
msgstr "Họ người dùng"

#: users/serializers.py:243
msgid "Last Name"
msgstr "Họ"

#: users/serializers.py:243
msgid "Last name of the user"
msgstr "Tên người dùng"

#: users/serializers.py:247
msgid "Email address of the user"
msgstr "Địa chỉ email của người dùng"

#: users/serializers.py:323
msgid "Staff"
msgstr "Nhân viên"

#: users/serializers.py:324
msgid "Does this user have staff permissions"
msgstr "Người dùng có quyền nhân viên"

#: users/serializers.py:329
msgid "Superuser"
msgstr "Superuser"

#: users/serializers.py:329
msgid "Is this user a superuser"
msgstr "Người dùng này là superuser"

#: users/serializers.py:333
msgid "Is this user account active"
msgstr "Tài khoản người dùng đang hoạt động"

#: users/serializers.py:345
msgid "Only a superuser can adjust this field"
msgstr ""

#: users/serializers.py:399
msgid "Only staff users can create new users"
msgstr ""

#: users/serializers.py:404
msgid "You do not have permission to create users"
msgstr ""

#: users/serializers.py:425
msgid "Your account has been created."
msgstr "Tài khoản của bạn đã được tạo."

#: users/serializers.py:427
msgid "Please use the password reset function to login"
msgstr "Xin hãy sử dụng chức năng tạo lại mật khẩu để đăng nhập"

#: users/serializers.py:433
msgid "Welcome to InvenTree"
msgstr "Chào mừng đến với InvenTree"

