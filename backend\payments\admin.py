"""
Django admin configuration for payments app
"""
from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils.safestring import mark_safe

from .models import Order, OrderItem, Payment, PaymentLog, PaymentCallback


class OrderItemInline(admin.TabularInline):
    """Inline admin for order items"""
    model = OrderItem
    extra = 0
    readonly_fields = ['total_price']
    
    def total_price(self, obj):
        if obj.id:
            return f"KSH {obj.total_price:,.2f}"
        return "-"


@admin.register(Order)
class OrderAdmin(admin.ModelAdmin):
    """Admin interface for orders"""
    list_display = [
        'order_reference', 'user', 'total_with_delivery_display', 
        'payment_status', 'status', 'created_at'
    ]
    list_filter = ['payment_status', 'status', 'delivery_county', 'created_at']
    search_fields = ['order_reference', 'user__username', 'user__email', 'pesapal_transaction_id']
    readonly_fields = [
        'order_reference', 'total_with_delivery_display', 'created_at', 
        'updated_at', 'paid_at', 'pesapal_transaction_id'
    ]
    inlines = [OrderItemInline]
    
    fieldsets = (
        ('Order Information', {
            'fields': ('order_reference', 'user', 'status', 'created_at', 'updated_at')
        }),
        ('Payment Information', {
            'fields': ('payment_status', 'payment_method', 'pesapal_transaction_id', 'paid_at')
        }),
        ('Amounts', {
            'fields': ('total_amount_ksh', 'delivery_cost_ksh', 'total_with_delivery_display')
        }),
        ('Delivery Information', {
            'fields': ('delivery_address', 'delivery_county')
        }),
    )
    
    def total_with_delivery_display(self, obj):
        return f"KSH {obj.total_with_delivery:,.2f}"
    total_with_delivery_display.short_description = 'Total with Delivery'
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('user')


@admin.register(Payment)
class PaymentAdmin(admin.ModelAdmin):
    """Admin interface for payments"""
    list_display = [
        'pesapal_transaction_id', 'order_link', 'amount_ksh_display', 
        'payment_method', 'status', 'created_at'
    ]
    list_filter = ['status', 'payment_method', 'created_at']
    search_fields = ['pesapal_transaction_id', 'pesapal_merchant_reference', 'order__order_reference']
    readonly_fields = ['pesapal_transaction_id', 'created_at', 'completed_at']
    
    def order_link(self, obj):
        if obj.order:
            url = reverse('admin:payments_order_change', args=[obj.order.id])
            return format_html('<a href="{}">{}</a>', url, obj.order.order_reference)
        return "-"
    order_link.short_description = 'Order'
    
    def amount_ksh_display(self, obj):
        return f"KSH {obj.amount_ksh:,.2f}"
    amount_ksh_display.short_description = 'Amount'


@admin.register(PaymentLog)
class PaymentLogAdmin(admin.ModelAdmin):
    """Admin interface for payment logs"""
    list_display = [
        'created_at', 'action', 'status', 'order_link', 
        'pesapal_transaction_id', 'message_short'
    ]
    list_filter = ['action', 'status', 'created_at']
    search_fields = ['pesapal_transaction_id', 'order__order_reference', 'message']
    readonly_fields = ['created_at']
    
    fieldsets = (
        ('Log Information', {
            'fields': ('created_at', 'action', 'status', 'message')
        }),
        ('Related Objects', {
            'fields': ('order', 'payment')
        }),
        ('Pesapal Information', {
            'fields': ('pesapal_transaction_id', 'pesapal_tracking_id')
        }),
        ('API Data', {
            'fields': ('request_data', 'response_data'),
            'classes': ('collapse',)
        }),
    )
    
    def order_link(self, obj):
        if obj.order:
            url = reverse('admin:payments_order_change', args=[obj.order.id])
            return format_html('<a href="{}">{}</a>', url, obj.order.order_reference)
        return "-"
    order_link.short_description = 'Order'
    
    def message_short(self, obj):
        return obj.message[:50] + "..." if len(obj.message) > 50 else obj.message
    message_short.short_description = 'Message'


@admin.register(PaymentCallback)
class PaymentCallbackAdmin(admin.ModelAdmin):
    """Admin interface for payment callbacks"""
    list_display = [
        'created_at', 'pesapal_notification_type', 'pesapal_transaction_tracking_id',
        'pesapal_merchant_reference', 'processed', 'processed_at'
    ]
    list_filter = ['pesapal_notification_type', 'processed', 'created_at']
    search_fields = ['pesapal_transaction_tracking_id', 'pesapal_merchant_reference']
    readonly_fields = ['created_at', 'processed_at']
    
    fieldsets = (
        ('Callback Information', {
            'fields': (
                'created_at', 'pesapal_notification_type', 
                'pesapal_transaction_tracking_id', 'pesapal_merchant_reference'
            )
        }),
        ('Processing Status', {
            'fields': ('processed', 'processed_at', 'error_message')
        }),
        ('Raw Data', {
            'fields': ('raw_data',),
            'classes': ('collapse',)
        }),
    )
    
    def get_queryset(self, request):
        return super().get_queryset(request).order_by('-created_at')
