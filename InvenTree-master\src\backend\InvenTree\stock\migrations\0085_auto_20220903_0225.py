# Generated by Django 3.2.15 on 2022-09-03 02:25

from django.db import migrations


def uid_to_barcode(apps, schama_editor):
    """Migrate old 'uid' field to new 'barcode_hash' field"""

    StockItem = apps.get_model('stock', 'stockitem')

    # Find all StockItem objects with non-empty UID field
    items = StockItem.objects.exclude(uid=None).exclude(uid='')

    for item in items:
        item.barcode_hash = item.uid
        item.save()

    if items.count() > 0:
        print(f"Updated barcode data for {items.count()} StockItem objects")

def barcode_to_uid(apps, schema_editor):
    """Migrate new 'barcode_hash' field to old 'uid' field"""

    StockItem = apps.get_model('stock', 'stockitem')

    # Find all StockItem objects with non-empty UID field
    items = StockItem.objects.exclude(barcode_hash=None).exclude(barcode_hash='')

    for item in items:
        item.uid = item.barcode_hash
        item.save()

    if items.count() > 0:
        print(f"Updated barcode data for {items.count()} StockItem objects")


class Migration(migrations.Migration):

    dependencies = [
        ('stock', '0084_auto_20220903_0154'),
    ]

    operations = [
        migrations.RunPython(
            uid_to_barcode,
            reverse_code=barcode_to_uid
        )
    ]
