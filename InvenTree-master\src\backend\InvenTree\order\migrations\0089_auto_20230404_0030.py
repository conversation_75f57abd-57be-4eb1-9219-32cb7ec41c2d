# Generated by Django 3.2.18 on 2023-04-04 00:30

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('order', '0088_auto_20230403_1402'),
    ]

    operations = [
        migrations.AddField(
            model_name='purchaseorder',
            name='barcode_data',
            field=models.CharField(blank=True, help_text='Third party barcode data', max_length=500, verbose_name='Barcode Data'),
        ),
        migrations.AddField(
            model_name='purchaseorder',
            name='barcode_hash',
            field=models.Char<PERSON>ield(blank=True, help_text='Unique hash of barcode data', max_length=128, verbose_name='Barcode Hash'),
        ),
        migrations.AddField(
            model_name='returnorder',
            name='barcode_data',
            field=models.CharField(blank=True, help_text='Third party barcode data', max_length=500, verbose_name='Barcode Data'),
        ),
        migrations.Add<PERSON>ield(
            model_name='returnorder',
            name='barcode_hash',
            field=models.Char<PERSON>ield(blank=True, help_text='Unique hash of barcode data', max_length=128, verbose_name='Barcode Hash'),
        ),
        migrations.AddField(
            model_name='salesorder',
            name='barcode_data',
            field=models.CharField(blank=True, help_text='Third party barcode data', max_length=500, verbose_name='Barcode Data'),
        ),
        migrations.AddField(
            model_name='salesorder',
            name='barcode_hash',
            field=models.CharField(blank=True, help_text='Unique hash of barcode data', max_length=128, verbose_name='Barcode Hash'),
        ),
    ]
