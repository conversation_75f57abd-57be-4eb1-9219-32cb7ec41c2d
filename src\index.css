
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 250 250 249;
    --foreground: 32 32 32;

    --card: 255 255 255;
    --card-foreground: 32 32 32;

    --popover: 255 255 255;
    --popover-foreground: 32 32 32;

    /* Updated primary color to industrial rust tone */
    --primary: 183 110 71;
    --primary-foreground: 255 255 255;

    /* Updated secondary color to steel gray */
    --secondary: 105 117 143;
    --secondary-foreground: 255 255 255;

    --muted: 244 244 245;
    --muted-foreground: 113 113 122;

    /* Updated accent to a warm brown */
    --accent: 229 215 200;
    --accent-foreground: 32 32 32;

    --destructive: 239 68 68;
    --destructive-foreground: 255 255 255;

    --border: 228 228 231;
    --input: 228 228 231;
    --ring: 183 110 71;

    --radius: 0.5rem;

    --sidebar-background: 255 255 255;
    --sidebar-foreground: 32 32 32;
    --sidebar-primary: 183 110 71;
    --sidebar-primary-foreground: 255 255 255;
    --sidebar-accent: 244 244 245;
    --sidebar-accent-foreground: 32 32 32;
    --sidebar-border: 228 228 231;
    --sidebar-ring: 183 110 71;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}

@layer components {
  .btn-primary {
    @apply bg-primary hover:bg-primary/90 text-white px-6 py-3 rounded-lg font-medium transition-all duration-200 hover:scale-105 hover:shadow-md;
  }
  
  .btn-secondary {
    @apply bg-forest-steel-500 hover:bg-forest-steel-600 text-white px-6 py-3 rounded-lg font-medium transition-all duration-200 hover:scale-105 hover:shadow-md;
  }

  .hardware-btn {
    @apply flex items-center justify-center gap-2 font-medium transition-all duration-200 rounded-lg hover:scale-105 hover:shadow-md;
  }
}
