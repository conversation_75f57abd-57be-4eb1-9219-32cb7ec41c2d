# Docker compose recipe for a production-ready InvenTree setup, with the following containers:
# - PostgreS<PERSON> as the database backend
# - gunicorn as the InvenTree web server
# - django-q as the InvenTree background worker process
# - Caddy as a reverse proxy
# - redis as the cache manager (optional, disabled by default)

# ---------------------
# READ BEFORE STARTING!
# ---------------------

# -----------------------------
# Setting environment variables
# -----------------------------
# Shared environment variables should be stored in the .env file
# Changes made to this file are reflected across all containers!
#
# IMPORTANT NOTE:
# You should not have to change *anything* within this docker-compose.yml file!
# Instead, make any changes in the .env file!

# ------------------------
# InvenTree Image Versions
# ------------------------
# By default, this docker-compose script targets the STABLE version of InvenTree,
# image: inventree/inventree:stable
#
# To run the LATEST (development) version of InvenTree,
# change the INVENTREE_TAG variable (in the .env file) to "latest"
#
# Alternatively, you could target a specific tagged release version with (for example):
# INVENTREE_TAG=0.7.5
#

services:
    # Database service
    # Use PostgreSQL as the database backend
    inventree-db:
        image: postgres:16
        container_name: inventree-db
        expose:
            - ${INVENTREE_DB_PORT:-5432}/tcp
        environment:
            - PGDATA=/var/lib/postgresql/data/pgdb
            - POSTGRES_USER=${INVENTREE_DB_USER:?You must provide the 'INVENTREE_DB_USER' variable in the .env file}
            - POSTGRES_PASSWORD=${INVENTREE_DB_PASSWORD:?You must provide the 'INVENTREE_DB_PASSWORD' variable in the .env file}
            - POSTGRES_DB=${INVENTREE_DB_NAME:?You must provide the 'INVENTREE_DB_NAME' variable in the .env file}
        volumes:
            # Map 'data' volume such that postgres database is stored externally
            - ${INVENTREE_EXT_VOLUME:?You must specify the 'INVENTREE_EXT_VOLUME' variable in the .env file!}:/var/lib/postgresql/data/:z
        restart: unless-stopped

    # redis acts as database cache manager
    inventree-cache:
        image: redis:7-alpine
        container_name: inventree-cache
        env_file:
            - .env
        expose:
            - ${INVENTREE_CACHE_PORT:-6379}
        restart: always

    # InvenTree web server service
    # Uses gunicorn as the web server
    inventree-server:
        # If you wish to specify a particular InvenTree version, do so here
        image: inventree/inventree:${INVENTREE_TAG:-stable}
        container_name: inventree-server
        # Only change this port if you understand the stack.
        expose:
            - 8000
        depends_on:
            - inventree-db
            - inventree-cache
        env_file:
            - .env
        volumes:
            # Data volume must map to /home/<USER>/data
            - ${INVENTREE_EXT_VOLUME}:/home/<USER>/data:z
        restart: unless-stopped

    # Background worker process handles long-running or periodic tasks
    inventree-worker:
        # If you wish to specify a particular InvenTree version, do so here
        image: inventree/inventree:${INVENTREE_TAG:-stable}
        container_name: inventree-worker
        command: invoke worker
        depends_on:
            - inventree-server
        env_file:
            - .env
        volumes:
            # Data volume must map to /home/<USER>/data
            - ${INVENTREE_EXT_VOLUME}:/home/<USER>/data:z
        restart: unless-stopped

    # caddy acts as reverse proxy and static file server
    # https://hub.docker.com/_/caddy
    inventree-proxy:
        container_name: inventree-proxy
        image: caddy:alpine
        restart: always
        depends_on:
            - inventree-server
        ports:
            - ${INVENTREE_WEB_PORT:-80}:80
            - 443:443
        env_file:
            - .env
        volumes:
            - ./Caddyfile:/etc/caddy/Caddyfile:ro,z
            - ${INVENTREE_EXT_VOLUME}/static:/var/www/static:z
            - ${INVENTREE_EXT_VOLUME}/media:/var/www/media:z
            - ${INVENTREE_EXT_VOLUME}:/var/log:z
            - ${INVENTREE_EXT_VOLUME}:/data:z
            - ${INVENTREE_EXT_VOLUME}:/config:z

    # alternative: run nginx as reverse proxy
    # inventree-proxy:
    #     container_name: inventree-proxy
    #     image: nginx:stable
    #     restart: always
    #     depends_on:
    #         - inventree-server
    #     ports:
    #         - ${INVENTREE_WEB_PORT:-80}:80
    #         - 443:443
    #     volumes:
    #         - ./nginx.conf:/etc/nginx/conf.d/default.conf:ro,z
    #         - ${INVENTREE_EXT_VOLUME}:/var/www:z
