import React, { useEffect, useState } from 'react';
import { useSearchParams, Link, useNavigate } from 'react-router-dom';
import { CheckCircle, XCircle, Clock, ArrowLeft, Package } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import Header from '@/components/Header';
import CurrencyFormatter from '@/components/ui/currency-formatter';

interface PaymentStatus {
  success: boolean;
  order?: {
    id: number;
    order_reference: string;
    total_amount_ksh: number;
    delivery_cost_ksh: number;
    total_with_delivery: number;
    payment_status: string;
    status: string;
    created_at: string;
    items: Array<{
      product_name: string;
      quantity: number;
      unit_price_ksh: number;
      total_price: number;
    }>;
  };
  error?: string;
}

const PaymentSuccess = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const [paymentStatus, setPaymentStatus] = useState<PaymentStatus | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const checkPaymentStatus = async () => {
      try {
        const transactionId = searchParams.get('pesapal_transaction_tracking_id') || 
                             searchParams.get('pesapal_merchant_reference');
        
        if (!transactionId) {
          setPaymentStatus({ success: false, error: 'No transaction ID found' });
          setIsLoading(false);
          return;
        }

        const token = localStorage.getItem('token');
        if (!token) {
          navigate('/login');
          return;
        }

        const response = await fetch(
          `http://localhost:8000/api/payments/status/${transactionId}/`,
          {
            headers: {
              'Authorization': `Bearer ${token}`,
            },
          }
        );

        const data = await response.json();
        setPaymentStatus(data);
      } catch (error) {
        console.error('Error checking payment status:', error);
        setPaymentStatus({ success: false, error: 'Failed to check payment status' });
      } finally {
        setIsLoading(false);
      }
    };

    checkPaymentStatus();
  }, [searchParams, navigate]);

  const getStatusIcon = () => {
    if (!paymentStatus?.order) return <XCircle className="w-16 h-16 text-red-500" />;
    
    switch (paymentStatus.order.payment_status) {
      case 'completed':
        return <CheckCircle className="w-16 h-16 text-green-500" />;
      case 'pending':
        return <Clock className="w-16 h-16 text-yellow-500" />;
      default:
        return <XCircle className="w-16 h-16 text-red-500" />;
    }
  };

  const getStatusMessage = () => {
    if (!paymentStatus?.order) {
      return {
        title: 'Payment Failed',
        message: paymentStatus?.error || 'Unable to process payment',
        color: 'text-red-600'
      };
    }

    switch (paymentStatus.order.payment_status) {
      case 'completed':
        return {
          title: 'Payment Successful!',
          message: 'Your order has been confirmed and will be processed shortly.',
          color: 'text-green-600'
        };
      case 'pending':
        return {
          title: 'Payment Pending',
          message: 'Your payment is being processed. You will receive a confirmation shortly.',
          color: 'text-yellow-600'
        };
      default:
        return {
          title: 'Payment Failed',
          message: 'Your payment could not be processed. Please try again.',
          color: 'text-red-600'
        };
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-forest-grey-50">
        <Header />
        <div className="container mx-auto px-4 py-8">
          <div className="flex justify-center items-center min-h-[400px]">
            <div className="text-center">
              <Clock className="w-16 h-16 text-forest-green-600 mx-auto mb-4 animate-spin" />
              <p className="text-lg text-forest-grey-600">Checking payment status...</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  const statusInfo = getStatusMessage();

  return (
    <div className="min-h-screen bg-forest-grey-50">
      <Header />
      
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-2xl mx-auto">
          {/* Status Card */}
          <Card className="mb-6">
            <CardContent className="pt-6">
              <div className="text-center">
                {getStatusIcon()}
                <h1 className={`text-2xl font-bold mt-4 ${statusInfo.color}`}>
                  {statusInfo.title}
                </h1>
                <p className="text-forest-grey-600 mt-2">
                  {statusInfo.message}
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Order Details */}
          {paymentStatus?.order && (
            <Card className="mb-6">
              <CardHeader>
                <CardTitle className="flex items-center text-forest-grey-800">
                  <Package className="w-5 h-5 mr-2" />
                  Order Details
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-forest-grey-600">Order Reference:</span>
                    <p className="font-medium">{paymentStatus.order.order_reference}</p>
                  </div>
                  <div>
                    <span className="text-forest-grey-600">Order Date:</span>
                    <p className="font-medium">
                      {new Date(paymentStatus.order.created_at).toLocaleDateString()}
                    </p>
                  </div>
                  <div>
                    <span className="text-forest-grey-600">Payment Status:</span>
                    <p className={`font-medium capitalize ${
                      paymentStatus.order.payment_status === 'completed' ? 'text-green-600' :
                      paymentStatus.order.payment_status === 'pending' ? 'text-yellow-600' :
                      'text-red-600'
                    }`}>
                      {paymentStatus.order.payment_status}
                    </p>
                  </div>
                  <div>
                    <span className="text-forest-grey-600">Order Status:</span>
                    <p className="font-medium capitalize">{paymentStatus.order.status}</p>
                  </div>
                </div>

                <Separator />

                {/* Order Items */}
                <div>
                  <h3 className="font-medium text-forest-grey-800 mb-3">Items Ordered</h3>
                  <div className="space-y-2">
                    {paymentStatus.order.items.map((item, index) => (
                      <div key={index} className="flex justify-between items-center">
                        <div className="flex-1">
                          <p className="font-medium text-forest-grey-800">{item.product_name}</p>
                          <p className="text-sm text-forest-grey-600">Qty: {item.quantity}</p>
                        </div>
                        <p className="font-medium text-forest-green-600">
                          <CurrencyFormatter amount={item.total_price} />
                        </p>
                      </div>
                    ))}
                  </div>
                </div>

                <Separator />

                {/* Order Total */}
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-forest-grey-600">Subtotal</span>
                    <span className="font-medium">
                      <CurrencyFormatter amount={paymentStatus.order.total_amount_ksh} />
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-forest-grey-600">Delivery</span>
                    <span className="font-medium">
                      <CurrencyFormatter amount={paymentStatus.order.delivery_cost_ksh} />
                    </span>
                  </div>
                  <Separator />
                  <div className="flex justify-between text-lg font-bold">
                    <span className="text-forest-grey-800">Total</span>
                    <span className="text-forest-green-600">
                      <CurrencyFormatter amount={paymentStatus.order.total_with_delivery} />
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-4">
            <Button
              asChild
              variant="outline"
              className="flex-1"
            >
              <Link to="/" className="flex items-center justify-center">
                <ArrowLeft className="w-4 h-4 mr-2" />
                Continue Shopping
              </Link>
            </Button>
            
            {paymentStatus?.order && (
              <Button
                asChild
                className="flex-1 bg-forest-green-600 hover:bg-forest-green-700"
              >
                <Link to="/orders" className="flex items-center justify-center">
                  <Package className="w-4 h-4 mr-2" />
                  View Orders
                </Link>
              </Button>
            )}
          </div>

          {/* Support Information */}
          <Card className="mt-6">
            <CardContent className="pt-6">
              <div className="text-center text-sm text-forest-grey-600">
                <p>Need help with your order?</p>
                <Link 
                  to="/contact" 
                  className="text-forest-green-600 hover:text-forest-green-700 font-medium"
                >
                  Contact our support team
                </Link>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default PaymentSuccess;
