# Generated by Django 2.2.5 on 2019-09-08 09:16

from django.db import migrations
import django.db.models.deletion
import mptt.fields


class Migration(migrations.Migration):

    dependencies = [
        ('part', '0020_auto_20190908_0404'),
    ]

    operations = [
        migrations.AlterField(
            model_name='part',
            name='category',
            field=mptt.fields.TreeForeignKey(blank=True, help_text='Part category', null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='parts', to='part.PartCategory'),
        ),
        migrations.AlterField(
            model_name='part',
            name='default_location',
            field=mptt.fields.TreeForeignKey(blank=True, help_text='Where is this item normally stored?', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='default_parts', to='stock.StockLocation'),
        ),
        migrations.<PERSON>er<PERSON><PERSON>(
            model_name='partcategory',
            name='default_location',
            field=mptt.fields.TreeForeignKey(blank=True, help_text='Default location for parts in this category', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='default_categories', to='stock.StockLocation'),
        ),
    ]
