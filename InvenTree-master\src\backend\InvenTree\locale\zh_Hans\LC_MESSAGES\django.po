msgid ""
msgstr ""
"Project-Id-Version: inventree\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-09 19:12+0000\n"
"PO-Revision-Date: 2025-05-14 11:50\n"
"Last-Translator: \n"
"Language-Team: Chinese Simplified\n"
"Language: zh_CN\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"X-Crowdin-Project: inventree\n"
"X-Crowdin-Project-ID: 452300\n"
"X-Crowdin-Language: zh-CN\n"
"X-Crowdin-File: /src/backend/InvenTree/locale/en/LC_MESSAGES/django.po\n"
"X-Crowdin-File-ID: 250\n"

#: InvenTree/AllUserRequire2FAMiddleware.py:42
msgid "You must enable two-factor authentication before doing anything else."
msgstr "您必须启用双重身份验证才能进行后续操作。"

#: InvenTree/api.py:355
msgid "API endpoint not found"
msgstr "未找到 API 端点"

#: InvenTree/api.py:432
msgid "List of items or filters must be provided for bulk operation"
msgstr ""

#: InvenTree/api.py:439
msgid "Items must be provided as a list"
msgstr "必须以列表形式提供项目"

#: InvenTree/api.py:447
msgid "Invalid items list provided"
msgstr "提供了无效的单位"

#: InvenTree/api.py:453
msgid "Filters must be provided as a dict"
msgstr "必须以字典形式提供筛选器"

#: InvenTree/api.py:460
msgid "Invalid filters provided"
msgstr "提供了无效的过滤器"

#: InvenTree/api.py:465
msgid "All filter must only be used with true"
msgstr ""

#: InvenTree/api.py:470
msgid "No items match the provided criteria"
msgstr "没有符合所供条件的项目"

#: InvenTree/api.py:713
msgid "User does not have permission to view this model"
msgstr "用户没有权限查阅当前模型。"

#: InvenTree/auth_overrides.py:57
msgid "Email (again)"
msgstr "电子邮件 (重复)"

#: InvenTree/auth_overrides.py:61
msgid "Email address confirmation"
msgstr "邮箱地址已确认"

#: InvenTree/auth_overrides.py:84
msgid "You must type the same email each time."
msgstr "您必须每次输入相同的电子邮件。"

#: InvenTree/auth_overrides.py:126 InvenTree/auth_overrides.py:134
msgid "The provided primary email address is not valid."
msgstr "提供的主电子邮件地址无效。"

#: InvenTree/auth_overrides.py:140
msgid "The provided email domain is not approved."
msgstr "提供的邮箱域名未被批准。"

#: InvenTree/conversion.py:162
#, python-brace-format
msgid "Invalid unit provided ({unit})"
msgstr "提供了无效的单位 ({unit})"

#: InvenTree/conversion.py:179
msgid "No value provided"
msgstr "没有提供数值"

#: InvenTree/conversion.py:206
#, python-brace-format
msgid "Could not convert {original} to {unit}"
msgstr "不能将 {original} 转换到 {unit}"

#: InvenTree/conversion.py:208 InvenTree/conversion.py:222
#: InvenTree/helpers.py:548 order/models.py:707 order/models.py:975
msgid "Invalid quantity provided"
msgstr "提供的数量无效"

#: InvenTree/exceptions.py:109
msgid "Error details can be found in the admin panel"
msgstr "在管理面板中可以找到错误详细信息"

#: InvenTree/fields.py:138
msgid "Enter date"
msgstr "输入日期"

#: InvenTree/fields.py:161
msgid "Invalid decimal value"
msgstr "无效的数值"

#: InvenTree/fields.py:210 InvenTree/models.py:887 build/serializers.py:526
#: build/serializers.py:597 company/models.py:834 order/models.py:1579
#: part/models.py:3366
#: report/templates/report/inventree_build_order_report.html:172
#: stock/models.py:2708 stock/models.py:2832 stock/serializers.py:756
#: stock/serializers.py:918 stock/serializers.py:1048 stock/serializers.py:1100
#: stock/serializers.py:1419 stock/serializers.py:1508
#: stock/serializers.py:1684
msgid "Notes"
msgstr "备注"

#: InvenTree/format.py:166
#, python-brace-format
msgid "Value '{name}' does not appear in pattern format"
msgstr "值' {name}' 未出现在模式格式中"

#: InvenTree/format.py:177
msgid "Provided value does not match required pattern: "
msgstr "提供的值与所需模式不匹配："

#: InvenTree/helpers.py:552
msgid "Cannot serialize more than 1000 items at once"
msgstr "无法一次序列化超过 1000 个项目"

#: InvenTree/helpers.py:558
msgid "Empty serial number string"
msgstr "序號為空白"

#: InvenTree/helpers.py:587
msgid "Duplicate serial"
msgstr "复制序列号"

#: InvenTree/helpers.py:619 InvenTree/helpers.py:662 InvenTree/helpers.py:680
#: InvenTree/helpers.py:687 InvenTree/helpers.py:706
#, python-brace-format
msgid "Invalid group: {group}"
msgstr "无效群组: {group}"

#: InvenTree/helpers.py:650
#, python-brace-format
msgid "Group range {group} exceeds allowed quantity ({expected_quantity})"
msgstr "组范围 {group} 超出了允许的数量 ({expected_quantity})"

#: InvenTree/helpers.py:716
msgid "No serial numbers found"
msgstr "未找到序列号"

#: InvenTree/helpers.py:721
msgid "Number of unique serial numbers ({len(serials)}) must match quantity ({expected_quantity})"
msgstr "唯一序列号的数量 ({len(serials)}) 必须与数量匹配 ({expected_quantity})"

#: InvenTree/helpers.py:840
msgid "Remove HTML tags from this value"
msgstr "从这个值中删除 HTML 标签"

#: InvenTree/helpers.py:919
msgid "Data contains prohibited markdown content"
msgstr "数据包含禁止的 markdown 内容"

#: InvenTree/helpers_model.py:131
msgid "Connection error"
msgstr "连接错误"

#: InvenTree/helpers_model.py:136 InvenTree/helpers_model.py:143
msgid "Server responded with invalid status code"
msgstr "服务器响应状态码无效"

#: InvenTree/helpers_model.py:139
msgid "Exception occurred"
msgstr "发生异常"

#: InvenTree/helpers_model.py:149
msgid "Server responded with invalid Content-Length value"
msgstr "服务器响应的内容长度值无效"

#: InvenTree/helpers_model.py:152
msgid "Image size is too large"
msgstr "图片尺寸过大"

#: InvenTree/helpers_model.py:164
msgid "Image download exceeded maximum size"
msgstr "图片下载超出最大尺寸"

#: InvenTree/helpers_model.py:169
msgid "Remote server returned empty response"
msgstr "远程服务器返回了空响应"

#: InvenTree/helpers_model.py:177
msgid "Supplied URL is not a valid image file"
msgstr "提供的 URL 不是一个有效的图片文件"

#: InvenTree/locales.py:20
msgid "Arabic"
msgstr "阿拉伯语"

#: InvenTree/locales.py:21
msgid "Bulgarian"
msgstr "Bulgarian"

#: InvenTree/locales.py:22
msgid "Czech"
msgstr "Czech"

#: InvenTree/locales.py:23
msgid "Danish"
msgstr "Danish"

#: InvenTree/locales.py:24
msgid "German"
msgstr "German"

#: InvenTree/locales.py:25
msgid "Greek"
msgstr "Greek"

#: InvenTree/locales.py:26
msgid "English"
msgstr "English"

#: InvenTree/locales.py:27
msgid "Spanish"
msgstr "Spanish"

#: InvenTree/locales.py:28
msgid "Spanish (Mexican)"
msgstr "Spanish (Mexican)"

#: InvenTree/locales.py:29
msgid "Estonian"
msgstr "爱沙尼亚语"

#: InvenTree/locales.py:30
msgid "Farsi / Persian"
msgstr "Farsi / Persian"

#: InvenTree/locales.py:31
msgid "Finnish"
msgstr "Finnish"

#: InvenTree/locales.py:32
msgid "French"
msgstr "French"

#: InvenTree/locales.py:33
msgid "Hebrew"
msgstr "Hebrew"

#: InvenTree/locales.py:34
msgid "Hindi"
msgstr "Hindi"

#: InvenTree/locales.py:35
msgid "Hungarian"
msgstr "Hungarian"

#: InvenTree/locales.py:36
msgid "Italian"
msgstr "Italian"

#: InvenTree/locales.py:37
msgid "Japanese"
msgstr "Japanese"

#: InvenTree/locales.py:38
msgid "Korean"
msgstr "Korean"

#: InvenTree/locales.py:39
msgid "Lithuanian"
msgstr "立陶宛语"

#: InvenTree/locales.py:40
msgid "Latvian"
msgstr "Latvian"

#: InvenTree/locales.py:41
msgid "Dutch"
msgstr "Dutch"

#: InvenTree/locales.py:42
msgid "Norwegian"
msgstr "Norwegian"

#: InvenTree/locales.py:43
msgid "Polish"
msgstr "Polish"

#: InvenTree/locales.py:44
msgid "Portuguese"
msgstr "Portuguese"

#: InvenTree/locales.py:45
msgid "Portuguese (Brazilian)"
msgstr "Portuguese (Brazilian)"

#: InvenTree/locales.py:46
msgid "Romanian"
msgstr "罗马尼亚语"

#: InvenTree/locales.py:47
msgid "Russian"
msgstr "Russian"

#: InvenTree/locales.py:48
msgid "Slovak"
msgstr "Slovak"

#: InvenTree/locales.py:49
msgid "Slovenian"
msgstr "Slovenian"

#: InvenTree/locales.py:50
msgid "Serbian"
msgstr "Serbian"

#: InvenTree/locales.py:51
msgid "Swedish"
msgstr "Swedish"

#: InvenTree/locales.py:52
msgid "Thai"
msgstr "Thai"

#: InvenTree/locales.py:53
msgid "Turkish"
msgstr "Turkish"

#: InvenTree/locales.py:54
msgid "Ukrainian"
msgstr "乌克兰语"

#: InvenTree/locales.py:55
msgid "Vietnamese"
msgstr "Vietnamese"

#: InvenTree/locales.py:56
msgid "Chinese (Simplified)"
msgstr "中文 (简体)"

#: InvenTree/locales.py:57
msgid "Chinese (Traditional)"
msgstr "中文 (繁体)"

#: InvenTree/magic_login.py:31
msgid "Log in to the app"
msgstr "登录应用程序"

#: InvenTree/magic_login.py:41 company/models.py:136 users/serializers.py:247
msgid "Email"
msgstr "电子邮件"

#: InvenTree/models.py:107
msgid "Error running plugin validation"
msgstr "驗證外掛程式時發生錯誤"

#: InvenTree/models.py:184
msgid "Metadata must be a python dict object"
msgstr "Metadata必須是一個Python Dictionary物件"

#: InvenTree/models.py:190
msgid "Plugin Metadata"
msgstr "外掛程式Metadata"

#: InvenTree/models.py:191
msgid "JSON metadata field, for use by external plugins"
msgstr "外掛程式使用的JSON Metadata欄位"

#: InvenTree/models.py:374
msgid "Improperly formatted pattern"
msgstr "格式錯誤"

#: InvenTree/models.py:381
msgid "Unknown format key specified"
msgstr "指定了不明的格式鍵值"

#: InvenTree/models.py:387
msgid "Missing required format key"
msgstr "缺少必須的格式鍵值"

#: InvenTree/models.py:398
msgid "Reference field cannot be empty"
msgstr "參考欄位不能空白"

#: InvenTree/models.py:406
msgid "Reference must match required pattern"
msgstr "參考欄位並須符合格式"

#: InvenTree/models.py:437
msgid "Reference number is too large"
msgstr "參考編號過大"

#: InvenTree/models.py:688
msgid "Duplicate names cannot exist under the same parent"
msgstr "同一個上層元件下不能有重複的名字"

#: InvenTree/models.py:705
msgid "Invalid choice"
msgstr "無效的選項"

#: InvenTree/models.py:734 common/models.py:1325 common/models.py:1752
#: common/models.py:2011 common/models.py:2136 common/serializers.py:488
#: company/models.py:593 generic/states/serializers.py:20 machine/models.py:24
#: part/models.py:1039 part/models.py:3837 plugin/models.py:53
#: report/models.py:203 stock/models.py:83
msgid "Name"
msgstr "名稱"

#: InvenTree/models.py:740 build/models.py:242 common/models.py:123
#: common/models.py:2143 common/models.py:2256 company/models.py:521
#: company/models.py:825 order/models.py:429 order/models.py:1615
#: part/models.py:1062 part/models.py:3852 report/models.py:209
#: report/models.py:714 report/models.py:740
#: report/templates/report/inventree_build_order_report.html:117
#: stock/models.py:89
msgid "Description"
msgstr "描述"

#: InvenTree/models.py:741 stock/models.py:90
msgid "Description (optional)"
msgstr "描述（選填）"

#: InvenTree/models.py:756 common/models.py:2309
msgid "Path"
msgstr "路径"

#: InvenTree/models.py:887
msgid "Markdown notes (optional)"
msgstr "Markdown 註記（選填）"

#: InvenTree/models.py:918
msgid "Barcode Data"
msgstr "條碼資料"

#: InvenTree/models.py:919
msgid "Third party barcode data"
msgstr "第三方條碼資料"

#: InvenTree/models.py:925
msgid "Barcode Hash"
msgstr "條碼雜湊值"

#: InvenTree/models.py:926
msgid "Unique hash of barcode data"
msgstr "條碼資料的唯一雜湊值"

#: InvenTree/models.py:1003
msgid "Existing barcode found"
msgstr "發現現有條碼"

#: InvenTree/models.py:1084
msgid "Task Failure"
msgstr "任务失败"

#: InvenTree/models.py:1085
#, python-brace-format
msgid "Background worker task '{f}' failed after {n} attempts"
msgstr "后台工作任务“{f}”在 {n} 次尝试后失败"

#: InvenTree/models.py:1112
msgid "Server Error"
msgstr "伺服器錯誤"

#: InvenTree/models.py:1113
msgid "An error has been logged by the server."
msgstr "伺服器紀錄了一個錯誤。"

#: InvenTree/serializers.py:69 part/models.py:4455
msgid "Must be a valid number"
msgstr "必须是有效数字"

#: InvenTree/serializers.py:111 company/models.py:187 part/models.py:3184
msgid "Currency"
msgstr "貨幣"

#: InvenTree/serializers.py:114 part/serializers.py:1335
msgid "Select currency from available options"
msgstr "從可用選項中選擇貨幣"

#: InvenTree/serializers.py:466
msgid "Invalid value"
msgstr "无效值"

#: InvenTree/serializers.py:503
msgid "Remote Image"
msgstr "远程图片"

#: InvenTree/serializers.py:504
msgid "URL of remote image file"
msgstr "远程图片文件的 URL"

#: InvenTree/serializers.py:522
msgid "Downloading images from remote URL is not enabled"
msgstr "未启用从远程 URL下载图片"

#: InvenTree/serializers.py:529
msgid "Failed to download image from remote URL"
msgstr "从远程URL下载图像失败"

#: InvenTree/validators.py:30
msgid "Invalid physical unit"
msgstr "無效的物理單位"

#: InvenTree/validators.py:36
msgid "Not a valid currency code"
msgstr "無效的貨幣代碼"

#: InvenTree/validators.py:116 InvenTree/validators.py:132
msgid "Overage value must not be negative"
msgstr "損失值不能為負"

#: InvenTree/validators.py:134
msgid "Overage must not exceed 100%"
msgstr "損失率不能超過100%"

#: InvenTree/validators.py:140
msgid "Invalid value for overage"
msgstr "無效的損失值"

#: build/api.py:39 order/api.py:104 order/api.py:263 order/serializers.py:128
msgid "Order Status"
msgstr "订单状态"

#: build/api.py:65 build/models.py:254
msgid "Parent Build"
msgstr "上層生產工單"

#: build/api.py:69 build/api.py:768 order/api.py:512 order/api.py:732
#: order/api.py:1106 order/api.py:1334 stock/api.py:522
msgid "Include Variants"
msgstr "包含变体"

#: build/api.py:85 build/api.py:470 build/api.py:782 build/models.py:260
#: build/serializers.py:1254 build/serializers.py:1376
#: build/serializers.py:1427 company/models.py:1044 company/serializers.py:432
#: order/api.py:291 order/api.py:295 order/api.py:880 order/api.py:1119
#: order/api.py:1122 order/models.py:1704 order/models.py:1863
#: order/models.py:1864 part/api.py:1448 part/api.py:1451 part/api.py:1517
#: part/api.py:1827 part/models.py:457 part/models.py:3195 part/models.py:3339
#: part/models.py:3487 part/models.py:3508 part/models.py:3530
#: part/models.py:3666 part/models.py:4027 part/models.py:4299
#: part/models.py:4665 part/serializers.py:1255 part/serializers.py:1924
#: report/templates/report/inventree_bill_of_materials_report.html:110
#: report/templates/report/inventree_bill_of_materials_report.html:137
#: report/templates/report/inventree_build_order_report.html:109
#: report/templates/report/inventree_purchase_order_report.html:27
#: report/templates/report/inventree_return_order_report.html:24
#: report/templates/report/inventree_sales_order_report.html:27
#: report/templates/report/inventree_sales_order_shipment_report.html:28
#: report/templates/report/inventree_stock_location_report.html:102
#: stock/api.py:535 stock/serializers.py:119 stock/serializers.py:171
#: stock/serializers.py:481 stock/serializers.py:643 stock/serializers.py:948
#: templates/email/build_order_completed.html:17
#: templates/email/build_order_required_stock.html:17
#: templates/email/low_stock_notification.html:15
#: templates/email/overdue_build_order.html:16
#: templates/email/part_event_notification.html:15
msgid "Part"
msgstr "零件"

#: build/api.py:105 build/api.py:108 part/api.py:1531 part/models.py:1080
#: part/models.py:3558 part/models.py:4136 part/serializers.py:1265
#: stock/api.py:818
msgid "Category"
msgstr "类别"

#: build/api.py:116 build/api.py:120
msgid "Ancestor Build"
msgstr "可测试部分"

#: build/api.py:137 order/api.py:122
msgid "Assigned to me"
msgstr "分配给我"

#: build/api.py:152
#: report/templates/report/inventree_build_order_report.html:152
msgid "Issued By"
msgstr "发布者"

#: build/api.py:167
msgid "Assigned To"
msgstr "负责人"

#: build/api.py:202
msgid "Created before"
msgstr ""

#: build/api.py:206
msgid "Created after"
msgstr ""

#: build/api.py:210
msgid "Has start date"
msgstr ""

#: build/api.py:218
msgid "Start date before"
msgstr ""

#: build/api.py:222
msgid "Start date after"
msgstr ""

#: build/api.py:226
msgid "Has target date"
msgstr ""

#: build/api.py:234
msgid "Target date before"
msgstr ""

#: build/api.py:238
msgid "Target date after"
msgstr ""

#: build/api.py:242
msgid "Completed before"
msgstr ""

#: build/api.py:246
msgid "Completed after"
msgstr ""

#: build/api.py:249 order/api.py:219
msgid "Min Date"
msgstr ""

#: build/api.py:272 order/api.py:238
msgid "Max Date"
msgstr ""

#: build/api.py:297 build/api.py:300 part/api.py:224
msgid "Exclude Tree"
msgstr "排除树"

#: build/api.py:409
msgid "Build must be cancelled before it can be deleted"
msgstr "工單必須被取消才能被刪除"

#: build/api.py:453 build/serializers.py:1392 part/models.py:4333
msgid "Consumable"
msgstr "耗材"

#: build/api.py:456 build/serializers.py:1395 part/models.py:4327
msgid "Optional"
msgstr "非必須項目"

#: build/api.py:459 common/setting/system.py:429 part/models.py:1211
#: part/serializers.py:1663 part/serializers.py:1672 stock/api.py:588
msgid "Assembly"
msgstr "装配"

#: build/api.py:462
msgid "Tracked"
msgstr "追蹤中"

#: build/api.py:465 build/serializers.py:1398 part/models.py:1229
msgid "Testable"
msgstr "可测试"

#: build/api.py:475 order/api.py:925
msgid "Order Outstanding"
msgstr "未结算订单"

#: build/api.py:485 order/api.py:884
msgid "Allocated"
msgstr "已分配"

#: build/api.py:495 company/models.py:889 company/serializers.py:427
#: templates/email/build_order_required_stock.html:19
#: templates/email/low_stock_notification.html:17
#: templates/email/part_event_notification.html:18
msgid "Available"
msgstr "可用数量"

#: build/api.py:805 build/models.py:116
#: report/templates/report/inventree_build_order_report.html:105
#: stock/serializers.py:92 templates/email/build_order_completed.html:16
#: templates/email/overdue_build_order.html:15
msgid "Build Order"
msgstr "生产订单"

#: build/models.py:117 users/ruleset.py:37
msgid "Build Orders"
msgstr "生产订单"

#: build/models.py:162
msgid "Assembly BOM has not been validated"
msgstr "装配物料清单尚未验证"

#: build/models.py:169
msgid "Build order cannot be created for an inactive part"
msgstr "无法为未激活的零件创建生产订单"

#: build/models.py:176
msgid "Build order cannot be created for an unlocked part"
msgstr "无法为已解锁的零件创建生产订单"

#: build/models.py:188
msgid "Invalid choice for parent build"
msgstr "無效的上層生產工單選擇"

#: build/models.py:197 order/models.py:363
msgid "Responsible user or group must be specified"
msgstr "必须指定负责的用户或组"

#: build/models.py:202
msgid "Build order part cannot be changed"
msgstr "無法更改生產工單"

#: build/models.py:207 order/models.py:376
msgid "Target date must be after start date"
msgstr ""

#: build/models.py:235
msgid "Build Order Reference"
msgstr "生產工單代號"

#: build/models.py:236 build/serializers.py:1389 order/models.py:601
#: order/models.py:1140 order/models.py:1572 order/models.py:2403
#: part/models.py:4348
#: report/templates/report/inventree_bill_of_materials_report.html:139
#: report/templates/report/inventree_purchase_order_report.html:28
#: report/templates/report/inventree_return_order_report.html:26
#: report/templates/report/inventree_sales_order_report.html:28
msgid "Reference"
msgstr "參考代號"

#: build/models.py:245
msgid "Brief description of the build (optional)"
msgstr "關於生產工單的簡單說明（選填）"

#: build/models.py:255
msgid "BuildOrder to which this build is allocated"
msgstr "這張生產工單對應的上層生產工單"

#: build/models.py:264
msgid "Select part to build"
msgstr "選擇要生產的零件"

#: build/models.py:269
msgid "Sales Order Reference"
msgstr "銷售訂單代號"

#: build/models.py:274
msgid "SalesOrder to which this build is allocated"
msgstr "這張生產工單對應的銷售訂單"

#: build/models.py:279 build/serializers.py:1104
msgid "Source Location"
msgstr "來源倉儲地點"

#: build/models.py:285
msgid "Select location to take stock from for this build (leave blank to take from any stock location)"
msgstr "選擇領取料件的倉儲地點（留白表示可以從任何地點領取）"

#: build/models.py:291
msgid "Destination Location"
msgstr "目標倉儲地點"

#: build/models.py:296
msgid "Select location where the completed items will be stored"
msgstr "选择已完成项目库存地点"

#: build/models.py:300
msgid "Build Quantity"
msgstr "生产数量"

#: build/models.py:303
msgid "Number of stock items to build"
msgstr "要生产的项目数量"

#: build/models.py:307
msgid "Completed items"
msgstr "已完成项目"

#: build/models.py:309
msgid "Number of stock items which have been completed"
msgstr "已經完成的庫存品數量"

#: build/models.py:313
msgid "Build Status"
msgstr "生產狀態"

#: build/models.py:318
msgid "Build status code"
msgstr "生產狀態代碼"

#: build/models.py:327 build/serializers.py:376 order/serializers.py:785
#: stock/models.py:1023 stock/serializers.py:84 stock/serializers.py:1651
msgid "Batch Code"
msgstr "批号"

#: build/models.py:331 build/serializers.py:377
msgid "Batch code for this build output"
msgstr "此产出的批号"

#: build/models.py:335 order/models.py:466 order/serializers.py:166
#: part/models.py:1286
msgid "Creation Date"
msgstr "建立日期"

#: build/models.py:341
msgid "Build start date"
msgstr ""

#: build/models.py:342
msgid "Scheduled start date for this build order"
msgstr ""

#: build/models.py:348
msgid "Target completion date"
msgstr "目標完成日期"

#: build/models.py:350
msgid "Target date for build completion. Build will be overdue after this date."
msgstr "生產的預計完成日期。若超過此日期則工單會逾期。"

#: build/models.py:355 order/models.py:654 order/models.py:2442
msgid "Completion Date"
msgstr "完成日期"

#: build/models.py:363
msgid "completed by"
msgstr "完成者"

#: build/models.py:372
msgid "Issued by"
msgstr "發布者"

#: build/models.py:373
msgid "User who issued this build order"
msgstr "發布此生產工單的使用者"

#: build/models.py:382 common/models.py:132 order/api.py:172
#: order/models.py:491 part/models.py:1303
#: report/templates/report/inventree_build_order_report.html:158
msgid "Responsible"
msgstr "負責人"

#: build/models.py:383
msgid "User or group responsible for this build order"
msgstr "負責此生產工單的使用者或群組"

#: build/models.py:388 stock/models.py:1016
msgid "External Link"
msgstr "外部連結"

#: build/models.py:390 common/models.py:1899 part/models.py:1114
#: stock/models.py:1018
msgid "Link to external URL"
msgstr "外部URL連結"

#: build/models.py:395
msgid "Build Priority"
msgstr "製造優先度"

#: build/models.py:398
msgid "Priority of this build order"
msgstr "此生产订单的优先级"

#: build/models.py:406 common/models.py:102 common/models.py:116
#: order/api.py:158 order/models.py:438
msgid "Project Code"
msgstr "專案代碼"

#: build/models.py:407
msgid "Project code for this build order"
msgstr "此生產工單隸屬的專案代碼"

#: build/models.py:680 build/models.py:803
msgid "Failed to offload task to complete build allocations"
msgstr "未能卸载任务以完成生产分配"

#: build/models.py:703
#, python-brace-format
msgid "Build order {build} has been completed"
msgstr "生產工單 {build} 已經完成"

#: build/models.py:709
msgid "A build order has been completed"
msgstr "一張生產工單已經完成"

#: build/models.py:881 build/serializers.py:424
msgid "Serial numbers must be provided for trackable parts"
msgstr "对于可跟踪的零件，必须提供序列号"

#: build/models.py:991 build/models.py:1076
msgid "No build output specified"
msgstr "未指定产出"

#: build/models.py:994
msgid "Build output is already completed"
msgstr "产出已完成"

#: build/models.py:997
msgid "Build output does not match Build Order"
msgstr "产出与生产订单不匹配"

#: build/models.py:1079 build/serializers.py:303 build/serializers.py:352
#: build/serializers.py:972 order/models.py:704 order/serializers.py:601
#: order/serializers.py:780 part/serializers.py:1657 stock/models.py:863
#: stock/models.py:1730 stock/serializers.py:727
msgid "Quantity must be greater than zero"
msgstr "數量必須大於零"

#: build/models.py:1083 build/serializers.py:307
msgid "Quantity cannot be greater than the output quantity"
msgstr "数量不能大于输出数量"

#: build/models.py:1148 build/serializers.py:615
#, python-brace-format
msgid "Build output {serial} has not passed all required tests"
msgstr "产出 {serial} 未通过所有必要测试"

#: build/models.py:1534
msgid "Build Order Line Item"
msgstr "生产订单行项目"

#: build/models.py:1558
msgid "Build object"
msgstr "生产对象"

#: build/models.py:1570 build/models.py:1829 build/serializers.py:291
#: build/serializers.py:337 build/serializers.py:1410 common/models.py:1255
#: order/models.py:1555 order/models.py:2288 order/serializers.py:1670
#: order/serializers.py:2131 part/models.py:3353 part/models.py:4321
#: part/serializers.py:275
#: report/templates/report/inventree_bill_of_materials_report.html:138
#: report/templates/report/inventree_build_order_report.html:113
#: report/templates/report/inventree_purchase_order_report.html:29
#: report/templates/report/inventree_sales_order_report.html:29
#: report/templates/report/inventree_sales_order_shipment_report.html:47
#: report/templates/report/inventree_sales_order_shipment_report.html:49
#: report/templates/report/inventree_stock_location_report.html:104
#: report/templates/report/inventree_test_report.html:90
#: report/templates/report/inventree_test_report.html:169
#: stock/serializers.py:135 stock/serializers.py:179 stock/serializers.py:718
#: templates/email/build_order_completed.html:18
msgid "Quantity"
msgstr "數量"

#: build/models.py:1571
msgid "Required quantity for build order"
msgstr "生產工單所需數量"

#: build/models.py:1655
msgid "Build item must specify a build output, as master part is marked as trackable"
msgstr "生产项必须指定产出，因为主零件已经被标记为可追踪的"

#: build/models.py:1666
#, python-brace-format
msgid "Allocated quantity ({q}) must not exceed available stock quantity ({a})"
msgstr "分配的數量（{q}）不能超過可用的庫存數量（{a}）"

#: build/models.py:1687 order/models.py:2237
msgid "Stock item is over-allocated"
msgstr "庫存品項超額分配"

#: build/models.py:1692 order/models.py:2240
msgid "Allocation quantity must be greater than zero"
msgstr "分配的數量必須大於零"

#: build/models.py:1698
msgid "Quantity must be 1 for serialized stock"
msgstr "有序號的品項數量必須為1"

#: build/models.py:1758
msgid "Selected stock item does not match BOM line"
msgstr "選擇的庫存品項和BOM的項目不符"

#: build/models.py:1819 build/serializers.py:955 build/serializers.py:1263
#: order/serializers.py:1507 order/serializers.py:1528
#: report/templates/report/inventree_sales_order_shipment_report.html:29
#: stock/models.py:452 stock/serializers.py:101 stock/serializers.py:825
#: stock/serializers.py:1357 stock/serializers.py:1469
msgid "Stock Item"
msgstr "庫存品項"

#: build/models.py:1820
msgid "Source stock item"
msgstr "來源庫存項目"

#: build/models.py:1830
msgid "Stock quantity to allocate to build"
msgstr "要分配的庫存數量"

#: build/models.py:1839
msgid "Install into"
msgstr "安裝到"

#: build/models.py:1840
msgid "Destination stock item"
msgstr "目的庫存品項"

#: build/serializers.py:116
msgid "Build Level"
msgstr "生产等级"

#: build/serializers.py:125 part/stocktake.py:219
msgid "Part Name"
msgstr "零件名称"

#: build/serializers.py:143
msgid "Project Code Label"
msgstr "项目编码标签"

#: build/serializers.py:155
msgid "Create Child Builds"
msgstr "新建子生产项目"

#: build/serializers.py:156
msgid "Automatically generate child build orders"
msgstr "自动生成子生成工单"

#: build/serializers.py:239 build/serializers.py:981
msgid "Build Output"
msgstr "产出"

#: build/serializers.py:251
msgid "Build output does not match the parent build"
msgstr "产出与之前的生产不匹配"

#: build/serializers.py:255
msgid "Output part does not match BuildOrder part"
msgstr "产出零件与生产订单零件不匹配"

#: build/serializers.py:259
msgid "This build output has already been completed"
msgstr "此产出已经完成"

#: build/serializers.py:273
msgid "This build output is not fully allocated"
msgstr "此产出尚未完全分配"

#: build/serializers.py:292 build/serializers.py:338
msgid "Enter quantity for build output"
msgstr "输入产出数量"

#: build/serializers.py:360
msgid "Integer quantity required for trackable parts"
msgstr "可追蹤的零件數量必須為整數"

#: build/serializers.py:366
msgid "Integer quantity required, as the bill of materials contains trackable parts"
msgstr "因為BOM包含可追蹤的零件，所以數量必須為整數"

#: build/serializers.py:383 order/serializers.py:801 order/serializers.py:1674
#: stock/serializers.py:738
msgid "Serial Numbers"
msgstr "序號"

#: build/serializers.py:384
msgid "Enter serial numbers for build outputs"
msgstr "输出产出的序列号"

#: build/serializers.py:389 build/serializers.py:514 build/serializers.py:584
#: build/serializers.py:1267 build/serializers.py:1271 order/serializers.py:769
#: order/serializers.py:916 order/serializers.py:2022 part/serializers.py:1275
#: stock/serializers.py:110 stock/serializers.py:647 stock/serializers.py:749
#: stock/serializers.py:913 stock/serializers.py:1041 stock/serializers.py:1501
#: stock/serializers.py:1789 users/models.py:551
msgid "Location"
msgstr "地點"

#: build/serializers.py:390
msgid "Stock location for build output"
msgstr "生产输出的库存地点"

#: build/serializers.py:405
msgid "Auto Allocate Serial Numbers"
msgstr "自動分配序號"

#: build/serializers.py:407
msgid "Automatically allocate required items with matching serial numbers"
msgstr "自動為需要項目分配對應的序號"

#: build/serializers.py:440 order/serializers.py:894 stock/api.py:1077
#: stock/models.py:1753
msgid "The following serial numbers already exist or are invalid"
msgstr "序號已存在或無效"

#: build/serializers.py:482 build/serializers.py:538 build/serializers.py:622
msgid "A list of build outputs must be provided"
msgstr "必须提供产出清单"

#: build/serializers.py:515
msgid "Stock location for scrapped outputs"
msgstr "废品产出的库存位置"

#: build/serializers.py:521
msgid "Discard Allocations"
msgstr "放棄分配"

#: build/serializers.py:522
msgid "Discard any stock allocations for scrapped outputs"
msgstr "取消对废品产出的任何库存分配"

#: build/serializers.py:527
msgid "Reason for scrapping build output(s)"
msgstr "废品产出的原因"

#: build/serializers.py:585
msgid "Location for completed build outputs"
msgstr "已完成删除的库存地点"

#: build/serializers.py:593
msgid "Accept Incomplete Allocation"
msgstr "接受不完整的分配"

#: build/serializers.py:594
msgid "Complete outputs if stock has not been fully allocated"
msgstr "如果库存尚未全部分配，则完成产出"

#: build/serializers.py:709
msgid "Consume Allocated Stock"
msgstr "消费已分配的库存"

#: build/serializers.py:710
msgid "Consume any stock which has already been allocated to this build"
msgstr "消耗已分配给此生产的任何库存"

#: build/serializers.py:716
msgid "Remove Incomplete Outputs"
msgstr "移除未完成的产出"

#: build/serializers.py:717
msgid "Delete any build outputs which have not been completed"
msgstr "删除所有未完成的产出"

#: build/serializers.py:744
msgid "Not permitted"
msgstr "不允许"

#: build/serializers.py:745
msgid "Accept as consumed by this build order"
msgstr "接受作为此生产订单的消费"

#: build/serializers.py:746
msgid "Deallocate before completing this build order"
msgstr "完成此生产订单前取消分配"

#: build/serializers.py:773
msgid "Overallocated Stock"
msgstr "超出分配的库存"

#: build/serializers.py:776
msgid "How do you want to handle extra stock items assigned to the build order"
msgstr "如何处理分配给生产订单的额外库存项"

#: build/serializers.py:787
msgid "Some stock items have been overallocated"
msgstr "有库存项目已被过度分配"

#: build/serializers.py:792
msgid "Accept Unallocated"
msgstr "接受未分配"

#: build/serializers.py:794
msgid "Accept that stock items have not been fully allocated to this build order"
msgstr "接受库存项未被完全分配至生产订单"

#: build/serializers.py:805
msgid "Required stock has not been fully allocated"
msgstr "所需库存尚未完全分配"

#: build/serializers.py:810 order/serializers.py:444 order/serializers.py:1575
msgid "Accept Incomplete"
msgstr "接受不完整"

#: build/serializers.py:812
msgid "Accept that the required number of build outputs have not been completed"
msgstr "允许所需数量的产出未完成"

#: build/serializers.py:823
msgid "Required build quantity has not been completed"
msgstr "未完成所需生产数量"

#: build/serializers.py:835
msgid "Build order has open child build orders"
msgstr "生产订单有打开的子生产订单"

#: build/serializers.py:838
msgid "Build order must be in production state"
msgstr "生产订单必须处于生产状态"

#: build/serializers.py:841
msgid "Build order has incomplete outputs"
msgstr "生产订单有未完成的产出"

#: build/serializers.py:880
msgid "Build Line"
msgstr "生产行"

#: build/serializers.py:888
msgid "Build output"
msgstr "产出"

#: build/serializers.py:896
msgid "Build output must point to the same build"
msgstr "生产产出必须指向相同的生产"

#: build/serializers.py:927
msgid "Build Line Item"
msgstr "生产行项目"

#: build/serializers.py:945
msgid "bom_item.part must point to the same part as the build order"
msgstr "bom_item.part 必须与生产订单零件相同"

#: build/serializers.py:961 stock/serializers.py:1370
msgid "Item must be in stock"
msgstr "项目必须在库存中"

#: build/serializers.py:1004 order/serializers.py:1561
#, python-brace-format
msgid "Available quantity ({q}) exceeded"
msgstr "可用量 ({q}) 超出限制"

#: build/serializers.py:1010
msgid "Build output must be specified for allocation of tracked parts"
msgstr "对于被追踪的零件的分配，必须指定生产产出"

#: build/serializers.py:1018
msgid "Build output cannot be specified for allocation of untracked parts"
msgstr "对于未被追踪的零件，无法指定生产产出"

#: build/serializers.py:1042 order/serializers.py:1834
msgid "Allocation items must be provided"
msgstr "必须提供分配项目"

#: build/serializers.py:1106
msgid "Stock location where parts are to be sourced (leave blank to take from any location)"
msgstr "零件来源的库存地点(留空则可来源于任何库存地点)"

#: build/serializers.py:1115
msgid "Exclude Location"
msgstr "排除位置"

#: build/serializers.py:1116
msgid "Exclude stock items from this selected location"
msgstr "从该选定的库存地点排除库存项"

#: build/serializers.py:1121
msgid "Interchangeable Stock"
msgstr "可互換庫存"

#: build/serializers.py:1122
msgid "Stock items in multiple locations can be used interchangeably"
msgstr "在多个位置的库存项目可以互换使用"

#: build/serializers.py:1127
msgid "Substitute Stock"
msgstr "替代品库存"

#: build/serializers.py:1128
msgid "Allow allocation of substitute parts"
msgstr "允许分配可替换的零件"

#: build/serializers.py:1133
msgid "Optional Items"
msgstr "可选项目"

#: build/serializers.py:1134
msgid "Allocate optional BOM items to build order"
msgstr "分配可选的物料清单给生产订单"

#: build/serializers.py:1156
msgid "Failed to start auto-allocation task"
msgstr "启动自动分配任务失败"

#: build/serializers.py:1230
msgid "BOM Reference"
msgstr "物料清单参考"

#: build/serializers.py:1236
msgid "BOM Part ID"
msgstr "物料清单零件识别号码"

#: build/serializers.py:1243
msgid "BOM Part Name"
msgstr "物料清单零件名称"

#: build/serializers.py:1278 build/serializers.py:1434
msgid "Build"
msgstr ""

#: build/serializers.py:1286 company/models.py:662 order/api.py:304
#: order/api.py:309 order/api.py:508 order/serializers.py:593
#: stock/models.py:959 stock/serializers.py:631
msgid "Supplier Part"
msgstr "供应商零件"

#: build/serializers.py:1294 stock/serializers.py:662
msgid "Allocated Quantity"
msgstr "已分配数量"

#: build/serializers.py:1371
msgid "Build Reference"
msgstr "构建参考"

#: build/serializers.py:1381
msgid "Part Category Name"
msgstr "零件类别名称"

#: build/serializers.py:1401 common/setting/system.py:453 part/models.py:1223
msgid "Trackable"
msgstr "可追踪"

#: build/serializers.py:1404
msgid "Inherited"
msgstr "已继承的"

#: build/serializers.py:1407 part/models.py:4381
msgid "Allow Variants"
msgstr "允许变体"

#: build/serializers.py:1412 build/serializers.py:1416 part/models.py:4179
#: part/models.py:4657 stock/api.py:831
msgid "BOM Item"
msgstr "物料清单项"

#: build/serializers.py:1445
msgid "Allocated Stock"
msgstr "分配库存"

#: build/serializers.py:1447 company/serializers.py:424
#: order/serializers.py:1271 part/serializers.py:943 part/serializers.py:1690
msgid "On Order"
msgstr "已订购"

#: build/serializers.py:1449 order/serializers.py:1272 part/serializers.py:1694
msgid "In Production"
msgstr "生产中"

#: build/serializers.py:1451 part/serializers.py:964
msgid "External Stock"
msgstr "外部库存"

#: build/serializers.py:1452 part/serializers.py:1723
msgid "Available Stock"
msgstr "可用库存"

#: build/serializers.py:1454
msgid "Available Substitute Stock"
msgstr "可用的替代品库存"

#: build/serializers.py:1457
msgid "Available Variant Stock"
msgstr "可用的变体库存"

#: build/status_codes.py:11 generic/states/tests.py:21
#: generic/states/tests.py:131 order/status_codes.py:12
#: order/status_codes.py:44 order/status_codes.py:76 order/status_codes.py:102
msgid "Pending"
msgstr "待定"

#: build/status_codes.py:12
msgid "Production"
msgstr "生產"

#: build/status_codes.py:13 order/status_codes.py:14 order/status_codes.py:51
#: order/status_codes.py:81
msgid "On Hold"
msgstr "被挂起"

#: build/status_codes.py:14 order/status_codes.py:16 order/status_codes.py:53
#: order/status_codes.py:84
msgid "Cancelled"
msgstr "已取消"

#: build/status_codes.py:15 generic/states/tests.py:23 importer/models.py:529
#: importer/status_codes.py:27 order/status_codes.py:15
#: order/status_codes.py:52 order/status_codes.py:83
msgid "Complete"
msgstr "完成"

#: build/tasks.py:193
msgid "Stock required for build order"
msgstr "生产订单所需库存"

#: build/tasks.py:267
msgid "Overdue Build Order"
msgstr "逾期的生产订单"

#: build/tasks.py:272
#, python-brace-format
msgid "Build order {bo} is now overdue"
msgstr "生产订单 {bo} 现已逾期"

#: common/api.py:710
msgid "Is Link"
msgstr "是否链接"

#: common/api.py:718
msgid "Is File"
msgstr "是否为文件"

#: common/api.py:761
msgid "User does not have permission to delete these attachments"
msgstr "用户没有权限删除此附件"

#: common/api.py:778
msgid "User does not have permission to delete this attachment"
msgstr "用户没有权限删除此附件"

#: common/currency.py:120
msgid "Invalid currency code"
msgstr "无效的货币代码"

#: common/currency.py:122
msgid "Duplicate currency code"
msgstr "重复的货币代码"

#: common/currency.py:127
msgid "No valid currency codes provided"
msgstr "未提供有效的货币代码"

#: common/currency.py:144
msgid "No plugin"
msgstr "暂无插件"

#: common/models.py:89
msgid "Updated"
msgstr "已是最新"

#: common/models.py:90
msgid "Timestamp of last update"
msgstr "最后更新时间戳"

#: common/models.py:117
msgid "Unique project code"
msgstr "唯一项目编码"

#: common/models.py:124
msgid "Project description"
msgstr "项目描述"

#: common/models.py:133
msgid "User or group responsible for this project"
msgstr "负责此项目的用户或群组"

#: common/models.py:721 common/models.py:1187 common/models.py:1225
msgid "Settings key"
msgstr "设置密钥"

#: common/models.py:725
msgid "Settings value"
msgstr "设定值"

#: common/models.py:780
msgid "Chosen value is not a valid option"
msgstr "所选值不是一个有效的选项"

#: common/models.py:796
msgid "Value must be a boolean value"
msgstr "该值必须是布尔值"

#: common/models.py:804
msgid "Value must be an integer value"
msgstr "该值必须为整数"

#: common/models.py:812
msgid "Value must be a valid number"
msgstr "必须是有效数字"

#: common/models.py:837
msgid "Value does not pass validation checks"
msgstr "值未通过验证检查"

#: common/models.py:859
msgid "Key string must be unique"
msgstr "键字符串必须是唯一的"

#: common/models.py:1233 common/models.py:1234 common/models.py:1338
#: common/models.py:1339 common/models.py:1584 common/models.py:1585
#: common/models.py:1915 common/models.py:1916 common/models.py:2297
#: importer/models.py:97 part/models.py:3376 part/models.py:3463
#: part/models.py:3537 part/models.py:3565 plugin/models.py:322
#: plugin/models.py:323 report/templates/report/inventree_test_report.html:105
#: users/models.py:130 users/models.py:503
msgid "User"
msgstr "使用者"

#: common/models.py:1256
msgid "Price break quantity"
msgstr "批发价数量"

#: common/models.py:1263 company/serializers.py:567 order/models.py:1632
#: order/models.py:2734
msgid "Price"
msgstr "价格"

#: common/models.py:1264
msgid "Unit price at specified quantity"
msgstr "指定数量的单位价格"

#: common/models.py:1315 common/models.py:1500
msgid "Endpoint"
msgstr "端点"

#: common/models.py:1316
msgid "Endpoint at which this webhook is received"
msgstr "接收此网络钩子的端点"

#: common/models.py:1326
msgid "Name for this webhook"
msgstr "此网络钩子的名称"

#: common/models.py:1330 common/models.py:2156 common/models.py:2263
#: company/models.py:164 company/models.py:799 machine/models.py:39
#: part/models.py:1246 plugin/models.py:68 stock/api.py:591 users/models.py:201
#: users/models.py:556 users/serializers.py:333
msgid "Active"
msgstr "激活"

#: common/models.py:1330
msgid "Is this webhook active"
msgstr "网络钩子是否已启用"

#: common/models.py:1346 users/models.py:178
msgid "Token"
msgstr "令牌"

#: common/models.py:1347
msgid "Token for access"
msgstr "访问令牌"

#: common/models.py:1355
msgid "Secret"
msgstr "密钥"

#: common/models.py:1356
msgid "Shared secret for HMAC"
msgstr "HMAC共享密钥"

#: common/models.py:1464
msgid "Message ID"
msgstr "消息ID"

#: common/models.py:1465
msgid "Unique identifier for this message"
msgstr "此邮件的唯一标识符"

#: common/models.py:1473
msgid "Host"
msgstr "主机"

#: common/models.py:1474
msgid "Host from which this message was received"
msgstr "接收此消息的主机"

#: common/models.py:1482
msgid "Header"
msgstr "标题"

#: common/models.py:1483
msgid "Header of this message"
msgstr "此消息的标题"

#: common/models.py:1490
msgid "Body"
msgstr "正文"

#: common/models.py:1491
msgid "Body of this message"
msgstr "此消息的正文"

#: common/models.py:1501
msgid "Endpoint on which this message was received"
msgstr "接收此消息的终点"

#: common/models.py:1506
msgid "Worked on"
msgstr "工作于"

#: common/models.py:1507
msgid "Was the work on this message finished?"
msgstr "这条消息的工作完成了吗？"

#: common/models.py:1633
msgid "Id"
msgstr "标识"

#: common/models.py:1635 part/serializers.py:281
msgid "Title"
msgstr "标题"

#: common/models.py:1637 common/models.py:1898 company/models.py:149
#: company/models.py:445 company/models.py:512 company/models.py:816
#: order/models.py:444 order/models.py:1585 order/models.py:2056
#: part/models.py:1113
#: report/templates/report/inventree_build_order_report.html:164
msgid "Link"
msgstr "連結"

#: common/models.py:1639
msgid "Published"
msgstr "已发布"

#: common/models.py:1641
msgid "Author"
msgstr "作者"

#: common/models.py:1643
msgid "Summary"
msgstr "摘要"

#: common/models.py:1646
msgid "Read"
msgstr "阅读"

#: common/models.py:1646
msgid "Was this news item read?"
msgstr "这条新闻被阅读了吗？"

#: common/models.py:1663 company/models.py:160 part/models.py:1124
#: report/templates/report/inventree_bill_of_materials_report.html:126
#: report/templates/report/inventree_bill_of_materials_report.html:148
#: report/templates/report/inventree_return_order_report.html:35
msgid "Image"
msgstr "图像"

#: common/models.py:1663
msgid "Image file"
msgstr "图像文件"

#: common/models.py:1675
msgid "Target model type for this image"
msgstr "此图像的目标模型类型"

#: common/models.py:1679
msgid "Target model ID for this image"
msgstr "此图像的目标型号ID"

#: common/models.py:1701
msgid "Custom Unit"
msgstr "自定义单位"

#: common/models.py:1719
msgid "Unit symbol must be unique"
msgstr "单位符号必须唯一"

#: common/models.py:1734
msgid "Unit name must be a valid identifier"
msgstr "单位名称必须是有效的标识符"

#: common/models.py:1753
msgid "Unit name"
msgstr "单位名称"

#: common/models.py:1760
msgid "Symbol"
msgstr "符号"

#: common/models.py:1761
msgid "Optional unit symbol"
msgstr "可选单位符号"

#: common/models.py:1767
msgid "Definition"
msgstr "定义"

#: common/models.py:1768
msgid "Unit definition"
msgstr "单位定义"

#: common/models.py:1826 common/models.py:1889 stock/models.py:2827
#: stock/serializers.py:258
msgid "Attachment"
msgstr "附件"

#: common/models.py:1843
msgid "Missing file"
msgstr "缺少檔案"

#: common/models.py:1844
msgid "Missing external link"
msgstr "缺少外部連結"

#: common/models.py:1881
msgid "Model type"
msgstr ""

#: common/models.py:1882
msgid "Target model type for image"
msgstr ""

#: common/models.py:1890
msgid "Select file to attach"
msgstr "選擇附件"

#: common/models.py:1906
msgid "Comment"
msgstr "註解"

#: common/models.py:1907
msgid "Attachment comment"
msgstr "附件评论"

#: common/models.py:1923
msgid "Upload date"
msgstr "上传日期"

#: common/models.py:1924
msgid "Date the file was uploaded"
msgstr "上传文件的日期"

#: common/models.py:1928
msgid "File size"
msgstr "文件大小"

#: common/models.py:1928
msgid "File size in bytes"
msgstr "文件大小，以字节为单位"

#: common/models.py:1966 common/serializers.py:637
msgid "Invalid model type specified for attachment"
msgstr "为附件指定的模型类型无效"

#: common/models.py:1987
msgid "Custom State"
msgstr "自定状态"

#: common/models.py:1988
msgid "Custom States"
msgstr "定制状态"

#: common/models.py:1993
msgid "Reference Status Set"
msgstr "参考状态设置"

#: common/models.py:1994
msgid "Status set that is extended with this custom state"
msgstr "使用此自定义状态扩展状态的状态集"

#: common/models.py:1998 generic/states/serializers.py:18
msgid "Logical Key"
msgstr "逻辑密钥"

#: common/models.py:2000
msgid "State logical key that is equal to this custom state in business logic"
msgstr "等同于商业逻辑中自定义状态的状态逻辑键"

#: common/models.py:2005 common/models.py:2244 company/models.py:600
#: report/templates/report/inventree_test_report.html:104 stock/models.py:2819
msgid "Value"
msgstr "值"

#: common/models.py:2006
msgid "Numerical value that will be saved in the models database"
msgstr ""

#: common/models.py:2012
msgid "Name of the state"
msgstr "状态名"

#: common/models.py:2021 common/models.py:2250 generic/states/serializers.py:22
#: part/serializers.py:283
msgid "Label"
msgstr "标签"

#: common/models.py:2022
msgid "Label that will be displayed in the frontend"
msgstr "将在前端显示的标签"

#: common/models.py:2029 generic/states/serializers.py:24
msgid "Color"
msgstr "颜色"

#: common/models.py:2030
msgid "Color that will be displayed in the frontend"
msgstr "将在前端显示颜色"

#: common/models.py:2038 part/serializers.py:285
msgid "Model"
msgstr "型号"

#: common/models.py:2039
msgid "Model this state is associated with"
msgstr "该状态关联的模型"

#: common/models.py:2054
msgid "Model must be selected"
msgstr "必须选定模型"

#: common/models.py:2057
msgid "Key must be selected"
msgstr "必须选取密钥"

#: common/models.py:2060
msgid "Logical key must be selected"
msgstr "必须选中逻辑密钥"

#: common/models.py:2064
msgid "Key must be different from logical key"
msgstr "密钥必须不同于逻辑密钥"

#: common/models.py:2071
msgid "Valid reference status class must be provided"
msgstr ""

#: common/models.py:2077
msgid "Key must be different from the logical keys of the reference status"
msgstr "密钥必须不同于参考状态的逻辑密钥"

#: common/models.py:2084
msgid "Logical key must be in the logical keys of the reference status"
msgstr "逻辑密钥必须在参考状态的逻辑键中"

#: common/models.py:2091
msgid "Name must be different from the names of the reference status"
msgstr ""

#: common/models.py:2131 common/models.py:2238 part/models.py:3876
msgid "Selection List"
msgstr ""

#: common/models.py:2132
msgid "Selection Lists"
msgstr ""

#: common/models.py:2137
msgid "Name of the selection list"
msgstr ""

#: common/models.py:2144
msgid "Description of the selection list"
msgstr ""

#: common/models.py:2150 part/models.py:1251
msgid "Locked"
msgstr "已锁定"

#: common/models.py:2151
msgid "Is this selection list locked?"
msgstr ""

#: common/models.py:2157
msgid "Can this selection list be used?"
msgstr ""

#: common/models.py:2165
msgid "Source Plugin"
msgstr ""

#: common/models.py:2166
msgid "Plugin which provides the selection list"
msgstr ""

#: common/models.py:2171
msgid "Source String"
msgstr ""

#: common/models.py:2172
msgid "Optional string identifying the source used for this list"
msgstr ""

#: common/models.py:2181
msgid "Default Entry"
msgstr ""

#: common/models.py:2182
msgid "Default entry for this selection list"
msgstr ""

#: common/models.py:2187
msgid "Created"
msgstr "已创建"

#: common/models.py:2188
msgid "Date and time that the selection list was created"
msgstr ""

#: common/models.py:2193
msgid "Last Updated"
msgstr "最近更新"

#: common/models.py:2194
msgid "Date and time that the selection list was last updated"
msgstr ""

#: common/models.py:2228
msgid "Selection List Entry"
msgstr ""

#: common/models.py:2229
msgid "Selection List Entries"
msgstr ""

#: common/models.py:2239
msgid "Selection list to which this entry belongs"
msgstr ""

#: common/models.py:2245
msgid "Value of the selection list entry"
msgstr ""

#: common/models.py:2251
msgid "Label for the selection list entry"
msgstr ""

#: common/models.py:2257
msgid "Description of the selection list entry"
msgstr ""

#: common/models.py:2264
msgid "Is this selection list entry active?"
msgstr ""

#: common/models.py:2282
msgid "Barcode Scan"
msgstr "扫描条码"

#: common/models.py:2286 importer/models.py:523 part/models.py:4041
msgid "Data"
msgstr "数据"

#: common/models.py:2287
msgid "Barcode data"
msgstr "条码数据"

#: common/models.py:2298
msgid "User who scanned the barcode"
msgstr "扫描条形码"

#: common/models.py:2303 importer/models.py:66
msgid "Timestamp"
msgstr "时间戳"

#: common/models.py:2304
msgid "Date and time of the barcode scan"
msgstr "扫描条形码的日期和时间"

#: common/models.py:2310
msgid "URL endpoint which processed the barcode"
msgstr "处理条形码的 URL 终点"

#: common/models.py:2317 order/models.py:1622 plugin/serializers.py:93
msgid "Context"
msgstr "上下文"

#: common/models.py:2318
msgid "Context data for the barcode scan"
msgstr "扫描条形码的上下文数据"

#: common/models.py:2325
msgid "Response"
msgstr "响应"

#: common/models.py:2326
msgid "Response data from the barcode scan"
msgstr "扫描条形码的响应数据"

#: common/models.py:2332 report/templates/report/inventree_test_report.html:103
#: stock/models.py:2813
msgid "Result"
msgstr "结果"

#: common/models.py:2333
msgid "Was the barcode scan successful?"
msgstr "条码扫描成功吗？"

#: common/notifications.py:332
#, python-brace-format
msgid "New {verbose_name}"
msgstr "新建{verbose_name}"

#: common/notifications.py:334
msgid "A new order has been created and assigned to you"
msgstr "新订单已创建并分配给您"

#: common/notifications.py:340
#, python-brace-format
msgid "{verbose_name} canceled"
msgstr "{verbose_name} 已取消"

#: common/notifications.py:342
msgid "A order that is assigned to you was canceled"
msgstr "分配给您的订单已取消"

#: common/notifications.py:348 common/notifications.py:355 order/api.py:559
msgid "Items Received"
msgstr "收到的物品"

#: common/notifications.py:350
msgid "Items have been received against a purchase order"
msgstr "已根据采购订单收到物品"

#: common/notifications.py:357
msgid "Items have been received against a return order"
msgstr "已收到退货订单中的物品"

#: common/notifications.py:505
msgid "Error raised by plugin"
msgstr "插件引发的错误"

#: common/serializers.py:451
msgid "Is Running"
msgstr "正在运行"

#: common/serializers.py:457
msgid "Pending Tasks"
msgstr "等待完成的任务"

#: common/serializers.py:463
msgid "Scheduled Tasks"
msgstr "预定的任务"

#: common/serializers.py:469
msgid "Failed Tasks"
msgstr "失败的任务"

#: common/serializers.py:484
msgid "Task ID"
msgstr "任务ID"

#: common/serializers.py:484
msgid "Unique task ID"
msgstr "唯一任务ID"

#: common/serializers.py:486
msgid "Lock"
msgstr "锁定"

#: common/serializers.py:486
msgid "Lock time"
msgstr "锁定时间"

#: common/serializers.py:488
msgid "Task name"
msgstr "任务名称"

#: common/serializers.py:490
msgid "Function"
msgstr "功能"

#: common/serializers.py:490
msgid "Function name"
msgstr "功能名称"

#: common/serializers.py:492
msgid "Arguments"
msgstr "参数"

#: common/serializers.py:492
msgid "Task arguments"
msgstr "任务参数"

#: common/serializers.py:495
msgid "Keyword Arguments"
msgstr "关键字参数"

#: common/serializers.py:495
msgid "Task keyword arguments"
msgstr "任务关键词参数"

#: common/serializers.py:605
msgid "Filename"
msgstr "檔案名稱"

#: common/serializers.py:612 importer/models.py:86 report/api.py:41
#: report/models.py:277 report/serializers.py:53
msgid "Model Type"
msgstr "模型类型"

#: common/serializers.py:640
msgid "User does not have permission to create or edit attachments for this model"
msgstr "用户无权为此模式创建或编辑附件"

#: common/serializers.py:684 common/serializers.py:787
msgid "Selection list is locked"
msgstr ""

#: common/setting/system.py:97
msgid "No group"
msgstr "无分组"

#: common/setting/system.py:156
msgid "Site URL is locked by configuration"
msgstr "网站 URL 已配置为锁定"

#: common/setting/system.py:167
msgid "Restart required"
msgstr "需要重启"

#: common/setting/system.py:168
msgid "A setting has been changed which requires a server restart"
msgstr "设置已更改，需要服务器重启"

#: common/setting/system.py:174
msgid "Pending migrations"
msgstr "等待迁移"

#: common/setting/system.py:175
msgid "Number of pending database migrations"
msgstr "待处理的数据库迁移数"

#: common/setting/system.py:180
msgid "Instance ID"
msgstr ""

#: common/setting/system.py:181
msgid "Unique identifier for this InvenTree instance"
msgstr ""

#: common/setting/system.py:186
msgid "Announce ID"
msgstr ""

#: common/setting/system.py:188
msgid "Announce the instance ID of the server in the server status info (unauthenticated)"
msgstr ""

#: common/setting/system.py:194
msgid "Server Instance Name"
msgstr "服务器实例名称"

#: common/setting/system.py:196
msgid "String descriptor for the server instance"
msgstr "服务器实例的字符串描述符"

#: common/setting/system.py:200
msgid "Use instance name"
msgstr "使用实例名称"

#: common/setting/system.py:201
msgid "Use the instance name in the title-bar"
msgstr "在标题栏中使用实例名称"

#: common/setting/system.py:206
msgid "Restrict showing `about`"
msgstr "限制显示 `关于` 信息"

#: common/setting/system.py:207
msgid "Show the `about` modal only to superusers"
msgstr "只向超级管理员显示关于信息"

#: common/setting/system.py:212 company/models.py:108 company/models.py:109
msgid "Company name"
msgstr "公司名称"

#: common/setting/system.py:213
msgid "Internal company name"
msgstr "内部公司名称"

#: common/setting/system.py:217
msgid "Base URL"
msgstr "基本 URL"

#: common/setting/system.py:218
msgid "Base URL for server instance"
msgstr "服务器实例的基准 URL"

#: common/setting/system.py:224
msgid "Default Currency"
msgstr "默认货币单位"

#: common/setting/system.py:225
msgid "Select base currency for pricing calculations"
msgstr "选择价格计算的默认货币"

#: common/setting/system.py:231
msgid "Supported Currencies"
msgstr "支持币种"

#: common/setting/system.py:232
msgid "List of supported currency codes"
msgstr "支持的货币代码列表"

#: common/setting/system.py:238
msgid "Currency Update Interval"
msgstr "货币更新间隔时间"

#: common/setting/system.py:239
msgid "How often to update exchange rates (set to zero to disable)"
msgstr "检查更新的频率(设置为零以禁用)"

#: common/setting/system.py:241 common/setting/system.py:281
#: common/setting/system.py:294 common/setting/system.py:302
#: common/setting/system.py:309 common/setting/system.py:318
#: common/setting/system.py:567 common/setting/system.py:587
#: common/setting/system.py:684 common/setting/system.py:1068
msgid "days"
msgstr "天"

#: common/setting/system.py:245
msgid "Currency Update Plugin"
msgstr "币种更新插件"

#: common/setting/system.py:246
msgid "Currency update plugin to use"
msgstr "使用货币更新插件"

#: common/setting/system.py:251
msgid "Download from URL"
msgstr "从URL下载"

#: common/setting/system.py:252
msgid "Allow download of remote images and files from external URL"
msgstr "允许从外部 URL 下载远程图片和文件"

#: common/setting/system.py:257
msgid "Download Size Limit"
msgstr "下载大小限制"

#: common/setting/system.py:258
msgid "Maximum allowable download size for remote image"
msgstr "远程图片的最大允许下载大小"

#: common/setting/system.py:264
msgid "User-agent used to download from URL"
msgstr "用于从 URL 下载的 User-agent"

#: common/setting/system.py:266
msgid "Allow to override the user-agent used to download images and files from external URL (leave blank for the default)"
msgstr "允许覆盖用于从外部 URL 下载图片和文件的 user-agent(留空为默认值)"

#: common/setting/system.py:271
msgid "Strict URL Validation"
msgstr "严格的 URL 验证"

#: common/setting/system.py:272
msgid "Require schema specification when validating URLs"
msgstr "验证 URL 时需要 schema 规范"

#: common/setting/system.py:277
msgid "Update Check Interval"
msgstr "更新检查间隔"

#: common/setting/system.py:278
msgid "How often to check for updates (set to zero to disable)"
msgstr "检查更新的频率(设置为零以禁用)"

#: common/setting/system.py:284
msgid "Automatic Backup"
msgstr "自動備份"

#: common/setting/system.py:285
msgid "Enable automatic backup of database and media files"
msgstr "啟動資料庫和媒體文件自動備份"

#: common/setting/system.py:290
msgid "Auto Backup Interval"
msgstr "自動備份間隔"

#: common/setting/system.py:291
msgid "Specify number of days between automated backup events"
msgstr "指定自动备份之间的间隔天数"

#: common/setting/system.py:297
msgid "Task Deletion Interval"
msgstr "任务删除间隔"

#: common/setting/system.py:299
msgid "Background task results will be deleted after specified number of days"
msgstr "后台任务结果将在指定天数后删除"

#: common/setting/system.py:306
msgid "Error Log Deletion Interval"
msgstr "错误日志删除间隔"

#: common/setting/system.py:307
msgid "Error logs will be deleted after specified number of days"
msgstr "错误日志将在指定天数后被删除"

#: common/setting/system.py:313
msgid "Notification Deletion Interval"
msgstr "通知删除间隔"

#: common/setting/system.py:315
msgid "User notifications will be deleted after specified number of days"
msgstr "用户通知将在指定天数后被删除"

#: common/setting/system.py:322
msgid "Barcode Support"
msgstr "条形码支持"

#: common/setting/system.py:323
msgid "Enable barcode scanner support in the web interface"
msgstr "在网页界面启用条形码扫描器支持"

#: common/setting/system.py:328
msgid "Store Barcode Results"
msgstr "存储条码结果"

#: common/setting/system.py:329
msgid "Store barcode scan results in the database"
msgstr "存储条形码扫描结果"

#: common/setting/system.py:334
msgid "Barcode Scans Maximum Count"
msgstr "条码扫描最大计数"

#: common/setting/system.py:335
msgid "Maximum number of barcode scan results to store"
msgstr "存储条码扫描结果的最大数量"

#: common/setting/system.py:340
msgid "Barcode Input Delay"
msgstr "条形码扫描延迟设置"

#: common/setting/system.py:341
msgid "Barcode input processing delay time"
msgstr "条形码输入处理延迟时间"

#: common/setting/system.py:347
msgid "Barcode Webcam Support"
msgstr "条码摄像头支持"

#: common/setting/system.py:348
msgid "Allow barcode scanning via webcam in browser"
msgstr "允许通过网络摄像头扫描条形码"

#: common/setting/system.py:353
msgid "Barcode Show Data"
msgstr "条形码显示数据"

#: common/setting/system.py:354
msgid "Display barcode data in browser as text"
msgstr "在浏览器中将条形码数据显示为文本"

#: common/setting/system.py:359
msgid "Barcode Generation Plugin"
msgstr "条形码生成插件"

#: common/setting/system.py:360
msgid "Plugin to use for internal barcode data generation"
msgstr "用于内部条形码数据生成的插件"

#: common/setting/system.py:365
msgid "Part Revisions"
msgstr "零件修订"

#: common/setting/system.py:366
msgid "Enable revision field for Part"
msgstr "启用零件修订字段"

#: common/setting/system.py:371
msgid "Assembly Revision Only"
msgstr "仅限装配修订版本"

#: common/setting/system.py:372
msgid "Only allow revisions for assembly parts"
msgstr "仅允许对装配零件进行修订"

#: common/setting/system.py:377
msgid "Allow Deletion from Assembly"
msgstr "允许从装配中删除"

#: common/setting/system.py:378
msgid "Allow deletion of parts which are used in an assembly"
msgstr "允许删除已在装配中使用的零件"

#: common/setting/system.py:383
msgid "IPN Regex"
msgstr "IPN 内部零件号"

#: common/setting/system.py:384
msgid "Regular expression pattern for matching Part IPN"
msgstr "匹配零件 IPN（内部零件号）的正则表达式模式"

#: common/setting/system.py:387
msgid "Allow Duplicate IPN"
msgstr "允许重复的 IPN（内部零件号）"

#: common/setting/system.py:388
msgid "Allow multiple parts to share the same IPN"
msgstr "允许多个零件共享相同的 IPN（内部零件号）"

#: common/setting/system.py:393
msgid "Allow Editing IPN"
msgstr "允许编辑 IPN（内部零件号）"

#: common/setting/system.py:394
msgid "Allow changing the IPN value while editing a part"
msgstr "允许编辑零件时更改内部零件号"

#: common/setting/system.py:399
msgid "Copy Part BOM Data"
msgstr "复制零件物料清单数据"

#: common/setting/system.py:400
msgid "Copy BOM data by default when duplicating a part"
msgstr "复制零件时默认复制物料清单数据"

#: common/setting/system.py:405
msgid "Copy Part Parameter Data"
msgstr "复制零件参数数据"

#: common/setting/system.py:406
msgid "Copy parameter data by default when duplicating a part"
msgstr "复制零件时默认复制参数数据"

#: common/setting/system.py:411
msgid "Copy Part Test Data"
msgstr "复制零件测试数据"

#: common/setting/system.py:412
msgid "Copy test data by default when duplicating a part"
msgstr "复制零件时默认复制测试数据"

#: common/setting/system.py:417
msgid "Copy Category Parameter Templates"
msgstr "复制类别参数模板"

#: common/setting/system.py:418
msgid "Copy category parameter templates when creating a part"
msgstr "创建零件时复制类别参数模板"

#: common/setting/system.py:423 part/models.py:4035 report/models.py:357
#: report/models.py:563 report/serializers.py:90 report/serializers.py:131
#: stock/serializers.py:247
msgid "Template"
msgstr "模板"

#: common/setting/system.py:424
msgid "Parts are templates by default"
msgstr "零件默认为模板"

#: common/setting/system.py:430
msgid "Parts can be assembled from other components by default"
msgstr "默认情况下，元件可由其他零件组装而成"

#: common/setting/system.py:435 part/models.py:1217 part/serializers.py:1677
#: part/serializers.py:1683
msgid "Component"
msgstr "组件"

#: common/setting/system.py:436
msgid "Parts can be used as sub-components by default"
msgstr "默认情况下，零件可用作子部件"

#: common/setting/system.py:441 part/models.py:1235
msgid "Purchaseable"
msgstr "可购买"

#: common/setting/system.py:442
msgid "Parts are purchaseable by default"
msgstr "默认情况下可购买零件"

#: common/setting/system.py:447 part/models.py:1241 stock/api.py:592
msgid "Salable"
msgstr "可销售"

#: common/setting/system.py:448
msgid "Parts are salable by default"
msgstr "零件默认为可销售"

#: common/setting/system.py:454
msgid "Parts are trackable by default"
msgstr "默认情况下可跟踪零件"

#: common/setting/system.py:459 part/models.py:1257
msgid "Virtual"
msgstr "虚拟的"

#: common/setting/system.py:460
msgid "Parts are virtual by default"
msgstr "默认情况下，零件是虚拟的"

#: common/setting/system.py:465
msgid "Show Import in Views"
msgstr "在视图中显示导入"

#: common/setting/system.py:466
msgid "Display the import wizard in some part views"
msgstr "在某些零件视图中显示导入向导"

#: common/setting/system.py:471
msgid "Show related parts"
msgstr "显示相关零件"

#: common/setting/system.py:472
msgid "Display related parts for a part"
msgstr "显示零件的相关零件"

#: common/setting/system.py:477
msgid "Initial Stock Data"
msgstr "初始库存数据"

#: common/setting/system.py:478
msgid "Allow creation of initial stock when adding a new part"
msgstr "允许在添加新零件时创建初始库存"

#: common/setting/system.py:483
msgid "Initial Supplier Data"
msgstr "初始供应商数据"

#: common/setting/system.py:485
msgid "Allow creation of initial supplier data when adding a new part"
msgstr "允许在添加新零件时创建初始供应商数据"

#: common/setting/system.py:491
msgid "Part Name Display Format"
msgstr "零件名称显示格式"

#: common/setting/system.py:492
msgid "Format to display the part name"
msgstr "显示零件名称的格式"

#: common/setting/system.py:498
msgid "Part Category Default Icon"
msgstr "零件类别默认图标"

#: common/setting/system.py:499
msgid "Part category default icon (empty means no icon)"
msgstr "零件类别默认图标 (空表示没有图标)"

#: common/setting/system.py:504
msgid "Enforce Parameter Units"
msgstr "强制参数单位"

#: common/setting/system.py:506
msgid "If units are provided, parameter values must match the specified units"
msgstr "如果提供了单位，参数值必须与指定的单位匹配"

#: common/setting/system.py:512
msgid "Minimum Pricing Decimal Places"
msgstr "最小定价小数位数"

#: common/setting/system.py:514
msgid "Minimum number of decimal places to display when rendering pricing data"
msgstr "呈现定价数据时显示的最小小数位数"

#: common/setting/system.py:525
msgid "Maximum Pricing Decimal Places"
msgstr "最大定价小数位数"

#: common/setting/system.py:527
msgid "Maximum number of decimal places to display when rendering pricing data"
msgstr "呈现定价数据时显示的最大小数位数"

#: common/setting/system.py:538
msgid "Use Supplier Pricing"
msgstr "使用供应商定价"

#: common/setting/system.py:540
msgid "Include supplier price breaks in overall pricing calculations"
msgstr "将供应商的价批发价纳入总体定价计算中"

#: common/setting/system.py:546
msgid "Purchase History Override"
msgstr "购买历史记录覆盖"

#: common/setting/system.py:548
msgid "Historical purchase order pricing overrides supplier price breaks"
msgstr "历史采购订单定价优先于供应商批发价"

#: common/setting/system.py:554
msgid "Use Stock Item Pricing"
msgstr "使用库存项定价"

#: common/setting/system.py:556
msgid "Use pricing from manually entered stock data for pricing calculations"
msgstr "使用手动输入的库存数据进行定价计算"

#: common/setting/system.py:562
msgid "Stock Item Pricing Age"
msgstr "库存项目定价时间"

#: common/setting/system.py:564
msgid "Exclude stock items older than this number of days from pricing calculations"
msgstr "从定价计算中排除超过此天数的库存项目"

#: common/setting/system.py:571
msgid "Use Variant Pricing"
msgstr "使用变体定价"

#: common/setting/system.py:572
msgid "Include variant pricing in overall pricing calculations"
msgstr "在整体定价计算中包括变体定价"

#: common/setting/system.py:577
msgid "Active Variants Only"
msgstr "仅限活跃变体"

#: common/setting/system.py:579
msgid "Only use active variant parts for calculating variant pricing"
msgstr "仅使用活跃变体零件计算变体价格"

#: common/setting/system.py:585
msgid "Pricing Rebuild Interval"
msgstr "价格重建间隔"

#: common/setting/system.py:586
msgid "Number of days before part pricing is automatically updated"
msgstr "零件价格自动更新前的天数"

#: common/setting/system.py:592
msgid "Internal Prices"
msgstr "内部价格"

#: common/setting/system.py:593
msgid "Enable internal prices for parts"
msgstr "启用内部零件价格"

#: common/setting/system.py:598
msgid "Internal Price Override"
msgstr "覆盖内部价格"

#: common/setting/system.py:600
msgid "If available, internal prices override price range calculations"
msgstr "如果有内部价格，内部价格将覆盖价格范围计算"

#: common/setting/system.py:606
msgid "Enable label printing"
msgstr "启用标签打印功能"

#: common/setting/system.py:607
msgid "Enable label printing from the web interface"
msgstr "启用从网络界面打印标签"

#: common/setting/system.py:612
msgid "Label Image DPI"
msgstr "标签图片 DPI"

#: common/setting/system.py:614
msgid "DPI resolution when generating image files to supply to label printing plugins"
msgstr "生成图像文件以供标签打印插件使用时的 DPI 分辨率"

#: common/setting/system.py:620
msgid "Enable Reports"
msgstr "启用报告"

#: common/setting/system.py:621
msgid "Enable generation of reports"
msgstr "启用报告生成"

#: common/setting/system.py:626
msgid "Debug Mode"
msgstr "调试模式"

#: common/setting/system.py:627
msgid "Generate reports in debug mode (HTML output)"
msgstr "以调试模式生成报告（HTML 输出）"

#: common/setting/system.py:632
msgid "Log Report Errors"
msgstr "日志错误报告"

#: common/setting/system.py:633
msgid "Log errors which occur when generating reports"
msgstr "记录生成报告时出现的错误"

#: common/setting/system.py:638 plugin/builtin/labels/label_sheet.py:29
#: report/models.py:365
msgid "Page Size"
msgstr "页面大小"

#: common/setting/system.py:639
msgid "Default page size for PDF reports"
msgstr "PDF 报告默认页面大小"

#: common/setting/system.py:644
msgid "Globally Unique Serials"
msgstr "全局唯一序列号"

#: common/setting/system.py:645
msgid "Serial numbers for stock items must be globally unique"
msgstr "库存项的序列号必须全局唯一"

#: common/setting/system.py:650
msgid "Autofill Serial Numbers"
msgstr "自动填充序列号"

#: common/setting/system.py:651
msgid "Autofill serial numbers in forms"
msgstr "在表格中自动填充序列号"

#: common/setting/system.py:656
msgid "Delete Depleted Stock"
msgstr "删除已耗尽的库存"

#: common/setting/system.py:657
msgid "Determines default behavior when a stock item is depleted"
msgstr "设置库存耗尽时的默认行为"

#: common/setting/system.py:662
msgid "Batch Code Template"
msgstr "批号模板"

#: common/setting/system.py:663
msgid "Template for generating default batch codes for stock items"
msgstr "为库存项生成默认批号的模板"

#: common/setting/system.py:667
msgid "Stock Expiry"
msgstr "库存过期"

#: common/setting/system.py:668
msgid "Enable stock expiry functionality"
msgstr "启用库存过期功能"

#: common/setting/system.py:673
msgid "Sell Expired Stock"
msgstr "销售过期库存"

#: common/setting/system.py:674
msgid "Allow sale of expired stock"
msgstr "允许销售过期库存"

#: common/setting/system.py:679
msgid "Stock Stale Time"
msgstr "库存过期时间"

#: common/setting/system.py:681
msgid "Number of days stock items are considered stale before expiring"
msgstr "库存项在到期前被视为过期的天数"

#: common/setting/system.py:688
msgid "Build Expired Stock"
msgstr "生产过期库存"

#: common/setting/system.py:689
msgid "Allow building with expired stock"
msgstr "允许用过期的库存生产"

#: common/setting/system.py:694
msgid "Stock Ownership Control"
msgstr "库存所有权控制"

#: common/setting/system.py:695
msgid "Enable ownership control over stock locations and items"
msgstr "启用库存地点和项目的所有权控制"

#: common/setting/system.py:700
msgid "Stock Location Default Icon"
msgstr "库存地点默认图标"

#: common/setting/system.py:701
msgid "Stock location default icon (empty means no icon)"
msgstr "库存地点默认图标 (空表示没有图标)"

#: common/setting/system.py:706
msgid "Show Installed Stock Items"
msgstr "显示已安装的库存项"

#: common/setting/system.py:707
msgid "Display installed stock items in stock tables"
msgstr "在库存表中显示已安装的库存项"

#: common/setting/system.py:712
msgid "Check BOM when installing items"
msgstr "在安装项目时检查物料清单"

#: common/setting/system.py:714
msgid "Installed stock items must exist in the BOM for the parent part"
msgstr "已安装的库存项目必须存在于上级零件的物料清单中"

#: common/setting/system.py:720
msgid "Allow Out of Stock Transfer"
msgstr "允许超出库存转移"

#: common/setting/system.py:722
msgid "Allow stock items which are not in stock to be transferred between stock locations"
msgstr "允许非库存的库存项目在库存位置之间转移"

#: common/setting/system.py:728
msgid "Build Order Reference Pattern"
msgstr "生产订单参考模式"

#: common/setting/system.py:729
msgid "Required pattern for generating Build Order reference field"
msgstr "生成生产订单参考字段所需的模式"

#: common/setting/system.py:734 common/setting/system.py:788
#: common/setting/system.py:808 common/setting/system.py:844
msgid "Require Responsible Owner"
msgstr "要求负责人"

#: common/setting/system.py:735 common/setting/system.py:789
#: common/setting/system.py:809 common/setting/system.py:845
msgid "A responsible owner must be assigned to each order"
msgstr "必须为每个订单分配一个负责人"

#: common/setting/system.py:740
msgid "Require Active Part"
msgstr "需要活动零件"

#: common/setting/system.py:741
msgid "Prevent build order creation for inactive parts"
msgstr "防止为非活动零件创建生产订单"

#: common/setting/system.py:746
msgid "Require Locked Part"
msgstr "需要锁定零件"

#: common/setting/system.py:747
msgid "Prevent build order creation for unlocked parts"
msgstr "防止为未锁定的零件创建生产订单"

#: common/setting/system.py:752
msgid "Require Valid BOM"
msgstr "需要有效的物料清单"

#: common/setting/system.py:753
msgid "Prevent build order creation unless BOM has been validated"
msgstr "除非物料清单已验证，否则禁止创建生产订单"

#: common/setting/system.py:758
msgid "Require Closed Child Orders"
msgstr "需要关闭子订单"

#: common/setting/system.py:760
msgid "Prevent build order completion until all child orders are closed"
msgstr "在所有子订单关闭之前，阻止生产订单的完成"

#: common/setting/system.py:766
msgid "Block Until Tests Pass"
msgstr "阻止直到测试通过"

#: common/setting/system.py:768
msgid "Prevent build outputs from being completed until all required tests pass"
msgstr "在所有必要的测试通过之前，阻止产出完成"

#: common/setting/system.py:774
msgid "Enable Return Orders"
msgstr "启用订单退货"

#: common/setting/system.py:775
msgid "Enable return order functionality in the user interface"
msgstr "在用户界面中启用订单退货功能"

#: common/setting/system.py:780
msgid "Return Order Reference Pattern"
msgstr "退货订单参考模式"

#: common/setting/system.py:782
msgid "Required pattern for generating Return Order reference field"
msgstr "生成退货订单参考字段所需的模式"

#: common/setting/system.py:794
msgid "Edit Completed Return Orders"
msgstr "编辑已完成的退货订单"

#: common/setting/system.py:796
msgid "Allow editing of return orders after they have been completed"
msgstr "允许编辑已完成的退货订单"

#: common/setting/system.py:802
msgid "Sales Order Reference Pattern"
msgstr "销售订单参考模式"

#: common/setting/system.py:803
msgid "Required pattern for generating Sales Order reference field"
msgstr "生成销售订单参考字段所需参照模式"

#: common/setting/system.py:814
msgid "Sales Order Default Shipment"
msgstr "销售订单默认配送方式"

#: common/setting/system.py:815
msgid "Enable creation of default shipment with sales orders"
msgstr "启用创建销售订单的默认配送功能"

#: common/setting/system.py:820
msgid "Edit Completed Sales Orders"
msgstr "编辑已完成的销售订单"

#: common/setting/system.py:822
msgid "Allow editing of sales orders after they have been shipped or completed"
msgstr "允许在订单配送或完成后编辑销售订单"

#: common/setting/system.py:828
msgid "Mark Shipped Orders as Complete"
msgstr "标记该订单为已完成？"

#: common/setting/system.py:830
msgid "Sales orders marked as shipped will automatically be completed, bypassing the \"shipped\" status"
msgstr "标记为已发货的销售订单将自动完成，绕过“已发货”状态"

#: common/setting/system.py:836
msgid "Purchase Order Reference Pattern"
msgstr "采购订单参考模式"

#: common/setting/system.py:838
msgid "Required pattern for generating Purchase Order reference field"
msgstr "生成采购订单参考字段所需的模式"

#: common/setting/system.py:850
msgid "Edit Completed Purchase Orders"
msgstr "编辑已完成的采购订单"

#: common/setting/system.py:852
msgid "Allow editing of purchase orders after they have been shipped or completed"
msgstr "允许在采购订单已配送或完成后编辑订单"

#: common/setting/system.py:858
msgid "Convert Currency"
msgstr ""

#: common/setting/system.py:859
msgid "Convert item value to base currency when receiving stock"
msgstr ""

#: common/setting/system.py:864
msgid "Auto Complete Purchase Orders"
msgstr "自动完成采购订单"

#: common/setting/system.py:866
msgid "Automatically mark purchase orders as complete when all line items are received"
msgstr "当收到所有行项目时，自动将采购订单标记为完成"

#: common/setting/system.py:873
msgid "Enable password forgot"
msgstr "忘记启用密码"

#: common/setting/system.py:874
msgid "Enable password forgot function on the login pages"
msgstr "在登录页面上启用忘记密码功能"

#: common/setting/system.py:879
msgid "Enable registration"
msgstr "启用注册"

#: common/setting/system.py:880
msgid "Enable self-registration for users on the login pages"
msgstr "在登录页面为用户启用自行注册功能"

#: common/setting/system.py:885
msgid "Enable SSO"
msgstr "启用单点登录"

#: common/setting/system.py:886
msgid "Enable SSO on the login pages"
msgstr "在登录界面启用单点登录"

#: common/setting/system.py:891
msgid "Enable SSO registration"
msgstr "启用单点登录注册"

#: common/setting/system.py:893
msgid "Enable self-registration via SSO for users on the login pages"
msgstr "允许登录页面上的用户通过 SSO 进行自我注册"

#: common/setting/system.py:899
msgid "Enable SSO group sync"
msgstr "启用单点登录群组同步"

#: common/setting/system.py:901
msgid "Enable synchronizing InvenTree groups with groups provided by the IdP"
msgstr "启用库存管理系统组和由身份提供者提供的组的同步功能"

#: common/setting/system.py:907
msgid "SSO group key"
msgstr "单点登录系统组密钥"

#: common/setting/system.py:908
msgid "The name of the groups claim attribute provided by the IdP"
msgstr "由身份提供者提供的组声明属性名称"

#: common/setting/system.py:913
msgid "SSO group map"
msgstr "单点登录系统组地图"

#: common/setting/system.py:915
msgid "A mapping from SSO groups to local InvenTree groups. If the local group does not exist, it will be created."
msgstr "从单点登录系统组组到本地库存管理系统组的映射。如果本地组不存在，它将被创建。"

#: common/setting/system.py:921
msgid "Remove groups outside of SSO"
msgstr "移除单点登录系统以外的群组"

#: common/setting/system.py:923
msgid "Whether groups assigned to the user should be removed if they are not backend by the IdP. Disabling this setting might cause security issues"
msgstr "如果分配给用户的组不是身份提供者的后端，是否应该删除它们。禁用此设置可能会造成安全问题"

#: common/setting/system.py:929
msgid "Email required"
msgstr "需要邮箱地址"

#: common/setting/system.py:930
msgid "Require user to supply mail on signup"
msgstr "要求用户在注册时提供邮件"

#: common/setting/system.py:935
msgid "Auto-fill SSO users"
msgstr "自动填充单点登录系统用户"

#: common/setting/system.py:936
msgid "Automatically fill out user-details from SSO account-data"
msgstr "自动使用单点登录系统账户的数据填写用户详细信息"

#: common/setting/system.py:941
msgid "Mail twice"
msgstr "发两次邮件"

#: common/setting/system.py:942
msgid "On signup ask users twice for their mail"
msgstr "注册时询问用户他们的电子邮件两次"

#: common/setting/system.py:947
msgid "Password twice"
msgstr "两次输入密码"

#: common/setting/system.py:948
msgid "On signup ask users twice for their password"
msgstr "当注册时请用户输入密码两次"

#: common/setting/system.py:953
msgid "Allowed domains"
msgstr "域名白名单"

#: common/setting/system.py:955
msgid "Restrict signup to certain domains (comma-separated, starting with @)"
msgstr "限制注册到某些域名 (逗号分隔，以 @ 开头)"

#: common/setting/system.py:961
msgid "Group on signup"
msgstr "注册群组"

#: common/setting/system.py:963
msgid "Group to which new users are assigned on registration. If SSO group sync is enabled, this group is only set if no group can be assigned from the IdP."
msgstr "注册时分配给新用户的组。 如果启用了单点登录系统群组同步，此群组仅在无法从 IdP 分配任何群组的情况下才被设置。"

#: common/setting/system.py:969
msgid "Enforce MFA"
msgstr "强制启用多因素安全认证"

#: common/setting/system.py:970
msgid "Users must use multifactor security."
msgstr "用户必须使用多因素安全认证。"

#: common/setting/system.py:975
msgid "Check plugins on startup"
msgstr "启动时检查插件"

#: common/setting/system.py:977
msgid "Check that all plugins are installed on startup - enable in container environments"
msgstr "启动时检查全部插件是否已安装 - 在容器环境中启用"

#: common/setting/system.py:984
msgid "Check for plugin updates"
msgstr "检查插件更新"

#: common/setting/system.py:985
msgid "Enable periodic checks for updates to installed plugins"
msgstr "启用定期检查已安装插件的更新"

#: common/setting/system.py:991
msgid "Enable URL integration"
msgstr "启用统一资源定位符集成"

#: common/setting/system.py:992
msgid "Enable plugins to add URL routes"
msgstr "启用插件以添加统一资源定位符路由"

#: common/setting/system.py:998
msgid "Enable navigation integration"
msgstr "启用导航集成"

#: common/setting/system.py:999
msgid "Enable plugins to integrate into navigation"
msgstr "启用插件以集成到导航中"

#: common/setting/system.py:1005
msgid "Enable app integration"
msgstr "启用应用集成"

#: common/setting/system.py:1006
msgid "Enable plugins to add apps"
msgstr "启用插件添加应用"

#: common/setting/system.py:1012
msgid "Enable schedule integration"
msgstr "启用调度集成"

#: common/setting/system.py:1013
msgid "Enable plugins to run scheduled tasks"
msgstr "启用插件来运行预定任务"

#: common/setting/system.py:1019
msgid "Enable event integration"
msgstr "启用事件集成"

#: common/setting/system.py:1020
msgid "Enable plugins to respond to internal events"
msgstr "启用插件响应内部事件"

#: common/setting/system.py:1026
msgid "Enable interface integration"
msgstr "启用界面集成"

#: common/setting/system.py:1027
msgid "Enable plugins to integrate into the user interface"
msgstr "启用插件集成到用户界面"

#: common/setting/system.py:1033
msgid "Enable project codes"
msgstr ""

#: common/setting/system.py:1034
msgid "Enable project codes for tracking projects"
msgstr ""

#: common/setting/system.py:1039
msgid "Stocktake Functionality"
msgstr "盘点功能"

#: common/setting/system.py:1041
msgid "Enable stocktake functionality for recording stock levels and calculating stock value"
msgstr "启用盘点功能以记录库存水平和计算库存值"

#: common/setting/system.py:1047
msgid "Exclude External Locations"
msgstr "排除外部地点"

#: common/setting/system.py:1049
msgid "Exclude stock items in external locations from stocktake calculations"
msgstr "从盘点计算中排除外部地点的库存项"

#: common/setting/system.py:1055
msgid "Automatic Stocktake Period"
msgstr "自动盘点周期"

#: common/setting/system.py:1057
msgid "Number of days between automatic stocktake recording (set to zero to disable)"
msgstr "自动盘点记录之间的天数 (设置为零以禁用)"

#: common/setting/system.py:1063
msgid "Report Deletion Interval"
msgstr "报告删除间隔"

#: common/setting/system.py:1065
msgid "Stocktake reports will be deleted after specified number of days"
msgstr "盘点报告将在指定天数后删除"

#: common/setting/system.py:1072
msgid "Display Users full names"
msgstr "显示用户全名"

#: common/setting/system.py:1073
msgid "Display Users full names instead of usernames"
msgstr "显示用户全名而不是用户名"

#: common/setting/system.py:1078
msgid "Display User Profiles"
msgstr ""

#: common/setting/system.py:1079
msgid "Display Users Profiles on their profile page"
msgstr ""

#: common/setting/system.py:1084
msgid "Enable Test Station Data"
msgstr "启用测试站数据"

#: common/setting/system.py:1085
msgid "Enable test station data collection for test results"
msgstr "启用测试站数据收集以获取测试结果"

#: common/setting/system.py:1090
msgid "Create Template on Upload"
msgstr "上传时创建模板"

#: common/setting/system.py:1092
msgid "Create a new test template when uploading test data which does not match an existing template"
msgstr "上传测试数据与现有模板不匹配时创建一个新的测试模板"

#: common/setting/user.py:23
msgid "Inline label display"
msgstr "内联标签显示"

#: common/setting/user.py:25
msgid "Display PDF labels in the browser, instead of downloading as a file"
msgstr "在浏览器中显示PDF标签，而不是作为文件下载"

#: common/setting/user.py:31
msgid "Default label printer"
msgstr "默认标签打印机"

#: common/setting/user.py:32
msgid "Configure which label printer should be selected by default"
msgstr "配置默认情况下应选择哪个标签打印机"

#: common/setting/user.py:37
msgid "Inline report display"
msgstr "内联报告显示"

#: common/setting/user.py:39
msgid "Display PDF reports in the browser, instead of downloading as a file"
msgstr "在浏览器中显示PDF报告，而不是作为文件下载"

#: common/setting/user.py:45
msgid "Search Parts"
msgstr "搜索零件"

#: common/setting/user.py:46
msgid "Display parts in search preview window"
msgstr "在搜索预览窗口中显示零件"

#: common/setting/user.py:51
msgid "Search Supplier Parts"
msgstr "搜索供应商零件"

#: common/setting/user.py:52
msgid "Display supplier parts in search preview window"
msgstr "在搜索预览窗口中显示供应商零件"

#: common/setting/user.py:57
msgid "Search Manufacturer Parts"
msgstr "搜索制造商零件"

#: common/setting/user.py:58
msgid "Display manufacturer parts in search preview window"
msgstr "在搜索预览窗口中显示制造商零件"

#: common/setting/user.py:63
msgid "Hide Inactive Parts"
msgstr "隐藏非活动零件"

#: common/setting/user.py:64
msgid "Excluded inactive parts from search preview window"
msgstr "从搜索预览窗口中排除非活动零件"

#: common/setting/user.py:69
msgid "Search Categories"
msgstr "搜索分类"

#: common/setting/user.py:70
msgid "Display part categories in search preview window"
msgstr "在搜索预览窗口中显示零件类别"

#: common/setting/user.py:75
msgid "Search Stock"
msgstr "搜索库存"

#: common/setting/user.py:76
msgid "Display stock items in search preview window"
msgstr "在搜索预览窗口中显示库存项目"

#: common/setting/user.py:81
msgid "Hide Unavailable Stock Items"
msgstr "隐藏不可用的库存项目"

#: common/setting/user.py:83
msgid "Exclude stock items which are not available from the search preview window"
msgstr "排除搜索预览窗口中不可用的库存项目"

#: common/setting/user.py:89
msgid "Search Locations"
msgstr "搜索地点"

#: common/setting/user.py:90
msgid "Display stock locations in search preview window"
msgstr "在搜索预览窗口中显示库存位置"

#: common/setting/user.py:95
msgid "Search Companies"
msgstr "搜索公司"

#: common/setting/user.py:96
msgid "Display companies in search preview window"
msgstr "在搜索预览窗口中显示公司"

#: common/setting/user.py:101
msgid "Search Build Orders"
msgstr "搜索生产订单"

#: common/setting/user.py:102
msgid "Display build orders in search preview window"
msgstr "在搜索预览窗口中显示生产订单"

#: common/setting/user.py:107
msgid "Search Purchase Orders"
msgstr "搜索采购订单"

#: common/setting/user.py:108
msgid "Display purchase orders in search preview window"
msgstr "在搜索预览窗口中显示采购订单"

#: common/setting/user.py:113
msgid "Exclude Inactive Purchase Orders"
msgstr "排除未激活的采购订单"

#: common/setting/user.py:114
msgid "Exclude inactive purchase orders from search preview window"
msgstr "从搜索预览窗口中排除不活动的采购订单"

#: common/setting/user.py:119
msgid "Search Sales Orders"
msgstr "搜索销售订单"

#: common/setting/user.py:120
msgid "Display sales orders in search preview window"
msgstr "在搜索预览窗口中显示销售订单"

#: common/setting/user.py:125
msgid "Exclude Inactive Sales Orders"
msgstr "排除未激活的销售订单"

#: common/setting/user.py:126
msgid "Exclude inactive sales orders from search preview window"
msgstr "从搜索预览窗口中排除不活动的销售订单"

#: common/setting/user.py:131
msgid "Search Sales Order Shipments"
msgstr ""

#: common/setting/user.py:132
msgid "Display sales order shipments in search preview window"
msgstr ""

#: common/setting/user.py:137
msgid "Search Return Orders"
msgstr "搜索退货订单"

#: common/setting/user.py:138
msgid "Display return orders in search preview window"
msgstr "在搜索预览窗口中显示退货订单"

#: common/setting/user.py:143
msgid "Exclude Inactive Return Orders"
msgstr "排除未激活的退货订单"

#: common/setting/user.py:144
msgid "Exclude inactive return orders from search preview window"
msgstr "从搜索预览窗口中排除不活动的退货订单"

#: common/setting/user.py:149
msgid "Search Preview Results"
msgstr "搜索预览结果"

#: common/setting/user.py:151
msgid "Number of results to show in each section of the search preview window"
msgstr "在搜索预览窗口的每个部分中显示的结果数"

#: common/setting/user.py:157
msgid "Regex Search"
msgstr "正则表达式搜索"

#: common/setting/user.py:158
msgid "Enable regular expressions in search queries"
msgstr "在搜索查询中启用正则表达式"

#: common/setting/user.py:163
msgid "Whole Word Search"
msgstr "整词搜索"

#: common/setting/user.py:164
msgid "Search queries return results for whole word matches"
msgstr "搜索查询返回整词匹配的结果"

#: common/setting/user.py:169
msgid "Search Notes"
msgstr ""

#: common/setting/user.py:171
msgid "Search queries return results for matches from the item's notes"
msgstr ""

#: common/setting/user.py:177
msgid "Show Quantity in Forms"
msgstr "在表格中显示数量"

#: common/setting/user.py:178
msgid "Display available part quantity in some forms"
msgstr "以某些形式显示可用零件数量"

#: common/setting/user.py:183
msgid "Escape Key Closes Forms"
msgstr "Esc键关闭窗体"

#: common/setting/user.py:184
msgid "Use the escape key to close modal forms"
msgstr "使用ESC键关闭模态窗体"

#: common/setting/user.py:189
msgid "Fixed Navbar"
msgstr "固定导航栏"

#: common/setting/user.py:190
msgid "The navbar position is fixed to the top of the screen"
msgstr "导航栏位置固定在屏幕顶部"

#: common/setting/user.py:195
msgid "Navigation Icons"
msgstr ""

#: common/setting/user.py:196
msgid "Display icons in the navigation bar"
msgstr ""

#: common/setting/user.py:201
msgid "Date Format"
msgstr "时间格式"

#: common/setting/user.py:202
msgid "Preferred format for displaying dates"
msgstr "显示时间的首选格式"

#: common/setting/user.py:215
msgid "Part Scheduling"
msgstr "零件调度"

#: common/setting/user.py:216
msgid "Display part scheduling information"
msgstr "显示零件排程信息"

#: common/setting/user.py:221
msgid "Part Stocktake"
msgstr "零件盘点"

#: common/setting/user.py:223
msgid "Display part stocktake information (if stocktake functionality is enabled)"
msgstr "显示零件盘点信息 (如果启用了盘点功能)"

#: common/setting/user.py:229
msgid "Table String Length"
msgstr "表字符串长度"

#: common/setting/user.py:230
msgid "Maximum length limit for strings displayed in table views"
msgstr "表视图中显示的字符串的最大长度限制"

#: common/setting/user.py:235
msgid "Show Last Breadcrumb"
msgstr ""

#: common/setting/user.py:236
msgid "Show the current page in breadcrumbs"
msgstr ""

#: common/setting/user.py:241
msgid "Receive error reports"
msgstr "接收错误报告"

#: common/setting/user.py:242
msgid "Receive notifications for system errors"
msgstr "接收系统错误通知"

#: common/setting/user.py:247
msgid "Last used printing machines"
msgstr "上次使用的打印设备"

#: common/setting/user.py:248
msgid "Save the last used printing machines for a user"
msgstr "为用户保存上次使用的打印设备"

#: common/validators.py:35
msgid "No attachment model type provided"
msgstr "未提供附件型号"

#: common/validators.py:41
msgid "Invalid attachment model type"
msgstr "附件模型类型无效"

#: common/validators.py:82
msgid "Minimum places cannot be greater than maximum places"
msgstr "最小位置不能大于最大位置"

#: common/validators.py:94
msgid "Maximum places cannot be less than minimum places"
msgstr "最大名额不能小于最小名额"

#: common/validators.py:105
msgid "An empty domain is not allowed."
msgstr "不允许空域。"

#: common/validators.py:107
#, python-brace-format
msgid "Invalid domain name: {domain}"
msgstr "无效的域名: {domain}"

#: common/validators.py:123
msgid "Value must be uppercase"
msgstr ""

#: common/validators.py:129
msgid "Value must be a valid variable identifier"
msgstr ""

#: company/api.py:141
msgid "Part is Active"
msgstr "零件已激活"

#: company/api.py:145
msgid "Manufacturer is Active"
msgstr "制造商处于活动状态"

#: company/api.py:278
msgid "Supplier Part is Active"
msgstr "供应商零件处于激活状态"

#: company/api.py:282
msgid "Internal Part is Active"
msgstr "内部零件已激活"

#: company/api.py:287
msgid "Supplier is Active"
msgstr "供应商已激活"

#: company/api.py:299 company/models.py:498 company/serializers.py:444
#: part/serializers.py:577
msgid "Manufacturer"
msgstr "制造商"

#: company/api.py:306 company/models.py:97 company/models.py:370
#: stock/api.py:849
msgid "Company"
msgstr "公司"

#: company/api.py:316
msgid "Has Stock"
msgstr ""

#: company/models.py:98
msgid "Companies"
msgstr "公司"

#: company/models.py:114
msgid "Company description"
msgstr "公司简介"

#: company/models.py:115
msgid "Description of the company"
msgstr "公司简介"

#: company/models.py:121
msgid "Website"
msgstr "网站"

#: company/models.py:122
msgid "Company website URL"
msgstr "公司网站"

#: company/models.py:128
msgid "Phone number"
msgstr "电话号码"

#: company/models.py:130
msgid "Contact phone number"
msgstr "联系电话"

#: company/models.py:137
msgid "Contact email address"
msgstr "联系人电子邮箱地址"

#: company/models.py:142 company/models.py:274 order/models.py:500
#: users/models.py:563
msgid "Contact"
msgstr "联系人"

#: company/models.py:144
msgid "Point of contact"
msgstr "联络点"

#: company/models.py:150
msgid "Link to external company information"
msgstr "外部公司信息链接"

#: company/models.py:164
msgid "Is this company active?"
msgstr "这家公司是否激活？"

#: company/models.py:169
msgid "Is customer"
msgstr "是客户"

#: company/models.py:170
msgid "Do you sell items to this company?"
msgstr "你是否向该公司出售商品？"

#: company/models.py:175
msgid "Is supplier"
msgstr "是否为供应商"

#: company/models.py:176
msgid "Do you purchase items from this company?"
msgstr "你从这家公司买东西吗？"

#: company/models.py:181
msgid "Is manufacturer"
msgstr "是制造商吗"

#: company/models.py:182
msgid "Does this company manufacture parts?"
msgstr "这家公司生产零件吗？"

#: company/models.py:190
msgid "Default currency used for this company"
msgstr "此公司使用的默认货币"

#: company/models.py:313 order/models.py:510
msgid "Address"
msgstr "地址"

#: company/models.py:314
msgid "Addresses"
msgstr "地址"

#: company/models.py:371
msgid "Select company"
msgstr "选择公司"

#: company/models.py:376
msgid "Address title"
msgstr "地址标题"

#: company/models.py:377
msgid "Title describing the address entry"
msgstr "描述地址条目的标题"

#: company/models.py:383
msgid "Primary address"
msgstr "主要地址"

#: company/models.py:384
msgid "Set as primary address"
msgstr "设置主要地址"

#: company/models.py:389
msgid "Line 1"
msgstr "第1行"

#: company/models.py:390
msgid "Address line 1"
msgstr "地址行1"

#: company/models.py:396
msgid "Line 2"
msgstr "第2行"

#: company/models.py:397
msgid "Address line 2"
msgstr "地址行2"

#: company/models.py:403 company/models.py:404
msgid "Postal code"
msgstr "邮政编码"

#: company/models.py:410
msgid "City/Region"
msgstr "城市/地区"

#: company/models.py:411
msgid "Postal code city/region"
msgstr "邮政编码城市/地区"

#: company/models.py:417
msgid "State/Province"
msgstr "省/市/自治区"

#: company/models.py:418
msgid "State or province"
msgstr "省、自治区或直辖市"

#: company/models.py:424
msgid "Country"
msgstr "国家/地区"

#: company/models.py:425
msgid "Address country"
msgstr "地址所在国家"

#: company/models.py:431
msgid "Courier shipping notes"
msgstr "快递运单"

#: company/models.py:432
msgid "Notes for shipping courier"
msgstr "运输快递注意事项"

#: company/models.py:438
msgid "Internal shipping notes"
msgstr "内部装运通知单"

#: company/models.py:439
msgid "Shipping notes for internal use"
msgstr "内部使用的装运通知单"

#: company/models.py:446
msgid "Link to address information (external)"
msgstr "链接地址信息 (外部)"

#: company/models.py:470 company/models.py:587 company/models.py:809
#: company/serializers.py:458
msgid "Manufacturer Part"
msgstr "制造商零件"

#: company/models.py:487 company/models.py:777 stock/models.py:948
#: stock/serializers.py:480
msgid "Base Part"
msgstr "基础零件"

#: company/models.py:489 company/models.py:779
msgid "Select part"
msgstr "选择零件"

#: company/models.py:499
msgid "Select manufacturer"
msgstr "选择制造商"

#: company/models.py:505 company/serializers.py:466 order/serializers.py:665
#: part/serializers.py:587
msgid "MPN"
msgstr "制造商零件编号"

#: company/models.py:506 stock/serializers.py:625
msgid "Manufacturer Part Number"
msgstr "制造商零件编号"

#: company/models.py:513
msgid "URL for external manufacturer part link"
msgstr "外部制造商零件链接的URL"

#: company/models.py:522
msgid "Manufacturer part description"
msgstr "制造商零件说明"

#: company/models.py:575
msgid "Manufacturer Part Parameter"
msgstr "制造商零件参数"

#: company/models.py:594
msgid "Parameter name"
msgstr "参数名称"

#: company/models.py:601
msgid "Parameter value"
msgstr "参数值"

#: company/models.py:608 part/models.py:1204 part/models.py:3844
msgid "Units"
msgstr "单位"

#: company/models.py:609
msgid "Parameter units"
msgstr "参数单位"

#: company/models.py:717
msgid "Pack units must be compatible with the base part units"
msgstr "包装单位必须与基础零件单位兼容"

#: company/models.py:724
msgid "Pack units must be greater than zero"
msgstr "包装单位必须大于零"

#: company/models.py:738
msgid "Linked manufacturer part must reference the same base part"
msgstr "链接的制造商零件必须引用相同的基础零件"

#: company/models.py:787 company/serializers.py:436 company/serializers.py:454
#: order/models.py:626 part/serializers.py:561
#: plugin/builtin/suppliers/digikey.py:25 plugin/builtin/suppliers/lcsc.py:26
#: plugin/builtin/suppliers/mouser.py:24 plugin/builtin/suppliers/tme.py:26
#: stock/api.py:516 templates/email/overdue_purchase_order.html:16
msgid "Supplier"
msgstr "供应商"

#: company/models.py:788
msgid "Select supplier"
msgstr "选择供应商"

#: company/models.py:794 part/serializers.py:572
msgid "Supplier stock keeping unit"
msgstr "供应商库存管理单位"

#: company/models.py:800
msgid "Is this supplier part active?"
msgstr "此供应商零件是否处于活动状态？"

#: company/models.py:810
msgid "Select manufacturer part"
msgstr "选择制造商零件"

#: company/models.py:817
msgid "URL for external supplier part link"
msgstr "外部供应商零件链接的URL"

#: company/models.py:826
msgid "Supplier part description"
msgstr "供应商零件说明"

#: company/models.py:833 order/serializers.py:819 order/serializers.py:2027
#: part/models.py:4356 part/models.py:4697
#: report/templates/report/inventree_bill_of_materials_report.html:140
#: report/templates/report/inventree_purchase_order_report.html:32
#: report/templates/report/inventree_return_order_report.html:27
#: report/templates/report/inventree_sales_order_report.html:32
#: report/templates/report/inventree_stock_location_report.html:105
#: stock/serializers.py:838
msgid "Note"
msgstr "备注"

#: company/models.py:842 part/models.py:2182
msgid "base cost"
msgstr "基本费用"

#: company/models.py:843 part/models.py:2183
msgid "Minimum charge (e.g. stocking fee)"
msgstr "最低费用(例如库存费)"

#: company/models.py:850 order/serializers.py:811 stock/models.py:979
#: stock/serializers.py:1666
msgid "Packaging"
msgstr "打包"

#: company/models.py:851
msgid "Part packaging"
msgstr "零件打包"

#: company/models.py:856
msgid "Pack Quantity"
msgstr "包装数量"

#: company/models.py:858
msgid "Total quantity supplied in a single pack. Leave empty for single items."
msgstr "单包供应的总数量。为单个项目留空。"

#: company/models.py:877 part/models.py:2189
msgid "multiple"
msgstr "多个"

#: company/models.py:878
msgid "Order multiple"
msgstr "订购多个"

#: company/models.py:890
msgid "Quantity available from supplier"
msgstr "供应商提供的数量"

#: company/models.py:896
msgid "Availability Updated"
msgstr "可用性已更新"

#: company/models.py:897
msgid "Date of last update of availability data"
msgstr "上次更新可用性数据的日期"

#: company/models.py:1025
msgid "Supplier Price Break"
msgstr "供应商批发价"

#: company/serializers.py:166
msgid "Return the string representation for the primary address. This property exists for backwards compatibility."
msgstr ""

#: company/serializers.py:185
msgid "Default currency used for this supplier"
msgstr "此供应商使用的默认货币"

#: company/serializers.py:221
msgid "Company Name"
msgstr "公司名称"

#: company/serializers.py:420 part/serializers.py:940 stock/serializers.py:498
msgid "In Stock"
msgstr "有库存"

#: data_exporter/mixins.py:324 data_exporter/mixins.py:393
msgid "Error occurred during data export"
msgstr ""

#: data_exporter/mixins.py:375
msgid "Data export plugin returned incorrect data format"
msgstr ""

#: data_exporter/serializers.py:74
msgid "Export Format"
msgstr ""

#: data_exporter/serializers.py:75
msgid "Select export file format"
msgstr ""

#: data_exporter/serializers.py:82
msgid "Export Plugin"
msgstr ""

#: data_exporter/serializers.py:83
msgid "Select export plugin"
msgstr ""

#: generic/states/fields.py:146
msgid "Additional status information for this item"
msgstr "此项目的附加状态信息"

#: generic/states/fields.py:160
msgid "Custom status key"
msgstr "自定义状态密钥"

#: generic/states/serializers.py:16 plugin/models.py:45 users/models.py:119
msgid "Key"
msgstr "键"

#: generic/states/serializers.py:26
msgid "Custom"
msgstr ""

#: generic/states/serializers.py:37
msgid "Class"
msgstr ""

#: generic/states/serializers.py:40
msgid "Values"
msgstr ""

#: generic/states/tests.py:22 order/status_codes.py:13
msgid "Placed"
msgstr "放置"

#: generic/states/validators.py:21
msgid "Invalid status code"
msgstr ""

#: importer/models.py:70
msgid "Data File"
msgstr "数据文件"

#: importer/models.py:71
msgid "Data file to import"
msgstr "要导入的数据文件"

#: importer/models.py:80
msgid "Columns"
msgstr "列"

#: importer/models.py:87
msgid "Target model type for this import session"
msgstr ""

#: importer/models.py:93
msgid "Import status"
msgstr "导入状态"

#: importer/models.py:103
msgid "Field Defaults"
msgstr "字段默认值"

#: importer/models.py:110
msgid "Field Overrides"
msgstr "字段覆盖"

#: importer/models.py:117
msgid "Field Filters"
msgstr "字段筛选器"

#: importer/models.py:250
msgid "Some required fields have not been mapped"
msgstr "某些必填字段尚未映射"

#: importer/models.py:407
msgid "Column is already mapped to a database field"
msgstr "列已映射到数据库字段"

#: importer/models.py:412
msgid "Field is already mapped to a data column"
msgstr "字段已映射到数据列"

#: importer/models.py:421
msgid "Column mapping must be linked to a valid import session"
msgstr "列映射必须链接到有效的导入会话"

#: importer/models.py:426
msgid "Column does not exist in the data file"
msgstr "数据文件中不存在列"

#: importer/models.py:433
msgid "Field does not exist in the target model"
msgstr "目标模型中不存在字段"

#: importer/models.py:437
msgid "Selected field is read-only"
msgstr "所选字段为只读"

#: importer/models.py:442 importer/models.py:513
msgid "Import Session"
msgstr "导入会话"

#: importer/models.py:446
msgid "Field"
msgstr "字段"

#: importer/models.py:448
msgid "Column"
msgstr "列"

#: importer/models.py:517
msgid "Row Index"
msgstr "行索引"

#: importer/models.py:520
msgid "Original row data"
msgstr "原始行数据"

#: importer/models.py:525 machine/models.py:110
msgid "Errors"
msgstr "错误"

#: importer/models.py:527 part/api.py:864
msgid "Valid"
msgstr "有效"

#: importer/operations.py:28 importer/operations.py:49
msgid "Unsupported data file format"
msgstr "不支持的数据文件格式"

#: importer/operations.py:40
msgid "Failed to open data file"
msgstr "打开数据文件失败"

#: importer/operations.py:51
msgid "Invalid data file dimensions"
msgstr "数据文件维度无效"

#: importer/serializers.py:91
msgid "Invalid field defaults"
msgstr "字段默认值无效"

#: importer/serializers.py:104
msgid "Invalid field overrides"
msgstr "无效的字段覆盖"

#: importer/serializers.py:117
msgid "Invalid field filters"
msgstr "字段筛选器无效"

#: importer/serializers.py:178
msgid "Rows"
msgstr "行"

#: importer/serializers.py:179
msgid "List of row IDs to accept"
msgstr "要接受的行ID列表"

#: importer/serializers.py:192
msgid "No rows provided"
msgstr "未提供行"

#: importer/serializers.py:196
msgid "Row does not belong to this session"
msgstr "行不属于此会话"

#: importer/serializers.py:199
msgid "Row contains invalid data"
msgstr "行包含无效数据"

#: importer/serializers.py:202
msgid "Row has already been completed"
msgstr "行已完成"

#: importer/status_codes.py:13
msgid "Initializing"
msgstr "正在初始化"

#: importer/status_codes.py:18
msgid "Mapping Columns"
msgstr "映射列"

#: importer/status_codes.py:21
msgid "Importing Data"
msgstr "导入数据"

#: importer/status_codes.py:24
msgid "Processing Data"
msgstr "处理数据中"

#: importer/validators.py:21
msgid "Data file exceeds maximum size limit"
msgstr "数据文件超出最大大小限制"

#: importer/validators.py:26
msgid "Data file contains no headers"
msgstr "数据文件不包含标头"

#: importer/validators.py:29
msgid "Data file contains too many columns"
msgstr "数据文件包含的列太多"

#: importer/validators.py:32
msgid "Data file contains too many rows"
msgstr "数据文件包含的行太多"

#: importer/validators.py:53
msgid "Value must be a valid dictionary object"
msgstr "值必须是有效的字典对象"

#: machine/machine_types/label_printer.py:212
msgid "Copies"
msgstr "拷贝"

#: machine/machine_types/label_printer.py:213
msgid "Number of copies to print for each label"
msgstr "每个标签要打印的份数"

#: machine/machine_types/label_printer.py:228
msgid "Connected"
msgstr "已连接"

#: machine/machine_types/label_printer.py:229 order/api.py:1679
msgid "Unknown"
msgstr "未知"

#: machine/machine_types/label_printer.py:230
msgid "Printing"
msgstr "正在打印"

#: machine/machine_types/label_printer.py:231
msgid "No media"
msgstr "无媒体"

#: machine/machine_types/label_printer.py:232
msgid "Paper jam"
msgstr "卡纸"

#: machine/machine_types/label_printer.py:233
msgid "Disconnected"
msgstr "已断开连接"

#: machine/machine_types/label_printer.py:240
msgid "Label Printer"
msgstr "标签打印机"

#: machine/machine_types/label_printer.py:241
msgid "Directly print labels for various items."
msgstr "直接打印各种物品的标签。"

#: machine/machine_types/label_printer.py:247
msgid "Printer Location"
msgstr "打印机位置"

#: machine/machine_types/label_printer.py:248
msgid "Scope the printer to a specific location"
msgstr "将打印机定位到特定位置"

#: machine/models.py:25
msgid "Name of machine"
msgstr "设备名称"

#: machine/models.py:29
msgid "Machine Type"
msgstr "设备类型"

#: machine/models.py:29
msgid "Type of machine"
msgstr "设备类型"

#: machine/models.py:34 machine/models.py:146
msgid "Driver"
msgstr "驱动"

#: machine/models.py:35
msgid "Driver used for the machine"
msgstr "设备使用的驱动器"

#: machine/models.py:39
msgid "Machines can be disabled"
msgstr "可以禁用设备"

#: machine/models.py:95
msgid "Driver available"
msgstr "可用驱动"

#: machine/models.py:100
msgid "No errors"
msgstr "无错误"

#: machine/models.py:105
msgid "Initialized"
msgstr "已初始化"

#: machine/models.py:117
msgid "Machine status"
msgstr "设备状态"

#: machine/models.py:145
msgid "Machine"
msgstr "设备"

#: machine/models.py:151
msgid "Machine Config"
msgstr "设备配置"

#: machine/models.py:156
msgid "Config type"
msgstr "配置类型"

#: order/api.py:118
msgid "Order Reference"
msgstr "订单参考"

#: order/api.py:146 order/api.py:1139
msgid "Outstanding"
msgstr "未完成"

#: order/api.py:162
msgid "Has Project Code"
msgstr "有项目编码"

#: order/api.py:176 order/models.py:475
msgid "Created By"
msgstr "创建人"

#: order/api.py:180
msgid "Created Before"
msgstr ""

#: order/api.py:184
msgid "Created After"
msgstr ""

#: order/api.py:188
msgid "Has Start Date"
msgstr ""

#: order/api.py:196
msgid "Start Date Before"
msgstr ""

#: order/api.py:200
msgid "Start Date After"
msgstr ""

#: order/api.py:204
msgid "Has Target Date"
msgstr ""

#: order/api.py:212
msgid "Target Date Before"
msgstr ""

#: order/api.py:216
msgid "Target Date After"
msgstr ""

#: order/api.py:267
msgid "Has Pricing"
msgstr "有定价"

#: order/api.py:320 order/api.py:774 order/api.py:1375
msgid "Completed Before"
msgstr ""

#: order/api.py:324 order/api.py:778 order/api.py:1379
msgid "Completed After"
msgstr ""

#: order/api.py:491 order/api.py:876 order/api.py:1102 order/models.py:1685
#: order/models.py:1803 order/models.py:1854 order/models.py:2002
#: order/models.py:2168 order/models.py:2690 order/models.py:2756
msgid "Order"
msgstr "订单"

#: order/api.py:495 order/api.py:914
msgid "Order Complete"
msgstr "订单完成"

#: order/api.py:527 order/api.py:531 order/serializers.py:676
msgid "Internal Part"
msgstr "内部零件"

#: order/api.py:549
msgid "Order Pending"
msgstr "订单待定"

#: order/api.py:899
msgid "Completed"
msgstr "已完成"

#: order/api.py:1155
msgid "Has Shipment"
msgstr "有配送"

#: order/api.py:1673 order/models.py:539 order/models.py:1686
#: order/models.py:1804
#: report/templates/report/inventree_purchase_order_report.html:14
#: stock/serializers.py:128 templates/email/overdue_purchase_order.html:15
msgid "Purchase Order"
msgstr "采购订单"

#: order/api.py:1675 order/models.py:1080 order/models.py:1855
#: order/models.py:2003 order/models.py:2169
#: report/templates/report/inventree_build_order_report.html:135
#: report/templates/report/inventree_sales_order_report.html:14
#: report/templates/report/inventree_sales_order_shipment_report.html:15
#: templates/email/overdue_sales_order.html:15
msgid "Sales Order"
msgstr "销售订单"

#: order/api.py:1677 order/models.py:2340 order/models.py:2691
#: order/models.py:2757
#: report/templates/report/inventree_return_order_report.html:13
#: templates/email/overdue_return_order.html:15
msgid "Return Order"
msgstr "退货订单"

#: order/models.py:89
#: report/templates/report/inventree_purchase_order_report.html:31
#: report/templates/report/inventree_sales_order_report.html:31
msgid "Total Price"
msgstr "总价格"

#: order/models.py:90
msgid "Total price for this order"
msgstr "此订单的总价"

#: order/models.py:95 order/serializers.py:75
msgid "Order Currency"
msgstr "订单货币"

#: order/models.py:98 order/serializers.py:76
msgid "Currency for this order (leave blank to use company default)"
msgstr "此订单的货币 (留空以使用公司默认值)"

#: order/models.py:323
msgid "This order is locked and cannot be modified"
msgstr ""

#: order/models.py:370
msgid "Contact does not match selected company"
msgstr "联系人与所选公司不匹配"

#: order/models.py:377
msgid "Start date must be before target date"
msgstr ""

#: order/models.py:430
msgid "Order description (optional)"
msgstr "订单描述 (可选)"

#: order/models.py:439
msgid "Select project code for this order"
msgstr "为此订单选择项目编码"

#: order/models.py:445 order/models.py:1586 order/models.py:2057
msgid "Link to external page"
msgstr "链接到外部页面"

#: order/models.py:452
msgid "Start date"
msgstr ""

#: order/models.py:453
msgid "Scheduled start date for this order"
msgstr ""

#: order/models.py:459 order/models.py:1593 order/serializers.py:269
#: report/templates/report/inventree_build_order_report.html:125
msgid "Target Date"
msgstr "预计日期"

#: order/models.py:461
msgid "Expected date for order delivery. Order will be overdue after this date."
msgstr "订单交付的预期日期。订单将在此日期后过期。"

#: order/models.py:481
msgid "Issue Date"
msgstr "签发日期"

#: order/models.py:482
msgid "Date order was issued"
msgstr "订单发出日期"

#: order/models.py:490
msgid "User or group responsible for this order"
msgstr "负责此订单的用户或组"

#: order/models.py:501
msgid "Point of contact for this order"
msgstr "此订单的联系人"

#: order/models.py:511
msgid "Company address for this order"
msgstr "此订单的公司地址"

#: order/models.py:602 order/models.py:1141
msgid "Order reference"
msgstr "订单参考"

#: order/models.py:611 order/models.py:1165 order/models.py:2428
#: stock/serializers.py:612 stock/serializers.py:1010 users/models.py:544
msgid "Status"
msgstr "狀態"

#: order/models.py:612
msgid "Purchase order status"
msgstr "采购订单状态"

#: order/models.py:627
msgid "Company from which the items are being ordered"
msgstr "订购物品的公司"

#: order/models.py:638
msgid "Supplier Reference"
msgstr "供应商参考"

#: order/models.py:639
msgid "Supplier order reference code"
msgstr "供应商订单参考代码"

#: order/models.py:648
msgid "received by"
msgstr "接收人"

#: order/models.py:655 order/models.py:2443
msgid "Date order was completed"
msgstr "订单完成日期"

#: order/models.py:664 order/models.py:1733
msgid "Destination"
msgstr "目的地"

#: order/models.py:665 order/models.py:1737
msgid "Destination for received items"
msgstr "接收物品的目标"

#: order/models.py:711
msgid "Part supplier must match PO supplier"
msgstr "零件供应商必须与采购订单供应商匹配"

#: order/models.py:971
msgid "Quantity must be a positive number"
msgstr "数量必须是正数"

#: order/models.py:1152 order/models.py:2415 stock/models.py:1001
#: stock/models.py:1002 stock/serializers.py:1405
#: templates/email/overdue_return_order.html:16
#: templates/email/overdue_sales_order.html:16
msgid "Customer"
msgstr "客户"

#: order/models.py:1153
msgid "Company to which the items are being sold"
msgstr "出售物品的公司"

#: order/models.py:1166
msgid "Sales order status"
msgstr "销售订单状态"

#: order/models.py:1177 order/models.py:2435
msgid "Customer Reference "
msgstr "客户参考 "

#: order/models.py:1178 order/models.py:2436
msgid "Customer order reference code"
msgstr "客户订单参考代码"

#: order/models.py:1182 order/models.py:2009
msgid "Shipment Date"
msgstr "发货日期"

#: order/models.py:1191
msgid "shipped by"
msgstr "发货人"

#: order/models.py:1230
msgid "Order is already complete"
msgstr "订单已完成"

#: order/models.py:1233
msgid "Order is already cancelled"
msgstr "订单已取消"

#: order/models.py:1237
msgid "Only an open order can be marked as complete"
msgstr "只有未结订单才能标记为已完成"

#: order/models.py:1241
msgid "Order cannot be completed as there are incomplete shipments"
msgstr "由于发货不完整，订单无法完成"

#: order/models.py:1246
msgid "Order cannot be completed as there are incomplete allocations"
msgstr "由于缺货，订单无法完成"

#: order/models.py:1251
msgid "Order cannot be completed as there are incomplete line items"
msgstr "订单无法完成，因为行项目不完整"

#: order/models.py:1535 order/models.py:1548
msgid "The order is locked and cannot be modified"
msgstr ""

#: order/models.py:1556
msgid "Item quantity"
msgstr "项目数量"

#: order/models.py:1573
msgid "Line item reference"
msgstr "行项目参考"

#: order/models.py:1580
msgid "Line item notes"
msgstr "行项目注释"

#: order/models.py:1595
msgid "Target date for this line item (leave blank to use the target date from the order)"
msgstr "此行项目的目标日期 (留空以使用订单中的目标日期)"

#: order/models.py:1616
msgid "Line item description (optional)"
msgstr "行项目描述 (可选)"

#: order/models.py:1623
msgid "Additional context for this line"
msgstr "此行的附加上下文"

#: order/models.py:1633
msgid "Unit price"
msgstr "单位价格"

#: order/models.py:1647
msgid "Purchase Order Line Item"
msgstr "采购订单行项目"

#: order/models.py:1671
msgid "Supplier part must match supplier"
msgstr "供应商零件必须与供应商匹配"

#: order/models.py:1705
msgid "Supplier part"
msgstr "供应商零件"

#: order/models.py:1712
msgid "Received"
msgstr "已接收"

#: order/models.py:1713
msgid "Number of items received"
msgstr "收到的物品数量"

#: order/models.py:1721 stock/models.py:1124 stock/serializers.py:679
msgid "Purchase Price"
msgstr "采购价格"

#: order/models.py:1722
msgid "Unit purchase price"
msgstr "每单位的采购价格"

#: order/models.py:1792
msgid "Purchase Order Extra Line"
msgstr "采购订单附加行"

#: order/models.py:1821
msgid "Sales Order Line Item"
msgstr "销售订单行项目"

#: order/models.py:1842
msgid "Virtual part cannot be assigned to a sales order"
msgstr "虚拟零件不能分配给销售订单"

#: order/models.py:1847
msgid "Only salable parts can be assigned to a sales order"
msgstr "只有可销售的零件才能分配给销售订单"

#: order/models.py:1873
msgid "Sale Price"
msgstr "售出价格"

#: order/models.py:1874
msgid "Unit sale price"
msgstr "单位售出价格"

#: order/models.py:1883 order/status_codes.py:50
msgid "Shipped"
msgstr "已配送"

#: order/models.py:1884
msgid "Shipped quantity"
msgstr "发货数量"

#: order/models.py:1978
msgid "Sales Order Shipment"
msgstr "销售订单发货"

#: order/models.py:2010
msgid "Date of shipment"
msgstr "发货日期"

#: order/models.py:2016
msgid "Delivery Date"
msgstr "送达日期"

#: order/models.py:2017
msgid "Date of delivery of shipment"
msgstr "装运交货日期"

#: order/models.py:2025
msgid "Checked By"
msgstr "审核人"

#: order/models.py:2026
msgid "User who checked this shipment"
msgstr "检查此装运的用户"

#: order/models.py:2033 order/models.py:2265 order/serializers.py:1685
#: order/serializers.py:1809
#: report/templates/report/inventree_sales_order_shipment_report.html:14
msgid "Shipment"
msgstr "配送"

#: order/models.py:2034
msgid "Shipment number"
msgstr "配送单号"

#: order/models.py:2042
msgid "Tracking Number"
msgstr "跟踪单号"

#: order/models.py:2043
msgid "Shipment tracking information"
msgstr "配送跟踪信息"

#: order/models.py:2050
msgid "Invoice Number"
msgstr "发票编号"

#: order/models.py:2051
msgid "Reference number for associated invoice"
msgstr "相关发票的参考号"

#: order/models.py:2074
msgid "Shipment has already been sent"
msgstr "货物已发出"

#: order/models.py:2077
msgid "Shipment has no allocated stock items"
msgstr "发货没有分配库存项目"

#: order/models.py:2157
msgid "Sales Order Extra Line"
msgstr "销售订单加行"

#: order/models.py:2186
msgid "Sales Order Allocation"
msgstr "销售订单分配"

#: order/models.py:2209 order/models.py:2211
msgid "Stock item has not been assigned"
msgstr "库存项目尚未分配"

#: order/models.py:2218
msgid "Cannot allocate stock item to a line with a different part"
msgstr "无法将库存项目分配给具有不同零件的行"

#: order/models.py:2221
msgid "Cannot allocate stock to a line without a part"
msgstr "无法将库存分配给没有零件的生产线"

#: order/models.py:2224
msgid "Allocation quantity cannot exceed stock quantity"
msgstr "分配数量不能超过库存数量"

#: order/models.py:2243 order/serializers.py:1555
msgid "Quantity must be 1 for serialized stock item"
msgstr "序列化库存项目的数量必须为1"

#: order/models.py:2246
msgid "Sales order does not match shipment"
msgstr "销售订单与发货不匹配"

#: order/models.py:2247 plugin/base/barcodes/api.py:642
msgid "Shipment does not match sales order"
msgstr "发货与销售订单不匹配"

#: order/models.py:2255
msgid "Line"
msgstr "行"

#: order/models.py:2266
msgid "Sales order shipment reference"
msgstr "销售订单发货参考"

#: order/models.py:2279 order/models.py:2698
msgid "Item"
msgstr "项目"

#: order/models.py:2280
msgid "Select stock item to allocate"
msgstr "选择要分配的库存项目"

#: order/models.py:2289
msgid "Enter stock allocation quantity"
msgstr "输入库存分配数量"

#: order/models.py:2404
msgid "Return Order reference"
msgstr "退货订单参考"

#: order/models.py:2416
msgid "Company from which items are being returned"
msgstr "退回物品的公司"

#: order/models.py:2429
msgid "Return order status"
msgstr "退货订单状态"

#: order/models.py:2656
msgid "Return Order Line Item"
msgstr "退货订单行项目"

#: order/models.py:2669
msgid "Stock item must be specified"
msgstr ""

#: order/models.py:2673
msgid "Return quantity exceeds stock quantity"
msgstr ""

#: order/models.py:2678
msgid "Return quantity must be greater than zero"
msgstr ""

#: order/models.py:2683
msgid "Invalid quantity for serialized stock item"
msgstr ""

#: order/models.py:2699
msgid "Select item to return from customer"
msgstr "选择要从客户处退回的商品"

#: order/models.py:2714
msgid "Received Date"
msgstr "接收日期"

#: order/models.py:2715
msgid "The date this this return item was received"
msgstr "收到此退货的日期"

#: order/models.py:2727
msgid "Outcome"
msgstr "结果"

#: order/models.py:2728
msgid "Outcome for this line item"
msgstr "该行项目的结果"

#: order/models.py:2735
msgid "Cost associated with return or repair for this line item"
msgstr "与此行项目的退货或维修相关的成本"

#: order/models.py:2745
msgid "Return Order Extra Line"
msgstr "退货订单附加行"

#: order/serializers.py:89
msgid "Order ID"
msgstr "订单ID"

#: order/serializers.py:89
msgid "ID of the order to duplicate"
msgstr "要复制的订单ID"

#: order/serializers.py:95
msgid "Copy Lines"
msgstr "复制行"

#: order/serializers.py:96
msgid "Copy line items from the original order"
msgstr "从原始订单复制行项目"

#: order/serializers.py:102
msgid "Copy Extra Lines"
msgstr "复制额外行"

#: order/serializers.py:103
msgid "Copy extra line items from the original order"
msgstr "从原始订单复制额外的行项目"

#: order/serializers.py:116
#: report/templates/report/inventree_purchase_order_report.html:22
#: report/templates/report/inventree_return_order_report.html:19
#: report/templates/report/inventree_sales_order_report.html:22
msgid "Line Items"
msgstr "行项目"

#: order/serializers.py:121
msgid "Completed Lines"
msgstr "已完成行项目"

#: order/serializers.py:172
msgid "Duplicate Order"
msgstr "复制订单"

#: order/serializers.py:173
msgid "Specify options for duplicating this order"
msgstr "指定复制此订单的选项"

#: order/serializers.py:249
msgid "Invalid order ID"
msgstr "订单ID不正确"

#: order/serializers.py:388
msgid "Supplier Name"
msgstr "供应商名称"

#: order/serializers.py:430
msgid "Order cannot be cancelled"
msgstr "订单不能取消"

#: order/serializers.py:445 order/serializers.py:1576
msgid "Allow order to be closed with incomplete line items"
msgstr "允许关闭行项目不完整的订单"

#: order/serializers.py:455 order/serializers.py:1586
msgid "Order has incomplete line items"
msgstr "订单中的行项目不完整"

#: order/serializers.py:608
msgid "Order is not open"
msgstr "订单未打开"

#: order/serializers.py:629
msgid "Auto Pricing"
msgstr "自动定价"

#: order/serializers.py:631
msgid "Automatically calculate purchase price based on supplier part data"
msgstr "根据供应商零件数据自动计算采购价格"

#: order/serializers.py:641
msgid "Purchase price currency"
msgstr "购买价格货币"

#: order/serializers.py:649
msgid "Merge Items"
msgstr "合并项目"

#: order/serializers.py:651
msgid "Merge items with the same part, destination and target date into one line item"
msgstr "将具有相同零件、目的地和目标日期的项目合并到一个行项目中"

#: order/serializers.py:658 part/serializers.py:571
msgid "SKU"
msgstr "库存量单位"

#: order/serializers.py:672 part/models.py:1089 part/serializers.py:412
msgid "Internal Part Number"
msgstr "内部零件编号"

#: order/serializers.py:680
msgid "Internal Part Name"
msgstr "内部零件名称"

#: order/serializers.py:696
msgid "Supplier part must be specified"
msgstr "必须指定供应商零件"

#: order/serializers.py:699
msgid "Purchase order must be specified"
msgstr "必须指定采购订单"

#: order/serializers.py:707
msgid "Supplier must match purchase order"
msgstr "供应商必须匹配采购订单"

#: order/serializers.py:708
msgid "Purchase order must match supplier"
msgstr "采购订单必须与供应商匹配"

#: order/serializers.py:754 order/serializers.py:1656
msgid "Line Item"
msgstr "行项目"

#: order/serializers.py:760
msgid "Line item does not match purchase order"
msgstr "行项目与采购订单不匹配"

#: order/serializers.py:770 order/serializers.py:917 order/serializers.py:2023
msgid "Select destination location for received items"
msgstr "为收到的物品选择目的地位置"

#: order/serializers.py:786
msgid "Enter batch code for incoming stock items"
msgstr "输入入库项目的批号"

#: order/serializers.py:793 stock/models.py:1083 users/models.py:143
msgid "Expiry Date"
msgstr "有效期至"

#: order/serializers.py:794
msgid "Enter expiry date for incoming stock items"
msgstr ""

#: order/serializers.py:802
msgid "Enter serial numbers for incoming stock items"
msgstr "输入入库库存项目的序列号"

#: order/serializers.py:812
msgid "Override packaging information for incoming stock items"
msgstr "覆盖传入库存项目的包装资料"

#: order/serializers.py:820 order/serializers.py:2028
msgid "Additional note for incoming stock items"
msgstr "传入库存项目的附加说明"

#: order/serializers.py:827
msgid "Barcode"
msgstr "条形码"

#: order/serializers.py:828
msgid "Scanned barcode"
msgstr "扫描条形码"

#: order/serializers.py:844
msgid "Barcode is already in use"
msgstr "条形码已被使用"

#: order/serializers.py:867
msgid "An integer quantity must be provided for trackable parts"
msgstr "必须为可跟踪零件提供整数数量"

#: order/serializers.py:934 order/serializers.py:2047
msgid "Line items must be provided"
msgstr "必须提供行项目"

#: order/serializers.py:950
msgid "Destination location must be specified"
msgstr "必须指定目标位置"

#: order/serializers.py:961
msgid "Supplied barcode values must be unique"
msgstr "提供的条形码值必须是唯一的"

#: order/serializers.py:1092
msgid "Shipments"
msgstr "配送"

#: order/serializers.py:1096
msgid "Completed Shipments"
msgstr "完成配送"

#: order/serializers.py:1283
msgid "Sale price currency"
msgstr "售出价格货币"

#: order/serializers.py:1331
msgid "Allocated Items"
msgstr "已分配的项目"

#: order/serializers.py:1458
msgid "No shipment details provided"
msgstr "未提供装运详细信息"

#: order/serializers.py:1519 order/serializers.py:1665
msgid "Line item is not associated with this order"
msgstr "行项目与此订单不关联"

#: order/serializers.py:1538
msgid "Quantity must be positive"
msgstr "数量必须为正"

#: order/serializers.py:1675
msgid "Enter serial numbers to allocate"
msgstr "输入要分配的序列号"

#: order/serializers.py:1697 order/serializers.py:1817
msgid "Shipment has already been shipped"
msgstr "货物已发出"

#: order/serializers.py:1700 order/serializers.py:1820
msgid "Shipment is not associated with this order"
msgstr "发货与此订单无关"

#: order/serializers.py:1755
msgid "No match found for the following serial numbers"
msgstr "未找到以下序列号的匹配项"

#: order/serializers.py:1762
msgid "The following serial numbers are unavailable"
msgstr "以下序列号不可用"

#: order/serializers.py:1989
msgid "Return order line item"
msgstr "退货订单行项目"

#: order/serializers.py:1999
msgid "Line item does not match return order"
msgstr "行项目与退货订单不匹配"

#: order/serializers.py:2002
msgid "Line item has already been received"
msgstr "行项目已收到"

#: order/serializers.py:2039
msgid "Items can only be received against orders which are in progress"
msgstr "只能根据正在进行的订单接收物品"

#: order/serializers.py:2131
msgid "Quantity to return"
msgstr ""

#: order/serializers.py:2143
msgid "Line price currency"
msgstr "行价格货币"

#: order/status_codes.py:17 order/status_codes.py:54 stock/status_codes.py:16
msgid "Lost"
msgstr "丢失"

#: order/status_codes.py:18 order/status_codes.py:55 stock/status_codes.py:24
msgid "Returned"
msgstr "已退回"

#: order/status_codes.py:47 order/status_codes.py:79
msgid "In Progress"
msgstr "正在进行"

#: order/status_codes.py:105
msgid "Return"
msgstr "退回"

#: order/status_codes.py:108
msgid "Repair"
msgstr "維修"

#: order/status_codes.py:111
msgid "Replace"
msgstr "替換"

#: order/status_codes.py:114
msgid "Refund"
msgstr "退款"

#: order/status_codes.py:117
msgid "Reject"
msgstr "拒絕"

#: order/tasks.py:44
msgid "Overdue Purchase Order"
msgstr "逾期采购订单"

#: order/tasks.py:49
#, python-brace-format
msgid "Purchase order {po} is now overdue"
msgstr "采购订单 {po} 已逾期"

#: order/tasks.py:112
msgid "Overdue Sales Order"
msgstr "逾期销售订单"

#: order/tasks.py:117
#, python-brace-format
msgid "Sales order {so} is now overdue"
msgstr "销售订单 {so} 已逾期"

#: order/tasks.py:177
msgid "Overdue Return Order"
msgstr ""

#: order/tasks.py:182
#, python-brace-format
msgid "Return order {ro} is now overdue"
msgstr ""

#: part/api.py:115
msgid "Starred"
msgstr "已加星标"

#: part/api.py:117
msgid "Filter by starred categories"
msgstr "按星标类别筛选"

#: part/api.py:134 stock/api.py:255
msgid "Depth"
msgstr "深度"

#: part/api.py:134
msgid "Filter by category depth"
msgstr "按类别深度筛选"

#: part/api.py:152 stock/api.py:273
msgid "Top Level"
msgstr "顶级"

#: part/api.py:154
msgid "Filter by top-level categories"
msgstr "按顶级类别筛选"

#: part/api.py:167 stock/api.py:288
msgid "Cascade"
msgstr "级联"

#: part/api.py:169
msgid "Include sub-categories in filtered results"
msgstr "在筛选结果中包含子类别"

#: part/api.py:189
msgid "Parent"
msgstr "父类"

#: part/api.py:191
msgid "Filter by parent category"
msgstr "按父类别筛选"

#: part/api.py:226
msgid "Exclude sub-categories under the specified category"
msgstr "排除指定类别下的子类别"

#: part/api.py:438
msgid "Has Results"
msgstr "有结果"

#: part/api.py:611
msgid "Incoming Purchase Order"
msgstr "收到的采购订单"

#: part/api.py:625
msgid "Outgoing Sales Order"
msgstr "外发销售订单"

#: part/api.py:637
msgid "Stock produced by Build Order"
msgstr "建造生产订单产生的库存"

#: part/api.py:719
msgid "Stock required for Build Order"
msgstr "生产订单所需的库存"

#: part/api.py:865
msgid "Validate entire Bill of Materials"
msgstr "验证整个物料清单"

#: part/api.py:871
msgid "This option must be selected"
msgstr "必须选择此项"

#: part/api.py:907
msgid "Is Variant"
msgstr ""

#: part/api.py:915
msgid "Is Revision"
msgstr "是修订版本"

#: part/api.py:925
msgid "Has Revisions"
msgstr "有修订版本"

#: part/api.py:1116
msgid "BOM Valid"
msgstr "物料清单合规"

#: part/api.py:1775
msgid "Assembly part is testable"
msgstr "装配部份是可测试的"

#: part/api.py:1784
msgid "Component part is testable"
msgstr "组件部份是可测试的"

#: part/api.py:1835
msgid "Uses"
msgstr "使用"

#: part/models.py:90 part/models.py:4137
#: templates/email/part_event_notification.html:16
msgid "Part Category"
msgstr "零件类别"

#: part/models.py:91 users/ruleset.py:32
msgid "Part Categories"
msgstr "零件类别"

#: part/models.py:109 part/models.py:1134
msgid "Default Location"
msgstr "默认位置"

#: part/models.py:110
msgid "Default location for parts in this category"
msgstr "此类别零件的默认库存地点"

#: part/models.py:115 stock/models.py:212
msgid "Structural"
msgstr "结构性"

#: part/models.py:117
msgid "Parts may not be directly assigned to a structural category, but may be assigned to child categories."
msgstr "零件可能无法直接分配到结构类别，但可以分配到子类别。"

#: part/models.py:126
msgid "Default keywords"
msgstr "默认关键字"

#: part/models.py:127
msgid "Default keywords for parts in this category"
msgstr "此类别零件的默认关键字"

#: part/models.py:134 stock/models.py:96 stock/models.py:194
msgid "Icon"
msgstr "图标"

#: part/models.py:135 part/serializers.py:152 part/serializers.py:171
#: stock/models.py:195
msgid "Icon (optional)"
msgstr "图标(可选)"

#: part/models.py:179
msgid "You cannot make this part category structural because some parts are already assigned to it!"
msgstr "您不能使这个零件类别结构化，因为有些零件已经分配给了它！"

#: part/models.py:458 part/serializers.py:130 part/serializers.py:343
#: users/ruleset.py:33
msgid "Parts"
msgstr "零件"

#: part/models.py:510
msgid "Cannot delete this part as it is locked"
msgstr "无法删除这个零件，因为它已被锁定"

#: part/models.py:513
msgid "Cannot delete this part as it is still active"
msgstr "无法删除这个零件，因为它仍然处于活动状态"

#: part/models.py:518
msgid "Cannot delete this part as it is used in an assembly"
msgstr "无法删除这个零件，因为它被使用在了装配中"

#: part/models.py:556
msgid "Invalid choice for parent part"
msgstr "无效的上级零件选择"

#: part/models.py:604 part/models.py:611
#, python-brace-format
msgid "Part '{self}' cannot be used in BOM for '{parent}' (recursive)"
msgstr "零件 \"{self}\" 不能用在 \"{parent}\" 的物料清单 (递归)"

#: part/models.py:623
#, python-brace-format
msgid "Part '{parent}' is  used in BOM for '{self}' (recursive)"
msgstr "零件 \"{parent}\" 被使用在了 \"{self}\" 的物料清单 (递归)"

#: part/models.py:690
#, python-brace-format
msgid "IPN must match regex pattern {pattern}"
msgstr "内部零件号必须匹配正则表达式 {pattern}"

#: part/models.py:698
msgid "Part cannot be a revision of itself"
msgstr "零件不能是对自身的修订"

#: part/models.py:705
msgid "Cannot make a revision of a part which is already a revision"
msgstr "无法对已经是修订版本的零件进行修订"

#: part/models.py:712
msgid "Revision code must be specified"
msgstr "必须指定修订代码"

#: part/models.py:719
msgid "Revisions are only allowed for assembly parts"
msgstr "修订仅对装配零件允许"

#: part/models.py:726
msgid "Cannot make a revision of a template part"
msgstr "无法对模版零件进行修订"

#: part/models.py:732
msgid "Parent part must point to the same template"
msgstr "上级零件必须指向相同的模版"

#: part/models.py:829
msgid "Stock item with this serial number already exists"
msgstr "该序列号库存项己存在"

#: part/models.py:971
msgid "Duplicate IPN not allowed in part settings"
msgstr "在零件设置中不允许重复的内部零件号"

#: part/models.py:983
msgid "Duplicate part revision already exists."
msgstr "重复的零件修订版本已经存在。"

#: part/models.py:992
msgid "Part with this Name, IPN and Revision already exists."
msgstr "有这个名字，内部零件号，和修订版本的零件已经存在"

#: part/models.py:1007
msgid "Parts cannot be assigned to structural part categories!"
msgstr "零件不能分配到结构性零件类别！"

#: part/models.py:1039
msgid "Part name"
msgstr "零件名称"

#: part/models.py:1044
msgid "Is Template"
msgstr "是模板"

#: part/models.py:1045
msgid "Is this part a template part?"
msgstr "这个零件是一个模版零件吗?"

#: part/models.py:1055
msgid "Is this part a variant of another part?"
msgstr "这个零件是另一零件的变体吗？"

#: part/models.py:1056
msgid "Variant Of"
msgstr "变体"

#: part/models.py:1063
msgid "Part description (optional)"
msgstr "零件描述(可选)"

#: part/models.py:1070
msgid "Keywords"
msgstr "关键词"

#: part/models.py:1071
msgid "Part keywords to improve visibility in search results"
msgstr "提高搜索结果可见性的零件关键字"

#: part/models.py:1081
msgid "Part category"
msgstr "零件类别"

#: part/models.py:1088 part/serializers.py:926
#: report/templates/report/inventree_stock_location_report.html:103
msgid "IPN"
msgstr "内部零件号 IPN"

#: part/models.py:1096
msgid "Part revision or version number"
msgstr "零件修订版本或版本号"

#: part/models.py:1097 report/models.py:215
msgid "Revision"
msgstr "版本"

#: part/models.py:1106
msgid "Is this part a revision of another part?"
msgstr "这零件是另一零件的修订版本吗？"

#: part/models.py:1107
msgid "Revision Of"
msgstr "修订版本"

#: part/models.py:1132
msgid "Where is this item normally stored?"
msgstr "该物品通常存放在哪里？"

#: part/models.py:1178
msgid "Default Supplier"
msgstr "默认供应商"

#: part/models.py:1179
msgid "Default supplier part"
msgstr "默认供应商零件"

#: part/models.py:1186
msgid "Default Expiry"
msgstr "默认到期"

#: part/models.py:1187
msgid "Expiry time (in days) for stock items of this part"
msgstr "此零件库存项的过期时间 (天)"

#: part/models.py:1195 part/serializers.py:977
msgid "Minimum Stock"
msgstr "最低库存"

#: part/models.py:1196
msgid "Minimum allowed stock level"
msgstr "允许的最小库存量"

#: part/models.py:1205
msgid "Units of measure for this part"
msgstr "此零件的计量单位"

#: part/models.py:1212
msgid "Can this part be built from other parts?"
msgstr "这个零件可由其他零件加工而成吗？"

#: part/models.py:1218
msgid "Can this part be used to build other parts?"
msgstr "这个零件可用于创建其他零件吗？"

#: part/models.py:1224
msgid "Does this part have tracking for unique items?"
msgstr "此零件是否有唯一物品的追踪功能"

#: part/models.py:1230
msgid "Can this part have test results recorded against it?"
msgstr "这一部件能否记录到测试结果？"

#: part/models.py:1236
msgid "Can this part be purchased from external suppliers?"
msgstr "这个零件可从外部供应商购买吗？"

#: part/models.py:1242
msgid "Can this part be sold to customers?"
msgstr "此零件可以销售给客户吗?"

#: part/models.py:1246
msgid "Is this part active?"
msgstr "这个零件是否已激活？"

#: part/models.py:1252
msgid "Locked parts cannot be edited"
msgstr "无法编辑锁定的零件"

#: part/models.py:1258
msgid "Is this a virtual part, such as a software product or license?"
msgstr "这是一个虚拟零件，例如一个软件产品或许可证吗？"

#: part/models.py:1264
msgid "BOM checksum"
msgstr "物料清单校验和"

#: part/models.py:1265
msgid "Stored BOM checksum"
msgstr "保存的物料清单校验和"

#: part/models.py:1273
msgid "BOM checked by"
msgstr "物料清单检查人"

#: part/models.py:1278
msgid "BOM checked date"
msgstr "物料清单检查日期"

#: part/models.py:1294
msgid "Creation User"
msgstr "新建用户"

#: part/models.py:1304
msgid "Owner responsible for this part"
msgstr "此零件的负责人"

#: part/models.py:1309
msgid "Last Stocktake"
msgstr "最近库存盘点"

#: part/models.py:2190
msgid "Sell multiple"
msgstr "出售多个"

#: part/models.py:3185
msgid "Currency used to cache pricing calculations"
msgstr "用于缓存定价计算的货币"

#: part/models.py:3201
msgid "Minimum BOM Cost"
msgstr "最低物料清单成本"

#: part/models.py:3202
msgid "Minimum cost of component parts"
msgstr "元件的最低成本"

#: part/models.py:3208
msgid "Maximum BOM Cost"
msgstr "物料清单的最高成本"

#: part/models.py:3209
msgid "Maximum cost of component parts"
msgstr "元件的最高成本"

#: part/models.py:3215
msgid "Minimum Purchase Cost"
msgstr "最低购买成本"

#: part/models.py:3216
msgid "Minimum historical purchase cost"
msgstr "最高历史购买成本"

#: part/models.py:3222
msgid "Maximum Purchase Cost"
msgstr "最大购买成本"

#: part/models.py:3223
msgid "Maximum historical purchase cost"
msgstr "最高历史购买成本"

#: part/models.py:3229
msgid "Minimum Internal Price"
msgstr "最低内部价格"

#: part/models.py:3230
msgid "Minimum cost based on internal price breaks"
msgstr "基于内部批发价的最低成本"

#: part/models.py:3236
msgid "Maximum Internal Price"
msgstr "最大内部价格"

#: part/models.py:3237
msgid "Maximum cost based on internal price breaks"
msgstr "基于内部批发价的最高成本"

#: part/models.py:3243
msgid "Minimum Supplier Price"
msgstr "供应商最低价格"

#: part/models.py:3244
msgid "Minimum price of part from external suppliers"
msgstr "外部供应商零件的最低价格"

#: part/models.py:3250
msgid "Maximum Supplier Price"
msgstr "供应商最高价格"

#: part/models.py:3251
msgid "Maximum price of part from external suppliers"
msgstr "来自外部供应商的商零件的最高价格"

#: part/models.py:3257
msgid "Minimum Variant Cost"
msgstr "最小变体成本"

#: part/models.py:3258
msgid "Calculated minimum cost of variant parts"
msgstr "计算出的变体零件的最低成本"

#: part/models.py:3264
msgid "Maximum Variant Cost"
msgstr "最大变体成本"

#: part/models.py:3265
msgid "Calculated maximum cost of variant parts"
msgstr "计算出的变体零件的最大成本"

#: part/models.py:3271 part/models.py:3285
msgid "Minimum Cost"
msgstr "最低成本"

#: part/models.py:3272
msgid "Override minimum cost"
msgstr "覆盖最低成本"

#: part/models.py:3278 part/models.py:3292
msgid "Maximum Cost"
msgstr "最高成本"

#: part/models.py:3279
msgid "Override maximum cost"
msgstr "覆盖最大成本"

#: part/models.py:3286
msgid "Calculated overall minimum cost"
msgstr "计算总最低成本"

#: part/models.py:3293
msgid "Calculated overall maximum cost"
msgstr "计算总最大成本"

#: part/models.py:3299
msgid "Minimum Sale Price"
msgstr "最低售出价格"

#: part/models.py:3300
msgid "Minimum sale price based on price breaks"
msgstr "基于批发价的最低售出价格"

#: part/models.py:3306
msgid "Maximum Sale Price"
msgstr "最高售出价格"

#: part/models.py:3307
msgid "Maximum sale price based on price breaks"
msgstr "基于批发价的最大售出价格"

#: part/models.py:3313
msgid "Minimum Sale Cost"
msgstr "最低销售成本"

#: part/models.py:3314
msgid "Minimum historical sale price"
msgstr "历史最低售出价格"

#: part/models.py:3320
msgid "Maximum Sale Cost"
msgstr "最高销售成本"

#: part/models.py:3321
msgid "Maximum historical sale price"
msgstr "历史最高售出价格"

#: part/models.py:3340
msgid "Part for stocktake"
msgstr "用于盘点的零件"

#: part/models.py:3345
msgid "Item Count"
msgstr "物品数量"

#: part/models.py:3346
msgid "Number of individual stock entries at time of stocktake"
msgstr "盘点时的个别库存条目数"

#: part/models.py:3354
msgid "Total available stock at time of stocktake"
msgstr "盘点时可用库存总额"

#: part/models.py:3358 part/models.py:3441 part/serializers.py:273
#: report/templates/report/inventree_test_report.html:106
msgid "Date"
msgstr "日期"

#: part/models.py:3359
msgid "Date stocktake was performed"
msgstr "进行盘点的日期"

#: part/models.py:3367
msgid "Additional notes"
msgstr "附加注释"

#: part/models.py:3377
msgid "User who performed this stocktake"
msgstr "进行此盘点的用户"

#: part/models.py:3383
msgid "Minimum Stock Cost"
msgstr "最低库存成本"

#: part/models.py:3384
msgid "Estimated minimum cost of stock on hand"
msgstr "现有存库存最低成本估算"

#: part/models.py:3390
msgid "Maximum Stock Cost"
msgstr "最高库存成本"

#: part/models.py:3391
msgid "Estimated maximum cost of stock on hand"
msgstr "目前库存最高成本估算"

#: part/models.py:3447
msgid "Report"
msgstr "报告"

#: part/models.py:3448
msgid "Stocktake report file (generated internally)"
msgstr "盘点报告文件(内部生成)"

#: part/models.py:3453
msgid "Part Count"
msgstr "零件计数"

#: part/models.py:3454
msgid "Number of parts covered by stocktake"
msgstr "盘点涵盖的零件数量"

#: part/models.py:3464
msgid "User who requested this stocktake report"
msgstr "请求此盘点报告的用户"

#: part/models.py:3474
msgid "Part Sale Price Break"
msgstr "零件售出价格折扣"

#: part/models.py:3586
msgid "Part Test Template"
msgstr "零件测试模板"

#: part/models.py:3612
msgid "Invalid template name - must include at least one alphanumeric character"
msgstr "模板名称无效 - 必须包含至少一个字母或者数字"

#: part/models.py:3633 part/models.py:3803
msgid "Choices must be unique"
msgstr "选择必须是唯一的"

#: part/models.py:3644
msgid "Test templates can only be created for testable parts"
msgstr "测试模板只能为可拆分的部件创建"

#: part/models.py:3655
msgid "Test template with the same key already exists for part"
msgstr "零件已存在具有相同主键的测试模板"

#: part/models.py:3672
msgid "Test Name"
msgstr "测试名"

#: part/models.py:3673
msgid "Enter a name for the test"
msgstr "输入测试的名称"

#: part/models.py:3679
msgid "Test Key"
msgstr "测试主键"

#: part/models.py:3680
msgid "Simplified key for the test"
msgstr "简化测试主键"

#: part/models.py:3687
msgid "Test Description"
msgstr "测试说明"

#: part/models.py:3688
msgid "Enter description for this test"
msgstr "输入测试的描述"

#: part/models.py:3692 report/models.py:271
msgid "Enabled"
msgstr "已启用"

#: part/models.py:3692
msgid "Is this test enabled?"
msgstr "此测试是否已启用？"

#: part/models.py:3697
msgid "Required"
msgstr "必须的"

#: part/models.py:3698
msgid "Is this test required to pass?"
msgstr "需要此测试才能通过吗？"

#: part/models.py:3703
msgid "Requires Value"
msgstr "需要值"

#: part/models.py:3704
msgid "Does this test require a value when adding a test result?"
msgstr "添加测试结果时是否需要一个值？"

#: part/models.py:3709
msgid "Requires Attachment"
msgstr "需要附件"

#: part/models.py:3711
msgid "Does this test require a file attachment when adding a test result?"
msgstr "添加测试结果时是否需要文件附件？"

#: part/models.py:3717 part/models.py:3865
msgid "Choices"
msgstr "选项"

#: part/models.py:3718
msgid "Valid choices for this test (comma-separated)"
msgstr "此测试的有效选择 (逗号分隔)"

#: part/models.py:3751
msgid "Part Parameter Template"
msgstr "零件参数模板"

#: part/models.py:3778
msgid "Checkbox parameters cannot have units"
msgstr "勾选框参数不能有单位"

#: part/models.py:3783
msgid "Checkbox parameters cannot have choices"
msgstr "复选框参数不能有选项"

#: part/models.py:3820
msgid "Parameter template name must be unique"
msgstr "参数模板名称必须是唯一的"

#: part/models.py:3838
msgid "Parameter Name"
msgstr "参数名称"

#: part/models.py:3845
msgid "Physical units for this parameter"
msgstr "此参数的物理单位"

#: part/models.py:3853
msgid "Parameter description"
msgstr "参数说明"

#: part/models.py:3859
msgid "Checkbox"
msgstr "勾选框"

#: part/models.py:3860
msgid "Is this parameter a checkbox?"
msgstr "此参数是否为勾选框？"

#: part/models.py:3866
msgid "Valid choices for this parameter (comma-separated)"
msgstr "此参数的有效选择 (逗号分隔)"

#: part/models.py:3877
msgid "Selection list for this parameter"
msgstr ""

#: part/models.py:3913
msgid "Part Parameter"
msgstr "零件参数"

#: part/models.py:3939
msgid "Parameter cannot be modified - part is locked"
msgstr "参数不能被修改 - 零件被锁定"

#: part/models.py:3977
msgid "Invalid choice for parameter value"
msgstr "无效的参数值选择"

#: part/models.py:4028
msgid "Parent Part"
msgstr "父零件"

#: part/models.py:4036 part/models.py:4144 part/models.py:4145
msgid "Parameter Template"
msgstr "参数模板"

#: part/models.py:4042
msgid "Parameter Value"
msgstr "参数值"

#: part/models.py:4092
msgid "Part Category Parameter Template"
msgstr "零件类别参数模板"

#: part/models.py:4151
msgid "Default Value"
msgstr "默认值"

#: part/models.py:4152
msgid "Default Parameter Value"
msgstr "默认参数值"

#: part/models.py:4283
msgid "BOM item cannot be modified - assembly is locked"
msgstr "物料清单项目不能被修改 - 装配已锁定"

#: part/models.py:4290
msgid "BOM item cannot be modified - variant assembly is locked"
msgstr "物料清单项目不能修改 - 变体装配已锁定"

#: part/models.py:4300
msgid "Select parent part"
msgstr "选择父零件"

#: part/models.py:4310
msgid "Sub part"
msgstr "子零件"

#: part/models.py:4311
msgid "Select part to be used in BOM"
msgstr "选择要用于物料清单的零件"

#: part/models.py:4322
msgid "BOM quantity for this BOM item"
msgstr "此物料清单项目的数量"

#: part/models.py:4328
msgid "This BOM item is optional"
msgstr "此物料清单项目是可选的"

#: part/models.py:4334
msgid "This BOM item is consumable (it is not tracked in build orders)"
msgstr "这个物料清单项目是耗材 (它没有在生产订单中被追踪)"

#: part/models.py:4341
msgid "Overage"
msgstr "超量"

#: part/models.py:4342
msgid "Estimated build wastage quantity (absolute or percentage)"
msgstr "估计生产物浪费量(绝对值或百分比)"

#: part/models.py:4349
msgid "BOM item reference"
msgstr "物料清单项目引用"

#: part/models.py:4357
msgid "BOM item notes"
msgstr "物料清单项目注释"

#: part/models.py:4363
msgid "Checksum"
msgstr "校验和"

#: part/models.py:4364
msgid "BOM line checksum"
msgstr "物料清单行校验和"

#: part/models.py:4369
msgid "Validated"
msgstr "已验证"

#: part/models.py:4370
msgid "This BOM item has been validated"
msgstr "此物料清单项目已验证"

#: part/models.py:4375
msgid "Gets inherited"
msgstr "获取继承的"

#: part/models.py:4376
msgid "This BOM item is inherited by BOMs for variant parts"
msgstr "此物料清单项目是由物料清单继承的变体零件"

#: part/models.py:4382
msgid "Stock items for variant parts can be used for this BOM item"
msgstr "变体零件的库存项可以用于此物料清单项目"

#: part/models.py:4467 stock/models.py:848
msgid "Quantity must be integer value for trackable parts"
msgstr "可追踪零件的数量必须是整数"

#: part/models.py:4477 part/models.py:4479
msgid "Sub part must be specified"
msgstr "必须指定子零件"

#: part/models.py:4624
msgid "BOM Item Substitute"
msgstr "物料清单项目替代品"

#: part/models.py:4645
msgid "Substitute part cannot be the same as the master part"
msgstr "替代品零件不能与主零件相同"

#: part/models.py:4658
msgid "Parent BOM item"
msgstr "上级物料清单项目"

#: part/models.py:4666
msgid "Substitute part"
msgstr "替代品零件"

#: part/models.py:4682
msgid "Part 1"
msgstr "零件 1"

#: part/models.py:4690
msgid "Part 2"
msgstr "零件2"

#: part/models.py:4691
msgid "Select Related Part"
msgstr "选择相关的零件"

#: part/models.py:4698
msgid "Note for this relationship"
msgstr "此关系的注释"

#: part/models.py:4717
msgid "Part relationship cannot be created between a part and itself"
msgstr "零件关系不能在零件和自身之间创建"

#: part/models.py:4722
msgid "Duplicate relationship already exists"
msgstr "复制关系已经存在"

#: part/serializers.py:125
msgid "Parent Category"
msgstr "上级类别"

#: part/serializers.py:126
msgid "Parent part category"
msgstr "上级零件类别"

#: part/serializers.py:134 part/serializers.py:168
msgid "Subcategories"
msgstr "子类别"

#: part/serializers.py:207
msgid "Results"
msgstr "结果"

#: part/serializers.py:208
msgid "Number of results recorded against this template"
msgstr "根据该模板记录的结果数量"

#: part/serializers.py:235 part/serializers.py:253 stock/serializers.py:685
msgid "Purchase currency of this stock item"
msgstr "购买此库存项的货币"

#: part/serializers.py:278
msgid "Speculative Quantity"
msgstr "投机数量"

#: part/serializers.py:287
msgid "Model ID"
msgstr "型号ID"

#: part/serializers.py:313
msgid "File is not an image"
msgstr ""

#: part/serializers.py:344
msgid "Number of parts using this template"
msgstr "使用此模板的零件数"

#: part/serializers.py:489
msgid "Original Part"
msgstr "原始零件"

#: part/serializers.py:490
msgid "Select original part to duplicate"
msgstr "选择要复制的原始零件"

#: part/serializers.py:495
msgid "Copy Image"
msgstr "复制图片"

#: part/serializers.py:496
msgid "Copy image from original part"
msgstr "从原零件复制图片"

#: part/serializers.py:502
msgid "Copy BOM"
msgstr "复制物料清单"

#: part/serializers.py:503
msgid "Copy bill of materials from original part"
msgstr "从原始零件复制材料清单"

#: part/serializers.py:509
msgid "Copy Parameters"
msgstr "复制参数"

#: part/serializers.py:510
msgid "Copy parameter data from original part"
msgstr "从原始零件复制参数数据"

#: part/serializers.py:516
msgid "Copy Notes"
msgstr "复制备注"

#: part/serializers.py:517
msgid "Copy notes from original part"
msgstr "从原始零件复制备注"

#: part/serializers.py:535
msgid "Initial Stock Quantity"
msgstr "初始化库存数量"

#: part/serializers.py:537
msgid "Specify initial stock quantity for this Part. If quantity is zero, no stock is added."
msgstr "指定此零件的初始库存数量。如果数量为零，则不添加任何库存。"

#: part/serializers.py:544
msgid "Initial Stock Location"
msgstr "初始化库存地点"

#: part/serializers.py:545
msgid "Specify initial stock location for this Part"
msgstr "初始化指定此零件的库存地点"

#: part/serializers.py:562
msgid "Select supplier (or leave blank to skip)"
msgstr "选择供应商(或为空以跳过)"

#: part/serializers.py:578
msgid "Select manufacturer (or leave blank to skip)"
msgstr "选择制造商(或为空)"

#: part/serializers.py:588
msgid "Manufacturer part number"
msgstr "制造商零件号"

#: part/serializers.py:595
msgid "Selected company is not a valid supplier"
msgstr "所选公司不是一个有效的供应商"

#: part/serializers.py:604
msgid "Selected company is not a valid manufacturer"
msgstr "所选公司不是一个有效的制造商"

#: part/serializers.py:615
msgid "Manufacturer part matching this MPN already exists"
msgstr "与此制造商零件编号 (MPN) 的相匹配的制造商零件已存在"

#: part/serializers.py:622
msgid "Supplier part matching this SKU already exists"
msgstr "匹配此库存单位 (SKU) 的供应商零件已存在"

#: part/serializers.py:911 part/stocktake.py:222
msgid "Category Name"
msgstr "类别名称"

#: part/serializers.py:937
msgid "Building"
msgstr "正在生产"

#: part/serializers.py:952 part/stocktake.py:223 stock/serializers.py:1084
#: stock/serializers.py:1260 users/ruleset.py:36
msgid "Stock Items"
msgstr "库存项"

#: part/serializers.py:955
msgid "Revisions"
msgstr "修订"

#: part/serializers.py:958
msgid "Suppliers"
msgstr "供应商"

#: part/serializers.py:961 templates/email/low_stock_notification.html:16
#: templates/email/part_event_notification.html:17
msgid "Total Stock"
msgstr "库存总量"

#: part/serializers.py:967
msgid "Unallocated Stock"
msgstr "未分配的库存"

#: part/serializers.py:973
msgid "Variant Stock"
msgstr "变体库存"

#: part/serializers.py:1006
msgid "Duplicate Part"
msgstr "重复零件"

#: part/serializers.py:1007
msgid "Copy initial data from another Part"
msgstr "从另一个零件复制初始数据"

#: part/serializers.py:1013
msgid "Initial Stock"
msgstr "初始库存"

#: part/serializers.py:1014
msgid "Create Part with initial stock quantity"
msgstr "创建具有初始库存数量的零件"

#: part/serializers.py:1020
msgid "Supplier Information"
msgstr "供应商信息"

#: part/serializers.py:1021
msgid "Add initial supplier information for this part"
msgstr "添加此零件的初始供应商信息"

#: part/serializers.py:1029
msgid "Copy Category Parameters"
msgstr "复制类别参数"

#: part/serializers.py:1030
msgid "Copy parameter templates from selected part category"
msgstr "从选择的零件复制参数模版"

#: part/serializers.py:1035
msgid "Existing Image"
msgstr "现有的图片"

#: part/serializers.py:1036
msgid "Filename of an existing part image"
msgstr "现有零件图片的文件名"

#: part/serializers.py:1053
msgid "Image file does not exist"
msgstr "图片不存在"

#: part/serializers.py:1257
msgid "Limit stocktake report to a particular part, and any variant parts"
msgstr "限制盘点报告到某个特定零件以及任何变体零件"

#: part/serializers.py:1267
msgid "Limit stocktake report to a particular part category, and any child categories"
msgstr "限制盘点报告到某个特定零件类别以及任何子类别"

#: part/serializers.py:1277
msgid "Limit stocktake report to a particular stock location, and any child locations"
msgstr "限制盘点报告到某个特定零件库存地点以及任何子位置"

#: part/serializers.py:1283
msgid "Exclude External Stock"
msgstr "排除外部库存"

#: part/serializers.py:1284
msgid "Exclude stock items in external locations"
msgstr "排除外部位置的库存项"

#: part/serializers.py:1289
msgid "Generate Report"
msgstr "生成报告"

#: part/serializers.py:1290
msgid "Generate report file containing calculated stocktake data"
msgstr "生成包含计算出来的盘点数据的报告文件"

#: part/serializers.py:1295
msgid "Update Parts"
msgstr "更新零件"

#: part/serializers.py:1296
msgid "Update specified parts with calculated stocktake data"
msgstr "使用计算出的盘点数据更新指定零件"

#: part/serializers.py:1304
msgid "Stocktake functionality is not enabled"
msgstr "盘点功能未启用"

#: part/serializers.py:1309
msgid "Background worker check failed"
msgstr "后台执行器检查失败"

#: part/serializers.py:1425
msgid "Minimum Price"
msgstr "最低价格"

#: part/serializers.py:1426
msgid "Override calculated value for minimum price"
msgstr "覆盖已计算的最低价格值"

#: part/serializers.py:1433
msgid "Minimum price currency"
msgstr "最低价格货币"

#: part/serializers.py:1440
msgid "Maximum Price"
msgstr "最高价格"

#: part/serializers.py:1441
msgid "Override calculated value for maximum price"
msgstr "覆盖已计算的最高价格值"

#: part/serializers.py:1448
msgid "Maximum price currency"
msgstr "最高价格货币"

#: part/serializers.py:1477
msgid "Update"
msgstr "更新"

#: part/serializers.py:1478
msgid "Update pricing for this part"
msgstr "更新这个零件的价格"

#: part/serializers.py:1501
#, python-brace-format
msgid "Could not convert from provided currencies to {default_currency}"
msgstr "无法将所提供的货币转换为 {default_currency}"

#: part/serializers.py:1508
msgid "Minimum price must not be greater than maximum price"
msgstr "最低价格不能高于最高价格。"

#: part/serializers.py:1511
msgid "Maximum price must not be less than minimum price"
msgstr "最高价格不能低于最低价格"

#: part/serializers.py:1664
msgid "Select the parent assembly"
msgstr "选择父装配"

#: part/serializers.py:1678
msgid "Select the component part"
msgstr "选择零部件"

#: part/serializers.py:1698
msgid "Can Build"
msgstr "可以创建"

#: part/serializers.py:1925
msgid "Select part to copy BOM from"
msgstr "选择要复制物料清单的零件"

#: part/serializers.py:1933
msgid "Remove Existing Data"
msgstr "移除现有数据"

#: part/serializers.py:1934
msgid "Remove existing BOM items before copying"
msgstr "复制前删除现有的物料清单项目"

#: part/serializers.py:1939
msgid "Include Inherited"
msgstr "包含继承的"

#: part/serializers.py:1940
msgid "Include BOM items which are inherited from templated parts"
msgstr "包含从模板零件继承的物料清单项目"

#: part/serializers.py:1945
msgid "Skip Invalid Rows"
msgstr "跳过无效行"

#: part/serializers.py:1946
msgid "Enable this option to skip invalid rows"
msgstr "启用此选项以跳过无效行"

#: part/serializers.py:1951
msgid "Copy Substitute Parts"
msgstr "复制替代品零件"

#: part/serializers.py:1952
msgid "Copy substitute parts when duplicate BOM items"
msgstr "复制物料清单项目时复制替代品零件"

#: part/stocktake.py:218
msgid "Part ID"
msgstr "零件编号"

#: part/stocktake.py:220
msgid "Part Description"
msgstr "零件描述"

#: part/stocktake.py:221
msgid "Category ID"
msgstr "类别 ID"

#: part/stocktake.py:224
msgid "Total Quantity"
msgstr "总数量"

#: part/stocktake.py:225
msgid "Total Cost Min"
msgstr "总费用最小值"

#: part/stocktake.py:226
msgid "Total Cost Max"
msgstr "总费用最大值"

#: part/stocktake.py:284
msgid "Stocktake Report Available"
msgstr "库存盘点报告可用"

#: part/stocktake.py:285
msgid "A new stocktake report is available for download"
msgstr "有新的库存盘点报告可供下载"

#: part/tasks.py:38
msgid "Low stock notification"
msgstr "低库存通知"

#: part/tasks.py:40
#, python-brace-format
msgid "The available stock for {part.name} has fallen below the configured minimum level"
msgstr "可用的 {part.name}库存已经跌到设置的最低值"

#: plugin/api.py:78
msgid "Builtin"
msgstr ""

#: plugin/api.py:92
msgid "Mandatory"
msgstr ""

#: plugin/api.py:103
msgid "Sample"
msgstr ""

#: plugin/api.py:117 plugin/models.py:159
msgid "Installed"
msgstr "已安装"

#: plugin/api.py:184
msgid "Plugin cannot be deleted as it is currently active"
msgstr "插件不能被删除，因为它当前处于激活状态"

#: plugin/base/action/api.py:34
msgid "No action specified"
msgstr "未指定操作"

#: plugin/base/action/api.py:46
msgid "No matching action found"
msgstr "未找到指定操作"

#: plugin/base/barcodes/api.py:211
msgid "No match found for barcode data"
msgstr "未找到匹配条形码数据"

#: plugin/base/barcodes/api.py:215
msgid "Match found for barcode data"
msgstr "找到匹配条形码数据"

#: plugin/base/barcodes/api.py:253 plugin/base/barcodes/serializers.py:77
msgid "Model is not supported"
msgstr "不支持模型"

#: plugin/base/barcodes/api.py:258
msgid "Model instance not found"
msgstr "找不到模型实例"

#: plugin/base/barcodes/api.py:287
msgid "Barcode matches existing item"
msgstr "条形码匹配现有项目"

#: plugin/base/barcodes/api.py:418
msgid "No matching part data found"
msgstr "没有找到匹配的零件数据"

#: plugin/base/barcodes/api.py:434
msgid "No matching supplier parts found"
msgstr "没有找到匹配的供应商零件"

#: plugin/base/barcodes/api.py:437
msgid "Multiple matching supplier parts found"
msgstr "找到多个匹配的供应商零件"

#: plugin/base/barcodes/api.py:450 plugin/base/barcodes/api.py:677
msgid "No matching plugin found for barcode data"
msgstr "没有找到匹配条形码数据的插件"

#: plugin/base/barcodes/api.py:460
msgid "Matched supplier part"
msgstr "匹配的供应商零件"

#: plugin/base/barcodes/api.py:528
msgid "Item has already been received"
msgstr "项目已被接收"

#: plugin/base/barcodes/api.py:576
msgid "No plugin match for supplier barcode"
msgstr ""

#: plugin/base/barcodes/api.py:625
msgid "Multiple matching line items found"
msgstr "找到多个匹配的行项目"

#: plugin/base/barcodes/api.py:628
msgid "No matching line item found"
msgstr "未找到匹配的行项目"

#: plugin/base/barcodes/api.py:674
msgid "No sales order provided"
msgstr "未提供销售订单"

#: plugin/base/barcodes/api.py:683
msgid "Barcode does not match an existing stock item"
msgstr "条形码与现有的库存项不匹配"

#: plugin/base/barcodes/api.py:699
msgid "Stock item does not match line item"
msgstr "库存项与行项目不匹配"

#: plugin/base/barcodes/api.py:729
msgid "Insufficient stock available"
msgstr "可用库存不足"

#: plugin/base/barcodes/api.py:742
msgid "Stock item allocated to sales order"
msgstr "库存项已分配到销售订单"

#: plugin/base/barcodes/api.py:745
msgid "Not enough information"
msgstr "没有足够的信息"

#: plugin/base/barcodes/mixins.py:308
#: plugin/builtin/barcodes/inventree_barcode.py:101
msgid "Found matching item"
msgstr ""

#: plugin/base/barcodes/mixins.py:374
msgid "Supplier part does not match line item"
msgstr ""

#: plugin/base/barcodes/mixins.py:377
msgid "Line item is already completed"
msgstr ""

#: plugin/base/barcodes/mixins.py:414
msgid "Further information required to receive line item"
msgstr "需要更多信息以接收行项目"

#: plugin/base/barcodes/mixins.py:422
msgid "Received purchase order line item"
msgstr "已收到采购订单行项目"

#: plugin/base/barcodes/mixins.py:429
msgid "Failed to receive line item"
msgstr ""

#: plugin/base/barcodes/serializers.py:53
msgid "Scanned barcode data"
msgstr "已扫描的条形码数据"

#: plugin/base/barcodes/serializers.py:62
msgid "Model name to generate barcode for"
msgstr "要生成条形码的模型名称"

#: plugin/base/barcodes/serializers.py:67
msgid "Primary key of model object to generate barcode for"
msgstr "要生成条形码的模型对象的主键"

#: plugin/base/barcodes/serializers.py:137
msgid "Purchase Order to allocate items against"
msgstr "根据采购订单以分配项目"

#: plugin/base/barcodes/serializers.py:143
msgid "Purchase order is not open"
msgstr ""

#: plugin/base/barcodes/serializers.py:161
msgid "Supplier to receive items from"
msgstr ""

#: plugin/base/barcodes/serializers.py:168
msgid "PurchaseOrder to receive items against"
msgstr "根据采购订单以接收项目"

#: plugin/base/barcodes/serializers.py:174
msgid "Purchase order has not been placed"
msgstr "采购订单尚未提交"

#: plugin/base/barcodes/serializers.py:182
msgid "Location to receive items into"
msgstr "项目接收地点"

#: plugin/base/barcodes/serializers.py:188
msgid "Cannot select a structural location"
msgstr "无法选择一个结构性位置"

#: plugin/base/barcodes/serializers.py:196
msgid "Purchase order line item to receive items against"
msgstr ""

#: plugin/base/barcodes/serializers.py:202
msgid "Automatically allocate stock items to the purchase order"
msgstr ""

#: plugin/base/barcodes/serializers.py:215
msgid "Sales Order to allocate items against"
msgstr "根据销售订单以分配项目"

#: plugin/base/barcodes/serializers.py:221
msgid "Sales order is not open"
msgstr ""

#: plugin/base/barcodes/serializers.py:229
msgid "Sales order line item to allocate items against"
msgstr "根据销售订单行项目分配项目"

#: plugin/base/barcodes/serializers.py:236
msgid "Sales order shipment to allocate items against"
msgstr "根据销售订单配送分配项目"

#: plugin/base/barcodes/serializers.py:242
msgid "Shipment has already been delivered"
msgstr "已交付"

#: plugin/base/barcodes/serializers.py:247
msgid "Quantity to allocate"
msgstr "待分配数"

#: plugin/base/label/label.py:39
msgid "Label printing failed"
msgstr "标签打印失败"

#: plugin/base/label/mixins.py:55
msgid "Error rendering label to PDF"
msgstr "渲染标签到 PDF 时出错"

#: plugin/base/label/mixins.py:69
msgid "Error rendering label to HTML"
msgstr "渲染标签到 HTML 时出错"

#: plugin/base/label/mixins.py:146
msgid "No items provided to print"
msgstr "没有要打印的项目"

#: plugin/base/ui/serializers.py:30
msgid "Plugin Name"
msgstr "插件名称"

#: plugin/base/ui/serializers.py:34
msgid "Feature Type"
msgstr "功能类别"

#: plugin/base/ui/serializers.py:39
msgid "Feature Label"
msgstr "特征标签"

#: plugin/base/ui/serializers.py:44
msgid "Feature Title"
msgstr "功能标题"

#: plugin/base/ui/serializers.py:49
msgid "Feature Description"
msgstr "功能描述"

#: plugin/base/ui/serializers.py:54
msgid "Feature Icon"
msgstr "功能图标"

#: plugin/base/ui/serializers.py:58
msgid "Feature Options"
msgstr "特色选项"

#: plugin/base/ui/serializers.py:61
msgid "Feature Context"
msgstr "功能背景"

#: plugin/base/ui/serializers.py:64
msgid "Feature Source (javascript)"
msgstr "功能源 (javascript)"

#: plugin/builtin/barcodes/inventree_barcode.py:27
msgid "InvenTree Barcodes"
msgstr "InvenTree 条形码"

#: plugin/builtin/barcodes/inventree_barcode.py:28
msgid "Provides native support for barcodes"
msgstr "提供条形码本地支持"

#: plugin/builtin/barcodes/inventree_barcode.py:30
#: plugin/builtin/exporter/bom_exporter.py:61
#: plugin/builtin/exporter/inventree_exporter.py:17
#: plugin/builtin/exporter/part_parameter_exporter.py:36
#: plugin/builtin/integration/core_notifications.py:33
#: plugin/builtin/integration/currency_exchange.py:21
#: plugin/builtin/integration/part_notifications.py:25
#: plugin/builtin/labels/inventree_label.py:26
#: plugin/builtin/labels/inventree_machine.py:64
#: plugin/builtin/labels/label_sheet.py:71
#: plugin/builtin/suppliers/digikey.py:19 plugin/builtin/suppliers/lcsc.py:21
#: plugin/builtin/suppliers/mouser.py:19 plugin/builtin/suppliers/tme.py:21
msgid "InvenTree contributors"
msgstr "InvenTree 贡献者"

#: plugin/builtin/barcodes/inventree_barcode.py:34
msgid "Internal Barcode Format"
msgstr "条形码内部格式"

#: plugin/builtin/barcodes/inventree_barcode.py:35
msgid "Select an internal barcode format"
msgstr "选择内部条形码格式"

#: plugin/builtin/barcodes/inventree_barcode.py:37
msgid "JSON barcodes (human readable)"
msgstr "JSON 条形码 (人类可读)"

#: plugin/builtin/barcodes/inventree_barcode.py:38
msgid "Short barcodes (space optimized)"
msgstr "短条形码 (空间优化)"

#: plugin/builtin/barcodes/inventree_barcode.py:43
msgid "Short Barcode Prefix"
msgstr "短条形码前缀"

#: plugin/builtin/barcodes/inventree_barcode.py:45
msgid "Customize the prefix used for short barcodes, may be useful for environments with multiple InvenTree instances"
msgstr "自定义用于短条形码的前缀，可能对有多个InvenTree实例的环境有用。"

#: plugin/builtin/exporter/bom_exporter.py:17
msgid "Levels"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:18
msgid "Number of levels to export"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:23
#: plugin/builtin/exporter/part_parameter_exporter.py:17
msgid "Stock Data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:23
#: plugin/builtin/exporter/part_parameter_exporter.py:17
msgid "Include part stock data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:27
#: plugin/builtin/exporter/part_parameter_exporter.py:21
msgid "Pricing Data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:27
#: plugin/builtin/exporter/part_parameter_exporter.py:21
msgid "Include part pricing data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:31
msgid "Supplier Data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:31
msgid "Include supplier data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:36
msgid "Manufacturer Data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:37
msgid "Include manufacturer data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:42
msgid "Substitute Data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:43
msgid "Include substitute part data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:48
msgid "Parameter Data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:49
msgid "Include part parameter data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:58
msgid "Multi-Level BOM Exporter"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:59
msgid "Provides support for exporting multi-level BOMs"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:96
msgid "BOM Level"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:102
#, python-brace-format
msgid "Substitute {n}"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:108
#, python-brace-format
msgid "Supplier {n}"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:109
#, python-brace-format
msgid "Supplier {n} SKU"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:110
#, python-brace-format
msgid "Supplier {n} MPN"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:116
#, python-brace-format
msgid "Manufacturer {n}"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:117
#, python-brace-format
msgid "Manufacturer {n} MPN"
msgstr ""

#: plugin/builtin/exporter/inventree_exporter.py:14
msgid "InvenTree Generic Exporter"
msgstr ""

#: plugin/builtin/exporter/inventree_exporter.py:15
msgid "Provides support for exporting data from InvenTree"
msgstr ""

#: plugin/builtin/exporter/part_parameter_exporter.py:33
msgid "Part Parameter Exporter"
msgstr ""

#: plugin/builtin/exporter/part_parameter_exporter.py:34
msgid "Exporter for part parameter data"
msgstr ""

#: plugin/builtin/integration/core_notifications.py:32
msgid "InvenTree Notifications"
msgstr "Inventree 通知"

#: plugin/builtin/integration/core_notifications.py:34
msgid "Integrated outgoing notification methods"
msgstr "集成的输出通知方法"

#: plugin/builtin/integration/core_notifications.py:39
#: plugin/builtin/integration/core_notifications.py:78
msgid "Enable email notifications"
msgstr "启用电子邮件通知"

#: plugin/builtin/integration/core_notifications.py:40
#: plugin/builtin/integration/core_notifications.py:79
msgid "Allow sending of emails for event notifications"
msgstr "允许发送事件通知邮件"

#: plugin/builtin/integration/core_notifications.py:45
msgid "Enable slack notifications"
msgstr "启用slack通知"

#: plugin/builtin/integration/core_notifications.py:47
msgid "Allow sending of slack channel messages for event notifications"
msgstr "允许发送事件通知的 slack 频道消息"

#: plugin/builtin/integration/core_notifications.py:53
msgid "Slack incoming webhook url"
msgstr "Slack传入Webhook url"

#: plugin/builtin/integration/core_notifications.py:54
msgid "URL that is used to send messages to a slack channel"
msgstr "用于发送消息到slack频道的 URL"

#: plugin/builtin/integration/core_notifications.py:164
msgid "Open link"
msgstr "打开链接"

#: plugin/builtin/integration/currency_exchange.py:22
msgid "InvenTree Currency Exchange"
msgstr "InvenTree 货币兑换"

#: plugin/builtin/integration/currency_exchange.py:23
msgid "Default currency exchange integration"
msgstr "默认货币兑换集成"

#: plugin/builtin/integration/part_notifications.py:24
msgid "Part Notifications"
msgstr ""

#: plugin/builtin/integration/part_notifications.py:26
msgid "Notify users about part changes"
msgstr ""

#: plugin/builtin/integration/part_notifications.py:31
msgid "Send notifications"
msgstr ""

#: plugin/builtin/integration/part_notifications.py:32
msgid "Send notifications for part changes to subscribed users"
msgstr ""

#: plugin/builtin/integration/part_notifications.py:49
msgid "Changed part notification"
msgstr ""

#: plugin/builtin/integration/part_notifications.py:59
#, python-brace-format
msgid "The part `{part.name}` has been triggered with a `{part_action}` event"
msgstr ""

#: plugin/builtin/labels/inventree_label.py:23
msgid "InvenTree PDF label printer"
msgstr "InvenTree PDF 标签打印机"

#: plugin/builtin/labels/inventree_label.py:24
msgid "Provides native support for printing PDF labels"
msgstr "为打印 PDF 标签提供本机支持"

#: plugin/builtin/labels/inventree_label.py:32
#: plugin/builtin/labels/label_sheet.py:77
msgid "Debug mode"
msgstr "Debug模式"

#: plugin/builtin/labels/inventree_label.py:33
#: plugin/builtin/labels/label_sheet.py:78
msgid "Enable debug mode - returns raw HTML instead of PDF"
msgstr "启用Debug模式 - 返回原始的 HTML 而不是 PDF"

#: plugin/builtin/labels/inventree_machine.py:61
msgid "InvenTree machine label printer"
msgstr "InvenTree 设备标签打印机"

#: plugin/builtin/labels/inventree_machine.py:62
msgid "Provides support for printing using a machine"
msgstr "提供使用设备打印的支持"

#: plugin/builtin/labels/inventree_machine.py:164
msgid "last used"
msgstr "最近使用"

#: plugin/builtin/labels/inventree_machine.py:181
msgid "Options"
msgstr "选项"

#: plugin/builtin/labels/label_sheet.py:30
msgid "Page size for the label sheet"
msgstr "标签页大小"

#: plugin/builtin/labels/label_sheet.py:35
msgid "Skip Labels"
msgstr "跳过标签"

#: plugin/builtin/labels/label_sheet.py:36
msgid "Skip this number of labels when printing label sheets"
msgstr "打印标签页时跳过标签的数量"

#: plugin/builtin/labels/label_sheet.py:42
msgid "Border"
msgstr "边框"

#: plugin/builtin/labels/label_sheet.py:43
msgid "Print a border around each label"
msgstr "打印每个标签的边框"

#: plugin/builtin/labels/label_sheet.py:48 report/models.py:371
msgid "Landscape"
msgstr "横屏模式"

#: plugin/builtin/labels/label_sheet.py:49
msgid "Print the label sheet in landscape mode"
msgstr "在横屏模式下打印标签表"

#: plugin/builtin/labels/label_sheet.py:54
msgid "Page Margin"
msgstr ""

#: plugin/builtin/labels/label_sheet.py:55
msgid "Margin around the page in mm"
msgstr ""

#: plugin/builtin/labels/label_sheet.py:68
msgid "InvenTree Label Sheet Printer"
msgstr "库存树标签工作表"

#: plugin/builtin/labels/label_sheet.py:69
msgid "Arrays multiple labels onto a single sheet"
msgstr "单张纸上的组合多个标签"

#: plugin/builtin/labels/label_sheet.py:121
msgid "Label is too large for page size"
msgstr "标签大过页面"

#: plugin/builtin/labels/label_sheet.py:160
msgid "No labels were generated"
msgstr "没有生成标签"

#: plugin/builtin/suppliers/digikey.py:16
msgid "Supplier Integration - DigiKey"
msgstr "供应商集成 - DigiKey"

#: plugin/builtin/suppliers/digikey.py:17
msgid "Provides support for scanning DigiKey barcodes"
msgstr "为扫描 DigiKey 条形码提供支持"

#: plugin/builtin/suppliers/digikey.py:26
msgid "The Supplier which acts as 'DigiKey'"
msgstr "作为“DigiKey”的供应商。"

#: plugin/builtin/suppliers/lcsc.py:18
msgid "Supplier Integration - LCSC"
msgstr "供应商集成 - LCSC"

#: plugin/builtin/suppliers/lcsc.py:19
msgid "Provides support for scanning LCSC barcodes"
msgstr "为扫描 LCSC 条形码提供支持"

#: plugin/builtin/suppliers/lcsc.py:27
msgid "The Supplier which acts as 'LCSC'"
msgstr "作为“LCSC”的供应商。"

#: plugin/builtin/suppliers/mouser.py:16
msgid "Supplier Integration - Mouser"
msgstr "供应商集成 - Mouser"

#: plugin/builtin/suppliers/mouser.py:17
msgid "Provides support for scanning Mouser barcodes"
msgstr "为扫描 Mouser条形码提供支持"

#: plugin/builtin/suppliers/mouser.py:25
msgid "The Supplier which acts as 'Mouser'"
msgstr "作为“Mouser”的供应商。"

#: plugin/builtin/suppliers/tme.py:18
msgid "Supplier Integration - TME"
msgstr "供应商集成 - TME"

#: plugin/builtin/suppliers/tme.py:19
msgid "Provides support for scanning TME barcodes"
msgstr "为扫描 TME 条形码提供支持"

#: plugin/builtin/suppliers/tme.py:27
msgid "The Supplier which acts as 'TME'"
msgstr "作为‘TME’的供应商"

#: plugin/installer.py:229 plugin/installer.py:307
msgid "Only staff users can administer plugins"
msgstr "只有员工用户可以管理插件"

#: plugin/installer.py:232
msgid "Plugin installation is disabled"
msgstr "插件安装已禁用"

#: plugin/installer.py:269
msgid "Installed plugin successfully"
msgstr "插件安装成功"

#: plugin/installer.py:274
#, python-brace-format
msgid "Installed plugin into {path}"
msgstr "插件安装到 {path}"

#: plugin/installer.py:298
msgid "Plugin was not found in registry"
msgstr "在插件仓库中找不到插件"

#: plugin/installer.py:301
msgid "Plugin is not a packaged plugin"
msgstr "插件不是一个打包的插件"

#: plugin/installer.py:304
msgid "Plugin package name not found"
msgstr "找不到插件包名称"

#: plugin/installer.py:324
msgid "Plugin uninstalling is disabled"
msgstr "插件卸载已禁用"

#: plugin/installer.py:328
msgid "Plugin cannot be uninstalled as it is currently active"
msgstr "插件无法卸载，因为它目前处于激活状态"

#: plugin/installer.py:332
msgid "Plugin is not installed"
msgstr ""

#: plugin/installer.py:348
msgid "Plugin installation not found"
msgstr ""

#: plugin/installer.py:363
msgid "Uninstalled plugin successfully"
msgstr "插件卸载成功"

#: plugin/models.py:38
msgid "Plugin Configuration"
msgstr "插件配置"

#: plugin/models.py:39
msgid "Plugin Configurations"
msgstr "插件配置"

#: plugin/models.py:46
msgid "Key of plugin"
msgstr "插件的键"

#: plugin/models.py:54
msgid "PluginName of the plugin"
msgstr "插件名称"

#: plugin/models.py:61 plugin/serializers.py:119
msgid "Package Name"
msgstr "软件包名"

#: plugin/models.py:63
msgid "Name of the installed package, if the plugin was installed via PIP"
msgstr "已安装的软件包名字，如果插件是通过 PIP 安装的"

#: plugin/models.py:68
msgid "Is the plugin active"
msgstr "插件是否激活"

#: plugin/models.py:168
msgid "Sample plugin"
msgstr "示例插件"

#: plugin/models.py:176
msgid "Builtin Plugin"
msgstr "内置插件"

#: plugin/models.py:184
msgid "Mandatory Plugin"
msgstr ""

#: plugin/models.py:189
msgid "Package Plugin"
msgstr "软件包插件"

#: plugin/models.py:268
msgid "Plugin"
msgstr "插件"

#: plugin/models.py:315
msgid "Method"
msgstr "方法"

#: plugin/plugin.py:312
msgid "No author found"
msgstr "未找到作者"

#: plugin/registry.py:611
#, python-brace-format
msgid "Plugin '{p}' is not compatible with the current InvenTree version {v}"
msgstr "插件 '{p}' 与当前 InvenTree 版本{v} 不兼容"

#: plugin/registry.py:614
#, python-brace-format
msgid "Plugin requires at least version {v}"
msgstr "插件所需最低版本 {v}"

#: plugin/registry.py:616
#, python-brace-format
msgid "Plugin requires at most version {v}"
msgstr "插件所需最高版本 {v}"

#: plugin/samples/integration/sample.py:52
msgid "Enable PO"
msgstr "启用 采购功能"

#: plugin/samples/integration/sample.py:53
msgid "Enable PO functionality in InvenTree interface"
msgstr "在 InvenTree 界面中启用采购功能"

#: plugin/samples/integration/sample.py:58
msgid "API Key"
msgstr "API密钥"

#: plugin/samples/integration/sample.py:59
msgid "Key required for accessing external API"
msgstr "访问外部 API 所需的密钥"

#: plugin/samples/integration/sample.py:63
msgid "Numerical"
msgstr "数字化"

#: plugin/samples/integration/sample.py:64
msgid "A numerical setting"
msgstr "数值设置"

#: plugin/samples/integration/sample.py:69
msgid "Choice Setting"
msgstr "选择设置"

#: plugin/samples/integration/sample.py:70
msgid "A setting with multiple choices"
msgstr "带有多个选项的设置"

#: plugin/samples/integration/sample_currency_exchange.py:15
msgid "Sample currency exchange plugin"
msgstr "货币兑换插件示例"

#: plugin/samples/integration/sample_currency_exchange.py:18
msgid "InvenTree Contributors"
msgstr "InvenTree 贡献者"

#: plugin/samples/integration/user_interface_sample.py:27
msgid "Enable Part Panels"
msgstr "启用零件面板"

#: plugin/samples/integration/user_interface_sample.py:28
msgid "Enable custom panels for Part views"
msgstr "启用自定义面板来查看部件"

#: plugin/samples/integration/user_interface_sample.py:33
msgid "Enable Purchase Order Panels"
msgstr "启用采购订单面板"

#: plugin/samples/integration/user_interface_sample.py:34
msgid "Enable custom panels for Purchase Order views"
msgstr "启用自定义面板以查看购买订单"

#: plugin/samples/integration/user_interface_sample.py:39
msgid "Enable Broken Panels"
msgstr "启用破损面板"

#: plugin/samples/integration/user_interface_sample.py:40
msgid "Enable broken panels for testing"
msgstr "启用损坏的面板来测试"

#: plugin/samples/integration/user_interface_sample.py:45
msgid "Enable Dynamic Panel"
msgstr "启用动态面板"

#: plugin/samples/integration/user_interface_sample.py:46
msgid "Enable dynamic panels for testing"
msgstr "启用动态面板来测试"

#: plugin/samples/integration/user_interface_sample.py:98
msgid "Part Panel"
msgstr "部件面板"

#: plugin/samples/integration/user_interface_sample.py:131
msgid "Broken Dashboard Item"
msgstr "损坏的仪表板项目"

#: plugin/samples/integration/user_interface_sample.py:133
msgid "This is a broken dashboard item - it will not render!"
msgstr "这是一个损坏的仪表板项 - 它不会呈现！"

#: plugin/samples/integration/user_interface_sample.py:139
msgid "Sample Dashboard Item"
msgstr "仪表盘项目示例"

#: plugin/samples/integration/user_interface_sample.py:141
msgid "This is a sample dashboard item. It renders a simple string of HTML content."
msgstr "这是一个示例仪表板项目。它提供了一个简单的HTML内容字符串。"

#: plugin/samples/integration/user_interface_sample.py:147
msgid "Context Dashboard Item"
msgstr "上下文面板"

#: plugin/samples/integration/user_interface_sample.py:161
msgid "Admin Dashboard Item"
msgstr "管理面板"

#: plugin/samples/integration/user_interface_sample.py:162
msgid "This is an admin-only dashboard item."
msgstr "这是一个仅限管理员查看的面板。"

#: plugin/serializers.py:86
msgid "Source File"
msgstr "源文件"

#: plugin/serializers.py:87
msgid "Path to the source file for admin integration"
msgstr "管理员集成的源文件路径"

#: plugin/serializers.py:94
msgid "Optional context data for the admin integration"
msgstr "管理员集成的可选上下文数据"

#: plugin/serializers.py:110
msgid "Source URL"
msgstr "源URL"

#: plugin/serializers.py:112
msgid "Source for the package - this can be a custom registry or a VCS path"
msgstr "软件包的来源 - 这可以是自定义注册表或 VCS 路径"

#: plugin/serializers.py:121
msgid "Name for the Plugin Package - can also contain a version indicator"
msgstr "插件包名称 - 也可以包含版本指示器"

#: plugin/serializers.py:128
msgid "Version"
msgstr "版本"

#: plugin/serializers.py:130
msgid "Version specifier for the plugin. Leave blank for latest version."
msgstr "插件版本说明。新版请留白。"

#: plugin/serializers.py:135
msgid "Confirm plugin installation"
msgstr "确认插件安装"

#: plugin/serializers.py:137
msgid "This will install this plugin now into the current instance. The instance will go into maintenance."
msgstr "这将把这个插件安装到当前实例中。这个实例将进行维护。"

#: plugin/serializers.py:150
msgid "Installation not confirmed"
msgstr "安装尚未确认"

#: plugin/serializers.py:152
msgid "Either packagename of URL must be provided"
msgstr "必须提供软件包名称或者URL"

#: plugin/serializers.py:188
msgid "Full reload"
msgstr "完全重载"

#: plugin/serializers.py:189
msgid "Perform a full reload of the plugin registry"
msgstr "执行插件库的完整重载"

#: plugin/serializers.py:195
msgid "Force reload"
msgstr "强制重载"

#: plugin/serializers.py:197
msgid "Force a reload of the plugin registry, even if it is already loaded"
msgstr "强制重载插件库，即使已经加载"

#: plugin/serializers.py:204
msgid "Collect plugins"
msgstr "收集插件"

#: plugin/serializers.py:205
msgid "Collect plugins and add them to the registry"
msgstr "收集插件并添加到注册表中"

#: plugin/serializers.py:232
msgid "Activate Plugin"
msgstr "激活插件"

#: plugin/serializers.py:233
msgid "Activate this plugin"
msgstr "激活此插件"

#: plugin/serializers.py:253
msgid "Delete configuration"
msgstr "删除配置"

#: plugin/serializers.py:254
msgid "Delete the plugin configuration from the database"
msgstr "从数据库中删除插件配置"

#: report/api.py:44 report/serializers.py:98 report/serializers.py:148
msgid "Items"
msgstr "项目"

#: report/api.py:121
msgid "Plugin not found"
msgstr "插件未找到"

#: report/api.py:123
msgid "Plugin is not active"
msgstr "插件未激活"

#: report/api.py:125
msgid "Plugin does not support label printing"
msgstr "插件不支持标签打印"

#: report/api.py:173
msgid "Invalid label dimensions"
msgstr "无效的标签尺寸"

#: report/api.py:188 report/api.py:267
msgid "No valid items provided to template"
msgstr "没有有效的项目提供到模板"

#: report/helpers.py:43
msgid "A4"
msgstr "A4"

#: report/helpers.py:44
msgid "A3"
msgstr "A3"

#: report/helpers.py:45
msgid "Legal"
msgstr "法律"

#: report/helpers.py:46
msgid "Letter"
msgstr "字母"

#: report/models.py:117
msgid "Template file with this name already exists"
msgstr "已存在具有此名称的模板"

#: report/models.py:204
msgid "Template name"
msgstr "模版名称"

#: report/models.py:210
msgid "Template description"
msgstr "模板说明"

#: report/models.py:216
msgid "Revision number (auto-increments)"
msgstr "修订编号 (自动增量)"

#: report/models.py:222
msgid "Attach to Model on Print"
msgstr "打印时附加到模型"

#: report/models.py:224
msgid "Save report output as an attachment against linked model instance when printing"
msgstr "打印时将报告输出保存为附件与链接模型实例"

#: report/models.py:265
msgid "Filename Pattern"
msgstr "文件名样式"

#: report/models.py:266
msgid "Pattern for generating filenames"
msgstr "生成文件名模式"

#: report/models.py:271
msgid "Template is enabled"
msgstr "模板已启用"

#: report/models.py:278
msgid "Target model type for template"
msgstr "模版的目标模型类型"

#: report/models.py:298
msgid "Filters"
msgstr "筛选器"

#: report/models.py:299
msgid "Template query filters (comma-separated list of key=value pairs)"
msgstr "模版查询筛选器 (逗号分隔的键值对列表)"

#: report/models.py:358 report/models.py:564
msgid "Template file"
msgstr "模板包文件"

#: report/models.py:366
msgid "Page size for PDF reports"
msgstr "PDF 报告的页面大小"

#: report/models.py:372
msgid "Render report in landscape orientation"
msgstr "横向渲染报告"

#: report/models.py:486
#, python-brace-format
msgid "Report generated from template {self.name}"
msgstr ""

#: report/models.py:511
msgid "Error generating report"
msgstr ""

#: report/models.py:570
msgid "Width [mm]"
msgstr "宽度 [mm]"

#: report/models.py:571
msgid "Label width, specified in mm"
msgstr "标签宽度，以毫米为单位。"

#: report/models.py:577
msgid "Height [mm]"
msgstr "高度 [mm]"

#: report/models.py:578
msgid "Label height, specified in mm"
msgstr "标签高度，以毫米为单位。"

#: report/models.py:688
msgid "Error printing labels"
msgstr ""

#: report/models.py:707
msgid "Snippet"
msgstr "代码片段"

#: report/models.py:708
msgid "Report snippet file"
msgstr "报告代码片段文件"

#: report/models.py:715
msgid "Snippet file description"
msgstr "代码片段文件描述"

#: report/models.py:733
msgid "Asset"
msgstr "资产"

#: report/models.py:734
msgid "Report asset file"
msgstr "报告资产文件"

#: report/models.py:741
msgid "Asset file description"
msgstr "资产文件描述"

#: report/serializers.py:91
msgid "Select report template"
msgstr "选择报表模板"

#: report/serializers.py:99 report/serializers.py:149
msgid "List of item primary keys to include in the report"
msgstr "要包含在报告中的项目主键列表"

#: report/serializers.py:132
msgid "Select label template"
msgstr "选择标签模板"

#: report/serializers.py:140
msgid "Printing Plugin"
msgstr "打印插件"

#: report/serializers.py:141
msgid "Select plugin to use for label printing"
msgstr "选择用于标签打印的插件"

#: report/templates/label/part_label.html:31
#: report/templates/label/stockitem_qr.html:21
#: report/templates/label/stocklocation_qr.html:20
msgid "QR Code"
msgstr "二维码"

#: report/templates/label/part_label_code128.html:31
#: report/templates/label/stocklocation_qr_and_text.html:31
msgid "QR code"
msgstr "二维码"

#: report/templates/report/inventree_bill_of_materials_report.html:100
msgid "Bill of Materials"
msgstr "物料清单"

#: report/templates/report/inventree_bill_of_materials_report.html:133
msgid "Materials needed"
msgstr "所需材料"

#: report/templates/report/inventree_build_order_report.html:98
#: report/templates/report/inventree_purchase_order_report.html:40
#: report/templates/report/inventree_sales_order_report.html:40
#: report/templates/report/inventree_sales_order_shipment_report.html:37
#: report/templates/report/inventree_test_report.html:84
#: report/templates/report/inventree_test_report.html:162
msgid "Part image"
msgstr "零件图像"

#: report/templates/report/inventree_build_order_report.html:121
msgid "Issued"
msgstr "已派发"

#: report/templates/report/inventree_build_order_report.html:146
msgid "Required For"
msgstr "需要给"

#: report/templates/report/inventree_purchase_order_report.html:15
msgid "Supplier was deleted"
msgstr "供应商已删除"

#: report/templates/report/inventree_purchase_order_report.html:30
#: report/templates/report/inventree_sales_order_report.html:30
msgid "Unit Price"
msgstr "单位价格"

#: report/templates/report/inventree_purchase_order_report.html:55
#: report/templates/report/inventree_return_order_report.html:48
#: report/templates/report/inventree_sales_order_report.html:55
msgid "Extra Line Items"
msgstr "额外行项目"

#: report/templates/report/inventree_purchase_order_report.html:72
#: report/templates/report/inventree_sales_order_report.html:72
msgid "Total"
msgstr "总计"

#: report/templates/report/inventree_return_order_report.html:25
#: report/templates/report/inventree_sales_order_shipment_report.html:45
#: report/templates/report/inventree_test_report.html:88 stock/models.py:1006
#: stock/serializers.py:163
msgid "Serial Number"
msgstr "序列号"

#: report/templates/report/inventree_sales_order_shipment_report.html:23
msgid "Allocations"
msgstr "分配"

#: report/templates/report/inventree_sales_order_shipment_report.html:47
msgid "Batch"
msgstr "队列"

#: report/templates/report/inventree_stock_location_report.html:97
msgid "Stock location items"
msgstr "库存地点项目"

#: report/templates/report/inventree_test_report.html:21
msgid "Stock Item Test Report"
msgstr "库存项测试报告"

#: report/templates/report/inventree_test_report.html:97
msgid "Test Results"
msgstr "测试结果"

#: report/templates/report/inventree_test_report.html:102
msgid "Test"
msgstr "测试"

#: report/templates/report/inventree_test_report.html:129
msgid "Pass"
msgstr "通过"

#: report/templates/report/inventree_test_report.html:131
msgid "Fail"
msgstr "失败"

#: report/templates/report/inventree_test_report.html:138
msgid "No result (required)"
msgstr "无结果 (必填)"

#: report/templates/report/inventree_test_report.html:140
msgid "No result"
msgstr "没有结果"

#: report/templates/report/inventree_test_report.html:153
#: stock/serializers.py:668
msgid "Installed Items"
msgstr "已安装的项目"

#: report/templates/report/inventree_test_report.html:167
msgid "Serial"
msgstr "系列"

#: report/templatetags/report.py:144
msgid "Asset file does not exist"
msgstr "资产文件不存在"

#: report/templatetags/report.py:201 report/templatetags/report.py:277
msgid "Image file not found"
msgstr "找不到图片文件"

#: report/templatetags/report.py:302
msgid "part_image tag requires a Part instance"
msgstr "parpart_image 标签需要一个零件实例"

#: report/templatetags/report.py:349
msgid "company_image tag requires a Company instance"
msgstr "公司_图片标签需要一个公司实例"

#: stock/api.py:255
msgid "Filter by location depth"
msgstr "按位置深度筛选"

#: stock/api.py:275
msgid "Filter by top-level locations"
msgstr "按顶级位置筛选"

#: stock/api.py:290
msgid "Include sub-locations in filtered results"
msgstr "在筛选结果中包含子地点"

#: stock/api.py:311 stock/serializers.py:1256
msgid "Parent Location"
msgstr "上级地点"

#: stock/api.py:312
msgid "Filter by parent location"
msgstr "按上级位置筛选"

#: stock/api.py:554
msgid "Part name (case insensitive)"
msgstr "零件名称 (不区分大小写)"

#: stock/api.py:560
msgid "Part name contains (case insensitive)"
msgstr "零件名称包含 (不区分大小写)"

#: stock/api.py:566
msgid "Part name (regex)"
msgstr "零件名称 (正则表达式)"

#: stock/api.py:571
msgid "Part IPN (case insensitive)"
msgstr "内部零件号 (不区分大小写)"

#: stock/api.py:577
msgid "Part IPN contains (case insensitive)"
msgstr "内部零件号 (不区分大小写)"

#: stock/api.py:583
msgid "Part IPN (regex)"
msgstr "内部零件号 (正则表达式)"

#: stock/api.py:595
msgid "Minimum stock"
msgstr "最低库存"

#: stock/api.py:599
msgid "Maximum stock"
msgstr "最大库存"

#: stock/api.py:602
msgid "Status Code"
msgstr "状态代码"

#: stock/api.py:642
msgid "External Location"
msgstr "外部地点"

#: stock/api.py:741
msgid "Consumed by Build Order"
msgstr ""

#: stock/api.py:751
msgid "Installed in other stock item"
msgstr ""

#: stock/api.py:840
msgid "Part Tree"
msgstr "零件树"

#: stock/api.py:862
msgid "Updated before"
msgstr ""

#: stock/api.py:866
msgid "Updated after"
msgstr ""

#: stock/api.py:870
msgid "Stocktake Before"
msgstr ""

#: stock/api.py:874
msgid "Stocktake After"
msgstr ""

#: stock/api.py:879
msgid "Expiry date before"
msgstr "过期日期前"

#: stock/api.py:883
msgid "Expiry date after"
msgstr "过期日期后"

#: stock/api.py:886 stock/serializers.py:673
msgid "Stale"
msgstr "过期"

#: stock/api.py:987
msgid "Quantity is required"
msgstr "请先输入数量"

#: stock/api.py:992
msgid "Valid part must be supplied"
msgstr "必须提供有效的零件"

#: stock/api.py:1019
msgid "The given supplier part does not exist"
msgstr "给定的供应商零件不存在"

#: stock/api.py:1029
msgid "The supplier part has a pack size defined, but flag use_pack_size not set"
msgstr "供应商零件有定义的包装大小，但 use_pack_size 标志未设置"

#: stock/api.py:1056
msgid "Serial numbers cannot be supplied for a non-trackable part"
msgstr "不能为不可跟踪的零件提供序列号"

#: stock/models.py:70
msgid "Stock Location type"
msgstr "库存地点类型"

#: stock/models.py:71
msgid "Stock Location types"
msgstr "库存地点类型"

#: stock/models.py:97
msgid "Default icon for all locations that have no icon set (optional)"
msgstr "为所有没有图标的位置设置默认图标(可选)"

#: stock/models.py:155 stock/models.py:968
msgid "Stock Location"
msgstr "库存地点"

#: stock/models.py:156 users/ruleset.py:35
msgid "Stock Locations"
msgstr "库存地点"

#: stock/models.py:205 stock/models.py:1133
msgid "Owner"
msgstr "所有者"

#: stock/models.py:206 stock/models.py:1134
msgid "Select Owner"
msgstr "选择所有者"

#: stock/models.py:214
msgid "Stock items may not be directly located into a structural stock locations, but may be located to child locations."
msgstr "库存项可能不直接位于结构库存地点，但可能位于其子地点。"

#: stock/models.py:221 users/models.py:499
msgid "External"
msgstr "外部"

#: stock/models.py:222
msgid "This is an external stock location"
msgstr "这是一个外部库存地点"

#: stock/models.py:228
msgid "Location type"
msgstr "位置类型"

#: stock/models.py:232
msgid "Stock location type of this location"
msgstr "该位置的库存地点类型"

#: stock/models.py:304
msgid "You cannot make this stock location structural because some stock items are already located into it!"
msgstr "您不能将此库存地点设置为结构性，因为某些库存项已经位于它！"

#: stock/models.py:562
msgid "Part must be specified"
msgstr "必须指定零件"

#: stock/models.py:827
msgid "Stock items cannot be located into structural stock locations!"
msgstr "库存项不能存放在结构性库存地点！"

#: stock/models.py:854 stock/serializers.py:516
msgid "Stock item cannot be created for virtual parts"
msgstr "无法为虚拟零件创建库存项"

#: stock/models.py:871
#, python-brace-format
msgid "Part type ('{self.supplier_part.part}') must be {self.part}"
msgstr "零件类型 ('{self.supplier_part.part}') 必须为 {self.part}"

#: stock/models.py:881 stock/models.py:894
msgid "Quantity must be 1 for item with a serial number"
msgstr "有序列号的项目的数量必须是1"

#: stock/models.py:884
msgid "Serial number cannot be set if quantity greater than 1"
msgstr "如果数量大于1，则不能设置序列号"

#: stock/models.py:906
msgid "Item cannot belong to itself"
msgstr "项目不能属于其自身"

#: stock/models.py:911
msgid "Item must have a build reference if is_building=True"
msgstr "如果is_building=True，则项必须具有构建引用"

#: stock/models.py:924
msgid "Build reference does not point to the same part object"
msgstr "构建引用未指向同一零件对象"

#: stock/models.py:938
msgid "Parent Stock Item"
msgstr "母库存项目"

#: stock/models.py:950
msgid "Base part"
msgstr "基础零件"

#: stock/models.py:960
msgid "Select a matching supplier part for this stock item"
msgstr "为此库存项目选择匹配的供应商零件"

#: stock/models.py:972
msgid "Where is this stock item located?"
msgstr "这个库存物品在哪里？"

#: stock/models.py:980 stock/serializers.py:1667
msgid "Packaging this stock item is stored in"
msgstr "包装此库存物品存储在"

#: stock/models.py:986
msgid "Installed In"
msgstr "安装于"

#: stock/models.py:991
msgid "Is this item installed in another item?"
msgstr "此项目是否安装在另一个项目中？"

#: stock/models.py:1010
msgid "Serial number for this item"
msgstr "此项目的序列号"

#: stock/models.py:1027 stock/serializers.py:1652
msgid "Batch code for this stock item"
msgstr "此库存项的批号"

#: stock/models.py:1032
msgid "Stock Quantity"
msgstr "库存数量"

#: stock/models.py:1042
msgid "Source Build"
msgstr "源代码构建"

#: stock/models.py:1045
msgid "Build for this stock item"
msgstr "为此库存项目构建"

#: stock/models.py:1052
msgid "Consumed By"
msgstr "消费者"

#: stock/models.py:1055
msgid "Build order which consumed this stock item"
msgstr "构建消耗此库存项的生产订单"

#: stock/models.py:1064
msgid "Source Purchase Order"
msgstr "采购订单来源"

#: stock/models.py:1068
msgid "Purchase order for this stock item"
msgstr "此库存商品的采购订单"

#: stock/models.py:1074
msgid "Destination Sales Order"
msgstr "目的地销售订单"

#: stock/models.py:1085
msgid "Expiry date for stock item. Stock will be considered expired after this date"
msgstr "库存物品的到期日。在此日期之后，库存将被视为过期"

#: stock/models.py:1103
msgid "Delete on deplete"
msgstr "耗尽时删除"

#: stock/models.py:1104
msgid "Delete this Stock Item when stock is depleted"
msgstr "当库存耗尽时删除此库存项"

#: stock/models.py:1125
msgid "Single unit purchase price at time of purchase"
msgstr "购买时一个单位的价格"

#: stock/models.py:1156
msgid "Converted to part"
msgstr "转换为零件"

#: stock/models.py:1721
msgid "Part is not set as trackable"
msgstr "零件未设置为可跟踪"

#: stock/models.py:1727
msgid "Quantity must be integer"
msgstr "数量必须是整数"

#: stock/models.py:1735
#, python-brace-format
msgid "Quantity must not exceed available stock quantity ({self.quantity})"
msgstr "数量不得超过现有库存量 ({self.quantity})"

#: stock/models.py:1741
msgid "Serial numbers must be provided as a list"
msgstr "必须以列表形式提供序列号"

#: stock/models.py:1746
msgid "Quantity does not match serial numbers"
msgstr "数量不匹配序列号"

#: stock/models.py:1868 stock/models.py:2777
msgid "Test template does not exist"
msgstr "测试模板不存在"

#: stock/models.py:1886
msgid "Stock item has been assigned to a sales order"
msgstr "库存项已分配到销售订单"

#: stock/models.py:1890
msgid "Stock item is installed in another item"
msgstr "库存项已安装在另一个项目中"

#: stock/models.py:1893
msgid "Stock item contains other items"
msgstr "库存项包含其他项目"

#: stock/models.py:1896
msgid "Stock item has been assigned to a customer"
msgstr "库存项已分配给客户"

#: stock/models.py:1899 stock/models.py:2073
msgid "Stock item is currently in production"
msgstr "库存项目前正在生产"

#: stock/models.py:1902
msgid "Serialized stock cannot be merged"
msgstr "序列化的库存不能合并"

#: stock/models.py:1909 stock/serializers.py:1545
msgid "Duplicate stock items"
msgstr "复制库存项"

#: stock/models.py:1913
msgid "Stock items must refer to the same part"
msgstr "库存项必须指相同零件"

#: stock/models.py:1921
msgid "Stock items must refer to the same supplier part"
msgstr "库存项必须是同一供应商的零件"

#: stock/models.py:1926
msgid "Stock status codes must match"
msgstr "库存状态码必须匹配"

#: stock/models.py:2196
msgid "StockItem cannot be moved as it is not in stock"
msgstr "库存项不能移动，因为它没有库存"

#: stock/models.py:2678
msgid "Stock Item Tracking"
msgstr "库存项跟踪"

#: stock/models.py:2709
msgid "Entry notes"
msgstr "条目注释"

#: stock/models.py:2749
msgid "Stock Item Test Result"
msgstr "库存项测试结果"

#: stock/models.py:2780
msgid "Value must be provided for this test"
msgstr "必须为此测试提供值"

#: stock/models.py:2784
msgid "Attachment must be uploaded for this test"
msgstr "测试附件必须上传"

#: stock/models.py:2789
msgid "Invalid value for this test"
msgstr "此测试的值无效"

#: stock/models.py:2813
msgid "Test result"
msgstr "测试结果"

#: stock/models.py:2820
msgid "Test output value"
msgstr "测试输出值"

#: stock/models.py:2828 stock/serializers.py:259
msgid "Test result attachment"
msgstr "测验结果附件"

#: stock/models.py:2832
msgid "Test notes"
msgstr "测试备注"

#: stock/models.py:2840
msgid "Test station"
msgstr "测试站"

#: stock/models.py:2841
msgid "The identifier of the test station where the test was performed"
msgstr "进行测试的测试站的标识符"

#: stock/models.py:2847
msgid "Started"
msgstr "已开始"

#: stock/models.py:2848
msgid "The timestamp of the test start"
msgstr "测试开始的时间戳"

#: stock/models.py:2854
msgid "Finished"
msgstr "已完成"

#: stock/models.py:2855
msgid "The timestamp of the test finish"
msgstr "测试结束的时间戳"

#: stock/serializers.py:84
msgid "Generated batch code"
msgstr "生成批量代码"

#: stock/serializers.py:93
msgid "Select build order"
msgstr "选择生产订单"

#: stock/serializers.py:102
msgid "Select stock item to generate batch code for"
msgstr "选择要生成批量代码的库存项"

#: stock/serializers.py:111
msgid "Select location to generate batch code for"
msgstr "选择要生成批量代码的位置"

#: stock/serializers.py:120
msgid "Select part to generate batch code for"
msgstr "选择要生成批量代码的零件"

#: stock/serializers.py:129
msgid "Select purchase order"
msgstr "选择采购订单"

#: stock/serializers.py:136
msgid "Enter quantity for batch code"
msgstr "输入批量代码的数量"

#: stock/serializers.py:162
msgid "Generated serial number"
msgstr "生成的序列号"

#: stock/serializers.py:172
msgid "Select part to generate serial number for"
msgstr "选择要生成序列号的零件"

#: stock/serializers.py:180
msgid "Quantity of serial numbers to generate"
msgstr "要生成的序列号的数量"

#: stock/serializers.py:248
msgid "Test template for this result"
msgstr "此结果的测试模板"

#: stock/serializers.py:272
msgid "Template ID or test name must be provided"
msgstr "必须提供模板 ID 或测试名称"

#: stock/serializers.py:304
msgid "The test finished time cannot be earlier than the test started time"
msgstr "测试完成时间不能早于测试开始时间"

#: stock/serializers.py:341
msgid "Serial number is too large"
msgstr "序列号太大"

#: stock/serializers.py:487
msgid "Parent Item"
msgstr "父项"

#: stock/serializers.py:488
msgid "Parent stock item"
msgstr "父库存项"

#: stock/serializers.py:508
msgid "Use pack size when adding: the quantity defined is the number of packs"
msgstr "添加时使用包装尺寸：定义的数量是包装的数量"

#: stock/serializers.py:618
msgid "Supplier Part Number"
msgstr "供应商零件编号"

#: stock/serializers.py:665 users/models.py:193
msgid "Expired"
msgstr "已过期"

#: stock/serializers.py:671
msgid "Child Items"
msgstr "子项目"

#: stock/serializers.py:675
msgid "Tracking Items"
msgstr "跟踪项目"

#: stock/serializers.py:681
msgid "Purchase price of this stock item, per unit or pack"
msgstr "此库存商品的购买价格，单位或包装"

#: stock/serializers.py:719
msgid "Enter number of stock items to serialize"
msgstr "输入要序列化的库存项目数量"

#: stock/serializers.py:732
#, python-brace-format
msgid "Quantity must not exceed available stock quantity ({q})"
msgstr "数量不得超过现有库存量 ({q})"

#: stock/serializers.py:739
msgid "Enter serial numbers for new items"
msgstr "输入新项目的序列号"

#: stock/serializers.py:750 stock/serializers.py:1502 stock/serializers.py:1790
msgid "Destination stock location"
msgstr "目标库存位置"

#: stock/serializers.py:757
msgid "Optional note field"
msgstr "可选注释字段"

#: stock/serializers.py:767
msgid "Serial numbers cannot be assigned to this part"
msgstr "此零件不能分配序列号"

#: stock/serializers.py:787
msgid "Serial numbers already exist"
msgstr "序列号已存在"

#: stock/serializers.py:826
msgid "Select stock item to install"
msgstr "选择要安装的库存项目"

#: stock/serializers.py:833
msgid "Quantity to Install"
msgstr "安装数量"

#: stock/serializers.py:834
msgid "Enter the quantity of items to install"
msgstr "输入要安装的项目数量"

#: stock/serializers.py:839 stock/serializers.py:919 stock/serializers.py:1049
#: stock/serializers.py:1101
msgid "Add transaction note (optional)"
msgstr "添加交易记录 (可选)"

#: stock/serializers.py:847
msgid "Quantity to install must be at least 1"
msgstr "安装数量必须至少为1"

#: stock/serializers.py:855
msgid "Stock item is unavailable"
msgstr "库存项不可用"

#: stock/serializers.py:866
msgid "Selected part is not in the Bill of Materials"
msgstr "所选零件不在物料清单中"

#: stock/serializers.py:879
msgid "Quantity to install must not exceed available quantity"
msgstr "安装数量不得超过可用数量"

#: stock/serializers.py:914
msgid "Destination location for uninstalled item"
msgstr "已卸载项目的目标位置"

#: stock/serializers.py:949
msgid "Select part to convert stock item into"
msgstr "选择要将库存项目转换为的零件"

#: stock/serializers.py:962
msgid "Selected part is not a valid option for conversion"
msgstr "所选零件不是有效的转换选项"

#: stock/serializers.py:979
msgid "Cannot convert stock item with assigned SupplierPart"
msgstr "无法转换已分配供应商零件的库存项"

#: stock/serializers.py:1013
msgid "Stock item status code"
msgstr "库存项状态代码"

#: stock/serializers.py:1042
msgid "Destination location for returned item"
msgstr "退回物品的目的地位置"

#: stock/serializers.py:1085
msgid "Select stock items to change status"
msgstr "选择要更改状态的库存项目"

#: stock/serializers.py:1091
msgid "No stock items selected"
msgstr "未选择库存商品"

#: stock/serializers.py:1185 stock/serializers.py:1262
msgid "Sublocations"
msgstr "子位置"

#: stock/serializers.py:1257
msgid "Parent stock location"
msgstr "上级库存地点"

#: stock/serializers.py:1374
msgid "Part must be salable"
msgstr "零件必须可销售"

#: stock/serializers.py:1378
msgid "Item is allocated to a sales order"
msgstr "物料已分配到销售订单"

#: stock/serializers.py:1382
msgid "Item is allocated to a build order"
msgstr "项目被分配到生产订单中"

#: stock/serializers.py:1406
msgid "Customer to assign stock items"
msgstr "客户分配库存项目"

#: stock/serializers.py:1412
msgid "Selected company is not a customer"
msgstr "所选公司不是客户"

#: stock/serializers.py:1420
msgid "Stock assignment notes"
msgstr "库存分配说明"

#: stock/serializers.py:1430 stock/serializers.py:1695
msgid "A list of stock items must be provided"
msgstr "必须提供库存物品清单"

#: stock/serializers.py:1509
msgid "Stock merging notes"
msgstr "库存合并说明"

#: stock/serializers.py:1514
msgid "Allow mismatched suppliers"
msgstr "允许不匹配的供应商"

#: stock/serializers.py:1515
msgid "Allow stock items with different supplier parts to be merged"
msgstr "允许合并具有不同供应商零件的库存项目"

#: stock/serializers.py:1520
msgid "Allow mismatched status"
msgstr "允许不匹配的状态"

#: stock/serializers.py:1521
msgid "Allow stock items with different status codes to be merged"
msgstr "允许合并具有不同状态代码的库存项目"

#: stock/serializers.py:1531
msgid "At least two stock items must be provided"
msgstr "必须提供至少两件库存物品"

#: stock/serializers.py:1598
msgid "No Change"
msgstr "无更改"

#: stock/serializers.py:1627
msgid "StockItem primary key value"
msgstr "库存项主键值"

#: stock/serializers.py:1639
msgid "Stock item is not in stock"
msgstr ""

#: stock/serializers.py:1685
msgid "Stock transaction notes"
msgstr "库存交易记录"

#: stock/serializers.py:1831
msgid "Next Serial Number"
msgstr ""

#: stock/serializers.py:1837
msgid "Previous Serial Number"
msgstr ""

#: stock/status_codes.py:11
msgid "OK"
msgstr "OK"

#: stock/status_codes.py:12
msgid "Attention needed"
msgstr "需要关注"

#: stock/status_codes.py:13
msgid "Damaged"
msgstr "破损"

#: stock/status_codes.py:14
msgid "Destroyed"
msgstr "销毁"

#: stock/status_codes.py:15
msgid "Rejected"
msgstr "拒绝"

#: stock/status_codes.py:19
msgid "Quarantined"
msgstr "隔离"

#: stock/status_codes.py:44
msgid "Legacy stock tracking entry"
msgstr "旧库存跟踪条目"

#: stock/status_codes.py:46
msgid "Stock item created"
msgstr "库存项已创建"

#: stock/status_codes.py:49
msgid "Edited stock item"
msgstr "已编辑库存项"

#: stock/status_codes.py:50
msgid "Assigned serial number"
msgstr "已分配序列号"

#: stock/status_codes.py:53
msgid "Stock counted"
msgstr "库存计数"

#: stock/status_codes.py:54
msgid "Stock manually added"
msgstr "已手动添加库存"

#: stock/status_codes.py:55
msgid "Stock manually removed"
msgstr "已手动删除库存"

#: stock/status_codes.py:58
msgid "Location changed"
msgstr "地点已更改"

#: stock/status_codes.py:59
msgid "Stock updated"
msgstr "库存已更新"

#: stock/status_codes.py:62
msgid "Installed into assembly"
msgstr "已安装到装配中"

#: stock/status_codes.py:63
msgid "Removed from assembly"
msgstr "已从装配中删除"

#: stock/status_codes.py:65
msgid "Installed component item"
msgstr "已安装组件项"

#: stock/status_codes.py:66
msgid "Removed component item"
msgstr "已删除组件项"

#: stock/status_codes.py:69
msgid "Split from parent item"
msgstr "从上级项拆分"

#: stock/status_codes.py:70
msgid "Split child item"
msgstr "拆分子项"

#: stock/status_codes.py:73
msgid "Merged stock items"
msgstr "合并的库存项"

#: stock/status_codes.py:76
msgid "Converted to variant"
msgstr "转换为变体"

#: stock/status_codes.py:79
msgid "Build order output created"
msgstr "已创建生产订单产出"

#: stock/status_codes.py:80
msgid "Build order output completed"
msgstr "生产订单已出产"

#: stock/status_codes.py:81
msgid "Build order output rejected"
msgstr "生产订单产出被拒绝"

#: stock/status_codes.py:82
msgid "Consumed by build order"
msgstr "被工單消耗的"

#: stock/status_codes.py:85
msgid "Shipped against Sales Order"
msgstr "按銷售訂單出貨"

#: stock/status_codes.py:88
msgid "Received against Purchase Order"
msgstr "按採購訂單接收"

#: stock/status_codes.py:91
msgid "Returned against Return Order"
msgstr "按退貨訂單退回"

#: stock/status_codes.py:94
msgid "Sent to customer"
msgstr "寄送給客戶"

#: stock/status_codes.py:95
msgid "Returned from customer"
msgstr "從客戶端退回"

#: templates/403.html:6 templates/403.html:10 templates/403_csrf.html:7
msgid "Permission Denied"
msgstr "权限受限"

#: templates/403.html:11
msgid "You do not have permission to view this page."
msgstr "您没有查看此页面的权限。"

#: templates/403_csrf.html:11
msgid "Authentication Failure"
msgstr "认证失败"

#: templates/403_csrf.html:12
msgid "You have been logged out from InvenTree."
msgstr "您已从InvenTree注销。"

#: templates/404.html:6 templates/404.html:10
msgid "Page Not Found"
msgstr "找不到页面"

#: templates/404.html:11
msgid "The requested page does not exist"
msgstr "请求的页面不存在"

#: templates/500.html:6 templates/500.html:10
msgid "Internal Server Error"
msgstr "服务器内部错误"

#: templates/500.html:11
#, python-format
msgid "The %(inventree_title)s server raised an internal error"
msgstr "%(inventree_title)s 服务器引起一个内部错误"

#: templates/500.html:12
msgid "Refer to the error log in the admin interface for further details"
msgstr "有关更多详细信息，请参阅管理界面中的错误日志"

#: templates/503.html:11 templates/503.html:15
msgid "Site is in Maintenance"
msgstr "网站正在维护中"

#: templates/503.html:17
msgid "The site is currently in maintenance and should be up again soon!"
msgstr "该网站目前正在维护中，应该很快就会重新上线！"

#: templates/base.html:51
msgid "Server Restart Required"
msgstr "需要重新启动服务器"

#: templates/base.html:54
msgid "A configuration option has been changed which requires a server restart"
msgstr "配置选项已更改，需要重新启动服务器"

#: templates/base.html:54 templates/base.html:64
msgid "Contact your system administrator for further information"
msgstr "有关详细信息，请与系统管理员联系"

#: templates/base.html:61
msgid "Pending Database Migrations"
msgstr "待处理的数据库迁移"

#: templates/base.html:64
msgid "There are pending database migrations which require attention"
msgstr "有一些待处理的数据库迁移需要注意"

#: templates/email/build_order_completed.html:9
#: templates/email/canceled_order_assigned.html:9
#: templates/email/new_order_assigned.html:9
#: templates/email/overdue_build_order.html:9
#: templates/email/overdue_purchase_order.html:9
#: templates/email/overdue_return_order.html:9
#: templates/email/overdue_sales_order.html:9
#: templates/email/purchase_order_received.html:9
#: templates/email/return_order_received.html:9
msgid "Click on the following link to view this order"
msgstr "点击以下链接查看此订单"

#: templates/email/build_order_required_stock.html:7
msgid "Stock is required for the following build order"
msgstr "以下生产订单需要库存"

#: templates/email/build_order_required_stock.html:8
#, python-format
msgid "Build order %(build)s - building %(quantity)s x %(part)s"
msgstr "生产订单 %(build)s - 生产… %(quantity)s x %(part)s"

#: templates/email/build_order_required_stock.html:10
msgid "Click on the following link to view this build order"
msgstr "点击以下链接查看此生产订单"

#: templates/email/build_order_required_stock.html:14
msgid "The following parts are low on required stock"
msgstr "以下零件所需库存不足"

#: templates/email/build_order_required_stock.html:18
msgid "Required Quantity"
msgstr "所需数量"

#: templates/email/build_order_required_stock.html:38
#: templates/email/low_stock_notification.html:30
msgid "You are receiving this email because you are subscribed to notifications for this part "
msgstr "您收到此邮件是因为您订阅了此零件的通知 "

#: templates/email/low_stock_notification.html:9
#: templates/email/part_event_notification.html:9
msgid "Click on the following link to view this part"
msgstr "点击以下链接查看此零件"

#: templates/email/low_stock_notification.html:18
#: templates/email/part_event_notification.html:19
msgid "Minimum Quantity"
msgstr "最小数量"

#: templates/email/part_event_notification.html:32
msgid "You are receiving this email because you are subscribed to notifications for this part or a category that it is part of "
msgstr ""

#: users/admin.py:101
msgid "Users"
msgstr "用户"

#: users/admin.py:102
msgid "Select which users are assigned to this group"
msgstr "选择分配给此组的用户"

#: users/admin.py:137
msgid "Personal info"
msgstr "个人信息"

#: users/admin.py:139
msgid "Permissions"
msgstr "权限"

#: users/admin.py:142
msgid "Important dates"
msgstr "重要日期"

#: users/authentication.py:30 users/models.py:157
msgid "Token has been revoked"
msgstr "令牌已被撤销"

#: users/authentication.py:33
msgid "Token has expired"
msgstr "令牌已过期"

#: users/models.py:100
msgid "API Token"
msgstr "API 令牌"

#: users/models.py:101
msgid "API Tokens"
msgstr "API 令牌"

#: users/models.py:137
msgid "Token Name"
msgstr "令牌名称"

#: users/models.py:138
msgid "Custom token name"
msgstr "自定义令牌名称"

#: users/models.py:144
msgid "Token expiry date"
msgstr "令牌过期日期"

#: users/models.py:152
msgid "Last Seen"
msgstr "最近一次在线"

#: users/models.py:153
msgid "Last time the token was used"
msgstr "最近使用令牌的时间"

#: users/models.py:157
msgid "Revoked"
msgstr "撤销"

#: users/models.py:235
msgid "Permission set"
msgstr "权限设置"

#: users/models.py:244
msgid "Group"
msgstr "组"

#: users/models.py:248
msgid "View"
msgstr "查看"

#: users/models.py:248
msgid "Permission to view items"
msgstr "查看项目的权限"

#: users/models.py:252
msgid "Add"
msgstr "添加"

#: users/models.py:252
msgid "Permission to add items"
msgstr "添加项目的权限"

#: users/models.py:256
msgid "Change"
msgstr "更改"

#: users/models.py:258
msgid "Permissions to edit items"
msgstr "编辑项目的权限"

#: users/models.py:262
msgid "Delete"
msgstr "删除"

#: users/models.py:264
msgid "Permission to delete items"
msgstr "删除项目的权限"

#: users/models.py:497
msgid "Bot"
msgstr ""

#: users/models.py:498
msgid "Internal"
msgstr ""

#: users/models.py:500
msgid "Guest"
msgstr ""

#: users/models.py:509
msgid "Language"
msgstr ""

#: users/models.py:510
msgid "Preferred language for the user"
msgstr ""

#: users/models.py:515
msgid "Theme"
msgstr ""

#: users/models.py:516
msgid "Settings for the web UI as JSON - do not edit manually!"
msgstr ""

#: users/models.py:521
msgid "Widgets"
msgstr ""

#: users/models.py:523
msgid "Settings for the dashboard widgets as JSON - do not edit manually!"
msgstr ""

#: users/models.py:530
msgid "Display Name"
msgstr ""

#: users/models.py:531
msgid "Chosen display name for the user"
msgstr ""

#: users/models.py:537
msgid "Position"
msgstr ""

#: users/models.py:538
msgid "Main job title or position"
msgstr ""

#: users/models.py:545
msgid "User status message"
msgstr ""

#: users/models.py:552
msgid "User location information"
msgstr ""

#: users/models.py:557
msgid "User is actively using the system"
msgstr ""

#: users/models.py:564
msgid "Preferred contact information for the user"
msgstr ""

#: users/models.py:570
msgid "User Type"
msgstr ""

#: users/models.py:571
msgid "Which type of user is this?"
msgstr ""

#: users/models.py:577
msgid "Organisation"
msgstr ""

#: users/models.py:578
msgid "Users primary organisation/affiliation"
msgstr ""

#: users/models.py:586
msgid "Primary Group"
msgstr ""

#: users/models.py:587
msgid "Primary group for the user"
msgstr ""

#: users/ruleset.py:31
msgid "Admin"
msgstr "管理员"

#: users/ruleset.py:34
msgid "Stocktake"
msgstr "库存盘点"

#: users/ruleset.py:38
msgid "Purchase Orders"
msgstr "采购订单"

#: users/ruleset.py:39
msgid "Sales Orders"
msgstr "销售订单"

#: users/ruleset.py:40
msgid "Return Orders"
msgstr "退货订单"

#: users/serializers.py:236
msgid "Username"
msgstr "用户名"

#: users/serializers.py:239
msgid "First Name"
msgstr "名"

#: users/serializers.py:239
msgid "First name of the user"
msgstr "用户的名字（不包括姓氏）"

#: users/serializers.py:243
msgid "Last Name"
msgstr "姓"

#: users/serializers.py:243
msgid "Last name of the user"
msgstr "用户的姓氏"

#: users/serializers.py:247
msgid "Email address of the user"
msgstr "用户的电子邮件地址"

#: users/serializers.py:323
msgid "Staff"
msgstr "职员"

#: users/serializers.py:324
msgid "Does this user have staff permissions"
msgstr "此用户是否拥有员工权限"

#: users/serializers.py:329
msgid "Superuser"
msgstr "超级用户"

#: users/serializers.py:329
msgid "Is this user a superuser"
msgstr "此用户是否为超级用户"

#: users/serializers.py:333
msgid "Is this user account active"
msgstr "此用户帐户是否已激活"

#: users/serializers.py:345
msgid "Only a superuser can adjust this field"
msgstr ""

#: users/serializers.py:399
msgid "Only staff users can create new users"
msgstr ""

#: users/serializers.py:404
msgid "You do not have permission to create users"
msgstr ""

#: users/serializers.py:425
msgid "Your account has been created."
msgstr "您的帳號已經建立完成。"

#: users/serializers.py:427
msgid "Please use the password reset function to login"
msgstr "請使用重設密碼功能來登入"

#: users/serializers.py:433
msgid "Welcome to InvenTree"
msgstr "欢迎使用 InventTree"

