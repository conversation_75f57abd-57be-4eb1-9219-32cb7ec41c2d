# Generated by Django 4.2.14 on 2024-08-07 22:40

import django.core.validators
from django.db import migrations

import generic.states.fields
import generic.states.validators
import InvenTree.status_codes


class Migration(migrations.Migration):

    dependencies = [
        ("build", "0051_delete_buildorderattachment"),
    ]

    operations = [
        migrations.AddField(
            model_name="build",
            name="status_custom_key",
            field=generic.states.fields.ExtraInvenTreeCustomStatusModelField(
                blank=True,
                default=None,
                help_text="Additional status information for this item",
                null=True,
                verbose_name="Custom status key",
                validators=[
                    generic.states.validators.CustomStatusCodeValidator(
                        status_class=InvenTree.status_codes.BuildStatus
                    ),
                ]
            ),
        ),
        migrations.AlterField(
            model_name="build",
            name="status",
            field=generic.states.fields.InvenTreeCustomStatusModelField(
                choices=InvenTree.status_codes.BuildStatus.items(),
                default=10,
                help_text="Build status code",
                validators=[
                    django.core.validators.MinValueValidator(0),
                    generic.states.validators.CustomStatusCodeValidator(
                        status_class=InvenTree.status_codes.BuildStatus
                    ),
                ],
                verbose_name="Build Status",
            ),
        ),
    ]
