# Generated by Django 3.0.7 on 2020-10-03 13:44

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('auth', '0011_update_proxy_permissions'),
    ]

    operations = [
        migrations.CreateModel(
            name='RuleSet',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(choices=[('general', 'General'), ('admin', 'Admin'), ('part', 'Parts'), ('stock', 'Stock'), ('build', 'Build Orders'), ('supplier', 'Suppliers'), ('purchase_order', 'Purchase Orders'), ('customer', 'Customers'), ('sales_order', 'Sales Orders')], help_text='Permission set', max_length=50)),
                ('can_view', models.BooleanField(default=True, help_text='Permission to view items', verbose_name='View')),
                ('can_add', models.BooleanField(default=False, help_text='Permission to add items', verbose_name='Create')),
                ('can_change', models.BooleanField(default=False, help_text='Permissions to edit items', verbose_name='Update')),
                ('can_delete', models.BooleanField(default=False, help_text='Permission to delete items', verbose_name='Delete')),
                ('group', models.ForeignKey(help_text='Group', on_delete=django.db.models.deletion.CASCADE, related_name='rule_sets', to='auth.Group')),
            ],
            options={
                'unique_together': {('name', 'group')},
            },
        ),
    ]
