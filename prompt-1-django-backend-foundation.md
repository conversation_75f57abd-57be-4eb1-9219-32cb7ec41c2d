# Prompt 1: Django Backend Foundation & E-commerce API Development

**Persona:** You are a Full-Stack Developer with expertise in Django, Django Rest Framework, and React integration.

**Objective:** Build a complete Django backend infrastructure for DeepForest Hardware Emporium that will serve as the foundation for InvenTree integration. Create a robust REST API and refactor the React frontend to be fully dynamic.

**Context:** The frontend is now polished and functional. This phase builds the Django backend engine that will later integrate with InvenTree. The project is at `d:\PRJs\WORK ACTION\DeepForest\deepforest-hardware-emporium` and must use Django + DRF to match InvenTree's technology stack.

## Key Tasks & Requirements:

### 1. Django Project Setup:
- **New Django Project:** Create a new Django project in a `backend/` directory
- **Database:** Configure PostgreSQL as the database
- **API Framework:** Set up Django Rest Framework (DRF)
- **CORS:** Configure `django-cors-headers` for React frontend communication
- **Environment:** Set up proper environment variable management

### 2. Database Models (`backend/models.py`):
- **Category Model:** name, description, image_url, slug, created_at, updated_at
- **Product Model:** name, description, price_ksh, sku, category (FK), image_url, is_active, on_sale, sale_price_ksh, stock_quantity (temporary field), created_at, updated_at
- **User Profile:** Extend Django User with Kenyan-specific fields (phone, county, delivery_address)
- **Cart & CartItem:** User-linked shopping cart functionality
- **Order & OrderItem:** Order history and tracking
- **Data Migration:** Script to populate initial product/category data from frontend

### 3. DRF API Endpoints:
- **Products API:** `/api/products/` with filtering (category, search, price range), pagination
- **Categories API:** `/api/categories/` for category listing
- **Authentication:** JWT-based auth with `/api/auth/` endpoints (register, login, logout)
- **Cart API:** Protected `/api/cart/` endpoints for authenticated users
- **Orders API:** Protected `/api/orders/` for order creation and history
- **User API:** `/api/user/profile/` for user profile management

### 4. Kenyan Market Specific Features:
- **Currency Handling:** All prices stored and returned in KSH
- **Delivery Zones:** Model for Kenyan counties and delivery costs
- **Payment Integration Prep:** Models for payment tracking (ready for Pesapal API integration in Prompt 2)

### 5. Frontend Integration:
- **Remove Static Data:** Replace all hardcoded arrays in React components
- **TanStack Query:** Implement React Query for all API calls with proper loading/error states
- **Authentication Flow:** Build login/register components with JWT handling
- **Dynamic Cart:** Connect cart UI to backend API
- **Order Processing:** Wire checkout flow to create real orders

### 6. Admin Interface:
- **Django Admin:** Customize admin interface for product/category management
- **Permissions:** Set up proper user roles and permissions
- **Data Management:** Tools for bulk product import/export

## Acceptance Criteria:
- Fully functional Django backend with PostgreSQL
- Complete REST API with proper authentication
- React frontend is 100% dynamic (no hardcoded data)
- User registration, login, and profile management works
- Cart persists in database for logged-in users
- Orders are created and stored in database
- Admin interface allows product/category management
- All prices and weights use Kenyan standards (KSH, KG)
- Payment models are prepared for Pesapal API integration
