# DeepForest Hardware Emporium

A comprehensive e-commerce platform for hardware and building materials, featuring integrated inventory management and Kenyan payment systems.

## Project Overview

DeepForest Hardware Emporium is a full-stack e-commerce solution built for the Kenyan market, featuring:

- **Frontend**: React + TypeScript with shadcn/ui components
- **Backend**: Django + Django Rest Framework with PostgreSQL
- **Payment Integration**: Pesapal API for M-Pesa, Visa, Mastercard
- **Inventory Management**: Preparation for InvenTree integration
- **Kenyan Market Features**: KSH currency, county-based delivery, local payment methods

## Project Status: Phase 2.2 Complete ✅

**Current Implementation**: Payment Integration & Order Processing with Pesapal API

### ✅ Completed Features:
- Complete Pesapal API integration with OAuth 1.0 authentication
- Support for M-Pesa, Visa, Mastercard, and other Kenyan payment methods
- Real-time payment status tracking with IPN callbacks
- Order management system with delivery tracking
- Comprehensive payment logging and audit trail
- Kenyan market features (KSH currency, county delivery zones)
- Mobile-optimized payment flow

**Lovable Project URL**: https://lovable.dev/projects/f2261de5-36a7-4170-b761-8c7ac3e08bfb

## How can I edit this code?

There are several ways of editing your application.

**Use Lovable**

Simply visit the [Lovable Project](https://lovable.dev/projects/f2261de5-36a7-4170-b761-8c7ac3e08bfb) and start prompting.

Changes made via Lovable will be committed automatically to this repo.

**Use your preferred IDE**

If you want to work locally using your own IDE, you can clone this repo and push changes. Pushed changes will also be reflected in Lovable.

The only requirement is having Node.js & npm installed - [install with nvm](https://github.com/nvm-sh/nvm#installing-and-updating)

Follow these steps:

```sh
# Step 1: Clone the repository using the project's Git URL.
git clone <YOUR_GIT_URL>

# Step 2: Navigate to the project directory.
cd <YOUR_PROJECT_NAME>

# Step 3: Install the necessary dependencies.
npm i

# Step 4: Start the development server with auto-reloading and an instant preview.
npm run dev
```

**Edit a file directly in GitHub**

- Navigate to the desired file(s).
- Click the "Edit" button (pencil icon) at the top right of the file view.
- Make your changes and commit the changes.

**Use GitHub Codespaces**

- Navigate to the main page of your repository.
- Click on the "Code" button (green button) near the top right.
- Select the "Codespaces" tab.
- Click on "New codespace" to launch a new Codespace environment.
- Edit files directly within the Codespace and commit and push your changes once you're done.

## Technology Stack

### Frontend
- **React 18** with TypeScript
- **Vite** for fast development and building
- **shadcn/ui** component library
- **Tailwind CSS** for styling
- **TanStack Query** for API state management
- **React Router** for navigation

### Backend
- **Django 5.2** with Django Rest Framework
- **PostgreSQL** database
- **JWT Authentication** with SimpleJWT
- **CORS** support for frontend integration
- **Pesapal API** integration for payments

### Payment Integration
- **Pesapal API** with OAuth 1.0 authentication
- **M-Pesa** payment support
- **Credit/Debit Cards** (Visa, Mastercard)
- **Real-time payment tracking** with IPN callbacks
- **Kenyan market optimization** (KSH currency, local payment methods)

## Quick Start

### Frontend Setup
```bash
# Install dependencies
npm install

# Start development server
npm run dev
```

### Backend Setup
```bash
# Navigate to backend directory
cd backend

# Activate virtual environment
.\venv\Scripts\Activate.ps1  # Windows
# or
source venv/bin/activate     # Linux/Mac

# Install dependencies
pip install -r requirements.txt

# Set up environment variables
cp .env.example .env
# Edit .env with your configuration

# Run migrations
python manage.py migrate

# Create superuser
python manage.py createsuperuser

# Start development server
python manage.py runserver
```

### Pesapal Configuration
1. Register for a Pesapal merchant account at [demo.pesapal.com](http://demo.pesapal.com) (sandbox)
2. Get your Consumer Key and Consumer Secret
3. Update `.env` file:
   ```
   PESAPAL_CONSUMER_KEY=your_consumer_key
   PESAPAL_CONSUMER_SECRET=your_consumer_secret
   ```
4. Test the integration:
   ```bash
   python test_pesapal.py
   ```

## Payment Integration Features

### Supported Payment Methods
- **M-Pesa**: Kenya's leading mobile money service
- **Visa/Mastercard**: Credit and debit cards
- **Airtel Money**: Alternative mobile money
- **Equitel**: Equity Bank mobile service
- **Bank Transfer**: Direct bank payments

### Payment Flow
1. **Checkout**: User fills delivery information and selects payment method
2. **Order Creation**: System creates order from cart items
3. **Payment Initiation**: Generates Pesapal payment URL
4. **Payment Processing**: User redirected to Pesapal gateway
5. **Callback Handling**: Real-time status updates via IPN
6. **Order Completion**: Automatic order status updates

### API Endpoints
- `POST /api/payments/initiate/` - Start payment process
- `GET /api/payments/status/<id>/` - Check payment status
- `GET /api/payments/orders/` - List user orders
- `POST /api/payments/callback/` - Handle payment callbacks

## How can I deploy this project?

Simply open [Lovable](https://lovable.dev/projects/f2261de5-36a7-4170-b761-8c7ac3e08bfb) and click on Share -> Publish.

## Can I connect a custom domain to my Lovable project?

Yes, you can!

To connect a domain, navigate to Project > Settings > Domains and click Connect Domain.

Read more here: [Setting up a custom domain](https://docs.lovable.dev/tips-tricks/custom-domain#step-by-step-guide)
