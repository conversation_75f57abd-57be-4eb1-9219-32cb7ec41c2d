"""
Pesapal API Client
Based on the existing Pesapal API files in src/PesapalAPI-Files/
Implements OAuth 1.0 authentication and payment processing
"""
import requests
from typing import Dict, Any
import logging
from requests_oauthlib import OAuth1

from .pesapal_config import PesapalConfig
from .pesapal_utils import build_pesapal_xml, parse_pesapal_response
from .pesapal_exceptions import (
    PesapalException, PesapalConfigurationError,
    PesapalAPIError, PesapalNetworkError
)

logger = logging.getLogger(__name__)


class PesapalClient:
    """
    Main Pesapal API client class
    Handles OAuth authentication and payment processing
    """

    def __init__(self):
        """Initialize Pesapal client with configuration"""
        try:
            PesapalConfig.validate_config()
            self.consumer_key = PesapalConfig.CONSUMER_KEY
            self.consumer_secret = PesapalConfig.CONSUMER_SECRET
            self.base_url = PesapalConfig.get_base_url()
            self.callback_url = PesapalConfig.CALLBACK_URL

            # Initialize OAuth1 session
            self.oauth = OAuth1(
                client_key=self.consumer_key,
                client_secret=self.consumer_secret,
                signature_method='HMAC-SHA1',
                signature_type='AUTH_HEADER'
            )
        except ValueError as e:
            raise PesapalConfigurationError(str(e))

    def initiate_payment(self, order_data: Dict[str, Any]) -> str:
        """
        Initiate payment with Pesapal
        Returns the iframe URL for payment processing
        """
        try:
            # Build XML data
            xml_data = build_pesapal_xml(order_data)

            # Prepare parameters for the request
            params = {
                'oauth_callback': self.callback_url,
                'pesapal_request_data': xml_data
            }

            # Get URL for the request
            url = PesapalConfig.get_post_order_url()

            # Create OAuth1 session with callback
            oauth_with_callback = OAuth1(
                client_key=self.consumer_key,
                client_secret=self.consumer_secret,
                callback_uri=self.callback_url,
                signature_method='HMAC-SHA1',
                signature_type='QUERY'
            )

            # Prepare the request with OAuth
            session = requests.Session()
            session.auth = oauth_with_callback

            # Prepare the request
            prepared_request = session.prepare_request(
                requests.Request('GET', url, params=params)
            )

            # The iframe URL is the prepared request URL
            iframe_url = prepared_request.url

            logger.info(f"Payment initiated for reference: {order_data.get('reference')}")
            return iframe_url

        except Exception as e:
            logger.error(f"Payment initiation failed: {str(e)}")
            if isinstance(e, PesapalException):
                raise
            raise PesapalAPIError(f"Failed to initiate payment: {str(e)}")
    
    def query_payment_status(self, merchant_reference: str, transaction_tracking_id: str) -> Dict[str, str]:
        """
        Query payment status from Pesapal
        Based on pesapal-ipn-listener.php implementation
        """
        try:
            # Prepare parameters for the request
            params = {
                'pesapal_merchant_reference': merchant_reference,
                'pesapal_transaction_tracking_id': transaction_tracking_id
            }

            # Get URL for the request
            url = PesapalConfig.get_query_status_url()

            # Make API request using OAuth1 session
            response = requests.get(url, params=params, auth=self.oauth, timeout=30)
            response.raise_for_status()

            # Parse response
            result = parse_pesapal_response(response.text)

            logger.info(f"Payment status queried for reference: {merchant_reference}")
            return result

        except requests.RequestException as e:
            logger.error(f"Network error querying payment status: {str(e)}")
            raise PesapalNetworkError(f"Failed to query payment status: {str(e)}")
        except Exception as e:
            logger.error(f"Error querying payment status: {str(e)}")
            raise PesapalAPIError(f"Failed to query payment status: {str(e)}")
    
    def verify_callback(self, callback_data: Dict[str, str]) -> bool:
        """
        Verify callback data from Pesapal
        Returns True if callback is valid
        """
        try:
            required_fields = ['pesapal_notification_type', 'pesapal_transaction_tracking_id', 'pesapal_merchant_reference']
            
            for field in required_fields:
                if field not in callback_data:
                    logger.warning(f"Missing required callback field: {field}")
                    return False
            
            # Additional validation can be added here
            return True
            
        except Exception as e:
            logger.error(f"Callback verification failed: {str(e)}")
            return False
