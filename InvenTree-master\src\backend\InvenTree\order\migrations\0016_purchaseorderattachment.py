# Generated by Django 2.2.9 on 2020-03-22 07:01

import InvenTree.models
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('order', '0015_auto_20200201_2346'),
    ]

    operations = [
        migrations.CreateModel(
            name='PurchaseOrderAttachment',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('attachment', models.FileField(help_text='Select file to attach', upload_to='attachments')),
                ('comment', models.CharField(help_text='File comment', max_length=100)),
                ('order', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='attachments', to='order.PurchaseOrder')),
            ],
            options={
                'abstract': False,
            },
        ),
    ]
