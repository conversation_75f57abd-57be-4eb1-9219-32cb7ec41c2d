"""
URL configuration for payments app
"""
from django.urls import path
from . import views

app_name = 'payments'

urlpatterns = [
    # Payment initiation
    path('initiate/', views.PaymentInitiationView.as_view(), name='initiate_payment'),
    
    # Payment callbacks
    path('callback/', views.PaymentCallbackView.as_view(), name='payment_callback'),
    path('ipn/', views.pesapal_ipn, name='pesapal_ipn'),
    
    # Payment status
    path('status/<str:transaction_id>/', views.payment_status, name='payment_status'),
    
    # Checkout
    path('checkout/summary/', views.checkout_summary, name='checkout_summary'),
    
    # Orders
    path('orders/', views.user_orders, name='user_orders'),
    path('orders/<int:order_id>/', views.order_detail, name='order_detail'),
]
