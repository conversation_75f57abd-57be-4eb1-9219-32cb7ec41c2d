msgid ""
msgstr ""
"Project-Id-Version: inventree\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-09 19:12+0000\n"
"PO-Revision-Date: 2025-05-09 19:14\n"
"Last-Translator: \n"
"Language-Team: Dutch\n"
"Language: nl_NL\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Crowdin-Project: inventree\n"
"X-Crowdin-Project-ID: 452300\n"
"X-Crowdin-Language: nl\n"
"X-Crowdin-File: /src/backend/InvenTree/locale/en/LC_MESSAGES/django.po\n"
"X-Crowdin-File-ID: 250\n"

#: InvenTree/AllUserRequire2FAMiddleware.py:42
msgid "You must enable two-factor authentication before doing anything else."
msgstr "Schakel tweestapsverificatie in voordat je iets anders kunt doen."

#: InvenTree/api.py:355
msgid "API endpoint not found"
msgstr "API eindpunt niet gevonden"

#: InvenTree/api.py:432
msgid "List of items or filters must be provided for bulk operation"
msgstr "Lijst met items of filters moet worden opgegeven voor bulk bewerking"

#: InvenTree/api.py:439
msgid "Items must be provided as a list"
msgstr "Items moeten worden opgegeven als een lijst"

#: InvenTree/api.py:447
msgid "Invalid items list provided"
msgstr "Ongeldige items lijst verstrekt"

#: InvenTree/api.py:453
msgid "Filters must be provided as a dict"
msgstr "Filters moeten als woordenboek worden opgegeven"

#: InvenTree/api.py:460
msgid "Invalid filters provided"
msgstr "Ongeldige filters opgegeven"

#: InvenTree/api.py:465
msgid "All filter must only be used with true"
msgstr "Alles filteren alleen gebruiken met True"

#: InvenTree/api.py:470
msgid "No items match the provided criteria"
msgstr "Geen items die overeenkomen met de opgegeven criteria"

#: InvenTree/api.py:713
msgid "User does not have permission to view this model"
msgstr "Gebruiker heeft geen rechten om dit model te bekijken"

#: InvenTree/auth_overrides.py:57
msgid "Email (again)"
msgstr "E-mailadres (opnieuw)"

#: InvenTree/auth_overrides.py:61
msgid "Email address confirmation"
msgstr "E-mailadres bevestiging"

#: InvenTree/auth_overrides.py:84
msgid "You must type the same email each time."
msgstr "Er moet hetzelfde e-mailadres ingevoerd worden."

#: InvenTree/auth_overrides.py:126 InvenTree/auth_overrides.py:134
msgid "The provided primary email address is not valid."
msgstr "Het opgegeven primaire e-mailadres is ongeldig."

#: InvenTree/auth_overrides.py:140
msgid "The provided email domain is not approved."
msgstr "Het ingevoerde e-maildomein is niet goedgekeurd."

#: InvenTree/conversion.py:162
#, python-brace-format
msgid "Invalid unit provided ({unit})"
msgstr "Ongeldige eenheid opgegeven ({unit})"

#: InvenTree/conversion.py:179
msgid "No value provided"
msgstr "Geen waarde opgegeven"

#: InvenTree/conversion.py:206
#, python-brace-format
msgid "Could not convert {original} to {unit}"
msgstr "{original} kon niet worden omgezet naar {unit}"

#: InvenTree/conversion.py:208 InvenTree/conversion.py:222
#: InvenTree/helpers.py:548 order/models.py:707 order/models.py:975
msgid "Invalid quantity provided"
msgstr "Ongeldige hoeveelheid ingevoerd"

#: InvenTree/exceptions.py:109
msgid "Error details can be found in the admin panel"
msgstr "Error details kunnen worden gevonden in het admin scherm"

#: InvenTree/fields.py:138
msgid "Enter date"
msgstr "Voer datum in"

#: InvenTree/fields.py:161
msgid "Invalid decimal value"
msgstr "Ongeldige decimale waarde"

#: InvenTree/fields.py:210 InvenTree/models.py:887 build/serializers.py:526
#: build/serializers.py:597 company/models.py:834 order/models.py:1579
#: part/models.py:3366
#: report/templates/report/inventree_build_order_report.html:172
#: stock/models.py:2708 stock/models.py:2832 stock/serializers.py:756
#: stock/serializers.py:918 stock/serializers.py:1048 stock/serializers.py:1100
#: stock/serializers.py:1419 stock/serializers.py:1508
#: stock/serializers.py:1684
msgid "Notes"
msgstr "Opmerkingen"

#: InvenTree/format.py:166
#, python-brace-format
msgid "Value '{name}' does not appear in pattern format"
msgstr "Waarde '{name}' verschijnt niet in patroonformaat"

#: InvenTree/format.py:177
msgid "Provided value does not match required pattern: "
msgstr "Opgegeven waarde komt niet overeen met vereist patroon: "

#: InvenTree/helpers.py:552
msgid "Cannot serialize more than 1000 items at once"
msgstr "Kan niet meer dan 1000 items tegelijk serienummers geven."

#: InvenTree/helpers.py:558
msgid "Empty serial number string"
msgstr "Leeg serienummer"

#: InvenTree/helpers.py:587
msgid "Duplicate serial"
msgstr "Duplicaat serienummer"

#: InvenTree/helpers.py:619 InvenTree/helpers.py:662 InvenTree/helpers.py:680
#: InvenTree/helpers.py:687 InvenTree/helpers.py:706
#, python-brace-format
msgid "Invalid group: {group}"
msgstr "Ongeldige groep: {group}"

#: InvenTree/helpers.py:650
#, python-brace-format
msgid "Group range {group} exceeds allowed quantity ({expected_quantity})"
msgstr "Groepsbereik {group} overschrijdt toegestane hoeveelheid ({expected_quantity})"

#: InvenTree/helpers.py:716
msgid "No serial numbers found"
msgstr "Geen serienummers gevonden"

#: InvenTree/helpers.py:721
msgid "Number of unique serial numbers ({len(serials)}) must match quantity ({expected_quantity})"
msgstr "Hoeveelheid van unieke serienummers ({s}) moet overeenkomen met de hoeveelheid ({q})"

#: InvenTree/helpers.py:840
msgid "Remove HTML tags from this value"
msgstr "Verwijder HTML tags van deze waarde"

#: InvenTree/helpers.py:919
msgid "Data contains prohibited markdown content"
msgstr "Gegevens bevatten verboden markdown inhoud"

#: InvenTree/helpers_model.py:131
msgid "Connection error"
msgstr "Verbindingsfout"

#: InvenTree/helpers_model.py:136 InvenTree/helpers_model.py:143
msgid "Server responded with invalid status code"
msgstr "Server reageerde met ongeldige statuscode"

#: InvenTree/helpers_model.py:139
msgid "Exception occurred"
msgstr "Uitzondering opgetreden"

#: InvenTree/helpers_model.py:149
msgid "Server responded with invalid Content-Length value"
msgstr "Server reageerde met ongeldige Content-Length waarde"

#: InvenTree/helpers_model.py:152
msgid "Image size is too large"
msgstr "Afbeeldingsformaat is te groot"

#: InvenTree/helpers_model.py:164
msgid "Image download exceeded maximum size"
msgstr "Beelddownload overschrijdt de maximale grootte"

#: InvenTree/helpers_model.py:169
msgid "Remote server returned empty response"
msgstr "Externe server heeft lege reactie teruggegeven"

#: InvenTree/helpers_model.py:177
msgid "Supplied URL is not a valid image file"
msgstr "Opgegeven URL is geen geldig afbeeldingsbestand"

#: InvenTree/locales.py:20
msgid "Arabic"
msgstr "Arabisch"

#: InvenTree/locales.py:21
msgid "Bulgarian"
msgstr "Bulgaars"

#: InvenTree/locales.py:22
msgid "Czech"
msgstr "Tsjechisch"

#: InvenTree/locales.py:23
msgid "Danish"
msgstr "Deens"

#: InvenTree/locales.py:24
msgid "German"
msgstr "Duits"

#: InvenTree/locales.py:25
msgid "Greek"
msgstr "Grieks"

#: InvenTree/locales.py:26
msgid "English"
msgstr "Engels"

#: InvenTree/locales.py:27
msgid "Spanish"
msgstr "Spaans"

#: InvenTree/locales.py:28
msgid "Spanish (Mexican)"
msgstr "Spaans (Mexicaans)"

#: InvenTree/locales.py:29
msgid "Estonian"
msgstr "Estlands"

#: InvenTree/locales.py:30
msgid "Farsi / Persian"
msgstr "Farsi / Perzisch"

#: InvenTree/locales.py:31
msgid "Finnish"
msgstr "Fins"

#: InvenTree/locales.py:32
msgid "French"
msgstr "Frans"

#: InvenTree/locales.py:33
msgid "Hebrew"
msgstr "Hebreeuws"

#: InvenTree/locales.py:34
msgid "Hindi"
msgstr "Hindi"

#: InvenTree/locales.py:35
msgid "Hungarian"
msgstr "Hongaars"

#: InvenTree/locales.py:36
msgid "Italian"
msgstr "Italiaans"

#: InvenTree/locales.py:37
msgid "Japanese"
msgstr "Japans"

#: InvenTree/locales.py:38
msgid "Korean"
msgstr "Koreaans"

#: InvenTree/locales.py:39
msgid "Lithuanian"
msgstr "Litouws"

#: InvenTree/locales.py:40
msgid "Latvian"
msgstr "Lets"

#: InvenTree/locales.py:41
msgid "Dutch"
msgstr "Nederlands"

#: InvenTree/locales.py:42
msgid "Norwegian"
msgstr "Noors"

#: InvenTree/locales.py:43
msgid "Polish"
msgstr "Pools"

#: InvenTree/locales.py:44
msgid "Portuguese"
msgstr "Portugees"

#: InvenTree/locales.py:45
msgid "Portuguese (Brazilian)"
msgstr "Portugees (Braziliaans)"

#: InvenTree/locales.py:46
msgid "Romanian"
msgstr "Roemeens"

#: InvenTree/locales.py:47
msgid "Russian"
msgstr "Russisch"

#: InvenTree/locales.py:48
msgid "Slovak"
msgstr "Slowaaks"

#: InvenTree/locales.py:49
msgid "Slovenian"
msgstr "Sloveens"

#: InvenTree/locales.py:50
msgid "Serbian"
msgstr "Servisch"

#: InvenTree/locales.py:51
msgid "Swedish"
msgstr "Zweeds"

#: InvenTree/locales.py:52
msgid "Thai"
msgstr "Thais"

#: InvenTree/locales.py:53
msgid "Turkish"
msgstr "Turks"

#: InvenTree/locales.py:54
msgid "Ukrainian"
msgstr "Oekraïnes"

#: InvenTree/locales.py:55
msgid "Vietnamese"
msgstr "Vietnamees"

#: InvenTree/locales.py:56
msgid "Chinese (Simplified)"
msgstr "Chinees (vereenvoudigd)"

#: InvenTree/locales.py:57
msgid "Chinese (Traditional)"
msgstr "Chinees (traditioneel)"

#: InvenTree/magic_login.py:31
msgid "Log in to the app"
msgstr "Log in op de app"

#: InvenTree/magic_login.py:41 company/models.py:136 users/serializers.py:247
msgid "Email"
msgstr "E-mail"

#: InvenTree/models.py:107
msgid "Error running plugin validation"
msgstr "Fout bij uitvoeren plug-in validatie"

#: InvenTree/models.py:184
msgid "Metadata must be a python dict object"
msgstr "Metadata moeten een python dict object zijn"

#: InvenTree/models.py:190
msgid "Plugin Metadata"
msgstr "Plug-in metadata"

#: InvenTree/models.py:191
msgid "JSON metadata field, for use by external plugins"
msgstr "JSON metadata veld, voor gebruik door externe plugins"

#: InvenTree/models.py:374
msgid "Improperly formatted pattern"
msgstr "Onjuist opgemaakt patroon"

#: InvenTree/models.py:381
msgid "Unknown format key specified"
msgstr "Onbekende opmaaksleutel gespecificeerd"

#: InvenTree/models.py:387
msgid "Missing required format key"
msgstr "Vereiste opmaaksleutel ontbreekt"

#: InvenTree/models.py:398
msgid "Reference field cannot be empty"
msgstr "Referentieveld mag niet leeg zijn"

#: InvenTree/models.py:406
msgid "Reference must match required pattern"
msgstr "Referentie moet overeenkomen met verplicht patroon"

#: InvenTree/models.py:437
msgid "Reference number is too large"
msgstr "Referentienummer is te groot"

#: InvenTree/models.py:688
msgid "Duplicate names cannot exist under the same parent"
msgstr "Dubbele namen kunnen niet bestaan onder hetzelfde bovenliggende object"

#: InvenTree/models.py:705
msgid "Invalid choice"
msgstr "Ongeldige keuze"

#: InvenTree/models.py:734 common/models.py:1325 common/models.py:1752
#: common/models.py:2011 common/models.py:2136 common/serializers.py:488
#: company/models.py:593 generic/states/serializers.py:20 machine/models.py:24
#: part/models.py:1039 part/models.py:3837 plugin/models.py:53
#: report/models.py:203 stock/models.py:83
msgid "Name"
msgstr "Naam"

#: InvenTree/models.py:740 build/models.py:242 common/models.py:123
#: common/models.py:2143 common/models.py:2256 company/models.py:521
#: company/models.py:825 order/models.py:429 order/models.py:1615
#: part/models.py:1062 part/models.py:3852 report/models.py:209
#: report/models.py:714 report/models.py:740
#: report/templates/report/inventree_build_order_report.html:117
#: stock/models.py:89
msgid "Description"
msgstr "Omschrijving"

#: InvenTree/models.py:741 stock/models.py:90
msgid "Description (optional)"
msgstr "Omschrijving (optioneel)"

#: InvenTree/models.py:756 common/models.py:2309
msgid "Path"
msgstr "Pad"

#: InvenTree/models.py:887
msgid "Markdown notes (optional)"
msgstr "Markdown notitie (optioneel)"

#: InvenTree/models.py:918
msgid "Barcode Data"
msgstr "Streepjescode gegevens"

#: InvenTree/models.py:919
msgid "Third party barcode data"
msgstr "Streepjescode van derden"

#: InvenTree/models.py:925
msgid "Barcode Hash"
msgstr "Hash van Streepjescode"

#: InvenTree/models.py:926
msgid "Unique hash of barcode data"
msgstr "Unieke hash van barcode gegevens"

#: InvenTree/models.py:1003
msgid "Existing barcode found"
msgstr "Bestaande barcode gevonden"

#: InvenTree/models.py:1084
msgid "Task Failure"
msgstr "Taak mislukt"

#: InvenTree/models.py:1085
#, python-brace-format
msgid "Background worker task '{f}' failed after {n} attempts"
msgstr "Achtergrondtaak '{f}' is mislukt na {n} pogingen"

#: InvenTree/models.py:1112
msgid "Server Error"
msgstr "Serverfout"

#: InvenTree/models.py:1113
msgid "An error has been logged by the server."
msgstr "Er is een fout gelogd door de server."

#: InvenTree/serializers.py:69 part/models.py:4455
msgid "Must be a valid number"
msgstr "Moet een geldig nummer zijn"

#: InvenTree/serializers.py:111 company/models.py:187 part/models.py:3184
msgid "Currency"
msgstr "Valuta"

#: InvenTree/serializers.py:114 part/serializers.py:1335
msgid "Select currency from available options"
msgstr "Selecteer valuta uit beschikbare opties"

#: InvenTree/serializers.py:466
msgid "Invalid value"
msgstr "Ongeldige waarde"

#: InvenTree/serializers.py:503
msgid "Remote Image"
msgstr "Externe afbeelding"

#: InvenTree/serializers.py:504
msgid "URL of remote image file"
msgstr "URL van extern afbeeldingsbestand"

#: InvenTree/serializers.py:522
msgid "Downloading images from remote URL is not enabled"
msgstr "Afbeeldingen van externe URL downloaden is niet ingeschakeld"

#: InvenTree/serializers.py:529
msgid "Failed to download image from remote URL"
msgstr "Fout bij het downloaden van afbeelding van externe URL"

#: InvenTree/validators.py:30
msgid "Invalid physical unit"
msgstr "Ongeldige fysieke eenheid"

#: InvenTree/validators.py:36
msgid "Not a valid currency code"
msgstr "Geen geldige valutacode"

#: InvenTree/validators.py:116 InvenTree/validators.py:132
msgid "Overage value must not be negative"
msgstr "Overschotwaarde mag niet negatief zijn"

#: InvenTree/validators.py:134
msgid "Overage must not exceed 100%"
msgstr "Overschot mag niet groter zijn dan 100%"

#: InvenTree/validators.py:140
msgid "Invalid value for overage"
msgstr "Ongeldige waarde voor overschot"

#: build/api.py:39 order/api.py:104 order/api.py:263 order/serializers.py:128
msgid "Order Status"
msgstr "Status van bestelling"

#: build/api.py:65 build/models.py:254
msgid "Parent Build"
msgstr "Bovenliggende Productie"

#: build/api.py:69 build/api.py:768 order/api.py:512 order/api.py:732
#: order/api.py:1106 order/api.py:1334 stock/api.py:522
msgid "Include Variants"
msgstr "Inclusief varianten"

#: build/api.py:85 build/api.py:470 build/api.py:782 build/models.py:260
#: build/serializers.py:1254 build/serializers.py:1376
#: build/serializers.py:1427 company/models.py:1044 company/serializers.py:432
#: order/api.py:291 order/api.py:295 order/api.py:880 order/api.py:1119
#: order/api.py:1122 order/models.py:1704 order/models.py:1863
#: order/models.py:1864 part/api.py:1448 part/api.py:1451 part/api.py:1517
#: part/api.py:1827 part/models.py:457 part/models.py:3195 part/models.py:3339
#: part/models.py:3487 part/models.py:3508 part/models.py:3530
#: part/models.py:3666 part/models.py:4027 part/models.py:4299
#: part/models.py:4665 part/serializers.py:1255 part/serializers.py:1924
#: report/templates/report/inventree_bill_of_materials_report.html:110
#: report/templates/report/inventree_bill_of_materials_report.html:137
#: report/templates/report/inventree_build_order_report.html:109
#: report/templates/report/inventree_purchase_order_report.html:27
#: report/templates/report/inventree_return_order_report.html:24
#: report/templates/report/inventree_sales_order_report.html:27
#: report/templates/report/inventree_sales_order_shipment_report.html:28
#: report/templates/report/inventree_stock_location_report.html:102
#: stock/api.py:535 stock/serializers.py:119 stock/serializers.py:171
#: stock/serializers.py:481 stock/serializers.py:643 stock/serializers.py:948
#: templates/email/build_order_completed.html:17
#: templates/email/build_order_required_stock.html:17
#: templates/email/low_stock_notification.html:15
#: templates/email/overdue_build_order.html:16
#: templates/email/part_event_notification.html:15
msgid "Part"
msgstr "Onderdeel"

#: build/api.py:105 build/api.py:108 part/api.py:1531 part/models.py:1080
#: part/models.py:3558 part/models.py:4136 part/serializers.py:1265
#: stock/api.py:818
msgid "Category"
msgstr "Categorie"

#: build/api.py:116 build/api.py:120
msgid "Ancestor Build"
msgstr "Voorouderlijke bouw"

#: build/api.py:137 order/api.py:122
msgid "Assigned to me"
msgstr "Toegewezen aan mij"

#: build/api.py:152
#: report/templates/report/inventree_build_order_report.html:152
msgid "Issued By"
msgstr "Uitgegeven door"

#: build/api.py:167
msgid "Assigned To"
msgstr "Toegewezen aan"

#: build/api.py:202
msgid "Created before"
msgstr "Gemaakt voor"

#: build/api.py:206
msgid "Created after"
msgstr "Gemaakt na"

#: build/api.py:210
msgid "Has start date"
msgstr "Heeft een startdatum"

#: build/api.py:218
msgid "Start date before"
msgstr "Vervaldatum voor"

#: build/api.py:222
msgid "Start date after"
msgstr "Vervaldatum na"

#: build/api.py:226
msgid "Has target date"
msgstr "Heeft doel datum"

#: build/api.py:234
msgid "Target date before"
msgstr "Doel datum voor"

#: build/api.py:238
msgid "Target date after"
msgstr "Doel datum na"

#: build/api.py:242
msgid "Completed before"
msgstr "Voltooid voor"

#: build/api.py:246
msgid "Completed after"
msgstr "Voltooid na"

#: build/api.py:249 order/api.py:219
msgid "Min Date"
msgstr "Min. datum"

#: build/api.py:272 order/api.py:238
msgid "Max Date"
msgstr "Max. datum"

#: build/api.py:297 build/api.py:300 part/api.py:224
msgid "Exclude Tree"
msgstr "Boomstructuur uitsluiten"

#: build/api.py:409
msgid "Build must be cancelled before it can be deleted"
msgstr "Productie moet geannuleerd worden voordat het kan worden verwijderd"

#: build/api.py:453 build/serializers.py:1392 part/models.py:4333
msgid "Consumable"
msgstr "Verbruiksartikelen"

#: build/api.py:456 build/serializers.py:1395 part/models.py:4327
msgid "Optional"
msgstr "Optioneel"

#: build/api.py:459 common/setting/system.py:429 part/models.py:1211
#: part/serializers.py:1663 part/serializers.py:1672 stock/api.py:588
msgid "Assembly"
msgstr "Samenstelling"

#: build/api.py:462
msgid "Tracked"
msgstr "Gevolgd"

#: build/api.py:465 build/serializers.py:1398 part/models.py:1229
msgid "Testable"
msgstr "Testbaar"

#: build/api.py:475 order/api.py:925
msgid "Order Outstanding"
msgstr "Openstaande order"

#: build/api.py:485 order/api.py:884
msgid "Allocated"
msgstr "Toegewezen"

#: build/api.py:495 company/models.py:889 company/serializers.py:427
#: templates/email/build_order_required_stock.html:19
#: templates/email/low_stock_notification.html:17
#: templates/email/part_event_notification.html:18
msgid "Available"
msgstr "Beschikbaar"

#: build/api.py:805 build/models.py:116
#: report/templates/report/inventree_build_order_report.html:105
#: stock/serializers.py:92 templates/email/build_order_completed.html:16
#: templates/email/overdue_build_order.html:15
msgid "Build Order"
msgstr "Productieorder"

#: build/models.py:117 users/ruleset.py:37
msgid "Build Orders"
msgstr "Productieorders"

#: build/models.py:162
msgid "Assembly BOM has not been validated"
msgstr "Assemblage stuklijst is niet gevalideerd"

#: build/models.py:169
msgid "Build order cannot be created for an inactive part"
msgstr "Bouw bestelling kan niet worden aangemaakt voor een inactief onderdeel"

#: build/models.py:176
msgid "Build order cannot be created for an unlocked part"
msgstr "Maken opdracht kan niet worden gemaakt voor een ontgrendeld onderdeel"

#: build/models.py:188
msgid "Invalid choice for parent build"
msgstr "Ongeldige keuze voor bovenliggende productie"

#: build/models.py:197 order/models.py:363
msgid "Responsible user or group must be specified"
msgstr "Verantwoorde gebruiker of groep moet worden opgegeven"

#: build/models.py:202
msgid "Build order part cannot be changed"
msgstr "Bouworder onderdeel kan niet worden gewijzigd"

#: build/models.py:207 order/models.py:376
msgid "Target date must be after start date"
msgstr "Doeldatum moet na startdatum zijn"

#: build/models.py:235
msgid "Build Order Reference"
msgstr "Productieorderreferentie"

#: build/models.py:236 build/serializers.py:1389 order/models.py:601
#: order/models.py:1140 order/models.py:1572 order/models.py:2403
#: part/models.py:4348
#: report/templates/report/inventree_bill_of_materials_report.html:139
#: report/templates/report/inventree_purchase_order_report.html:28
#: report/templates/report/inventree_return_order_report.html:26
#: report/templates/report/inventree_sales_order_report.html:28
msgid "Reference"
msgstr "Referentie"

#: build/models.py:245
msgid "Brief description of the build (optional)"
msgstr "Korte beschrijving van de build (optioneel)"

#: build/models.py:255
msgid "BuildOrder to which this build is allocated"
msgstr "Productieorder waar deze productie aan is toegewezen"

#: build/models.py:264
msgid "Select part to build"
msgstr "Selecteer onderdeel om te produceren"

#: build/models.py:269
msgid "Sales Order Reference"
msgstr "Verkooporder Referentie"

#: build/models.py:274
msgid "SalesOrder to which this build is allocated"
msgstr "Verkooporder waar deze productie aan is toegewezen"

#: build/models.py:279 build/serializers.py:1104
msgid "Source Location"
msgstr "Bronlocatie"

#: build/models.py:285
msgid "Select location to take stock from for this build (leave blank to take from any stock location)"
msgstr "Selecteer de locatie waar de voorraad van de productie vandaan moet komen (laat leeg om vanaf elke standaard locatie te nemen)"

#: build/models.py:291
msgid "Destination Location"
msgstr "Bestemmings Locatie"

#: build/models.py:296
msgid "Select location where the completed items will be stored"
msgstr "Selecteer locatie waar de voltooide items zullen worden opgeslagen"

#: build/models.py:300
msgid "Build Quantity"
msgstr "Productiehoeveelheid"

#: build/models.py:303
msgid "Number of stock items to build"
msgstr "Aantal voorraaditems om te produceren"

#: build/models.py:307
msgid "Completed items"
msgstr "Voltooide voorraadartikelen"

#: build/models.py:309
msgid "Number of stock items which have been completed"
msgstr "Aantal voorraadartikelen die zijn voltooid"

#: build/models.py:313
msgid "Build Status"
msgstr "Productiestatus"

#: build/models.py:318
msgid "Build status code"
msgstr "Productiestatuscode"

#: build/models.py:327 build/serializers.py:376 order/serializers.py:785
#: stock/models.py:1023 stock/serializers.py:84 stock/serializers.py:1651
msgid "Batch Code"
msgstr "Batchcode"

#: build/models.py:331 build/serializers.py:377
msgid "Batch code for this build output"
msgstr "Batchcode voor deze productieuitvoer"

#: build/models.py:335 order/models.py:466 order/serializers.py:166
#: part/models.py:1286
msgid "Creation Date"
msgstr "Aanmaakdatum"

#: build/models.py:341
msgid "Build start date"
msgstr "Bouw start datum"

#: build/models.py:342
msgid "Scheduled start date for this build order"
msgstr "Geplande startdatum voor deze bestelling"

#: build/models.py:348
msgid "Target completion date"
msgstr "Verwachte opleveringsdatum"

#: build/models.py:350
msgid "Target date for build completion. Build will be overdue after this date."
msgstr "Doeldatum voor productie voltooiing. Productie zal achterstallig zijn na deze datum."

#: build/models.py:355 order/models.py:654 order/models.py:2442
msgid "Completion Date"
msgstr "Opleveringsdatum"

#: build/models.py:363
msgid "completed by"
msgstr "voltooid door"

#: build/models.py:372
msgid "Issued by"
msgstr "Uitgegeven door"

#: build/models.py:373
msgid "User who issued this build order"
msgstr "Gebruiker die de productieorder heeft gegeven"

#: build/models.py:382 common/models.py:132 order/api.py:172
#: order/models.py:491 part/models.py:1303
#: report/templates/report/inventree_build_order_report.html:158
msgid "Responsible"
msgstr "Verantwoordelijke"

#: build/models.py:383
msgid "User or group responsible for this build order"
msgstr "Gebruiker of groep verantwoordelijk voor deze bouwopdracht"

#: build/models.py:388 stock/models.py:1016
msgid "External Link"
msgstr "Externe Link"

#: build/models.py:390 common/models.py:1899 part/models.py:1114
#: stock/models.py:1018
msgid "Link to external URL"
msgstr "Link naar externe URL"

#: build/models.py:395
msgid "Build Priority"
msgstr "Bouw prioriteit"

#: build/models.py:398
msgid "Priority of this build order"
msgstr "Prioriteit van deze bouwopdracht"

#: build/models.py:406 common/models.py:102 common/models.py:116
#: order/api.py:158 order/models.py:438
msgid "Project Code"
msgstr "Project code"

#: build/models.py:407
msgid "Project code for this build order"
msgstr "Project code voor deze build order"

#: build/models.py:680 build/models.py:803
msgid "Failed to offload task to complete build allocations"
msgstr "Verwijderen van taak om toewijzingen te voltooien mislukt"

#: build/models.py:703
#, python-brace-format
msgid "Build order {build} has been completed"
msgstr "Productieorder {build} is voltooid"

#: build/models.py:709
msgid "A build order has been completed"
msgstr "Een productieorder is voltooid"

#: build/models.py:881 build/serializers.py:424
msgid "Serial numbers must be provided for trackable parts"
msgstr "Serienummers moeten worden opgegeven voor traceerbare onderdelen"

#: build/models.py:991 build/models.py:1076
msgid "No build output specified"
msgstr "Geen productie uitvoer opgegeven"

#: build/models.py:994
msgid "Build output is already completed"
msgstr "Productie uitvoer is al voltooid"

#: build/models.py:997
msgid "Build output does not match Build Order"
msgstr "Productuitvoer komt niet overeen met de Productieorder"

#: build/models.py:1079 build/serializers.py:303 build/serializers.py:352
#: build/serializers.py:972 order/models.py:704 order/serializers.py:601
#: order/serializers.py:780 part/serializers.py:1657 stock/models.py:863
#: stock/models.py:1730 stock/serializers.py:727
msgid "Quantity must be greater than zero"
msgstr "Hoeveelheid moet groter zijn dan nul"

#: build/models.py:1083 build/serializers.py:307
msgid "Quantity cannot be greater than the output quantity"
msgstr "Hoeveelheid kan niet groter zijn dan aantal"

#: build/models.py:1148 build/serializers.py:615
#, python-brace-format
msgid "Build output {serial} has not passed all required tests"
msgstr "Build output {serial} heeft niet alle vereiste tests doorstaan"

#: build/models.py:1534
msgid "Build Order Line Item"
msgstr "Bouw order regel item"

#: build/models.py:1558
msgid "Build object"
msgstr "Bouw object"

#: build/models.py:1570 build/models.py:1829 build/serializers.py:291
#: build/serializers.py:337 build/serializers.py:1410 common/models.py:1255
#: order/models.py:1555 order/models.py:2288 order/serializers.py:1670
#: order/serializers.py:2131 part/models.py:3353 part/models.py:4321
#: part/serializers.py:275
#: report/templates/report/inventree_bill_of_materials_report.html:138
#: report/templates/report/inventree_build_order_report.html:113
#: report/templates/report/inventree_purchase_order_report.html:29
#: report/templates/report/inventree_sales_order_report.html:29
#: report/templates/report/inventree_sales_order_shipment_report.html:47
#: report/templates/report/inventree_sales_order_shipment_report.html:49
#: report/templates/report/inventree_stock_location_report.html:104
#: report/templates/report/inventree_test_report.html:90
#: report/templates/report/inventree_test_report.html:169
#: stock/serializers.py:135 stock/serializers.py:179 stock/serializers.py:718
#: templates/email/build_order_completed.html:18
msgid "Quantity"
msgstr "Hoeveelheid"

#: build/models.py:1571
msgid "Required quantity for build order"
msgstr "Vereiste hoeveelheid voor bouwopdracht"

#: build/models.py:1655
msgid "Build item must specify a build output, as master part is marked as trackable"
msgstr "Productieartikel moet een productieuitvoer specificeren, omdat het hoofdonderdeel gemarkeerd is als traceerbaar"

#: build/models.py:1666
#, python-brace-format
msgid "Allocated quantity ({q}) must not exceed available stock quantity ({a})"
msgstr "Toegewezen hoeveelheid ({q}) mag de beschikbare voorraad ({a}) niet overschrijden"

#: build/models.py:1687 order/models.py:2237
msgid "Stock item is over-allocated"
msgstr "Voorraad item is te veel toegewezen"

#: build/models.py:1692 order/models.py:2240
msgid "Allocation quantity must be greater than zero"
msgstr "Toewijzing hoeveelheid moet groter zijn dan nul"

#: build/models.py:1698
msgid "Quantity must be 1 for serialized stock"
msgstr "Hoeveelheid moet 1 zijn voor geserialiseerde voorraad"

#: build/models.py:1758
msgid "Selected stock item does not match BOM line"
msgstr "Geselecteerde voorraadartikelen komen niet overeen met de BOM-regel"

#: build/models.py:1819 build/serializers.py:955 build/serializers.py:1263
#: order/serializers.py:1507 order/serializers.py:1528
#: report/templates/report/inventree_sales_order_shipment_report.html:29
#: stock/models.py:452 stock/serializers.py:101 stock/serializers.py:825
#: stock/serializers.py:1357 stock/serializers.py:1469
msgid "Stock Item"
msgstr "Voorraadartikel"

#: build/models.py:1820
msgid "Source stock item"
msgstr "Bron voorraadartikel"

#: build/models.py:1830
msgid "Stock quantity to allocate to build"
msgstr "Voorraad hoeveelheid toe te wijzen aan productie"

#: build/models.py:1839
msgid "Install into"
msgstr "Installeren in"

#: build/models.py:1840
msgid "Destination stock item"
msgstr "Bestemming voorraadartikel"

#: build/serializers.py:116
msgid "Build Level"
msgstr "Bouw level"

#: build/serializers.py:125 part/stocktake.py:219
msgid "Part Name"
msgstr "Onderdeel naam"

#: build/serializers.py:143
msgid "Project Code Label"
msgstr "Projectcode label"

#: build/serializers.py:155
msgid "Create Child Builds"
msgstr "Onderliggende builds aanmaken"

#: build/serializers.py:156
msgid "Automatically generate child build orders"
msgstr "Automatisch onderliggende build orders genereren"

#: build/serializers.py:239 build/serializers.py:981
msgid "Build Output"
msgstr "Productieuitvoer"

#: build/serializers.py:251
msgid "Build output does not match the parent build"
msgstr "Productieuitvoer komt niet overeen met de bovenliggende productie"

#: build/serializers.py:255
msgid "Output part does not match BuildOrder part"
msgstr "Uitvoeronderdeel komt niet overeen met productieorderonderdeel"

#: build/serializers.py:259
msgid "This build output has already been completed"
msgstr "Deze productieuitvoer is al voltooid"

#: build/serializers.py:273
msgid "This build output is not fully allocated"
msgstr "Deze productieuitvoer is niet volledig toegewezen"

#: build/serializers.py:292 build/serializers.py:338
msgid "Enter quantity for build output"
msgstr "Voer hoeveelheid in voor productie uitvoer"

#: build/serializers.py:360
msgid "Integer quantity required for trackable parts"
msgstr "Hoeveelheid als geheel getal vereist voor traceerbare onderdelen"

#: build/serializers.py:366
msgid "Integer quantity required, as the bill of materials contains trackable parts"
msgstr "Geheel getal vereist omdat de stuklijst traceerbare onderdelen bevat"

#: build/serializers.py:383 order/serializers.py:801 order/serializers.py:1674
#: stock/serializers.py:738
msgid "Serial Numbers"
msgstr "Serienummers"

#: build/serializers.py:384
msgid "Enter serial numbers for build outputs"
msgstr "Voer serienummers in voor productieuitvoeren"

#: build/serializers.py:389 build/serializers.py:514 build/serializers.py:584
#: build/serializers.py:1267 build/serializers.py:1271 order/serializers.py:769
#: order/serializers.py:916 order/serializers.py:2022 part/serializers.py:1275
#: stock/serializers.py:110 stock/serializers.py:647 stock/serializers.py:749
#: stock/serializers.py:913 stock/serializers.py:1041 stock/serializers.py:1501
#: stock/serializers.py:1789 users/models.py:551
msgid "Location"
msgstr "Locatie"

#: build/serializers.py:390
msgid "Stock location for build output"
msgstr "Voorraad locatie voor project uitvoer"

#: build/serializers.py:405
msgid "Auto Allocate Serial Numbers"
msgstr "Serienummers automatisch toewijzen"

#: build/serializers.py:407
msgid "Automatically allocate required items with matching serial numbers"
msgstr "Vereiste artikelen automatisch toewijzen met overeenkomende serienummers"

#: build/serializers.py:440 order/serializers.py:894 stock/api.py:1077
#: stock/models.py:1753
msgid "The following serial numbers already exist or are invalid"
msgstr "De volgende serienummers bestaan al of zijn ongeldig"

#: build/serializers.py:482 build/serializers.py:538 build/serializers.py:622
msgid "A list of build outputs must be provided"
msgstr "Een lijst van productieuitvoeren moet worden verstrekt"

#: build/serializers.py:515
msgid "Stock location for scrapped outputs"
msgstr "Voorraadlocatie voor geannuleerde outputs"

#: build/serializers.py:521
msgid "Discard Allocations"
msgstr "Toewijzingen weggooien"

#: build/serializers.py:522
msgid "Discard any stock allocations for scrapped outputs"
msgstr "Verwijder alle voorraadtoewijzingen voor geannuleerde outputs"

#: build/serializers.py:527
msgid "Reason for scrapping build output(s)"
msgstr "Reden voor annulering van bouworder(s)"

#: build/serializers.py:585
msgid "Location for completed build outputs"
msgstr "Locatie van voltooide productieuitvoeren"

#: build/serializers.py:593
msgid "Accept Incomplete Allocation"
msgstr "Incomplete Toewijzing Accepteren"

#: build/serializers.py:594
msgid "Complete outputs if stock has not been fully allocated"
msgstr "Voltooi de uitvoer als de voorraad niet volledig is toegewezen"

#: build/serializers.py:709
msgid "Consume Allocated Stock"
msgstr "Toegewezen voorraad gebruiken"

#: build/serializers.py:710
msgid "Consume any stock which has already been allocated to this build"
msgstr "Verbruik elke voorraad die al is toegewezen aan deze build"

#: build/serializers.py:716
msgid "Remove Incomplete Outputs"
msgstr "Verwijder Incomplete Uitvoeren"

#: build/serializers.py:717
msgid "Delete any build outputs which have not been completed"
msgstr "Verwijder alle productieuitvoeren die niet zijn voltooid"

#: build/serializers.py:744
msgid "Not permitted"
msgstr "Niet toegestaan"

#: build/serializers.py:745
msgid "Accept as consumed by this build order"
msgstr "Accepteer zoals geconsumeerd onder deze bouwopdracht"

#: build/serializers.py:746
msgid "Deallocate before completing this build order"
msgstr "De-alloceren voordat deze bouwopdracht voltooid wordt"

#: build/serializers.py:773
msgid "Overallocated Stock"
msgstr "Overgealloceerde voorraad"

#: build/serializers.py:776
msgid "How do you want to handle extra stock items assigned to the build order"
msgstr "Hoe wilt u omgaan met extra voorraaditems toegewezen aan de bouworder"

#: build/serializers.py:787
msgid "Some stock items have been overallocated"
msgstr "Sommige voorraadartikelen zijn overalloceerd"

#: build/serializers.py:792
msgid "Accept Unallocated"
msgstr "Accepteer Niet-toegewezen"

#: build/serializers.py:794
msgid "Accept that stock items have not been fully allocated to this build order"
msgstr "Accepteer dat voorraadartikelen niet volledig zijn toegewezen aan deze productieorder"

#: build/serializers.py:805
msgid "Required stock has not been fully allocated"
msgstr "Vereiste voorraad is niet volledig toegewezen"

#: build/serializers.py:810 order/serializers.py:444 order/serializers.py:1575
msgid "Accept Incomplete"
msgstr "Accepteer Onvolledig"

#: build/serializers.py:812
msgid "Accept that the required number of build outputs have not been completed"
msgstr "Accepteer dat het vereist aantal productieuitvoeren niet is voltooid"

#: build/serializers.py:823
msgid "Required build quantity has not been completed"
msgstr "Vereiste productiehoeveelheid is voltooid"

#: build/serializers.py:835
msgid "Build order has open child build orders"
msgstr "Bouw opdracht heeft open sub bouw orders"

#: build/serializers.py:838
msgid "Build order must be in production state"
msgstr "Bouwen moet in de productiestatus staan"

#: build/serializers.py:841
msgid "Build order has incomplete outputs"
msgstr "Productieorder heeft onvolledige uitvoeren"

#: build/serializers.py:880
msgid "Build Line"
msgstr "Productielijn"

#: build/serializers.py:888
msgid "Build output"
msgstr "Productieuitvoer"

#: build/serializers.py:896
msgid "Build output must point to the same build"
msgstr "Productieuitvoer moet naar dezelfde productie wijzen"

#: build/serializers.py:927
msgid "Build Line Item"
msgstr "Bouw lijn-item"

#: build/serializers.py:945
msgid "bom_item.part must point to the same part as the build order"
msgstr "bom_item.part moet naar hetzelfde onderdeel wijzen als de productieorder"

#: build/serializers.py:961 stock/serializers.py:1370
msgid "Item must be in stock"
msgstr "Artikel moet op voorraad zijn"

#: build/serializers.py:1004 order/serializers.py:1561
#, python-brace-format
msgid "Available quantity ({q}) exceeded"
msgstr "Beschikbare hoeveelheid ({q}) overschreden"

#: build/serializers.py:1010
msgid "Build output must be specified for allocation of tracked parts"
msgstr "Productieuitvoer moet worden opgegeven voor de toewijzing van gevolgde onderdelen"

#: build/serializers.py:1018
msgid "Build output cannot be specified for allocation of untracked parts"
msgstr "Productieuitvoer kan niet worden gespecificeerd voor de toewijzing van niet gevolgde onderdelen"

#: build/serializers.py:1042 order/serializers.py:1834
msgid "Allocation items must be provided"
msgstr "Allocaties voor artikelen moeten worden opgegeven"

#: build/serializers.py:1106
msgid "Stock location where parts are to be sourced (leave blank to take from any location)"
msgstr "Voorraadlocatie waar onderdelen afkomstig zijn (laat leeg om van elke locatie te nemen)"

#: build/serializers.py:1115
msgid "Exclude Location"
msgstr "Locatie uitsluiten"

#: build/serializers.py:1116
msgid "Exclude stock items from this selected location"
msgstr "Voorraadartikelen van deze geselecteerde locatie uitsluiten"

#: build/serializers.py:1121
msgid "Interchangeable Stock"
msgstr "Uitwisselbare voorraad"

#: build/serializers.py:1122
msgid "Stock items in multiple locations can be used interchangeably"
msgstr "Voorraadartikelen op meerdere locaties kunnen uitwisselbaar worden gebruikt"

#: build/serializers.py:1127
msgid "Substitute Stock"
msgstr "Vervangende Voorraad"

#: build/serializers.py:1128
msgid "Allow allocation of substitute parts"
msgstr "Toewijzing van vervangende onderdelen toestaan"

#: build/serializers.py:1133
msgid "Optional Items"
msgstr "Optionele Items"

#: build/serializers.py:1134
msgid "Allocate optional BOM items to build order"
msgstr "Alloceer optionele BOM items om bestelling te bouwen"

#: build/serializers.py:1156
msgid "Failed to start auto-allocation task"
msgstr "Starten van automatische toewijzing taak mislukt"

#: build/serializers.py:1230
msgid "BOM Reference"
msgstr "BOM referentie"

#: build/serializers.py:1236
msgid "BOM Part ID"
msgstr "BOM onderdeel ID"

#: build/serializers.py:1243
msgid "BOM Part Name"
msgstr "BOM onderdeel naam"

#: build/serializers.py:1278 build/serializers.py:1434
msgid "Build"
msgstr "Bouwen"

#: build/serializers.py:1286 company/models.py:662 order/api.py:304
#: order/api.py:309 order/api.py:508 order/serializers.py:593
#: stock/models.py:959 stock/serializers.py:631
msgid "Supplier Part"
msgstr "Leveranciersonderdeel"

#: build/serializers.py:1294 stock/serializers.py:662
msgid "Allocated Quantity"
msgstr "Toegewezen hoeveelheid"

#: build/serializers.py:1371
msgid "Build Reference"
msgstr "Bouw referentie"

#: build/serializers.py:1381
msgid "Part Category Name"
msgstr "Naam categorie onderdeel"

#: build/serializers.py:1401 common/setting/system.py:453 part/models.py:1223
msgid "Trackable"
msgstr "Volgbaar"

#: build/serializers.py:1404
msgid "Inherited"
msgstr "Overgenomen"

#: build/serializers.py:1407 part/models.py:4381
msgid "Allow Variants"
msgstr "Varianten toestaan"

#: build/serializers.py:1412 build/serializers.py:1416 part/models.py:4179
#: part/models.py:4657 stock/api.py:831
msgid "BOM Item"
msgstr "Stuklijstartikel"

#: build/serializers.py:1445
msgid "Allocated Stock"
msgstr "Toegewezen voorraad"

#: build/serializers.py:1447 company/serializers.py:424
#: order/serializers.py:1271 part/serializers.py:943 part/serializers.py:1690
msgid "On Order"
msgstr "In bestelling"

#: build/serializers.py:1449 order/serializers.py:1272 part/serializers.py:1694
msgid "In Production"
msgstr "In productie"

#: build/serializers.py:1451 part/serializers.py:964
msgid "External Stock"
msgstr "Externe voorraad"

#: build/serializers.py:1452 part/serializers.py:1723
msgid "Available Stock"
msgstr "Beschikbare Voorraad"

#: build/serializers.py:1454
msgid "Available Substitute Stock"
msgstr "Beschikbare vervanging voorraad"

#: build/serializers.py:1457
msgid "Available Variant Stock"
msgstr "Beschikbare varianten voorraad"

#: build/status_codes.py:11 generic/states/tests.py:21
#: generic/states/tests.py:131 order/status_codes.py:12
#: order/status_codes.py:44 order/status_codes.py:76 order/status_codes.py:102
msgid "Pending"
msgstr "Bezig"

#: build/status_codes.py:12
msgid "Production"
msgstr "Productie"

#: build/status_codes.py:13 order/status_codes.py:14 order/status_codes.py:51
#: order/status_codes.py:81
msgid "On Hold"
msgstr "In de wacht"

#: build/status_codes.py:14 order/status_codes.py:16 order/status_codes.py:53
#: order/status_codes.py:84
msgid "Cancelled"
msgstr "Geannuleerd"

#: build/status_codes.py:15 generic/states/tests.py:23 importer/models.py:529
#: importer/status_codes.py:27 order/status_codes.py:15
#: order/status_codes.py:52 order/status_codes.py:83
msgid "Complete"
msgstr "Voltooid"

#: build/tasks.py:193
msgid "Stock required for build order"
msgstr "Voorraad vereist voor productieorder"

#: build/tasks.py:267
msgid "Overdue Build Order"
msgstr "Achterstallige Productieorder"

#: build/tasks.py:272
#, python-brace-format
msgid "Build order {bo} is now overdue"
msgstr "Productieorder {bo} is nu achterstallig"

#: common/api.py:710
msgid "Is Link"
msgstr "Is koppeling"

#: common/api.py:718
msgid "Is File"
msgstr "Is een bestand"

#: common/api.py:761
msgid "User does not have permission to delete these attachments"
msgstr "Gebruiker heeft geen toestemming om deze bijlagen te verwijderen"

#: common/api.py:778
msgid "User does not have permission to delete this attachment"
msgstr "Gebruiker heeft geen toestemming om deze bijlage te verwijderen."

#: common/currency.py:120
msgid "Invalid currency code"
msgstr "Ongeldige valuta code"

#: common/currency.py:122
msgid "Duplicate currency code"
msgstr "Dubbele valutacode"

#: common/currency.py:127
msgid "No valid currency codes provided"
msgstr "Geen geldige valuta codes opgegeven"

#: common/currency.py:144
msgid "No plugin"
msgstr "Geen plug-in gevonden"

#: common/models.py:89
msgid "Updated"
msgstr "Bijgewerkt"

#: common/models.py:90
msgid "Timestamp of last update"
msgstr "Tijdstempel van laatste update"

#: common/models.py:117
msgid "Unique project code"
msgstr "Unieke projectcode"

#: common/models.py:124
msgid "Project description"
msgstr "Projectbeschrijving"

#: common/models.py:133
msgid "User or group responsible for this project"
msgstr "Gebruiker of groep die verantwoordelijk is voor dit project"

#: common/models.py:721 common/models.py:1187 common/models.py:1225
msgid "Settings key"
msgstr "Instellingen"

#: common/models.py:725
msgid "Settings value"
msgstr "Instellingswaarde"

#: common/models.py:780
msgid "Chosen value is not a valid option"
msgstr "Gekozen waarde is geen geldige optie"

#: common/models.py:796
msgid "Value must be a boolean value"
msgstr "Waarde moet een booleaanse waarde zijn"

#: common/models.py:804
msgid "Value must be an integer value"
msgstr "Waarde moet een geheel getal zijn"

#: common/models.py:812
msgid "Value must be a valid number"
msgstr "Waarde moet een geldig getal zijn"

#: common/models.py:837
msgid "Value does not pass validation checks"
msgstr "Waarde is niet geldig voor validatiecontrole"

#: common/models.py:859
msgid "Key string must be unique"
msgstr "Sleutelreeks moet uniek zijn"

#: common/models.py:1233 common/models.py:1234 common/models.py:1338
#: common/models.py:1339 common/models.py:1584 common/models.py:1585
#: common/models.py:1915 common/models.py:1916 common/models.py:2297
#: importer/models.py:97 part/models.py:3376 part/models.py:3463
#: part/models.py:3537 part/models.py:3565 plugin/models.py:322
#: plugin/models.py:323 report/templates/report/inventree_test_report.html:105
#: users/models.py:130 users/models.py:503
msgid "User"
msgstr "Gebruiker"

#: common/models.py:1256
msgid "Price break quantity"
msgstr "Prijs pauze hoeveelheid"

#: common/models.py:1263 company/serializers.py:567 order/models.py:1632
#: order/models.py:2734
msgid "Price"
msgstr "Prijs"

#: common/models.py:1264
msgid "Unit price at specified quantity"
msgstr "Stukprijs op opgegeven hoeveelheid"

#: common/models.py:1315 common/models.py:1500
msgid "Endpoint"
msgstr "Eindpunt"

#: common/models.py:1316
msgid "Endpoint at which this webhook is received"
msgstr "Eindpunt waarop deze webhook wordt ontvangen"

#: common/models.py:1326
msgid "Name for this webhook"
msgstr "Naam van deze webhook"

#: common/models.py:1330 common/models.py:2156 common/models.py:2263
#: company/models.py:164 company/models.py:799 machine/models.py:39
#: part/models.py:1246 plugin/models.py:68 stock/api.py:591 users/models.py:201
#: users/models.py:556 users/serializers.py:333
msgid "Active"
msgstr "Actief"

#: common/models.py:1330
msgid "Is this webhook active"
msgstr "Is deze webhook actief"

#: common/models.py:1346 users/models.py:178
msgid "Token"
msgstr "Sleutel"

#: common/models.py:1347
msgid "Token for access"
msgstr "Token voor toegang"

#: common/models.py:1355
msgid "Secret"
msgstr "Geheim"

#: common/models.py:1356
msgid "Shared secret for HMAC"
msgstr "Gedeeld geheim voor HMAC"

#: common/models.py:1464
msgid "Message ID"
msgstr "Bericht ID"

#: common/models.py:1465
msgid "Unique identifier for this message"
msgstr "Unieke identificatie voor dit bericht"

#: common/models.py:1473
msgid "Host"
msgstr "Host"

#: common/models.py:1474
msgid "Host from which this message was received"
msgstr "Host waarvan dit bericht is ontvangen"

#: common/models.py:1482
msgid "Header"
msgstr "Koptekst"

#: common/models.py:1483
msgid "Header of this message"
msgstr "Koptekst van dit bericht"

#: common/models.py:1490
msgid "Body"
msgstr "Berichtinhoud"

#: common/models.py:1491
msgid "Body of this message"
msgstr "Inhoud van dit bericht"

#: common/models.py:1501
msgid "Endpoint on which this message was received"
msgstr "Eindpunt waarop dit bericht is ontvangen"

#: common/models.py:1506
msgid "Worked on"
msgstr "Aan gewerkt"

#: common/models.py:1507
msgid "Was the work on this message finished?"
msgstr "Is het werk aan dit bericht voltooid?"

#: common/models.py:1633
msgid "Id"
msgstr "Id"

#: common/models.py:1635 part/serializers.py:281
msgid "Title"
msgstr "Titel"

#: common/models.py:1637 common/models.py:1898 company/models.py:149
#: company/models.py:445 company/models.py:512 company/models.py:816
#: order/models.py:444 order/models.py:1585 order/models.py:2056
#: part/models.py:1113
#: report/templates/report/inventree_build_order_report.html:164
msgid "Link"
msgstr "Koppeling"

#: common/models.py:1639
msgid "Published"
msgstr "Gepubliceerd"

#: common/models.py:1641
msgid "Author"
msgstr "Auteur"

#: common/models.py:1643
msgid "Summary"
msgstr "Samenvatting"

#: common/models.py:1646
msgid "Read"
msgstr "Gelezen"

#: common/models.py:1646
msgid "Was this news item read?"
msgstr "Is dit nieuwsitem gelezen?"

#: common/models.py:1663 company/models.py:160 part/models.py:1124
#: report/templates/report/inventree_bill_of_materials_report.html:126
#: report/templates/report/inventree_bill_of_materials_report.html:148
#: report/templates/report/inventree_return_order_report.html:35
msgid "Image"
msgstr "Afbeelding"

#: common/models.py:1663
msgid "Image file"
msgstr "Afbeelding"

#: common/models.py:1675
msgid "Target model type for this image"
msgstr "Doel type voor deze afbeelding"

#: common/models.py:1679
msgid "Target model ID for this image"
msgstr "Doel modelnummer voor deze afbeelding"

#: common/models.py:1701
msgid "Custom Unit"
msgstr "Aangepaste eenheid"

#: common/models.py:1719
msgid "Unit symbol must be unique"
msgstr "Eenheid symbool moet uniek zijn"

#: common/models.py:1734
msgid "Unit name must be a valid identifier"
msgstr "Naam van de unit moet een geldig id zijn"

#: common/models.py:1753
msgid "Unit name"
msgstr "Naam van eenheid"

#: common/models.py:1760
msgid "Symbol"
msgstr "Symbool"

#: common/models.py:1761
msgid "Optional unit symbol"
msgstr "Optionele eenheid symbool"

#: common/models.py:1767
msgid "Definition"
msgstr "Definitie"

#: common/models.py:1768
msgid "Unit definition"
msgstr "Definitie van eenheid"

#: common/models.py:1826 common/models.py:1889 stock/models.py:2827
#: stock/serializers.py:258
msgid "Attachment"
msgstr "Bijlage"

#: common/models.py:1843
msgid "Missing file"
msgstr "Ontbrekend bestand"

#: common/models.py:1844
msgid "Missing external link"
msgstr "Externe link ontbreekt"

#: common/models.py:1881
msgid "Model type"
msgstr "Model type"

#: common/models.py:1882
msgid "Target model type for image"
msgstr "Doel type voor afbeelding"

#: common/models.py:1890
msgid "Select file to attach"
msgstr "Bestand als bijlage selecteren"

#: common/models.py:1906
msgid "Comment"
msgstr "Opmerking"

#: common/models.py:1907
msgid "Attachment comment"
msgstr "Opmerking van bijlage"

#: common/models.py:1923
msgid "Upload date"
msgstr "Uploaddatum"

#: common/models.py:1924
msgid "Date the file was uploaded"
msgstr "Datum waarop het bestand is geüpload"

#: common/models.py:1928
msgid "File size"
msgstr "Bestandsgrootte"

#: common/models.py:1928
msgid "File size in bytes"
msgstr "Bestandsgrootte in bytes"

#: common/models.py:1966 common/serializers.py:637
msgid "Invalid model type specified for attachment"
msgstr "Ongeldig modeltype opgegeven voor bijlage"

#: common/models.py:1987
msgid "Custom State"
msgstr "Aangepaste staat"

#: common/models.py:1988
msgid "Custom States"
msgstr "Aangepaste statussen"

#: common/models.py:1993
msgid "Reference Status Set"
msgstr "Referentie status set"

#: common/models.py:1994
msgid "Status set that is extended with this custom state"
msgstr "Status set die met deze aangepaste status wordt uitgebreid"

#: common/models.py:1998 generic/states/serializers.py:18
msgid "Logical Key"
msgstr "Logische sleutel"

#: common/models.py:2000
msgid "State logical key that is equal to this custom state in business logic"
msgstr "Staat logische sleutel die gelijk is aan deze staat in zakelijke logica"

#: common/models.py:2005 common/models.py:2244 company/models.py:600
#: report/templates/report/inventree_test_report.html:104 stock/models.py:2819
msgid "Value"
msgstr "Waarde"

#: common/models.py:2006
msgid "Numerical value that will be saved in the models database"
msgstr "De numerieke waarde die wordt opgeslagen in de modellendatabase"

#: common/models.py:2012
msgid "Name of the state"
msgstr "Naam van de toestand"

#: common/models.py:2021 common/models.py:2250 generic/states/serializers.py:22
#: part/serializers.py:283
msgid "Label"
msgstr "Label"

#: common/models.py:2022
msgid "Label that will be displayed in the frontend"
msgstr "Label dat in de frontend getoond wordt"

#: common/models.py:2029 generic/states/serializers.py:24
msgid "Color"
msgstr "Kleur"

#: common/models.py:2030
msgid "Color that will be displayed in the frontend"
msgstr "Kleur die in de frontend getoond wordt"

#: common/models.py:2038 part/serializers.py:285
msgid "Model"
msgstr "Model"

#: common/models.py:2039
msgid "Model this state is associated with"
msgstr "Model met deze staat is gekoppeld aan"

#: common/models.py:2054
msgid "Model must be selected"
msgstr "Het model moet worden gekozen"

#: common/models.py:2057
msgid "Key must be selected"
msgstr "Sleutel moet worden geselecteerd"

#: common/models.py:2060
msgid "Logical key must be selected"
msgstr "Logische sleutel moet worden geselecteerd"

#: common/models.py:2064
msgid "Key must be different from logical key"
msgstr "Sleutel moet anders zijn dan logische sleutel"

#: common/models.py:2071
msgid "Valid reference status class must be provided"
msgstr "Geldige referentie status klasse moet worden opgegeven"

#: common/models.py:2077
msgid "Key must be different from the logical keys of the reference status"
msgstr "Sleutel moet verschillen van de logische sleutels van de referentie status"

#: common/models.py:2084
msgid "Logical key must be in the logical keys of the reference status"
msgstr "Logische sleutel moet in de logische sleutels van de referentiestatus staan"

#: common/models.py:2091
msgid "Name must be different from the names of the reference status"
msgstr "Naam moet anders zijn dan de namen van de referentie status"

#: common/models.py:2131 common/models.py:2238 part/models.py:3876
msgid "Selection List"
msgstr "Keuzelijst"

#: common/models.py:2132
msgid "Selection Lists"
msgstr "Selectielijst"

#: common/models.py:2137
msgid "Name of the selection list"
msgstr "Naam van de selectielijst"

#: common/models.py:2144
msgid "Description of the selection list"
msgstr "Beschrijving van de selectielijst"

#: common/models.py:2150 part/models.py:1251
msgid "Locked"
msgstr "Vergrendeld"

#: common/models.py:2151
msgid "Is this selection list locked?"
msgstr "Is deze selectielijst vergrendeld?"

#: common/models.py:2157
msgid "Can this selection list be used?"
msgstr "Kan deze selectielijst worden gebruikt?"

#: common/models.py:2165
msgid "Source Plugin"
msgstr "Bron plug-in"

#: common/models.py:2166
msgid "Plugin which provides the selection list"
msgstr "Plug-in die de selectielijst biedt"

#: common/models.py:2171
msgid "Source String"
msgstr "Bron tekenreeks"

#: common/models.py:2172
msgid "Optional string identifying the source used for this list"
msgstr "Optionele tekenreeks die de bron identificeert die voor deze lijst wordt gebruikt"

#: common/models.py:2181
msgid "Default Entry"
msgstr "Standaard vermelding"

#: common/models.py:2182
msgid "Default entry for this selection list"
msgstr "Standaard vermelding voor deze selectielijst"

#: common/models.py:2187
msgid "Created"
msgstr "Gecreëerd"

#: common/models.py:2188
msgid "Date and time that the selection list was created"
msgstr "Datum en tijd waarop de selectielijst is aangemaakt"

#: common/models.py:2193
msgid "Last Updated"
msgstr "Laatst bijgewerkt"

#: common/models.py:2194
msgid "Date and time that the selection list was last updated"
msgstr "Datum en tijd waarop de selectielijst voor het laatst is bijgewerkt"

#: common/models.py:2228
msgid "Selection List Entry"
msgstr "Selectielijst item"

#: common/models.py:2229
msgid "Selection List Entries"
msgstr "Selectielijst item"

#: common/models.py:2239
msgid "Selection list to which this entry belongs"
msgstr "Selectielijst waaraan dit item hoort"

#: common/models.py:2245
msgid "Value of the selection list entry"
msgstr "Naam van de selectielijst"

#: common/models.py:2251
msgid "Label for the selection list entry"
msgstr "Label voor het item in de selectielijst"

#: common/models.py:2257
msgid "Description of the selection list entry"
msgstr "Beschrijving van het item in de selectielijst"

#: common/models.py:2264
msgid "Is this selection list entry active?"
msgstr "Is dit item in deze lijst actief?"

#: common/models.py:2282
msgid "Barcode Scan"
msgstr "Barcode Scan"

#: common/models.py:2286 importer/models.py:523 part/models.py:4041
msgid "Data"
msgstr "Gegevens"

#: common/models.py:2287
msgid "Barcode data"
msgstr "Barcode gegevens"

#: common/models.py:2298
msgid "User who scanned the barcode"
msgstr "Gebruiker die de barcode gescand heeft"

#: common/models.py:2303 importer/models.py:66
msgid "Timestamp"
msgstr "Tijdstempel"

#: common/models.py:2304
msgid "Date and time of the barcode scan"
msgstr "Datum en tijd van de streepjescode scan"

#: common/models.py:2310
msgid "URL endpoint which processed the barcode"
msgstr "Adres eindpunt dat de streepjescode verwerkt"

#: common/models.py:2317 order/models.py:1622 plugin/serializers.py:93
msgid "Context"
msgstr "Inhoud"

#: common/models.py:2318
msgid "Context data for the barcode scan"
msgstr "Contextgegevens voor de barcode scan"

#: common/models.py:2325
msgid "Response"
msgstr "Reactie"

#: common/models.py:2326
msgid "Response data from the barcode scan"
msgstr "Reactiegegevens van de barcode scan"

#: common/models.py:2332 report/templates/report/inventree_test_report.html:103
#: stock/models.py:2813
msgid "Result"
msgstr "Resultaat"

#: common/models.py:2333
msgid "Was the barcode scan successful?"
msgstr "Was de barcode succesvol gescand?"

#: common/notifications.py:332
#, python-brace-format
msgid "New {verbose_name}"
msgstr "Nieuw: {verbose_name}"

#: common/notifications.py:334
msgid "A new order has been created and assigned to you"
msgstr "Een nieuwe order is aangemaakt en aan u toegewezen"

#: common/notifications.py:340
#, python-brace-format
msgid "{verbose_name} canceled"
msgstr "{verbose_name} is geannuleerd"

#: common/notifications.py:342
msgid "A order that is assigned to you was canceled"
msgstr "Een bestelling die aan u is toegewezen is geannuleerd"

#: common/notifications.py:348 common/notifications.py:355 order/api.py:559
msgid "Items Received"
msgstr "Ontvangen items"

#: common/notifications.py:350
msgid "Items have been received against a purchase order"
msgstr "Artikelen zijn ontvangen tegen een inkooporder"

#: common/notifications.py:357
msgid "Items have been received against a return order"
msgstr "Items zijn ontvangen tegen een retour bestelling"

#: common/notifications.py:505
msgid "Error raised by plugin"
msgstr "Fout veroorzaakt door plug-in"

#: common/serializers.py:451
msgid "Is Running"
msgstr "Is actief"

#: common/serializers.py:457
msgid "Pending Tasks"
msgstr "Openstaande taken"

#: common/serializers.py:463
msgid "Scheduled Tasks"
msgstr "Geplande taken"

#: common/serializers.py:469
msgid "Failed Tasks"
msgstr "Mislukte taken"

#: common/serializers.py:484
msgid "Task ID"
msgstr "Taak ID"

#: common/serializers.py:484
msgid "Unique task ID"
msgstr "Unieke taak ID"

#: common/serializers.py:486
msgid "Lock"
msgstr "Vergrendel"

#: common/serializers.py:486
msgid "Lock time"
msgstr "Tijdstip van vergrendeling"

#: common/serializers.py:488
msgid "Task name"
msgstr "Naam van de taak"

#: common/serializers.py:490
msgid "Function"
msgstr "Functie"

#: common/serializers.py:490
msgid "Function name"
msgstr "Functie naam"

#: common/serializers.py:492
msgid "Arguments"
msgstr "Argumenten"

#: common/serializers.py:492
msgid "Task arguments"
msgstr "Taak argumenten"

#: common/serializers.py:495
msgid "Keyword Arguments"
msgstr "Trefwoord argumenten"

#: common/serializers.py:495
msgid "Task keyword arguments"
msgstr "Taak trefwoord argumenten"

#: common/serializers.py:605
msgid "Filename"
msgstr "Bestandsnaam"

#: common/serializers.py:612 importer/models.py:86 report/api.py:41
#: report/models.py:277 report/serializers.py:53
msgid "Model Type"
msgstr "Model type"

#: common/serializers.py:640
msgid "User does not have permission to create or edit attachments for this model"
msgstr "Gebruiker heeft geen toestemming om bijlagen voor dit model te maken of te bewerken"

#: common/serializers.py:684 common/serializers.py:787
msgid "Selection list is locked"
msgstr "Lijst met selecties is vergrendeld"

#: common/setting/system.py:97
msgid "No group"
msgstr "Geen groep"

#: common/setting/system.py:156
msgid "Site URL is locked by configuration"
msgstr "Website URL is vergrendeld door configuratie"

#: common/setting/system.py:167
msgid "Restart required"
msgstr "Opnieuw opstarten vereist"

#: common/setting/system.py:168
msgid "A setting has been changed which requires a server restart"
msgstr "Een instelling is gewijzigd waarvoor een herstart van de server vereist is"

#: common/setting/system.py:174
msgid "Pending migrations"
msgstr "Migraties in behandeling"

#: common/setting/system.py:175
msgid "Number of pending database migrations"
msgstr "Aantal nog openstaande database migraties"

#: common/setting/system.py:180
msgid "Instance ID"
msgstr "Instantie Id"

#: common/setting/system.py:181
msgid "Unique identifier for this InvenTree instance"
msgstr "Unieke identificatie voor deze InvenTree instantie"

#: common/setting/system.py:186
msgid "Announce ID"
msgstr "Aankondiging ID"

#: common/setting/system.py:188
msgid "Announce the instance ID of the server in the server status info (unauthenticated)"
msgstr "Kondig de instantie ID van de server aan in de server status info (ongeautoriseerd)"

#: common/setting/system.py:194
msgid "Server Instance Name"
msgstr "ID Serverinstantie"

#: common/setting/system.py:196
msgid "String descriptor for the server instance"
msgstr "Stringbeschrijving voor de server instantie"

#: common/setting/system.py:200
msgid "Use instance name"
msgstr "Gebruik de instantie naam"

#: common/setting/system.py:201
msgid "Use the instance name in the title-bar"
msgstr "Gebruik de naam van de instantie in de titelbalk"

#: common/setting/system.py:206
msgid "Restrict showing `about`"
msgstr "Tonen `over` beperken"

#: common/setting/system.py:207
msgid "Show the `about` modal only to superusers"
msgstr "Toon de `over` modal alleen aan superusers"

#: common/setting/system.py:212 company/models.py:108 company/models.py:109
msgid "Company name"
msgstr "Bedrijfsnaam"

#: common/setting/system.py:213
msgid "Internal company name"
msgstr "Interne bedrijfsnaam"

#: common/setting/system.py:217
msgid "Base URL"
msgstr "Basis-URL"

#: common/setting/system.py:218
msgid "Base URL for server instance"
msgstr "Basis URL voor serverinstantie"

#: common/setting/system.py:224
msgid "Default Currency"
msgstr "Standaard Valuta"

#: common/setting/system.py:225
msgid "Select base currency for pricing calculations"
msgstr "Selecteer basisvaluta voor de berekening van prijzen"

#: common/setting/system.py:231
msgid "Supported Currencies"
msgstr "Ondersteunde valuta"

#: common/setting/system.py:232
msgid "List of supported currency codes"
msgstr "Lijst van ondersteunde valuta codes"

#: common/setting/system.py:238
msgid "Currency Update Interval"
msgstr "Valuta update interval"

#: common/setting/system.py:239
msgid "How often to update exchange rates (set to zero to disable)"
msgstr "Hoe vaak te controleren op updates (nul om uit te schakelen)"

#: common/setting/system.py:241 common/setting/system.py:281
#: common/setting/system.py:294 common/setting/system.py:302
#: common/setting/system.py:309 common/setting/system.py:318
#: common/setting/system.py:567 common/setting/system.py:587
#: common/setting/system.py:684 common/setting/system.py:1068
msgid "days"
msgstr "dagen"

#: common/setting/system.py:245
msgid "Currency Update Plugin"
msgstr "Valuta update plug-in"

#: common/setting/system.py:246
msgid "Currency update plugin to use"
msgstr "Munteenheid update plug-in om te gebruiken"

#: common/setting/system.py:251
msgid "Download from URL"
msgstr "Download van URL"

#: common/setting/system.py:252
msgid "Allow download of remote images and files from external URL"
msgstr "Download van afbeeldingen en bestanden vanaf een externe URL toestaan"

#: common/setting/system.py:257
msgid "Download Size Limit"
msgstr "Download limiet"

#: common/setting/system.py:258
msgid "Maximum allowable download size for remote image"
msgstr "Maximale downloadgrootte voor externe afbeelding"

#: common/setting/system.py:264
msgid "User-agent used to download from URL"
msgstr "User-agent gebruikt om te downloaden van URL"

#: common/setting/system.py:266
msgid "Allow to override the user-agent used to download images and files from external URL (leave blank for the default)"
msgstr "Sta toe om de user-agent te overschrijven die gebruikt wordt om afbeeldingen en bestanden van externe URL te downloaden (laat leeg voor de standaard)"

#: common/setting/system.py:271
msgid "Strict URL Validation"
msgstr "Strikte URL validatie"

#: common/setting/system.py:272
msgid "Require schema specification when validating URLs"
msgstr "Vereis schema specificatie bij het valideren van URLs"

#: common/setting/system.py:277
msgid "Update Check Interval"
msgstr "Interval voor update"

#: common/setting/system.py:278
msgid "How often to check for updates (set to zero to disable)"
msgstr "Hoe vaak te controleren op updates (nul om uit te schakelen)"

#: common/setting/system.py:284
msgid "Automatic Backup"
msgstr "Automatische backup"

#: common/setting/system.py:285
msgid "Enable automatic backup of database and media files"
msgstr "Automatische back-up van database- en mediabestanden inschakelen"

#: common/setting/system.py:290
msgid "Auto Backup Interval"
msgstr "Automatische backup interval"

#: common/setting/system.py:291
msgid "Specify number of days between automated backup events"
msgstr "Geef het aantal dagen op tussen geautomatiseerde backup"

#: common/setting/system.py:297
msgid "Task Deletion Interval"
msgstr "Interval Taak Verwijderen"

#: common/setting/system.py:299
msgid "Background task results will be deleted after specified number of days"
msgstr "Resultaten van achtergrondtaken worden verwijderd na het opgegeven aantal dagen"

#: common/setting/system.py:306
msgid "Error Log Deletion Interval"
msgstr "Error Log Verwijderings Interval"

#: common/setting/system.py:307
msgid "Error logs will be deleted after specified number of days"
msgstr "Resultaten van achtergrondtaken worden verwijderd na het opgegeven aantal dagen"

#: common/setting/system.py:313
msgid "Notification Deletion Interval"
msgstr "Interval Verwijderen Notificatie"

#: common/setting/system.py:315
msgid "User notifications will be deleted after specified number of days"
msgstr "Meldingen van gebruikers worden verwijderd na het opgegeven aantal dagen"

#: common/setting/system.py:322
msgid "Barcode Support"
msgstr "Streepjescodeondersteuning"

#: common/setting/system.py:323
msgid "Enable barcode scanner support in the web interface"
msgstr "Schakel barcodescanner ondersteuning in in de webinterface"

#: common/setting/system.py:328
msgid "Store Barcode Results"
msgstr "Sla de resultaten van de barcode op"

#: common/setting/system.py:329
msgid "Store barcode scan results in the database"
msgstr "Sla de barcode scan resultaten op in de database"

#: common/setting/system.py:334
msgid "Barcode Scans Maximum Count"
msgstr "Maximale aantal Barcode Scans"

#: common/setting/system.py:335
msgid "Maximum number of barcode scan results to store"
msgstr "Maximum aantal resultaten van de barcode scan op te slaan"

#: common/setting/system.py:340
msgid "Barcode Input Delay"
msgstr "Barcode Invoer Vertraging"

#: common/setting/system.py:341
msgid "Barcode input processing delay time"
msgstr "Barcode invoerverwerking vertraging"

#: common/setting/system.py:347
msgid "Barcode Webcam Support"
msgstr "Barcode Webcam Ondersteuning"

#: common/setting/system.py:348
msgid "Allow barcode scanning via webcam in browser"
msgstr "Barcode via webcam scannen in browser toestaan"

#: common/setting/system.py:353
msgid "Barcode Show Data"
msgstr "Barcode gegevens"

#: common/setting/system.py:354
msgid "Display barcode data in browser as text"
msgstr "Geef barcode gegevens weer in browser als tekst"

#: common/setting/system.py:359
msgid "Barcode Generation Plugin"
msgstr "Streepjescode Plug-in"

#: common/setting/system.py:360
msgid "Plugin to use for internal barcode data generation"
msgstr "Plug-in om te gebruiken voor interne barcode data genereren"

#: common/setting/system.py:365
msgid "Part Revisions"
msgstr "Herzieningen onderdeel"

#: common/setting/system.py:366
msgid "Enable revision field for Part"
msgstr "Revisieveld voor onderdeel inschakelen"

#: common/setting/system.py:371
msgid "Assembly Revision Only"
msgstr "Alleen assemblee revisie"

#: common/setting/system.py:372
msgid "Only allow revisions for assembly parts"
msgstr "Alleen revisies toestaan voor assemblageonderdelen"

#: common/setting/system.py:377
msgid "Allow Deletion from Assembly"
msgstr "Verwijderen uit Assemblage toestaan"

#: common/setting/system.py:378
msgid "Allow deletion of parts which are used in an assembly"
msgstr "Verwijderen van onderdelen die in een groep worden gebruikt toestaan"

#: common/setting/system.py:383
msgid "IPN Regex"
msgstr "IPN Regex"

#: common/setting/system.py:384
msgid "Regular expression pattern for matching Part IPN"
msgstr "Regulier expressiepatroon voor het overeenkomende Onderdeel IPN"

#: common/setting/system.py:387
msgid "Allow Duplicate IPN"
msgstr "Duplicaat IPN toestaan"

#: common/setting/system.py:388
msgid "Allow multiple parts to share the same IPN"
msgstr "Toestaan dat meerdere onderdelen dezelfde IPN gebruiken"

#: common/setting/system.py:393
msgid "Allow Editing IPN"
msgstr "Bewerken IPN toestaan"

#: common/setting/system.py:394
msgid "Allow changing the IPN value while editing a part"
msgstr "Sta het wijzigen van de IPN toe tijdens het bewerken van een onderdeel"

#: common/setting/system.py:399
msgid "Copy Part BOM Data"
msgstr "Kopieer Onderdeel Stuklijstgegevens"

#: common/setting/system.py:400
msgid "Copy BOM data by default when duplicating a part"
msgstr "Kopieer standaard stuklijstgegevens bij het dupliceren van een onderdeel"

#: common/setting/system.py:405
msgid "Copy Part Parameter Data"
msgstr "Kopieer Onderdeel Parametergegevens"

#: common/setting/system.py:406
msgid "Copy parameter data by default when duplicating a part"
msgstr "Parametergegevens standaard kopiëren bij het dupliceren van een onderdeel"

#: common/setting/system.py:411
msgid "Copy Part Test Data"
msgstr "Kopieer Onderdeel Testdata"

#: common/setting/system.py:412
msgid "Copy test data by default when duplicating a part"
msgstr "Testdata standaard kopiëren bij het dupliceren van een onderdeel"

#: common/setting/system.py:417
msgid "Copy Category Parameter Templates"
msgstr "Kopiëer Categorieparameter Sjablonen"

#: common/setting/system.py:418
msgid "Copy category parameter templates when creating a part"
msgstr "Kopieer categorieparameter sjablonen bij het aanmaken van een onderdeel"

#: common/setting/system.py:423 part/models.py:4035 report/models.py:357
#: report/models.py:563 report/serializers.py:90 report/serializers.py:131
#: stock/serializers.py:247
msgid "Template"
msgstr "Sjabloon"

#: common/setting/system.py:424
msgid "Parts are templates by default"
msgstr "Onderdelen zijn standaard sjablonen"

#: common/setting/system.py:430
msgid "Parts can be assembled from other components by default"
msgstr "Onderdelen kunnen standaard vanuit andere componenten worden samengesteld"

#: common/setting/system.py:435 part/models.py:1217 part/serializers.py:1677
#: part/serializers.py:1683
msgid "Component"
msgstr "Onderdeel"

#: common/setting/system.py:436
msgid "Parts can be used as sub-components by default"
msgstr "Onderdelen kunnen standaard worden gebruikt als subcomponenten"

#: common/setting/system.py:441 part/models.py:1235
msgid "Purchaseable"
msgstr "Koopbaar"

#: common/setting/system.py:442
msgid "Parts are purchaseable by default"
msgstr "Onderdelen kunnen standaard gekocht worden"

#: common/setting/system.py:447 part/models.py:1241 stock/api.py:592
msgid "Salable"
msgstr "Verkoopbaar"

#: common/setting/system.py:448
msgid "Parts are salable by default"
msgstr "Onderdelen kunnen standaard verkocht worden"

#: common/setting/system.py:454
msgid "Parts are trackable by default"
msgstr "Onderdelen kunnen standaard gevolgd worden"

#: common/setting/system.py:459 part/models.py:1257
msgid "Virtual"
msgstr "Virtueel"

#: common/setting/system.py:460
msgid "Parts are virtual by default"
msgstr "Onderdelen zijn standaard virtueel"

#: common/setting/system.py:465
msgid "Show Import in Views"
msgstr "Toon Import in Weergaven"

#: common/setting/system.py:466
msgid "Display the import wizard in some part views"
msgstr "Toon de importwizard in sommige onderdelenweergaven"

#: common/setting/system.py:471
msgid "Show related parts"
msgstr "Verwante onderdelen tonen"

#: common/setting/system.py:472
msgid "Display related parts for a part"
msgstr "Verwante onderdelen voor een onderdeel tonen"

#: common/setting/system.py:477
msgid "Initial Stock Data"
msgstr "Initiële voorraadgegevens"

#: common/setting/system.py:478
msgid "Allow creation of initial stock when adding a new part"
msgstr "Aanmaken van eerste voorraad toestaan bij het toevoegen van een nieuw onderdeel"

#: common/setting/system.py:483
msgid "Initial Supplier Data"
msgstr "Initiële leveranciergegevens"

#: common/setting/system.py:485
msgid "Allow creation of initial supplier data when adding a new part"
msgstr "Aanmaken van eerste leveranciersgegevens toestaan bij het toevoegen van een nieuw onderdeel"

#: common/setting/system.py:491
msgid "Part Name Display Format"
msgstr "Onderdelennaam Weergaveopmaak"

#: common/setting/system.py:492
msgid "Format to display the part name"
msgstr "Opmaak om de onderdeelnaam weer te geven"

#: common/setting/system.py:498
msgid "Part Category Default Icon"
msgstr "Standaardicoon voor onderdeel catagorie"

#: common/setting/system.py:499
msgid "Part category default icon (empty means no icon)"
msgstr "Standaardicoon voor onderdeel catagorie (leeg betekent geen pictogram)"

#: common/setting/system.py:504
msgid "Enforce Parameter Units"
msgstr "Forceer Parameter Eenheden"

#: common/setting/system.py:506
msgid "If units are provided, parameter values must match the specified units"
msgstr "Als er eenheden worden opgegeven, moeten parameterwaarden overeenkomen met de opgegeven eenheden"

#: common/setting/system.py:512
msgid "Minimum Pricing Decimal Places"
msgstr "Minimaal aantal prijs decimalen"

#: common/setting/system.py:514
msgid "Minimum number of decimal places to display when rendering pricing data"
msgstr "Minimaal aantal decimalen om weer te geven bij het weergeven van prijsgegevens"

#: common/setting/system.py:525
msgid "Maximum Pricing Decimal Places"
msgstr "Maximum prijs decimalen"

#: common/setting/system.py:527
msgid "Maximum number of decimal places to display when rendering pricing data"
msgstr "Maximum aantal decimalen om weer te geven bij het weergeven van prijsgegevens"

#: common/setting/system.py:538
msgid "Use Supplier Pricing"
msgstr "Gebruik leveranciersprijzen"

#: common/setting/system.py:540
msgid "Include supplier price breaks in overall pricing calculations"
msgstr "Prijsvoordelen leveranciers opnemen in de totale prijsberekening"

#: common/setting/system.py:546
msgid "Purchase History Override"
msgstr "Aankoopgeschiedenis overschrijven"

#: common/setting/system.py:548
msgid "Historical purchase order pricing overrides supplier price breaks"
msgstr "Historische order prijzen overschrijven de prijzen van de leverancier"

#: common/setting/system.py:554
msgid "Use Stock Item Pricing"
msgstr "Gebruik voorraaditem prijzen"

#: common/setting/system.py:556
msgid "Use pricing from manually entered stock data for pricing calculations"
msgstr "Gebruik prijzen van handmatig ingevoerde voorraadgegevens voor prijsberekeningen"

#: common/setting/system.py:562
msgid "Stock Item Pricing Age"
msgstr "Voorraad artikelprijs leeftijd"

#: common/setting/system.py:564
msgid "Exclude stock items older than this number of days from pricing calculations"
msgstr "Voorraaditems ouder dan dit aantal dagen uitsluiten van prijsberekeningen"

#: common/setting/system.py:571
msgid "Use Variant Pricing"
msgstr "Gebruik variantprijzen"

#: common/setting/system.py:572
msgid "Include variant pricing in overall pricing calculations"
msgstr "Variantenprijzen opnemen in de totale prijsberekening"

#: common/setting/system.py:577
msgid "Active Variants Only"
msgstr "Alleen actieve varianten"

#: common/setting/system.py:579
msgid "Only use active variant parts for calculating variant pricing"
msgstr "Gebruik alleen actieve variantonderdelen voor het berekenen van variantprijzen"

#: common/setting/system.py:585
msgid "Pricing Rebuild Interval"
msgstr "Prijzen Herbouw interval"

#: common/setting/system.py:586
msgid "Number of days before part pricing is automatically updated"
msgstr "Aantal dagen voordat de prijzen voor onderdelen automatisch worden bijgewerkt"

#: common/setting/system.py:592
msgid "Internal Prices"
msgstr "Interne Prijzen"

#: common/setting/system.py:593
msgid "Enable internal prices for parts"
msgstr "Inschakelen van interne prijzen voor onderdelen"

#: common/setting/system.py:598
msgid "Internal Price Override"
msgstr "Interne prijs overschrijven"

#: common/setting/system.py:600
msgid "If available, internal prices override price range calculations"
msgstr "Indien beschikbaar, interne prijzen overschrijven berekeningen van prijsbereik"

#: common/setting/system.py:606
msgid "Enable label printing"
msgstr "Printen van labels Inschakelen"

#: common/setting/system.py:607
msgid "Enable label printing from the web interface"
msgstr "Printen van labels via de webinterface inschakelen"

#: common/setting/system.py:612
msgid "Label Image DPI"
msgstr "Label Afbeelding DPI"

#: common/setting/system.py:614
msgid "DPI resolution when generating image files to supply to label printing plugins"
msgstr "DPI resolutie bij het genereren van afbeelginsbestanden voor label printer plugins"

#: common/setting/system.py:620
msgid "Enable Reports"
msgstr "Activeer Rapportages"

#: common/setting/system.py:621
msgid "Enable generation of reports"
msgstr "Activeer het genereren van rapporten"

#: common/setting/system.py:626
msgid "Debug Mode"
msgstr "Foutopsporingsmodus"

#: common/setting/system.py:627
msgid "Generate reports in debug mode (HTML output)"
msgstr "Rapporten genereren in debug modus (HTML uitvoer)"

#: common/setting/system.py:632
msgid "Log Report Errors"
msgstr "Log fouten"

#: common/setting/system.py:633
msgid "Log errors which occur when generating reports"
msgstr "Registreer fouten die optreden bij het genereren van rapporten"

#: common/setting/system.py:638 plugin/builtin/labels/label_sheet.py:29
#: report/models.py:365
msgid "Page Size"
msgstr "Paginagrootte"

#: common/setting/system.py:639
msgid "Default page size for PDF reports"
msgstr "Standaard paginagrootte voor PDF rapporten"

#: common/setting/system.py:644
msgid "Globally Unique Serials"
msgstr "Globaal unieke serienummers"

#: common/setting/system.py:645
msgid "Serial numbers for stock items must be globally unique"
msgstr "Serienummers voor voorraaditems moeten globaal uniek zijn"

#: common/setting/system.py:650
msgid "Autofill Serial Numbers"
msgstr "Serienummers automatisch invullen"

#: common/setting/system.py:651
msgid "Autofill serial numbers in forms"
msgstr "Automatisch invullen van serienummer in formulieren"

#: common/setting/system.py:656
msgid "Delete Depleted Stock"
msgstr "Verwijder uitgeputte voorraad"

#: common/setting/system.py:657
msgid "Determines default behavior when a stock item is depleted"
msgstr "Bepaalt standaard gedrag wanneer een voorraadartikel leeg is"

#: common/setting/system.py:662
msgid "Batch Code Template"
msgstr "Batchcode Sjabloon"

#: common/setting/system.py:663
msgid "Template for generating default batch codes for stock items"
msgstr "Sjabloon voor het genereren van standaard batchcodes voor voorraadartikelen"

#: common/setting/system.py:667
msgid "Stock Expiry"
msgstr "Verlopen Voorraad"

#: common/setting/system.py:668
msgid "Enable stock expiry functionality"
msgstr "Verlopen voorraad functionaliteit inschakelen"

#: common/setting/system.py:673
msgid "Sell Expired Stock"
msgstr "Verkoop Verlopen Voorraad"

#: common/setting/system.py:674
msgid "Allow sale of expired stock"
msgstr "Verkoop verlopen voorraad toestaan"

#: common/setting/system.py:679
msgid "Stock Stale Time"
msgstr "Voorraad Vervaltijd"

#: common/setting/system.py:681
msgid "Number of days stock items are considered stale before expiring"
msgstr "Aantal dagen voordat voorraadartikelen als verouderd worden beschouwd voor ze verlopen"

#: common/setting/system.py:688
msgid "Build Expired Stock"
msgstr "Produceer Verlopen Voorraad"

#: common/setting/system.py:689
msgid "Allow building with expired stock"
msgstr "Sta productie met verlopen voorraad toe"

#: common/setting/system.py:694
msgid "Stock Ownership Control"
msgstr "Voorraad Eigenaar Toezicht"

#: common/setting/system.py:695
msgid "Enable ownership control over stock locations and items"
msgstr "Eigenaarstoezicht over voorraadlocaties en items inschakelen"

#: common/setting/system.py:700
msgid "Stock Location Default Icon"
msgstr "Voorraadlocatie standaard icoon"

#: common/setting/system.py:701
msgid "Stock location default icon (empty means no icon)"
msgstr "Standaard locatie pictogram (leeg betekent geen icoon)"

#: common/setting/system.py:706
msgid "Show Installed Stock Items"
msgstr "Geïnstalleerde voorraad items weergeven"

#: common/setting/system.py:707
msgid "Display installed stock items in stock tables"
msgstr "Geïnstalleerde voorraadartikelen in voorraadtabellen tonen"

#: common/setting/system.py:712
msgid "Check BOM when installing items"
msgstr "Controleer BOM bij het installeren van items"

#: common/setting/system.py:714
msgid "Installed stock items must exist in the BOM for the parent part"
msgstr "Geïnstalleerde voorraad items moeten in de BOM voor het bovenliggende deel bestaan"

#: common/setting/system.py:720
msgid "Allow Out of Stock Transfer"
msgstr "Sta 'Niet op voorraad overschrijving' toe"

#: common/setting/system.py:722
msgid "Allow stock items which are not in stock to be transferred between stock locations"
msgstr "Toestaan dat voorraadartikelen die niet op voorraad zijn worden overgebracht tussen voorraadlocaties"

#: common/setting/system.py:728
msgid "Build Order Reference Pattern"
msgstr "Productieorderreferentiepatroon"

#: common/setting/system.py:729
msgid "Required pattern for generating Build Order reference field"
msgstr "Vereist patroon voor het genereren van het Bouworderreferentieveld"

#: common/setting/system.py:734 common/setting/system.py:788
#: common/setting/system.py:808 common/setting/system.py:844
msgid "Require Responsible Owner"
msgstr "Vereis verantwoordelijke eigenaar"

#: common/setting/system.py:735 common/setting/system.py:789
#: common/setting/system.py:809 common/setting/system.py:845
msgid "A responsible owner must be assigned to each order"
msgstr "Een verantwoordelijke eigenaar moet worden toegewezen aan elke bestelling"

#: common/setting/system.py:740
msgid "Require Active Part"
msgstr "Vereist een actief onderdeel"

#: common/setting/system.py:741
msgid "Prevent build order creation for inactive parts"
msgstr "Voorkom het maken van orders voor inactieve onderdelen"

#: common/setting/system.py:746
msgid "Require Locked Part"
msgstr "Vergrendeld onderdeel vereisen"

#: common/setting/system.py:747
msgid "Prevent build order creation for unlocked parts"
msgstr "Voorkom het maken van orders voor ontgrendelde onderdelen"

#: common/setting/system.py:752
msgid "Require Valid BOM"
msgstr "Vereist een geldige BOM"

#: common/setting/system.py:753
msgid "Prevent build order creation unless BOM has been validated"
msgstr "Voorkom het creëren van bouworders tenzij BOM is gevalideerd"

#: common/setting/system.py:758
msgid "Require Closed Child Orders"
msgstr "Onderliggende bestellingen vereist"

#: common/setting/system.py:760
msgid "Prevent build order completion until all child orders are closed"
msgstr "Voorkom voltooiing van de bouw tot alle sub orders gesloten zijn"

#: common/setting/system.py:766
msgid "Block Until Tests Pass"
msgstr "Blokkeren tot test geslaagd"

#: common/setting/system.py:768
msgid "Prevent build outputs from being completed until all required tests pass"
msgstr "Voorkom dat de bouw van de uitvoer wordt voltooid totdat alle vereiste testen zijn geslaagd"

#: common/setting/system.py:774
msgid "Enable Return Orders"
msgstr "Retourorders inschakelen"

#: common/setting/system.py:775
msgid "Enable return order functionality in the user interface"
msgstr "Retourorder functionaliteit inschakelen in de gebruikersinterface"

#: common/setting/system.py:780
msgid "Return Order Reference Pattern"
msgstr "Retourorder referentie patroon"

#: common/setting/system.py:782
msgid "Required pattern for generating Return Order reference field"
msgstr "Verplicht patroon voor het genereren van Retourorder referentie veld"

#: common/setting/system.py:794
msgid "Edit Completed Return Orders"
msgstr "Bewerk voltooide retourorders"

#: common/setting/system.py:796
msgid "Allow editing of return orders after they have been completed"
msgstr "Bewerken van retourorders toestaan nadat deze zijn voltooid"

#: common/setting/system.py:802
msgid "Sales Order Reference Pattern"
msgstr "Verkooporderreferentiepatroon"

#: common/setting/system.py:803
msgid "Required pattern for generating Sales Order reference field"
msgstr "Vereist patroon voor het genereren van het Verkooporderreferentieveld"

#: common/setting/system.py:814
msgid "Sales Order Default Shipment"
msgstr "Standaard Verzending Verkooporder"

#: common/setting/system.py:815
msgid "Enable creation of default shipment with sales orders"
msgstr "Aanmaken standaard verzending bij verkooporders inschakelen"

#: common/setting/system.py:820
msgid "Edit Completed Sales Orders"
msgstr "Bewerk voltooide verkooporders"

#: common/setting/system.py:822
msgid "Allow editing of sales orders after they have been shipped or completed"
msgstr "Bewerken van verkooporders toestaan nadat deze zijn verzonden of voltooid"

#: common/setting/system.py:828
msgid "Mark Shipped Orders as Complete"
msgstr "Verstuurde bestellingen markeren als voltooid"

#: common/setting/system.py:830
msgid "Sales orders marked as shipped will automatically be completed, bypassing the \"shipped\" status"
msgstr "Verkooporders gemarkeerd als verzonden zullen automatisch worden voltooid, zonder de status \"verzonden\""

#: common/setting/system.py:836
msgid "Purchase Order Reference Pattern"
msgstr "Inkooporderreferentiepatroon"

#: common/setting/system.py:838
msgid "Required pattern for generating Purchase Order reference field"
msgstr "Vereist patroon voor het genereren van het Inkooporderreferentieveld"

#: common/setting/system.py:850
msgid "Edit Completed Purchase Orders"
msgstr "Bewerk voltooide verkooporders"

#: common/setting/system.py:852
msgid "Allow editing of purchase orders after they have been shipped or completed"
msgstr "Bewerken van inkooporders toestaan nadat deze zijn verzonden of voltooid"

#: common/setting/system.py:858
msgid "Convert Currency"
msgstr "Valuta converteren"

#: common/setting/system.py:859
msgid "Convert item value to base currency when receiving stock"
msgstr "Verander artikelwaarde naar basisvaluta bij het ontvangen van voorraad"

#: common/setting/system.py:864
msgid "Auto Complete Purchase Orders"
msgstr "Inkooporders automatisch voltooien"

#: common/setting/system.py:866
msgid "Automatically mark purchase orders as complete when all line items are received"
msgstr "Markeer orders automatisch als voltooid wanneer alle regelitems worden ontvangen"

#: common/setting/system.py:873
msgid "Enable password forgot"
msgstr "Wachtwoord vergeten functie inschakelen"

#: common/setting/system.py:874
msgid "Enable password forgot function on the login pages"
msgstr "Wachtwoord vergeten functie inschakelen op de inlogpagina's"

#: common/setting/system.py:879
msgid "Enable registration"
msgstr "Registratie inschakelen"

#: common/setting/system.py:880
msgid "Enable self-registration for users on the login pages"
msgstr "Zelfregistratie voor gebruikers op de inlogpagina's inschakelen"

#: common/setting/system.py:885
msgid "Enable SSO"
msgstr "SSO inschakelen"

#: common/setting/system.py:886
msgid "Enable SSO on the login pages"
msgstr "SSO inschakelen op de inlogpagina's"

#: common/setting/system.py:891
msgid "Enable SSO registration"
msgstr "Schakel gebruikersregistratie met SSO in"

#: common/setting/system.py:893
msgid "Enable self-registration via SSO for users on the login pages"
msgstr "Zelfregistratie voor gebruikers middels SSO op de inlogpagina's inschakelen"

#: common/setting/system.py:899
msgid "Enable SSO group sync"
msgstr "SSO-groep synchroniseren inschakelen"

#: common/setting/system.py:901
msgid "Enable synchronizing InvenTree groups with groups provided by the IdP"
msgstr "Inschakelen van het synchroniseren van InvenTree groepen met groepen geboden door de IdP"

#: common/setting/system.py:907
msgid "SSO group key"
msgstr "SSO groep sleutel"

#: common/setting/system.py:908
msgid "The name of the groups claim attribute provided by the IdP"
msgstr "De naam van de groepen claim attribuut van de IdP"

#: common/setting/system.py:913
msgid "SSO group map"
msgstr "SSO groep kaart"

#: common/setting/system.py:915
msgid "A mapping from SSO groups to local InvenTree groups. If the local group does not exist, it will be created."
msgstr "Een mapping van SSO-groepen naar lokale InvenTree groepen. Als de lokale groep niet bestaat, zal deze worden aangemaakt."

#: common/setting/system.py:921
msgid "Remove groups outside of SSO"
msgstr "Verwijder groepen buiten SSO"

#: common/setting/system.py:923
msgid "Whether groups assigned to the user should be removed if they are not backend by the IdP. Disabling this setting might cause security issues"
msgstr "Of groepen die zijn toegewezen aan de gebruiker moeten worden verwijderd als ze geen backend zijn door de IdP. Het uitschakelen van deze instelling kan beveiligingsproblemen veroorzaken"

#: common/setting/system.py:929
msgid "Email required"
msgstr "E-mailadres verplicht"

#: common/setting/system.py:930
msgid "Require user to supply mail on signup"
msgstr "Vereis gebruiker om e-mailadres te registreren bij aanmelding"

#: common/setting/system.py:935
msgid "Auto-fill SSO users"
msgstr "SSO-gebruikers automatisch invullen"

#: common/setting/system.py:936
msgid "Automatically fill out user-details from SSO account-data"
msgstr "Gebruikersdetails van SSO-accountgegevens automatisch invullen"

#: common/setting/system.py:941
msgid "Mail twice"
msgstr "E-mail twee keer"

#: common/setting/system.py:942
msgid "On signup ask users twice for their mail"
msgstr "Bij inschrijving gebruikers twee keer om hun e-mail vragen"

#: common/setting/system.py:947
msgid "Password twice"
msgstr "Wachtwoord tweemaal"

#: common/setting/system.py:948
msgid "On signup ask users twice for their password"
msgstr "Laat gebruikers twee keer om hun wachtwoord vragen tijdens het aanmelden"

#: common/setting/system.py:953
msgid "Allowed domains"
msgstr "Toegestane domeinen"

#: common/setting/system.py:955
msgid "Restrict signup to certain domains (comma-separated, starting with @)"
msgstr "Inschrijven beperken tot bepaalde domeinen (komma-gescheiden, beginnend met @)"

#: common/setting/system.py:961
msgid "Group on signup"
msgstr "Groep bij aanmelding"

#: common/setting/system.py:963
msgid "Group to which new users are assigned on registration. If SSO group sync is enabled, this group is only set if no group can be assigned from the IdP."
msgstr "Groep waaraan nieuwe gebruikers zijn toegewezen op registratie. Als SSO-groepssynchronisatie is ingeschakeld, is deze groep alleen ingesteld als er geen groep vanuit de IdP kan worden toegewezen."

#: common/setting/system.py:969
msgid "Enforce MFA"
msgstr "MFA afdwingen"

#: common/setting/system.py:970
msgid "Users must use multifactor security."
msgstr "Gebruikers moeten multifactor-beveiliging gebruiken."

#: common/setting/system.py:975
msgid "Check plugins on startup"
msgstr "Controleer plugins bij het opstarten"

#: common/setting/system.py:977
msgid "Check that all plugins are installed on startup - enable in container environments"
msgstr "Controleer of alle plug-ins zijn geïnstalleerd bij het opstarten - inschakelen in container-omgevingen"

#: common/setting/system.py:984
msgid "Check for plugin updates"
msgstr "Controleren op plug-in updates"

#: common/setting/system.py:985
msgid "Enable periodic checks for updates to installed plugins"
msgstr "Schakel periodieke controles voor updates voor geïnstalleerde plug-ins in"

#: common/setting/system.py:991
msgid "Enable URL integration"
msgstr "Activeer URL-integratie"

#: common/setting/system.py:992
msgid "Enable plugins to add URL routes"
msgstr "Plugins toestaan om URL-routes toe te voegen"

#: common/setting/system.py:998
msgid "Enable navigation integration"
msgstr "Activeer navigatie integratie"

#: common/setting/system.py:999
msgid "Enable plugins to integrate into navigation"
msgstr "Plugins toestaan om te integreren in navigatie"

#: common/setting/system.py:1005
msgid "Enable app integration"
msgstr "Activeer app integratie"

#: common/setting/system.py:1006
msgid "Enable plugins to add apps"
msgstr "Activeer plug-ins om apps toe te voegen"

#: common/setting/system.py:1012
msgid "Enable schedule integration"
msgstr "Activeer planning integratie"

#: common/setting/system.py:1013
msgid "Enable plugins to run scheduled tasks"
msgstr "Activeer plugin om periodiek taken uit te voeren"

#: common/setting/system.py:1019
msgid "Enable event integration"
msgstr "Activeer evenement integratie"

#: common/setting/system.py:1020
msgid "Enable plugins to respond to internal events"
msgstr "Activeer plugin om op interne evenementen te reageren"

#: common/setting/system.py:1026
msgid "Enable interface integration"
msgstr "Interface integratie activeren"

#: common/setting/system.py:1027
msgid "Enable plugins to integrate into the user interface"
msgstr "Plug-ins inschakelen om te integreren in de gebruikersinterface"

#: common/setting/system.py:1033
msgid "Enable project codes"
msgstr "Activeer project codes"

#: common/setting/system.py:1034
msgid "Enable project codes for tracking projects"
msgstr "Schakel projectcodes in voor het bijhouden van projecten"

#: common/setting/system.py:1039
msgid "Stocktake Functionality"
msgstr "Voorraadcontrole functionaliteit"

#: common/setting/system.py:1041
msgid "Enable stocktake functionality for recording stock levels and calculating stock value"
msgstr "Schakel voorraadfunctionaliteit in voor het opnemen van voorraadniveaus en het berekenen van voorraadwaarde"

#: common/setting/system.py:1047
msgid "Exclude External Locations"
msgstr "Externe locaties uitsluiten"

#: common/setting/system.py:1049
msgid "Exclude stock items in external locations from stocktake calculations"
msgstr "Voorraadartikelen op externe locaties uitsluiten van voorraadberekeningen"

#: common/setting/system.py:1055
msgid "Automatic Stocktake Period"
msgstr "Automatische Voorraadcontrole Periode"

#: common/setting/system.py:1057
msgid "Number of days between automatic stocktake recording (set to zero to disable)"
msgstr "Aantal dagen tussen automatische voorraadopname (ingesteld op nul om uit te schakelen)"

#: common/setting/system.py:1063
msgid "Report Deletion Interval"
msgstr "Rapport Verwijdering Interval"

#: common/setting/system.py:1065
msgid "Stocktake reports will be deleted after specified number of days"
msgstr "Voorraadrapportage zal worden verwijderd na het opgegeven aantal dagen"

#: common/setting/system.py:1072
msgid "Display Users full names"
msgstr "Gebruikers volledige namen weergeven"

#: common/setting/system.py:1073
msgid "Display Users full names instead of usernames"
msgstr "Laat gebruikers volledige namen zien in plaats van gebruikersnamen"

#: common/setting/system.py:1078
msgid "Display User Profiles"
msgstr "Gebruikersprofielen tonen"

#: common/setting/system.py:1079
msgid "Display Users Profiles on their profile page"
msgstr "Toon gebruikersprofielen op hun profielpagina"

#: common/setting/system.py:1084
msgid "Enable Test Station Data"
msgstr "Inschakelen van teststation data"

#: common/setting/system.py:1085
msgid "Enable test station data collection for test results"
msgstr "Schakel teststation gegevensverzameling in voor testresultaten"

#: common/setting/system.py:1090
msgid "Create Template on Upload"
msgstr "Maak template aan bij het uploaden"

#: common/setting/system.py:1092
msgid "Create a new test template when uploading test data which does not match an existing template"
msgstr "Maak een nieuw testsjabloon bij het uploaden van testgegevens die niet overeenkomen met een bestaande sjabloon"

#: common/setting/user.py:23
msgid "Inline label display"
msgstr "Inline labelweergave"

#: common/setting/user.py:25
msgid "Display PDF labels in the browser, instead of downloading as a file"
msgstr "PDF-labels in browser weergeven, in plaats van als bestand te downloaden"

#: common/setting/user.py:31
msgid "Default label printer"
msgstr "Standaard label printer"

#: common/setting/user.py:32
msgid "Configure which label printer should be selected by default"
msgstr "Instellen welke label printer standaard moet worden geselecteerd"

#: common/setting/user.py:37
msgid "Inline report display"
msgstr "Inline rapport weergeven"

#: common/setting/user.py:39
msgid "Display PDF reports in the browser, instead of downloading as a file"
msgstr "PDF-rapporten in de browser weergeven, in plaats van als bestand te downloaden"

#: common/setting/user.py:45
msgid "Search Parts"
msgstr "Zoek Onderdelen"

#: common/setting/user.py:46
msgid "Display parts in search preview window"
msgstr "Onderdelen weergeven in zoekscherm"

#: common/setting/user.py:51
msgid "Search Supplier Parts"
msgstr "Zoek leveranciersonderdelen"

#: common/setting/user.py:52
msgid "Display supplier parts in search preview window"
msgstr "Leveranciersonderdelen weergeven in zoekscherm"

#: common/setting/user.py:57
msgid "Search Manufacturer Parts"
msgstr "Fabrikant onderdelen zoeken"

#: common/setting/user.py:58
msgid "Display manufacturer parts in search preview window"
msgstr "Fabrikant onderdelen weergeven in zoekscherm"

#: common/setting/user.py:63
msgid "Hide Inactive Parts"
msgstr "Inactieve Onderdelen Verbergen"

#: common/setting/user.py:64
msgid "Excluded inactive parts from search preview window"
msgstr "Inactieve verkooporders weglaten in het zoekvenster"

#: common/setting/user.py:69
msgid "Search Categories"
msgstr "Zoek categorieën"

#: common/setting/user.py:70
msgid "Display part categories in search preview window"
msgstr "Toon onderdeelcategorieën in zoekvenster"

#: common/setting/user.py:75
msgid "Search Stock"
msgstr "Zoek in Voorraad"

#: common/setting/user.py:76
msgid "Display stock items in search preview window"
msgstr "Toon voorraad items in zoekvenster"

#: common/setting/user.py:81
msgid "Hide Unavailable Stock Items"
msgstr "Verberg niet beschikbare voorraad items"

#: common/setting/user.py:83
msgid "Exclude stock items which are not available from the search preview window"
msgstr "Voorraadartikelen die niet beschikbaar zijn niet in het zoekvenster weergeven"

#: common/setting/user.py:89
msgid "Search Locations"
msgstr "Locaties doorzoeken"

#: common/setting/user.py:90
msgid "Display stock locations in search preview window"
msgstr "Toon voorraadlocaties in zoekvenster"

#: common/setting/user.py:95
msgid "Search Companies"
msgstr "Zoek bedrijven"

#: common/setting/user.py:96
msgid "Display companies in search preview window"
msgstr "Toon bedrijven in zoekvenster"

#: common/setting/user.py:101
msgid "Search Build Orders"
msgstr "Zoek Bouworders"

#: common/setting/user.py:102
msgid "Display build orders in search preview window"
msgstr "Toon bouworders in zoekvenster"

#: common/setting/user.py:107
msgid "Search Purchase Orders"
msgstr "Inkooporders Zoeken"

#: common/setting/user.py:108
msgid "Display purchase orders in search preview window"
msgstr "Toon inkooporders in het zoekvenster"

#: common/setting/user.py:113
msgid "Exclude Inactive Purchase Orders"
msgstr "Inactieve Inkooporders Weglaten"

#: common/setting/user.py:114
msgid "Exclude inactive purchase orders from search preview window"
msgstr "Inactieve inkooporders weglaten in het zoekvenster"

#: common/setting/user.py:119
msgid "Search Sales Orders"
msgstr "Verkooporders zoeken"

#: common/setting/user.py:120
msgid "Display sales orders in search preview window"
msgstr "Toon verkooporders in het zoekvenster"

#: common/setting/user.py:125
msgid "Exclude Inactive Sales Orders"
msgstr "Inactieve Verkooporders Weglaten"

#: common/setting/user.py:126
msgid "Exclude inactive sales orders from search preview window"
msgstr "Inactieve verkooporders weglaten in het zoekvenster"

#: common/setting/user.py:131
msgid "Search Sales Order Shipments"
msgstr "Verzendingen verkooporder doorzoeken"

#: common/setting/user.py:132
msgid "Display sales order shipments in search preview window"
msgstr "Toon verkooporders in het zoekvenster"

#: common/setting/user.py:137
msgid "Search Return Orders"
msgstr "Zoek retourorders"

#: common/setting/user.py:138
msgid "Display return orders in search preview window"
msgstr "Toon bouworders in zoekvenster"

#: common/setting/user.py:143
msgid "Exclude Inactive Return Orders"
msgstr "Inactieve retourbestellingen weglaten"

#: common/setting/user.py:144
msgid "Exclude inactive return orders from search preview window"
msgstr "Inactieve retourorders uitsluiten in zoekvenster"

#: common/setting/user.py:149
msgid "Search Preview Results"
msgstr "Zoekvoorbeeld resultaten"

#: common/setting/user.py:151
msgid "Number of results to show in each section of the search preview window"
msgstr "Aantal resultaten om weer te geven in elk gedeelte van het zoekvenster"

#: common/setting/user.py:157
msgid "Regex Search"
msgstr "Regex zoeken"

#: common/setting/user.py:158
msgid "Enable regular expressions in search queries"
msgstr "Schakel reguliere expressies in zoekopdrachten in"

#: common/setting/user.py:163
msgid "Whole Word Search"
msgstr "Hele woorden zoeken"

#: common/setting/user.py:164
msgid "Search queries return results for whole word matches"
msgstr "Zoekopdrachten geven resultaat voor hele woord overeenkomsten"

#: common/setting/user.py:169
msgid "Search Notes"
msgstr "Zoek notities"

#: common/setting/user.py:171
msgid "Search queries return results for matches from the item's notes"
msgstr "Zoekopdrachten geven resultaten voor overeenkomsten met artikel notities"

#: common/setting/user.py:177
msgid "Show Quantity in Forms"
msgstr "Toon hoeveelheid in formulieren"

#: common/setting/user.py:178
msgid "Display available part quantity in some forms"
msgstr "Hoeveelheid beschikbare onderdelen in sommige formulieren weergeven"

#: common/setting/user.py:183
msgid "Escape Key Closes Forms"
msgstr "Escape-toets sluit formulieren"

#: common/setting/user.py:184
msgid "Use the escape key to close modal forms"
msgstr "Gebruik de Escape-toets om standaard formulieren te sluiten"

#: common/setting/user.py:189
msgid "Fixed Navbar"
msgstr "Vaste navigatiebalk"

#: common/setting/user.py:190
msgid "The navbar position is fixed to the top of the screen"
msgstr "De navigatiebalk positie is gefixeerd aan de bovenkant van het scherm"

#: common/setting/user.py:195
msgid "Navigation Icons"
msgstr "Navigatiepictogrammen"

#: common/setting/user.py:196
msgid "Display icons in the navigation bar"
msgstr "Pictogrammen weergeven in de navigatiebalk"

#: common/setting/user.py:201
msgid "Date Format"
msgstr "Datum formaat"

#: common/setting/user.py:202
msgid "Preferred format for displaying dates"
msgstr "Voorkeursindeling voor weergave van datums"

#: common/setting/user.py:215
msgid "Part Scheduling"
msgstr "Onderdeel planning"

#: common/setting/user.py:216
msgid "Display part scheduling information"
msgstr "Toon informatie voor het plannen van onderdelen"

#: common/setting/user.py:221
msgid "Part Stocktake"
msgstr "Voorraadcontrole onderdeel"

#: common/setting/user.py:223
msgid "Display part stocktake information (if stocktake functionality is enabled)"
msgstr "Toon voorraadinformatie van onderdeel (als voorraadcontrole functionaliteit is ingeschakeld)"

#: common/setting/user.py:229
msgid "Table String Length"
msgstr "Tabel tekenreekslengte"

#: common/setting/user.py:230
msgid "Maximum length limit for strings displayed in table views"
msgstr "Maximale lengte voor tekenreeksen weergegeven in tabelweergaven"

#: common/setting/user.py:235
msgid "Show Last Breadcrumb"
msgstr "Toon laatste broodkruimel"

#: common/setting/user.py:236
msgid "Show the current page in breadcrumbs"
msgstr "Toon de huidige pagina in het kruimelpad"

#: common/setting/user.py:241
msgid "Receive error reports"
msgstr "Foutrapportages ontvangen"

#: common/setting/user.py:242
msgid "Receive notifications for system errors"
msgstr "Meldingen ontvangen van systeemfouten"

#: common/setting/user.py:247
msgid "Last used printing machines"
msgstr "Laatst gebruikte printer"

#: common/setting/user.py:248
msgid "Save the last used printing machines for a user"
msgstr "Sla de laatst gebruikte printer op voor een gebruiker"

#: common/validators.py:35
msgid "No attachment model type provided"
msgstr "Geen bijlage model type opgegeven"

#: common/validators.py:41
msgid "Invalid attachment model type"
msgstr "Ongeldig bijlage type"

#: common/validators.py:82
msgid "Minimum places cannot be greater than maximum places"
msgstr "Minimale plaatsen mogen niet groter zijn dan het maximum"

#: common/validators.py:94
msgid "Maximum places cannot be less than minimum places"
msgstr "Maximum aantal plaatsen kan niet minder zijn dan minimaal"

#: common/validators.py:105
msgid "An empty domain is not allowed."
msgstr "Een leeg domein is niet toegestaan."

#: common/validators.py:107
#, python-brace-format
msgid "Invalid domain name: {domain}"
msgstr "Ongeldige domeinnaam: {domain}"

#: common/validators.py:123
msgid "Value must be uppercase"
msgstr "De waarde moet hoofdletters zijn"

#: common/validators.py:129
msgid "Value must be a valid variable identifier"
msgstr "Waarde moet een geldige variabele id zijn"

#: company/api.py:141
msgid "Part is Active"
msgstr "Onderdeel is actief"

#: company/api.py:145
msgid "Manufacturer is Active"
msgstr "Fabrikant is actief"

#: company/api.py:278
msgid "Supplier Part is Active"
msgstr "Leveranciersonderdelen is actief"

#: company/api.py:282
msgid "Internal Part is Active"
msgstr "Intern onderdeel is actief"

#: company/api.py:287
msgid "Supplier is Active"
msgstr "Leverancier is actief"

#: company/api.py:299 company/models.py:498 company/serializers.py:444
#: part/serializers.py:577
msgid "Manufacturer"
msgstr "Fabrikant"

#: company/api.py:306 company/models.py:97 company/models.py:370
#: stock/api.py:849
msgid "Company"
msgstr "Bedrijf"

#: company/api.py:316
msgid "Has Stock"
msgstr "Heeft voorraad"

#: company/models.py:98
msgid "Companies"
msgstr "Bedrijven"

#: company/models.py:114
msgid "Company description"
msgstr "Bedrijf omschrijving"

#: company/models.py:115
msgid "Description of the company"
msgstr "Omschrijving van het bedrijf"

#: company/models.py:121
msgid "Website"
msgstr "Website"

#: company/models.py:122
msgid "Company website URL"
msgstr "URL bedrijfswebsite"

#: company/models.py:128
msgid "Phone number"
msgstr "Telefoonnummer"

#: company/models.py:130
msgid "Contact phone number"
msgstr "Telefoonnummer voor contact"

#: company/models.py:137
msgid "Contact email address"
msgstr "Contact e-mailadres"

#: company/models.py:142 company/models.py:274 order/models.py:500
#: users/models.py:563
msgid "Contact"
msgstr "Contact"

#: company/models.py:144
msgid "Point of contact"
msgstr "Contactpunt"

#: company/models.py:150
msgid "Link to external company information"
msgstr "Link naar externe bedrijfsinformatie"

#: company/models.py:164
msgid "Is this company active?"
msgstr "Is dit bedrijf actief?"

#: company/models.py:169
msgid "Is customer"
msgstr "Is klant"

#: company/models.py:170
msgid "Do you sell items to this company?"
msgstr "Verkoop je artikelen aan dit bedrijf?"

#: company/models.py:175
msgid "Is supplier"
msgstr "Is leverancier"

#: company/models.py:176
msgid "Do you purchase items from this company?"
msgstr "Koop je artikelen van dit bedrijf?"

#: company/models.py:181
msgid "Is manufacturer"
msgstr "Is fabrikant"

#: company/models.py:182
msgid "Does this company manufacture parts?"
msgstr "Fabriceert dit bedrijf onderdelen?"

#: company/models.py:190
msgid "Default currency used for this company"
msgstr "Standaardvaluta die gebruikt wordt voor dit bedrijf"

#: company/models.py:313 order/models.py:510
msgid "Address"
msgstr "Adres"

#: company/models.py:314
msgid "Addresses"
msgstr "Adres"

#: company/models.py:371
msgid "Select company"
msgstr "Selecteer bedrijf"

#: company/models.py:376
msgid "Address title"
msgstr "Adres titel"

#: company/models.py:377
msgid "Title describing the address entry"
msgstr "Titel die het adres beschrijft"

#: company/models.py:383
msgid "Primary address"
msgstr "Primair adres"

#: company/models.py:384
msgid "Set as primary address"
msgstr "Instellen als primair adres"

#: company/models.py:389
msgid "Line 1"
msgstr "Lijn 1"

#: company/models.py:390
msgid "Address line 1"
msgstr "Adresregel 1"

#: company/models.py:396
msgid "Line 2"
msgstr "Lijn 2"

#: company/models.py:397
msgid "Address line 2"
msgstr "Adresregel 2"

#: company/models.py:403 company/models.py:404
msgid "Postal code"
msgstr "Post code"

#: company/models.py:410
msgid "City/Region"
msgstr "Plaats/regio"

#: company/models.py:411
msgid "Postal code city/region"
msgstr "Postcode plaats/regio"

#: company/models.py:417
msgid "State/Province"
msgstr "Staat/provincie"

#: company/models.py:418
msgid "State or province"
msgstr "Staat of provincie"

#: company/models.py:424
msgid "Country"
msgstr "Land"

#: company/models.py:425
msgid "Address country"
msgstr "Adres land"

#: company/models.py:431
msgid "Courier shipping notes"
msgstr "Koerier verzend notities"

#: company/models.py:432
msgid "Notes for shipping courier"
msgstr "Opmerkingen voor verzending koerier"

#: company/models.py:438
msgid "Internal shipping notes"
msgstr "Interne verzend notities"

#: company/models.py:439
msgid "Shipping notes for internal use"
msgstr "Verzend notities voor intern gebruik"

#: company/models.py:446
msgid "Link to address information (external)"
msgstr "Link naar adres gegevens (extern)"

#: company/models.py:470 company/models.py:587 company/models.py:809
#: company/serializers.py:458
msgid "Manufacturer Part"
msgstr "Fabrikant onderdeel"

#: company/models.py:487 company/models.py:777 stock/models.py:948
#: stock/serializers.py:480
msgid "Base Part"
msgstr "Basis onderdeel"

#: company/models.py:489 company/models.py:779
msgid "Select part"
msgstr "Onderdeel selecteren"

#: company/models.py:499
msgid "Select manufacturer"
msgstr "Fabrikant selecteren"

#: company/models.py:505 company/serializers.py:466 order/serializers.py:665
#: part/serializers.py:587
msgid "MPN"
msgstr "Fabrikant artikel nummer"

#: company/models.py:506 stock/serializers.py:625
msgid "Manufacturer Part Number"
msgstr "Fabrikant artikel nummer (MPN)"

#: company/models.py:513
msgid "URL for external manufacturer part link"
msgstr "URL voor externe link van het fabrikant onderdeel"

#: company/models.py:522
msgid "Manufacturer part description"
msgstr "Omschrijving onderdeel fabrikant"

#: company/models.py:575
msgid "Manufacturer Part Parameter"
msgstr "Fabrikant onderdeel parameter"

#: company/models.py:594
msgid "Parameter name"
msgstr "Parameternaam"

#: company/models.py:601
msgid "Parameter value"
msgstr "Parameterwaarde"

#: company/models.py:608 part/models.py:1204 part/models.py:3844
msgid "Units"
msgstr "Eenheden"

#: company/models.py:609
msgid "Parameter units"
msgstr "Parameter eenheden"

#: company/models.py:717
msgid "Pack units must be compatible with the base part units"
msgstr "Pakket eenheden moeten compatibel zijn met de basis onderdeel eenheden"

#: company/models.py:724
msgid "Pack units must be greater than zero"
msgstr "Hoeveelheid moet groter zijn dan nul"

#: company/models.py:738
msgid "Linked manufacturer part must reference the same base part"
msgstr "Gekoppeld fabrikant onderdeel moet verwijzen naar hetzelfde basis onderdeel"

#: company/models.py:787 company/serializers.py:436 company/serializers.py:454
#: order/models.py:626 part/serializers.py:561
#: plugin/builtin/suppliers/digikey.py:25 plugin/builtin/suppliers/lcsc.py:26
#: plugin/builtin/suppliers/mouser.py:24 plugin/builtin/suppliers/tme.py:26
#: stock/api.py:516 templates/email/overdue_purchase_order.html:16
msgid "Supplier"
msgstr "Leverancier"

#: company/models.py:788
msgid "Select supplier"
msgstr "Leverancier selecteren"

#: company/models.py:794 part/serializers.py:572
msgid "Supplier stock keeping unit"
msgstr "Voorraad beheers eenheid voor leveranciers"

#: company/models.py:800
msgid "Is this supplier part active?"
msgstr "Is dit leveranciersdeel actief?"

#: company/models.py:810
msgid "Select manufacturer part"
msgstr "Selecteer fabrikant onderdeel"

#: company/models.py:817
msgid "URL for external supplier part link"
msgstr "URL voor link externe leveranciers onderdeel"

#: company/models.py:826
msgid "Supplier part description"
msgstr "Omschrijving leveranciersdeel"

#: company/models.py:833 order/serializers.py:819 order/serializers.py:2027
#: part/models.py:4356 part/models.py:4697
#: report/templates/report/inventree_bill_of_materials_report.html:140
#: report/templates/report/inventree_purchase_order_report.html:32
#: report/templates/report/inventree_return_order_report.html:27
#: report/templates/report/inventree_sales_order_report.html:32
#: report/templates/report/inventree_stock_location_report.html:105
#: stock/serializers.py:838
msgid "Note"
msgstr "Opmerking"

#: company/models.py:842 part/models.py:2182
msgid "base cost"
msgstr "basisprijs"

#: company/models.py:843 part/models.py:2183
msgid "Minimum charge (e.g. stocking fee)"
msgstr "Minimale kosten (bijv. voorraadkosten)"

#: company/models.py:850 order/serializers.py:811 stock/models.py:979
#: stock/serializers.py:1666
msgid "Packaging"
msgstr "Verpakking"

#: company/models.py:851
msgid "Part packaging"
msgstr "Onderdeel verpakking"

#: company/models.py:856
msgid "Pack Quantity"
msgstr "Pakket hoeveelheid"

#: company/models.py:858
msgid "Total quantity supplied in a single pack. Leave empty for single items."
msgstr "Totale hoeveelheid geleverd in één pakket. Laat leeg voor enkele afzonderlijke items."

#: company/models.py:877 part/models.py:2189
msgid "multiple"
msgstr "meerdere"

#: company/models.py:878
msgid "Order multiple"
msgstr "Order meerdere"

#: company/models.py:890
msgid "Quantity available from supplier"
msgstr "Beschikbare hoeveelheid van leverancier"

#: company/models.py:896
msgid "Availability Updated"
msgstr "Beschikbaarheid bijgewerkt"

#: company/models.py:897
msgid "Date of last update of availability data"
msgstr "Datum van de laatste update van de beschikbaarheid gegevens"

#: company/models.py:1025
msgid "Supplier Price Break"
msgstr "Prijsverschil van leverancier"

#: company/serializers.py:166
msgid "Return the string representation for the primary address. This property exists for backwards compatibility."
msgstr "Geeft als resultaat de string representatie voor het primaire adres. Deze eigenschap bestaat voor achterwaartse compatibiliteit."

#: company/serializers.py:185
msgid "Default currency used for this supplier"
msgstr "Standaardvaluta die gebruikt wordt voor deze leverancier"

#: company/serializers.py:221
msgid "Company Name"
msgstr "Bedrijfsnaam"

#: company/serializers.py:420 part/serializers.py:940 stock/serializers.py:498
msgid "In Stock"
msgstr "Op voorraad"

#: data_exporter/mixins.py:324 data_exporter/mixins.py:393
msgid "Error occurred during data export"
msgstr "Fout opgetreden tijdens data export"

#: data_exporter/mixins.py:375
msgid "Data export plugin returned incorrect data format"
msgstr "Gegevensexport plug-in geeft onjuiste gegevensindeling terug"

#: data_exporter/serializers.py:74
msgid "Export Format"
msgstr "Exporteer formaat"

#: data_exporter/serializers.py:75
msgid "Select export file format"
msgstr "Selecteer export bestandsindeling"

#: data_exporter/serializers.py:82
msgid "Export Plugin"
msgstr "Plug-in exporteren"

#: data_exporter/serializers.py:83
msgid "Select export plugin"
msgstr "Export plug-in selecteren"

#: generic/states/fields.py:146
msgid "Additional status information for this item"
msgstr "Additionele statusinformatie voor dit item"

#: generic/states/fields.py:160
msgid "Custom status key"
msgstr "Aangepaste status sleutel"

#: generic/states/serializers.py:16 plugin/models.py:45 users/models.py:119
msgid "Key"
msgstr "Sleutel"

#: generic/states/serializers.py:26
msgid "Custom"
msgstr "Aangepast"

#: generic/states/serializers.py:37
msgid "Class"
msgstr "Klasse"

#: generic/states/serializers.py:40
msgid "Values"
msgstr "Waardes"

#: generic/states/tests.py:22 order/status_codes.py:13
msgid "Placed"
msgstr "Geplaatst"

#: generic/states/validators.py:21
msgid "Invalid status code"
msgstr "Ongeldige statuscode"

#: importer/models.py:70
msgid "Data File"
msgstr "Data bestand"

#: importer/models.py:71
msgid "Data file to import"
msgstr "Te importeren databestand"

#: importer/models.py:80
msgid "Columns"
msgstr "Kolommen"

#: importer/models.py:87
msgid "Target model type for this import session"
msgstr "Doel modeltype voor deze importsessie"

#: importer/models.py:93
msgid "Import status"
msgstr "Status van importeren"

#: importer/models.py:103
msgid "Field Defaults"
msgstr "Veld standaard"

#: importer/models.py:110
msgid "Field Overrides"
msgstr "Veld overschrijven"

#: importer/models.py:117
msgid "Field Filters"
msgstr "Veld filters"

#: importer/models.py:250
msgid "Some required fields have not been mapped"
msgstr "Sommige verplichte velden zijn niet toegewezen"

#: importer/models.py:407
msgid "Column is already mapped to a database field"
msgstr "De kolom is al toegewezen aan een database veld"

#: importer/models.py:412
msgid "Field is already mapped to a data column"
msgstr "Het veld is al toegewezen aan een data-kolom"

#: importer/models.py:421
msgid "Column mapping must be linked to a valid import session"
msgstr "Kolom toewijzing moet worden gekoppeld aan een geldige importsessie"

#: importer/models.py:426
msgid "Column does not exist in the data file"
msgstr "Kolom bestaat niet in het gegevensbestand"

#: importer/models.py:433
msgid "Field does not exist in the target model"
msgstr "Veld bestaat niet in het doel model"

#: importer/models.py:437
msgid "Selected field is read-only"
msgstr "Geselecteerde veld is alleen lezen"

#: importer/models.py:442 importer/models.py:513
msgid "Import Session"
msgstr "Importeer sessie"

#: importer/models.py:446
msgid "Field"
msgstr "Veld"

#: importer/models.py:448
msgid "Column"
msgstr "Kolom"

#: importer/models.py:517
msgid "Row Index"
msgstr "Rij index"

#: importer/models.py:520
msgid "Original row data"
msgstr "Oorspronkelijke rij gegevens"

#: importer/models.py:525 machine/models.py:110
msgid "Errors"
msgstr "Fouten"

#: importer/models.py:527 part/api.py:864
msgid "Valid"
msgstr "Geldig"

#: importer/operations.py:28 importer/operations.py:49
msgid "Unsupported data file format"
msgstr "Niet ondersteunde gegevens bestandsindeling"

#: importer/operations.py:40
msgid "Failed to open data file"
msgstr "Kan het databestand niet openen"

#: importer/operations.py:51
msgid "Invalid data file dimensions"
msgstr "Ongeldige afmetingen van gegevensbestand"

#: importer/serializers.py:91
msgid "Invalid field defaults"
msgstr "Ongeldig veld standaard"

#: importer/serializers.py:104
msgid "Invalid field overrides"
msgstr "Ongeldige veld overschrijvingen"

#: importer/serializers.py:117
msgid "Invalid field filters"
msgstr "Ongeldige veld filters"

#: importer/serializers.py:178
msgid "Rows"
msgstr "Rijen"

#: importer/serializers.py:179
msgid "List of row IDs to accept"
msgstr "Lijst van te accepteren rij IDs"

#: importer/serializers.py:192
msgid "No rows provided"
msgstr "Geen rijen opgegeven"

#: importer/serializers.py:196
msgid "Row does not belong to this session"
msgstr "Regel behoort niet tot deze sessie"

#: importer/serializers.py:199
msgid "Row contains invalid data"
msgstr "Regel bevat ongeldige gegevens"

#: importer/serializers.py:202
msgid "Row has already been completed"
msgstr "Regel is al voltooid"

#: importer/status_codes.py:13
msgid "Initializing"
msgstr "Initialiseren"

#: importer/status_codes.py:18
msgid "Mapping Columns"
msgstr "Toewijzing van kolommen"

#: importer/status_codes.py:21
msgid "Importing Data"
msgstr "Gegevens importeren"

#: importer/status_codes.py:24
msgid "Processing Data"
msgstr "Gegevens verwerken"

#: importer/validators.py:21
msgid "Data file exceeds maximum size limit"
msgstr "Gegevensbestand overschrijdt maximum grootte"

#: importer/validators.py:26
msgid "Data file contains no headers"
msgstr "Gegevensbestand bevat geen kopteksten"

#: importer/validators.py:29
msgid "Data file contains too many columns"
msgstr "Gegevensbestand bevat te veel kolommen"

#: importer/validators.py:32
msgid "Data file contains too many rows"
msgstr "Gegevensbestand bevat te veel rijen"

#: importer/validators.py:53
msgid "Value must be a valid dictionary object"
msgstr "Waarde moet een geldig woordenboek teken zijn"

#: machine/machine_types/label_printer.py:212
msgid "Copies"
msgstr "Kopieën"

#: machine/machine_types/label_printer.py:213
msgid "Number of copies to print for each label"
msgstr "Aantal afdrukken voor elk label"

#: machine/machine_types/label_printer.py:228
msgid "Connected"
msgstr "Verbonden"

#: machine/machine_types/label_printer.py:229 order/api.py:1679
msgid "Unknown"
msgstr "Onbekend"

#: machine/machine_types/label_printer.py:230
msgid "Printing"
msgstr "Afdrukken"

#: machine/machine_types/label_printer.py:231
msgid "No media"
msgstr "Geen media"

#: machine/machine_types/label_printer.py:232
msgid "Paper jam"
msgstr "Het papier is vastgelopen"

#: machine/machine_types/label_printer.py:233
msgid "Disconnected"
msgstr "Verbinding verbroken"

#: machine/machine_types/label_printer.py:240
msgid "Label Printer"
msgstr "Label printer"

#: machine/machine_types/label_printer.py:241
msgid "Directly print labels for various items."
msgstr "Direct labels afdrukken voor verschillende items."

#: machine/machine_types/label_printer.py:247
msgid "Printer Location"
msgstr "Printer locatie"

#: machine/machine_types/label_printer.py:248
msgid "Scope the printer to a specific location"
msgstr "Bereik de printer naar een specifieke locatie"

#: machine/models.py:25
msgid "Name of machine"
msgstr "Naam van de machine"

#: machine/models.py:29
msgid "Machine Type"
msgstr "Machine type"

#: machine/models.py:29
msgid "Type of machine"
msgstr "Type machine"

#: machine/models.py:34 machine/models.py:146
msgid "Driver"
msgstr "Stuurprogramma"

#: machine/models.py:35
msgid "Driver used for the machine"
msgstr "Stuurprogramma gebruikt voor de machine"

#: machine/models.py:39
msgid "Machines can be disabled"
msgstr "Machines kunnen worden uitgeschakeld"

#: machine/models.py:95
msgid "Driver available"
msgstr "Stuurprogramma beschikbaar"

#: machine/models.py:100
msgid "No errors"
msgstr "Geen fouten"

#: machine/models.py:105
msgid "Initialized"
msgstr "Geïnitialiseerd"

#: machine/models.py:117
msgid "Machine status"
msgstr "Machine status"

#: machine/models.py:145
msgid "Machine"
msgstr "Machine"

#: machine/models.py:151
msgid "Machine Config"
msgstr "Machine configuratie"

#: machine/models.py:156
msgid "Config type"
msgstr "Configuratie type"

#: order/api.py:118
msgid "Order Reference"
msgstr "Order Referentie"

#: order/api.py:146 order/api.py:1139
msgid "Outstanding"
msgstr "Uitmuntend"

#: order/api.py:162
msgid "Has Project Code"
msgstr "Heeft een projectcode"

#: order/api.py:176 order/models.py:475
msgid "Created By"
msgstr "Aangemaakt Door"

#: order/api.py:180
msgid "Created Before"
msgstr "Gemaakt vóór"

#: order/api.py:184
msgid "Created After"
msgstr "Gemaakt na"

#: order/api.py:188
msgid "Has Start Date"
msgstr "Heeft vervaldatum"

#: order/api.py:196
msgid "Start Date Before"
msgstr "Vervaldatum voor"

#: order/api.py:200
msgid "Start Date After"
msgstr "Vervaldatum na"

#: order/api.py:204
msgid "Has Target Date"
msgstr "Heeft doel datum"

#: order/api.py:212
msgid "Target Date Before"
msgstr "Doel datum voor"

#: order/api.py:216
msgid "Target Date After"
msgstr "Doel datum na"

#: order/api.py:267
msgid "Has Pricing"
msgstr "Heeft prijsstelling"

#: order/api.py:320 order/api.py:774 order/api.py:1375
msgid "Completed Before"
msgstr "Voltooid voor"

#: order/api.py:324 order/api.py:778 order/api.py:1379
msgid "Completed After"
msgstr "Voltooid na"

#: order/api.py:491 order/api.py:876 order/api.py:1102 order/models.py:1685
#: order/models.py:1803 order/models.py:1854 order/models.py:2002
#: order/models.py:2168 order/models.py:2690 order/models.py:2756
msgid "Order"
msgstr "Bestellen"

#: order/api.py:495 order/api.py:914
msgid "Order Complete"
msgstr "Bestelling voltooid"

#: order/api.py:527 order/api.py:531 order/serializers.py:676
msgid "Internal Part"
msgstr "Intern onderdeel"

#: order/api.py:549
msgid "Order Pending"
msgstr "Bestelling in behandeling"

#: order/api.py:899
msgid "Completed"
msgstr "Voltooid"

#: order/api.py:1155
msgid "Has Shipment"
msgstr "Heeft verzending"

#: order/api.py:1673 order/models.py:539 order/models.py:1686
#: order/models.py:1804
#: report/templates/report/inventree_purchase_order_report.html:14
#: stock/serializers.py:128 templates/email/overdue_purchase_order.html:15
msgid "Purchase Order"
msgstr "Inkooporder"

#: order/api.py:1675 order/models.py:1080 order/models.py:1855
#: order/models.py:2003 order/models.py:2169
#: report/templates/report/inventree_build_order_report.html:135
#: report/templates/report/inventree_sales_order_report.html:14
#: report/templates/report/inventree_sales_order_shipment_report.html:15
#: templates/email/overdue_sales_order.html:15
msgid "Sales Order"
msgstr "Verkooporder"

#: order/api.py:1677 order/models.py:2340 order/models.py:2691
#: order/models.py:2757
#: report/templates/report/inventree_return_order_report.html:13
#: templates/email/overdue_return_order.html:15
msgid "Return Order"
msgstr "Retour bestelling"

#: order/models.py:89
#: report/templates/report/inventree_purchase_order_report.html:31
#: report/templates/report/inventree_sales_order_report.html:31
msgid "Total Price"
msgstr "Totaalprijs"

#: order/models.py:90
msgid "Total price for this order"
msgstr "Totaalprijs van deze bestelling"

#: order/models.py:95 order/serializers.py:75
msgid "Order Currency"
msgstr "Valuta bestelling"

#: order/models.py:98 order/serializers.py:76
msgid "Currency for this order (leave blank to use company default)"
msgstr "Valuta voor deze order (laat leeg om de standaard van het bedrijf te gebruiken)"

#: order/models.py:323
msgid "This order is locked and cannot be modified"
msgstr "Deze bestelling is vergrendeld en kan niet worden gewijzigd"

#: order/models.py:370
msgid "Contact does not match selected company"
msgstr "Contact komt niet overeen met het geselecteerde bedrijf"

#: order/models.py:377
msgid "Start date must be before target date"
msgstr "Startdatum moet voor einddatum liggen"

#: order/models.py:430
msgid "Order description (optional)"
msgstr "Bestelling beschrijving (optioneel)"

#: order/models.py:439
msgid "Select project code for this order"
msgstr "Selecteer projectcode voor deze bestelling"

#: order/models.py:445 order/models.py:1586 order/models.py:2057
msgid "Link to external page"
msgstr "Link naar externe pagina"

#: order/models.py:452
msgid "Start date"
msgstr "Start datum"

#: order/models.py:453
msgid "Scheduled start date for this order"
msgstr "Geplande startdatum voor deze bestelling"

#: order/models.py:459 order/models.py:1593 order/serializers.py:269
#: report/templates/report/inventree_build_order_report.html:125
msgid "Target Date"
msgstr "Streefdatum"

#: order/models.py:461
msgid "Expected date for order delivery. Order will be overdue after this date."
msgstr "Verwachte datum voor levering van de bestelling. De bestelling wordt achterstallig na deze datum."

#: order/models.py:481
msgid "Issue Date"
msgstr "Datum van uitgifte"

#: order/models.py:482
msgid "Date order was issued"
msgstr "Order uitgegeven op datum"

#: order/models.py:490
msgid "User or group responsible for this order"
msgstr "Gebruiker of groep verantwoordelijk voor deze order"

#: order/models.py:501
msgid "Point of contact for this order"
msgstr "Contactpunt voor deze volgorde"

#: order/models.py:511
msgid "Company address for this order"
msgstr "Bedrijf adres voor deze bestelling"

#: order/models.py:602 order/models.py:1141
msgid "Order reference"
msgstr "Orderreferentie"

#: order/models.py:611 order/models.py:1165 order/models.py:2428
#: stock/serializers.py:612 stock/serializers.py:1010 users/models.py:544
msgid "Status"
msgstr "Status"

#: order/models.py:612
msgid "Purchase order status"
msgstr "Inkooporder status"

#: order/models.py:627
msgid "Company from which the items are being ordered"
msgstr "Bedrijf waar de artikelen van worden besteld"

#: order/models.py:638
msgid "Supplier Reference"
msgstr "Leveranciersreferentie"

#: order/models.py:639
msgid "Supplier order reference code"
msgstr "Order referentiecode van leverancier"

#: order/models.py:648
msgid "received by"
msgstr "ontvangen door"

#: order/models.py:655 order/models.py:2443
msgid "Date order was completed"
msgstr "Order voltooid op datum"

#: order/models.py:664 order/models.py:1733
msgid "Destination"
msgstr "Bestemming"

#: order/models.py:665 order/models.py:1737
msgid "Destination for received items"
msgstr "Bestemming voor ontvangen items"

#: order/models.py:711
msgid "Part supplier must match PO supplier"
msgstr "Onderdeelleverancier moet overeenkomen met de Inkooporderleverancier"

#: order/models.py:971
msgid "Quantity must be a positive number"
msgstr "Hoeveelheid moet een positief getal zijn"

#: order/models.py:1152 order/models.py:2415 stock/models.py:1001
#: stock/models.py:1002 stock/serializers.py:1405
#: templates/email/overdue_return_order.html:16
#: templates/email/overdue_sales_order.html:16
msgid "Customer"
msgstr "Klant"

#: order/models.py:1153
msgid "Company to which the items are being sold"
msgstr "Bedrijf waaraan de artikelen worden verkocht"

#: order/models.py:1166
msgid "Sales order status"
msgstr "Verkooporder status"

#: order/models.py:1177 order/models.py:2435
msgid "Customer Reference "
msgstr "Klantreferentie "

#: order/models.py:1178 order/models.py:2436
msgid "Customer order reference code"
msgstr "Klant order referentiecode"

#: order/models.py:1182 order/models.py:2009
msgid "Shipment Date"
msgstr "Verzenddatum"

#: order/models.py:1191
msgid "shipped by"
msgstr "verzonden door"

#: order/models.py:1230
msgid "Order is already complete"
msgstr "Bestelling is al afgerond"

#: order/models.py:1233
msgid "Order is already cancelled"
msgstr "Order is al geannuleerd"

#: order/models.py:1237
msgid "Only an open order can be marked as complete"
msgstr "Alleen een open bestelling kan als voltooid worden gemarkeerd"

#: order/models.py:1241
msgid "Order cannot be completed as there are incomplete shipments"
msgstr "Bestelling kan niet worden voltooid omdat er onvolledige verzendingen aanwezig zijn"

#: order/models.py:1246
msgid "Order cannot be completed as there are incomplete allocations"
msgstr "Order kan niet worden voltooid omdat er onvolledige artikelen aanwezig zijn"

#: order/models.py:1251
msgid "Order cannot be completed as there are incomplete line items"
msgstr "Order kan niet worden voltooid omdat er onvolledige artikelen aanwezig zijn"

#: order/models.py:1535 order/models.py:1548
msgid "The order is locked and cannot be modified"
msgstr "De bestelling is vergrendeld en kan niet worden gewijzigd"

#: order/models.py:1556
msgid "Item quantity"
msgstr "Hoeveelheid artikelen"

#: order/models.py:1573
msgid "Line item reference"
msgstr "Artikelregel referentie"

#: order/models.py:1580
msgid "Line item notes"
msgstr "Artikel notities"

#: order/models.py:1595
msgid "Target date for this line item (leave blank to use the target date from the order)"
msgstr "Doeldatum voor dit regelitem (laat leeg om de doeldatum van de bestelling te gebruiken)"

#: order/models.py:1616
msgid "Line item description (optional)"
msgstr "Regelomschrijving (optioneel)"

#: order/models.py:1623
msgid "Additional context for this line"
msgstr "Additionele context voor deze regel"

#: order/models.py:1633
msgid "Unit price"
msgstr "Stukprijs"

#: order/models.py:1647
msgid "Purchase Order Line Item"
msgstr "Inkooporder regel item"

#: order/models.py:1671
msgid "Supplier part must match supplier"
msgstr "Leveranciersonderdeel moet overeenkomen met leverancier"

#: order/models.py:1705
msgid "Supplier part"
msgstr "Leveranciersonderdeel"

#: order/models.py:1712
msgid "Received"
msgstr "Ontvangen"

#: order/models.py:1713
msgid "Number of items received"
msgstr "Aantal ontvangen artikelen"

#: order/models.py:1721 stock/models.py:1124 stock/serializers.py:679
msgid "Purchase Price"
msgstr "Inkoopprijs"

#: order/models.py:1722
msgid "Unit purchase price"
msgstr "Aankoopprijs per stuk"

#: order/models.py:1792
msgid "Purchase Order Extra Line"
msgstr "Extra regel inkooporder"

#: order/models.py:1821
msgid "Sales Order Line Item"
msgstr "Verkooporder regel item"

#: order/models.py:1842
msgid "Virtual part cannot be assigned to a sales order"
msgstr "Virtueel onderdeel kan niet worden toegewezen aan een verkooporder"

#: order/models.py:1847
msgid "Only salable parts can be assigned to a sales order"
msgstr "Alleen verkoopbare onderdelen kunnen aan een verkooporder worden toegewezen"

#: order/models.py:1873
msgid "Sale Price"
msgstr "Verkoopprijs"

#: order/models.py:1874
msgid "Unit sale price"
msgstr "Prijs per stuk"

#: order/models.py:1883 order/status_codes.py:50
msgid "Shipped"
msgstr "Verzonden"

#: order/models.py:1884
msgid "Shipped quantity"
msgstr "Verzonden hoeveelheid"

#: order/models.py:1978
msgid "Sales Order Shipment"
msgstr "Verzending van verkooporder"

#: order/models.py:2010
msgid "Date of shipment"
msgstr "Datum van verzending"

#: order/models.py:2016
msgid "Delivery Date"
msgstr "Leveringsdatum"

#: order/models.py:2017
msgid "Date of delivery of shipment"
msgstr "Datum van levering van zending"

#: order/models.py:2025
msgid "Checked By"
msgstr "Gecontroleerd door"

#: order/models.py:2026
msgid "User who checked this shipment"
msgstr "Gebruiker die deze zending gecontroleerd heeft"

#: order/models.py:2033 order/models.py:2265 order/serializers.py:1685
#: order/serializers.py:1809
#: report/templates/report/inventree_sales_order_shipment_report.html:14
msgid "Shipment"
msgstr "Zending"

#: order/models.py:2034
msgid "Shipment number"
msgstr "Zendingsnummer"

#: order/models.py:2042
msgid "Tracking Number"
msgstr "Volgnummer"

#: order/models.py:2043
msgid "Shipment tracking information"
msgstr "Zending volginformatie"

#: order/models.py:2050
msgid "Invoice Number"
msgstr "Factuurnummer"

#: order/models.py:2051
msgid "Reference number for associated invoice"
msgstr "Referentienummer voor bijbehorende factuur"

#: order/models.py:2074
msgid "Shipment has already been sent"
msgstr "Verzending is al verzonden"

#: order/models.py:2077
msgid "Shipment has no allocated stock items"
msgstr "Zending heeft geen toegewezen voorraadartikelen"

#: order/models.py:2157
msgid "Sales Order Extra Line"
msgstr "Verkooporder extra regel"

#: order/models.py:2186
msgid "Sales Order Allocation"
msgstr "Toewijzing verkooporder"

#: order/models.py:2209 order/models.py:2211
msgid "Stock item has not been assigned"
msgstr "Voorraadartikel is niet toegewezen"

#: order/models.py:2218
msgid "Cannot allocate stock item to a line with a different part"
msgstr "Kan het voorraadartikel niet toewijzen aan een regel met een ander onderdeel"

#: order/models.py:2221
msgid "Cannot allocate stock to a line without a part"
msgstr "Kan voorraad niet toewijzen aan een regel zonder onderdeel"

#: order/models.py:2224
msgid "Allocation quantity cannot exceed stock quantity"
msgstr "Toewijzingshoeveelheid kan niet hoger zijn dan de voorraadhoeveelheid"

#: order/models.py:2243 order/serializers.py:1555
msgid "Quantity must be 1 for serialized stock item"
msgstr "Hoeveelheid moet 1 zijn voor geserialiseerd voorraadartikel"

#: order/models.py:2246
msgid "Sales order does not match shipment"
msgstr "Verkooporder komt niet overeen met zending"

#: order/models.py:2247 plugin/base/barcodes/api.py:642
msgid "Shipment does not match sales order"
msgstr "Verzending komt niet overeen met verkooporder"

#: order/models.py:2255
msgid "Line"
msgstr "Regel"

#: order/models.py:2266
msgid "Sales order shipment reference"
msgstr "Verzendreferentie verkooporder"

#: order/models.py:2279 order/models.py:2698
msgid "Item"
msgstr "Artikel"

#: order/models.py:2280
msgid "Select stock item to allocate"
msgstr "Selecteer voorraadartikel om toe te wijzen"

#: order/models.py:2289
msgid "Enter stock allocation quantity"
msgstr "Voer voorraadtoewijzingshoeveelheid in"

#: order/models.py:2404
msgid "Return Order reference"
msgstr "Retour order referentie"

#: order/models.py:2416
msgid "Company from which items are being returned"
msgstr "Bedrijf van waaruit items worden teruggestuurd"

#: order/models.py:2429
msgid "Return order status"
msgstr "Retour bestelling status"

#: order/models.py:2656
msgid "Return Order Line Item"
msgstr "Retourneer bestelregel item"

#: order/models.py:2669
msgid "Stock item must be specified"
msgstr "Voorraad item moet worden opgegeven"

#: order/models.py:2673
msgid "Return quantity exceeds stock quantity"
msgstr "Retour hoeveelheid overschrijdt voorraad hoeveelheid"

#: order/models.py:2678
msgid "Return quantity must be greater than zero"
msgstr "Het retour aantal moet groter zijn dan nul"

#: order/models.py:2683
msgid "Invalid quantity for serialized stock item"
msgstr "Ongeldige hoeveelheid voor geserialiseerde voorraad"

#: order/models.py:2699
msgid "Select item to return from customer"
msgstr "Selecteer te retourneren product van de klant"

#: order/models.py:2714
msgid "Received Date"
msgstr "Ontvangst datum"

#: order/models.py:2715
msgid "The date this this return item was received"
msgstr "De datum waarop dit retour item is ontvangen"

#: order/models.py:2727
msgid "Outcome"
msgstr "Resultaat"

#: order/models.py:2728
msgid "Outcome for this line item"
msgstr "Resultaat van deze regel item"

#: order/models.py:2735
msgid "Cost associated with return or repair for this line item"
msgstr "Kosten geassocieerd met teruggave of reparatie voor deze regel item"

#: order/models.py:2745
msgid "Return Order Extra Line"
msgstr "Retourneren extra regel"

#: order/serializers.py:89
msgid "Order ID"
msgstr "Bestelling ID"

#: order/serializers.py:89
msgid "ID of the order to duplicate"
msgstr "ID van de bestelling om te dupliceren"

#: order/serializers.py:95
msgid "Copy Lines"
msgstr "Kopieer regels"

#: order/serializers.py:96
msgid "Copy line items from the original order"
msgstr "Kopieer regelitems uit de oorspronkelijke bestelling"

#: order/serializers.py:102
msgid "Copy Extra Lines"
msgstr "Extra regels kopiëren"

#: order/serializers.py:103
msgid "Copy extra line items from the original order"
msgstr "Extra regelitems van de oorspronkelijke bestelling kopiëren"

#: order/serializers.py:116
#: report/templates/report/inventree_purchase_order_report.html:22
#: report/templates/report/inventree_return_order_report.html:19
#: report/templates/report/inventree_sales_order_report.html:22
msgid "Line Items"
msgstr "Artikelen"

#: order/serializers.py:121
msgid "Completed Lines"
msgstr "Afgeronde regel items"

#: order/serializers.py:172
msgid "Duplicate Order"
msgstr "Artikel dupliceren"

#: order/serializers.py:173
msgid "Specify options for duplicating this order"
msgstr "Specificeer opties voor het dupliceren van deze bestelling"

#: order/serializers.py:249
msgid "Invalid order ID"
msgstr "Ongeldige  order ID"

#: order/serializers.py:388
msgid "Supplier Name"
msgstr "Leveranciers Naam"

#: order/serializers.py:430
msgid "Order cannot be cancelled"
msgstr "Order kan niet worden geannuleerd"

#: order/serializers.py:445 order/serializers.py:1576
msgid "Allow order to be closed with incomplete line items"
msgstr "Toestaan order te sluiten met onvolledige regelitems"

#: order/serializers.py:455 order/serializers.py:1586
msgid "Order has incomplete line items"
msgstr "Bestelling heeft onvolledige regelitems"

#: order/serializers.py:608
msgid "Order is not open"
msgstr "Order is niet open"

#: order/serializers.py:629
msgid "Auto Pricing"
msgstr "Automatisch prijzen"

#: order/serializers.py:631
msgid "Automatically calculate purchase price based on supplier part data"
msgstr "Koopprijs automatisch berekenen gebaseerd op leveranciers \n"
" onderdelen gegevens"

#: order/serializers.py:641
msgid "Purchase price currency"
msgstr "Valuta Inkoopprijs"

#: order/serializers.py:649
msgid "Merge Items"
msgstr "Items samenvoegen"

#: order/serializers.py:651
msgid "Merge items with the same part, destination and target date into one line item"
msgstr "Items met hetzelfde onderdeel, bestemming en doeldatum samenvoegen in één regelitem"

#: order/serializers.py:658 part/serializers.py:571
msgid "SKU"
msgstr "SKU"

#: order/serializers.py:672 part/models.py:1089 part/serializers.py:412
msgid "Internal Part Number"
msgstr "Intern Onderdeelnummer"

#: order/serializers.py:680
msgid "Internal Part Name"
msgstr "Interne naam onderdeel"

#: order/serializers.py:696
msgid "Supplier part must be specified"
msgstr "Leveranciersonderdeel moet worden gespecificeerd"

#: order/serializers.py:699
msgid "Purchase order must be specified"
msgstr "Inkooporder moet worden gespecificeerd"

#: order/serializers.py:707
msgid "Supplier must match purchase order"
msgstr "De leverancier moet overeenkomen met de inkooporder"

#: order/serializers.py:708
msgid "Purchase order must match supplier"
msgstr "Inkooporder moet overeenkomen met de leverancier"

#: order/serializers.py:754 order/serializers.py:1656
msgid "Line Item"
msgstr "Artikel"

#: order/serializers.py:760
msgid "Line item does not match purchase order"
msgstr "Artikelregel komt niet overeen met inkooporder"

#: order/serializers.py:770 order/serializers.py:917 order/serializers.py:2023
msgid "Select destination location for received items"
msgstr "Selecteer bestemmingslocatie voor ontvangen artikelen"

#: order/serializers.py:786
msgid "Enter batch code for incoming stock items"
msgstr "Voer batch code in voor inkomende voorraad items"

#: order/serializers.py:793 stock/models.py:1083 users/models.py:143
msgid "Expiry Date"
msgstr "Vervaldatum"

#: order/serializers.py:794
msgid "Enter expiry date for incoming stock items"
msgstr "Voer vervaldatum in voor inkomende voorraad items"

#: order/serializers.py:802
msgid "Enter serial numbers for incoming stock items"
msgstr "Voer serienummers in voor inkomende voorraadartikelen"

#: order/serializers.py:812
msgid "Override packaging information for incoming stock items"
msgstr "Overschrijf verpakkingsinformatie voor binnenkomende voorraad"

#: order/serializers.py:820 order/serializers.py:2028
msgid "Additional note for incoming stock items"
msgstr "Extra opmerking voor inkomende voorraad items"

#: order/serializers.py:827
msgid "Barcode"
msgstr "Streepjescode"

#: order/serializers.py:828
msgid "Scanned barcode"
msgstr "Gescande streepjescode"

#: order/serializers.py:844
msgid "Barcode is already in use"
msgstr "Streepjescode is al in gebruik"

#: order/serializers.py:867
msgid "An integer quantity must be provided for trackable parts"
msgstr "Hoeveelheid als geheel getal vereist voor traceerbare onderdelen"

#: order/serializers.py:934 order/serializers.py:2047
msgid "Line items must be provided"
msgstr "Artikelen moeten worden opgegeven"

#: order/serializers.py:950
msgid "Destination location must be specified"
msgstr "Bestemmingslocatie moet worden opgegeven"

#: order/serializers.py:961
msgid "Supplied barcode values must be unique"
msgstr "Geleverde streepjescodewaarden moeten uniek zijn"

#: order/serializers.py:1092
msgid "Shipments"
msgstr "Verzendingen"

#: order/serializers.py:1096
msgid "Completed Shipments"
msgstr "Voltooide Verzendingen"

#: order/serializers.py:1283
msgid "Sale price currency"
msgstr "Valuta verkoopprijs"

#: order/serializers.py:1331
msgid "Allocated Items"
msgstr "Toegewezen items"

#: order/serializers.py:1458
msgid "No shipment details provided"
msgstr "Geen verzenddetails opgegeven"

#: order/serializers.py:1519 order/serializers.py:1665
msgid "Line item is not associated with this order"
msgstr "Artikelregel is niet gekoppeld aan deze bestelling"

#: order/serializers.py:1538
msgid "Quantity must be positive"
msgstr "Hoeveelheid moet positief zijn"

#: order/serializers.py:1675
msgid "Enter serial numbers to allocate"
msgstr "Voer serienummers in om toe te wijzen"

#: order/serializers.py:1697 order/serializers.py:1817
msgid "Shipment has already been shipped"
msgstr "Verzending is al verzonden"

#: order/serializers.py:1700 order/serializers.py:1820
msgid "Shipment is not associated with this order"
msgstr "Zending is niet gekoppeld aan deze bestelling"

#: order/serializers.py:1755
msgid "No match found for the following serial numbers"
msgstr "Geen overeenkomst gevonden voor de volgende serienummers"

#: order/serializers.py:1762
msgid "The following serial numbers are unavailable"
msgstr "De volgende serienummers zijn niet beschikbaar"

#: order/serializers.py:1989
msgid "Return order line item"
msgstr "Retourneer regel item"

#: order/serializers.py:1999
msgid "Line item does not match return order"
msgstr "Artikelregel komt niet overeen met inkooporder"

#: order/serializers.py:2002
msgid "Line item has already been received"
msgstr "Regel item is al ontvangen"

#: order/serializers.py:2039
msgid "Items can only be received against orders which are in progress"
msgstr "Artikelen kunnen alleen worden ontvangen tegen lopende bestellingen"

#: order/serializers.py:2131
msgid "Quantity to return"
msgstr "Hoeveelheid te retourneren"

#: order/serializers.py:2143
msgid "Line price currency"
msgstr "Lijn prijs valuta"

#: order/status_codes.py:17 order/status_codes.py:54 stock/status_codes.py:16
msgid "Lost"
msgstr "Kwijt"

#: order/status_codes.py:18 order/status_codes.py:55 stock/status_codes.py:24
msgid "Returned"
msgstr "Retour"

#: order/status_codes.py:47 order/status_codes.py:79
msgid "In Progress"
msgstr "In Behandeling"

#: order/status_codes.py:105
msgid "Return"
msgstr "Retour"

#: order/status_codes.py:108
msgid "Repair"
msgstr "Herstel"

#: order/status_codes.py:111
msgid "Replace"
msgstr "Vervangen"

#: order/status_codes.py:114
msgid "Refund"
msgstr "Restitutie"

#: order/status_codes.py:117
msgid "Reject"
msgstr "Afwijzen"

#: order/tasks.py:44
msgid "Overdue Purchase Order"
msgstr "Achterstallige inkooporder"

#: order/tasks.py:49
#, python-brace-format
msgid "Purchase order {po} is now overdue"
msgstr "Inkooporder {po} is nu achterstallig"

#: order/tasks.py:112
msgid "Overdue Sales Order"
msgstr "Achterstallige Verkooporder"

#: order/tasks.py:117
#, python-brace-format
msgid "Sales order {so} is now overdue"
msgstr "Verkooporder {so} is nu achterstallig"

#: order/tasks.py:177
msgid "Overdue Return Order"
msgstr "Achterstallige retour orders"

#: order/tasks.py:182
#, python-brace-format
msgid "Return order {ro} is now overdue"
msgstr "Productieorder {ro} is nu achterstallig"

#: part/api.py:115
msgid "Starred"
msgstr "Favoriet"

#: part/api.py:117
msgid "Filter by starred categories"
msgstr "Filter op categorieën met ster"

#: part/api.py:134 stock/api.py:255
msgid "Depth"
msgstr "Diepte"

#: part/api.py:134
msgid "Filter by category depth"
msgstr "Filteren op categorie diepte"

#: part/api.py:152 stock/api.py:273
msgid "Top Level"
msgstr "Hoogste niveau"

#: part/api.py:154
msgid "Filter by top-level categories"
msgstr "Filteren op topniveau categorieën"

#: part/api.py:167 stock/api.py:288
msgid "Cascade"
msgstr "Stapelen"

#: part/api.py:169
msgid "Include sub-categories in filtered results"
msgstr "Inclusief subcategorieën in gefilterde resultaten"

#: part/api.py:189
msgid "Parent"
msgstr "Bovenliggend"

#: part/api.py:191
msgid "Filter by parent category"
msgstr "Filter op bovenliggende categorie"

#: part/api.py:226
msgid "Exclude sub-categories under the specified category"
msgstr "Subcategorieën uitsluiten in de opgegeven categorie"

#: part/api.py:438
msgid "Has Results"
msgstr "Heeft resultaten"

#: part/api.py:611
msgid "Incoming Purchase Order"
msgstr "Binnenkomende Inkooporder"

#: part/api.py:625
msgid "Outgoing Sales Order"
msgstr "Uitgaande Verkooporder"

#: part/api.py:637
msgid "Stock produced by Build Order"
msgstr "Geproduceerde voorraad door Productieorder"

#: part/api.py:719
msgid "Stock required for Build Order"
msgstr "Voorraad vereist voor Productieorder"

#: part/api.py:865
msgid "Validate entire Bill of Materials"
msgstr "Valideer de gehele materiaalbon"

#: part/api.py:871
msgid "This option must be selected"
msgstr "Deze optie moet geselecteerd worden"

#: part/api.py:907
msgid "Is Variant"
msgstr "Is een variant"

#: part/api.py:915
msgid "Is Revision"
msgstr "Is revisie"

#: part/api.py:925
msgid "Has Revisions"
msgstr "Heeft revisies"

#: part/api.py:1116
msgid "BOM Valid"
msgstr "BOM Valid"

#: part/api.py:1775
msgid "Assembly part is testable"
msgstr "Assemblage deel is testbaar"

#: part/api.py:1784
msgid "Component part is testable"
msgstr "Component onderdeel is testbaar"

#: part/api.py:1835
msgid "Uses"
msgstr "Gebruik"

#: part/models.py:90 part/models.py:4137
#: templates/email/part_event_notification.html:16
msgid "Part Category"
msgstr "Onderdeel Categorie"

#: part/models.py:91 users/ruleset.py:32
msgid "Part Categories"
msgstr "Onderdeel Categorieën"

#: part/models.py:109 part/models.py:1134
msgid "Default Location"
msgstr "Standaard locatie"

#: part/models.py:110
msgid "Default location for parts in this category"
msgstr "Standaard locatie voor onderdelen in deze categorie"

#: part/models.py:115 stock/models.py:212
msgid "Structural"
msgstr "Structureel"

#: part/models.py:117
msgid "Parts may not be directly assigned to a structural category, but may be assigned to child categories."
msgstr "Onderdelen mogen niet rechtstreeks aan een structurele categorie worden toegewezen, maar kunnen worden toegewezen aan subcategorieën."

#: part/models.py:126
msgid "Default keywords"
msgstr "Standaard trefwoorden"

#: part/models.py:127
msgid "Default keywords for parts in this category"
msgstr "Standaard trefwoorden voor delen in deze categorie"

#: part/models.py:134 stock/models.py:96 stock/models.py:194
msgid "Icon"
msgstr "Pictogram"

#: part/models.py:135 part/serializers.py:152 part/serializers.py:171
#: stock/models.py:195
msgid "Icon (optional)"
msgstr "Pictogram (optioneel)"

#: part/models.py:179
msgid "You cannot make this part category structural because some parts are already assigned to it!"
msgstr "U kunt deze voorraadlocatie niet structureel maken omdat sommige voorraadartikelen er al in liggen!"

#: part/models.py:458 part/serializers.py:130 part/serializers.py:343
#: users/ruleset.py:33
msgid "Parts"
msgstr "Onderdelen"

#: part/models.py:510
msgid "Cannot delete this part as it is locked"
msgstr "Kan dit deel niet verwijderen omdat het vergrendeld is"

#: part/models.py:513
msgid "Cannot delete this part as it is still active"
msgstr "Kan dit deel niet verwijderen omdat het nog actief is"

#: part/models.py:518
msgid "Cannot delete this part as it is used in an assembly"
msgstr "Kan dit deel niet verwijderen omdat het in een groep gebruikt is"

#: part/models.py:556
msgid "Invalid choice for parent part"
msgstr "Ongeldige keuze voor bovenliggend deel"

#: part/models.py:604 part/models.py:611
#, python-brace-format
msgid "Part '{self}' cannot be used in BOM for '{parent}' (recursive)"
msgstr "{self}' kan niet worden gebruikt in BOM voor '{parent}' (recursief)"

#: part/models.py:623
#, python-brace-format
msgid "Part '{parent}' is  used in BOM for '{self}' (recursive)"
msgstr "{parent}' wordt gebruikt in BOM voor '{self}' (recursief)"

#: part/models.py:690
#, python-brace-format
msgid "IPN must match regex pattern {pattern}"
msgstr "IPN moet overeenkomen met regex patroon {pattern}"

#: part/models.py:698
msgid "Part cannot be a revision of itself"
msgstr "Onderdeel kan geen herziening van zichzelf zijn"

#: part/models.py:705
msgid "Cannot make a revision of a part which is already a revision"
msgstr "Kan geen revisie maken van een onderdeel dat al een revisie is"

#: part/models.py:712
msgid "Revision code must be specified"
msgstr "Revisie code moet worden opgegeven"

#: part/models.py:719
msgid "Revisions are only allowed for assembly parts"
msgstr "Herzieningen zijn alleen toegestaan voor assemblageonderdelen"

#: part/models.py:726
msgid "Cannot make a revision of a template part"
msgstr "Kan geen revisie maken van een sjabloon onderdeel"

#: part/models.py:732
msgid "Parent part must point to the same template"
msgstr "Bovenliggend onderdeel moet naar dezelfde sjabloon verwijzen"

#: part/models.py:829
msgid "Stock item with this serial number already exists"
msgstr "Voorraadartikel met dit serienummer bestaat al"

#: part/models.py:971
msgid "Duplicate IPN not allowed in part settings"
msgstr "Dubbele IPN niet toegestaan in deelinstellingen"

#: part/models.py:983
msgid "Duplicate part revision already exists."
msgstr "Dubbele onderdeel revisie bestaat al."

#: part/models.py:992
msgid "Part with this Name, IPN and Revision already exists."
msgstr "Onderdeel met deze naam, IPN en Revisie bestaat al."

#: part/models.py:1007
msgid "Parts cannot be assigned to structural part categories!"
msgstr "Onderdelen kunnen niet worden toegewezen aan categorieën van structurele onderdelen!"

#: part/models.py:1039
msgid "Part name"
msgstr "Onderdeel naam"

#: part/models.py:1044
msgid "Is Template"
msgstr "Is een sjabloon"

#: part/models.py:1045
msgid "Is this part a template part?"
msgstr "Is dit deel van een sjabloon?"

#: part/models.py:1055
msgid "Is this part a variant of another part?"
msgstr "Is dit een variant van een ander deel?"

#: part/models.py:1056
msgid "Variant Of"
msgstr "Variant van"

#: part/models.py:1063
msgid "Part description (optional)"
msgstr "Beschrijving (optioneel)"

#: part/models.py:1070
msgid "Keywords"
msgstr "Sleutelwoorden"

#: part/models.py:1071
msgid "Part keywords to improve visibility in search results"
msgstr "Deel sleutelwoorden om de zichtbaarheid van de zoekresultaten te verbeteren"

#: part/models.py:1081
msgid "Part category"
msgstr "Onderdeel Categorie"

#: part/models.py:1088 part/serializers.py:926
#: report/templates/report/inventree_stock_location_report.html:103
msgid "IPN"
msgstr "IPN"

#: part/models.py:1096
msgid "Part revision or version number"
msgstr "Onderdeel revisie of versienummer"

#: part/models.py:1097 report/models.py:215
msgid "Revision"
msgstr "Revisie"

#: part/models.py:1106
msgid "Is this part a revision of another part?"
msgstr "Is dit deel een herziening van een ander deel?"

#: part/models.py:1107
msgid "Revision Of"
msgstr "Revisie van"

#: part/models.py:1132
msgid "Where is this item normally stored?"
msgstr "Waar wordt dit item normaal opgeslagen?"

#: part/models.py:1178
msgid "Default Supplier"
msgstr "Standaard leverancier"

#: part/models.py:1179
msgid "Default supplier part"
msgstr "Standaardleverancier"

#: part/models.py:1186
msgid "Default Expiry"
msgstr "Standaard verval datum"

#: part/models.py:1187
msgid "Expiry time (in days) for stock items of this part"
msgstr "Verlooptijd (in dagen) voor voorraadartikelen van dit deel"

#: part/models.py:1195 part/serializers.py:977
msgid "Minimum Stock"
msgstr "Minimum voorraad"

#: part/models.py:1196
msgid "Minimum allowed stock level"
msgstr "Minimaal toegelaten stock niveau"

#: part/models.py:1205
msgid "Units of measure for this part"
msgstr "Eenheden voor dit onderdeel"

#: part/models.py:1212
msgid "Can this part be built from other parts?"
msgstr "Kan dit onderdeel uit andere delen worden gebouwd?"

#: part/models.py:1218
msgid "Can this part be used to build other parts?"
msgstr "Kan dit onderdeel gebruikt worden om andere onderdelen te bouwen?"

#: part/models.py:1224
msgid "Does this part have tracking for unique items?"
msgstr "Heeft dit onderdeel een tracking voor unieke items?"

#: part/models.py:1230
msgid "Can this part have test results recorded against it?"
msgstr "Kunnen de testresultaten van dit onderdeel tegen dit onderdeel worden geregistreerd?"

#: part/models.py:1236
msgid "Can this part be purchased from external suppliers?"
msgstr "Kan dit onderdeel worden gekocht van externe leveranciers?"

#: part/models.py:1242
msgid "Can this part be sold to customers?"
msgstr "Kan dit onderdeel aan klanten worden verkocht?"

#: part/models.py:1246
msgid "Is this part active?"
msgstr "Is dit onderdeel actief?"

#: part/models.py:1252
msgid "Locked parts cannot be edited"
msgstr "Vergrendelde onderdelen kunnen niet worden bewerkt"

#: part/models.py:1258
msgid "Is this a virtual part, such as a software product or license?"
msgstr "Is dit een virtueel onderdeel, zoals een softwareproduct of licentie?"

#: part/models.py:1264
msgid "BOM checksum"
msgstr "BOM checksum"

#: part/models.py:1265
msgid "Stored BOM checksum"
msgstr "Checksum van BOM opgeslagen"

#: part/models.py:1273
msgid "BOM checked by"
msgstr "BOM gecontroleerd door"

#: part/models.py:1278
msgid "BOM checked date"
msgstr "BOM gecontroleerd datum"

#: part/models.py:1294
msgid "Creation User"
msgstr "Aanmaken gebruiker"

#: part/models.py:1304
msgid "Owner responsible for this part"
msgstr "Eigenaar verantwoordelijk voor dit deel"

#: part/models.py:1309
msgid "Last Stocktake"
msgstr "Laatste voorraadcontrole"

#: part/models.py:2190
msgid "Sell multiple"
msgstr "Verkopen van meerdere"

#: part/models.py:3185
msgid "Currency used to cache pricing calculations"
msgstr "Valuta die gebruikt wordt voor de cache berekeningen"

#: part/models.py:3201
msgid "Minimum BOM Cost"
msgstr "Minimale BOM kosten"

#: part/models.py:3202
msgid "Minimum cost of component parts"
msgstr "Minimale kosten van onderdelen"

#: part/models.py:3208
msgid "Maximum BOM Cost"
msgstr "Maximale BOM kosten"

#: part/models.py:3209
msgid "Maximum cost of component parts"
msgstr "Maximale kosten van onderdelen"

#: part/models.py:3215
msgid "Minimum Purchase Cost"
msgstr "Minimale aankoop kosten"

#: part/models.py:3216
msgid "Minimum historical purchase cost"
msgstr "Minimale historische aankoop kosten"

#: part/models.py:3222
msgid "Maximum Purchase Cost"
msgstr "Maximale aanschaf kosten"

#: part/models.py:3223
msgid "Maximum historical purchase cost"
msgstr "Maximum historische aankoop kosten"

#: part/models.py:3229
msgid "Minimum Internal Price"
msgstr "Minimale interne prijs"

#: part/models.py:3230
msgid "Minimum cost based on internal price breaks"
msgstr "Minimale kosten op basis van interne prijsschommelingen"

#: part/models.py:3236
msgid "Maximum Internal Price"
msgstr "Maximale interne prijs"

#: part/models.py:3237
msgid "Maximum cost based on internal price breaks"
msgstr "Maximale kosten gebaseerd op interne prijsvoordelen"

#: part/models.py:3243
msgid "Minimum Supplier Price"
msgstr "Minimale leverancier prijs"

#: part/models.py:3244
msgid "Minimum price of part from external suppliers"
msgstr "Minimale prijs van onderdeel van externe leveranciers"

#: part/models.py:3250
msgid "Maximum Supplier Price"
msgstr "Maximale leverancier prijs"

#: part/models.py:3251
msgid "Maximum price of part from external suppliers"
msgstr "Maximale prijs van onderdeel van externe leveranciers"

#: part/models.py:3257
msgid "Minimum Variant Cost"
msgstr "Minimale variant kosten"

#: part/models.py:3258
msgid "Calculated minimum cost of variant parts"
msgstr "Berekende minimale kosten van variant onderdelen"

#: part/models.py:3264
msgid "Maximum Variant Cost"
msgstr "Maximale variant kosten"

#: part/models.py:3265
msgid "Calculated maximum cost of variant parts"
msgstr "Berekende maximale kosten van variant onderdelen"

#: part/models.py:3271 part/models.py:3285
msgid "Minimum Cost"
msgstr "Minimale kostprijs"

#: part/models.py:3272
msgid "Override minimum cost"
msgstr "Overschrijf minimale kosten"

#: part/models.py:3278 part/models.py:3292
msgid "Maximum Cost"
msgstr "Maximale kosten"

#: part/models.py:3279
msgid "Override maximum cost"
msgstr "Overschrijf maximale kosten"

#: part/models.py:3286
msgid "Calculated overall minimum cost"
msgstr "Berekende minimale kosten"

#: part/models.py:3293
msgid "Calculated overall maximum cost"
msgstr "Berekende totale maximale kosten"

#: part/models.py:3299
msgid "Minimum Sale Price"
msgstr "Minimale verkoop prijs"

#: part/models.py:3300
msgid "Minimum sale price based on price breaks"
msgstr "Minimale verkoopprijs gebaseerd op prijsschommelingen"

#: part/models.py:3306
msgid "Maximum Sale Price"
msgstr "Maximale verkoop prijs"

#: part/models.py:3307
msgid "Maximum sale price based on price breaks"
msgstr "Maximale verkoopprijs gebaseerd op prijsschommelingen"

#: part/models.py:3313
msgid "Minimum Sale Cost"
msgstr "Minimale verkoop prijs"

#: part/models.py:3314
msgid "Minimum historical sale price"
msgstr "Minimale historische verkoop prijs"

#: part/models.py:3320
msgid "Maximum Sale Cost"
msgstr "Maximale verkoop prijs"

#: part/models.py:3321
msgid "Maximum historical sale price"
msgstr "Maximale historische verkoop prijs"

#: part/models.py:3340
msgid "Part for stocktake"
msgstr "Onderdeel voor voorraadcontrole"

#: part/models.py:3345
msgid "Item Count"
msgstr "Getelde items"

#: part/models.py:3346
msgid "Number of individual stock entries at time of stocktake"
msgstr "Aantal individuele voorraadvermeldingen op het moment van voorraadcontrole"

#: part/models.py:3354
msgid "Total available stock at time of stocktake"
msgstr "Totale voorraad op het moment van voorraadcontrole"

#: part/models.py:3358 part/models.py:3441 part/serializers.py:273
#: report/templates/report/inventree_test_report.html:106
msgid "Date"
msgstr "Datum"

#: part/models.py:3359
msgid "Date stocktake was performed"
msgstr "Datum waarop voorraad werd uitgevoerd"

#: part/models.py:3367
msgid "Additional notes"
msgstr "Aanvullende notities"

#: part/models.py:3377
msgid "User who performed this stocktake"
msgstr "Gebruiker die deze voorraad heeft uitgevoerd"

#: part/models.py:3383
msgid "Minimum Stock Cost"
msgstr "Minimale voorraadprijs"

#: part/models.py:3384
msgid "Estimated minimum cost of stock on hand"
msgstr "Geschatte minimum kosten van de voorraad op de hand"

#: part/models.py:3390
msgid "Maximum Stock Cost"
msgstr "Maximale voorraadkosten"

#: part/models.py:3391
msgid "Estimated maximum cost of stock on hand"
msgstr "Geschatte maximale kosten van de hand van voorraad"

#: part/models.py:3447
msgid "Report"
msgstr "Rapport"

#: part/models.py:3448
msgid "Stocktake report file (generated internally)"
msgstr "Bestand voorraadcontrole (intern gegenereerd)"

#: part/models.py:3453
msgid "Part Count"
msgstr "Aantal onderdelen"

#: part/models.py:3454
msgid "Number of parts covered by stocktake"
msgstr "Aantal door voorraadopname gedekte onderdelen"

#: part/models.py:3464
msgid "User who requested this stocktake report"
msgstr "Gebruiker die om dit voorraadrapport heeft gevraagd"

#: part/models.py:3474
msgid "Part Sale Price Break"
msgstr "Periodieke verkoopprijs voor onderdelen"

#: part/models.py:3586
msgid "Part Test Template"
msgstr "Sjabloon test onderdeel"

#: part/models.py:3612
msgid "Invalid template name - must include at least one alphanumeric character"
msgstr "Ongeldige sjabloonnaam - moet minstens één alfanumeriek teken bevatten"

#: part/models.py:3633 part/models.py:3803
msgid "Choices must be unique"
msgstr "Keuzes moeten uniek zijn"

#: part/models.py:3644
msgid "Test templates can only be created for testable parts"
msgstr "Test sjablonen kunnen alleen worden gemaakt voor testbare onderdelen"

#: part/models.py:3655
msgid "Test template with the same key already exists for part"
msgstr "Test template met dezelfde sleutel bestaat al voor een deel"

#: part/models.py:3672
msgid "Test Name"
msgstr "Test naam"

#: part/models.py:3673
msgid "Enter a name for the test"
msgstr "Geef een naam op voor de test"

#: part/models.py:3679
msgid "Test Key"
msgstr "Test sleutel"

#: part/models.py:3680
msgid "Simplified key for the test"
msgstr "Vereenvoudigde sleutel voor de test"

#: part/models.py:3687
msgid "Test Description"
msgstr "Test beschrijving"

#: part/models.py:3688
msgid "Enter description for this test"
msgstr "Voer beschrijving in voor deze test"

#: part/models.py:3692 report/models.py:271
msgid "Enabled"
msgstr "Ingeschakeld"

#: part/models.py:3692
msgid "Is this test enabled?"
msgstr "Is deze test ingeschakeld?"

#: part/models.py:3697
msgid "Required"
msgstr "Vereist"

#: part/models.py:3698
msgid "Is this test required to pass?"
msgstr "Is deze test nodig om te doorlopen?"

#: part/models.py:3703
msgid "Requires Value"
msgstr "Waarde vereist"

#: part/models.py:3704
msgid "Does this test require a value when adding a test result?"
msgstr "Heeft deze test een waarde nodig bij het toevoegen van een testresultaat?"

#: part/models.py:3709
msgid "Requires Attachment"
msgstr "Vereist bijlage"

#: part/models.py:3711
msgid "Does this test require a file attachment when adding a test result?"
msgstr "Vereist deze test een bestandsbijlage bij het toevoegen van een testresultaat?"

#: part/models.py:3717 part/models.py:3865
msgid "Choices"
msgstr "Keuzes"

#: part/models.py:3718
msgid "Valid choices for this test (comma-separated)"
msgstr "Geldige keuzes voor deze parameter (komma gescheiden)"

#: part/models.py:3751
msgid "Part Parameter Template"
msgstr "Sjabloon deel parameter"

#: part/models.py:3778
msgid "Checkbox parameters cannot have units"
msgstr "Checkbox parameters kunnen geen eenheden bevatten"

#: part/models.py:3783
msgid "Checkbox parameters cannot have choices"
msgstr "Checkbox parameters kunnen geen eenheden bevatten"

#: part/models.py:3820
msgid "Parameter template name must be unique"
msgstr "De template van de parameter moet uniek zijn"

#: part/models.py:3838
msgid "Parameter Name"
msgstr "Parameternaam"

#: part/models.py:3845
msgid "Physical units for this parameter"
msgstr "Fysieke eenheden voor deze parameter"

#: part/models.py:3853
msgid "Parameter description"
msgstr "Parameter omschrijving"

#: part/models.py:3859
msgid "Checkbox"
msgstr "Selectievakje"

#: part/models.py:3860
msgid "Is this parameter a checkbox?"
msgstr "Is deze parameter een selectievak?"

#: part/models.py:3866
msgid "Valid choices for this parameter (comma-separated)"
msgstr "Geldige keuzes voor deze parameter (komma gescheiden)"

#: part/models.py:3877
msgid "Selection list for this parameter"
msgstr "Lijst met selecties voor deze parameter"

#: part/models.py:3913
msgid "Part Parameter"
msgstr "Onderdeel parameters"

#: part/models.py:3939
msgid "Parameter cannot be modified - part is locked"
msgstr "Parameter kan niet worden gewijzigd - onderdeel is vergrendeld"

#: part/models.py:3977
msgid "Invalid choice for parameter value"
msgstr "Ongeldige keuze voor parameter waarde"

#: part/models.py:4028
msgid "Parent Part"
msgstr "Hoofd onderdeel"

#: part/models.py:4036 part/models.py:4144 part/models.py:4145
msgid "Parameter Template"
msgstr "Parameter sjabloon"

#: part/models.py:4042
msgid "Parameter Value"
msgstr "Parameterwaarde"

#: part/models.py:4092
msgid "Part Category Parameter Template"
msgstr "Sjabloon categorie parameters onderdeel"

#: part/models.py:4151
msgid "Default Value"
msgstr "Standaard waarde"

#: part/models.py:4152
msgid "Default Parameter Value"
msgstr "Standaard Parameter Waarde"

#: part/models.py:4283
msgid "BOM item cannot be modified - assembly is locked"
msgstr "BOM item kan niet worden gewijzigd - assemblage is vergrendeld "

#: part/models.py:4290
msgid "BOM item cannot be modified - variant assembly is locked"
msgstr "BOM item kan niet worden gewijzigd - assemblage is vergrendeld"

#: part/models.py:4300
msgid "Select parent part"
msgstr "Selecteer boven liggend onderdeel"

#: part/models.py:4310
msgid "Sub part"
msgstr "Sub onderdeel"

#: part/models.py:4311
msgid "Select part to be used in BOM"
msgstr "Selecteer onderdeel dat moet worden gebruikt in BOM"

#: part/models.py:4322
msgid "BOM quantity for this BOM item"
msgstr "BOM hoeveelheid voor dit BOM item"

#: part/models.py:4328
msgid "This BOM item is optional"
msgstr "Dit BOM item is optioneel"

#: part/models.py:4334
msgid "This BOM item is consumable (it is not tracked in build orders)"
msgstr "Dit BOM item is verbruikbaar (het wordt niet bijgehouden in build orders)"

#: part/models.py:4341
msgid "Overage"
msgstr "Over tijd"

#: part/models.py:4342
msgid "Estimated build wastage quantity (absolute or percentage)"
msgstr "Geschatte hoeveelheid verspilling (absoluut of percentage)"

#: part/models.py:4349
msgid "BOM item reference"
msgstr "Artikelregel referentie"

#: part/models.py:4357
msgid "BOM item notes"
msgstr "BOM item notities"

#: part/models.py:4363
msgid "Checksum"
msgstr "Controle som"

#: part/models.py:4364
msgid "BOM line checksum"
msgstr "BOM lijn controle som"

#: part/models.py:4369
msgid "Validated"
msgstr "Goedgekeurd"

#: part/models.py:4370
msgid "This BOM item has been validated"
msgstr "Dit BOM item is goedgekeurd"

#: part/models.py:4375
msgid "Gets inherited"
msgstr "Wordt overgenomen"

#: part/models.py:4376
msgid "This BOM item is inherited by BOMs for variant parts"
msgstr "Dit BOM item wordt overgenomen door BOMs voor variant onderdelen"

#: part/models.py:4382
msgid "Stock items for variant parts can be used for this BOM item"
msgstr "Voorraaditems voor variant onderdelen kunnen worden gebruikt voor dit BOM artikel"

#: part/models.py:4467 stock/models.py:848
msgid "Quantity must be integer value for trackable parts"
msgstr "Hoeveelheid moet een geheel getal zijn voor trackable onderdelen"

#: part/models.py:4477 part/models.py:4479
msgid "Sub part must be specified"
msgstr "Onderdeel moet gespecificeerd worden"

#: part/models.py:4624
msgid "BOM Item Substitute"
msgstr "BOM Item vervangingen bewerken"

#: part/models.py:4645
msgid "Substitute part cannot be the same as the master part"
msgstr "Vervanging onderdeel kan niet hetzelfde zijn als het hoofddeel"

#: part/models.py:4658
msgid "Parent BOM item"
msgstr "Bovenliggend BOM item"

#: part/models.py:4666
msgid "Substitute part"
msgstr "Vervanging onderdeel"

#: part/models.py:4682
msgid "Part 1"
msgstr "Eerste deel"

#: part/models.py:4690
msgid "Part 2"
msgstr "Tweede deel"

#: part/models.py:4691
msgid "Select Related Part"
msgstr "Selecteer gerelateerd onderdeel"

#: part/models.py:4698
msgid "Note for this relationship"
msgstr "Opmerking voor deze relatie"

#: part/models.py:4717
msgid "Part relationship cannot be created between a part and itself"
msgstr "Onderdeel relatie kan niet worden gecreëerd tussen een deel en zichzelf"

#: part/models.py:4722
msgid "Duplicate relationship already exists"
msgstr "Dubbele relatie bestaat al"

#: part/serializers.py:125
msgid "Parent Category"
msgstr "Bovenliggende categorie"

#: part/serializers.py:126
msgid "Parent part category"
msgstr "Bovenliggende onderdeel categorie"

#: part/serializers.py:134 part/serializers.py:168
msgid "Subcategories"
msgstr "Subcategorieën"

#: part/serializers.py:207
msgid "Results"
msgstr "Resultaten"

#: part/serializers.py:208
msgid "Number of results recorded against this template"
msgstr "Aantal resultaten opgenomen ten opzichte van deze template"

#: part/serializers.py:235 part/serializers.py:253 stock/serializers.py:685
msgid "Purchase currency of this stock item"
msgstr "Inkooporder voor dit voorraadartikel"

#: part/serializers.py:278
msgid "Speculative Quantity"
msgstr "Speculatieve hoeveelheid"

#: part/serializers.py:287
msgid "Model ID"
msgstr "Model Id"

#: part/serializers.py:313
msgid "File is not an image"
msgstr "Bestand is geen afbeelding"

#: part/serializers.py:344
msgid "Number of parts using this template"
msgstr "Aantal onderdelen die deze sjabloon gebruiken"

#: part/serializers.py:489
msgid "Original Part"
msgstr "Oorspronkelijk onderdeel"

#: part/serializers.py:490
msgid "Select original part to duplicate"
msgstr "Selecteer origineel onderdeel om te dupliceren"

#: part/serializers.py:495
msgid "Copy Image"
msgstr "Afbeelding kopiëren"

#: part/serializers.py:496
msgid "Copy image from original part"
msgstr "Afbeelding kopiëren van het oorspronkelijke onderdeel"

#: part/serializers.py:502
msgid "Copy BOM"
msgstr "Copy BOM"

#: part/serializers.py:503
msgid "Copy bill of materials from original part"
msgstr "Kopieer materiaal van het oorspronkelijke deel"

#: part/serializers.py:509
msgid "Copy Parameters"
msgstr "Parameters kopiëren"

#: part/serializers.py:510
msgid "Copy parameter data from original part"
msgstr "Parameter data kopiëren van het originele onderdeel"

#: part/serializers.py:516
msgid "Copy Notes"
msgstr "Notities kopiëren"

#: part/serializers.py:517
msgid "Copy notes from original part"
msgstr "Kopieer notities van het originele deel"

#: part/serializers.py:535
msgid "Initial Stock Quantity"
msgstr "Eerste voorraad hoeveelheid"

#: part/serializers.py:537
msgid "Specify initial stock quantity for this Part. If quantity is zero, no stock is added."
msgstr "Specificeer de initiële voorraad hoeveelheid voor dit onderdeel. Als het aantal nul is, wordt er geen voorraad toegevoegd."

#: part/serializers.py:544
msgid "Initial Stock Location"
msgstr "Eerste voorraad locatie"

#: part/serializers.py:545
msgid "Specify initial stock location for this Part"
msgstr "Specificeer locatie van de eerste voorraad voor dit onderdeel"

#: part/serializers.py:562
msgid "Select supplier (or leave blank to skip)"
msgstr "Selecteer leverancier (of laat leeg om niets in te vullen)"

#: part/serializers.py:578
msgid "Select manufacturer (or leave blank to skip)"
msgstr "Selecteer fabrikant (of laat leeg om niets in te vullen)"

#: part/serializers.py:588
msgid "Manufacturer part number"
msgstr "Fabrikant artikel nummer"

#: part/serializers.py:595
msgid "Selected company is not a valid supplier"
msgstr "Geselecteerde onderneming is geen geldige leverancier"

#: part/serializers.py:604
msgid "Selected company is not a valid manufacturer"
msgstr "Geselecteerde bedrijf is geen geldige fabrikant"

#: part/serializers.py:615
msgid "Manufacturer part matching this MPN already exists"
msgstr "Fabrikant deel dat overeenkomt met deze MPN bestaat al"

#: part/serializers.py:622
msgid "Supplier part matching this SKU already exists"
msgstr "Leveranciersdeel dat overeenkomt met deze SKU bestaat al"

#: part/serializers.py:911 part/stocktake.py:222
msgid "Category Name"
msgstr "Categorie naam"

#: part/serializers.py:937
msgid "Building"
msgstr "Bouwen"

#: part/serializers.py:952 part/stocktake.py:223 stock/serializers.py:1084
#: stock/serializers.py:1260 users/ruleset.py:36
msgid "Stock Items"
msgstr "Voorraadartikelen"

#: part/serializers.py:955
msgid "Revisions"
msgstr "Revisies"

#: part/serializers.py:958
msgid "Suppliers"
msgstr "Leveranciers"

#: part/serializers.py:961 templates/email/low_stock_notification.html:16
#: templates/email/part_event_notification.html:17
msgid "Total Stock"
msgstr "Totale Voorraad"

#: part/serializers.py:967
msgid "Unallocated Stock"
msgstr "Niet toegewezen voorraad"

#: part/serializers.py:973
msgid "Variant Stock"
msgstr "Variant voorraad"

#: part/serializers.py:1006
msgid "Duplicate Part"
msgstr "Dupliceer onderdeel"

#: part/serializers.py:1007
msgid "Copy initial data from another Part"
msgstr "Kopieer eerste gegevens uit een ander onderdeel"

#: part/serializers.py:1013
msgid "Initial Stock"
msgstr "Eerste voorraad"

#: part/serializers.py:1014
msgid "Create Part with initial stock quantity"
msgstr "Maak onderdeel met eerste voorraad"

#: part/serializers.py:1020
msgid "Supplier Information"
msgstr "Leveranciersgegevens"

#: part/serializers.py:1021
msgid "Add initial supplier information for this part"
msgstr "Aanvankelijke leveranciersinformatie voor dit deel toevoegen"

#: part/serializers.py:1029
msgid "Copy Category Parameters"
msgstr "Categorie parameters kopiëren"

#: part/serializers.py:1030
msgid "Copy parameter templates from selected part category"
msgstr "Parameter sjablonen kopiëren uit geselecteerde onderdeel categorie"

#: part/serializers.py:1035
msgid "Existing Image"
msgstr "Bestaande afbeelding"

#: part/serializers.py:1036
msgid "Filename of an existing part image"
msgstr "Bestandsnaam van een bestaande onderdeel afbeelding"

#: part/serializers.py:1053
msgid "Image file does not exist"
msgstr "Afbeeldingsbestand bestaat niet"

#: part/serializers.py:1257
msgid "Limit stocktake report to a particular part, and any variant parts"
msgstr "Limiteer de voorraadrapportage tot een bepaald onderdeel en eventuele variant onderdelen"

#: part/serializers.py:1267
msgid "Limit stocktake report to a particular part category, and any child categories"
msgstr "Limiteer de voorraadrapportage tot een bepaalde deelcategorie en alle onderliggende categorieën"

#: part/serializers.py:1277
msgid "Limit stocktake report to a particular stock location, and any child locations"
msgstr "Limiteer de voorraadrapportage tot een bepaalde voorraadlocatie en alle onderliggende locaties"

#: part/serializers.py:1283
msgid "Exclude External Stock"
msgstr "Externe voorraad uitsluiten"

#: part/serializers.py:1284
msgid "Exclude stock items in external locations"
msgstr "Voorraadartikelen op externe locaties uitsluiten"

#: part/serializers.py:1289
msgid "Generate Report"
msgstr "Rapport genereren"

#: part/serializers.py:1290
msgid "Generate report file containing calculated stocktake data"
msgstr "Genereer een bestand met berekende voorraad namen gegevens"

#: part/serializers.py:1295
msgid "Update Parts"
msgstr "Onderdelen bijwerken"

#: part/serializers.py:1296
msgid "Update specified parts with calculated stocktake data"
msgstr "Bijwerken van de opgegeven onderdelen met berekende voorraad gegevens"

#: part/serializers.py:1304
msgid "Stocktake functionality is not enabled"
msgstr "Voorraadcontrole functionaliteit is niet ingeschakeld"

#: part/serializers.py:1309
msgid "Background worker check failed"
msgstr "Achtergrondwerker check is gefaald"

#: part/serializers.py:1425
msgid "Minimum Price"
msgstr "Minimale prijs"

#: part/serializers.py:1426
msgid "Override calculated value for minimum price"
msgstr "Overschrijf berekende waarde voor minimale prijs"

#: part/serializers.py:1433
msgid "Minimum price currency"
msgstr "Minimale prijs valuta"

#: part/serializers.py:1440
msgid "Maximum Price"
msgstr "Maximale prijs"

#: part/serializers.py:1441
msgid "Override calculated value for maximum price"
msgstr "Overschrijf de berekende waarde voor de maximale prijs"

#: part/serializers.py:1448
msgid "Maximum price currency"
msgstr "Maximale prijs valuta"

#: part/serializers.py:1477
msgid "Update"
msgstr "Bijwerken"

#: part/serializers.py:1478
msgid "Update pricing for this part"
msgstr "Prijzen voor dit onderdeel bijwerken"

#: part/serializers.py:1501
#, python-brace-format
msgid "Could not convert from provided currencies to {default_currency}"
msgstr "Kan niet converteren van de verstrekte valuta naar {default_currency}"

#: part/serializers.py:1508
msgid "Minimum price must not be greater than maximum price"
msgstr "Minimumprijs mag niet hoger zijn dan de maximale prijs"

#: part/serializers.py:1511
msgid "Maximum price must not be less than minimum price"
msgstr "Maximale prijs mag niet lager zijn dan de minimale prijs"

#: part/serializers.py:1664
msgid "Select the parent assembly"
msgstr "Selecteer de bovenliggende assemblage"

#: part/serializers.py:1678
msgid "Select the component part"
msgstr "Selecteer het onderdeel"

#: part/serializers.py:1698
msgid "Can Build"
msgstr "Kan bouwen"

#: part/serializers.py:1925
msgid "Select part to copy BOM from"
msgstr "Selecteer onderdeel om BOM van te kopiëren"

#: part/serializers.py:1933
msgid "Remove Existing Data"
msgstr "Bestaande gegevens verwijderen"

#: part/serializers.py:1934
msgid "Remove existing BOM items before copying"
msgstr "Verwijder bestaande BOM items voor het kopiëren"

#: part/serializers.py:1939
msgid "Include Inherited"
msgstr "Inclusief overgenomen"

#: part/serializers.py:1940
msgid "Include BOM items which are inherited from templated parts"
msgstr "Inclusief stuklijst BOM items die worden overgenomen van getemplated onderdelen"

#: part/serializers.py:1945
msgid "Skip Invalid Rows"
msgstr "Ongeldige regels overslaan"

#: part/serializers.py:1946
msgid "Enable this option to skip invalid rows"
msgstr "Schakel deze optie in om ongeldige rijen over te slaan"

#: part/serializers.py:1951
msgid "Copy Substitute Parts"
msgstr "Verwijder vervangend deel"

#: part/serializers.py:1952
msgid "Copy substitute parts when duplicate BOM items"
msgstr "Kopieer vervangende onderdelen bij dubbele stuklijst BOM items"

#: part/stocktake.py:218
msgid "Part ID"
msgstr "Onderdeel-id"

#: part/stocktake.py:220
msgid "Part Description"
msgstr "Onderdeel omschrijving"

#: part/stocktake.py:221
msgid "Category ID"
msgstr "Categorie ID"

#: part/stocktake.py:224
msgid "Total Quantity"
msgstr "Totale hoeveelheid"

#: part/stocktake.py:225
msgid "Total Cost Min"
msgstr "Totale kosten Min"

#: part/stocktake.py:226
msgid "Total Cost Max"
msgstr "Totale kosten Max"

#: part/stocktake.py:284
msgid "Stocktake Report Available"
msgstr "Voorraadcontrole rapport beschikbaar"

#: part/stocktake.py:285
msgid "A new stocktake report is available for download"
msgstr "Een nieuwe voorraadrapportage is beschikbaar voor download"

#: part/tasks.py:38
msgid "Low stock notification"
msgstr "Lage voorraad melding"

#: part/tasks.py:40
#, python-brace-format
msgid "The available stock for {part.name} has fallen below the configured minimum level"
msgstr "De beschikbare voorraad voor {part.name} is onder het ingestelde minimumniveau gedaald"

#: plugin/api.py:78
msgid "Builtin"
msgstr "Ingebouwd"

#: plugin/api.py:92
msgid "Mandatory"
msgstr "verplicht"

#: plugin/api.py:103
msgid "Sample"
msgstr "Voorbeeld"

#: plugin/api.py:117 plugin/models.py:159
msgid "Installed"
msgstr "Geïnstalleerd"

#: plugin/api.py:184
msgid "Plugin cannot be deleted as it is currently active"
msgstr "De plug-in kan niet worden verwijderd omdat deze momenteel actief is"

#: plugin/base/action/api.py:34
msgid "No action specified"
msgstr "Geen actie gespecificeerd"

#: plugin/base/action/api.py:46
msgid "No matching action found"
msgstr "Geen overeenkomende actie gevonden"

#: plugin/base/barcodes/api.py:211
msgid "No match found for barcode data"
msgstr "Geen overeenkomst gevonden voor streepjescodegegevens"

#: plugin/base/barcodes/api.py:215
msgid "Match found for barcode data"
msgstr "Overeenkomst gevonden voor streepjescodegegevens"

#: plugin/base/barcodes/api.py:253 plugin/base/barcodes/serializers.py:77
msgid "Model is not supported"
msgstr "Model wordt niet ondersteund"

#: plugin/base/barcodes/api.py:258
msgid "Model instance not found"
msgstr "Model instantie niet gevonden"

#: plugin/base/barcodes/api.py:287
msgid "Barcode matches existing item"
msgstr "Barcode komt overeen met bestaand item"

#: plugin/base/barcodes/api.py:418
msgid "No matching part data found"
msgstr "Geen overeenkomende actie gevonden"

#: plugin/base/barcodes/api.py:434
msgid "No matching supplier parts found"
msgstr "Geen overeenkomende leveranciers onderdelen gevonden"

#: plugin/base/barcodes/api.py:437
msgid "Multiple matching supplier parts found"
msgstr "Meerdere overeenkomende leveranciers onderdelen gevonden"

#: plugin/base/barcodes/api.py:450 plugin/base/barcodes/api.py:677
msgid "No matching plugin found for barcode data"
msgstr "Geen overeenkomende plug-in gevonden voor barcode gegevens"

#: plugin/base/barcodes/api.py:460
msgid "Matched supplier part"
msgstr "Overeenkomende leverancier onderdeel"

#: plugin/base/barcodes/api.py:528
msgid "Item has already been received"
msgstr "Regel item is al ontvangen"

#: plugin/base/barcodes/api.py:576
msgid "No plugin match for supplier barcode"
msgstr "Er komt geen plug-in overeen met de barcode van de leverancier"

#: plugin/base/barcodes/api.py:625
msgid "Multiple matching line items found"
msgstr "Meerdere overeenkomende regelitems gevonden"

#: plugin/base/barcodes/api.py:628
msgid "No matching line item found"
msgstr "Geen overeenkomend voorraaditem gevonden"

#: plugin/base/barcodes/api.py:674
msgid "No sales order provided"
msgstr "Geen verkooporder opgegeven"

#: plugin/base/barcodes/api.py:683
msgid "Barcode does not match an existing stock item"
msgstr "Streepjescode komt niet overeen met een bestaand voorraadartikel"

#: plugin/base/barcodes/api.py:699
msgid "Stock item does not match line item"
msgstr "Voorraad item komt niet overeen met regelitem"

#: plugin/base/barcodes/api.py:729
msgid "Insufficient stock available"
msgstr "Onvoldoende voorraad beschikbaar"

#: plugin/base/barcodes/api.py:742
msgid "Stock item allocated to sales order"
msgstr "Voorraad item toegewezen aan verkooporder"

#: plugin/base/barcodes/api.py:745
msgid "Not enough information"
msgstr "Te weinig informatie"

#: plugin/base/barcodes/mixins.py:308
#: plugin/builtin/barcodes/inventree_barcode.py:101
msgid "Found matching item"
msgstr "Gevonden overeenkomend item"

#: plugin/base/barcodes/mixins.py:374
msgid "Supplier part does not match line item"
msgstr "Leveranciersdeel komt niet overeen met regelitem"

#: plugin/base/barcodes/mixins.py:377
msgid "Line item is already completed"
msgstr "Regelitem is al voltooid"

#: plugin/base/barcodes/mixins.py:414
msgid "Further information required to receive line item"
msgstr "Verdere informatie vereist om regelitem te ontvangen"

#: plugin/base/barcodes/mixins.py:422
msgid "Received purchase order line item"
msgstr "Inkoopregel ontvangen"

#: plugin/base/barcodes/mixins.py:429
msgid "Failed to receive line item"
msgstr "Kon geen regelitem ontvangen"

#: plugin/base/barcodes/serializers.py:53
msgid "Scanned barcode data"
msgstr "Gescande barcode gegevens"

#: plugin/base/barcodes/serializers.py:62
msgid "Model name to generate barcode for"
msgstr "Modelnaam om een streepjescode te genereren voor"

#: plugin/base/barcodes/serializers.py:67
msgid "Primary key of model object to generate barcode for"
msgstr "Primaire sleutel van modelobject om barcode te genereren voor"

#: plugin/base/barcodes/serializers.py:137
msgid "Purchase Order to allocate items against"
msgstr "Inkooporder om items toe te wijzen"

#: plugin/base/barcodes/serializers.py:143
msgid "Purchase order is not open"
msgstr "Inkooporder is niet open"

#: plugin/base/barcodes/serializers.py:161
msgid "Supplier to receive items from"
msgstr "Leverancier om items te ontvangen van"

#: plugin/base/barcodes/serializers.py:168
msgid "PurchaseOrder to receive items against"
msgstr "Inkooporder om items tegen te ontvangen"

#: plugin/base/barcodes/serializers.py:174
msgid "Purchase order has not been placed"
msgstr "Inkooporder is niet geplaatst"

#: plugin/base/barcodes/serializers.py:182
msgid "Location to receive items into"
msgstr "Locatie voor het ontvangen van items"

#: plugin/base/barcodes/serializers.py:188
msgid "Cannot select a structural location"
msgstr "Kan geen structurele locatie selecteren"

#: plugin/base/barcodes/serializers.py:196
msgid "Purchase order line item to receive items against"
msgstr "Inkooporder regel item om items tegen te ontvangen"

#: plugin/base/barcodes/serializers.py:202
msgid "Automatically allocate stock items to the purchase order"
msgstr "Voorraadartikelen automatisch toewijzen aan de inkooporder"

#: plugin/base/barcodes/serializers.py:215
msgid "Sales Order to allocate items against"
msgstr "Verkooporder om items toe te wijzen tegen"

#: plugin/base/barcodes/serializers.py:221
msgid "Sales order is not open"
msgstr "Verkooporder is niet open"

#: plugin/base/barcodes/serializers.py:229
msgid "Sales order line item to allocate items against"
msgstr "Verkoop bestelling regel item om artikelen tegen toe te wijzen"

#: plugin/base/barcodes/serializers.py:236
msgid "Sales order shipment to allocate items against"
msgstr "Verkoop bestelling om items toe te wijzen aan"

#: plugin/base/barcodes/serializers.py:242
msgid "Shipment has already been delivered"
msgstr "Zending is al geleverd"

#: plugin/base/barcodes/serializers.py:247
msgid "Quantity to allocate"
msgstr "Toe te wijzen hoeveelheid"

#: plugin/base/label/label.py:39
msgid "Label printing failed"
msgstr "Label afdrukken mislukt"

#: plugin/base/label/mixins.py:55
msgid "Error rendering label to PDF"
msgstr "Fout bij het renderen label naar PDF"

#: plugin/base/label/mixins.py:69
msgid "Error rendering label to HTML"
msgstr "Fout bij het renderen van label naar HTML"

#: plugin/base/label/mixins.py:146
msgid "No items provided to print"
msgstr "Geen producten aangeboden om af te drukken"

#: plugin/base/ui/serializers.py:30
msgid "Plugin Name"
msgstr "Plug-in naam"

#: plugin/base/ui/serializers.py:34
msgid "Feature Type"
msgstr "Kenmerk type"

#: plugin/base/ui/serializers.py:39
msgid "Feature Label"
msgstr "Feature label"

#: plugin/base/ui/serializers.py:44
msgid "Feature Title"
msgstr "Titel feature"

#: plugin/base/ui/serializers.py:49
msgid "Feature Description"
msgstr "Kenmerk beschrijving"

#: plugin/base/ui/serializers.py:54
msgid "Feature Icon"
msgstr "Feature pictogram"

#: plugin/base/ui/serializers.py:58
msgid "Feature Options"
msgstr "Feature opties"

#: plugin/base/ui/serializers.py:61
msgid "Feature Context"
msgstr "Feature context"

#: plugin/base/ui/serializers.py:64
msgid "Feature Source (javascript)"
msgstr "Feature bron (javascript)"

#: plugin/builtin/barcodes/inventree_barcode.py:27
msgid "InvenTree Barcodes"
msgstr "InvenTree barcodes"

#: plugin/builtin/barcodes/inventree_barcode.py:28
msgid "Provides native support for barcodes"
msgstr "Biedt ondersteuning voor barcodes"

#: plugin/builtin/barcodes/inventree_barcode.py:30
#: plugin/builtin/exporter/bom_exporter.py:61
#: plugin/builtin/exporter/inventree_exporter.py:17
#: plugin/builtin/exporter/part_parameter_exporter.py:36
#: plugin/builtin/integration/core_notifications.py:33
#: plugin/builtin/integration/currency_exchange.py:21
#: plugin/builtin/integration/part_notifications.py:25
#: plugin/builtin/labels/inventree_label.py:26
#: plugin/builtin/labels/inventree_machine.py:64
#: plugin/builtin/labels/label_sheet.py:71
#: plugin/builtin/suppliers/digikey.py:19 plugin/builtin/suppliers/lcsc.py:21
#: plugin/builtin/suppliers/mouser.py:19 plugin/builtin/suppliers/tme.py:21
msgid "InvenTree contributors"
msgstr "InvenTree bijdragers"

#: plugin/builtin/barcodes/inventree_barcode.py:34
msgid "Internal Barcode Format"
msgstr "Interne barcode formaat"

#: plugin/builtin/barcodes/inventree_barcode.py:35
msgid "Select an internal barcode format"
msgstr "Selecteer een interne streepjescode formaat"

#: plugin/builtin/barcodes/inventree_barcode.py:37
msgid "JSON barcodes (human readable)"
msgstr "JSON barcodes (menselijk leesbaar)"

#: plugin/builtin/barcodes/inventree_barcode.py:38
msgid "Short barcodes (space optimized)"
msgstr "Korte barcodes (ruimte geoptimaliseerd)"

#: plugin/builtin/barcodes/inventree_barcode.py:43
msgid "Short Barcode Prefix"
msgstr "Korte barcode voorvoegsel"

#: plugin/builtin/barcodes/inventree_barcode.py:45
msgid "Customize the prefix used for short barcodes, may be useful for environments with multiple InvenTree instances"
msgstr "Aanpassen van prefix voor korte streepjescodes, kan handig zijn voor omgevingen met meerdere InvenTree instanties"

#: plugin/builtin/exporter/bom_exporter.py:17
msgid "Levels"
msgstr "Niveau"

#: plugin/builtin/exporter/bom_exporter.py:18
msgid "Number of levels to export"
msgstr "Aantal niveaus om te exporteren"

#: plugin/builtin/exporter/bom_exporter.py:23
#: plugin/builtin/exporter/part_parameter_exporter.py:17
msgid "Stock Data"
msgstr "Voorraad gegevens"

#: plugin/builtin/exporter/bom_exporter.py:23
#: plugin/builtin/exporter/part_parameter_exporter.py:17
msgid "Include part stock data"
msgstr "Inclusief voorraadgegevens"

#: plugin/builtin/exporter/bom_exporter.py:27
#: plugin/builtin/exporter/part_parameter_exporter.py:21
msgid "Pricing Data"
msgstr "Prijs gegevens"

#: plugin/builtin/exporter/bom_exporter.py:27
#: plugin/builtin/exporter/part_parameter_exporter.py:21
msgid "Include part pricing data"
msgstr "Inclusief prijsgegevens"

#: plugin/builtin/exporter/bom_exporter.py:31
msgid "Supplier Data"
msgstr "Leveranciersgegevens"

#: plugin/builtin/exporter/bom_exporter.py:31
msgid "Include supplier data"
msgstr "Inclusief leveranciersgegevens"

#: plugin/builtin/exporter/bom_exporter.py:36
msgid "Manufacturer Data"
msgstr "Fabrikant gegevens"

#: plugin/builtin/exporter/bom_exporter.py:37
msgid "Include manufacturer data"
msgstr "Inclusief fabrikant gegevens"

#: plugin/builtin/exporter/bom_exporter.py:42
msgid "Substitute Data"
msgstr "Vervang Data"

#: plugin/builtin/exporter/bom_exporter.py:43
msgid "Include substitute part data"
msgstr "Voeg vervangende deelgegevens toe"

#: plugin/builtin/exporter/bom_exporter.py:48
msgid "Parameter Data"
msgstr "Parameter gegevens"

#: plugin/builtin/exporter/bom_exporter.py:49
msgid "Include part parameter data"
msgstr "Parametergegevens van onderdeel opnemen"

#: plugin/builtin/exporter/bom_exporter.py:58
msgid "Multi-Level BOM Exporter"
msgstr "Meerdere niveau BOM export"

#: plugin/builtin/exporter/bom_exporter.py:59
msgid "Provides support for exporting multi-level BOMs"
msgstr "Biedt ondersteuning voor het exporteren van multi-level BOMs"

#: plugin/builtin/exporter/bom_exporter.py:96
msgid "BOM Level"
msgstr "BOM niveau"

#: plugin/builtin/exporter/bom_exporter.py:102
#, python-brace-format
msgid "Substitute {n}"
msgstr "Vervanging {n}"

#: plugin/builtin/exporter/bom_exporter.py:108
#, python-brace-format
msgid "Supplier {n}"
msgstr "Leverancier {n}"

#: plugin/builtin/exporter/bom_exporter.py:109
#, python-brace-format
msgid "Supplier {n} SKU"
msgstr "Leverancier {n} SKU"

#: plugin/builtin/exporter/bom_exporter.py:110
#, python-brace-format
msgid "Supplier {n} MPN"
msgstr "Leverancier {n} MPN"

#: plugin/builtin/exporter/bom_exporter.py:116
#, python-brace-format
msgid "Manufacturer {n}"
msgstr "Fabrikant {n}"

#: plugin/builtin/exporter/bom_exporter.py:117
#, python-brace-format
msgid "Manufacturer {n} MPN"
msgstr "Fabrikant {n} MPN"

#: plugin/builtin/exporter/inventree_exporter.py:14
msgid "InvenTree Generic Exporter"
msgstr "InvenTree generieke exporteur"

#: plugin/builtin/exporter/inventree_exporter.py:15
msgid "Provides support for exporting data from InvenTree"
msgstr "Biedt ondersteuning voor het exporteren van gegevens van InvenTree"

#: plugin/builtin/exporter/part_parameter_exporter.py:33
msgid "Part Parameter Exporter"
msgstr "Exporteer onderdeel parameter"

#: plugin/builtin/exporter/part_parameter_exporter.py:34
msgid "Exporter for part parameter data"
msgstr "Exporteerder voor gegevens van gedeeltelijke parameter"

#: plugin/builtin/integration/core_notifications.py:32
msgid "InvenTree Notifications"
msgstr "InvenTree notificaties"

#: plugin/builtin/integration/core_notifications.py:34
msgid "Integrated outgoing notification methods"
msgstr "Geïntegreerde uitgaande notificatie methodes"

#: plugin/builtin/integration/core_notifications.py:39
#: plugin/builtin/integration/core_notifications.py:78
msgid "Enable email notifications"
msgstr "Email meldingen inschakelen"

#: plugin/builtin/integration/core_notifications.py:40
#: plugin/builtin/integration/core_notifications.py:79
msgid "Allow sending of emails for event notifications"
msgstr "Versturen van e-mails voor event meldingen toestaan"

#: plugin/builtin/integration/core_notifications.py:45
msgid "Enable slack notifications"
msgstr "Activeer slack notificaties"

#: plugin/builtin/integration/core_notifications.py:47
msgid "Allow sending of slack channel messages for event notifications"
msgstr "Versturen van slack kanaal berichten voor event meldingen toestaan"

#: plugin/builtin/integration/core_notifications.py:53
msgid "Slack incoming webhook url"
msgstr "Slack inkomende webhook url"

#: plugin/builtin/integration/core_notifications.py:54
msgid "URL that is used to send messages to a slack channel"
msgstr "URL die wordt gebruikt om berichten te verzenden naar een slack kanaal"

#: plugin/builtin/integration/core_notifications.py:164
msgid "Open link"
msgstr "Open link"

#: plugin/builtin/integration/currency_exchange.py:22
msgid "InvenTree Currency Exchange"
msgstr "InvenTree valuta wisselkoers"

#: plugin/builtin/integration/currency_exchange.py:23
msgid "Default currency exchange integration"
msgstr "Standaard munteenheden omwisselen integratie"

#: plugin/builtin/integration/part_notifications.py:24
msgid "Part Notifications"
msgstr "Meldingen voor onderdelen"

#: plugin/builtin/integration/part_notifications.py:26
msgid "Notify users about part changes"
msgstr "Gebruikers inlichten over wijzigingen in onderdelen"

#: plugin/builtin/integration/part_notifications.py:31
msgid "Send notifications"
msgstr "Meldingen verzenden"

#: plugin/builtin/integration/part_notifications.py:32
msgid "Send notifications for part changes to subscribed users"
msgstr "Stuur meldingen van wijzigingen in onderdelen aan geabonneerde gebruikers"

#: plugin/builtin/integration/part_notifications.py:49
msgid "Changed part notification"
msgstr "Melding gewijzigd onderdeel"

#: plugin/builtin/integration/part_notifications.py:59
#, python-brace-format
msgid "The part `{part.name}` has been triggered with a `{part_action}` event"
msgstr "Het deel `{part.name}` is geactiveerd met een `{part_action}` event"

#: plugin/builtin/labels/inventree_label.py:23
msgid "InvenTree PDF label printer"
msgstr "InvenTree PDF label printer"

#: plugin/builtin/labels/inventree_label.py:24
msgid "Provides native support for printing PDF labels"
msgstr "Biedt ondersteuning voor het drukken van PDF labels"

#: plugin/builtin/labels/inventree_label.py:32
#: plugin/builtin/labels/label_sheet.py:77
msgid "Debug mode"
msgstr "Foutopsporing modus"

#: plugin/builtin/labels/inventree_label.py:33
#: plugin/builtin/labels/label_sheet.py:78
msgid "Enable debug mode - returns raw HTML instead of PDF"
msgstr "Schakel debug modus in - retourneert ruwe HTML in plaats van PDF"

#: plugin/builtin/labels/inventree_machine.py:61
msgid "InvenTree machine label printer"
msgstr "InvenTree machine label printer"

#: plugin/builtin/labels/inventree_machine.py:62
msgid "Provides support for printing using a machine"
msgstr "Biedt ondersteuning voor het printen met een machine"

#: plugin/builtin/labels/inventree_machine.py:164
msgid "last used"
msgstr "laatst gebruikt"

#: plugin/builtin/labels/inventree_machine.py:181
msgid "Options"
msgstr "Opties"

#: plugin/builtin/labels/label_sheet.py:30
msgid "Page size for the label sheet"
msgstr "Paginaformaat voor het label blad"

#: plugin/builtin/labels/label_sheet.py:35
msgid "Skip Labels"
msgstr "Labels overslaan"

#: plugin/builtin/labels/label_sheet.py:36
msgid "Skip this number of labels when printing label sheets"
msgstr "Dit aantal labels bij het afdrukken van labels overslaan"

#: plugin/builtin/labels/label_sheet.py:42
msgid "Border"
msgstr "Rand"

#: plugin/builtin/labels/label_sheet.py:43
msgid "Print a border around each label"
msgstr "Print een rand rond elk label"

#: plugin/builtin/labels/label_sheet.py:48 report/models.py:371
msgid "Landscape"
msgstr "Liggend"

#: plugin/builtin/labels/label_sheet.py:49
msgid "Print the label sheet in landscape mode"
msgstr "Print het label blad in liggende modus"

#: plugin/builtin/labels/label_sheet.py:54
msgid "Page Margin"
msgstr ""

#: plugin/builtin/labels/label_sheet.py:55
msgid "Margin around the page in mm"
msgstr ""

#: plugin/builtin/labels/label_sheet.py:68
msgid "InvenTree Label Sheet Printer"
msgstr "InvenTree label plaat printer"

#: plugin/builtin/labels/label_sheet.py:69
msgid "Arrays multiple labels onto a single sheet"
msgstr "Arrays multiple labels op een enkele plaat"

#: plugin/builtin/labels/label_sheet.py:121
msgid "Label is too large for page size"
msgstr "Label is te groot voor pagina grootte"

#: plugin/builtin/labels/label_sheet.py:160
msgid "No labels were generated"
msgstr "Geen labels gegenereerd"

#: plugin/builtin/suppliers/digikey.py:16
msgid "Supplier Integration - DigiKey"
msgstr "Integratie met leverancier - DigiKey"

#: plugin/builtin/suppliers/digikey.py:17
msgid "Provides support for scanning DigiKey barcodes"
msgstr "Biedt ondersteuning voor het scannen van DigiKey barcodes"

#: plugin/builtin/suppliers/digikey.py:26
msgid "The Supplier which acts as 'DigiKey'"
msgstr "De leverancier die als 'DigiKey' fungeert"

#: plugin/builtin/suppliers/lcsc.py:18
msgid "Supplier Integration - LCSC"
msgstr "Integratie met leveranciers - LCSC"

#: plugin/builtin/suppliers/lcsc.py:19
msgid "Provides support for scanning LCSC barcodes"
msgstr "Biedt ondersteuning voor het scannen van LCSC-barcodes"

#: plugin/builtin/suppliers/lcsc.py:27
msgid "The Supplier which acts as 'LCSC'"
msgstr "De leverancier die fungeert als 'LCSC'"

#: plugin/builtin/suppliers/mouser.py:16
msgid "Supplier Integration - Mouser"
msgstr "Integratie met leverancier - Mouser"

#: plugin/builtin/suppliers/mouser.py:17
msgid "Provides support for scanning Mouser barcodes"
msgstr "Biedt ondersteuning voor het scannen van Mouser barcodes"

#: plugin/builtin/suppliers/mouser.py:25
msgid "The Supplier which acts as 'Mouser'"
msgstr "De leverancier die als 'Mouser' optreedt"

#: plugin/builtin/suppliers/tme.py:18
msgid "Supplier Integration - TME"
msgstr "Integratie met leverancier - TME"

#: plugin/builtin/suppliers/tme.py:19
msgid "Provides support for scanning TME barcodes"
msgstr "Biedt ondersteuning voor het scannen van TME barcodes  "

#: plugin/builtin/suppliers/tme.py:27
msgid "The Supplier which acts as 'TME'"
msgstr "De leverancier die als \"TME” optreedt "

#: plugin/installer.py:229 plugin/installer.py:307
msgid "Only staff users can administer plugins"
msgstr "Alleen medewerker gebruikers kunnen plug-ins beheren"

#: plugin/installer.py:232
msgid "Plugin installation is disabled"
msgstr "Plug-in installatie is uitgeschakeld"

#: plugin/installer.py:269
msgid "Installed plugin successfully"
msgstr "Plug-in succesvol geïnstalleerd"

#: plugin/installer.py:274
#, python-brace-format
msgid "Installed plugin into {path}"
msgstr "Plug-in geïnstalleerd in {path}"

#: plugin/installer.py:298
msgid "Plugin was not found in registry"
msgstr "De plug-in is niet gevonden in het register"

#: plugin/installer.py:301
msgid "Plugin is not a packaged plugin"
msgstr "De plug-in is geen verpakte plug-in"

#: plugin/installer.py:304
msgid "Plugin package name not found"
msgstr "Naam van plug-in pakket niet gevonden"

#: plugin/installer.py:324
msgid "Plugin uninstalling is disabled"
msgstr "Verwijderen van plug-in is uitgeschakeld"

#: plugin/installer.py:328
msgid "Plugin cannot be uninstalled as it is currently active"
msgstr "De plug-in kan niet worden verwijderd omdat deze momenteel actief is"

#: plugin/installer.py:332
msgid "Plugin is not installed"
msgstr "De plug-in is niet geïnstalleerd"

#: plugin/installer.py:348
msgid "Plugin installation not found"
msgstr "Plug-in installatie niet gevonden"

#: plugin/installer.py:363
msgid "Uninstalled plugin successfully"
msgstr "Deïnstalleerde plug-in succesvol"

#: plugin/models.py:38
msgid "Plugin Configuration"
msgstr "Plug-in configuratie"

#: plugin/models.py:39
msgid "Plugin Configurations"
msgstr "Plug-in configuratie"

#: plugin/models.py:46
msgid "Key of plugin"
msgstr "Sleutel van plug-in"

#: plugin/models.py:54
msgid "PluginName of the plugin"
msgstr "Plugin naam van de plug-in"

#: plugin/models.py:61 plugin/serializers.py:119
msgid "Package Name"
msgstr "Pakket naam"

#: plugin/models.py:63
msgid "Name of the installed package, if the plugin was installed via PIP"
msgstr "Naam van het geïnstalleerde pakket, als de plug-in is geïnstalleerd via PIP"

#: plugin/models.py:68
msgid "Is the plugin active"
msgstr "Is de plug-in actief"

#: plugin/models.py:168
msgid "Sample plugin"
msgstr "Voorbeeld plug-in"

#: plugin/models.py:176
msgid "Builtin Plugin"
msgstr "Ingebouwde plug-in"

#: plugin/models.py:184
msgid "Mandatory Plugin"
msgstr "Mandatory Plugin"

#: plugin/models.py:189
msgid "Package Plugin"
msgstr "Pakket plug-in"

#: plugin/models.py:268
msgid "Plugin"
msgstr "Plug-in"

#: plugin/models.py:315
msgid "Method"
msgstr "Methode"

#: plugin/plugin.py:312
msgid "No author found"
msgstr "Geen auteur gevonden"

#: plugin/registry.py:611
#, python-brace-format
msgid "Plugin '{p}' is not compatible with the current InvenTree version {v}"
msgstr "De plug-in '{p}' is niet compatibel met de huidige InvenTree versie {v}"

#: plugin/registry.py:614
#, python-brace-format
msgid "Plugin requires at least version {v}"
msgstr "De plug-in vereist minimaal versie {v}"

#: plugin/registry.py:616
#, python-brace-format
msgid "Plugin requires at most version {v}"
msgstr "Plug-in vereist op de hoogste versie {v}"

#: plugin/samples/integration/sample.py:52
msgid "Enable PO"
msgstr "PO inschakelen"

#: plugin/samples/integration/sample.py:53
msgid "Enable PO functionality in InvenTree interface"
msgstr "PO functionaliteit inschakelen in de InvenTree interface"

#: plugin/samples/integration/sample.py:58
msgid "API Key"
msgstr "API-sleutel"

#: plugin/samples/integration/sample.py:59
msgid "Key required for accessing external API"
msgstr "Sleutel vereist voor toegang tot externe API"

#: plugin/samples/integration/sample.py:63
msgid "Numerical"
msgstr "Numeriek"

#: plugin/samples/integration/sample.py:64
msgid "A numerical setting"
msgstr "Een numerieke instelling"

#: plugin/samples/integration/sample.py:69
msgid "Choice Setting"
msgstr "Keuze instellingen"

#: plugin/samples/integration/sample.py:70
msgid "A setting with multiple choices"
msgstr "Een instelling met meerdere keuzes"

#: plugin/samples/integration/sample_currency_exchange.py:15
msgid "Sample currency exchange plugin"
msgstr "Voorbeeld valuta uitwisselings plug-in"

#: plugin/samples/integration/sample_currency_exchange.py:18
msgid "InvenTree Contributors"
msgstr "InvenTree bijdragers"

#: plugin/samples/integration/user_interface_sample.py:27
msgid "Enable Part Panels"
msgstr "Onderdeel panelen inschakelen"

#: plugin/samples/integration/user_interface_sample.py:28
msgid "Enable custom panels for Part views"
msgstr "Schakel aangepaste panelen in voor deelweergave"

#: plugin/samples/integration/user_interface_sample.py:33
msgid "Enable Purchase Order Panels"
msgstr "Schakel order panelen in"

#: plugin/samples/integration/user_interface_sample.py:34
msgid "Enable custom panels for Purchase Order views"
msgstr "Schakel aangepaste panelen in voor inkooporders"

#: plugin/samples/integration/user_interface_sample.py:39
msgid "Enable Broken Panels"
msgstr "Kapotte panelen inschakelen"

#: plugin/samples/integration/user_interface_sample.py:40
msgid "Enable broken panels for testing"
msgstr "Schakel defecte panelen in voor testen"

#: plugin/samples/integration/user_interface_sample.py:45
msgid "Enable Dynamic Panel"
msgstr "Dynamisch paneel inschakelen"

#: plugin/samples/integration/user_interface_sample.py:46
msgid "Enable dynamic panels for testing"
msgstr "Activeer dynamische panelen om te testen"

#: plugin/samples/integration/user_interface_sample.py:98
msgid "Part Panel"
msgstr "Deel paneel"

#: plugin/samples/integration/user_interface_sample.py:131
msgid "Broken Dashboard Item"
msgstr "Kapot dashboard item"

#: plugin/samples/integration/user_interface_sample.py:133
msgid "This is a broken dashboard item - it will not render!"
msgstr "Dit is een gebroken dashboarditem - het zal niet meer renderen!"

#: plugin/samples/integration/user_interface_sample.py:139
msgid "Sample Dashboard Item"
msgstr "Voorbeeld dashboard item"

#: plugin/samples/integration/user_interface_sample.py:141
msgid "This is a sample dashboard item. It renders a simple string of HTML content."
msgstr "Dit is een voorbeeld dashboard item. Het maakt een eenvoudige string van HTML inhoud weer."

#: plugin/samples/integration/user_interface_sample.py:147
msgid "Context Dashboard Item"
msgstr "Context dashboard item"

#: plugin/samples/integration/user_interface_sample.py:161
msgid "Admin Dashboard Item"
msgstr "Beheerder dashboard item"

#: plugin/samples/integration/user_interface_sample.py:162
msgid "This is an admin-only dashboard item."
msgstr "Dit is een enkel dashboard item voor admins."

#: plugin/serializers.py:86
msgid "Source File"
msgstr "Bron bestand"

#: plugin/serializers.py:87
msgid "Path to the source file for admin integration"
msgstr "Pad naar het bronbestand voor administrator integratie"

#: plugin/serializers.py:94
msgid "Optional context data for the admin integration"
msgstr "Optionele context data voor de administrator integratie"

#: plugin/serializers.py:110
msgid "Source URL"
msgstr "Bron URL"

#: plugin/serializers.py:112
msgid "Source for the package - this can be a custom registry or a VCS path"
msgstr "Bron voor het pakket - dit kan een aangepast register of een VCS pad zijn"

#: plugin/serializers.py:121
msgid "Name for the Plugin Package - can also contain a version indicator"
msgstr "Naam voor het Plug-in Pakket - kan ook een versie indicator bevatten"

#: plugin/serializers.py:128
msgid "Version"
msgstr "Versie"

#: plugin/serializers.py:130
msgid "Version specifier for the plugin. Leave blank for latest version."
msgstr "Versie specifier voor de plug-in. Laat leeg voor de laatste versie."

#: plugin/serializers.py:135
msgid "Confirm plugin installation"
msgstr "Plug-in activeren bevestigen"

#: plugin/serializers.py:137
msgid "This will install this plugin now into the current instance. The instance will go into maintenance."
msgstr "Dit zal de plug-in nu installeren in de huidige instantie. De instantie zal in onderhoud gaan."

#: plugin/serializers.py:150
msgid "Installation not confirmed"
msgstr "Installatie niet bevestigd"

#: plugin/serializers.py:152
msgid "Either packagename of URL must be provided"
msgstr "Ofwel de pakketnaam van de URL moet worden opgegeven"

#: plugin/serializers.py:188
msgid "Full reload"
msgstr "Volledige herladen"

#: plugin/serializers.py:189
msgid "Perform a full reload of the plugin registry"
msgstr "Herlaad het plug-in register volledig"

#: plugin/serializers.py:195
msgid "Force reload"
msgstr "Herladen forceren"

#: plugin/serializers.py:197
msgid "Force a reload of the plugin registry, even if it is already loaded"
msgstr "Forceer herladen van het plug-in register, zelfs als het al geladen is"

#: plugin/serializers.py:204
msgid "Collect plugins"
msgstr "Plug-ins ophalen"

#: plugin/serializers.py:205
msgid "Collect plugins and add them to the registry"
msgstr "Verzamel plug-ins en voeg ze toe aan het register"

#: plugin/serializers.py:232
msgid "Activate Plugin"
msgstr "Activeer plug-in"

#: plugin/serializers.py:233
msgid "Activate this plugin"
msgstr "Deze plug-in activeren"

#: plugin/serializers.py:253
msgid "Delete configuration"
msgstr "Configuratie verwijderen"

#: plugin/serializers.py:254
msgid "Delete the plugin configuration from the database"
msgstr "Verwijder de plug-in configuratie uit de database"

#: report/api.py:44 report/serializers.py:98 report/serializers.py:148
msgid "Items"
msgstr "Items"

#: report/api.py:121
msgid "Plugin not found"
msgstr "Plug-in niet gevonden"

#: report/api.py:123
msgid "Plugin is not active"
msgstr "Plug-in is niet actief"

#: report/api.py:125
msgid "Plugin does not support label printing"
msgstr "Plug-in ondersteunt geen label printen"

#: report/api.py:173
msgid "Invalid label dimensions"
msgstr "Ongeldige label afmetingen"

#: report/api.py:188 report/api.py:267
msgid "No valid items provided to template"
msgstr "Geen geldige items aan de template verstrekt"

#: report/helpers.py:43
msgid "A4"
msgstr "A4"

#: report/helpers.py:44
msgid "A3"
msgstr "A3"

#: report/helpers.py:45
msgid "Legal"
msgstr "Juridisch"

#: report/helpers.py:46
msgid "Letter"
msgstr "Brief"

#: report/models.py:117
msgid "Template file with this name already exists"
msgstr "Sjabloonbestand met deze naam bestaat al"

#: report/models.py:204
msgid "Template name"
msgstr "Template naam"

#: report/models.py:210
msgid "Template description"
msgstr "Template beschrijving"

#: report/models.py:216
msgid "Revision number (auto-increments)"
msgstr "Revisie nummer (auto verhogen)"

#: report/models.py:222
msgid "Attach to Model on Print"
msgstr "Bevestig aan het model bij afdrukken"

#: report/models.py:224
msgid "Save report output as an attachment against linked model instance when printing"
msgstr "Sla rapport output op als bijlage ten opzichte van gekoppelde model instantie bij afdrukken"

#: report/models.py:265
msgid "Filename Pattern"
msgstr "Bestandsnaam Patroon"

#: report/models.py:266
msgid "Pattern for generating filenames"
msgstr "Patroon voor het genereren van bestandsnamen"

#: report/models.py:271
msgid "Template is enabled"
msgstr "Template is ingeschakeld"

#: report/models.py:278
msgid "Target model type for template"
msgstr "Doel type model voor sjabloon"

#: report/models.py:298
msgid "Filters"
msgstr "Filters"

#: report/models.py:299
msgid "Template query filters (comma-separated list of key=value pairs)"
msgstr "Sjabloon zoekfilters (door komma's gescheiden lijst van sleutel=waarde paren)"

#: report/models.py:358 report/models.py:564
msgid "Template file"
msgstr "Sjabloon bestand"

#: report/models.py:366
msgid "Page size for PDF reports"
msgstr "Standaard paginagrootte voor PDF rapport"

#: report/models.py:372
msgid "Render report in landscape orientation"
msgstr "Rapportage weergeven in liggende stand"

#: report/models.py:486
#, python-brace-format
msgid "Report generated from template {self.name}"
msgstr "Rapport gegenereerd door template {self.name}"

#: report/models.py:511
msgid "Error generating report"
msgstr "Fout bij genereren rapport"

#: report/models.py:570
msgid "Width [mm]"
msgstr "Breedte [mm]"

#: report/models.py:571
msgid "Label width, specified in mm"
msgstr "Label breedte, gespecificeerd in mm"

#: report/models.py:577
msgid "Height [mm]"
msgstr "Hoogte [mm]"

#: report/models.py:578
msgid "Label height, specified in mm"
msgstr "Label hoogte, gespecificeerd in mm"

#: report/models.py:688
msgid "Error printing labels"
msgstr "Fout afdrukken van labels"

#: report/models.py:707
msgid "Snippet"
msgstr "Tekstfragment"

#: report/models.py:708
msgid "Report snippet file"
msgstr "Rapporteer snippet bestand"

#: report/models.py:715
msgid "Snippet file description"
msgstr "Snippet bestandsbeschrijving"

#: report/models.py:733
msgid "Asset"
msgstr "Asset"

#: report/models.py:734
msgid "Report asset file"
msgstr "Rapporteer asset bestand"

#: report/models.py:741
msgid "Asset file description"
msgstr "Beschrijving asset bestand"

#: report/serializers.py:91
msgid "Select report template"
msgstr "Selecteer rapport template"

#: report/serializers.py:99 report/serializers.py:149
msgid "List of item primary keys to include in the report"
msgstr "Lijst van primaire productsleutels op te nemen in het verslag"

#: report/serializers.py:132
msgid "Select label template"
msgstr "Selecteer label sjabloon"

#: report/serializers.py:140
msgid "Printing Plugin"
msgstr "Plug-in printen"

#: report/serializers.py:141
msgid "Select plugin to use for label printing"
msgstr "Selecteer de plug-in voor het afdrukken van labels"

#: report/templates/label/part_label.html:31
#: report/templates/label/stockitem_qr.html:21
#: report/templates/label/stocklocation_qr.html:20
msgid "QR Code"
msgstr "QR Code"

#: report/templates/label/part_label_code128.html:31
#: report/templates/label/stocklocation_qr_and_text.html:31
msgid "QR code"
msgstr "QR code"

#: report/templates/report/inventree_bill_of_materials_report.html:100
msgid "Bill of Materials"
msgstr "Materiaallijst"

#: report/templates/report/inventree_bill_of_materials_report.html:133
msgid "Materials needed"
msgstr "Benodigde materialen"

#: report/templates/report/inventree_build_order_report.html:98
#: report/templates/report/inventree_purchase_order_report.html:40
#: report/templates/report/inventree_sales_order_report.html:40
#: report/templates/report/inventree_sales_order_shipment_report.html:37
#: report/templates/report/inventree_test_report.html:84
#: report/templates/report/inventree_test_report.html:162
msgid "Part image"
msgstr "Afbeelding onderdeel"

#: report/templates/report/inventree_build_order_report.html:121
msgid "Issued"
msgstr "Uitgegeven"

#: report/templates/report/inventree_build_order_report.html:146
msgid "Required For"
msgstr "Vereist Voor"

#: report/templates/report/inventree_purchase_order_report.html:15
msgid "Supplier was deleted"
msgstr "Leverancier is verwijderd"

#: report/templates/report/inventree_purchase_order_report.html:30
#: report/templates/report/inventree_sales_order_report.html:30
msgid "Unit Price"
msgstr "Stukprijs"

#: report/templates/report/inventree_purchase_order_report.html:55
#: report/templates/report/inventree_return_order_report.html:48
#: report/templates/report/inventree_sales_order_report.html:55
msgid "Extra Line Items"
msgstr "Extra regel items"

#: report/templates/report/inventree_purchase_order_report.html:72
#: report/templates/report/inventree_sales_order_report.html:72
msgid "Total"
msgstr "Totaal"

#: report/templates/report/inventree_return_order_report.html:25
#: report/templates/report/inventree_sales_order_shipment_report.html:45
#: report/templates/report/inventree_test_report.html:88 stock/models.py:1006
#: stock/serializers.py:163
msgid "Serial Number"
msgstr "Serienummer"

#: report/templates/report/inventree_sales_order_shipment_report.html:23
msgid "Allocations"
msgstr "Toewijzingen"

#: report/templates/report/inventree_sales_order_shipment_report.html:47
msgid "Batch"
msgstr "Batch"

#: report/templates/report/inventree_stock_location_report.html:97
msgid "Stock location items"
msgstr "Voorraad locatie items"

#: report/templates/report/inventree_test_report.html:21
msgid "Stock Item Test Report"
msgstr "Rapport voorraadcontrole"

#: report/templates/report/inventree_test_report.html:97
msgid "Test Results"
msgstr "Test resultaten"

#: report/templates/report/inventree_test_report.html:102
msgid "Test"
msgstr "Test"

#: report/templates/report/inventree_test_report.html:129
msgid "Pass"
msgstr "Geslaagd"

#: report/templates/report/inventree_test_report.html:131
msgid "Fail"
msgstr "Niet geslaagd"

#: report/templates/report/inventree_test_report.html:138
msgid "No result (required)"
msgstr "Geen resultaat (verplicht)"

#: report/templates/report/inventree_test_report.html:140
msgid "No result"
msgstr "Geen resultaat"

#: report/templates/report/inventree_test_report.html:153
#: stock/serializers.py:668
msgid "Installed Items"
msgstr "Geïnstalleerde items"

#: report/templates/report/inventree_test_report.html:167
msgid "Serial"
msgstr "Serienummer"

#: report/templatetags/report.py:144
msgid "Asset file does not exist"
msgstr "Asset bestand bestaat niet"

#: report/templatetags/report.py:201 report/templatetags/report.py:277
msgid "Image file not found"
msgstr "Afbeelding bestand niet gevonden"

#: report/templatetags/report.py:302
msgid "part_image tag requires a Part instance"
msgstr "part_image tag vereist een onderdeel instantie"

#: report/templatetags/report.py:349
msgid "company_image tag requires a Company instance"
msgstr "bedrijf_imagetag vereist een bedrijfsinstantie"

#: stock/api.py:255
msgid "Filter by location depth"
msgstr "Filter op locatie diepte"

#: stock/api.py:275
msgid "Filter by top-level locations"
msgstr "Filter op topniveau locaties"

#: stock/api.py:290
msgid "Include sub-locations in filtered results"
msgstr "Inclusief sublocaties in gefilterde resultaten"

#: stock/api.py:311 stock/serializers.py:1256
msgid "Parent Location"
msgstr "Bovenliggende locatie"

#: stock/api.py:312
msgid "Filter by parent location"
msgstr "Filter op bovenliggende locatie"

#: stock/api.py:554
msgid "Part name (case insensitive)"
msgstr "Naam van onderdeel (hoofdletter ongevoelig)"

#: stock/api.py:560
msgid "Part name contains (case insensitive)"
msgstr "Naam van onderdeel bevat (hoofdletter ongevoelig)"

#: stock/api.py:566
msgid "Part name (regex)"
msgstr "Naam onderdeel (regex)"

#: stock/api.py:571
msgid "Part IPN (case insensitive)"
msgstr "Deel IPN (hoofdletter ongevoelig)"

#: stock/api.py:577
msgid "Part IPN contains (case insensitive)"
msgstr "Onderdeel IPN bevat (hoofdletter ongevoelig)"

#: stock/api.py:583
msgid "Part IPN (regex)"
msgstr "Deel IPN (regex)"

#: stock/api.py:595
msgid "Minimum stock"
msgstr "Minimale voorraad"

#: stock/api.py:599
msgid "Maximum stock"
msgstr "Maximale voorraad"

#: stock/api.py:602
msgid "Status Code"
msgstr "Status code"

#: stock/api.py:642
msgid "External Location"
msgstr "Externe locatie"

#: stock/api.py:741
msgid "Consumed by Build Order"
msgstr "Verbruikt door productieorder"

#: stock/api.py:751
msgid "Installed in other stock item"
msgstr "Geïnstalleerd in een ander voorraadartikel"

#: stock/api.py:840
msgid "Part Tree"
msgstr "Boomstructuur onderdeel"

#: stock/api.py:862
msgid "Updated before"
msgstr "Eerder bijgewerkt"

#: stock/api.py:866
msgid "Updated after"
msgstr "Bijgewerkt na"

#: stock/api.py:870
msgid "Stocktake Before"
msgstr "Voorraadcontrole voor"

#: stock/api.py:874
msgid "Stocktake After"
msgstr "Voorraadcontrole na"

#: stock/api.py:879
msgid "Expiry date before"
msgstr "Vervaldatum voor"

#: stock/api.py:883
msgid "Expiry date after"
msgstr "Vervaldatum na"

#: stock/api.py:886 stock/serializers.py:673
msgid "Stale"
msgstr "Verouderd"

#: stock/api.py:987
msgid "Quantity is required"
msgstr "Hoeveelheid is vereist"

#: stock/api.py:992
msgid "Valid part must be supplied"
msgstr "Geldig onderdeel moet worden opgegeven"

#: stock/api.py:1019
msgid "The given supplier part does not exist"
msgstr "Het opgegeven leveranciers onderdeel bestaat niet"

#: stock/api.py:1029
msgid "The supplier part has a pack size defined, but flag use_pack_size not set"
msgstr "Het leveranciersdeel heeft een pakketgrootte gedefinieerd, maar vlag use_pack_size niet ingesteld"

#: stock/api.py:1056
msgid "Serial numbers cannot be supplied for a non-trackable part"
msgstr "Serienummers kunnen niet worden meegeleverd voor een niet traceerbaar onderdeel"

#: stock/models.py:70
msgid "Stock Location type"
msgstr "Voorraad locatie soort"

#: stock/models.py:71
msgid "Stock Location types"
msgstr "Voorraad locatie soorten"

#: stock/models.py:97
msgid "Default icon for all locations that have no icon set (optional)"
msgstr "Standaardpictogram voor alle locaties waarvoor geen pictogram is ingesteld (optioneel)"

#: stock/models.py:155 stock/models.py:968
msgid "Stock Location"
msgstr "Voorraadlocatie"

#: stock/models.py:156 users/ruleset.py:35
msgid "Stock Locations"
msgstr "Voorraadlocaties"

#: stock/models.py:205 stock/models.py:1133
msgid "Owner"
msgstr "Eigenaar"

#: stock/models.py:206 stock/models.py:1134
msgid "Select Owner"
msgstr "Selecteer eigenaar"

#: stock/models.py:214
msgid "Stock items may not be directly located into a structural stock locations, but may be located to child locations."
msgstr "Voorraaditems kunnen niet direct worden geplaatst op een structurele voorraadlocatie, maar kunnen zich op onderliggende locaties bevinden."

#: stock/models.py:221 users/models.py:499
msgid "External"
msgstr "Extern"

#: stock/models.py:222
msgid "This is an external stock location"
msgstr "Dit is een externe voorraadlocatie"

#: stock/models.py:228
msgid "Location type"
msgstr "Locatie type"

#: stock/models.py:232
msgid "Stock location type of this location"
msgstr "Voorraad locatie type van deze locatie"

#: stock/models.py:304
msgid "You cannot make this stock location structural because some stock items are already located into it!"
msgstr "U kunt deze voorraadlocatie niet structureel maken omdat sommige voorraadartikelen er al in liggen!"

#: stock/models.py:562
msgid "Part must be specified"
msgstr "Onderdeel moet gespecificeerd worden"

#: stock/models.py:827
msgid "Stock items cannot be located into structural stock locations!"
msgstr "Voorraaditems kunnen niet worden geplaatst in structurele voorraadlocaties!"

#: stock/models.py:854 stock/serializers.py:516
msgid "Stock item cannot be created for virtual parts"
msgstr "Voorraadartikel kan niet worden aangemaakt voor virtuele onderdelen"

#: stock/models.py:871
#, python-brace-format
msgid "Part type ('{self.supplier_part.part}') must be {self.part}"
msgstr "Onderdeel type ('{self.supplier_part.part}') moet {self.part} zijn"

#: stock/models.py:881 stock/models.py:894
msgid "Quantity must be 1 for item with a serial number"
msgstr "Hoeveelheid moet 1 zijn voor item met een serienummer"

#: stock/models.py:884
msgid "Serial number cannot be set if quantity greater than 1"
msgstr "Serienummer kan niet worden ingesteld als de hoeveelheid groter is dan 1"

#: stock/models.py:906
msgid "Item cannot belong to itself"
msgstr "Item kan niet tot zichzelf behoren"

#: stock/models.py:911
msgid "Item must have a build reference if is_building=True"
msgstr "Item moet een bouw referentie hebben als is_building=True"

#: stock/models.py:924
msgid "Build reference does not point to the same part object"
msgstr "Bouw referentie verwijst niet naar hetzelfde deel object"

#: stock/models.py:938
msgid "Parent Stock Item"
msgstr "Bovenliggend voorraad item"

#: stock/models.py:950
msgid "Base part"
msgstr "Basis onderdeel"

#: stock/models.py:960
msgid "Select a matching supplier part for this stock item"
msgstr "Selecteer een leveranciersdeel voor dit voorraadartikel"

#: stock/models.py:972
msgid "Where is this stock item located?"
msgstr "Waar bevindt zich dit voorraaditem?"

#: stock/models.py:980 stock/serializers.py:1667
msgid "Packaging this stock item is stored in"
msgstr "Het verpakken van dit voorraaditem is opgeslagen in"

#: stock/models.py:986
msgid "Installed In"
msgstr "Geïnstalleerd in"

#: stock/models.py:991
msgid "Is this item installed in another item?"
msgstr "Is dit item geïnstalleerd in een ander item?"

#: stock/models.py:1010
msgid "Serial number for this item"
msgstr "Serienummer van dit item"

#: stock/models.py:1027 stock/serializers.py:1652
msgid "Batch code for this stock item"
msgstr "Batch code voor dit voorraaditem"

#: stock/models.py:1032
msgid "Stock Quantity"
msgstr "Voorraad hoeveelheid"

#: stock/models.py:1042
msgid "Source Build"
msgstr "Bron Bouw"

#: stock/models.py:1045
msgid "Build for this stock item"
msgstr "Build voor dit voorraaditem"

#: stock/models.py:1052
msgid "Consumed By"
msgstr "Verbruikt door"

#: stock/models.py:1055
msgid "Build order which consumed this stock item"
msgstr "Bestelling bouwen welke dit voorraadartikel heeft verbruikt"

#: stock/models.py:1064
msgid "Source Purchase Order"
msgstr "Inkooporder Bron"

#: stock/models.py:1068
msgid "Purchase order for this stock item"
msgstr "Inkooporder voor dit voorraadartikel"

#: stock/models.py:1074
msgid "Destination Sales Order"
msgstr "Bestemming Verkooporder"

#: stock/models.py:1085
msgid "Expiry date for stock item. Stock will be considered expired after this date"
msgstr "Vervaldatum voor voorraadartikel. Voorraad zal worden beschouwd als verlopen na deze datum"

#: stock/models.py:1103
msgid "Delete on deplete"
msgstr "Verwijderen bij leegmaken"

#: stock/models.py:1104
msgid "Delete this Stock Item when stock is depleted"
msgstr "Verwijder dit voorraadproduct wanneer de voorraad is leeg"

#: stock/models.py:1125
msgid "Single unit purchase price at time of purchase"
msgstr "Enkele eenheidsprijs van de aankoop op het moment van aankoop"

#: stock/models.py:1156
msgid "Converted to part"
msgstr "Omgezet tot onderdeel"

#: stock/models.py:1721
msgid "Part is not set as trackable"
msgstr "Onderdeel is niet ingesteld als traceerbaar"

#: stock/models.py:1727
msgid "Quantity must be integer"
msgstr "Hoeveelheid moet heel getal zijn"

#: stock/models.py:1735
#, python-brace-format
msgid "Quantity must not exceed available stock quantity ({self.quantity})"
msgstr "Hoeveelheid mag niet hoger zijn dan de beschikbare voorraad ({self.quantity})"

#: stock/models.py:1741
msgid "Serial numbers must be provided as a list"
msgstr "Serienummers moeten als lijst worden opgegeven"

#: stock/models.py:1746
msgid "Quantity does not match serial numbers"
msgstr "Hoeveelheid komt niet overeen met serienummers"

#: stock/models.py:1868 stock/models.py:2777
msgid "Test template does not exist"
msgstr "Testsjabloon bestaat niet"

#: stock/models.py:1886
msgid "Stock item has been assigned to a sales order"
msgstr "Voorraadartikel is toegewezen aan een verkooporder"

#: stock/models.py:1890
msgid "Stock item is installed in another item"
msgstr "Voorraad item is geïnstalleerd in een ander item"

#: stock/models.py:1893
msgid "Stock item contains other items"
msgstr "Voorraadartikel bevat andere producten"

#: stock/models.py:1896
msgid "Stock item has been assigned to a customer"
msgstr "Voorraadartikel is aan een klant toegewezen"

#: stock/models.py:1899 stock/models.py:2073
msgid "Stock item is currently in production"
msgstr "Voorraad item is momenteel in productie"

#: stock/models.py:1902
msgid "Serialized stock cannot be merged"
msgstr "Geserialiseerde voorraad kan niet worden samengevoegd"

#: stock/models.py:1909 stock/serializers.py:1545
msgid "Duplicate stock items"
msgstr "Dupliceer voorraadartikelen"

#: stock/models.py:1913
msgid "Stock items must refer to the same part"
msgstr "Voorraadartikelen moeten hetzelfde onderdeel verwijzen"

#: stock/models.py:1921
msgid "Stock items must refer to the same supplier part"
msgstr "Voorraadartikelen moeten verwijzen naar dezelfde leveranciersdeel"

#: stock/models.py:1926
msgid "Stock status codes must match"
msgstr "De voorraad statuscodes moeten overeenkomen"

#: stock/models.py:2196
msgid "StockItem cannot be moved as it is not in stock"
msgstr "Voorraadartikel kan niet worden verplaatst omdat het niet op voorraad is"

#: stock/models.py:2678
msgid "Stock Item Tracking"
msgstr "Voorraad item volgen"

#: stock/models.py:2709
msgid "Entry notes"
msgstr "Item notities"

#: stock/models.py:2749
msgid "Stock Item Test Result"
msgstr "Resultaat voorraad test resultaten"

#: stock/models.py:2780
msgid "Value must be provided for this test"
msgstr "Waarde moet voor deze test worden opgegeven"

#: stock/models.py:2784
msgid "Attachment must be uploaded for this test"
msgstr "Bijlage moet worden geüpload voor deze test"

#: stock/models.py:2789
msgid "Invalid value for this test"
msgstr "Ongeldige waarde voor deze test"

#: stock/models.py:2813
msgid "Test result"
msgstr "Test resultaat"

#: stock/models.py:2820
msgid "Test output value"
msgstr "Test uitvoer waarde"

#: stock/models.py:2828 stock/serializers.py:259
msgid "Test result attachment"
msgstr "Test resultaat bijlage"

#: stock/models.py:2832
msgid "Test notes"
msgstr "Test notities"

#: stock/models.py:2840
msgid "Test station"
msgstr "Test station"

#: stock/models.py:2841
msgid "The identifier of the test station where the test was performed"
msgstr "De identificatie van het teststation waar de test werd uitgevoerd"

#: stock/models.py:2847
msgid "Started"
msgstr "Gestart"

#: stock/models.py:2848
msgid "The timestamp of the test start"
msgstr "Het tijdstip van de start test"

#: stock/models.py:2854
msgid "Finished"
msgstr "Afgerond"

#: stock/models.py:2855
msgid "The timestamp of the test finish"
msgstr "Het tijdstip van de afgeronde test"

#: stock/serializers.py:84
msgid "Generated batch code"
msgstr "Gegenereerde batch code"

#: stock/serializers.py:93
msgid "Select build order"
msgstr "Selecteer build order"

#: stock/serializers.py:102
msgid "Select stock item to generate batch code for"
msgstr "Selecteer het voorraaditem om een batchcode te genereren voor"

#: stock/serializers.py:111
msgid "Select location to generate batch code for"
msgstr "Selecteer locatie om batch code voor te genereren"

#: stock/serializers.py:120
msgid "Select part to generate batch code for"
msgstr "Selecteer onderdeel voor het genereren van batchcode voor"

#: stock/serializers.py:129
msgid "Select purchase order"
msgstr "Selecteer bestelling"

#: stock/serializers.py:136
msgid "Enter quantity for batch code"
msgstr "Voer aantal voor batch code in"

#: stock/serializers.py:162
msgid "Generated serial number"
msgstr "Gegenereerd serienummer"

#: stock/serializers.py:172
msgid "Select part to generate serial number for"
msgstr "Selecteer onderdeel voor het genereren van het serienummer voor"

#: stock/serializers.py:180
msgid "Quantity of serial numbers to generate"
msgstr "Aantal serienummers om te genereren"

#: stock/serializers.py:248
msgid "Test template for this result"
msgstr "Test template voor dit resultaat"

#: stock/serializers.py:272
msgid "Template ID or test name must be provided"
msgstr "SjabloonID of testnaam moet worden opgegeven"

#: stock/serializers.py:304
msgid "The test finished time cannot be earlier than the test started time"
msgstr "De testtijd kan niet eerder zijn dan de starttijd van de test"

#: stock/serializers.py:341
msgid "Serial number is too large"
msgstr "Serienummer is te groot"

#: stock/serializers.py:487
msgid "Parent Item"
msgstr "Bovenliggend Item"

#: stock/serializers.py:488
msgid "Parent stock item"
msgstr "Bovenliggende voorraad item"

#: stock/serializers.py:508
msgid "Use pack size when adding: the quantity defined is the number of packs"
msgstr "Gebruik pakketgrootte bij het toevoegen: de hoeveelheid gedefinieerd is het aantal pakketten"

#: stock/serializers.py:618
msgid "Supplier Part Number"
msgstr "Leverancier artikelnummer"

#: stock/serializers.py:665 users/models.py:193
msgid "Expired"
msgstr "Verlopen"

#: stock/serializers.py:671
msgid "Child Items"
msgstr "Onderliggende items"

#: stock/serializers.py:675
msgid "Tracking Items"
msgstr "Items volgen"

#: stock/serializers.py:681
msgid "Purchase price of this stock item, per unit or pack"
msgstr "Inkoopprijs van dit voorraadartikel, per eenheid of pakket"

#: stock/serializers.py:719
msgid "Enter number of stock items to serialize"
msgstr "Aantal voorraaditems om serienummers voor te maken"

#: stock/serializers.py:732
#, python-brace-format
msgid "Quantity must not exceed available stock quantity ({q})"
msgstr "Hoeveelheid mag niet hoger zijn dan de beschikbare voorraad ({q})"

#: stock/serializers.py:739
msgid "Enter serial numbers for new items"
msgstr "Voer serienummers voor nieuwe items in"

#: stock/serializers.py:750 stock/serializers.py:1502 stock/serializers.py:1790
msgid "Destination stock location"
msgstr "Locatie van bestemming"

#: stock/serializers.py:757
msgid "Optional note field"
msgstr "Optioneel notities veld"

#: stock/serializers.py:767
msgid "Serial numbers cannot be assigned to this part"
msgstr "Serienummers kunnen niet worden toegewezen aan dit deel"

#: stock/serializers.py:787
msgid "Serial numbers already exist"
msgstr "Serienummers bestaan al"

#: stock/serializers.py:826
msgid "Select stock item to install"
msgstr "Selecteer voorraaditem om te installeren"

#: stock/serializers.py:833
msgid "Quantity to Install"
msgstr "Te installeren hoeveelheid"

#: stock/serializers.py:834
msgid "Enter the quantity of items to install"
msgstr "Voer de te installeren hoeveelheid items in"

#: stock/serializers.py:839 stock/serializers.py:919 stock/serializers.py:1049
#: stock/serializers.py:1101
msgid "Add transaction note (optional)"
msgstr "Transactienotitie toevoegen (optioneel)"

#: stock/serializers.py:847
msgid "Quantity to install must be at least 1"
msgstr "Te installeren hoeveelheid moet minimaal 1 zijn"

#: stock/serializers.py:855
msgid "Stock item is unavailable"
msgstr "Voorraadartikel is niet beschikbaar"

#: stock/serializers.py:866
msgid "Selected part is not in the Bill of Materials"
msgstr "Het geselecteerde deel zit niet in de materialen lijst"

#: stock/serializers.py:879
msgid "Quantity to install must not exceed available quantity"
msgstr "De te installeren hoeveelheid mag niet groter zijn dan de beschikbare hoeveelheid"

#: stock/serializers.py:914
msgid "Destination location for uninstalled item"
msgstr "Bestemmingslocatie voor verwijderd item"

#: stock/serializers.py:949
msgid "Select part to convert stock item into"
msgstr "Selecteer onderdeel om voorraaditem om te zetten in"

#: stock/serializers.py:962
msgid "Selected part is not a valid option for conversion"
msgstr "Het geselecteerde deel is geen geldige optie voor de omzetting"

#: stock/serializers.py:979
msgid "Cannot convert stock item with assigned SupplierPart"
msgstr "Kan voorraadartikel niet converteren met toegewezen leverancier deel"

#: stock/serializers.py:1013
msgid "Stock item status code"
msgstr "Voorraad status code"

#: stock/serializers.py:1042
msgid "Destination location for returned item"
msgstr "Bestemmingslocatie voor teruggestuurd item"

#: stock/serializers.py:1085
msgid "Select stock items to change status"
msgstr "Selecteer voorraadartikelen om status te wijzigen"

#: stock/serializers.py:1091
msgid "No stock items selected"
msgstr "Geen voorraaditems geselecteerd"

#: stock/serializers.py:1185 stock/serializers.py:1262
msgid "Sublocations"
msgstr "Sublocaties"

#: stock/serializers.py:1257
msgid "Parent stock location"
msgstr "Bovenliggende voorraad locatie"

#: stock/serializers.py:1374
msgid "Part must be salable"
msgstr "Onderdeel moet verkoopbaar zijn"

#: stock/serializers.py:1378
msgid "Item is allocated to a sales order"
msgstr "Artikel is toegewezen aan een verkooporder"

#: stock/serializers.py:1382
msgid "Item is allocated to a build order"
msgstr "Artikel is toegewezen aan een productieorder"

#: stock/serializers.py:1406
msgid "Customer to assign stock items"
msgstr "Klant om voorraadartikelen toe te wijzen"

#: stock/serializers.py:1412
msgid "Selected company is not a customer"
msgstr "Geselecteerde bedrijf is geen klant"

#: stock/serializers.py:1420
msgid "Stock assignment notes"
msgstr "Voorraad toewijzing notities"

#: stock/serializers.py:1430 stock/serializers.py:1695
msgid "A list of stock items must be provided"
msgstr "Een lijst met voorraad artikelen moet worden opgegeven"

#: stock/serializers.py:1509
msgid "Stock merging notes"
msgstr "Voorraad samenvoegen notities"

#: stock/serializers.py:1514
msgid "Allow mismatched suppliers"
msgstr "Niet overeen komende leveranciers toestaan"

#: stock/serializers.py:1515
msgid "Allow stock items with different supplier parts to be merged"
msgstr "Toestaan dat voorraadartikelen met verschillende leveranciers onderdelen worden samengevoegd"

#: stock/serializers.py:1520
msgid "Allow mismatched status"
msgstr "Sta onjuiste status toe"

#: stock/serializers.py:1521
msgid "Allow stock items with different status codes to be merged"
msgstr "Toestaan dat voorraadartikelen met verschillende statuscodes worden samengevoegd"

#: stock/serializers.py:1531
msgid "At least two stock items must be provided"
msgstr "Er moeten ten minste twee voorraadartikelen worden opgegeven"

#: stock/serializers.py:1598
msgid "No Change"
msgstr "Geen wijziging"

#: stock/serializers.py:1627
msgid "StockItem primary key value"
msgstr "Voorraaditem primaire sleutel waarde"

#: stock/serializers.py:1639
msgid "Stock item is not in stock"
msgstr "Voorraad artikel is niet op voorraad"

#: stock/serializers.py:1685
msgid "Stock transaction notes"
msgstr "Voorraad transactie notities"

#: stock/serializers.py:1831
msgid "Next Serial Number"
msgstr "Volgend serienummer"

#: stock/serializers.py:1837
msgid "Previous Serial Number"
msgstr "Vorig serienummer"

#: stock/status_codes.py:11
msgid "OK"
msgstr "Ok"

#: stock/status_codes.py:12
msgid "Attention needed"
msgstr "Aandacht nodig"

#: stock/status_codes.py:13
msgid "Damaged"
msgstr "Beschadigd"

#: stock/status_codes.py:14
msgid "Destroyed"
msgstr "Verwoest"

#: stock/status_codes.py:15
msgid "Rejected"
msgstr "Afgewezen"

#: stock/status_codes.py:19
msgid "Quarantined"
msgstr "In quarantaine geplaatst"

#: stock/status_codes.py:44
msgid "Legacy stock tracking entry"
msgstr "Verouderde volgcode"

#: stock/status_codes.py:46
msgid "Stock item created"
msgstr "Voorraaditem gemaakt"

#: stock/status_codes.py:49
msgid "Edited stock item"
msgstr "Bewerken voorraadartikel"

#: stock/status_codes.py:50
msgid "Assigned serial number"
msgstr "Serienummer toegewezen"

#: stock/status_codes.py:53
msgid "Stock counted"
msgstr "Voorraad geteld"

#: stock/status_codes.py:54
msgid "Stock manually added"
msgstr "Voorraad handmatig toegevoegd"

#: stock/status_codes.py:55
msgid "Stock manually removed"
msgstr "Voorraad handmatig verwijderd"

#: stock/status_codes.py:58
msgid "Location changed"
msgstr "Locatie veranderd"

#: stock/status_codes.py:59
msgid "Stock updated"
msgstr "Voorraad bijgewerkt"

#: stock/status_codes.py:62
msgid "Installed into assembly"
msgstr "Gemonteerd"

#: stock/status_codes.py:63
msgid "Removed from assembly"
msgstr "Gedemonteerd"

#: stock/status_codes.py:65
msgid "Installed component item"
msgstr "Geïnstalleerd componentartikel"

#: stock/status_codes.py:66
msgid "Removed component item"
msgstr "Verwijderd componentartikel"

#: stock/status_codes.py:69
msgid "Split from parent item"
msgstr "Splits van bovenliggend item"

#: stock/status_codes.py:70
msgid "Split child item"
msgstr "Splits onderliggende item"

#: stock/status_codes.py:73
msgid "Merged stock items"
msgstr "Samengevoegde voorraadartikelen"

#: stock/status_codes.py:76
msgid "Converted to variant"
msgstr "Geconverteerd naar variant"

#: stock/status_codes.py:79
msgid "Build order output created"
msgstr "Product aangemaakt"

#: stock/status_codes.py:80
msgid "Build order output completed"
msgstr "Product voltooid"

#: stock/status_codes.py:81
msgid "Build order output rejected"
msgstr "Build order uitvoer afgewezen"

#: stock/status_codes.py:82
msgid "Consumed by build order"
msgstr "Verbruikt door productieorder"

#: stock/status_codes.py:85
msgid "Shipped against Sales Order"
msgstr "Verzonden onder verkooporder"

#: stock/status_codes.py:88
msgid "Received against Purchase Order"
msgstr "Ontvangen onder verkooporder"

#: stock/status_codes.py:91
msgid "Returned against Return Order"
msgstr "Geretourneerd onder retourorder"

#: stock/status_codes.py:94
msgid "Sent to customer"
msgstr "Naar klant verzonden"

#: stock/status_codes.py:95
msgid "Returned from customer"
msgstr "Geretourneerd door klant"

#: templates/403.html:6 templates/403.html:10 templates/403_csrf.html:7
msgid "Permission Denied"
msgstr "Toestemming geweigerd"

#: templates/403.html:11
msgid "You do not have permission to view this page."
msgstr "U heeft geen rechten om deze pagina te bekijken."

#: templates/403_csrf.html:11
msgid "Authentication Failure"
msgstr "Authenticatie mislukt"

#: templates/403_csrf.html:12
msgid "You have been logged out from InvenTree."
msgstr "U bent uitgelogd bij InvenTree."

#: templates/404.html:6 templates/404.html:10
msgid "Page Not Found"
msgstr "Pagina niet gevonden"

#: templates/404.html:11
msgid "The requested page does not exist"
msgstr "De opgevraagde pagina bestaat niet"

#: templates/500.html:6 templates/500.html:10
msgid "Internal Server Error"
msgstr "Server fout"

#: templates/500.html:11
#, python-format
msgid "The %(inventree_title)s server raised an internal error"
msgstr "De %(inventree_title)s server heeft een interne fout veroorzaakt"

#: templates/500.html:12
msgid "Refer to the error log in the admin interface for further details"
msgstr "Raadpleeg de foutmelding in de admin interface voor verdere details"

#: templates/503.html:11 templates/503.html:15
msgid "Site is in Maintenance"
msgstr "Site is in onderhoud"

#: templates/503.html:17
msgid "The site is currently in maintenance and should be up again soon!"
msgstr "De site is momenteel in onderhoud en zal binnenkort weer in bedrijf zijn!"

#: templates/base.html:51
msgid "Server Restart Required"
msgstr "Server herstart vereist"

#: templates/base.html:54
msgid "A configuration option has been changed which requires a server restart"
msgstr "Een instelling is gewijzigd waarvoor een herstart van de server vereist is"

#: templates/base.html:54 templates/base.html:64
msgid "Contact your system administrator for further information"
msgstr "Neem contact op met uw systeembeheerder voor meer informatie"

#: templates/base.html:61
msgid "Pending Database Migrations"
msgstr "Wachtende database migraties"

#: templates/base.html:64
msgid "There are pending database migrations which require attention"
msgstr "Er zijn in afwachting van database migraties die aandacht vereisen"

#: templates/email/build_order_completed.html:9
#: templates/email/canceled_order_assigned.html:9
#: templates/email/new_order_assigned.html:9
#: templates/email/overdue_build_order.html:9
#: templates/email/overdue_purchase_order.html:9
#: templates/email/overdue_return_order.html:9
#: templates/email/overdue_sales_order.html:9
#: templates/email/purchase_order_received.html:9
#: templates/email/return_order_received.html:9
msgid "Click on the following link to view this order"
msgstr "Klik op de volgende link om deze order te bekijken"

#: templates/email/build_order_required_stock.html:7
msgid "Stock is required for the following build order"
msgstr "Voorraad is vereist voor de volgende productieorder"

#: templates/email/build_order_required_stock.html:8
#, python-format
msgid "Build order %(build)s - building %(quantity)s x %(part)s"
msgstr "Productieorder %(build)s - In productie %(quantity)s x %(part)s"

#: templates/email/build_order_required_stock.html:10
msgid "Click on the following link to view this build order"
msgstr "Klik op de volgende link om deze productieorder te bekijken"

#: templates/email/build_order_required_stock.html:14
msgid "The following parts are low on required stock"
msgstr "De volgende onderdelen hebben een lage vereiste voorraad"

#: templates/email/build_order_required_stock.html:18
msgid "Required Quantity"
msgstr "Vereiste Hoeveelheid"

#: templates/email/build_order_required_stock.html:38
#: templates/email/low_stock_notification.html:30
msgid "You are receiving this email because you are subscribed to notifications for this part "
msgstr "Je ontvangt deze e-mail omdat je bent geabonneerd op notificaties van dit onderdeel "

#: templates/email/low_stock_notification.html:9
#: templates/email/part_event_notification.html:9
msgid "Click on the following link to view this part"
msgstr "Klik op de volgende link om dit deel te bekijken"

#: templates/email/low_stock_notification.html:18
#: templates/email/part_event_notification.html:19
msgid "Minimum Quantity"
msgstr "Minimale hoeveelheid"

#: templates/email/part_event_notification.html:32
msgid "You are receiving this email because you are subscribed to notifications for this part or a category that it is part of "
msgstr "Je ontvangt deze e-mail omdat je bent geabonneerd op notificaties van dit onderdeel "

#: users/admin.py:101
msgid "Users"
msgstr "Gebruikers"

#: users/admin.py:102
msgid "Select which users are assigned to this group"
msgstr "Selecteer welke gebruikers zijn toegewezen aan deze groep"

#: users/admin.py:137
msgid "Personal info"
msgstr "Persoonlijke info"

#: users/admin.py:139
msgid "Permissions"
msgstr "Machtigingen"

#: users/admin.py:142
msgid "Important dates"
msgstr "Belangrijke data"

#: users/authentication.py:30 users/models.py:157
msgid "Token has been revoked"
msgstr "Token is ingetrokken"

#: users/authentication.py:33
msgid "Token has expired"
msgstr "Token is verlopen"

#: users/models.py:100
msgid "API Token"
msgstr "API Token"

#: users/models.py:101
msgid "API Tokens"
msgstr "API tokens"

#: users/models.py:137
msgid "Token Name"
msgstr "Token naam"

#: users/models.py:138
msgid "Custom token name"
msgstr "Aangepaste token naam"

#: users/models.py:144
msgid "Token expiry date"
msgstr "Token vervaldatum"

#: users/models.py:152
msgid "Last Seen"
msgstr "Laatst gezien"

#: users/models.py:153
msgid "Last time the token was used"
msgstr "Laatste keer dat het token werd gebruikt"

#: users/models.py:157
msgid "Revoked"
msgstr "Intrekken"

#: users/models.py:235
msgid "Permission set"
msgstr "Toestemming set"

#: users/models.py:244
msgid "Group"
msgstr "Groep"

#: users/models.py:248
msgid "View"
msgstr "Weergeven"

#: users/models.py:248
msgid "Permission to view items"
msgstr "Machtiging om items te bekijken"

#: users/models.py:252
msgid "Add"
msgstr "Toevoegen"

#: users/models.py:252
msgid "Permission to add items"
msgstr "Machtiging om items toe te voegen"

#: users/models.py:256
msgid "Change"
msgstr "Wijzigen"

#: users/models.py:258
msgid "Permissions to edit items"
msgstr "Machtigingen om items te bewerken"

#: users/models.py:262
msgid "Delete"
msgstr "Verwijderen"

#: users/models.py:264
msgid "Permission to delete items"
msgstr "Toestemming om items te verwijderen"

#: users/models.py:497
msgid "Bot"
msgstr "Bot"

#: users/models.py:498
msgid "Internal"
msgstr "Intern"

#: users/models.py:500
msgid "Guest"
msgstr "Gast"

#: users/models.py:509
msgid "Language"
msgstr "Taal"

#: users/models.py:510
msgid "Preferred language for the user"
msgstr "Voorkeurstaal voor gebruiker"

#: users/models.py:515
msgid "Theme"
msgstr "Thema"

#: users/models.py:516
msgid "Settings for the web UI as JSON - do not edit manually!"
msgstr "Instellingen voor webinterface als JSON - niet handmatig bewerken!"

#: users/models.py:521
msgid "Widgets"
msgstr "Widget"

#: users/models.py:523
msgid "Settings for the dashboard widgets as JSON - do not edit manually!"
msgstr "Instellingen voor de dashboard widgets als JSON - wijzig niet handmatig!"

#: users/models.py:530
msgid "Display Name"
msgstr "Naam weergeven"

#: users/models.py:531
msgid "Chosen display name for the user"
msgstr "Gekozen weergavenaam voor de gebruiker"

#: users/models.py:537
msgid "Position"
msgstr "Functie"

#: users/models.py:538
msgid "Main job title or position"
msgstr "Titel of positie hoofdtaken"

#: users/models.py:545
msgid "User status message"
msgstr "Gebruiker status bericht"

#: users/models.py:552
msgid "User location information"
msgstr "Informatie over locatie gebruiker"

#: users/models.py:557
msgid "User is actively using the system"
msgstr "Gebruiker gebruikt actief het systeem"

#: users/models.py:564
msgid "Preferred contact information for the user"
msgstr "Voorkeursinformatie voor de gebruiker"

#: users/models.py:570
msgid "User Type"
msgstr "Gebruikers type"

#: users/models.py:571
msgid "Which type of user is this?"
msgstr "Welk type gebruiker is dit?"

#: users/models.py:577
msgid "Organisation"
msgstr "Organisatie"

#: users/models.py:578
msgid "Users primary organisation/affiliation"
msgstr "Gebruikers primaire organisatie/affiliatie"

#: users/models.py:586
msgid "Primary Group"
msgstr "Primaire groep"

#: users/models.py:587
msgid "Primary group for the user"
msgstr "Primaire groep van de gebruiker"

#: users/ruleset.py:31
msgid "Admin"
msgstr "Administrator"

#: users/ruleset.py:34
msgid "Stocktake"
msgstr "Voorraadcontrole"

#: users/ruleset.py:38
msgid "Purchase Orders"
msgstr "Inkooporders"

#: users/ruleset.py:39
msgid "Sales Orders"
msgstr "Verkooporders"

#: users/ruleset.py:40
msgid "Return Orders"
msgstr "Retour orders"

#: users/serializers.py:236
msgid "Username"
msgstr "Gebruikersnaam"

#: users/serializers.py:239
msgid "First Name"
msgstr "Voornaam :"

#: users/serializers.py:239
msgid "First name of the user"
msgstr "Voornaam van de gebruiker"

#: users/serializers.py:243
msgid "Last Name"
msgstr "Achternaam"

#: users/serializers.py:243
msgid "Last name of the user"
msgstr "Achternaam van de gebruiker"

#: users/serializers.py:247
msgid "Email address of the user"
msgstr "E-mailadres van de gebruiker"

#: users/serializers.py:323
msgid "Staff"
msgstr "Medewerkers"

#: users/serializers.py:324
msgid "Does this user have staff permissions"
msgstr "Heeft deze gebruiker medewerker machtigingen"

#: users/serializers.py:329
msgid "Superuser"
msgstr "Administrator "

#: users/serializers.py:329
msgid "Is this user a superuser"
msgstr "Is deze gebruiker een administrator "

#: users/serializers.py:333
msgid "Is this user account active"
msgstr "Is dit gebruikersaccount actief"

#: users/serializers.py:345
msgid "Only a superuser can adjust this field"
msgstr "Enkel een supergebruiker kan dit veld aanpassen"

#: users/serializers.py:399
msgid "Only staff users can create new users"
msgstr "Alleen administrators kunnen nieuwe gebruikers aanmaken"

#: users/serializers.py:404
msgid "You do not have permission to create users"
msgstr "U hebt geen toestemming om gebruikers aan te maken"

#: users/serializers.py:425
msgid "Your account has been created."
msgstr "Je account is aangemaakt."

#: users/serializers.py:427
msgid "Please use the password reset function to login"
msgstr "Gebruik de wachtwoordreset functie om in te loggen"

#: users/serializers.py:433
msgid "Welcome to InvenTree"
msgstr "Welkom bij InvenTree"

