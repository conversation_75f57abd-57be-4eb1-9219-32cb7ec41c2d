# Prompt 2.2: Payment Integration & Order Processing with Pesapal API

**Persona:** You are a Full-Stack Developer with expertise in Django, payment gateway integration, and Kenyan payment systems.

**Objective:** Implement complete payment processing functionality using Pesapal API for Kenyan market, including M-Pesa, card payments, and order management. Build robust order processing workflow with payment verification and status tracking.

**Context:** The Django backend foundation from Phase 2.1 is complete. This phase adds comprehensive payment processing using Pesapal API files and creates the complete order management system. The project is at `d:\PRJs\WORK ACTION\DeepForest\deepforest-hardware-emporium`.

## Key Tasks & Requirements:

### 1. Pesapal API Integration & File Structure:
- **Pesapal Setup:** Configure Pesapal merchant account and API credentials
- **Pesapal API Files:** Create dedicated Pesapal integration files:
  - `backend/payments/pesapal_client.py` - Main Pesapal API client class with methods for payment initiation, status checking, and transaction management
  - `backend/payments/pesapal_config.py` - Configuration management for Pesapal credentials, endpoints, and environment settings
  - `backend/payments/pesapal_utils.py` - Utility functions for payment processing, currency conversion, and data validation
  - `backend/payments/pesapal_webhooks.py` - IPN (Instant Payment Notification) handlers for payment status updates
  - `backend/payments/pesapal_exceptions.py` - Custom exception classes for payment errors and API failures
  - `backend/payments/pesapal_serializers.py` - DRF serializers for payment data validation and API responses
- **Payment Methods:** Support M-Pesa, Visa, Mastercard, and other Pesapal-supported methods

### 2. Order & Payment Models:
- **Order Model:** Extend with payment_status, payment_method, pesapal_transaction_id, total_amount_ksh, delivery_cost_ksh
- **OrderItem Model:** Order line items with product, quantity, unit_price_ksh
- **Payment Model:** pesapal_transaction_id, order (FK), amount_ksh, status, payment_method, created_at, completed_at
- **PaymentLog Model:** Comprehensive tracking of all payment attempts, API responses, and webhook notifications

### 3. Payment API Endpoints:
- **Payment Initiation:** `/api/payments/initiate/` - Create Pesapal payment request using pesapal_client.py
- **Payment Callback:** `/api/payments/callback/` - Handle Pesapal IPN using pesapal_webhooks.py
- **Payment Status:** `/api/payments/status/<transaction_id>/` - Check payment status via pesapal_client.py
- **Orders API:** `/api/orders/` for order creation with integrated payment processing

### 4. Frontend Payment Flow:
- **Checkout Process:** Multi-step checkout with payment method selection
- **Pesapal Integration:** Redirect to Pesapal payment page and handle return using the Pesapal API files
- **Payment Status:** Real-time payment status updates and order confirmation
- **Order Processing:** Wire checkout flow to create real orders with payment tracking

### 5. Kenyan Payment Features:
- **M-Pesa Integration:** Seamless M-Pesa payment flow through Pesapal API files
- **Mobile-First:** Optimized payment flow for mobile devices
- **KSH Currency:** All payment amounts in Kenyan Shillings
- **Local Payment Methods:** Support for local banks and mobile money via Pesapal

### 6. Security & Compliance:
- **Payment Security:** Secure handling of payment data and API keys in pesapal_config.py
- **Transaction Logging:** Comprehensive audit trail using PaymentLog model and pesapal_utils.py
- **Error Handling:** Robust error handling using pesapal_exceptions.py for failed payments
- **Webhook Verification:** Secure IPN verification in pesapal_webhooks.py

## Acceptance Criteria:
- Complete Pesapal API integration with all required API files implemented
- Functional M-Pesa payment flow through Pesapal API files
- Orders are created and stored in database with payment tracking
- Payment callback handling works reliably using pesapal_webhooks.py
- Frontend checkout flow is complete and user-friendly
- All payment amounts processed in KSH
- Comprehensive payment logging and error handling via Pesapal API files
- Ready for InvenTree integration in Phase 3
