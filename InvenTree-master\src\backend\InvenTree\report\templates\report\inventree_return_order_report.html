{% extends "report/inventree_order_report_base.html" %}

{% load i18n %}
{% load report %}
{% load barcode %}
{% load inventree_extras %}
{% load markdownify %}

{% block header_content %}
<img class='logo' src='{% company_image customer %}' alt="{{ customer }}" width='150'>

<div class='header-right'>
    <h3>{% trans "Return Order" %} {{ prefix }}{{ reference }}</h3>
    {% if customer %}{{ customer.name }}{% endif %}
</div>
{% endblock header_content %}

{% block page_content %}
<h3>{% trans "Line Items" %}</h3>

<table class='table table-striped table-condensed'>
    <thead>
        <tr>
            <th>{% trans "Part" %}</th>
            <th>{% trans "Serial Number" %}</th>
            <th>{% trans "Reference" %}</th>
            <th>{% trans "Note" %}</th>
        </tr>
    </thead>
    <tbody>
        {% for line in lines.all %}
        <tr>
            <td>
                <div class='thumb-container'>
                    <img src='{% part_image line.item.part height=240 %}' alt='{% trans "Image" %}' class='part-thumb'>
                </div>
                <div class='part-text'>
                    {{ line.item.part.full_name }}
                </div>
            </td>
            <td>{{ line.item.serial }}</td>
            <td>{{ line.reference }}</td>
            <td>{{ line.notes }}</td>
        </tr>
        {% endfor %}

        {% if extra_lines %}
        <tr><th colspan='4'>{% trans "Extra Line Items" %}</th></tr>
        {% for line in extra_lines.all %}
        <tr>
            <td><!-- No part --></td>
            <td><!-- No serial --></td>
            <td>{{ line.reference }}</td>
            <td>{{ line.notes }}</td>
        </tr>
        {% endfor %}
        {% endif %}
    </tbody>

</table>

{% endblock page_content %}
