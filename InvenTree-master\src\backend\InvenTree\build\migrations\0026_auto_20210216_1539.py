# Generated by Django 3.0.7 on 2021-02-16 04:39

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('users', '0005_owner_model'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('build', '0025_build_target_date'),
    ]

    operations = [
        migrations.AddField(
            model_name='build',
            name='issued_by',
            field=models.ForeignKey(blank=True, help_text='User who issued this build order', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='builds_issued', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='build',
            name='responsible',
            field=models.ForeignKey(blank=True, help_text='User responsible for this build order', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='builds_responsible', to='users.Owner'),
        ),
    ]
