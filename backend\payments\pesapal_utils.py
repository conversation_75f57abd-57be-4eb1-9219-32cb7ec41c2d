"""
Utility functions for Pesapal payment processing
Based on the existing Pesapal API files in src/PesapalAPI-Files/
"""
import hashlib
import hmac
import base64
import time
import uuid
import urllib.parse
from decimal import Decimal
from typing import Dict, Any, Optional
import xml.etree.ElementTree as ET

from .pesapal_exceptions import PesapalValidationError


def generate_nonce() -> str:
    """
    Generate a unique nonce for OAuth requests
    Based on the PHP implementation in OAuth.php
    """
    return str(uuid.uuid4()).replace('-', '')


def generate_timestamp() -> str:
    """Generate timestamp for OAuth requests"""
    return str(int(time.time()))


def urlencode_rfc3986(value: str) -> str:
    """
    URL encode according to RFC 3986
    Based on the PHP implementation in OAuth.php
    """
    return urllib.parse.quote(str(value), safe='~')


def build_signature_base_string(method: str, url: str, parameters: Dict[str, str]) -> str:
    """
    Build the signature base string for OAuth
    Based on the PHP implementation in OAuth.php
    """
    # Sort parameters
    sorted_params = sorted(parameters.items())
    
    # Build parameter string
    param_string = '&'.join([f"{urlencode_rfc3986(k)}={urlencode_rfc3986(v)}" for k, v in sorted_params])
    
    # Build base string
    base_string = f"{method.upper()}&{urlencode_rfc3986(url)}&{urlencode_rfc3986(param_string)}"
    
    return base_string


def generate_oauth_signature(base_string: str, consumer_secret: str, token_secret: str = "") -> str:
    """
    Generate OAuth signature using HMAC-SHA1
    Based on the PHP implementation in OAuth.php
    """
    key = f"{urlencode_rfc3986(consumer_secret)}&{urlencode_rfc3986(token_secret)}"
    signature = hmac.new(
        key.encode('utf-8'),
        base_string.encode('utf-8'),
        hashlib.sha1
    ).digest()
    return base64.b64encode(signature).decode('utf-8')


def validate_amount(amount: Any) -> Decimal:
    """Validate and format payment amount"""
    try:
        amount_decimal = Decimal(str(amount))
        if amount_decimal <= 0:
            raise PesapalValidationError("Amount must be greater than 0")
        # Format to 2 decimal places
        return amount_decimal.quantize(Decimal('0.01'))
    except (ValueError, TypeError):
        raise PesapalValidationError("Invalid amount format")


def validate_email(email: str) -> str:
    """Basic email validation"""
    if not email or '@' not in email:
        raise PesapalValidationError("Valid email address is required")
    return email.strip()


def validate_phone(phone: str) -> str:
    """Validate Kenyan phone number format"""
    if not phone:
        return ""
    
    # Remove spaces and special characters
    phone = ''.join(filter(str.isdigit, phone))
    
    # Kenyan phone number validation
    if phone.startswith('254'):
        if len(phone) != 12:
            raise PesapalValidationError("Invalid Kenyan phone number format")
    elif phone.startswith('0'):
        if len(phone) != 10:
            raise PesapalValidationError("Invalid Kenyan phone number format")
        phone = '254' + phone[1:]  # Convert to international format
    elif phone.startswith('7') or phone.startswith('1'):
        if len(phone) != 9:
            raise PesapalValidationError("Invalid Kenyan phone number format")
        phone = '254' + phone  # Add country code
    else:
        raise PesapalValidationError("Invalid Kenyan phone number format")
    
    return phone


def build_pesapal_xml(order_data: Dict[str, Any]) -> str:
    """
    Build Pesapal XML request data
    Based on the PHP implementation in pesapal-iframe.php
    """
    try:
        # Validate required fields
        amount = validate_amount(order_data['amount'])
        email = validate_email(order_data['email'])
        
        # Build XML
        xml_data = f"""<?xml version="1.0" encoding="utf-8"?>
<PesapalDirectOrderInfo 
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
    xmlns:xsd="http://www.w3.org/2001/XMLSchema" 
    Amount="{amount}" 
    Description="{order_data.get('description', '')}" 
    Type="{order_data.get('type', 'MERCHANT')}" 
    Reference="{order_data['reference']}" 
    FirstName="{order_data.get('first_name', '')}" 
    LastName="{order_data.get('last_name', '')}" 
    Email="{email}" 
    PhoneNumber="{validate_phone(order_data.get('phone', ''))}" 
    xmlns="http://www.pesapal.com" />"""
        
        return xml_data
        
    except KeyError as e:
        raise PesapalValidationError(f"Missing required field: {e}")


def parse_pesapal_response(response_text: str) -> Dict[str, str]:
    """Parse Pesapal API response"""
    try:
        # Handle different response formats
        if '=' in response_text:
            # Key-value pair format
            pairs = response_text.split('&')
            result = {}
            for pair in pairs:
                if '=' in pair:
                    key, value = pair.split('=', 1)
                    result[key] = urllib.parse.unquote(value)
            return result
        else:
            # Simple status response
            return {'status': response_text.strip()}
    except Exception:
        return {'status': 'unknown', 'raw_response': response_text}


def format_currency_ksh(amount: Decimal) -> str:
    """Format amount as Kenyan Shillings"""
    return f"KSH {amount:,.2f}"


def generate_order_reference(user_id: int, timestamp: Optional[int] = None) -> str:
    """Generate unique order reference"""
    if timestamp is None:
        timestamp = int(time.time())
    return f"DF{user_id:06d}{timestamp}"
