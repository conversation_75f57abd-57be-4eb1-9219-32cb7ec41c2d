# Generated by Django 3.2.13 on 2022-06-07 22:04

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('company', '0043_manufacturerpartattachment'),
    ]

    operations = [
        migrations.AddField(
            model_name='supplierpart',
            name='availability_updated',
            field=models.DateTimeField(blank=True, help_text='Date of last update of availability data', null=True, verbose_name='Availability Updated'),
        ),
        migrations.AddField(
            model_name='supplierpart',
            name='available',
            field=models.DecimalField(decimal_places=3, default=0, help_text='Quantity available from supplier', max_digits=10, validators=[django.core.validators.MinValueValidator(0)], verbose_name='Available'),
        ),
    ]
