---
title: Release 0.6.1
---

## Release 0.6.1

Release 0.6.1 is a bug-fix release on the 0.6.x stable branch.

For a comprehensive list of changes associated with this release, refer to the [InvenTree GitHub page](https://github.com/inventree/InvenTree/milestone/13).

## Bug Fixes

| Pull Request | Description |
| --- | --- |
| [#2666](https://github.com/inventree/InvenTree/pull/2666) | Fixes bug when creating build output via the API |
| [#2673](https://github.com/inventree/InvenTree/pull/2673) | Bug fixes for allocating stock items against a build order |
| [#2676](https://github.com/inventree/InvenTree/pull/2676) | Fixes HTML template bug for StockLocation display |
| [#2682](https://github.com/inventree/InvenTree/pull/2682) | Fixes plugin bug when running alongisde old (< 2.2.0) version of Git |
| [#2696](https://github.com/inventree/InvenTree/pull/2696) | Fixes some template / javascript rendering issues for the Part detail page |
| [#2697](https://github.com/inventree/InvenTree/pull/2697) | Fixes a coding bug determining if parts were fully allocated against a build order |
