from django.urls import path
from . import views

app_name = 'ecommerce'

urlpatterns = [
    # Categories
    path('categories/', views.CategoryListView.as_view(), name='category-list'),
    path('categories/<slug:slug>/', views.CategoryDetailView.as_view(), name='category-detail'),
    
    # Products
    path('products/', views.ProductListView.as_view(), name='product-list'),
    path('products/<int:pk>/', views.ProductDetailView.as_view(), name='product-detail'),
    
    # User Profile
    path('user/profile/', views.UserProfileView.as_view(), name='user-profile'),
    
    # Cart
    path('cart/', views.CartView.as_view(), name='cart'),
    path('cart/add/', views.add_to_cart, name='add-to-cart'),
    path('cart/items/<int:item_id>/', views.update_cart_item, name='update-cart-item'),
    path('cart/clear/', views.clear_cart, name='clear-cart'),
    
    # Delivery Zones
    path('delivery-zones/', views.DeliveryZoneListView.as_view(), name='delivery-zones'),
    
    # Authentication
    path('auth/register/', views.register_user, name='register'),
]
