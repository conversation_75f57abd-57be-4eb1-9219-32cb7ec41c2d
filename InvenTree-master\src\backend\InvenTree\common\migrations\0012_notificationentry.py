# Generated by Django 3.2.5 on 2021-11-03 13:54

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('common', '0011_auto_20210722_2114'),
    ]

    operations = [
        migrations.CreateModel(
            name='NotificationEntry',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('key', models.CharField(max_length=250)),
                ('uid', models.IntegerField()),
                ('updated', models.DateTimeField(auto_now=True)),
            ],
            options={
                'unique_together': {('key', 'uid')},
            },
        ),
    ]
