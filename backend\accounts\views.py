from rest_framework_simplejwt.views import TokenObtainPairView, TokenRefreshView
from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from django.contrib.auth.models import User


class CustomTokenObtainPairView(TokenObtainPairView):
    """Custom JWT token obtain view"""
    pass


class CustomTokenRefreshView(TokenRefreshView):
    """Custom JWT token refresh view"""
    pass


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def user_info(request):
    """Get current user information"""
    user = request.user
    return Response({
        'id': user.id,
        'username': user.username,
        'email': user.email,
        'first_name': user.first_name,
        'last_name': user.last_name,
        'is_staff': user.is_staff,
        'date_joined': user.date_joined,
    })


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def logout_user(request):
    """Logout user (client should delete tokens)"""
    return Response({'message': 'Logged out successfully'}, status=status.HTTP_200_OK)
