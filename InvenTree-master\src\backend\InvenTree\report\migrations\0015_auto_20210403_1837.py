# Generated by Django 3.0.7 on 2021-04-03 18:37

import django.core.validators
from django.db import migrations, models
import report.models


class Migration(migrations.Migration):

    dependencies = [
        ('report', '0014_purchaseorderreport_salesorderreport'),
    ]

    operations = [
        migrations.AlterField(
            model_name='reportasset',
            name='asset',
            field=models.FileField(help_text='Report asset file', upload_to='report/assets', verbose_name='Asset'),
        ),
        migrations.AlterField(
            model_name='reportasset',
            name='description',
            field=models.CharField(help_text='Asset file description', max_length=250, verbose_name='Description'),
        ),
        migrations.AlterField(
            model_name='reportsnippet',
            name='description',
            field=models.CharField(help_text='Snippet file description', max_length=250, verbose_name='Description'),
        ),
        migrations.AlterField(
            model_name='reportsnippet',
            name='snippet',
            field=models.FileField(help_text='Report snippet file', upload_to='report/snippets', validators=[django.core.validators.FileExtensionValidator(allowed_extensions=['html', 'htm'])], verbose_name='Snippet'),
        ),
    ]
