# Create some locations for stock

- model: stock.stocklocation
  pk: 1
  fields:
    name: 'Home'
    description: 'My house'
    level: 0
    tree_id: 1
    lft: 1
    rght: 6

- model: stock.stocklocation
  pk: 2
  fields:
    name: 'Bathroom'
    description: 'Where I keep my bath'
    parent: 1
    level: 1
    tree_id: 1
    lft: 2
    rght: 3

- model: stock.stocklocation
  pk: 3
  fields:
    name: 'Dining Room'
    description: 'A table lives here'
    parent: 1
    level: 0
    tree_id: 1
    lft: 4
    rght: 5

- model: stock.stocklocation
  pk: 4
  fields:
    name: 'Office'
    description: 'Place of work'
    level: 0
    tree_id: 2
    lft: 1
    rght: 8
    external: True

- model: stock.stocklocation
  pk: 5
  fields:
    name: 'Drawer_1'
    description: 'In my desk'
    parent: 4
    level: 0
    tree_id: 2
    lft: 2
    rght: 3

- model: stock.stocklocation
  pk: 6
  fields:
    name: 'Drawer_2'
    description: 'Also in my desk'
    parent: 4
    level: 0
    tree_id: 2
    lft: 4
    rght: 5

- model: stock.stocklocation
  pk: 7
  fields:
    name: 'Drawer_3'
    description: 'Again, in my desk'
    parent: 4
    level: 0
    tree_id: 2
    lft: 6
    rght: 7
