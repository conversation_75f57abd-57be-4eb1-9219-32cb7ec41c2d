# Generated by Django 3.2.12 on 2022-04-03 23:38

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('plugin', '0004_alter_pluginsetting_key'),
    ]

    operations = [
        migrations.CreateModel(
            name='NotificationUserSetting',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('key', models.CharField(help_text='Settings key', max_length=50)),
                ('value', models.CharField(blank=True, help_text='Settings value', max_length=200)),
                ('method', models.CharField(max_length=255, verbose_name='Method')),
                ('user', models.ForeignKey(blank=True, help_text='User', null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='User')),
            ],
            options={
                'unique_together': {('method', 'user', 'key')},
            },
        ),
    ]
