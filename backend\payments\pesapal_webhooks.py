"""
Pesapal webhook and IPN (Instant Payment Notification) handlers
Based on the existing pesapal-ipn-listener.php implementation
"""
import logging
from typing import Dict, Any, Optional
from django.utils import timezone
from django.db import transaction

from .models import Payment, PaymentCallback, PaymentLog, Order
from .pesapal_client import PesapalClient
from .pesapal_exceptions import PesapalCallbackError, PesapalAPIError

logger = logging.getLogger(__name__)


class PesapalWebhookHandler:
    """Handle Pesapal IPN callbacks and payment status updates"""
    
    def __init__(self):
        self.client = PesapalClient()
    
    def process_ipn_callback(self, callback_data: Dict[str, Any]) -> bool:
        """
        Process Pesapal IPN callback
        Based on pesapal-ipn-listener.php implementation
        """
        try:
            # Store raw callback data
            callback_record = PaymentCallback.objects.create(
                pesapal_notification_type=callback_data.get('pesapal_notification_type', ''),
                pesapal_transaction_tracking_id=callback_data.get('pesapal_transaction_tracking_id', ''),
                pesapal_merchant_reference=callback_data.get('pesapal_merchant_reference', ''),
                raw_data=callback_data
            )
            
            # Verify callback data
            if not self.client.verify_callback(callback_data):
                error_msg = "Invalid callback data received"
                callback_record.error_message = error_msg
                callback_record.save()
                logger.warning(f"Invalid callback: {callback_data}")
                return False
            
            # Process CHANGE notifications
            if (callback_data.get('pesapal_notification_type') == 'CHANGE' and 
                callback_data.get('pesapal_transaction_tracking_id')):
                
                success = self._process_payment_status_change(callback_data, callback_record)
                
                if success:
                    callback_record.processed = True
                    callback_record.processed_at = timezone.now()
                    callback_record.save()
                
                return success
            
            # Mark as processed for non-CHANGE notifications
            callback_record.processed = True
            callback_record.processed_at = timezone.now()
            callback_record.save()
            
            return True
            
        except Exception as e:
            logger.error(f"Error processing IPN callback: {str(e)}")
            if 'callback_record' in locals():
                callback_record.error_message = str(e)
                callback_record.save()
            return False
    
    def _process_payment_status_change(self, callback_data: Dict[str, Any], callback_record: PaymentCallback) -> bool:
        """Process payment status change notification"""
        try:
            merchant_reference = callback_data['pesapal_merchant_reference']
            tracking_id = callback_data['pesapal_transaction_tracking_id']
            
            # Query payment status from Pesapal
            status_response = self.client.query_payment_status(merchant_reference, tracking_id)
            
            # Log the status check
            PaymentLog.objects.create(
                action='status_check_callback',
                status=status_response.get('status', 'unknown'),
                message=f"Status check via IPN callback for {merchant_reference}",
                request_data={'merchant_reference': merchant_reference, 'tracking_id': tracking_id},
                response_data=status_response,
                pesapal_transaction_id=merchant_reference,
                pesapal_tracking_id=tracking_id
            )
            
            # Find the order
            try:
                order = Order.objects.get(order_reference=merchant_reference)
            except Order.DoesNotExist:
                error_msg = f"Order not found for reference: {merchant_reference}"
                logger.error(error_msg)
                callback_record.error_message = error_msg
                callback_record.save()
                return False
            
            # Update payment status
            payment_status = self._map_pesapal_status(status_response.get('status', ''))
            
            if payment_status:
                self._update_payment_status(order, payment_status, tracking_id, status_response)
                logger.info(f"Payment status updated for order {merchant_reference}: {payment_status}")
                return True
            else:
                logger.warning(f"Unknown payment status from Pesapal: {status_response.get('status')}")
                return False
                
        except Exception as e:
            logger.error(f"Error processing payment status change: {str(e)}")
            callback_record.error_message = str(e)
            callback_record.save()
            return False
    
    def _map_pesapal_status(self, pesapal_status: str) -> Optional[str]:
        """Map Pesapal status to our internal status"""
        status_mapping = {
            'COMPLETED': 'completed',
            'PENDING': 'pending',
            'FAILED': 'failed',
            'INVALID': 'invalid',
            'CANCELLED': 'cancelled'
        }
        return status_mapping.get(pesapal_status.upper())
    
    @transaction.atomic
    def _update_payment_status(self, order: Order, status: str, tracking_id: str, response_data: Dict[str, Any]):
        """Update payment and order status atomically"""
        try:
            # Update order payment status
            order.payment_status = status
            order.pesapal_transaction_id = tracking_id
            
            if status == 'completed':
                order.mark_as_paid()
            elif status in ['failed', 'cancelled', 'invalid']:
                order.status = 'cancelled'
            
            order.save()
            
            # Update or create payment record
            payment, created = Payment.objects.get_or_create(
                order=order,
                pesapal_merchant_reference=order.order_reference,
                defaults={
                    'pesapal_transaction_id': tracking_id,
                    'amount_ksh': order.total_with_delivery,
                    'payment_method': order.payment_method or 'unknown',
                    'status': status,
                    'pesapal_tracking_id': tracking_id
                }
            )
            
            if not created:
                payment.status = status
                payment.pesapal_tracking_id = tracking_id
                if status == 'completed':
                    payment.mark_as_completed()
                else:
                    payment.save()
            
            # Log the update
            PaymentLog.objects.create(
                payment=payment,
                order=order,
                action='status_update',
                status=status,
                message=f"Payment status updated to {status}",
                response_data=response_data,
                pesapal_transaction_id=tracking_id,
                pesapal_tracking_id=tracking_id
            )
            
        except Exception as e:
            logger.error(f"Error updating payment status: {str(e)}")
            raise
    
    def manual_status_check(self, order_reference: str) -> Dict[str, Any]:
        """Manually check payment status for an order"""
        try:
            order = Order.objects.get(order_reference=order_reference)
            
            if not order.pesapal_transaction_id:
                return {'success': False, 'error': 'No Pesapal transaction ID found'}
            
            # Query status from Pesapal
            status_response = self.client.query_payment_status(
                order_reference, 
                order.pesapal_transaction_id
            )
            
            # Log the manual check
            PaymentLog.objects.create(
                order=order,
                action='manual_status_check',
                status=status_response.get('status', 'unknown'),
                message=f"Manual status check for {order_reference}",
                response_data=status_response,
                pesapal_transaction_id=order.pesapal_transaction_id
            )
            
            # Update status if needed
            payment_status = self._map_pesapal_status(status_response.get('status', ''))
            if payment_status and payment_status != order.payment_status:
                self._update_payment_status(order, payment_status, order.pesapal_transaction_id, status_response)
            
            return {
                'success': True,
                'status': payment_status,
                'pesapal_response': status_response
            }
            
        except Order.DoesNotExist:
            return {'success': False, 'error': 'Order not found'}
        except Exception as e:
            logger.error(f"Error in manual status check: {str(e)}")
            return {'success': False, 'error': str(e)}
