# Generated by Django 3.0.7 on 2020-10-25 21:33

import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
import mptt.fields

from build.status_codes import BuildStatus


class Migration(migrations.Migration):

    replaces = [('build', '0021_auto_20201020_0908'), ('build', '0022_auto_20201020_0953'), ('build', '0023_auto_20201020_1009'), ('build', '0024_auto_20201020_1144'), ('build', '0025_auto_20201020_1248'), ('build', '0026_auto_20201023_1228')]

    dependencies = [
        ('stock', '0052_stockitem_is_building'),
        ('build', '0020_auto_20201019_1325'),
        ('part', '0051_bomitem_optional'),
    ]

    operations = [
        migrations.AddField(
            model_name='builditem',
            name='install_into',
            field=models.ForeignKey(blank=True, help_text='Destination stock item', limit_choices_to={'is_building': True}, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='items_to_install', to='stock.StockItem'),
        ),
        migrations.AlterField(
            model_name='builditem',
            name='stock_item',
            field=models.ForeignKey(help_text='Source stock item', limit_choices_to={'belongs_to': None, 'build_order': None, 'sales_order': None}, on_delete=django.db.models.deletion.CASCADE, related_name='allocations', to='stock.StockItem'),
        ),
        migrations.AddField(
            model_name='build',
            name='destination',
            field=models.ForeignKey(blank=True, help_text='Select location where the completed items will be stored', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='incoming_builds', to='stock.StockLocation', verbose_name='Destination Location'),
        ),
        migrations.AlterField(
            model_name='build',
            name='parent',
            field=mptt.fields.TreeForeignKey(blank=True, help_text='BuildOrder to which this build is allocated', null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='children', to='build.Build', verbose_name='Parent Build'),
        ),
        migrations.AlterField(
            model_name='build',
            name='status',
            field=models.PositiveIntegerField(choices=BuildStatus.items(), default=BuildStatus.PENDING.value, help_text='Build status code', validators=[django.core.validators.MinValueValidator(0)], verbose_name='Build Status'),
        ),
        migrations.AlterField(
            model_name='build',
            name='part',
            field=models.ForeignKey(help_text='Select part to build', limit_choices_to={'active': True, 'assembly': True, 'virtual': False}, on_delete=django.db.models.deletion.CASCADE, related_name='builds', to='part.Part', verbose_name='Part'),
        ),
        migrations.AddField(
            model_name='build',
            name='completed',
            field=models.PositiveIntegerField(default=0, help_text='Number of stock items which have been completed', verbose_name='Completed items'),
        ),
        migrations.AlterField(
            model_name='build',
            name='quantity',
            field=models.PositiveIntegerField(default=1, help_text='Number of stock items to build', validators=[django.core.validators.MinValueValidator(1)], verbose_name='Build Quantity'),
        ),
        migrations.AlterUniqueTogether(
            name='builditem',
            unique_together={('build', 'stock_item', 'install_into')},
        ),
    ]
