"""
Custom exception classes for Pesapal payment processing
Based on the existing Pesapal API files in src/PesapalAPI-Files/
"""


class PesapalException(Exception):
    """Base exception for all Pesapal-related errors"""
    pass


class PesapalConfigurationError(PesapalException):
    """Raised when Pesapal configuration is invalid or missing"""
    pass


class PesapalAuthenticationError(PesapalException):
    """Raised when OAuth authentication fails"""
    pass


class PesapalAPIError(PesapalException):
    """Raised when Pesapal API returns an error"""
    def __init__(self, message, status_code=None, response_data=None):
        super().__init__(message)
        self.status_code = status_code
        self.response_data = response_data


class PesapalNetworkError(PesapalException):
    """Raised when network communication with Pesapal fails"""
    pass


class PesapalValidationError(PesapalException):
    """Raised when payment data validation fails"""
    pass


class PesapalPaymentError(PesapalException):
    """Raised when payment processing fails"""
    def __init__(self, message, transaction_id=None, payment_status=None):
        super().__init__(message)
        self.transaction_id = transaction_id
        self.payment_status = payment_status


class PesapalCallbackError(PesapalException):
    """Raised when processing payment callbacks fails"""
    pass


class PesapalSignatureError(PesapalException):
    """Raised when OAuth signature verification fails"""
    pass
