#!/usr/bin/env python
"""
M-Pesa Payment Integration Test
Tests the complete M-Pesa payment flow with real Pesapal credentials
"""
import os
import sys
import django
from decimal import Decimal

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'deepforest_backend.settings')
django.setup()

from django.contrib.auth.models import User
from payments.models import Order, OrderItem, Payment, PaymentLog
from payments.pesapal_client import PesapalClient
from payments.pesapal_webhooks import PesapalWebhookHandler
from ecommerce.models import Product, Category, Cart, CartItem, DeliveryZone


def setup_mpesa_test_data():
    """Setup test data for M-Pesa payment testing"""
    print("Setting up M-Pesa test data...")
    
    # Create test user
    import time
    username = f'mpesa_user_{int(time.time())}'
    user = User.objects.create_user(
        username=username,
        email=f'{username}@example.com',
        password='testpass123',
        first_name='<PERSON>',
        last_name='Doe'
    )
    
    # Create test category and products
    category, _ = Category.objects.get_or_create(
        name='Hardware Tools',
        defaults={'description': 'Professional hardware tools'}
    )
    
    hammer, _ = Product.objects.get_or_create(
        sku='HAMMER_PRO_001',
        defaults={
            'name': 'Professional Hammer',
            'description': 'Heavy-duty construction hammer',
            'price_ksh': Decimal('1500.00'),
            'category': category,
            'stock_quantity': 20
        }
    )
    
    screwdriver, _ = Product.objects.get_or_create(
        sku='SCREW_SET_001',
        defaults={
            'name': 'Screwdriver Set',
            'description': 'Complete screwdriver set with multiple sizes',
            'price_ksh': Decimal('800.00'),
            'category': category,
            'stock_quantity': 15
        }
    )
    
    # Create delivery zone for Nairobi
    delivery_zone, _ = DeliveryZone.objects.get_or_create(
        county='nairobi',
        defaults={
            'delivery_cost_ksh': Decimal('300.00'),
            'estimated_days': 2
        }
    )
    
    # Create cart with items
    cart, _ = Cart.objects.get_or_create(user=user)
    # Clear existing items
    cart.items.all().delete()
    
    CartItem.objects.create(cart=cart, product=hammer, quantity=1)
    CartItem.objects.create(cart=cart, product=screwdriver, quantity=2)
    
    return user, [hammer, screwdriver], delivery_zone


def test_mpesa_payment_initiation():
    """Test M-Pesa payment initiation with real credentials"""
    print("\n=== Testing M-Pesa Payment Initiation ===")
    
    try:
        user, products, delivery_zone = setup_mpesa_test_data()
        
        # Calculate cart total
        cart = Cart.objects.get(user=user)
        cart_total = cart.total_price_ksh
        total_with_delivery = cart_total + delivery_zone.delivery_cost_ksh
        
        print(f"Cart total: KSH {cart_total}")
        print(f"Delivery cost: KSH {delivery_zone.delivery_cost_ksh}")
        print(f"Total amount: KSH {total_with_delivery}")
        
        # Create order
        import time
        order_ref = f'MPESA_{user.id}_{int(time.time())}'
        order = Order.objects.create(
            user=user,
            order_reference=order_ref,
            total_amount_ksh=cart_total,
            delivery_cost_ksh=delivery_zone.delivery_cost_ksh,
            delivery_address='123 Kenyatta Avenue, Nairobi, Kenya',
            delivery_county='nairobi',
            payment_method='mpesa'
        )
        
        # Create order items
        for cart_item in cart.items.all():
            OrderItem.objects.create(
                order=order,
                product=cart_item.product,
                quantity=cart_item.quantity,
                unit_price_ksh=cart_item.product.current_price
            )
        
        print(f"Order created: {order.order_reference}")
        
        # Test Pesapal client with real credentials
        client = PesapalClient()
        
        # Prepare M-Pesa payment data
        payment_data = {
            'amount': str(order.total_with_delivery),
            'description': f'DeepForest Hardware Order {order.order_reference}',
            'type': 'MERCHANT',
            'reference': order.order_reference,
            'first_name': user.first_name,
            'last_name': user.last_name,
            'email': user.email,
            'phone': '254700000000'  # Test M-Pesa number
        }
        
        # Initiate payment
        iframe_url = client.initiate_payment(payment_data)
        
        print(f"✓ M-Pesa payment initiated successfully!")
        print(f"  Order Reference: {order.order_reference}")
        print(f"  Amount: KSH {order.total_with_delivery}")
        print(f"  Payment Method: M-Pesa")
        print(f"  Phone: {payment_data['phone']}")
        
        # Log payment initiation
        PaymentLog.objects.create(
            order=order,
            action='initiate_mpesa_payment',
            status='pending',
            message=f"M-Pesa payment initiated for order {order.order_reference}",
            request_data=payment_data,
            pesapal_transaction_id=order.order_reference
        )
        
        print(f"\n🔗 M-Pesa Payment URL:")
        print(f"{iframe_url}")
        
        print(f"\n📱 M-Pesa Payment Instructions:")
        print(f"1. Open the payment URL in a browser")
        print(f"2. Select M-Pesa as payment method")
        print(f"3. Enter phone number: 254700000000")
        print(f"4. Complete payment on your phone")
        print(f"5. Payment will be confirmed via webhook")
        
        return order, iframe_url
        
    except Exception as e:
        print(f"✗ M-Pesa payment initiation failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return None, None


def test_payment_status_query():
    """Test payment status query with real credentials"""
    print("\n=== Testing Payment Status Query ===")
    
    try:
        client = PesapalClient()
        
        # Test with a sample order reference
        result = client.query_payment_status('SAMPLE_REF', 'SAMPLE_TRACKING_ID')
        
        print(f"✓ Payment status query successful")
        print(f"  Response: {result}")
        
        return True
        
    except Exception as e:
        print(f"✗ Payment status query failed: {str(e)}")
        return False


def test_webhook_simulation():
    """Test webhook processing for M-Pesa payment"""
    print("\n=== Testing M-Pesa Webhook Simulation ===")

    try:
        import time  # Fix: Add missing import

        # Create a test order first
        user, products, delivery_zone = setup_mpesa_test_data()

        order_ref = f'WEBHOOK_MPESA_{int(time.time())}'
        order = Order.objects.create(
            user=user,
            order_reference=order_ref,
            total_amount_ksh=Decimal('2300.00'),
            delivery_cost_ksh=Decimal('300.00'),
            delivery_address='456 Uhuru Highway, Nairobi',
            delivery_county='nairobi',
            payment_method='mpesa'
        )
        
        print(f"Created test order: {order.order_reference}")
        
        # Simulate successful M-Pesa payment webhook
        webhook_handler = PesapalWebhookHandler()
        
        callback_data = {
            'pesapal_notification_type': 'CHANGE',
            'pesapal_transaction_tracking_id': 'MPESA_TRACKING_123456',
            'pesapal_merchant_reference': order.order_reference
        }
        
        # Mock the status query to return completed
        original_query_method = webhook_handler.client.query_payment_status
        
        def mock_mpesa_status(merchant_ref, tracking_id):
            return {'status': 'COMPLETED', 'payment_method': 'MPESA'}
        
        webhook_handler.client.query_payment_status = mock_mpesa_status
        
        try:
            result = webhook_handler.process_ipn_callback(callback_data)
            
            if result:
                # Refresh order from database
                order.refresh_from_db()
                print(f"✓ M-Pesa webhook processing successful")
                print(f"  Order Status: {order.status}")
                print(f"  Payment Status: {order.payment_status}")
                print(f"  Payment Method: {order.payment_method}")
                print(f"  Paid At: {order.paid_at}")
                
                # Check payment record
                payment = Payment.objects.filter(order=order).first()
                if payment:
                    print(f"  Payment Record Status: {payment.status}")
                    print(f"  Transaction ID: {payment.pesapal_transaction_id}")
                
                return True
            else:
                print(f"✗ M-Pesa webhook processing failed")
                return False
        
        finally:
            # Restore original method
            webhook_handler.client.query_payment_status = original_query_method
    
    except Exception as e:
        print(f"✗ M-Pesa webhook test failed: {str(e)}")
        return False


def display_mpesa_integration_summary():
    """Display M-Pesa integration summary"""
    print("\n" + "=" * 60)
    print("📱 M-PESA INTEGRATION SUMMARY")
    print("=" * 60)
    
    print(f"\n✅ M-Pesa Configuration:")
    print(f"   • Consumer Key: YAKDivPQIyxTXCEO5jN5UcUuqWVi2KWRs2z6GkZq4Kjp4ykT")
    print(f"   • Consumer Secret: [CONFIGURED]")
    print(f"   • Sandbox Mode: True")
    print(f"   • Payment Method: M-Pesa")
    
    print(f"\n🔧 API Endpoints Ready:")
    print(f"   • Payment Initiation: POST /api/payments/initiate/")
    print(f"   • Payment Callback: GET/POST /api/payments/callback/")
    print(f"   • Webhook IPN: GET/POST /api/payments/ipn/")
    print(f"   • Status Check: GET /api/payments/status/<order_ref>/")
    
    print(f"\n📱 M-Pesa Payment Flow:")
    print(f"   1. Customer selects M-Pesa payment")
    print(f"   2. System generates Pesapal payment URL")
    print(f"   3. Customer redirected to Pesapal M-Pesa page")
    print(f"   4. Customer enters M-Pesa phone number")
    print(f"   5. M-Pesa STK push sent to customer's phone")
    print(f"   6. Customer completes payment on phone")
    print(f"   7. Pesapal sends webhook to update order status")
    print(f"   8. Customer redirected back to confirmation page")
    
    print(f"\n🇰🇪 Kenyan Market Features:")
    print(f"   • Currency: Kenyan Shillings (KSH)")
    print(f"   • Phone Format: +254 (Kenyan numbers)")
    print(f"   • Counties: Nairobi, Mombasa, Kisumu, etc.")
    print(f"   • Delivery Zones: County-based pricing")
    
    print(f"\n🔒 Security Features:")
    print(f"   • OAuth 1.0 signature verification")
    print(f"   • Webhook validation")
    print(f"   • Database transaction atomicity")
    print(f"   • JWT authentication for APIs")


def main():
    """Run M-Pesa integration tests"""
    print("🚀 Starting M-Pesa Payment Integration Tests")
    print("=" * 60)
    
    results = {}
    
    # Test 1: M-Pesa Payment Initiation
    order, payment_url = test_mpesa_payment_initiation()
    results['mpesa_initiation'] = order is not None
    
    # Test 2: Payment Status Query
    results['status_query'] = test_payment_status_query()
    
    # Test 3: M-Pesa Webhook Simulation
    results['webhook'] = test_webhook_simulation()
    
    print("\n" + "=" * 60)
    print("✅ M-Pesa Integration Tests Completed")
    
    # Summary
    print(f"\n📊 Test Results:")
    for test_name, result in results.items():
        status = "✓" if result else "✗"
        print(f"   {test_name.replace('_', ' ').title()}: {status}")
    
    passed = sum(results.values())
    total = len(results)
    print(f"\n🎯 Overall: {passed}/{total} tests passed")
    
    if order and payment_url:
        print(f"\n🔗 Live M-Pesa Payment URL:")
        print(f"{payment_url}")
        print(f"\n📱 Test this URL in your browser to complete M-Pesa payment!")
    
    # Display integration summary
    display_mpesa_integration_summary()
    
    print(f"\n💡 Next Steps:")
    print(f"   1. Test the payment URL in a browser")
    print(f"   2. Complete M-Pesa payment with test phone number")
    print(f"   3. Verify webhook receives payment confirmation")
    print(f"   4. Implement React frontend components")
    print(f"   5. Deploy to production with HTTPS webhooks")


if __name__ == '__main__':
    main()
