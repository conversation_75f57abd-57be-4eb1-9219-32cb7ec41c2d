# Generated by Django 3.2.15 on 2022-09-05 04:21

import InvenTree.fields
import django.core.validators
from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('company', '0046_alter_company_image'),
    ]

    operations = [
        migrations.AddField(
            model_name='supplierpart',
            name='pack_size',
            field=InvenTree.fields.RoundingDecimalField(decimal_places=5, default=1, help_text='Unit quantity supplied in a single pack', max_digits=15, validators=[django.core.validators.MinValueValidator(0.001)], verbose_name='Pack Quantity'),
        ),
    ]
