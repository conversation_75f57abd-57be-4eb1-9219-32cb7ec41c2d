{"name": "@inventreedb/ui", "description": "UI components for the InvenTree project", "version": "0.0.8", "private": false, "type": "module", "license": "MIT", "keywords": ["inventree"], "main": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": "./dist/index.js"}, "files": ["dist", "lib", "LICENSE", "README.md", "CHANGELOG.md"], "homepage": "https://inventree.org", "repository": {"type": "git", "url": "https://github.com/inventree/inventree"}, "author": {"name": "InvenTree Developers", "email": "<EMAIL>", "url": "https://inventree.org", "org": "InvenTree"}, "scripts": {"dev": "vite", "build": "tsc && vite build --emptyOutDir", "lib": "tsc --p ./tsconfig.lib.json && vite --config vite.lib.config.ts build", "preview": "vite preview", "extract": "lingui extract", "compile": "lingui compile --typescript"}, "dependencies": {"@codemirror/autocomplete": "6.18.4", "@codemirror/lang-liquid": "6.2.2", "@codemirror/language": "6.10.8", "@codemirror/lint": "6.8.4", "@codemirror/search": "6.5.8", "@codemirror/state": "6.5.1", "@codemirror/theme-one-dark": "6.1.2", "@codemirror/view": "6.36.2", "@emotion/react": "^11.13.3", "@fortawesome/fontawesome-svg-core": "^6.6.0", "@fortawesome/free-regular-svg-icons": "^6.6.0", "@fortawesome/free-solid-svg-icons": "^6.6.0", "@fortawesome/react-fontawesome": "^0.2.2", "@fullcalendar/core": "^6.1.15", "@fullcalendar/daygrid": "^6.1.15", "@fullcalendar/interaction": "^6.1.15", "@fullcalendar/react": "^6.1.15", "@lingui/core": "^5.3.0", "@lingui/react": "^5.3.0", "@mantine/carousel": "^7.16.0", "@mantine/charts": "^7.16.0", "@mantine/core": "^7.16.0", "@mantine/dates": "^7.16.0", "@mantine/dropzone": "^7.16.0", "@mantine/form": "^7.16.0", "@mantine/hooks": "^7.16.0", "@mantine/modals": "^7.16.0", "@mantine/notifications": "^7.16.0", "@mantine/spotlight": "^7.16.0", "@mantine/vanilla-extract": "^7.16.0", "@messageformat/date-skeleton": "^1.1.0", "@sentry/react": "^8.43.0", "@tabler/icons-react": "^3.17.0", "@tanstack/react-query": "^5.56.2", "@uiw/codemirror-theme-vscode": "4.23.7", "@uiw/react-codemirror": "4.23.7", "@uiw/react-split": "^5.9.3", "@vanilla-extract/css": "^1.17.1", "axios": "^1.8.4", "clsx": "^2.1.1", "codemirror": "6.0.1", "dayjs": "^1.11.13", "dompurify": "^3.2.4", "easymde": "^2.18.0", "embla-carousel-react": "^8.5.2", "fuse.js": "^7.0.0", "html5-qrcode": "^2.3.8", "mantine-contextmenu": "^7.15.3", "mantine-datatable": "^7.15.1", "qrcode": "^1.5.4", "react": "^18.3.1", "react-dom": "^18.3.1", "react-grid-layout": "1.4.4", "react-hook-form": "^7.54.2", "react-is": "^18.3.1", "react-router-dom": "^6.26.2", "react-select": "^5.9.0", "react-simplemde-editor": "^5.2.0", "react-window": "1.8.10", "recharts": "^2.15.0", "styled-components": "^6.1.14", "zustand": "^5.0.3"}, "devDependencies": {"@babel/core": "^7.26.10", "@babel/preset-react": "^7.26.3", "@babel/preset-typescript": "^7.27.0", "@babel/runtime": "^7.27.0", "@codecov/vite-plugin": "^1.9.0", "@lingui/babel-plugin-lingui-macro": "^5.3.0", "@lingui/cli": "^5.3.1", "@lingui/macro": "^5.3.0", "@playwright/test": "^1.49.1", "@types/node": "^22.13.14", "@types/qrcode": "^1.5.5", "@types/react": "^18.3.8", "@types/react-dom": "^18.3.0", "@types/react-grid-layout": "^1.3.5", "@types/react-router-dom": "^5.3.3", "@types/react-window": "^1.8.8", "@vanilla-extract/vite-plugin": "^5.0.1", "@vitejs/plugin-react": "^4.3.4", "babel-plugin-macros": "^3.1.0", "nyc": "^17.1.0", "path": "^0.12.7", "rollup": "^4.0.0", "rollup-plugin-license": "^3.5.3", "typescript": "^5.8.2", "vite": "^6.2.6", "vite-plugin-babel-macros": "^1.0.6", "vite-plugin-dts": "^4.5.3", "vite-plugin-istanbul": "^6.0.2"}}