# Generated by Django 3.2.14 on 2022-08-01 00:44

from django.db import migrations

from InvenTree.helpers import constructPathString


def update_pathstring(apps, schema_editor):
    """Construct pathstring for all existing StockLocation objects"""

    StockLocation = apps.get_model('stock', 'stocklocation')

    n = StockLocation.objects.count()

    if n > 0:

        for loc in StockLocation.objects.all():

            # Construct complete path for category
            path = [loc.name]

            parent = loc.parent

            # Iterate up the tree
            while parent is not None:
                path = [parent.name] + path
                parent = parent.parent

            pathstring = constructPathString(path)

            loc.pathstring = pathstring
            loc.save()

        print(f"\n--- Updated 'pathstring' for {n} StockLocation objects ---\n")


class Migration(migrations.Migration):

    dependencies = [
        ('stock', '0080_stocklocation_pathstring'),
    ]

    operations = [
        migrations.RunPython(
            update_pathstring,
            reverse_code=migrations.RunPython.noop
        )
    ]
