
import React, { useState } from 'react';
import { Truck, Package, Home, Clock } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Link } from 'react-router-dom';
import CurrencyFormatter from '@/components/ui/currency-formatter';
import Header from '@/components/Header';

const Delivery = () => {
  const [calculatorData, setCalculatorData] = useState({
    zipCode: '',
    weight: '',
    items: '',
    value: ''
  });
  const [calculatedCosts, setCalculatedCosts] = useState({
    standard: 0,
    express: 3500,
    calculated: false
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setCalculatorData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const calculateDeliveryCost = () => {
    const weight = parseFloat(calculatorData.weight) || 0;
    const items = parseInt(calculatorData.items) || 0;
    const value = parseFloat(calculatorData.value) || 0;

    // Basic calculation logic
    let standardCost = 0;
    let expressCost = 3500;

    // Free delivery for orders over KSH 10,000
    if (value >= 10000) {
      standardCost = 0;
    } else {
      // Base cost + weight-based cost + item-based cost
      standardCost = 1500 + (weight * 50) + (items * 100);
    }

    // Express delivery is always charged
    expressCost = 3500 + (weight * 75) + (items * 150);

    setCalculatedCosts({
      standard: standardCost,
      express: expressCost,
      calculated: true
    });
  };
  return (
    <div className="min-h-screen bg-forest-grey-50">
      <Header />
      
      <div className="container mx-auto px-4 py-8">
        {/* Hero Section */}
        <div className="bg-gradient-to-r from-forest-green-600 to-forest-brown-600 rounded-lg p-8 mb-8 text-white">
          <div className="text-center">
            <Truck className="w-16 h-16 mx-auto mb-4" />
            <h1 className="text-4xl md:text-5xl font-bold mb-4">Delivery Services</h1>
            <p className="text-xl text-forest-green-100">
              Fast, reliable delivery of building materials right to your doorstep
            </p>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">
          {/* Delivery Options */}
          <div>
            <h2 className="text-3xl font-bold text-forest-grey-800 mb-6">Delivery Options</h2>
            
            <div className="space-y-4">
              <Card className="border-forest-green-200 hover:shadow-md transition-shadow">
                <CardHeader>
                  <div className="flex items-center space-x-3">
                    <div className="w-12 h-12 bg-forest-green-500 rounded-lg flex items-center justify-center">
                      <Truck className="w-6 h-6 text-white" />
                    </div>
                    <div>
                      <CardTitle className="text-forest-grey-800">Standard Delivery</CardTitle>
                      <CardDescription>3-5 business days</CardDescription>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2 text-forest-grey-600">
                    <li>• Free on orders over KSH 10,000</li>
                    <li>• KSH 1,500 delivery fee for orders under KSH 10,000</li>
                    <li>• Delivery to curb or garage</li>
                    <li>• Tracking information provided</li>
                  </ul>
                </CardContent>
              </Card>

              <Card className="border-forest-brown-200 hover:shadow-md transition-shadow">
                <CardHeader>
                  <div className="flex items-center space-x-3">
                    <div className="w-12 h-12 bg-forest-brown-500 rounded-lg flex items-center justify-center">
                      <Clock className="w-6 h-6 text-white" />
                    </div>
                    <div>
                      <CardTitle className="text-forest-grey-800">Express Delivery</CardTitle>
                      <CardDescription>Next business day</CardDescription>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2 text-forest-grey-600">
                    <li>• KSH 3,500 delivery fee</li>
                    <li>• Available within Nairobi and surrounding areas</li>
                    <li>• Order by 2 PM for next-day delivery</li>
                    <li>• SMS notifications included</li>
                  </ul>
                </CardContent>
              </Card>

              <Card className="border-forest-green-300 hover:shadow-md transition-shadow">
                <CardHeader>
                  <div className="flex items-center space-x-3">
                    <div className="w-12 h-12 bg-forest-green-600 rounded-lg flex items-center justify-center">
                      <Home className="w-6 h-6 text-white" />
                    </div>
                    <div>
                      <CardTitle className="text-forest-grey-800">White Glove Service</CardTitle>
                      <CardDescription>Professional installation available</CardDescription>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2 text-forest-grey-600">
                    <li>• Inside delivery and placement</li>
                    <li>• Professional installation service</li>
                    <li>• Debris removal included</li>
                    <li>• Quote provided upon request</li>
                  </ul>
                </CardContent>
              </Card>
            </div>
          </div>

          {/* Delivery Calculator */}
          <div>
            <h2 className="text-3xl font-bold text-forest-grey-800 mb-6">Calculate Delivery Cost</h2>
            
            <Card>
              <CardHeader>
                <CardTitle className="text-forest-grey-800">Delivery Cost Calculator</CardTitle>
                <CardDescription>Enter your details to get an accurate delivery quote</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="zipCode">Area/Location</Label>
                  <Input
                    id="zipCode"
                    name="zipCode"
                    placeholder="e.g., Nairobi, Kiambu, Nakuru"
                    value={calculatorData.zipCode}
                    onChange={handleInputChange}
                  />
                </div>

                <div>
                  <Label htmlFor="weight">Estimated Weight (KG)</Label>
                  <Input
                    id="weight"
                    name="weight"
                    type="number"
                    placeholder="0"
                    value={calculatorData.weight}
                    onChange={handleInputChange}
                  />
                </div>

                <div>
                  <Label htmlFor="items">Number of Items</Label>
                  <Input
                    id="items"
                    name="items"
                    type="number"
                    placeholder="0"
                    value={calculatorData.items}
                    onChange={handleInputChange}
                  />
                </div>

                <div>
                  <Label htmlFor="value">Order Value (KSH)</Label>
                  <Input
                    id="value"
                    name="value"
                    type="number"
                    placeholder="0"
                    value={calculatorData.value}
                    onChange={handleInputChange}
                  />
                </div>

                <Button
                  className="w-full btn-primary"
                  onClick={calculateDeliveryCost}
                >
                  Calculate Delivery Cost
                </Button>

                <div className="mt-6 p-4 bg-forest-grey-100 rounded-lg">
                  <h4 className="font-medium text-forest-grey-800 mb-2">
                    {calculatedCosts.calculated ? 'Calculated Delivery Costs:' : 'Estimated Delivery:'}
                  </h4>
                  <div className="space-y-1 text-sm text-forest-grey-600">
                    <div className="flex justify-between">
                      <span>Standard (3-5 days):</span>
                      <span className="font-medium">
                        {calculatedCosts.calculated ? (
                          calculatedCosts.standard === 0 ? 'FREE' : <CurrencyFormatter amount={calculatedCosts.standard} />
                        ) : 'FREE*'}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span>Express (Next day):</span>
                      <span className="font-medium">
                        {calculatedCosts.calculated ? (
                          <CurrencyFormatter amount={calculatedCosts.express} />
                        ) : 'KSH 3,500+'}
                      </span>
                    </div>
                  </div>
                  {!calculatedCosts.calculated && (
                    <p className="text-xs text-forest-grey-500 mt-2">
                      *Free on orders over KSH 10,000
                    </p>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Delivery Areas */}
        <div className="bg-white rounded-lg p-8 mb-8">
          <h2 className="text-3xl font-bold text-forest-grey-800 mb-6 text-center">Delivery Areas</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="w-16 h-16 bg-forest-green-500 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-white font-bold text-xl">✓</span>
              </div>
              <h3 className="text-xl font-semibold text-forest-grey-800 mb-2">Local Delivery</h3>
              <p className="text-forest-grey-600">Within Nairobi - Free on orders over KSH 10,000</p>
            </div>
            
            <div className="text-center">
              <div className="w-16 h-16 bg-forest-brown-500 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-white font-bold text-xl">○</span>
              </div>
              <h3 className="text-xl font-semibold text-forest-grey-800 mb-2">Regional Delivery</h3>
              <p className="text-forest-grey-600">Central Kenya - Standard rates apply</p>
            </div>
            
            <div className="text-center">
              <div className="w-16 h-16 bg-forest-grey-500 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-white font-bold text-xl">?</span>
              </div>
              <h3 className="text-xl font-semibold text-forest-grey-800 mb-2">Extended Areas</h3>
              <p className="text-forest-grey-600">Nationwide - Custom quote required</p>
            </div>
          </div>
        </div>

        {/* Special Delivery Information */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          <Card>
            <CardHeader>
              <CardTitle className="text-forest-grey-800 flex items-center">
                <Package className="w-5 h-5 mr-2 text-forest-green-600" />
                Special Handling
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="space-y-2 text-forest-grey-600">
                <li>• Fragile items require special packaging</li>
                <li>• Heavy items (200+ KG) need crane delivery</li>
                <li>• Hazardous materials have shipping restrictions</li>
                <li>• Custom millwork requires protective crating</li>
              </ul>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-forest-grey-800 flex items-center">
                <Clock className="w-5 h-5 mr-2 text-forest-green-600" />
                Delivery Schedule
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="space-y-2 text-forest-grey-600">
                <li>• Monday - Friday: 7:00 AM - 5:00 PM</li>
                <li>• Saturday: 8:00 AM - 2:00 PM</li>
                <li>• Sunday: Emergency deliveries only</li>
                <li>• Holiday schedules may vary</li>
              </ul>
            </CardContent>
          </Card>
        </div>

        {/* Call to Action */}
        <div className="mt-12 bg-gradient-to-r from-forest-green-600 to-forest-brown-600 rounded-lg p-8 text-white text-center">
          <h2 className="text-3xl font-bold mb-4">Need Help with Your Delivery?</h2>
          <p className="text-xl mb-6 text-forest-green-100">
            Our delivery team is here to ensure your materials arrive safely and on time.
          </p>
          <Button size="lg" variant="outline" className="text-white border-white hover:bg-white hover:text-forest-green-600" asChild>
            <Link to="/contact">Contact Delivery Team</Link>
          </Button>
        </div>
      </div>
    </div>
  );
};

export default Delivery;
