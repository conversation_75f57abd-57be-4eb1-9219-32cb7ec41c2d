msgid ""
msgstr ""
"Project-Id-Version: inventree\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-09 19:12+0000\n"
"PO-Revision-Date: 2025-05-24 13:19\n"
"Last-Translator: \n"
"Language-Team: Italian\n"
"Language: it_IT\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Crowdin-Project: inventree\n"
"X-Crowdin-Project-ID: 452300\n"
"X-Crowdin-Language: it\n"
"X-Crowdin-File: /src/backend/InvenTree/locale/en/LC_MESSAGES/django.po\n"
"X-Crowdin-File-ID: 250\n"

#: InvenTree/AllUserRequire2FAMiddleware.py:42
msgid "You must enable two-factor authentication before doing anything else."
msgstr "Devi abilitare l'autenticazione a due fattori prima di fare qualsiasi altra cosa."

#: InvenTree/api.py:355
msgid "API endpoint not found"
msgstr "Endpoint API non trovato"

#: InvenTree/api.py:432
msgid "List of items or filters must be provided for bulk operation"
msgstr "L'elenco degli articoli o dei filtri devono essere forniti per le operazioni di massa"

#: InvenTree/api.py:439
msgid "Items must be provided as a list"
msgstr "Gli articoli devono essere forniti come elenco"

#: InvenTree/api.py:447
msgid "Invalid items list provided"
msgstr "Lista elementi fornita non valida"

#: InvenTree/api.py:453
msgid "Filters must be provided as a dict"
msgstr ""

#: InvenTree/api.py:460
msgid "Invalid filters provided"
msgstr "Filtri forniti non validi"

#: InvenTree/api.py:465
msgid "All filter must only be used with true"
msgstr "Tutti i filtri devono essere usati solo con true"

#: InvenTree/api.py:470
msgid "No items match the provided criteria"
msgstr "Nessun elemento corrisponde ai criteri forniti"

#: InvenTree/api.py:713
msgid "User does not have permission to view this model"
msgstr "L'utente non ha i permessi per vedere questo modello"

#: InvenTree/auth_overrides.py:57
msgid "Email (again)"
msgstr "Email (ancora)"

#: InvenTree/auth_overrides.py:61
msgid "Email address confirmation"
msgstr "Conferma indirizzo email"

#: InvenTree/auth_overrides.py:84
msgid "You must type the same email each time."
msgstr "È necessario digitare la stessa e-mail ogni volta."

#: InvenTree/auth_overrides.py:126 InvenTree/auth_overrides.py:134
msgid "The provided primary email address is not valid."
msgstr "L'indirizzo email principale fornito non è valido."

#: InvenTree/auth_overrides.py:140
msgid "The provided email domain is not approved."
msgstr "L'indirizzo di posta elettronica fornito non è approvato."

#: InvenTree/conversion.py:162
#, python-brace-format
msgid "Invalid unit provided ({unit})"
msgstr "Unità fornita non valida ({unit})"

#: InvenTree/conversion.py:179
msgid "No value provided"
msgstr "Nessun valore specificato"

#: InvenTree/conversion.py:206
#, python-brace-format
msgid "Could not convert {original} to {unit}"
msgstr "Impossibile convertire {original} in {unit}"

#: InvenTree/conversion.py:208 InvenTree/conversion.py:222
#: InvenTree/helpers.py:548 order/models.py:707 order/models.py:975
msgid "Invalid quantity provided"
msgstr "Quantità inserita non valida"

#: InvenTree/exceptions.py:109
msgid "Error details can be found in the admin panel"
msgstr "I dettagli dell'errore possono essere trovati nel pannello di amministrazione"

#: InvenTree/fields.py:138
msgid "Enter date"
msgstr "Inserisci la data"

#: InvenTree/fields.py:161
msgid "Invalid decimal value"
msgstr "Valore decimale non valido"

#: InvenTree/fields.py:210 InvenTree/models.py:887 build/serializers.py:526
#: build/serializers.py:597 company/models.py:834 order/models.py:1579
#: part/models.py:3366
#: report/templates/report/inventree_build_order_report.html:172
#: stock/models.py:2708 stock/models.py:2832 stock/serializers.py:756
#: stock/serializers.py:918 stock/serializers.py:1048 stock/serializers.py:1100
#: stock/serializers.py:1419 stock/serializers.py:1508
#: stock/serializers.py:1684
msgid "Notes"
msgstr "Note"

#: InvenTree/format.py:166
#, python-brace-format
msgid "Value '{name}' does not appear in pattern format"
msgstr "Il valore '{name}' non è nel formato del pattern"

#: InvenTree/format.py:177
msgid "Provided value does not match required pattern: "
msgstr "Il valore fornito non corrisponde al modello richiesto: "

#: InvenTree/helpers.py:552
msgid "Cannot serialize more than 1000 items at once"
msgstr "Impossibile serializzare più di 1000 elementi contemporaneamente"

#: InvenTree/helpers.py:558
msgid "Empty serial number string"
msgstr "Numero seriale vuoto"

#: InvenTree/helpers.py:587
msgid "Duplicate serial"
msgstr "Seriale Duplicato"

#: InvenTree/helpers.py:619 InvenTree/helpers.py:662 InvenTree/helpers.py:680
#: InvenTree/helpers.py:687 InvenTree/helpers.py:706
#, python-brace-format
msgid "Invalid group: {group}"
msgstr "Gruppo non valido: {group}"

#: InvenTree/helpers.py:650
#, python-brace-format
msgid "Group range {group} exceeds allowed quantity ({expected_quantity})"
msgstr "L'intervallo di gruppo {group} supera la quantità consentita ({expected_quantity})"

#: InvenTree/helpers.py:716
msgid "No serial numbers found"
msgstr "Nessun numero di serie trovato"

#: InvenTree/helpers.py:721
msgid "Number of unique serial numbers ({len(serials)}) must match quantity ({expected_quantity})"
msgstr "Il numero di numeri di serie univoci ({len(serials)}) deve corrispondere alla quantità ({expected_quantity})"

#: InvenTree/helpers.py:840
msgid "Remove HTML tags from this value"
msgstr "Rimuovi i tag HTML da questo valore"

#: InvenTree/helpers.py:919
msgid "Data contains prohibited markdown content"
msgstr "I dati contengono un contenuto in markdown proibito"

#: InvenTree/helpers_model.py:131
msgid "Connection error"
msgstr "Errore di connessione"

#: InvenTree/helpers_model.py:136 InvenTree/helpers_model.py:143
msgid "Server responded with invalid status code"
msgstr "Il server ha risposto con un codice di stato non valido"

#: InvenTree/helpers_model.py:139
msgid "Exception occurred"
msgstr "Si è verificata un'eccezione"

#: InvenTree/helpers_model.py:149
msgid "Server responded with invalid Content-Length value"
msgstr "Il server ha risposto con valore Content-Length non valido"

#: InvenTree/helpers_model.py:152
msgid "Image size is too large"
msgstr "Immagine troppo grande"

#: InvenTree/helpers_model.py:164
msgid "Image download exceeded maximum size"
msgstr "Il download dell'immagine ha superato la dimensione massima"

#: InvenTree/helpers_model.py:169
msgid "Remote server returned empty response"
msgstr "Il server remoto ha restituito una risposta vuota"

#: InvenTree/helpers_model.py:177
msgid "Supplied URL is not a valid image file"
msgstr "L'URL fornito non è un file immagine valido"

#: InvenTree/locales.py:20
msgid "Arabic"
msgstr "Arabo"

#: InvenTree/locales.py:21
msgid "Bulgarian"
msgstr "Bulgaro"

#: InvenTree/locales.py:22
msgid "Czech"
msgstr "Ceco"

#: InvenTree/locales.py:23
msgid "Danish"
msgstr "Danese"

#: InvenTree/locales.py:24
msgid "German"
msgstr "Tedesco"

#: InvenTree/locales.py:25
msgid "Greek"
msgstr "Greco"

#: InvenTree/locales.py:26
msgid "English"
msgstr "Inglese"

#: InvenTree/locales.py:27
msgid "Spanish"
msgstr "Spagnolo"

#: InvenTree/locales.py:28
msgid "Spanish (Mexican)"
msgstr "Spagnolo (Messicano)"

#: InvenTree/locales.py:29
msgid "Estonian"
msgstr "Estone"

#: InvenTree/locales.py:30
msgid "Farsi / Persian"
msgstr "Farsi / Persiano"

#: InvenTree/locales.py:31
msgid "Finnish"
msgstr "Finlandese"

#: InvenTree/locales.py:32
msgid "French"
msgstr "Francese"

#: InvenTree/locales.py:33
msgid "Hebrew"
msgstr "Ebraico"

#: InvenTree/locales.py:34
msgid "Hindi"
msgstr "Hindi"

#: InvenTree/locales.py:35
msgid "Hungarian"
msgstr "Ungherese"

#: InvenTree/locales.py:36
msgid "Italian"
msgstr "Italiano"

#: InvenTree/locales.py:37
msgid "Japanese"
msgstr "Giapponese"

#: InvenTree/locales.py:38
msgid "Korean"
msgstr "Coreano"

#: InvenTree/locales.py:39
msgid "Lithuanian"
msgstr "Lituano"

#: InvenTree/locales.py:40
msgid "Latvian"
msgstr "Lettone"

#: InvenTree/locales.py:41
msgid "Dutch"
msgstr "Olandese"

#: InvenTree/locales.py:42
msgid "Norwegian"
msgstr "Norvegese"

#: InvenTree/locales.py:43
msgid "Polish"
msgstr "Polacco"

#: InvenTree/locales.py:44
msgid "Portuguese"
msgstr "Portoghese"

#: InvenTree/locales.py:45
msgid "Portuguese (Brazilian)"
msgstr "Portoghese (Brasile)"

#: InvenTree/locales.py:46
msgid "Romanian"
msgstr "Rumeno"

#: InvenTree/locales.py:47
msgid "Russian"
msgstr "Russo"

#: InvenTree/locales.py:48
msgid "Slovak"
msgstr "Slovacco"

#: InvenTree/locales.py:49
msgid "Slovenian"
msgstr "Sloveno"

#: InvenTree/locales.py:50
msgid "Serbian"
msgstr "Serbo"

#: InvenTree/locales.py:51
msgid "Swedish"
msgstr "Svedese"

#: InvenTree/locales.py:52
msgid "Thai"
msgstr "Thailandese"

#: InvenTree/locales.py:53
msgid "Turkish"
msgstr "Turco"

#: InvenTree/locales.py:54
msgid "Ukrainian"
msgstr "Ucraino"

#: InvenTree/locales.py:55
msgid "Vietnamese"
msgstr "Vietnamita"

#: InvenTree/locales.py:56
msgid "Chinese (Simplified)"
msgstr "Cinese (Semplificato)"

#: InvenTree/locales.py:57
msgid "Chinese (Traditional)"
msgstr "Cinese (Tradizionale)"

#: InvenTree/magic_login.py:31
msgid "Log in to the app"
msgstr "Accedi all'app"

#: InvenTree/magic_login.py:41 company/models.py:136 users/serializers.py:247
msgid "Email"
msgstr "Email"

#: InvenTree/models.py:107
msgid "Error running plugin validation"
msgstr "Errore nell'eseguire la convalida del plugin"

#: InvenTree/models.py:184
msgid "Metadata must be a python dict object"
msgstr "I metadati devono essere un oggetto python dict"

#: InvenTree/models.py:190
msgid "Plugin Metadata"
msgstr "Metadati Plugin"

#: InvenTree/models.py:191
msgid "JSON metadata field, for use by external plugins"
msgstr "Campo di metadati JSON, da utilizzare con plugin esterni"

#: InvenTree/models.py:374
msgid "Improperly formatted pattern"
msgstr "Schema formattato impropriamente"

#: InvenTree/models.py:381
msgid "Unknown format key specified"
msgstr "Formato chiave sconosciuta"

#: InvenTree/models.py:387
msgid "Missing required format key"
msgstr "Formato chiave mancante"

#: InvenTree/models.py:398
msgid "Reference field cannot be empty"
msgstr "Il campo di riferimento non può essere vuoto"

#: InvenTree/models.py:406
msgid "Reference must match required pattern"
msgstr "Il campo deve corrispondere al modello richiesto"

#: InvenTree/models.py:437
msgid "Reference number is too large"
msgstr "Numero di riferimento troppo grande"

#: InvenTree/models.py:688
msgid "Duplicate names cannot exist under the same parent"
msgstr "Nomi duplicati non possono esistere sotto lo stesso genitore"

#: InvenTree/models.py:705
msgid "Invalid choice"
msgstr "Scelta non valida"

#: InvenTree/models.py:734 common/models.py:1325 common/models.py:1752
#: common/models.py:2011 common/models.py:2136 common/serializers.py:488
#: company/models.py:593 generic/states/serializers.py:20 machine/models.py:24
#: part/models.py:1039 part/models.py:3837 plugin/models.py:53
#: report/models.py:203 stock/models.py:83
msgid "Name"
msgstr "Nome"

#: InvenTree/models.py:740 build/models.py:242 common/models.py:123
#: common/models.py:2143 common/models.py:2256 company/models.py:521
#: company/models.py:825 order/models.py:429 order/models.py:1615
#: part/models.py:1062 part/models.py:3852 report/models.py:209
#: report/models.py:714 report/models.py:740
#: report/templates/report/inventree_build_order_report.html:117
#: stock/models.py:89
msgid "Description"
msgstr "Descrizione"

#: InvenTree/models.py:741 stock/models.py:90
msgid "Description (optional)"
msgstr "Descrizione (opzionale)"

#: InvenTree/models.py:756 common/models.py:2309
msgid "Path"
msgstr "Percorso"

#: InvenTree/models.py:887
msgid "Markdown notes (optional)"
msgstr "Note di Markdown (opzionale)"

#: InvenTree/models.py:918
msgid "Barcode Data"
msgstr "Dati del Codice a Barre"

#: InvenTree/models.py:919
msgid "Third party barcode data"
msgstr "Dati Codice a Barre applicazioni di terze parti"

#: InvenTree/models.py:925
msgid "Barcode Hash"
msgstr "Codice a Barre"

#: InvenTree/models.py:926
msgid "Unique hash of barcode data"
msgstr "Codice univoco del codice a barre"

#: InvenTree/models.py:1003
msgid "Existing barcode found"
msgstr "Trovato codice a barre esistente"

#: InvenTree/models.py:1084
msgid "Task Failure"
msgstr "Fallimento Attività"

#: InvenTree/models.py:1085
#, python-brace-format
msgid "Background worker task '{f}' failed after {n} attempts"
msgstr "Attività di lavoro in background '{f}' fallita dopo {n} tentativi"

#: InvenTree/models.py:1112
msgid "Server Error"
msgstr "Errore del server"

#: InvenTree/models.py:1113
msgid "An error has been logged by the server."
msgstr "Un errore è stato loggato dal server."

#: InvenTree/serializers.py:69 part/models.py:4455
msgid "Must be a valid number"
msgstr "Deve essere un numero valido"

#: InvenTree/serializers.py:111 company/models.py:187 part/models.py:3184
msgid "Currency"
msgstr "Valuta"

#: InvenTree/serializers.py:114 part/serializers.py:1335
msgid "Select currency from available options"
msgstr "Selezionare la valuta dalle opzioni disponibili"

#: InvenTree/serializers.py:466
msgid "Invalid value"
msgstr "Valore non valido"

#: InvenTree/serializers.py:503
msgid "Remote Image"
msgstr "Immagine Remota"

#: InvenTree/serializers.py:504
msgid "URL of remote image file"
msgstr "URL del file immagine remota"

#: InvenTree/serializers.py:522
msgid "Downloading images from remote URL is not enabled"
msgstr "Il download delle immagini da URL remoto non è abilitato"

#: InvenTree/serializers.py:529
msgid "Failed to download image from remote URL"
msgstr "Impossibile scaricare l'immagine dall'URL remoto"

#: InvenTree/validators.py:30
msgid "Invalid physical unit"
msgstr "Unità fisica non valida"

#: InvenTree/validators.py:36
msgid "Not a valid currency code"
msgstr "Non è un codice valuta valido"

#: InvenTree/validators.py:116 InvenTree/validators.py:132
msgid "Overage value must not be negative"
msgstr "Il sovra-valore non può essere negativo"

#: InvenTree/validators.py:134
msgid "Overage must not exceed 100%"
msgstr "L'eccesso non deve superare il 100%"

#: InvenTree/validators.py:140
msgid "Invalid value for overage"
msgstr "Valore non valido per eccedenza"

#: build/api.py:39 order/api.py:104 order/api.py:263 order/serializers.py:128
msgid "Order Status"
msgstr "Stato dell'ordine"

#: build/api.py:65 build/models.py:254
msgid "Parent Build"
msgstr "Produzione Genitore"

#: build/api.py:69 build/api.py:768 order/api.py:512 order/api.py:732
#: order/api.py:1106 order/api.py:1334 stock/api.py:522
msgid "Include Variants"
msgstr "Includi Varianti"

#: build/api.py:85 build/api.py:470 build/api.py:782 build/models.py:260
#: build/serializers.py:1254 build/serializers.py:1376
#: build/serializers.py:1427 company/models.py:1044 company/serializers.py:432
#: order/api.py:291 order/api.py:295 order/api.py:880 order/api.py:1119
#: order/api.py:1122 order/models.py:1704 order/models.py:1863
#: order/models.py:1864 part/api.py:1448 part/api.py:1451 part/api.py:1517
#: part/api.py:1827 part/models.py:457 part/models.py:3195 part/models.py:3339
#: part/models.py:3487 part/models.py:3508 part/models.py:3530
#: part/models.py:3666 part/models.py:4027 part/models.py:4299
#: part/models.py:4665 part/serializers.py:1255 part/serializers.py:1924
#: report/templates/report/inventree_bill_of_materials_report.html:110
#: report/templates/report/inventree_bill_of_materials_report.html:137
#: report/templates/report/inventree_build_order_report.html:109
#: report/templates/report/inventree_purchase_order_report.html:27
#: report/templates/report/inventree_return_order_report.html:24
#: report/templates/report/inventree_sales_order_report.html:27
#: report/templates/report/inventree_sales_order_shipment_report.html:28
#: report/templates/report/inventree_stock_location_report.html:102
#: stock/api.py:535 stock/serializers.py:119 stock/serializers.py:171
#: stock/serializers.py:481 stock/serializers.py:643 stock/serializers.py:948
#: templates/email/build_order_completed.html:17
#: templates/email/build_order_required_stock.html:17
#: templates/email/low_stock_notification.html:15
#: templates/email/overdue_build_order.html:16
#: templates/email/part_event_notification.html:15
msgid "Part"
msgstr "Articolo"

#: build/api.py:105 build/api.py:108 part/api.py:1531 part/models.py:1080
#: part/models.py:3558 part/models.py:4136 part/serializers.py:1265
#: stock/api.py:818
msgid "Category"
msgstr "Categoria"

#: build/api.py:116 build/api.py:120
msgid "Ancestor Build"
msgstr "Produzione Antenata"

#: build/api.py:137 order/api.py:122
msgid "Assigned to me"
msgstr "Assegnato a me"

#: build/api.py:152
#: report/templates/report/inventree_build_order_report.html:152
msgid "Issued By"
msgstr "Inviato da"

#: build/api.py:167
msgid "Assigned To"
msgstr "Assegnato a"

#: build/api.py:202
msgid "Created before"
msgstr "Creato prima"

#: build/api.py:206
msgid "Created after"
msgstr "Creato dopo"

#: build/api.py:210
msgid "Has start date"
msgstr "Ha data d'inizio"

#: build/api.py:218
msgid "Start date before"
msgstr "Data d'inizio prima"

#: build/api.py:222
msgid "Start date after"
msgstr "Data d'inizio dopo"

#: build/api.py:226
msgid "Has target date"
msgstr "Ha data di fine"

#: build/api.py:234
msgid "Target date before"
msgstr "Data obiettivo prima"

#: build/api.py:238
msgid "Target date after"
msgstr "Data obiettivo dopo"

#: build/api.py:242
msgid "Completed before"
msgstr "Completato prima"

#: build/api.py:246
msgid "Completed after"
msgstr "Completato dopo"

#: build/api.py:249 order/api.py:219
msgid "Min Date"
msgstr "Data minima"

#: build/api.py:272 order/api.py:238
msgid "Max Date"
msgstr "Data massima"

#: build/api.py:297 build/api.py:300 part/api.py:224
msgid "Exclude Tree"
msgstr "Escludi Albero"

#: build/api.py:409
msgid "Build must be cancelled before it can be deleted"
msgstr "La produzione deve essere annullata prima di poter essere eliminata"

#: build/api.py:453 build/serializers.py:1392 part/models.py:4333
msgid "Consumable"
msgstr "Consumabile"

#: build/api.py:456 build/serializers.py:1395 part/models.py:4327
msgid "Optional"
msgstr "Opzionale"

#: build/api.py:459 common/setting/system.py:429 part/models.py:1211
#: part/serializers.py:1663 part/serializers.py:1672 stock/api.py:588
msgid "Assembly"
msgstr "Assemblaggio"

#: build/api.py:462
msgid "Tracked"
msgstr "Monitorato"

#: build/api.py:465 build/serializers.py:1398 part/models.py:1229
msgid "Testable"
msgstr "Testabile"

#: build/api.py:475 order/api.py:925
msgid "Order Outstanding"
msgstr "Ordine In Corso"

#: build/api.py:485 order/api.py:884
msgid "Allocated"
msgstr "Allocato"

#: build/api.py:495 company/models.py:889 company/serializers.py:427
#: templates/email/build_order_required_stock.html:19
#: templates/email/low_stock_notification.html:17
#: templates/email/part_event_notification.html:18
msgid "Available"
msgstr "Disponibile"

#: build/api.py:805 build/models.py:116
#: report/templates/report/inventree_build_order_report.html:105
#: stock/serializers.py:92 templates/email/build_order_completed.html:16
#: templates/email/overdue_build_order.html:15
msgid "Build Order"
msgstr "Ordine di Produzione"

#: build/models.py:117 users/ruleset.py:37
msgid "Build Orders"
msgstr "Ordini di Produzione"

#: build/models.py:162
msgid "Assembly BOM has not been validated"
msgstr "Assembly BOM non è stato convalidato"

#: build/models.py:169
msgid "Build order cannot be created for an inactive part"
msgstr "L'ordine di generazione non può essere creato per una parte inattiva"

#: build/models.py:176
msgid "Build order cannot be created for an unlocked part"
msgstr "L'ordine di compilazione non può essere creato per una parte sbloccata"

#: build/models.py:188
msgid "Invalid choice for parent build"
msgstr "Scelta non valida per la produzione genitore"

#: build/models.py:197 order/models.py:363
msgid "Responsible user or group must be specified"
msgstr "L'utente o il gruppo responsabile deve essere specificato"

#: build/models.py:202
msgid "Build order part cannot be changed"
msgstr "L'ordine di costruzione della parte non può essere cambiata"

#: build/models.py:207 order/models.py:376
msgid "Target date must be after start date"
msgstr "La data di scadenza deve essere successiva alla data d'inizio"

#: build/models.py:235
msgid "Build Order Reference"
msgstr "Riferimento Ordine Di Produzione"

#: build/models.py:236 build/serializers.py:1389 order/models.py:601
#: order/models.py:1140 order/models.py:1572 order/models.py:2403
#: part/models.py:4348
#: report/templates/report/inventree_bill_of_materials_report.html:139
#: report/templates/report/inventree_purchase_order_report.html:28
#: report/templates/report/inventree_return_order_report.html:26
#: report/templates/report/inventree_sales_order_report.html:28
msgid "Reference"
msgstr "Riferimento"

#: build/models.py:245
msgid "Brief description of the build (optional)"
msgstr "Breve descrizione della build (facoltativo)"

#: build/models.py:255
msgid "BuildOrder to which this build is allocated"
msgstr "Ordine di produzione a cui questa produzione viene assegnata"

#: build/models.py:264
msgid "Select part to build"
msgstr "Selezionare parte da produrre"

#: build/models.py:269
msgid "Sales Order Reference"
msgstr "Numero di riferimento ordine di vendita"

#: build/models.py:274
msgid "SalesOrder to which this build is allocated"
msgstr "Ordine di vendita a cui questa produzione viene assegnata"

#: build/models.py:279 build/serializers.py:1104
msgid "Source Location"
msgstr "Posizione Di Origine"

#: build/models.py:285
msgid "Select location to take stock from for this build (leave blank to take from any stock location)"
msgstr "Seleziona la posizione da cui prelevare la giacenza (lasciare vuoto per prelevare da qualsiasi posizione di magazzino)"

#: build/models.py:291
msgid "Destination Location"
msgstr "Posizione Della Destinazione"

#: build/models.py:296
msgid "Select location where the completed items will be stored"
msgstr "Seleziona il luogo in cui gli articoli completati saranno immagazzinati"

#: build/models.py:300
msgid "Build Quantity"
msgstr "Quantità Produzione"

#: build/models.py:303
msgid "Number of stock items to build"
msgstr "Numero di articoli da costruire"

#: build/models.py:307
msgid "Completed items"
msgstr "Articoli completati"

#: build/models.py:309
msgid "Number of stock items which have been completed"
msgstr "Numero di articoli di magazzino che sono stati completati"

#: build/models.py:313
msgid "Build Status"
msgstr "Stato Produzione"

#: build/models.py:318
msgid "Build status code"
msgstr "Codice stato di produzione"

#: build/models.py:327 build/serializers.py:376 order/serializers.py:785
#: stock/models.py:1023 stock/serializers.py:84 stock/serializers.py:1651
msgid "Batch Code"
msgstr "Codice Lotto"

#: build/models.py:331 build/serializers.py:377
msgid "Batch code for this build output"
msgstr "Codice del lotto per questa produzione"

#: build/models.py:335 order/models.py:466 order/serializers.py:166
#: part/models.py:1286
msgid "Creation Date"
msgstr "Data di creazione"

#: build/models.py:341
msgid "Build start date"
msgstr "Data inizio produzione"

#: build/models.py:342
msgid "Scheduled start date for this build order"
msgstr "Data d'inizio programmata per questo ordine di produzione"

#: build/models.py:348
msgid "Target completion date"
msgstr "Data completamento obiettivo"

#: build/models.py:350
msgid "Target date for build completion. Build will be overdue after this date."
msgstr "Data di completamento della produzione. Dopo tale data la produzione sarà in ritardo."

#: build/models.py:355 order/models.py:654 order/models.py:2442
msgid "Completion Date"
msgstr "Data di completamento"

#: build/models.py:363
msgid "completed by"
msgstr "Completato da"

#: build/models.py:372
msgid "Issued by"
msgstr "Rilasciato da"

#: build/models.py:373
msgid "User who issued this build order"
msgstr "Utente che ha emesso questo ordine di costruzione"

#: build/models.py:382 common/models.py:132 order/api.py:172
#: order/models.py:491 part/models.py:1303
#: report/templates/report/inventree_build_order_report.html:158
msgid "Responsible"
msgstr "Responsabile"

#: build/models.py:383
msgid "User or group responsible for this build order"
msgstr "Utente o gruppo responsabile di questo ordine di produzione"

#: build/models.py:388 stock/models.py:1016
msgid "External Link"
msgstr "Collegamento esterno"

#: build/models.py:390 common/models.py:1899 part/models.py:1114
#: stock/models.py:1018
msgid "Link to external URL"
msgstr "Link a URL esterno"

#: build/models.py:395
msgid "Build Priority"
msgstr "Priorità di produzione"

#: build/models.py:398
msgid "Priority of this build order"
msgstr "Priorità di questo ordine di produzione"

#: build/models.py:406 common/models.py:102 common/models.py:116
#: order/api.py:158 order/models.py:438
msgid "Project Code"
msgstr "Codice del progetto"

#: build/models.py:407
msgid "Project code for this build order"
msgstr "Codice del progetto per questo ordine di produzione"

#: build/models.py:680 build/models.py:803
msgid "Failed to offload task to complete build allocations"
msgstr "Impossibile scaricare l'attività per completare le allocazioni di build"

#: build/models.py:703
#, python-brace-format
msgid "Build order {build} has been completed"
msgstr "L'ordine di produzione {build} è stato completato"

#: build/models.py:709
msgid "A build order has been completed"
msgstr "L'ordine di produzione è stato completato"

#: build/models.py:881 build/serializers.py:424
msgid "Serial numbers must be provided for trackable parts"
msgstr "Deve essere fornita un numero di serie per gli articoli rintracciabili"

#: build/models.py:991 build/models.py:1076
msgid "No build output specified"
msgstr "Nessun output di produzione specificato"

#: build/models.py:994
msgid "Build output is already completed"
msgstr "La produzione è stata completata"

#: build/models.py:997
msgid "Build output does not match Build Order"
msgstr "L'output della produzione non corrisponde all'ordine di compilazione"

#: build/models.py:1079 build/serializers.py:303 build/serializers.py:352
#: build/serializers.py:972 order/models.py:704 order/serializers.py:601
#: order/serializers.py:780 part/serializers.py:1657 stock/models.py:863
#: stock/models.py:1730 stock/serializers.py:727
msgid "Quantity must be greater than zero"
msgstr "La quantità deve essere maggiore di zero"

#: build/models.py:1083 build/serializers.py:307
msgid "Quantity cannot be greater than the output quantity"
msgstr "La quantità non può essere maggiore della quantità in uscita"

#: build/models.py:1148 build/serializers.py:615
#, python-brace-format
msgid "Build output {serial} has not passed all required tests"
msgstr "L'output della build {serial} non ha superato tutti i test richiesti"

#: build/models.py:1534
msgid "Build Order Line Item"
msgstr "Elemento di Riga Ordine di Produzione"

#: build/models.py:1558
msgid "Build object"
msgstr "Crea oggetto"

#: build/models.py:1570 build/models.py:1829 build/serializers.py:291
#: build/serializers.py:337 build/serializers.py:1410 common/models.py:1255
#: order/models.py:1555 order/models.py:2288 order/serializers.py:1670
#: order/serializers.py:2131 part/models.py:3353 part/models.py:4321
#: part/serializers.py:275
#: report/templates/report/inventree_bill_of_materials_report.html:138
#: report/templates/report/inventree_build_order_report.html:113
#: report/templates/report/inventree_purchase_order_report.html:29
#: report/templates/report/inventree_sales_order_report.html:29
#: report/templates/report/inventree_sales_order_shipment_report.html:47
#: report/templates/report/inventree_sales_order_shipment_report.html:49
#: report/templates/report/inventree_stock_location_report.html:104
#: report/templates/report/inventree_test_report.html:90
#: report/templates/report/inventree_test_report.html:169
#: stock/serializers.py:135 stock/serializers.py:179 stock/serializers.py:718
#: templates/email/build_order_completed.html:18
msgid "Quantity"
msgstr "Quantità"

#: build/models.py:1571
msgid "Required quantity for build order"
msgstr "Quantità richiesta per l'ordine di costruzione"

#: build/models.py:1655
msgid "Build item must specify a build output, as master part is marked as trackable"
msgstr "L'elemento di compilazione deve specificare un output poiché la parte principale è contrassegnata come rintracciabile"

#: build/models.py:1666
#, python-brace-format
msgid "Allocated quantity ({q}) must not exceed available stock quantity ({a})"
msgstr "La quantità assegnata ({q}) non deve essere maggiore della quantità disponibile ({a})"

#: build/models.py:1687 order/models.py:2237
msgid "Stock item is over-allocated"
msgstr "L'articolo in giacenza è sovrallocato"

#: build/models.py:1692 order/models.py:2240
msgid "Allocation quantity must be greater than zero"
msgstr "La quantità di assegnazione deve essere maggiore di zero"

#: build/models.py:1698
msgid "Quantity must be 1 for serialized stock"
msgstr "La quantità deve essere 1 per lo stock serializzato"

#: build/models.py:1758
msgid "Selected stock item does not match BOM line"
msgstr "L'articolo in stock selezionato non corrisponde alla voce nella BOM"

#: build/models.py:1819 build/serializers.py:955 build/serializers.py:1263
#: order/serializers.py:1507 order/serializers.py:1528
#: report/templates/report/inventree_sales_order_shipment_report.html:29
#: stock/models.py:452 stock/serializers.py:101 stock/serializers.py:825
#: stock/serializers.py:1357 stock/serializers.py:1469
msgid "Stock Item"
msgstr "Articoli in magazzino"

#: build/models.py:1820
msgid "Source stock item"
msgstr "Origine giacenza articolo"

#: build/models.py:1830
msgid "Stock quantity to allocate to build"
msgstr "Quantità di magazzino da assegnare per la produzione"

#: build/models.py:1839
msgid "Install into"
msgstr "Installa in"

#: build/models.py:1840
msgid "Destination stock item"
msgstr "Destinazione articolo in giacenza"

#: build/serializers.py:116
msgid "Build Level"
msgstr "Livello Produzione"

#: build/serializers.py:125 part/stocktake.py:219
msgid "Part Name"
msgstr "Nome Articolo"

#: build/serializers.py:143
msgid "Project Code Label"
msgstr "Etichetta Codice Progetto"

#: build/serializers.py:155
msgid "Create Child Builds"
msgstr "Crea Produzioni Figlie"

#: build/serializers.py:156
msgid "Automatically generate child build orders"
msgstr "Genera automaticamente ordini di produzione figli"

#: build/serializers.py:239 build/serializers.py:981
msgid "Build Output"
msgstr "Genera Output"

#: build/serializers.py:251
msgid "Build output does not match the parent build"
msgstr "L'output generato non corrisponde alla produzione principale"

#: build/serializers.py:255
msgid "Output part does not match BuildOrder part"
msgstr "L'output non corrisponde alle parti dell'ordine di produzione"

#: build/serializers.py:259
msgid "This build output has already been completed"
msgstr "Questa produzione è stata già completata"

#: build/serializers.py:273
msgid "This build output is not fully allocated"
msgstr "Questo output non è stato completamente assegnato"

#: build/serializers.py:292 build/serializers.py:338
msgid "Enter quantity for build output"
msgstr "Inserisci la quantità per l'output di compilazione"

#: build/serializers.py:360
msgid "Integer quantity required for trackable parts"
msgstr "Quantità totale richiesta per articoli rintracciabili"

#: build/serializers.py:366
msgid "Integer quantity required, as the bill of materials contains trackable parts"
msgstr "Quantità totale richiesta, poiché la fattura dei materiali contiene articoli rintracciabili"

#: build/serializers.py:383 order/serializers.py:801 order/serializers.py:1674
#: stock/serializers.py:738
msgid "Serial Numbers"
msgstr "Codice Seriale"

#: build/serializers.py:384
msgid "Enter serial numbers for build outputs"
msgstr "Inserisci i numeri di serie per gli output di compilazione (build option)"

#: build/serializers.py:389 build/serializers.py:514 build/serializers.py:584
#: build/serializers.py:1267 build/serializers.py:1271 order/serializers.py:769
#: order/serializers.py:916 order/serializers.py:2022 part/serializers.py:1275
#: stock/serializers.py:110 stock/serializers.py:647 stock/serializers.py:749
#: stock/serializers.py:913 stock/serializers.py:1041 stock/serializers.py:1501
#: stock/serializers.py:1789 users/models.py:551
msgid "Location"
msgstr "Posizione"

#: build/serializers.py:390
msgid "Stock location for build output"
msgstr "Posizione dello stock per l'output della produzione"

#: build/serializers.py:405
msgid "Auto Allocate Serial Numbers"
msgstr "Numeri di Serie Assegnazione automatica"

#: build/serializers.py:407
msgid "Automatically allocate required items with matching serial numbers"
msgstr "Assegna automaticamente gli articoli richiesti con i numeri di serie corrispondenti"

#: build/serializers.py:440 order/serializers.py:894 stock/api.py:1077
#: stock/models.py:1753
msgid "The following serial numbers already exist or are invalid"
msgstr "I seguenti numeri di serie sono già esistenti o non sono validi"

#: build/serializers.py:482 build/serializers.py:538 build/serializers.py:622
msgid "A list of build outputs must be provided"
msgstr "Deve essere fornito un elenco dei risultati di produzione"

#: build/serializers.py:515
msgid "Stock location for scrapped outputs"
msgstr "Posizione dello stock per l'output di produzione rimosso"

#: build/serializers.py:521
msgid "Discard Allocations"
msgstr "Scarta Assegnazioni"

#: build/serializers.py:522
msgid "Discard any stock allocations for scrapped outputs"
msgstr "Scartare tutte le assegnazioni di magazzino per gli output rimossi"

#: build/serializers.py:527
msgid "Reason for scrapping build output(s)"
msgstr "Motivo dell'eliminazione degli output di compilazione"

#: build/serializers.py:585
msgid "Location for completed build outputs"
msgstr "Posizione per gli output di build completati"

#: build/serializers.py:593
msgid "Accept Incomplete Allocation"
msgstr "Accetta Assegnazione Incompleta"

#: build/serializers.py:594
msgid "Complete outputs if stock has not been fully allocated"
msgstr "Completa l'output se le scorte non sono state interamente assegnate"

#: build/serializers.py:709
msgid "Consume Allocated Stock"
msgstr "Consuma Giacenze Allocate"

#: build/serializers.py:710
msgid "Consume any stock which has already been allocated to this build"
msgstr "Consuma tutte le scorte che sono già state assegnate a questa produzione"

#: build/serializers.py:716
msgid "Remove Incomplete Outputs"
msgstr "Rimuovi Output Incompleti"

#: build/serializers.py:717
msgid "Delete any build outputs which have not been completed"
msgstr "Elimina gli output di produzione che non sono stati completati"

#: build/serializers.py:744
msgid "Not permitted"
msgstr "Non permesso"

#: build/serializers.py:745
msgid "Accept as consumed by this build order"
msgstr "Accetta come consumato da questo ordine di produzione"

#: build/serializers.py:746
msgid "Deallocate before completing this build order"
msgstr "Non assegnare prima di aver completato questo ordine di produzione"

#: build/serializers.py:773
msgid "Overallocated Stock"
msgstr "Giacenza in eccesso assegnata"

#: build/serializers.py:776
msgid "How do you want to handle extra stock items assigned to the build order"
msgstr "Come si desidera gestire gli elementi extra giacenza assegnati all'ordine di produzione"

#: build/serializers.py:787
msgid "Some stock items have been overallocated"
msgstr "Alcuni articoli di magazzino sono stati assegnati in eccedenza"

#: build/serializers.py:792
msgid "Accept Unallocated"
msgstr "Accetta Non Assegnato"

#: build/serializers.py:794
msgid "Accept that stock items have not been fully allocated to this build order"
msgstr "Accetta che gli elementi in giacenza non sono stati completamente assegnati a questo ordine di produzione"

#: build/serializers.py:805
msgid "Required stock has not been fully allocated"
msgstr "La giacenza richiesta non è stata completamente assegnata"

#: build/serializers.py:810 order/serializers.py:444 order/serializers.py:1575
msgid "Accept Incomplete"
msgstr "Accetta Incompleta"

#: build/serializers.py:812
msgid "Accept that the required number of build outputs have not been completed"
msgstr "Accetta che il numero richiesto di output di produzione non sia stato completato"

#: build/serializers.py:823
msgid "Required build quantity has not been completed"
msgstr "La quantità di produzione richiesta non è stata completata"

#: build/serializers.py:835
msgid "Build order has open child build orders"
msgstr "L'ordine di costruzione ha ancora degli ordini di costruzione figli"

#: build/serializers.py:838
msgid "Build order must be in production state"
msgstr "L'ordine di costruzione deve essere in stato di produzione"

#: build/serializers.py:841
msgid "Build order has incomplete outputs"
msgstr "L'ordine di produzione ha output incompleti"

#: build/serializers.py:880
msgid "Build Line"
msgstr "Linea di produzione"

#: build/serializers.py:888
msgid "Build output"
msgstr "Genera Output"

#: build/serializers.py:896
msgid "Build output must point to the same build"
msgstr "L'output di produzione deve puntare alla stessa produzione"

#: build/serializers.py:927
msgid "Build Line Item"
msgstr "Articolo linea di produzione"

#: build/serializers.py:945
msgid "bom_item.part must point to the same part as the build order"
msgstr "gli elementi degli articoli della distinta base devono puntare alla stessa parte dell'ordine di produzione"

#: build/serializers.py:961 stock/serializers.py:1370
msgid "Item must be in stock"
msgstr "L'articolo deve essere disponibile"

#: build/serializers.py:1004 order/serializers.py:1561
#, python-brace-format
msgid "Available quantity ({q}) exceeded"
msgstr "Quantità disponibile ({q}) superata"

#: build/serializers.py:1010
msgid "Build output must be specified for allocation of tracked parts"
msgstr "L'output di produzione deve essere specificato per l'ubicazione delle parti tracciate"

#: build/serializers.py:1018
msgid "Build output cannot be specified for allocation of untracked parts"
msgstr "L'output di produzione non deve essere specificato per l'ubicazione delle parti non tracciate"

#: build/serializers.py:1042 order/serializers.py:1834
msgid "Allocation items must be provided"
msgstr "Deve essere indicata l'allocazione dell'articolo"

#: build/serializers.py:1106
msgid "Stock location where parts are to be sourced (leave blank to take from any location)"
msgstr "Posizione dello stock in cui le parti devono prelevate (lasciare vuoto per prelevare da qualsiasi luogo)"

#: build/serializers.py:1115
msgid "Exclude Location"
msgstr "Escludi Ubicazione"

#: build/serializers.py:1116
msgid "Exclude stock items from this selected location"
msgstr "Escludi gli elementi stock da questa ubicazione selezionata"

#: build/serializers.py:1121
msgid "Interchangeable Stock"
msgstr "Scorte Intercambiabili"

#: build/serializers.py:1122
msgid "Stock items in multiple locations can be used interchangeably"
msgstr "Gli elementi in magazzino in più sedi possono essere utilizzati in modo intercambiabile"

#: build/serializers.py:1127
msgid "Substitute Stock"
msgstr "Sostituisci Giacenze"

#: build/serializers.py:1128
msgid "Allow allocation of substitute parts"
msgstr "Consenti l'allocazione delle parti sostitutive"

#: build/serializers.py:1133
msgid "Optional Items"
msgstr "Articoli Opzionali"

#: build/serializers.py:1134
msgid "Allocate optional BOM items to build order"
msgstr "Assegna gli elementi opzionali della distinta base all'ordine di produzione"

#: build/serializers.py:1156
msgid "Failed to start auto-allocation task"
msgstr "Impossibile avviare l'attività di auto-allocazione"

#: build/serializers.py:1230
msgid "BOM Reference"
msgstr "Riferimento BOM"

#: build/serializers.py:1236
msgid "BOM Part ID"
msgstr "Identificativo dell'Articolo BOM"

#: build/serializers.py:1243
msgid "BOM Part Name"
msgstr "Nome Articolo BOM"

#: build/serializers.py:1278 build/serializers.py:1434
msgid "Build"
msgstr "Costruzione"

#: build/serializers.py:1286 company/models.py:662 order/api.py:304
#: order/api.py:309 order/api.py:508 order/serializers.py:593
#: stock/models.py:959 stock/serializers.py:631
msgid "Supplier Part"
msgstr "Articolo Fornitore"

#: build/serializers.py:1294 stock/serializers.py:662
msgid "Allocated Quantity"
msgstr "Quantità assegnata"

#: build/serializers.py:1371
msgid "Build Reference"
msgstr "Riferimento Ordine Di Costruzione"

#: build/serializers.py:1381
msgid "Part Category Name"
msgstr "Nome Categoria Articolo"

#: build/serializers.py:1401 common/setting/system.py:453 part/models.py:1223
msgid "Trackable"
msgstr "Tracciabile"

#: build/serializers.py:1404
msgid "Inherited"
msgstr "Ereditato"

#: build/serializers.py:1407 part/models.py:4381
msgid "Allow Variants"
msgstr "Consenti Le Varianti"

#: build/serializers.py:1412 build/serializers.py:1416 part/models.py:4179
#: part/models.py:4657 stock/api.py:831
msgid "BOM Item"
msgstr "Distinta base (Bom)"

#: build/serializers.py:1445
msgid "Allocated Stock"
msgstr "Scorte Assegnate"

#: build/serializers.py:1447 company/serializers.py:424
#: order/serializers.py:1271 part/serializers.py:943 part/serializers.py:1690
msgid "On Order"
msgstr "Ordinato"

#: build/serializers.py:1449 order/serializers.py:1272 part/serializers.py:1694
msgid "In Production"
msgstr "In Produzione"

#: build/serializers.py:1451 part/serializers.py:964
msgid "External Stock"
msgstr "Scorte esterne"

#: build/serializers.py:1452 part/serializers.py:1723
msgid "Available Stock"
msgstr "Disponibilità in magazzino"

#: build/serializers.py:1454
msgid "Available Substitute Stock"
msgstr "Disponibili scorte alternative"

#: build/serializers.py:1457
msgid "Available Variant Stock"
msgstr "Disponibili varianti delle scorte"

#: build/status_codes.py:11 generic/states/tests.py:21
#: generic/states/tests.py:131 order/status_codes.py:12
#: order/status_codes.py:44 order/status_codes.py:76 order/status_codes.py:102
msgid "Pending"
msgstr "In attesa"

#: build/status_codes.py:12
msgid "Production"
msgstr "Produzione"

#: build/status_codes.py:13 order/status_codes.py:14 order/status_codes.py:51
#: order/status_codes.py:81
msgid "On Hold"
msgstr "In Attesa"

#: build/status_codes.py:14 order/status_codes.py:16 order/status_codes.py:53
#: order/status_codes.py:84
msgid "Cancelled"
msgstr "Annullato"

#: build/status_codes.py:15 generic/states/tests.py:23 importer/models.py:529
#: importer/status_codes.py:27 order/status_codes.py:15
#: order/status_codes.py:52 order/status_codes.py:83
msgid "Complete"
msgstr "Completo"

#: build/tasks.py:193
msgid "Stock required for build order"
msgstr "Giacenza richiesta per l'ordine di produzione"

#: build/tasks.py:267
msgid "Overdue Build Order"
msgstr "Ordine di produzione in ritardo"

#: build/tasks.py:272
#, python-brace-format
msgid "Build order {bo} is now overdue"
msgstr "L'ordine di produzione {bo} è in ritardo"

#: common/api.py:710
msgid "Is Link"
msgstr "È Un Connegamento"

#: common/api.py:718
msgid "Is File"
msgstr "E' un file"

#: common/api.py:761
msgid "User does not have permission to delete these attachments"
msgstr "L'utente non ha il permesso di eliminare questi allegati"

#: common/api.py:778
msgid "User does not have permission to delete this attachment"
msgstr "L'utente non ha il permesso di eliminare questo allegato"

#: common/currency.py:120
msgid "Invalid currency code"
msgstr "Codice valuta non valido"

#: common/currency.py:122
msgid "Duplicate currency code"
msgstr "Codice valuta duplicato"

#: common/currency.py:127
msgid "No valid currency codes provided"
msgstr "Nessun codice valuta valido fornito"

#: common/currency.py:144
msgid "No plugin"
msgstr "Nessun plugin"

#: common/models.py:89
msgid "Updated"
msgstr "Aggiornato"

#: common/models.py:90
msgid "Timestamp of last update"
msgstr "Orario dell'ultimo aggiornamento"

#: common/models.py:117
msgid "Unique project code"
msgstr "Codice unico del progetto"

#: common/models.py:124
msgid "Project description"
msgstr "Descrizione del progetto"

#: common/models.py:133
msgid "User or group responsible for this project"
msgstr "Utente o gruppo responsabile di questo progetto"

#: common/models.py:721 common/models.py:1187 common/models.py:1225
msgid "Settings key"
msgstr "Tasto impostazioni"

#: common/models.py:725
msgid "Settings value"
msgstr "Valore impostazioni"

#: common/models.py:780
msgid "Chosen value is not a valid option"
msgstr "Il valore specificato non è un opzione valida"

#: common/models.py:796
msgid "Value must be a boolean value"
msgstr "Il valore deve essere un valore booleano"

#: common/models.py:804
msgid "Value must be an integer value"
msgstr "Il valore deve essere un intero"

#: common/models.py:812
msgid "Value must be a valid number"
msgstr "Il valore deve essere un numero valido"

#: common/models.py:837
msgid "Value does not pass validation checks"
msgstr "Il valore non supera i controlli di convalida"

#: common/models.py:859
msgid "Key string must be unique"
msgstr "La stringa chiave deve essere univoca"

#: common/models.py:1233 common/models.py:1234 common/models.py:1338
#: common/models.py:1339 common/models.py:1584 common/models.py:1585
#: common/models.py:1915 common/models.py:1916 common/models.py:2297
#: importer/models.py:97 part/models.py:3376 part/models.py:3463
#: part/models.py:3537 part/models.py:3565 plugin/models.py:322
#: plugin/models.py:323 report/templates/report/inventree_test_report.html:105
#: users/models.py:130 users/models.py:503
msgid "User"
msgstr "Utente"

#: common/models.py:1256
msgid "Price break quantity"
msgstr "Quantità prezzo limite"

#: common/models.py:1263 company/serializers.py:567 order/models.py:1632
#: order/models.py:2734
msgid "Price"
msgstr "Prezzo"

#: common/models.py:1264
msgid "Unit price at specified quantity"
msgstr "Prezzo unitario in quantità specificata"

#: common/models.py:1315 common/models.py:1500
msgid "Endpoint"
msgstr "Scadenza"

#: common/models.py:1316
msgid "Endpoint at which this webhook is received"
msgstr "Scadenza in cui questa notifica viene ricevuta"

#: common/models.py:1326
msgid "Name for this webhook"
msgstr "Nome per questa notifica"

#: common/models.py:1330 common/models.py:2156 common/models.py:2263
#: company/models.py:164 company/models.py:799 machine/models.py:39
#: part/models.py:1246 plugin/models.py:68 stock/api.py:591 users/models.py:201
#: users/models.py:556 users/serializers.py:333
msgid "Active"
msgstr "Attivo"

#: common/models.py:1330
msgid "Is this webhook active"
msgstr "È questa notifica attiva"

#: common/models.py:1346 users/models.py:178
msgid "Token"
msgstr "Token"

#: common/models.py:1347
msgid "Token for access"
msgstr "Token per l'accesso"

#: common/models.py:1355
msgid "Secret"
msgstr "Segreto"

#: common/models.py:1356
msgid "Shared secret for HMAC"
msgstr "Segreto condiviso per HMAC"

#: common/models.py:1464
msgid "Message ID"
msgstr "ID Messaggio"

#: common/models.py:1465
msgid "Unique identifier for this message"
msgstr "Identificatore unico per questo messaggio"

#: common/models.py:1473
msgid "Host"
msgstr "Host"

#: common/models.py:1474
msgid "Host from which this message was received"
msgstr "Host da cui questo messaggio è stato ricevuto"

#: common/models.py:1482
msgid "Header"
msgstr "Intestazione"

#: common/models.py:1483
msgid "Header of this message"
msgstr "Intestazione di questo messaggio"

#: common/models.py:1490
msgid "Body"
msgstr "Contenuto"

#: common/models.py:1491
msgid "Body of this message"
msgstr "Contenuto di questo messaggio"

#: common/models.py:1501
msgid "Endpoint on which this message was received"
msgstr "Scadenza in cui questo messaggio è stato ricevuto"

#: common/models.py:1506
msgid "Worked on"
msgstr "Lavorato il"

#: common/models.py:1507
msgid "Was the work on this message finished?"
msgstr "Il lavoro su questo messaggio è terminato?"

#: common/models.py:1633
msgid "Id"
msgstr "Id"

#: common/models.py:1635 part/serializers.py:281
msgid "Title"
msgstr "Titolo"

#: common/models.py:1637 common/models.py:1898 company/models.py:149
#: company/models.py:445 company/models.py:512 company/models.py:816
#: order/models.py:444 order/models.py:1585 order/models.py:2056
#: part/models.py:1113
#: report/templates/report/inventree_build_order_report.html:164
msgid "Link"
msgstr "Collegamento"

#: common/models.py:1639
msgid "Published"
msgstr "Pubblicato"

#: common/models.py:1641
msgid "Author"
msgstr "Autore"

#: common/models.py:1643
msgid "Summary"
msgstr "Riepilogo"

#: common/models.py:1646
msgid "Read"
msgstr "Letto"

#: common/models.py:1646
msgid "Was this news item read?"
msgstr "Queste notizie sull'elemento sono state lette?"

#: common/models.py:1663 company/models.py:160 part/models.py:1124
#: report/templates/report/inventree_bill_of_materials_report.html:126
#: report/templates/report/inventree_bill_of_materials_report.html:148
#: report/templates/report/inventree_return_order_report.html:35
msgid "Image"
msgstr "Immagine"

#: common/models.py:1663
msgid "Image file"
msgstr "File immagine"

#: common/models.py:1675
msgid "Target model type for this image"
msgstr "Tipo di modello di destinazione per questa immagine"

#: common/models.py:1679
msgid "Target model ID for this image"
msgstr "ID modello di destinazione per questa immagine"

#: common/models.py:1701
msgid "Custom Unit"
msgstr "Unità Personalizzata"

#: common/models.py:1719
msgid "Unit symbol must be unique"
msgstr "Il simbolo dell'unità deve essere univoco"

#: common/models.py:1734
msgid "Unit name must be a valid identifier"
msgstr "Il nome dell'unità deve essere un identificatore valido"

#: common/models.py:1753
msgid "Unit name"
msgstr "Nome dell'unità"

#: common/models.py:1760
msgid "Symbol"
msgstr "Simbolo"

#: common/models.py:1761
msgid "Optional unit symbol"
msgstr "Simbolo unità opzionale"

#: common/models.py:1767
msgid "Definition"
msgstr "Definizione"

#: common/models.py:1768
msgid "Unit definition"
msgstr "Definizione unità"

#: common/models.py:1826 common/models.py:1889 stock/models.py:2827
#: stock/serializers.py:258
msgid "Attachment"
msgstr "Allegato"

#: common/models.py:1843
msgid "Missing file"
msgstr "File mancante"

#: common/models.py:1844
msgid "Missing external link"
msgstr "Link esterno mancante"

#: common/models.py:1881
msgid "Model type"
msgstr "Tipo modello"

#: common/models.py:1882
msgid "Target model type for image"
msgstr "Tipo di modello di destinazione per l'immagine"

#: common/models.py:1890
msgid "Select file to attach"
msgstr "Seleziona file da allegare"

#: common/models.py:1906
msgid "Comment"
msgstr "Commento"

#: common/models.py:1907
msgid "Attachment comment"
msgstr "Commento allegato"

#: common/models.py:1923
msgid "Upload date"
msgstr "Data caricamento"

#: common/models.py:1924
msgid "Date the file was uploaded"
msgstr "Data di caricamento del file"

#: common/models.py:1928
msgid "File size"
msgstr "Dimensione file"

#: common/models.py:1928
msgid "File size in bytes"
msgstr "Dimensioni file in byte"

#: common/models.py:1966 common/serializers.py:637
msgid "Invalid model type specified for attachment"
msgstr "Tipo di modello specificato per l'allegato non valido"

#: common/models.py:1987
msgid "Custom State"
msgstr "Stato Personalizzato"

#: common/models.py:1988
msgid "Custom States"
msgstr "Stati Personalizzati"

#: common/models.py:1993
msgid "Reference Status Set"
msgstr "Imposta Stato Di Riferimento"

#: common/models.py:1994
msgid "Status set that is extended with this custom state"
msgstr "Set di stato esteso con questo stato personalizzato"

#: common/models.py:1998 generic/states/serializers.py:18
msgid "Logical Key"
msgstr "Chiave Logica"

#: common/models.py:2000
msgid "State logical key that is equal to this custom state in business logic"
msgstr "Chiave logica dello stato che è uguale a questo stato personalizzato nella logica commerciale"

#: common/models.py:2005 common/models.py:2244 company/models.py:600
#: report/templates/report/inventree_test_report.html:104 stock/models.py:2819
msgid "Value"
msgstr "Valore"

#: common/models.py:2006
msgid "Numerical value that will be saved in the models database"
msgstr "Valore numerico che verrà salvato nel database dei modelli"

#: common/models.py:2012
msgid "Name of the state"
msgstr "Nome dello Stato"

#: common/models.py:2021 common/models.py:2250 generic/states/serializers.py:22
#: part/serializers.py:283
msgid "Label"
msgstr "Etichetta"

#: common/models.py:2022
msgid "Label that will be displayed in the frontend"
msgstr "Etichetta che verrà visualizzata nel frontend"

#: common/models.py:2029 generic/states/serializers.py:24
msgid "Color"
msgstr "Colore"

#: common/models.py:2030
msgid "Color that will be displayed in the frontend"
msgstr "Colore che verrà visualizzato nel frontend"

#: common/models.py:2038 part/serializers.py:285
msgid "Model"
msgstr "Modello"

#: common/models.py:2039
msgid "Model this state is associated with"
msgstr "Modello a cui questo stato è associato"

#: common/models.py:2054
msgid "Model must be selected"
msgstr "Il modello deve essere selezionato"

#: common/models.py:2057
msgid "Key must be selected"
msgstr "La chiave deve essere selezionata"

#: common/models.py:2060
msgid "Logical key must be selected"
msgstr "La chiave logica deve essere selezionata"

#: common/models.py:2064
msgid "Key must be different from logical key"
msgstr "La chiave deve essere diversa dalla chiave logica"

#: common/models.py:2071
msgid "Valid reference status class must be provided"
msgstr "Deve essere fornita una classe di stato di riferimento valida"

#: common/models.py:2077
msgid "Key must be different from the logical keys of the reference status"
msgstr "La chiave deve essere diversa dalle chiavi logiche dello stato di riferimento"

#: common/models.py:2084
msgid "Logical key must be in the logical keys of the reference status"
msgstr "La chiave logica deve essere nelle chiavi logiche dello stato di riferimento"

#: common/models.py:2091
msgid "Name must be different from the names of the reference status"
msgstr "Il nome deve essere diverso dai nomi dello stato di riferimento"

#: common/models.py:2131 common/models.py:2238 part/models.py:3876
msgid "Selection List"
msgstr "Elenco Selezioni"

#: common/models.py:2132
msgid "Selection Lists"
msgstr "Elenchi di Selezione"

#: common/models.py:2137
msgid "Name of the selection list"
msgstr "Nome dell'elenco di selezione"

#: common/models.py:2144
msgid "Description of the selection list"
msgstr "Descrizione della lista di selezione"

#: common/models.py:2150 part/models.py:1251
msgid "Locked"
msgstr "Bloccato"

#: common/models.py:2151
msgid "Is this selection list locked?"
msgstr "Questa lista di selezione è bloccata?"

#: common/models.py:2157
msgid "Can this selection list be used?"
msgstr "Questo elenco di selezione può essere utilizzato?"

#: common/models.py:2165
msgid "Source Plugin"
msgstr "Plugin Sorgente"

#: common/models.py:2166
msgid "Plugin which provides the selection list"
msgstr "Plugin che fornisce l'elenco di selezione"

#: common/models.py:2171
msgid "Source String"
msgstr "Stringa Sorgente"

#: common/models.py:2172
msgid "Optional string identifying the source used for this list"
msgstr "Stringa opzionale che identifica il sorgente usato per questa lista"

#: common/models.py:2181
msgid "Default Entry"
msgstr "Voce Predefinita"

#: common/models.py:2182
msgid "Default entry for this selection list"
msgstr "Voce predefinita per questo elenco di selezione"

#: common/models.py:2187
msgid "Created"
msgstr "Creato"

#: common/models.py:2188
msgid "Date and time that the selection list was created"
msgstr "Data e ora in cui è stato creato l'elenco di selezione"

#: common/models.py:2193
msgid "Last Updated"
msgstr "Ultimo aggiornamento"

#: common/models.py:2194
msgid "Date and time that the selection list was last updated"
msgstr "Data e ora in cui l'elenco di selezione è stato aggiornato"

#: common/models.py:2228
msgid "Selection List Entry"
msgstr "Voce Lista Selezione"

#: common/models.py:2229
msgid "Selection List Entries"
msgstr "Voci Lista Selezione"

#: common/models.py:2239
msgid "Selection list to which this entry belongs"
msgstr "Elenco di selezione a cui appartiene questa voce"

#: common/models.py:2245
msgid "Value of the selection list entry"
msgstr "Valore della voce della lista di selezione"

#: common/models.py:2251
msgid "Label for the selection list entry"
msgstr "Etichetta per la voce elenco di selezione"

#: common/models.py:2257
msgid "Description of the selection list entry"
msgstr "Descrizione della voce della lista di selezione"

#: common/models.py:2264
msgid "Is this selection list entry active?"
msgstr "Questa voce della lista di selezione è attiva?"

#: common/models.py:2282
msgid "Barcode Scan"
msgstr "Scansione Codice A Barre"

#: common/models.py:2286 importer/models.py:523 part/models.py:4041
msgid "Data"
msgstr "Dati"

#: common/models.py:2287
msgid "Barcode data"
msgstr "Dati del Codice a Barre"

#: common/models.py:2298
msgid "User who scanned the barcode"
msgstr "Utente che ha scannerizzato il codice a barre"

#: common/models.py:2303 importer/models.py:66
msgid "Timestamp"
msgstr "Data e ora"

#: common/models.py:2304
msgid "Date and time of the barcode scan"
msgstr "Data e ora della scansione del codice a barre"

#: common/models.py:2310
msgid "URL endpoint which processed the barcode"
msgstr "Endpoint URL che ha elaborato il codice a barre"

#: common/models.py:2317 order/models.py:1622 plugin/serializers.py:93
msgid "Context"
msgstr "Contesto"

#: common/models.py:2318
msgid "Context data for the barcode scan"
msgstr "Dati contestuali per la scansione del codice a barre"

#: common/models.py:2325
msgid "Response"
msgstr "Risposta"

#: common/models.py:2326
msgid "Response data from the barcode scan"
msgstr "Dati di risposta dalla scansione del codice a barre"

#: common/models.py:2332 report/templates/report/inventree_test_report.html:103
#: stock/models.py:2813
msgid "Result"
msgstr "Risultato"

#: common/models.py:2333
msgid "Was the barcode scan successful?"
msgstr "La scansione del codice a barre è riuscita?"

#: common/notifications.py:332
#, python-brace-format
msgid "New {verbose_name}"
msgstr "Nuovo {verbose_name}"

#: common/notifications.py:334
msgid "A new order has been created and assigned to you"
msgstr "Un nuovo ordine è stato creato e assegnato a te"

#: common/notifications.py:340
#, python-brace-format
msgid "{verbose_name} canceled"
msgstr "{verbose_name} cancellato"

#: common/notifications.py:342
msgid "A order that is assigned to you was canceled"
msgstr "Un ordine assegnato a te è stato annullato"

#: common/notifications.py:348 common/notifications.py:355 order/api.py:559
msgid "Items Received"
msgstr "Elemento ricevuto"

#: common/notifications.py:350
msgid "Items have been received against a purchase order"
msgstr "Gli elementi sono stati ricevuti a fronte di un ordine di acquisto"

#: common/notifications.py:357
msgid "Items have been received against a return order"
msgstr "Gli articoli sono stati ricevuti contro un ordine di reso"

#: common/notifications.py:505
msgid "Error raised by plugin"
msgstr "Errore generato dal plugin"

#: common/serializers.py:451
msgid "Is Running"
msgstr "In Esecuzione"

#: common/serializers.py:457
msgid "Pending Tasks"
msgstr "Attività in sospeso"

#: common/serializers.py:463
msgid "Scheduled Tasks"
msgstr "Attività pianificate"

#: common/serializers.py:469
msgid "Failed Tasks"
msgstr "Attività Fallite"

#: common/serializers.py:484
msgid "Task ID"
msgstr "ID Attività"

#: common/serializers.py:484
msgid "Unique task ID"
msgstr "ID attività univoco"

#: common/serializers.py:486
msgid "Lock"
msgstr "Blocco"

#: common/serializers.py:486
msgid "Lock time"
msgstr "Tempo di blocco"

#: common/serializers.py:488
msgid "Task name"
msgstr "Nome attività"

#: common/serializers.py:490
msgid "Function"
msgstr "Funzione"

#: common/serializers.py:490
msgid "Function name"
msgstr "Nome della funzione"

#: common/serializers.py:492
msgid "Arguments"
msgstr "Argomenti"

#: common/serializers.py:492
msgid "Task arguments"
msgstr "Argomenti attività"

#: common/serializers.py:495
msgid "Keyword Arguments"
msgstr "Argomenti Parole Chiave"

#: common/serializers.py:495
msgid "Task keyword arguments"
msgstr "Argomenti parole chiave attività"

#: common/serializers.py:605
msgid "Filename"
msgstr "Nome del file"

#: common/serializers.py:612 importer/models.py:86 report/api.py:41
#: report/models.py:277 report/serializers.py:53
msgid "Model Type"
msgstr "Tipo di modello"

#: common/serializers.py:640
msgid "User does not have permission to create or edit attachments for this model"
msgstr "L'utente non ha il permesso di creare o modificare allegati per questo modello"

#: common/serializers.py:684 common/serializers.py:787
msgid "Selection list is locked"
msgstr "Lista di selezione bloccata"

#: common/setting/system.py:97
msgid "No group"
msgstr "Nessun gruppo"

#: common/setting/system.py:156
msgid "Site URL is locked by configuration"
msgstr "L'URL del sito è bloccato dalla configurazione"

#: common/setting/system.py:167
msgid "Restart required"
msgstr "Riavvio richiesto"

#: common/setting/system.py:168
msgid "A setting has been changed which requires a server restart"
msgstr "È stata modificata un'impostazione che richiede un riavvio del server"

#: common/setting/system.py:174
msgid "Pending migrations"
msgstr "Migrazioni in sospeso"

#: common/setting/system.py:175
msgid "Number of pending database migrations"
msgstr ""

#: common/setting/system.py:180
msgid "Instance ID"
msgstr ""

#: common/setting/system.py:181
msgid "Unique identifier for this InvenTree instance"
msgstr ""

#: common/setting/system.py:186
msgid "Announce ID"
msgstr ""

#: common/setting/system.py:188
msgid "Announce the instance ID of the server in the server status info (unauthenticated)"
msgstr ""

#: common/setting/system.py:194
msgid "Server Instance Name"
msgstr "Nome Istanza Del Server"

#: common/setting/system.py:196
msgid "String descriptor for the server instance"
msgstr "Descrittore stringa per l'istanza del server"

#: common/setting/system.py:200
msgid "Use instance name"
msgstr "Utilizza nome istanza"

#: common/setting/system.py:201
msgid "Use the instance name in the title-bar"
msgstr "Usa il nome dell'istanza nella barra del titolo"

#: common/setting/system.py:206
msgid "Restrict showing `about`"
msgstr "Limita visualizzazione `Informazioni`"

#: common/setting/system.py:207
msgid "Show the `about` modal only to superusers"
msgstr "Mostra la modalità `Informazioni` solo ai superusers"

#: common/setting/system.py:212 company/models.py:108 company/models.py:109
msgid "Company name"
msgstr "Nome azienda"

#: common/setting/system.py:213
msgid "Internal company name"
msgstr "Nome interno dell'azienda"

#: common/setting/system.py:217
msgid "Base URL"
msgstr "URL Base"

#: common/setting/system.py:218
msgid "Base URL for server instance"
msgstr "URL di base per l'istanza del server"

#: common/setting/system.py:224
msgid "Default Currency"
msgstr "Valuta predefinita"

#: common/setting/system.py:225
msgid "Select base currency for pricing calculations"
msgstr ""

#: common/setting/system.py:231
msgid "Supported Currencies"
msgstr ""

#: common/setting/system.py:232
msgid "List of supported currency codes"
msgstr ""

#: common/setting/system.py:238
msgid "Currency Update Interval"
msgstr ""

#: common/setting/system.py:239
msgid "How often to update exchange rates (set to zero to disable)"
msgstr ""

#: common/setting/system.py:241 common/setting/system.py:281
#: common/setting/system.py:294 common/setting/system.py:302
#: common/setting/system.py:309 common/setting/system.py:318
#: common/setting/system.py:567 common/setting/system.py:587
#: common/setting/system.py:684 common/setting/system.py:1068
msgid "days"
msgstr "giorni"

#: common/setting/system.py:245
msgid "Currency Update Plugin"
msgstr ""

#: common/setting/system.py:246
msgid "Currency update plugin to use"
msgstr ""

#: common/setting/system.py:251
msgid "Download from URL"
msgstr "Scarica dall'URL"

#: common/setting/system.py:252
msgid "Allow download of remote images and files from external URL"
msgstr "Consenti il download di immagini e file remoti da URL esterno"

#: common/setting/system.py:257
msgid "Download Size Limit"
msgstr "Limite Dimensione Download"

#: common/setting/system.py:258
msgid "Maximum allowable download size for remote image"
msgstr "Dimensione massima consentita per il download dell'immagine remota"

#: common/setting/system.py:264
msgid "User-agent used to download from URL"
msgstr "User-agent utilizzato per scaricare dall'URL"

#: common/setting/system.py:266
msgid "Allow to override the user-agent used to download images and files from external URL (leave blank for the default)"
msgstr "Consenti di sovrascrivere l'user-agent utilizzato per scaricare immagini e file da URL esterno (lasciare vuoto per il predefinito)"

#: common/setting/system.py:271
msgid "Strict URL Validation"
msgstr ""

#: common/setting/system.py:272
msgid "Require schema specification when validating URLs"
msgstr ""

#: common/setting/system.py:277
msgid "Update Check Interval"
msgstr "Aggiorna intervallo di controllo"

#: common/setting/system.py:278
msgid "How often to check for updates (set to zero to disable)"
msgstr "Quanto spesso controllare gli aggiornamenti (impostare a zero per disabilitare)"

#: common/setting/system.py:284
msgid "Automatic Backup"
msgstr "Backup automatico"

#: common/setting/system.py:285
msgid "Enable automatic backup of database and media files"
msgstr "Abilita il backup automatico di database e file multimediali"

#: common/setting/system.py:290
msgid "Auto Backup Interval"
msgstr "Intervallo Di Backup Automatico"

#: common/setting/system.py:291
msgid "Specify number of days between automated backup events"
msgstr "Definisci i giorni intercorrenti tra un backup automatico e l'altro"

#: common/setting/system.py:297
msgid "Task Deletion Interval"
msgstr ""

#: common/setting/system.py:299
msgid "Background task results will be deleted after specified number of days"
msgstr "I risultati delle attività in background verranno eliminati dopo un determinato numero di giorni"

#: common/setting/system.py:306
msgid "Error Log Deletion Interval"
msgstr ""

#: common/setting/system.py:307
msgid "Error logs will be deleted after specified number of days"
msgstr "I log di errore verranno eliminati dopo il numero specificato di giorni"

#: common/setting/system.py:313
msgid "Notification Deletion Interval"
msgstr ""

#: common/setting/system.py:315
msgid "User notifications will be deleted after specified number of days"
msgstr "Le notifiche dell'utente verranno eliminate dopo il numero di giorni specificato"

#: common/setting/system.py:322
msgid "Barcode Support"
msgstr "Supporto Codice A Barre"

#: common/setting/system.py:323
msgid "Enable barcode scanner support in the web interface"
msgstr ""

#: common/setting/system.py:328
msgid "Store Barcode Results"
msgstr "Memorizza Risultati Barcode"

#: common/setting/system.py:329
msgid "Store barcode scan results in the database"
msgstr "Memorizza i risultati della scansione del codice a barre nel database"

#: common/setting/system.py:334
msgid "Barcode Scans Maximum Count"
msgstr "Numero Massimo Scansioni Barcode"

#: common/setting/system.py:335
msgid "Maximum number of barcode scan results to store"
msgstr "Numero massimo di risultati della scansione del codice a barre da memorizzare"

#: common/setting/system.py:340
msgid "Barcode Input Delay"
msgstr "Codice a barre inserito scaduto"

#: common/setting/system.py:341
msgid "Barcode input processing delay time"
msgstr "Tempo di ritardo di elaborazione codice a barre"

#: common/setting/system.py:347
msgid "Barcode Webcam Support"
msgstr "Codice a Barre Supporto Webcam"

#: common/setting/system.py:348
msgid "Allow barcode scanning via webcam in browser"
msgstr "Consenti la scansione del codice a barre tramite webcam nel browser"

#: common/setting/system.py:353
msgid "Barcode Show Data"
msgstr ""

#: common/setting/system.py:354
msgid "Display barcode data in browser as text"
msgstr ""

#: common/setting/system.py:359
msgid "Barcode Generation Plugin"
msgstr ""

#: common/setting/system.py:360
msgid "Plugin to use for internal barcode data generation"
msgstr ""

#: common/setting/system.py:365
msgid "Part Revisions"
msgstr ""

#: common/setting/system.py:366
msgid "Enable revision field for Part"
msgstr "Abilita il campo revisione per l'articolo"

#: common/setting/system.py:371
msgid "Assembly Revision Only"
msgstr ""

#: common/setting/system.py:372
msgid "Only allow revisions for assembly parts"
msgstr ""

#: common/setting/system.py:377
msgid "Allow Deletion from Assembly"
msgstr ""

#: common/setting/system.py:378
msgid "Allow deletion of parts which are used in an assembly"
msgstr ""

#: common/setting/system.py:383
msgid "IPN Regex"
msgstr ""

#: common/setting/system.py:384
msgid "Regular expression pattern for matching Part IPN"
msgstr "Schema di espressione regolare per l'articolo corrispondente IPN"

#: common/setting/system.py:387
msgid "Allow Duplicate IPN"
msgstr "Consenti duplicati IPN"

#: common/setting/system.py:388
msgid "Allow multiple parts to share the same IPN"
msgstr "Permetti a più articoli di condividere lo stesso IPN"

#: common/setting/system.py:393
msgid "Allow Editing IPN"
msgstr "Permetti modifiche al part number interno (IPN)"

#: common/setting/system.py:394
msgid "Allow changing the IPN value while editing a part"
msgstr "Consenti di modificare il valore del part number durante la modifica di un articolo"

#: common/setting/system.py:399
msgid "Copy Part BOM Data"
msgstr "Copia I Dati Della distinta base dell'articolo"

#: common/setting/system.py:400
msgid "Copy BOM data by default when duplicating a part"
msgstr "Copia i dati della Distinta Base predefinita quando duplichi un articolo"

#: common/setting/system.py:405
msgid "Copy Part Parameter Data"
msgstr "Copia I Dati Parametro dell'articolo"

#: common/setting/system.py:406
msgid "Copy parameter data by default when duplicating a part"
msgstr "Copia i dati dei parametri di default quando si duplica un articolo"

#: common/setting/system.py:411
msgid "Copy Part Test Data"
msgstr "Copia I Dati dell'Articolo Test"

#: common/setting/system.py:412
msgid "Copy test data by default when duplicating a part"
msgstr "Copia i dati di prova di default quando si duplica un articolo"

#: common/setting/system.py:417
msgid "Copy Category Parameter Templates"
msgstr "Copia Template Parametri Categoria"

#: common/setting/system.py:418
msgid "Copy category parameter templates when creating a part"
msgstr "Copia i modelli dei parametri categoria quando si crea un articolo"

#: common/setting/system.py:423 part/models.py:4035 report/models.py:357
#: report/models.py:563 report/serializers.py:90 report/serializers.py:131
#: stock/serializers.py:247
msgid "Template"
msgstr "Modello"

#: common/setting/system.py:424
msgid "Parts are templates by default"
msgstr "Gli articoli sono modelli per impostazione predefinita"

#: common/setting/system.py:430
msgid "Parts can be assembled from other components by default"
msgstr "Gli articoli possono essere assemblate da altri componenti per impostazione predefinita"

#: common/setting/system.py:435 part/models.py:1217 part/serializers.py:1677
#: part/serializers.py:1683
msgid "Component"
msgstr "Componente"

#: common/setting/system.py:436
msgid "Parts can be used as sub-components by default"
msgstr "Gli articoli possono essere assemblati da altri componenti per impostazione predefinita"

#: common/setting/system.py:441 part/models.py:1235
msgid "Purchaseable"
msgstr "Acquistabile"

#: common/setting/system.py:442
msgid "Parts are purchaseable by default"
msgstr "Gli articoli sono acquistabili per impostazione predefinita"

#: common/setting/system.py:447 part/models.py:1241 stock/api.py:592
msgid "Salable"
msgstr "Vendibile"

#: common/setting/system.py:448
msgid "Parts are salable by default"
msgstr "Gli articoli sono acquistabili per impostazione predefinita"

#: common/setting/system.py:454
msgid "Parts are trackable by default"
msgstr "Gli articoli sono tracciabili per impostazione predefinita"

#: common/setting/system.py:459 part/models.py:1257
msgid "Virtual"
msgstr "Virtuale"

#: common/setting/system.py:460
msgid "Parts are virtual by default"
msgstr "Gli articoli sono virtuali per impostazione predefinita"

#: common/setting/system.py:465
msgid "Show Import in Views"
msgstr "Mostra l'importazione nelle viste"

#: common/setting/system.py:466
msgid "Display the import wizard in some part views"
msgstr "Mostra la procedura guidata di importazione in alcune viste articoli"

#: common/setting/system.py:471
msgid "Show related parts"
msgstr "Mostra articoli correlati"

#: common/setting/system.py:472
msgid "Display related parts for a part"
msgstr "Visualizza parti correlate per ogni articolo"

#: common/setting/system.py:477
msgid "Initial Stock Data"
msgstr "Dati iniziali dello stock"

#: common/setting/system.py:478
msgid "Allow creation of initial stock when adding a new part"
msgstr "Consentire la creazione di uno stock iniziale quando si aggiunge una nuova parte"

#: common/setting/system.py:483
msgid "Initial Supplier Data"
msgstr "Dati iniziali del fornitore"

#: common/setting/system.py:485
msgid "Allow creation of initial supplier data when adding a new part"
msgstr "Consentire la creazione dei dati iniziali del fornitore quando si aggiunge una nuova parte"

#: common/setting/system.py:491
msgid "Part Name Display Format"
msgstr "Formato di visualizzazione del nome articolo"

#: common/setting/system.py:492
msgid "Format to display the part name"
msgstr "Formato per visualizzare il nome dell'articolo"

#: common/setting/system.py:498
msgid "Part Category Default Icon"
msgstr "Icona predefinita Categoria Articolo"

#: common/setting/system.py:499
msgid "Part category default icon (empty means no icon)"
msgstr "Icona predefinita Categoria Articolo (vuoto significa nessuna icona)"

#: common/setting/system.py:504
msgid "Enforce Parameter Units"
msgstr ""

#: common/setting/system.py:506
msgid "If units are provided, parameter values must match the specified units"
msgstr ""

#: common/setting/system.py:512
msgid "Minimum Pricing Decimal Places"
msgstr ""

#: common/setting/system.py:514
msgid "Minimum number of decimal places to display when rendering pricing data"
msgstr ""

#: common/setting/system.py:525
msgid "Maximum Pricing Decimal Places"
msgstr ""

#: common/setting/system.py:527
msgid "Maximum number of decimal places to display when rendering pricing data"
msgstr ""

#: common/setting/system.py:538
msgid "Use Supplier Pricing"
msgstr "Usa Prezzi Fornitore"

#: common/setting/system.py:540
msgid "Include supplier price breaks in overall pricing calculations"
msgstr "Includere le discontinuità di prezzo del fornitore nei calcoli generali dei prezzi"

#: common/setting/system.py:546
msgid "Purchase History Override"
msgstr "Ignora la Cronologia Acquisti"

#: common/setting/system.py:548
msgid "Historical purchase order pricing overrides supplier price breaks"
msgstr "Cronologia dei prezzi dell'ordine di acquisto del fornitore superati con discontinuità di prezzo"

#: common/setting/system.py:554
msgid "Use Stock Item Pricing"
msgstr "Utilizzare i prezzi degli articoli in stock"

#: common/setting/system.py:556
msgid "Use pricing from manually entered stock data for pricing calculations"
msgstr "Utilizzare i prezzi dei dati di magazzino inseriti manualmente per il calcolo dei prezzi"

#: common/setting/system.py:562
msgid "Stock Item Pricing Age"
msgstr "Età dei prezzi degli articoli in stock"

#: common/setting/system.py:564
msgid "Exclude stock items older than this number of days from pricing calculations"
msgstr "Escludere dal calcolo dei prezzi gli articoli in giacenza più vecchi di questo numero di giorni"

#: common/setting/system.py:571
msgid "Use Variant Pricing"
msgstr "Utilizza Variazione di Prezzo"

#: common/setting/system.py:572
msgid "Include variant pricing in overall pricing calculations"
msgstr "Includi la variante dei prezzi nei calcoli dei prezzi complessivi"

#: common/setting/system.py:577
msgid "Active Variants Only"
msgstr "Solo Varianti Attive"

#: common/setting/system.py:579
msgid "Only use active variant parts for calculating variant pricing"
msgstr "Utilizza solo articoli di varianti attive per calcolare i prezzi delle varianti"

#: common/setting/system.py:585
msgid "Pricing Rebuild Interval"
msgstr ""

#: common/setting/system.py:586
msgid "Number of days before part pricing is automatically updated"
msgstr "Numero di giorni prima che il prezzo dell'articolo venga aggiornato automaticamente"

#: common/setting/system.py:592
msgid "Internal Prices"
msgstr "Prezzi interni"

#: common/setting/system.py:593
msgid "Enable internal prices for parts"
msgstr "Abilita prezzi interni per gli articoli"

#: common/setting/system.py:598
msgid "Internal Price Override"
msgstr "Sovrascrivi Prezzo Interno"

#: common/setting/system.py:600
msgid "If available, internal prices override price range calculations"
msgstr "Se disponibile, i prezzi interni sostituiscono i calcoli della fascia di prezzo"

#: common/setting/system.py:606
msgid "Enable label printing"
msgstr "Abilita stampa etichette"

#: common/setting/system.py:607
msgid "Enable label printing from the web interface"
msgstr "Abilita la stampa di etichette dall'interfaccia web"

#: common/setting/system.py:612
msgid "Label Image DPI"
msgstr "Etichetta Immagine DPI"

#: common/setting/system.py:614
msgid "DPI resolution when generating image files to supply to label printing plugins"
msgstr "Risoluzione DPI quando si generano file di immagine da fornire ai plugin di stampa per etichette"

#: common/setting/system.py:620
msgid "Enable Reports"
msgstr "Abilita Report di Stampa"

#: common/setting/system.py:621
msgid "Enable generation of reports"
msgstr "Abilita generazione di report di stampa"

#: common/setting/system.py:626
msgid "Debug Mode"
msgstr "Modalità Debug"

#: common/setting/system.py:627
msgid "Generate reports in debug mode (HTML output)"
msgstr "Genera report in modalità debug (output HTML)"

#: common/setting/system.py:632
msgid "Log Report Errors"
msgstr ""

#: common/setting/system.py:633
msgid "Log errors which occur when generating reports"
msgstr ""

#: common/setting/system.py:638 plugin/builtin/labels/label_sheet.py:29
#: report/models.py:365
msgid "Page Size"
msgstr "Dimensioni pagina"

#: common/setting/system.py:639
msgid "Default page size for PDF reports"
msgstr "Dimensione predefinita della pagina per i report PDF"

#: common/setting/system.py:644
msgid "Globally Unique Serials"
msgstr "Seriali Unici Globali"

#: common/setting/system.py:645
msgid "Serial numbers for stock items must be globally unique"
msgstr "I numeri di serie per gli articoli di magazzino devono essere univoci"

#: common/setting/system.py:650
msgid "Autofill Serial Numbers"
msgstr "Auto Riempimento Numeri Seriali"

#: common/setting/system.py:651
msgid "Autofill serial numbers in forms"
msgstr "Auto riempimento numeri nel modulo"

#: common/setting/system.py:656
msgid "Delete Depleted Stock"
msgstr "Elimina scorte esaurite"

#: common/setting/system.py:657
msgid "Determines default behavior when a stock item is depleted"
msgstr ""

#: common/setting/system.py:662
msgid "Batch Code Template"
msgstr "Modello Codice a Barre"

#: common/setting/system.py:663
msgid "Template for generating default batch codes for stock items"
msgstr "Modello per la generazione di codici batch predefiniti per gli elementi stock"

#: common/setting/system.py:667
msgid "Stock Expiry"
msgstr "Scadenza giacenza"

#: common/setting/system.py:668
msgid "Enable stock expiry functionality"
msgstr "Abilita funzionalità di scadenza della giacenza"

#: common/setting/system.py:673
msgid "Sell Expired Stock"
msgstr "Vendi giacenza scaduta"

#: common/setting/system.py:674
msgid "Allow sale of expired stock"
msgstr "Consenti la vendita di stock scaduti"

#: common/setting/system.py:679
msgid "Stock Stale Time"
msgstr "Tempo di Scorta del Magazzino"

#: common/setting/system.py:681
msgid "Number of days stock items are considered stale before expiring"
msgstr "Numero di giorni in cui gli articoli in magazzino sono considerati obsoleti prima della scadenza"

#: common/setting/system.py:688
msgid "Build Expired Stock"
msgstr "Crea giacenza scaduta"

#: common/setting/system.py:689
msgid "Allow building with expired stock"
msgstr "Permetti produzione con stock scaduto"

#: common/setting/system.py:694
msgid "Stock Ownership Control"
msgstr "Controllo della proprietà della giacenza"

#: common/setting/system.py:695
msgid "Enable ownership control over stock locations and items"
msgstr "Abilita il controllo della proprietà sulle posizioni e gli oggetti in giacenza"

#: common/setting/system.py:700
msgid "Stock Location Default Icon"
msgstr "Icona Predefinita Ubicazione di Magazzino"

#: common/setting/system.py:701
msgid "Stock location default icon (empty means no icon)"
msgstr "Icona Predefinita Ubicazione di Magazzino (vuoto significa nessuna icona)"

#: common/setting/system.py:706
msgid "Show Installed Stock Items"
msgstr ""

#: common/setting/system.py:707
msgid "Display installed stock items in stock tables"
msgstr ""

#: common/setting/system.py:712
msgid "Check BOM when installing items"
msgstr ""

#: common/setting/system.py:714
msgid "Installed stock items must exist in the BOM for the parent part"
msgstr ""

#: common/setting/system.py:720
msgid "Allow Out of Stock Transfer"
msgstr ""

#: common/setting/system.py:722
msgid "Allow stock items which are not in stock to be transferred between stock locations"
msgstr ""

#: common/setting/system.py:728
msgid "Build Order Reference Pattern"
msgstr "Modello Di Riferimento Ordine Di Produzione"

#: common/setting/system.py:729
msgid "Required pattern for generating Build Order reference field"
msgstr "Modello richiesto per generare il campo di riferimento ordine di produzione"

#: common/setting/system.py:734 common/setting/system.py:788
#: common/setting/system.py:808 common/setting/system.py:844
msgid "Require Responsible Owner"
msgstr ""

#: common/setting/system.py:735 common/setting/system.py:789
#: common/setting/system.py:809 common/setting/system.py:845
msgid "A responsible owner must be assigned to each order"
msgstr ""

#: common/setting/system.py:740
msgid "Require Active Part"
msgstr ""

#: common/setting/system.py:741
msgid "Prevent build order creation for inactive parts"
msgstr ""

#: common/setting/system.py:746
msgid "Require Locked Part"
msgstr ""

#: common/setting/system.py:747
msgid "Prevent build order creation for unlocked parts"
msgstr "Impedisci la creazione di ordini di costruzione per le parti sbloccate"

#: common/setting/system.py:752
msgid "Require Valid BOM"
msgstr "Richiede un BOM valido"

#: common/setting/system.py:753
msgid "Prevent build order creation unless BOM has been validated"
msgstr "Previene la creazione di ordini di costruzione a meno che BOM non sia stato convalidato"

#: common/setting/system.py:758
msgid "Require Closed Child Orders"
msgstr "Richiedi Ordini Dei Figli Chiusi"

#: common/setting/system.py:760
msgid "Prevent build order completion until all child orders are closed"
msgstr "Impedisci il completamento dell'ordine di costruzione fino alla chiusura di tutti gli ordini figli"

#: common/setting/system.py:766
msgid "Block Until Tests Pass"
msgstr "Blocca Fino Al Passaggio Dei Test"

#: common/setting/system.py:768
msgid "Prevent build outputs from being completed until all required tests pass"
msgstr "Impedisci che gli output di costruzione siano completati fino al superamento di tutti i test richiesti"

#: common/setting/system.py:774
msgid "Enable Return Orders"
msgstr "Abilita Ordini Di Reso"

#: common/setting/system.py:775
msgid "Enable return order functionality in the user interface"
msgstr "Abilita la funzionalità ordine di reso nell'interfaccia utente"

#: common/setting/system.py:780
msgid "Return Order Reference Pattern"
msgstr "Motivo di Riferimento per ordine di reso"

#: common/setting/system.py:782
msgid "Required pattern for generating Return Order reference field"
msgstr "Modello richiesto per generare il campo di riferimento ordine di reso"

#: common/setting/system.py:794
msgid "Edit Completed Return Orders"
msgstr "Modifica Ordini Di Reso Completati"

#: common/setting/system.py:796
msgid "Allow editing of return orders after they have been completed"
msgstr "Consenti la modifica degli ordini di reso dopo che sono stati completati"

#: common/setting/system.py:802
msgid "Sales Order Reference Pattern"
msgstr "Modello Di Riferimento Ordine Di Vendita"

#: common/setting/system.py:803
msgid "Required pattern for generating Sales Order reference field"
msgstr "Modello richiesto per generare il campo di riferimento ordine di vendita"

#: common/setting/system.py:814
msgid "Sales Order Default Shipment"
msgstr "Spedizione Predefinita Ordine Di Vendita"

#: common/setting/system.py:815
msgid "Enable creation of default shipment with sales orders"
msgstr "Abilita la creazione di spedizioni predefinite con ordini di vendita"

#: common/setting/system.py:820
msgid "Edit Completed Sales Orders"
msgstr "Modifica Ordini Di Vendita Completati"

#: common/setting/system.py:822
msgid "Allow editing of sales orders after they have been shipped or completed"
msgstr "Consenti la modifica degli ordini di vendita dopo che sono stati spediti o completati"

#: common/setting/system.py:828
msgid "Mark Shipped Orders as Complete"
msgstr "Segna gli ordini spediti come completati"

#: common/setting/system.py:830
msgid "Sales orders marked as shipped will automatically be completed, bypassing the \"shipped\" status"
msgstr "Gli ordini di vendita contrassegnati come spediti saranno automaticamente completati, bypassando lo stato \"spedito\""

#: common/setting/system.py:836
msgid "Purchase Order Reference Pattern"
msgstr "Modello di Riferimento Ordine D'Acquisto"

#: common/setting/system.py:838
msgid "Required pattern for generating Purchase Order reference field"
msgstr "Modello richiesto per generare il campo di riferimento ordine di acquisto"

#: common/setting/system.py:850
msgid "Edit Completed Purchase Orders"
msgstr "Modifica Ordini Di Acquisto Completati"

#: common/setting/system.py:852
msgid "Allow editing of purchase orders after they have been shipped or completed"
msgstr "Consenti la modifica degli ordini di acquisto dopo che sono stati spediti o completati"

#: common/setting/system.py:858
msgid "Convert Currency"
msgstr "Converti Valuta"

#: common/setting/system.py:859
msgid "Convert item value to base currency when receiving stock"
msgstr "Converti il valore dell'elemento in valuta base quando si riceve lo stock"

#: common/setting/system.py:864
msgid "Auto Complete Purchase Orders"
msgstr "Completa Automaticamente Gli Ordini D'Acquisto"

#: common/setting/system.py:866
msgid "Automatically mark purchase orders as complete when all line items are received"
msgstr "Contrassegna automaticamente gli ordini di acquisto come completi quando tutti gli elementi della riga sono ricevuti"

#: common/setting/system.py:873
msgid "Enable password forgot"
msgstr "Abilita password dimenticata"

#: common/setting/system.py:874
msgid "Enable password forgot function on the login pages"
msgstr "Abilita la funzione password dimenticata nelle pagine di accesso"

#: common/setting/system.py:879
msgid "Enable registration"
msgstr "Abilita registrazione"

#: common/setting/system.py:880
msgid "Enable self-registration for users on the login pages"
msgstr "Abilita auto-registrazione per gli utenti nelle pagine di accesso"

#: common/setting/system.py:885
msgid "Enable SSO"
msgstr "SSO abilitato"

#: common/setting/system.py:886
msgid "Enable SSO on the login pages"
msgstr "Abilita SSO nelle pagine di accesso"

#: common/setting/system.py:891
msgid "Enable SSO registration"
msgstr "Abilita registrazione SSO"

#: common/setting/system.py:893
msgid "Enable self-registration via SSO for users on the login pages"
msgstr "Abilita l'auto-registrazione tramite SSO per gli utenti nelle pagine di accesso"

#: common/setting/system.py:899
msgid "Enable SSO group sync"
msgstr "Abilita sincronizzazione dei gruppi SSO"

#: common/setting/system.py:901
msgid "Enable synchronizing InvenTree groups with groups provided by the IdP"
msgstr "Abilita la sincronizzazione dei gruppi InvenTree con i gruppi forniti dall'IdP"

#: common/setting/system.py:907
msgid "SSO group key"
msgstr "Chiave gruppo SSO"

#: common/setting/system.py:908
msgid "The name of the groups claim attribute provided by the IdP"
msgstr "Il nome dell'attributo di richiesta di gruppi fornito dall'IdP"

#: common/setting/system.py:913
msgid "SSO group map"
msgstr "Mappa del gruppo SSO"

#: common/setting/system.py:915
msgid "A mapping from SSO groups to local InvenTree groups. If the local group does not exist, it will be created."
msgstr "Una mappatura dai gruppi SSO ai gruppi InvenTree locali. Se il gruppo locale non esiste, verrà creato."

#: common/setting/system.py:921
msgid "Remove groups outside of SSO"
msgstr "Rimuovere i gruppi al di fuori dell'SSO"

#: common/setting/system.py:923
msgid "Whether groups assigned to the user should be removed if they are not backend by the IdP. Disabling this setting might cause security issues"
msgstr "Indica se i gruppi assegnati all'utente debbano essere rimossi se non sono backend dall'IdP. La disattivazione di questa impostazione potrebbe causare problemi di sicurezza"

#: common/setting/system.py:929
msgid "Email required"
msgstr "Email richiesta"

#: common/setting/system.py:930
msgid "Require user to supply mail on signup"
msgstr "Richiedi all'utente di fornire una email al momento dell'iscrizione"

#: common/setting/system.py:935
msgid "Auto-fill SSO users"
msgstr "Riempimento automatico degli utenti SSO"

#: common/setting/system.py:936
msgid "Automatically fill out user-details from SSO account-data"
msgstr "Compila automaticamente i dettagli dell'utente dai dati dell'account SSO"

#: common/setting/system.py:941
msgid "Mail twice"
msgstr "Posta due volte"

#: common/setting/system.py:942
msgid "On signup ask users twice for their mail"
msgstr "Al momento della registrazione chiedere due volte all'utente l'indirizzo di posta elettronica"

#: common/setting/system.py:947
msgid "Password twice"
msgstr "Password due volte"

#: common/setting/system.py:948
msgid "On signup ask users twice for their password"
msgstr "Al momento della registrazione chiedere agli utenti due volte l'inserimento della password"

#: common/setting/system.py:953
msgid "Allowed domains"
msgstr "Domini consentiti"

#: common/setting/system.py:955
msgid "Restrict signup to certain domains (comma-separated, starting with @)"
msgstr "Limita la registrazione a determinati domini (separati da virgola, a partire da @)"

#: common/setting/system.py:961
msgid "Group on signup"
msgstr "Gruppo iscrizione"

#: common/setting/system.py:963
msgid "Group to which new users are assigned on registration. If SSO group sync is enabled, this group is only set if no group can be assigned from the IdP."
msgstr "Gruppo a cui i nuovi utenti sono assegnati alla registrazione. Se la sincronizzazione di gruppo SSO è abilitata, questo gruppo è impostato solo se nessun gruppo può essere assegnato dall'IdP."

#: common/setting/system.py:969
msgid "Enforce MFA"
msgstr "Applica MFA"

#: common/setting/system.py:970
msgid "Users must use multifactor security."
msgstr "Gli utenti devono utilizzare la sicurezza a due fattori."

#: common/setting/system.py:975
msgid "Check plugins on startup"
msgstr "Controlla i plugin all'avvio"

#: common/setting/system.py:977
msgid "Check that all plugins are installed on startup - enable in container environments"
msgstr "Controlla che tutti i plugin siano installati all'avvio - abilita in ambienti contenitore"

#: common/setting/system.py:984
msgid "Check for plugin updates"
msgstr "Controlla gli aggiornamenti dei plugin"

#: common/setting/system.py:985
msgid "Enable periodic checks for updates to installed plugins"
msgstr "Abilita controlli periodici per gli aggiornamenti dei plugin installati"

#: common/setting/system.py:991
msgid "Enable URL integration"
msgstr "Abilita l'integrazione URL"

#: common/setting/system.py:992
msgid "Enable plugins to add URL routes"
msgstr "Attiva plugin per aggiungere percorsi URL"

#: common/setting/system.py:998
msgid "Enable navigation integration"
msgstr "Attiva integrazione navigazione"

#: common/setting/system.py:999
msgid "Enable plugins to integrate into navigation"
msgstr "Abilita i plugin per l'integrazione nella navigazione"

#: common/setting/system.py:1005
msgid "Enable app integration"
msgstr "Abilita l'app integrata"

#: common/setting/system.py:1006
msgid "Enable plugins to add apps"
msgstr "Abilita plugin per aggiungere applicazioni"

#: common/setting/system.py:1012
msgid "Enable schedule integration"
msgstr "Abilita integrazione pianificazione"

#: common/setting/system.py:1013
msgid "Enable plugins to run scheduled tasks"
msgstr "Abilita i plugin per eseguire le attività pianificate"

#: common/setting/system.py:1019
msgid "Enable event integration"
msgstr "Abilita eventi integrati"

#: common/setting/system.py:1020
msgid "Enable plugins to respond to internal events"
msgstr "Abilita plugin per rispondere agli eventi interni"

#: common/setting/system.py:1026
msgid "Enable interface integration"
msgstr "Abilita integrazione interfaccia"

#: common/setting/system.py:1027
msgid "Enable plugins to integrate into the user interface"
msgstr "Abilita i plugin per l'integrazione nell'interfaccia utente"

#: common/setting/system.py:1033
msgid "Enable project codes"
msgstr "Abilita codici progetto"

#: common/setting/system.py:1034
msgid "Enable project codes for tracking projects"
msgstr "Abilita i codici del progetto per tracciare i progetti"

#: common/setting/system.py:1039
msgid "Stocktake Functionality"
msgstr "Funzionalità Dell'Inventario"

#: common/setting/system.py:1041
msgid "Enable stocktake functionality for recording stock levels and calculating stock value"
msgstr "Abilita la funzionalità d'inventario per la registrazione dei livelli di magazzino e il calcolo del valore di magazzino"

#: common/setting/system.py:1047
msgid "Exclude External Locations"
msgstr "Escludi Posizioni Esterne"

#: common/setting/system.py:1049
msgid "Exclude stock items in external locations from stocktake calculations"
msgstr "Escludere le scorte in sedi esterne dai calcoli delle scorte"

#: common/setting/system.py:1055
msgid "Automatic Stocktake Period"
msgstr "Inventario periodico automatico"

#: common/setting/system.py:1057
msgid "Number of days between automatic stocktake recording (set to zero to disable)"
msgstr "Numero di giorni tra la registrazione automatica dell'inventario (imposta 0 per disabilitare)"

#: common/setting/system.py:1063
msgid "Report Deletion Interval"
msgstr "Intervallo Di Eliminazione Report"

#: common/setting/system.py:1065
msgid "Stocktake reports will be deleted after specified number of days"
msgstr "I rapporti d'inventario verranno eliminati dopo il numero specificato di giorni"

#: common/setting/system.py:1072
msgid "Display Users full names"
msgstr "Visualizza i nomi completi degli utenti"

#: common/setting/system.py:1073
msgid "Display Users full names instead of usernames"
msgstr "Mostra nomi completi degli utenti invece che nomi utente"

#: common/setting/system.py:1078
msgid "Display User Profiles"
msgstr "Visualizza Profili Utente"

#: common/setting/system.py:1079
msgid "Display Users Profiles on their profile page"
msgstr "Visualizza i profili degli utenti sulla pagina del loro profilo"

#: common/setting/system.py:1084
msgid "Enable Test Station Data"
msgstr "Abilita Dati Stazione Di Prova"

#: common/setting/system.py:1085
msgid "Enable test station data collection for test results"
msgstr "Abilita la raccolta dati della stazione di prova per i risultati del test"

#: common/setting/system.py:1090
msgid "Create Template on Upload"
msgstr "Crea modello al caricamento"

#: common/setting/system.py:1092
msgid "Create a new test template when uploading test data which does not match an existing template"
msgstr "Crea un nuovo modello di test quando si caricano dati di test che non corrispondono a un modello esistente"

#: common/setting/user.py:23
msgid "Inline label display"
msgstr "Visualizzazione dell'etichetta in linea"

#: common/setting/user.py:25
msgid "Display PDF labels in the browser, instead of downloading as a file"
msgstr "Visualizza le etichette PDF nel browser, invece di scaricare come file"

#: common/setting/user.py:31
msgid "Default label printer"
msgstr "Stampante per etichette predefinita"

#: common/setting/user.py:32
msgid "Configure which label printer should be selected by default"
msgstr "Configura quale stampante di etichette deve essere selezionata per impostazione predefinita"

#: common/setting/user.py:37
msgid "Inline report display"
msgstr "Visualizzazione dell'etichetta in linea"

#: common/setting/user.py:39
msgid "Display PDF reports in the browser, instead of downloading as a file"
msgstr "Visualizza le etichette PDF nel browser, invece di scaricare come file"

#: common/setting/user.py:45
msgid "Search Parts"
msgstr "Cerca Articoli"

#: common/setting/user.py:46
msgid "Display parts in search preview window"
msgstr "Mostra articoli della ricerca nella finestra di anteprima"

#: common/setting/user.py:51
msgid "Search Supplier Parts"
msgstr "Trova Articoli del Fornitore"

#: common/setting/user.py:52
msgid "Display supplier parts in search preview window"
msgstr "Mostra articoli del fornitore nella finestra di anteprima"

#: common/setting/user.py:57
msgid "Search Manufacturer Parts"
msgstr "Cerca Articoli Produttore"

#: common/setting/user.py:58
msgid "Display manufacturer parts in search preview window"
msgstr "Mostra articoli del produttore nella finestra di anteprima"

#: common/setting/user.py:63
msgid "Hide Inactive Parts"
msgstr "Nascondi Articoli Inattivi"

#: common/setting/user.py:64
msgid "Excluded inactive parts from search preview window"
msgstr "Escludi articoli inattivi dalla finestra di anteprima della ricerca"

#: common/setting/user.py:69
msgid "Search Categories"
msgstr "Cerca Categorie"

#: common/setting/user.py:70
msgid "Display part categories in search preview window"
msgstr "Mostra categorie articolo nella finestra di anteprima di ricerca"

#: common/setting/user.py:75
msgid "Search Stock"
msgstr "Cerca Giacenze"

#: common/setting/user.py:76
msgid "Display stock items in search preview window"
msgstr "Mostra articoli in giacenza nella finestra di anteprima della ricerca"

#: common/setting/user.py:81
msgid "Hide Unavailable Stock Items"
msgstr "Nascondi elementi non disponibili"

#: common/setting/user.py:83
msgid "Exclude stock items which are not available from the search preview window"
msgstr "Escludi gli elementi stock che non sono disponibili dalla finestra di anteprima di ricerca"

#: common/setting/user.py:89
msgid "Search Locations"
msgstr "Cerca Ubicazioni"

#: common/setting/user.py:90
msgid "Display stock locations in search preview window"
msgstr "Mostra ubicazioni delle giacenze nella finestra di anteprima di ricerca"

#: common/setting/user.py:95
msgid "Search Companies"
msgstr "Cerca Aziende"

#: common/setting/user.py:96
msgid "Display companies in search preview window"
msgstr "Mostra le aziende nella finestra di anteprima di ricerca"

#: common/setting/user.py:101
msgid "Search Build Orders"
msgstr "Cerca Ordini Di Produzione"

#: common/setting/user.py:102
msgid "Display build orders in search preview window"
msgstr "Mostra gli ordini di produzione nella finestra di anteprima di ricerca"

#: common/setting/user.py:107
msgid "Search Purchase Orders"
msgstr "Cerca Ordini di Acquisto"

#: common/setting/user.py:108
msgid "Display purchase orders in search preview window"
msgstr "Mostra gli ordini di acquisto nella finestra di anteprima di ricerca"

#: common/setting/user.py:113
msgid "Exclude Inactive Purchase Orders"
msgstr "Escludi Ordini D'Acquisto Inattivi"

#: common/setting/user.py:114
msgid "Exclude inactive purchase orders from search preview window"
msgstr "Escludi ordini di acquisto inattivi dalla finestra di anteprima di ricerca"

#: common/setting/user.py:119
msgid "Search Sales Orders"
msgstr "Cerca Ordini Di Vendita"

#: common/setting/user.py:120
msgid "Display sales orders in search preview window"
msgstr "Visualizzazione degli ordini di vendita nella finestra di anteprima della ricerca"

#: common/setting/user.py:125
msgid "Exclude Inactive Sales Orders"
msgstr "Escludi Ordini Di Vendita Inattivi"

#: common/setting/user.py:126
msgid "Exclude inactive sales orders from search preview window"
msgstr "Escludi ordini di vendita inattivi dalla finestra di anteprima di ricerca"

#: common/setting/user.py:131
msgid "Search Sales Order Shipments"
msgstr "Ricerca Spedizione Ordine Di Vendita"

#: common/setting/user.py:132
msgid "Display sales order shipments in search preview window"
msgstr "Mostra le spedizioni di ordini di vendita nella finestra di anteprima di ricerca"

#: common/setting/user.py:137
msgid "Search Return Orders"
msgstr "Cerca Ordini Di Reso"

#: common/setting/user.py:138
msgid "Display return orders in search preview window"
msgstr "Visualizza gli ordini di reso nella finestra di anteprima di ricerca"

#: common/setting/user.py:143
msgid "Exclude Inactive Return Orders"
msgstr "Escludi Ordini Inattivi Di Reso"

#: common/setting/user.py:144
msgid "Exclude inactive return orders from search preview window"
msgstr "Escludi ordini di reso inattivi dalla finestra di anteprima di ricerca"

#: common/setting/user.py:149
msgid "Search Preview Results"
msgstr "Risultati Dell'Anteprima Di Ricerca"

#: common/setting/user.py:151
msgid "Number of results to show in each section of the search preview window"
msgstr "Numero di risultati da visualizzare in ciascuna sezione della finestra di anteprima della ricerca"

#: common/setting/user.py:157
msgid "Regex Search"
msgstr "Ricerca con regex"

#: common/setting/user.py:158
msgid "Enable regular expressions in search queries"
msgstr "Abilita le espressioni regolari nelle query di ricerca"

#: common/setting/user.py:163
msgid "Whole Word Search"
msgstr "Ricerca Parole Intere"

#: common/setting/user.py:164
msgid "Search queries return results for whole word matches"
msgstr "Le query di ricerca restituiscono i risultati per intere corrispondenze di parola"

#: common/setting/user.py:169
msgid "Search Notes"
msgstr "Ricerca Note"

#: common/setting/user.py:171
msgid "Search queries return results for matches from the item's notes"
msgstr "Le query di ricerca restituiscono i risultati per le corrispondenze dalle note dell'elemento"

#: common/setting/user.py:177
msgid "Show Quantity in Forms"
msgstr "Mostra quantità nei moduli"

#: common/setting/user.py:178
msgid "Display available part quantity in some forms"
msgstr "Visualizzare la quantità di pezzi disponibili in alcuni moduli"

#: common/setting/user.py:183
msgid "Escape Key Closes Forms"
msgstr "Il tasto Esc chiude i moduli"

#: common/setting/user.py:184
msgid "Use the escape key to close modal forms"
msgstr "Utilizzare il tasto Esc per chiudere i moduli modali"

#: common/setting/user.py:189
msgid "Fixed Navbar"
msgstr "Barra di navigazione fissa"

#: common/setting/user.py:190
msgid "The navbar position is fixed to the top of the screen"
msgstr "La posizione della barra di navigazione è fissata nella parte superiore dello schermo"

#: common/setting/user.py:195
msgid "Navigation Icons"
msgstr "Icone per la Navigazione"

#: common/setting/user.py:196
msgid "Display icons in the navigation bar"
msgstr "Visualizza le icone nella barra di navigazione"

#: common/setting/user.py:201
msgid "Date Format"
msgstr "Formato Data"

#: common/setting/user.py:202
msgid "Preferred format for displaying dates"
msgstr "Formato predefinito per visualizzare le date"

#: common/setting/user.py:215
msgid "Part Scheduling"
msgstr "Programmazione Prodotto"

#: common/setting/user.py:216
msgid "Display part scheduling information"
msgstr "Mostra informazioni sulla pianificazione del prodotto"

#: common/setting/user.py:221
msgid "Part Stocktake"
msgstr "Inventario Prodotto"

#: common/setting/user.py:223
msgid "Display part stocktake information (if stocktake functionality is enabled)"
msgstr "Visualizza le informazioni d'inventario dell'articolo (se la funzionalità d'inventario è abilitata)"

#: common/setting/user.py:229
msgid "Table String Length"
msgstr "Lunghezza Stringa Tabella"

#: common/setting/user.py:230
msgid "Maximum length limit for strings displayed in table views"
msgstr "Limite massimo di lunghezza per le stringhe visualizzate nelle viste tabella"

#: common/setting/user.py:235
msgid "Show Last Breadcrumb"
msgstr "Mostra L'Ultimo Breadcrumb"

#: common/setting/user.py:236
msgid "Show the current page in breadcrumbs"
msgstr "Mostra la pagina corrente nel Breadcrumb"

#: common/setting/user.py:241
msgid "Receive error reports"
msgstr "Ricevi segnalazioni di errore"

#: common/setting/user.py:242
msgid "Receive notifications for system errors"
msgstr "Ricevi notifiche per errori di sistema"

#: common/setting/user.py:247
msgid "Last used printing machines"
msgstr "Ultime stampanti usate"

#: common/setting/user.py:248
msgid "Save the last used printing machines for a user"
msgstr "Salva le ultime stampanti usate da un'utente"

#: common/validators.py:35
msgid "No attachment model type provided"
msgstr "Nessun tipo di modello allegato fornito"

#: common/validators.py:41
msgid "Invalid attachment model type"
msgstr "Tipo di modello allegato non valido"

#: common/validators.py:82
msgid "Minimum places cannot be greater than maximum places"
msgstr "I posti minimi non possono essere superiori al massimo"

#: common/validators.py:94
msgid "Maximum places cannot be less than minimum places"
msgstr "Il numero massimo di posti non può essere inferiore al minimo"

#: common/validators.py:105
msgid "An empty domain is not allowed."
msgstr "Un dominio vuoto non è consentito."

#: common/validators.py:107
#, python-brace-format
msgid "Invalid domain name: {domain}"
msgstr "Nome dominio non valido: {domain}"

#: common/validators.py:123
msgid "Value must be uppercase"
msgstr "Il valore deve essere maiuscolo"

#: common/validators.py:129
msgid "Value must be a valid variable identifier"
msgstr "Il valore deve essere un identificatore variabile valido"

#: company/api.py:141
msgid "Part is Active"
msgstr "L'articolo è attivo"

#: company/api.py:145
msgid "Manufacturer is Active"
msgstr "Il produttore è attivo"

#: company/api.py:278
msgid "Supplier Part is Active"
msgstr "L'articolo fornitore è attivo"

#: company/api.py:282
msgid "Internal Part is Active"
msgstr "L'articolo interno è attivo"

#: company/api.py:287
msgid "Supplier is Active"
msgstr "Il fornitore è attivo"

#: company/api.py:299 company/models.py:498 company/serializers.py:444
#: part/serializers.py:577
msgid "Manufacturer"
msgstr "Produttore"

#: company/api.py:306 company/models.py:97 company/models.py:370
#: stock/api.py:849
msgid "Company"
msgstr "Azienda"

#: company/api.py:316
msgid "Has Stock"
msgstr "Ha Scorte"

#: company/models.py:98
msgid "Companies"
msgstr "Aziende"

#: company/models.py:114
msgid "Company description"
msgstr "Descrizione azienda"

#: company/models.py:115
msgid "Description of the company"
msgstr "Descrizione dell'azienda"

#: company/models.py:121
msgid "Website"
msgstr "Sito Web"

#: company/models.py:122
msgid "Company website URL"
msgstr "Sito web aziendale"

#: company/models.py:128
msgid "Phone number"
msgstr "Telefono"

#: company/models.py:130
msgid "Contact phone number"
msgstr "Numero di telefono di contatto"

#: company/models.py:137
msgid "Contact email address"
msgstr "Indirizzo email"

#: company/models.py:142 company/models.py:274 order/models.py:500
#: users/models.py:563
msgid "Contact"
msgstr "Contatto"

#: company/models.py:144
msgid "Point of contact"
msgstr "Punto di contatto"

#: company/models.py:150
msgid "Link to external company information"
msgstr "Collegamento alle informazioni aziendali esterne"

#: company/models.py:164
msgid "Is this company active?"
msgstr "Questa azienda è attiva?"

#: company/models.py:169
msgid "Is customer"
msgstr "È un cliente"

#: company/models.py:170
msgid "Do you sell items to this company?"
msgstr "Vendi oggetti a questa azienda?"

#: company/models.py:175
msgid "Is supplier"
msgstr "È un fornitore"

#: company/models.py:176
msgid "Do you purchase items from this company?"
msgstr "Acquistate articoli da questa azienda?"

#: company/models.py:181
msgid "Is manufacturer"
msgstr "È un produttore"

#: company/models.py:182
msgid "Does this company manufacture parts?"
msgstr "Questa azienda produce articoli?"

#: company/models.py:190
msgid "Default currency used for this company"
msgstr "Valuta predefinita utilizzata per questa azienda"

#: company/models.py:313 order/models.py:510
msgid "Address"
msgstr "Indirizzo"

#: company/models.py:314
msgid "Addresses"
msgstr "Indirizzi"

#: company/models.py:371
msgid "Select company"
msgstr "Seleziona azienda"

#: company/models.py:376
msgid "Address title"
msgstr "Titolo indirizzo"

#: company/models.py:377
msgid "Title describing the address entry"
msgstr "Titolo che descrive la voce indirizzo"

#: company/models.py:383
msgid "Primary address"
msgstr "Indirizzo Principale"

#: company/models.py:384
msgid "Set as primary address"
msgstr "Imposta come indirizzo primario"

#: company/models.py:389
msgid "Line 1"
msgstr "Linea 1"

#: company/models.py:390
msgid "Address line 1"
msgstr "Indirizzo (linea 1)"

#: company/models.py:396
msgid "Line 2"
msgstr "Linea 2"

#: company/models.py:397
msgid "Address line 2"
msgstr "Indirizzo (linea 2)"

#: company/models.py:403 company/models.py:404
msgid "Postal code"
msgstr "CAP"

#: company/models.py:410
msgid "City/Region"
msgstr "Città/Regione"

#: company/models.py:411
msgid "Postal code city/region"
msgstr "Codice postale città/regione"

#: company/models.py:417
msgid "State/Province"
msgstr "Stato/Provincia"

#: company/models.py:418
msgid "State or province"
msgstr "Stato o provincia"

#: company/models.py:424
msgid "Country"
msgstr "Nazione"

#: company/models.py:425
msgid "Address country"
msgstr "Indirizzo Paese"

#: company/models.py:431
msgid "Courier shipping notes"
msgstr "Note di spedizione del corriere"

#: company/models.py:432
msgid "Notes for shipping courier"
msgstr "Note per il corriere di spedizione"

#: company/models.py:438
msgid "Internal shipping notes"
msgstr "Note di spedizione interne"

#: company/models.py:439
msgid "Shipping notes for internal use"
msgstr "Note di spedizione per uso interno"

#: company/models.py:446
msgid "Link to address information (external)"
msgstr ""

#: company/models.py:470 company/models.py:587 company/models.py:809
#: company/serializers.py:458
msgid "Manufacturer Part"
msgstr "Codice articolo produttore"

#: company/models.py:487 company/models.py:777 stock/models.py:948
#: stock/serializers.py:480
msgid "Base Part"
msgstr "Articolo di base"

#: company/models.py:489 company/models.py:779
msgid "Select part"
msgstr "Seleziona articolo"

#: company/models.py:499
msgid "Select manufacturer"
msgstr "Seleziona Produttore"

#: company/models.py:505 company/serializers.py:466 order/serializers.py:665
#: part/serializers.py:587
msgid "MPN"
msgstr "Codice articolo produttore (MPN)"

#: company/models.py:506 stock/serializers.py:625
msgid "Manufacturer Part Number"
msgstr "Codice articolo produttore"

#: company/models.py:513
msgid "URL for external manufacturer part link"
msgstr "URL dell'articolo del fornitore"

#: company/models.py:522
msgid "Manufacturer part description"
msgstr "Descrizione articolo costruttore"

#: company/models.py:575
msgid "Manufacturer Part Parameter"
msgstr ""

#: company/models.py:594
msgid "Parameter name"
msgstr "Nome parametro"

#: company/models.py:601
msgid "Parameter value"
msgstr "Valore del parametro"

#: company/models.py:608 part/models.py:1204 part/models.py:3844
msgid "Units"
msgstr "Unità"

#: company/models.py:609
msgid "Parameter units"
msgstr "Unità parametri"

#: company/models.py:717
msgid "Pack units must be compatible with the base part units"
msgstr ""

#: company/models.py:724
msgid "Pack units must be greater than zero"
msgstr ""

#: company/models.py:738
msgid "Linked manufacturer part must reference the same base part"
msgstr "L'articolo del costruttore collegato deve riferirsi alla stesso articolo"

#: company/models.py:787 company/serializers.py:436 company/serializers.py:454
#: order/models.py:626 part/serializers.py:561
#: plugin/builtin/suppliers/digikey.py:25 plugin/builtin/suppliers/lcsc.py:26
#: plugin/builtin/suppliers/mouser.py:24 plugin/builtin/suppliers/tme.py:26
#: stock/api.py:516 templates/email/overdue_purchase_order.html:16
msgid "Supplier"
msgstr "Fornitore"

#: company/models.py:788
msgid "Select supplier"
msgstr "Seleziona fornitore"

#: company/models.py:794 part/serializers.py:572
msgid "Supplier stock keeping unit"
msgstr "Unità di giacenza magazzino fornitore"

#: company/models.py:800
msgid "Is this supplier part active?"
msgstr ""

#: company/models.py:810
msgid "Select manufacturer part"
msgstr "Selezionare un produttore"

#: company/models.py:817
msgid "URL for external supplier part link"
msgstr "URL dell'articolo del fornitore"

#: company/models.py:826
msgid "Supplier part description"
msgstr "Descrizione articolo fornitore"

#: company/models.py:833 order/serializers.py:819 order/serializers.py:2027
#: part/models.py:4356 part/models.py:4697
#: report/templates/report/inventree_bill_of_materials_report.html:140
#: report/templates/report/inventree_purchase_order_report.html:32
#: report/templates/report/inventree_return_order_report.html:27
#: report/templates/report/inventree_sales_order_report.html:32
#: report/templates/report/inventree_stock_location_report.html:105
#: stock/serializers.py:838
msgid "Note"
msgstr "Nota"

#: company/models.py:842 part/models.py:2182
msgid "base cost"
msgstr "costo base"

#: company/models.py:843 part/models.py:2183
msgid "Minimum charge (e.g. stocking fee)"
msgstr "Onere minimo (ad esempio tassa di stoccaggio)"

#: company/models.py:850 order/serializers.py:811 stock/models.py:979
#: stock/serializers.py:1666
msgid "Packaging"
msgstr "Confezionamento"

#: company/models.py:851
msgid "Part packaging"
msgstr "Imballaggio del pezzo"

#: company/models.py:856
msgid "Pack Quantity"
msgstr "Quantità Confezione"

#: company/models.py:858
msgid "Total quantity supplied in a single pack. Leave empty for single items."
msgstr ""

#: company/models.py:877 part/models.py:2189
msgid "multiple"
msgstr "multiplo"

#: company/models.py:878
msgid "Order multiple"
msgstr "Ordine multiplo"

#: company/models.py:890
msgid "Quantity available from supplier"
msgstr "Quantità disponibile dal fornitore"

#: company/models.py:896
msgid "Availability Updated"
msgstr "Disponibilità Aggiornata"

#: company/models.py:897
msgid "Date of last update of availability data"
msgstr "Data dell’ultimo aggiornamento dei dati sulla disponibilità"

#: company/models.py:1025
msgid "Supplier Price Break"
msgstr ""

#: company/serializers.py:166
msgid "Return the string representation for the primary address. This property exists for backwards compatibility."
msgstr ""

#: company/serializers.py:185
msgid "Default currency used for this supplier"
msgstr "Valuta predefinita utilizzata per questo fornitore"

#: company/serializers.py:221
msgid "Company Name"
msgstr ""

#: company/serializers.py:420 part/serializers.py:940 stock/serializers.py:498
msgid "In Stock"
msgstr "In magazzino"

#: data_exporter/mixins.py:324 data_exporter/mixins.py:393
msgid "Error occurred during data export"
msgstr ""

#: data_exporter/mixins.py:375
msgid "Data export plugin returned incorrect data format"
msgstr ""

#: data_exporter/serializers.py:74
msgid "Export Format"
msgstr ""

#: data_exporter/serializers.py:75
msgid "Select export file format"
msgstr ""

#: data_exporter/serializers.py:82
msgid "Export Plugin"
msgstr ""

#: data_exporter/serializers.py:83
msgid "Select export plugin"
msgstr ""

#: generic/states/fields.py:146
msgid "Additional status information for this item"
msgstr ""

#: generic/states/fields.py:160
msgid "Custom status key"
msgstr ""

#: generic/states/serializers.py:16 plugin/models.py:45 users/models.py:119
msgid "Key"
msgstr ""

#: generic/states/serializers.py:26
msgid "Custom"
msgstr ""

#: generic/states/serializers.py:37
msgid "Class"
msgstr ""

#: generic/states/serializers.py:40
msgid "Values"
msgstr ""

#: generic/states/tests.py:22 order/status_codes.py:13
msgid "Placed"
msgstr "Inviato"

#: generic/states/validators.py:21
msgid "Invalid status code"
msgstr ""

#: importer/models.py:70
msgid "Data File"
msgstr "File dati"

#: importer/models.py:71
msgid "Data file to import"
msgstr ""

#: importer/models.py:80
msgid "Columns"
msgstr ""

#: importer/models.py:87
msgid "Target model type for this import session"
msgstr ""

#: importer/models.py:93
msgid "Import status"
msgstr ""

#: importer/models.py:103
msgid "Field Defaults"
msgstr ""

#: importer/models.py:110
msgid "Field Overrides"
msgstr ""

#: importer/models.py:117
msgid "Field Filters"
msgstr ""

#: importer/models.py:250
msgid "Some required fields have not been mapped"
msgstr ""

#: importer/models.py:407
msgid "Column is already mapped to a database field"
msgstr ""

#: importer/models.py:412
msgid "Field is already mapped to a data column"
msgstr ""

#: importer/models.py:421
msgid "Column mapping must be linked to a valid import session"
msgstr ""

#: importer/models.py:426
msgid "Column does not exist in the data file"
msgstr ""

#: importer/models.py:433
msgid "Field does not exist in the target model"
msgstr ""

#: importer/models.py:437
msgid "Selected field is read-only"
msgstr ""

#: importer/models.py:442 importer/models.py:513
msgid "Import Session"
msgstr ""

#: importer/models.py:446
msgid "Field"
msgstr ""

#: importer/models.py:448
msgid "Column"
msgstr ""

#: importer/models.py:517
msgid "Row Index"
msgstr ""

#: importer/models.py:520
msgid "Original row data"
msgstr ""

#: importer/models.py:525 machine/models.py:110
msgid "Errors"
msgstr ""

#: importer/models.py:527 part/api.py:864
msgid "Valid"
msgstr "Valido"

#: importer/operations.py:28 importer/operations.py:49
msgid "Unsupported data file format"
msgstr ""

#: importer/operations.py:40
msgid "Failed to open data file"
msgstr ""

#: importer/operations.py:51
msgid "Invalid data file dimensions"
msgstr ""

#: importer/serializers.py:91
msgid "Invalid field defaults"
msgstr ""

#: importer/serializers.py:104
msgid "Invalid field overrides"
msgstr ""

#: importer/serializers.py:117
msgid "Invalid field filters"
msgstr ""

#: importer/serializers.py:178
msgid "Rows"
msgstr ""

#: importer/serializers.py:179
msgid "List of row IDs to accept"
msgstr ""

#: importer/serializers.py:192
msgid "No rows provided"
msgstr "Nessuna riga fornita"

#: importer/serializers.py:196
msgid "Row does not belong to this session"
msgstr "La riga non appartiene a questa sessione"

#: importer/serializers.py:199
msgid "Row contains invalid data"
msgstr "La riga contiene dati non validi"

#: importer/serializers.py:202
msgid "Row has already been completed"
msgstr "La riga è già stata completata"

#: importer/status_codes.py:13
msgid "Initializing"
msgstr "Inizializzazione..."

#: importer/status_codes.py:18
msgid "Mapping Columns"
msgstr "Mappatura Colonne"

#: importer/status_codes.py:21
msgid "Importing Data"
msgstr "Importazione dei dati"

#: importer/status_codes.py:24
msgid "Processing Data"
msgstr "Elaborazione dei dati"

#: importer/validators.py:21
msgid "Data file exceeds maximum size limit"
msgstr "Il file di dati supera il limite di dimensione massima"

#: importer/validators.py:26
msgid "Data file contains no headers"
msgstr "Il file dati non contiene intestazioni"

#: importer/validators.py:29
msgid "Data file contains too many columns"
msgstr "Il file dati contiene troppe colonne"

#: importer/validators.py:32
msgid "Data file contains too many rows"
msgstr "Il file di dati contiene troppe righe"

#: importer/validators.py:53
msgid "Value must be a valid dictionary object"
msgstr "Il valore deve essere un oggetto dizionario valido"

#: machine/machine_types/label_printer.py:212
msgid "Copies"
msgstr "Copie"

#: machine/machine_types/label_printer.py:213
msgid "Number of copies to print for each label"
msgstr "Numero di copie da stampare per ogni etichetta"

#: machine/machine_types/label_printer.py:228
msgid "Connected"
msgstr "Connesso"

#: machine/machine_types/label_printer.py:229 order/api.py:1679
msgid "Unknown"
msgstr "Sconosciuto"

#: machine/machine_types/label_printer.py:230
msgid "Printing"
msgstr "Stampando"

#: machine/machine_types/label_printer.py:231
msgid "No media"
msgstr "Nessun file multimediale"

#: machine/machine_types/label_printer.py:232
msgid "Paper jam"
msgstr "Inceppamento della carta"

#: machine/machine_types/label_printer.py:233
msgid "Disconnected"
msgstr "Disconnesso"

#: machine/machine_types/label_printer.py:240
msgid "Label Printer"
msgstr "Stampante Etichetta"

#: machine/machine_types/label_printer.py:241
msgid "Directly print labels for various items."
msgstr "Stampa direttamente etichette per vari articoli."

#: machine/machine_types/label_printer.py:247
msgid "Printer Location"
msgstr "Posizione Della Stampante"

#: machine/machine_types/label_printer.py:248
msgid "Scope the printer to a specific location"
msgstr "Ambito della stampante a una posizione specifica"

#: machine/models.py:25
msgid "Name of machine"
msgstr "Nome della macchina"

#: machine/models.py:29
msgid "Machine Type"
msgstr "Tipo di macchina"

#: machine/models.py:29
msgid "Type of machine"
msgstr "Tipo di macchina"

#: machine/models.py:34 machine/models.py:146
msgid "Driver"
msgstr "Driver"

#: machine/models.py:35
msgid "Driver used for the machine"
msgstr "Driver utilizzato per la macchina"

#: machine/models.py:39
msgid "Machines can be disabled"
msgstr "Le macchine possono essere disabilitate"

#: machine/models.py:95
msgid "Driver available"
msgstr "Driver disponibile"

#: machine/models.py:100
msgid "No errors"
msgstr "Nessun errore"

#: machine/models.py:105
msgid "Initialized"
msgstr "Inizializzato"

#: machine/models.py:117
msgid "Machine status"
msgstr "Stato della Macchina"

#: machine/models.py:145
msgid "Machine"
msgstr "Macchina"

#: machine/models.py:151
msgid "Machine Config"
msgstr "Configurazione Macchina"

#: machine/models.py:156
msgid "Config type"
msgstr "Tipo di configurazione"

#: order/api.py:118
msgid "Order Reference"
msgstr "Riferimento ordine"

#: order/api.py:146 order/api.py:1139
msgid "Outstanding"
msgstr "In Sospeso"

#: order/api.py:162
msgid "Has Project Code"
msgstr "Ha il codice del progetto"

#: order/api.py:176 order/models.py:475
msgid "Created By"
msgstr "Creato Da"

#: order/api.py:180
msgid "Created Before"
msgstr "Creato prima"

#: order/api.py:184
msgid "Created After"
msgstr "Creato dopo"

#: order/api.py:188
msgid "Has Start Date"
msgstr "Ha data d'inizio"

#: order/api.py:196
msgid "Start Date Before"
msgstr "Data d'inizio prima"

#: order/api.py:200
msgid "Start Date After"
msgstr "Data d'inizio dopo"

#: order/api.py:204
msgid "Has Target Date"
msgstr "Ha data di fine"

#: order/api.py:212
msgid "Target Date Before"
msgstr "Data obiettivo prima"

#: order/api.py:216
msgid "Target Date After"
msgstr "Data obiettivo dopo"

#: order/api.py:267
msgid "Has Pricing"
msgstr "Prezzo Articolo"

#: order/api.py:320 order/api.py:774 order/api.py:1375
msgid "Completed Before"
msgstr "Completato prima"

#: order/api.py:324 order/api.py:778 order/api.py:1379
msgid "Completed After"
msgstr "Completato dopo"

#: order/api.py:491 order/api.py:876 order/api.py:1102 order/models.py:1685
#: order/models.py:1803 order/models.py:1854 order/models.py:2002
#: order/models.py:2168 order/models.py:2690 order/models.py:2756
msgid "Order"
msgstr "Ordine"

#: order/api.py:495 order/api.py:914
msgid "Order Complete"
msgstr ""

#: order/api.py:527 order/api.py:531 order/serializers.py:676
msgid "Internal Part"
msgstr "Articolo interno"

#: order/api.py:549
msgid "Order Pending"
msgstr ""

#: order/api.py:899
msgid "Completed"
msgstr "Completato"

#: order/api.py:1155
msgid "Has Shipment"
msgstr "Ha Spedizione"

#: order/api.py:1673 order/models.py:539 order/models.py:1686
#: order/models.py:1804
#: report/templates/report/inventree_purchase_order_report.html:14
#: stock/serializers.py:128 templates/email/overdue_purchase_order.html:15
msgid "Purchase Order"
msgstr "Ordine D'Acquisto"

#: order/api.py:1675 order/models.py:1080 order/models.py:1855
#: order/models.py:2003 order/models.py:2169
#: report/templates/report/inventree_build_order_report.html:135
#: report/templates/report/inventree_sales_order_report.html:14
#: report/templates/report/inventree_sales_order_shipment_report.html:15
#: templates/email/overdue_sales_order.html:15
msgid "Sales Order"
msgstr "Ordini di Vendita"

#: order/api.py:1677 order/models.py:2340 order/models.py:2691
#: order/models.py:2757
#: report/templates/report/inventree_return_order_report.html:13
#: templates/email/overdue_return_order.html:15
msgid "Return Order"
msgstr "Restituisci ordine"

#: order/models.py:89
#: report/templates/report/inventree_purchase_order_report.html:31
#: report/templates/report/inventree_sales_order_report.html:31
msgid "Total Price"
msgstr "Prezzo Totale"

#: order/models.py:90
msgid "Total price for this order"
msgstr "Prezzo totale dell'ordine"

#: order/models.py:95 order/serializers.py:75
msgid "Order Currency"
msgstr ""

#: order/models.py:98 order/serializers.py:76
msgid "Currency for this order (leave blank to use company default)"
msgstr ""

#: order/models.py:323
msgid "This order is locked and cannot be modified"
msgstr ""

#: order/models.py:370
msgid "Contact does not match selected company"
msgstr "Il contatto non corrisponde all'azienda selezionata"

#: order/models.py:377
msgid "Start date must be before target date"
msgstr ""

#: order/models.py:430
msgid "Order description (optional)"
msgstr "Descrizione dell'ordine (opzionale)"

#: order/models.py:439
msgid "Select project code for this order"
msgstr "Seleziona il codice del progetto per questo ordine"

#: order/models.py:445 order/models.py:1586 order/models.py:2057
msgid "Link to external page"
msgstr "Collegamento a un sito web esterno"

#: order/models.py:452
msgid "Start date"
msgstr ""

#: order/models.py:453
msgid "Scheduled start date for this order"
msgstr ""

#: order/models.py:459 order/models.py:1593 order/serializers.py:269
#: report/templates/report/inventree_build_order_report.html:125
msgid "Target Date"
msgstr "Data scadenza"

#: order/models.py:461
msgid "Expected date for order delivery. Order will be overdue after this date."
msgstr "Data prevista per la consegna dell'ordine. L'ordine scadrà dopo questa data."

#: order/models.py:481
msgid "Issue Date"
msgstr "Data di emissione"

#: order/models.py:482
msgid "Date order was issued"
msgstr "Data di emissione ordine"

#: order/models.py:490
msgid "User or group responsible for this order"
msgstr "Utente o gruppo responsabile di questo ordine"

#: order/models.py:501
msgid "Point of contact for this order"
msgstr "Punto di contatto per questo ordine"

#: order/models.py:511
msgid "Company address for this order"
msgstr ""

#: order/models.py:602 order/models.py:1141
msgid "Order reference"
msgstr "Riferimento ordine"

#: order/models.py:611 order/models.py:1165 order/models.py:2428
#: stock/serializers.py:612 stock/serializers.py:1010 users/models.py:544
msgid "Status"
msgstr "Stato"

#: order/models.py:612
msgid "Purchase order status"
msgstr "Stato ordine d'acquisto"

#: order/models.py:627
msgid "Company from which the items are being ordered"
msgstr "Azienda da cui sono stati ordinati gli articoli"

#: order/models.py:638
msgid "Supplier Reference"
msgstr "Riferimento fornitore"

#: order/models.py:639
msgid "Supplier order reference code"
msgstr "Codice di riferimento ordine fornitore"

#: order/models.py:648
msgid "received by"
msgstr "ricevuto da"

#: order/models.py:655 order/models.py:2443
msgid "Date order was completed"
msgstr "Data ordine completato"

#: order/models.py:664 order/models.py:1733
msgid "Destination"
msgstr "Destinazione"

#: order/models.py:665 order/models.py:1737
msgid "Destination for received items"
msgstr "Destinazione per gli elementi ricevuti"

#: order/models.py:711
msgid "Part supplier must match PO supplier"
msgstr "Il fornitore dell'articolo deve corrispondere al fornitore dell'ordine di produzione"

#: order/models.py:971
msgid "Quantity must be a positive number"
msgstr "La quantità deve essere un numero positivo"

#: order/models.py:1152 order/models.py:2415 stock/models.py:1001
#: stock/models.py:1002 stock/serializers.py:1405
#: templates/email/overdue_return_order.html:16
#: templates/email/overdue_sales_order.html:16
msgid "Customer"
msgstr "Cliente"

#: order/models.py:1153
msgid "Company to which the items are being sold"
msgstr "Azienda da cui sono stati ordinati gli elementi"

#: order/models.py:1166
msgid "Sales order status"
msgstr ""

#: order/models.py:1177 order/models.py:2435
msgid "Customer Reference "
msgstr "Riferimento Cliente "

#: order/models.py:1178 order/models.py:2436
msgid "Customer order reference code"
msgstr "Codice di riferimento Ordine del Cliente"

#: order/models.py:1182 order/models.py:2009
msgid "Shipment Date"
msgstr "Data di spedizione"

#: order/models.py:1191
msgid "shipped by"
msgstr "spedito da"

#: order/models.py:1230
msgid "Order is already complete"
msgstr ""

#: order/models.py:1233
msgid "Order is already cancelled"
msgstr ""

#: order/models.py:1237
msgid "Only an open order can be marked as complete"
msgstr "Solo un ordine aperto può essere contrassegnato come completo"

#: order/models.py:1241
msgid "Order cannot be completed as there are incomplete shipments"
msgstr "L'ordine non può essere completato in quanto ci sono spedizioni incomplete"

#: order/models.py:1246
msgid "Order cannot be completed as there are incomplete allocations"
msgstr "L'ordine non può essere completato perché ci sono allocazioni incomplete"

#: order/models.py:1251
msgid "Order cannot be completed as there are incomplete line items"
msgstr "L'ordine non può essere completato perché ci sono elementi di riga incompleti"

#: order/models.py:1535 order/models.py:1548
msgid "The order is locked and cannot be modified"
msgstr ""

#: order/models.py:1556
msgid "Item quantity"
msgstr "Quantità Elementi"

#: order/models.py:1573
msgid "Line item reference"
msgstr "Riferimento Linea Elemento"

#: order/models.py:1580
msgid "Line item notes"
msgstr "Note linea elemento"

#: order/models.py:1595
msgid "Target date for this line item (leave blank to use the target date from the order)"
msgstr "Data di destinazione per questa voce di riga (lasciare vuoto per utilizzare la data di destinazione dall'ordine)"

#: order/models.py:1616
msgid "Line item description (optional)"
msgstr ""

#: order/models.py:1623
msgid "Additional context for this line"
msgstr "Contesto aggiuntivo per questa voce"

#: order/models.py:1633
msgid "Unit price"
msgstr "Prezzo unitario"

#: order/models.py:1647
msgid "Purchase Order Line Item"
msgstr ""

#: order/models.py:1671
msgid "Supplier part must match supplier"
msgstr "L'articolo del fornitore deve corrispondere al fornitore"

#: order/models.py:1705
msgid "Supplier part"
msgstr "Articolo Fornitore"

#: order/models.py:1712
msgid "Received"
msgstr "Ricevuto"

#: order/models.py:1713
msgid "Number of items received"
msgstr "Numero di elementi ricevuti"

#: order/models.py:1721 stock/models.py:1124 stock/serializers.py:679
msgid "Purchase Price"
msgstr "Prezzo di Acquisto"

#: order/models.py:1722
msgid "Unit purchase price"
msgstr "Prezzo di acquisto unitario"

#: order/models.py:1792
msgid "Purchase Order Extra Line"
msgstr ""

#: order/models.py:1821
msgid "Sales Order Line Item"
msgstr ""

#: order/models.py:1842
msgid "Virtual part cannot be assigned to a sales order"
msgstr "Un articolo virtuale non può essere assegnato ad un ordine di vendita"

#: order/models.py:1847
msgid "Only salable parts can be assigned to a sales order"
msgstr "Solo gli articoli vendibili possono essere assegnati a un ordine di vendita"

#: order/models.py:1873
msgid "Sale Price"
msgstr "Prezzo di Vendita"

#: order/models.py:1874
msgid "Unit sale price"
msgstr "Prezzo unitario di vendita"

#: order/models.py:1883 order/status_codes.py:50
msgid "Shipped"
msgstr "Spedito"

#: order/models.py:1884
msgid "Shipped quantity"
msgstr "Quantità spedita"

#: order/models.py:1978
msgid "Sales Order Shipment"
msgstr ""

#: order/models.py:2010
msgid "Date of shipment"
msgstr "Data di spedizione"

#: order/models.py:2016
msgid "Delivery Date"
msgstr ""

#: order/models.py:2017
msgid "Date of delivery of shipment"
msgstr ""

#: order/models.py:2025
msgid "Checked By"
msgstr "Verificato Da"

#: order/models.py:2026
msgid "User who checked this shipment"
msgstr "Utente che ha controllato questa spedizione"

#: order/models.py:2033 order/models.py:2265 order/serializers.py:1685
#: order/serializers.py:1809
#: report/templates/report/inventree_sales_order_shipment_report.html:14
msgid "Shipment"
msgstr "Spedizione"

#: order/models.py:2034
msgid "Shipment number"
msgstr "Numero di spedizione"

#: order/models.py:2042
msgid "Tracking Number"
msgstr "Numero di monitoraggio"

#: order/models.py:2043
msgid "Shipment tracking information"
msgstr "Informazioni di monitoraggio della spedizione"

#: order/models.py:2050
msgid "Invoice Number"
msgstr "Numero Fattura"

#: order/models.py:2051
msgid "Reference number for associated invoice"
msgstr "Numero di riferimento per la fattura associata"

#: order/models.py:2074
msgid "Shipment has already been sent"
msgstr "La spedizione è già stata spedita"

#: order/models.py:2077
msgid "Shipment has no allocated stock items"
msgstr "La spedizione non ha articoli di stock assegnati"

#: order/models.py:2157
msgid "Sales Order Extra Line"
msgstr ""

#: order/models.py:2186
msgid "Sales Order Allocation"
msgstr ""

#: order/models.py:2209 order/models.py:2211
msgid "Stock item has not been assigned"
msgstr "L'elemento di magazzino non è stato assegnato"

#: order/models.py:2218
msgid "Cannot allocate stock item to a line with a different part"
msgstr "Impossibile allocare l'elemento stock a una linea con un articolo diverso"

#: order/models.py:2221
msgid "Cannot allocate stock to a line without a part"
msgstr "Impossibile allocare stock a una riga senza un articolo"

#: order/models.py:2224
msgid "Allocation quantity cannot exceed stock quantity"
msgstr "La quantità di ripartizione non puo' superare la disponibilità della giacenza"

#: order/models.py:2243 order/serializers.py:1555
msgid "Quantity must be 1 for serialized stock item"
msgstr "La quantità deve essere 1 per l'elemento serializzato"

#: order/models.py:2246
msgid "Sales order does not match shipment"
msgstr "L'ordine di vendita non corrisponde alla spedizione"

#: order/models.py:2247 plugin/base/barcodes/api.py:642
msgid "Shipment does not match sales order"
msgstr "La spedizione non corrisponde all'ordine di vendita"

#: order/models.py:2255
msgid "Line"
msgstr "Linea"

#: order/models.py:2266
msgid "Sales order shipment reference"
msgstr "Riferimento della spedizione ordine di vendita"

#: order/models.py:2279 order/models.py:2698
msgid "Item"
msgstr "Elemento"

#: order/models.py:2280
msgid "Select stock item to allocate"
msgstr "Seleziona elemento stock da allocare"

#: order/models.py:2289
msgid "Enter stock allocation quantity"
msgstr "Inserisci la quantità assegnata alla giacenza"

#: order/models.py:2404
msgid "Return Order reference"
msgstr ""

#: order/models.py:2416
msgid "Company from which items are being returned"
msgstr ""

#: order/models.py:2429
msgid "Return order status"
msgstr ""

#: order/models.py:2656
msgid "Return Order Line Item"
msgstr ""

#: order/models.py:2669
msgid "Stock item must be specified"
msgstr ""

#: order/models.py:2673
msgid "Return quantity exceeds stock quantity"
msgstr ""

#: order/models.py:2678
msgid "Return quantity must be greater than zero"
msgstr ""

#: order/models.py:2683
msgid "Invalid quantity for serialized stock item"
msgstr ""

#: order/models.py:2699
msgid "Select item to return from customer"
msgstr "Seleziona l'elemento da restituire dal cliente"

#: order/models.py:2714
msgid "Received Date"
msgstr "Data di ricezione"

#: order/models.py:2715
msgid "The date this this return item was received"
msgstr ""

#: order/models.py:2727
msgid "Outcome"
msgstr "Risultati"

#: order/models.py:2728
msgid "Outcome for this line item"
msgstr ""

#: order/models.py:2735
msgid "Cost associated with return or repair for this line item"
msgstr ""

#: order/models.py:2745
msgid "Return Order Extra Line"
msgstr ""

#: order/serializers.py:89
msgid "Order ID"
msgstr "ID Ordine"

#: order/serializers.py:89
msgid "ID of the order to duplicate"
msgstr "ID dell'ordine da duplicare"

#: order/serializers.py:95
msgid "Copy Lines"
msgstr "Copia Linee"

#: order/serializers.py:96
msgid "Copy line items from the original order"
msgstr "Copia gli elementi di riga dall'ordine originale"

#: order/serializers.py:102
msgid "Copy Extra Lines"
msgstr "Copia Linee Extra"

#: order/serializers.py:103
msgid "Copy extra line items from the original order"
msgstr "Copia gli elementi di riga extra dall'ordine originale"

#: order/serializers.py:116
#: report/templates/report/inventree_purchase_order_report.html:22
#: report/templates/report/inventree_return_order_report.html:19
#: report/templates/report/inventree_sales_order_report.html:22
msgid "Line Items"
msgstr "Elementi Riga"

#: order/serializers.py:121
msgid "Completed Lines"
msgstr ""

#: order/serializers.py:172
msgid "Duplicate Order"
msgstr "Duplica Ordine"

#: order/serializers.py:173
msgid "Specify options for duplicating this order"
msgstr "Specifica le opzioni per duplicare questo ordine"

#: order/serializers.py:249
msgid "Invalid order ID"
msgstr "ID dell'ordine non corretto"

#: order/serializers.py:388
msgid "Supplier Name"
msgstr "Nome Fornitore"

#: order/serializers.py:430
msgid "Order cannot be cancelled"
msgstr "L'ordine non può essere cancellato"

#: order/serializers.py:445 order/serializers.py:1576
msgid "Allow order to be closed with incomplete line items"
msgstr "Consenti di chiudere l'ordine con elementi di riga incompleti"

#: order/serializers.py:455 order/serializers.py:1586
msgid "Order has incomplete line items"
msgstr "L'ordine ha elementi di riga incompleti"

#: order/serializers.py:608
msgid "Order is not open"
msgstr "L'ordine non è aperto"

#: order/serializers.py:629
msgid "Auto Pricing"
msgstr ""

#: order/serializers.py:631
msgid "Automatically calculate purchase price based on supplier part data"
msgstr ""

#: order/serializers.py:641
msgid "Purchase price currency"
msgstr "Valuta prezzo d'acquisto"

#: order/serializers.py:649
msgid "Merge Items"
msgstr ""

#: order/serializers.py:651
msgid "Merge items with the same part, destination and target date into one line item"
msgstr ""

#: order/serializers.py:658 part/serializers.py:571
msgid "SKU"
msgstr ""

#: order/serializers.py:672 part/models.py:1089 part/serializers.py:412
msgid "Internal Part Number"
msgstr "Numero Dell'articolo Interno"

#: order/serializers.py:680
msgid "Internal Part Name"
msgstr ""

#: order/serializers.py:696
msgid "Supplier part must be specified"
msgstr "L'articolo del fornitore deve essere specificato"

#: order/serializers.py:699
msgid "Purchase order must be specified"
msgstr "L'ordine di acquisto deve essere specificato"

#: order/serializers.py:707
msgid "Supplier must match purchase order"
msgstr "Il fornitore deve essere abbinato all'ordine d'acquisto"

#: order/serializers.py:708
msgid "Purchase order must match supplier"
msgstr "L'ordine di acquisto deve essere abbinato al fornitore"

#: order/serializers.py:754 order/serializers.py:1656
msgid "Line Item"
msgstr "Elemento Riga"

#: order/serializers.py:760
msgid "Line item does not match purchase order"
msgstr "L'elemento di riga non corrisponde all'ordine di acquisto"

#: order/serializers.py:770 order/serializers.py:917 order/serializers.py:2023
msgid "Select destination location for received items"
msgstr "Seleziona la posizione di destinazione per gli elementi ricevuti"

#: order/serializers.py:786
msgid "Enter batch code for incoming stock items"
msgstr "Inserisci il codice univoco per gli articoli in arrivo"

#: order/serializers.py:793 stock/models.py:1083 users/models.py:143
msgid "Expiry Date"
msgstr "Data di Scadenza"

#: order/serializers.py:794
msgid "Enter expiry date for incoming stock items"
msgstr ""

#: order/serializers.py:802
msgid "Enter serial numbers for incoming stock items"
msgstr "Inserisci i numeri di serie per gli articoli stock in arrivo"

#: order/serializers.py:812
msgid "Override packaging information for incoming stock items"
msgstr ""

#: order/serializers.py:820 order/serializers.py:2028
msgid "Additional note for incoming stock items"
msgstr ""

#: order/serializers.py:827
msgid "Barcode"
msgstr "Codice a Barre"

#: order/serializers.py:828
msgid "Scanned barcode"
msgstr "Codice a barre scansionato"

#: order/serializers.py:844
msgid "Barcode is already in use"
msgstr "Il codice a barre è già in uso"

#: order/serializers.py:867
msgid "An integer quantity must be provided for trackable parts"
msgstr "Deve essere fornita una quantità intera per gli articoli rintracciabili"

#: order/serializers.py:934 order/serializers.py:2047
msgid "Line items must be provided"
msgstr "Gli elementi di linea devono essere forniti"

#: order/serializers.py:950
msgid "Destination location must be specified"
msgstr "La destinazione deve essere specificata"

#: order/serializers.py:961
msgid "Supplied barcode values must be unique"
msgstr "I valori dei codici a barre forniti devono essere univoci"

#: order/serializers.py:1092
msgid "Shipments"
msgstr "Spedizioni"

#: order/serializers.py:1096
msgid "Completed Shipments"
msgstr "Spedizioni Completate"

#: order/serializers.py:1283
msgid "Sale price currency"
msgstr "Valuta prezzo di vendita"

#: order/serializers.py:1331
msgid "Allocated Items"
msgstr "Elementi Assegnati"

#: order/serializers.py:1458
msgid "No shipment details provided"
msgstr "Nessun dettaglio di spedizione fornito"

#: order/serializers.py:1519 order/serializers.py:1665
msgid "Line item is not associated with this order"
msgstr "L'elemento di riga non è associato a questo ordine"

#: order/serializers.py:1538
msgid "Quantity must be positive"
msgstr "La quantità deve essere positiva"

#: order/serializers.py:1675
msgid "Enter serial numbers to allocate"
msgstr "Inserisci i numeri di serie da assegnare"

#: order/serializers.py:1697 order/serializers.py:1817
msgid "Shipment has already been shipped"
msgstr "La spedizione è già stata spedita"

#: order/serializers.py:1700 order/serializers.py:1820
msgid "Shipment is not associated with this order"
msgstr "La spedizione non è associata con questo ordine"

#: order/serializers.py:1755
msgid "No match found for the following serial numbers"
msgstr "Nessuna corrispondenza trovata per i seguenti numeri di serie"

#: order/serializers.py:1762
msgid "The following serial numbers are unavailable"
msgstr "I seguenti numeri di serie non sono disponibili"

#: order/serializers.py:1989
msgid "Return order line item"
msgstr ""

#: order/serializers.py:1999
msgid "Line item does not match return order"
msgstr ""

#: order/serializers.py:2002
msgid "Line item has already been received"
msgstr ""

#: order/serializers.py:2039
msgid "Items can only be received against orders which are in progress"
msgstr ""

#: order/serializers.py:2131
msgid "Quantity to return"
msgstr ""

#: order/serializers.py:2143
msgid "Line price currency"
msgstr ""

#: order/status_codes.py:17 order/status_codes.py:54 stock/status_codes.py:16
msgid "Lost"
msgstr "Perso"

#: order/status_codes.py:18 order/status_codes.py:55 stock/status_codes.py:24
msgid "Returned"
msgstr "Reso"

#: order/status_codes.py:47 order/status_codes.py:79
msgid "In Progress"
msgstr "In corso"

#: order/status_codes.py:105
msgid "Return"
msgstr "Indietro"

#: order/status_codes.py:108
msgid "Repair"
msgstr "Riparare"

#: order/status_codes.py:111
msgid "Replace"
msgstr "Sostituire"

#: order/status_codes.py:114
msgid "Refund"
msgstr "Rimborso"

#: order/status_codes.py:117
msgid "Reject"
msgstr "Rifiuta"

#: order/tasks.py:44
msgid "Overdue Purchase Order"
msgstr "Ordine D'Acquisto in ritardo"

#: order/tasks.py:49
#, python-brace-format
msgid "Purchase order {po} is now overdue"
msgstr "L'ordine d'acquisto {po} è in ritardo"

#: order/tasks.py:112
msgid "Overdue Sales Order"
msgstr "Ordini Di Vendita in ritardo"

#: order/tasks.py:117
#, python-brace-format
msgid "Sales order {so} is now overdue"
msgstr "L'ordine di vendita {so} è ora in ritardo"

#: order/tasks.py:177
msgid "Overdue Return Order"
msgstr "Ordini di Reso in Ritardo"

#: order/tasks.py:182
#, python-brace-format
msgid "Return order {ro} is now overdue"
msgstr "L'ordine di reso {ro} è ora in ritardo"

#: part/api.py:115
msgid "Starred"
msgstr "Preferiti"

#: part/api.py:117
msgid "Filter by starred categories"
msgstr "Filtra per categorie preferite"

#: part/api.py:134 stock/api.py:255
msgid "Depth"
msgstr "Profondità"

#: part/api.py:134
msgid "Filter by category depth"
msgstr "Filtra per profondità categoria"

#: part/api.py:152 stock/api.py:273
msgid "Top Level"
msgstr "Livello principale"

#: part/api.py:154
msgid "Filter by top-level categories"
msgstr "Filtra per categorie di primo livello"

#: part/api.py:167 stock/api.py:288
msgid "Cascade"
msgstr "Cascata"

#: part/api.py:169
msgid "Include sub-categories in filtered results"
msgstr "Includi sottocategorie nei risultati filtrati"

#: part/api.py:189
msgid "Parent"
msgstr "Genitore"

#: part/api.py:191
msgid "Filter by parent category"
msgstr "Filtra per categoria genitore"

#: part/api.py:226
msgid "Exclude sub-categories under the specified category"
msgstr "Escludi sottocategorie sotto la categoria specificata"

#: part/api.py:438
msgid "Has Results"
msgstr "Ha Risultati"

#: part/api.py:611
msgid "Incoming Purchase Order"
msgstr "Ordine D'Acquisto In Arrivo"

#: part/api.py:625
msgid "Outgoing Sales Order"
msgstr "Ordine di Vendita in Uscita"

#: part/api.py:637
msgid "Stock produced by Build Order"
msgstr "Giacenza prodotta dall'Ordine di Costruzione"

#: part/api.py:719
msgid "Stock required for Build Order"
msgstr "Giacenza richiesta per l'Ordine di Produzione"

#: part/api.py:865
msgid "Validate entire Bill of Materials"
msgstr "Convalida l'intera Fattura dei Materiali"

#: part/api.py:871
msgid "This option must be selected"
msgstr "Questa opzione deve essere selezionata"

#: part/api.py:907
msgid "Is Variant"
msgstr "È una Variante"

#: part/api.py:915
msgid "Is Revision"
msgstr "E' una revisione"

#: part/api.py:925
msgid "Has Revisions"
msgstr "Ha revisioni"

#: part/api.py:1116
msgid "BOM Valid"
msgstr "BOM Valido"

#: part/api.py:1775
msgid "Assembly part is testable"
msgstr "L'articolo assemblato è provabile"

#: part/api.py:1784
msgid "Component part is testable"
msgstr "Il componente è provabile"

#: part/api.py:1835
msgid "Uses"
msgstr "Utilizzi"

#: part/models.py:90 part/models.py:4137
#: templates/email/part_event_notification.html:16
msgid "Part Category"
msgstr "Categoria Articoli"

#: part/models.py:91 users/ruleset.py:32
msgid "Part Categories"
msgstr "Categorie Articolo"

#: part/models.py:109 part/models.py:1134
msgid "Default Location"
msgstr "Posizione Predefinita"

#: part/models.py:110
msgid "Default location for parts in this category"
msgstr "Posizione predefinita per gli articoli di questa categoria"

#: part/models.py:115 stock/models.py:212
msgid "Structural"
msgstr "Strutturale"

#: part/models.py:117
msgid "Parts may not be directly assigned to a structural category, but may be assigned to child categories."
msgstr "Le parti non possono essere assegnate direttamente a una categoria strutturale, ma possono essere assegnate a categorie subordinate."

#: part/models.py:126
msgid "Default keywords"
msgstr "Keywords predefinite"

#: part/models.py:127
msgid "Default keywords for parts in this category"
msgstr "Parole chiave predefinite per gli articoli in questa categoria"

#: part/models.py:134 stock/models.py:96 stock/models.py:194
msgid "Icon"
msgstr "Icona"

#: part/models.py:135 part/serializers.py:152 part/serializers.py:171
#: stock/models.py:195
msgid "Icon (optional)"
msgstr "Icona (facoltativa)"

#: part/models.py:179
msgid "You cannot make this part category structural because some parts are already assigned to it!"
msgstr "Non puoi rendere principale questa categoria di articoli perché alcuni articoli sono già assegnati!"

#: part/models.py:458 part/serializers.py:130 part/serializers.py:343
#: users/ruleset.py:33
msgid "Parts"
msgstr "Articoli"

#: part/models.py:510
msgid "Cannot delete this part as it is locked"
msgstr "Impossibile eliminare questo articolo perché è bloccato"

#: part/models.py:513
msgid "Cannot delete this part as it is still active"
msgstr "Impossibile eliminare questo articolo perché è ancora attivo"

#: part/models.py:518
msgid "Cannot delete this part as it is used in an assembly"
msgstr "Non è possibile eliminare questo articolo in quanto è utilizzato in una costruzione"

#: part/models.py:556
msgid "Invalid choice for parent part"
msgstr "Scelta non valida per l'articolo principale"

#: part/models.py:604 part/models.py:611
#, python-brace-format
msgid "Part '{self}' cannot be used in BOM for '{parent}' (recursive)"
msgstr "L'articolo '{self}' non può essere usata nel BOM per '{parent}' (ricorsivo)"

#: part/models.py:623
#, python-brace-format
msgid "Part '{parent}' is  used in BOM for '{self}' (recursive)"
msgstr "L'articolo '{parent}' è usato nel BOM per '{self}' (ricorsivo)"

#: part/models.py:690
#, python-brace-format
msgid "IPN must match regex pattern {pattern}"
msgstr "IPN deve corrispondere al modello regex {pattern}"

#: part/models.py:698
msgid "Part cannot be a revision of itself"
msgstr "L'articolo non può essere una revisione di se stesso"

#: part/models.py:705
msgid "Cannot make a revision of a part which is already a revision"
msgstr "Non puoi fare la revisione di un articolo che è già una revisione"

#: part/models.py:712
msgid "Revision code must be specified"
msgstr "Il codice di revisione deve essere specificato"

#: part/models.py:719
msgid "Revisions are only allowed for assembly parts"
msgstr "Le revisioni sono consentite solo per le parti di assemblaggio"

#: part/models.py:726
msgid "Cannot make a revision of a template part"
msgstr "Non è possibile effettuare la revisione di un articolo modello"

#: part/models.py:732
msgid "Parent part must point to the same template"
msgstr "L'articolo genitore deve puntare allo stesso modello"

#: part/models.py:829
msgid "Stock item with this serial number already exists"
msgstr "Esiste già un elemento stock con questo numero seriale"

#: part/models.py:971
msgid "Duplicate IPN not allowed in part settings"
msgstr "Non è consentito duplicare IPN nelle impostazioni dell'articolo"

#: part/models.py:983
msgid "Duplicate part revision already exists."
msgstr "La revisione dell'articolo duplicata esiste già."

#: part/models.py:992
msgid "Part with this Name, IPN and Revision already exists."
msgstr "Un articolo con questo Nome, IPN e Revisione esiste già."

#: part/models.py:1007
msgid "Parts cannot be assigned to structural part categories!"
msgstr "Gli articoli non possono essere assegnati a categorie articolo principali!"

#: part/models.py:1039
msgid "Part name"
msgstr "Nome articolo"

#: part/models.py:1044
msgid "Is Template"
msgstr "È Template"

#: part/models.py:1045
msgid "Is this part a template part?"
msgstr "Quest'articolo è un articolo di template?"

#: part/models.py:1055
msgid "Is this part a variant of another part?"
msgstr "Questa parte è una variante di un altro articolo?"

#: part/models.py:1056
msgid "Variant Of"
msgstr "Variante Di"

#: part/models.py:1063
msgid "Part description (optional)"
msgstr "Descrizione della parte (opzionale)"

#: part/models.py:1070
msgid "Keywords"
msgstr "Parole Chiave"

#: part/models.py:1071
msgid "Part keywords to improve visibility in search results"
msgstr "Parole chiave per migliorare la visibilità nei risultati di ricerca"

#: part/models.py:1081
msgid "Part category"
msgstr "Categoria articolo"

#: part/models.py:1088 part/serializers.py:926
#: report/templates/report/inventree_stock_location_report.html:103
msgid "IPN"
msgstr "IPN - Numero di riferimento interno"

#: part/models.py:1096
msgid "Part revision or version number"
msgstr "Numero di revisione o di versione"

#: part/models.py:1097 report/models.py:215
msgid "Revision"
msgstr "Revisione"

#: part/models.py:1106
msgid "Is this part a revision of another part?"
msgstr "Questo articolo è una revisione di un altro articolo?"

#: part/models.py:1107
msgid "Revision Of"
msgstr "Revisione di"

#: part/models.py:1132
msgid "Where is this item normally stored?"
msgstr "Dove viene normalmente immagazzinato questo articolo?"

#: part/models.py:1178
msgid "Default Supplier"
msgstr "Fornitore predefinito"

#: part/models.py:1179
msgid "Default supplier part"
msgstr "Articolo fornitore predefinito"

#: part/models.py:1186
msgid "Default Expiry"
msgstr "Scadenza Predefinita"

#: part/models.py:1187
msgid "Expiry time (in days) for stock items of this part"
msgstr "Scadenza (in giorni) per gli articoli in giacenza di questo pezzo"

#: part/models.py:1195 part/serializers.py:977
msgid "Minimum Stock"
msgstr "Scorta Minima"

#: part/models.py:1196
msgid "Minimum allowed stock level"
msgstr "Livello minimo di giacenza consentito"

#: part/models.py:1205
msgid "Units of measure for this part"
msgstr "Unita di misura per questo articolo"

#: part/models.py:1212
msgid "Can this part be built from other parts?"
msgstr "Questo articolo può essere costruito da altri articoli?"

#: part/models.py:1218
msgid "Can this part be used to build other parts?"
msgstr "Questo articolo può essere utilizzato per costruire altri articoli?"

#: part/models.py:1224
msgid "Does this part have tracking for unique items?"
msgstr "Questo articolo ha il tracciamento per gli elementi unici?"

#: part/models.py:1230
msgid "Can this part have test results recorded against it?"
msgstr "Questo articolo può avere delle prove registrate?"

#: part/models.py:1236
msgid "Can this part be purchased from external suppliers?"
msgstr "Quest'articolo può essere acquistato da fornitori esterni?"

#: part/models.py:1242
msgid "Can this part be sold to customers?"
msgstr "Questo pezzo può essere venduto ai clienti?"

#: part/models.py:1246
msgid "Is this part active?"
msgstr "Quest'articolo è attivo?"

#: part/models.py:1252
msgid "Locked parts cannot be edited"
msgstr "Gli articoli bloccati non possono essere modificati"

#: part/models.py:1258
msgid "Is this a virtual part, such as a software product or license?"
msgstr "È una parte virtuale, come un prodotto software o una licenza?"

#: part/models.py:1264
msgid "BOM checksum"
msgstr "Somma di controllo Distinta Base"

#: part/models.py:1265
msgid "Stored BOM checksum"
msgstr "Somma di controllo immagazzinata Distinta Base"

#: part/models.py:1273
msgid "BOM checked by"
msgstr "Distinta Base controllata da"

#: part/models.py:1278
msgid "BOM checked date"
msgstr "Data di verifica Distinta Base"

#: part/models.py:1294
msgid "Creation User"
msgstr "Creazione Utente"

#: part/models.py:1304
msgid "Owner responsible for this part"
msgstr "Utente responsabile di questo articolo"

#: part/models.py:1309
msgid "Last Stocktake"
msgstr "Ultimo Inventario"

#: part/models.py:2190
msgid "Sell multiple"
msgstr "Vendita multipla"

#: part/models.py:3185
msgid "Currency used to cache pricing calculations"
msgstr "Valuta utilizzata per calcolare i prezzi"

#: part/models.py:3201
msgid "Minimum BOM Cost"
msgstr "Costo Minimo Distinta Base"

#: part/models.py:3202
msgid "Minimum cost of component parts"
msgstr "Costo minimo dei componenti dell'articolo"

#: part/models.py:3208
msgid "Maximum BOM Cost"
msgstr "Costo Massimo Distinta Base"

#: part/models.py:3209
msgid "Maximum cost of component parts"
msgstr "Costo massimo dei componenti dell'articolo"

#: part/models.py:3215
msgid "Minimum Purchase Cost"
msgstr "Importo Acquisto Minimo"

#: part/models.py:3216
msgid "Minimum historical purchase cost"
msgstr "Costo minimo di acquisto storico"

#: part/models.py:3222
msgid "Maximum Purchase Cost"
msgstr "Importo massimo acquisto"

#: part/models.py:3223
msgid "Maximum historical purchase cost"
msgstr "Costo massimo di acquisto storico"

#: part/models.py:3229
msgid "Minimum Internal Price"
msgstr "Prezzo Interno Minimo"

#: part/models.py:3230
msgid "Minimum cost based on internal price breaks"
msgstr "Costo minimo basato su interruzioni di prezzo interne"

#: part/models.py:3236
msgid "Maximum Internal Price"
msgstr "Prezzo Interno Massimo"

#: part/models.py:3237
msgid "Maximum cost based on internal price breaks"
msgstr "Costo massimo basato su interruzioni di prezzo interne"

#: part/models.py:3243
msgid "Minimum Supplier Price"
msgstr "Prezzo Minimo Fornitore"

#: part/models.py:3244
msgid "Minimum price of part from external suppliers"
msgstr "Prezzo minimo articolo da fornitori esterni"

#: part/models.py:3250
msgid "Maximum Supplier Price"
msgstr "Prezzo Massimo Fornitore"

#: part/models.py:3251
msgid "Maximum price of part from external suppliers"
msgstr "Prezzo massimo dell'articolo proveniente da fornitori esterni"

#: part/models.py:3257
msgid "Minimum Variant Cost"
msgstr "Variazione di costo minimo"

#: part/models.py:3258
msgid "Calculated minimum cost of variant parts"
msgstr "Costo minimo calcolato di variazione dell'articolo"

#: part/models.py:3264
msgid "Maximum Variant Cost"
msgstr "Massima variazione di costo"

#: part/models.py:3265
msgid "Calculated maximum cost of variant parts"
msgstr "Costo massimo calcolato di variazione dell'articolo"

#: part/models.py:3271 part/models.py:3285
msgid "Minimum Cost"
msgstr "Costo Minimo"

#: part/models.py:3272
msgid "Override minimum cost"
msgstr "Sovrascrivi il costo minimo"

#: part/models.py:3278 part/models.py:3292
msgid "Maximum Cost"
msgstr "Costo Massimo"

#: part/models.py:3279
msgid "Override maximum cost"
msgstr "Sovrascrivi il costo massimo"

#: part/models.py:3286
msgid "Calculated overall minimum cost"
msgstr "Costo minimo totale calcolato"

#: part/models.py:3293
msgid "Calculated overall maximum cost"
msgstr "Costo massimo totale calcolato"

#: part/models.py:3299
msgid "Minimum Sale Price"
msgstr "Prezzo Di Vendita Minimo"

#: part/models.py:3300
msgid "Minimum sale price based on price breaks"
msgstr "Prezzo minimo di vendita basato sulle interruzioni di prezzo"

#: part/models.py:3306
msgid "Maximum Sale Price"
msgstr "Prezzo Di Vendita Massimo"

#: part/models.py:3307
msgid "Maximum sale price based on price breaks"
msgstr "Prezzo massimo di vendita basato sulle interruzioni di prezzo"

#: part/models.py:3313
msgid "Minimum Sale Cost"
msgstr "Costo Di Vendita Minimo"

#: part/models.py:3314
msgid "Minimum historical sale price"
msgstr "Prezzo storico minimo di vendita"

#: part/models.py:3320
msgid "Maximum Sale Cost"
msgstr "Costo Di Vendita Minimo"

#: part/models.py:3321
msgid "Maximum historical sale price"
msgstr "Prezzo storico massimo di vendita"

#: part/models.py:3340
msgid "Part for stocktake"
msgstr "Articolo per l'inventario"

#: part/models.py:3345
msgid "Item Count"
msgstr "Contatore Elemento"

#: part/models.py:3346
msgid "Number of individual stock entries at time of stocktake"
msgstr "Numero di scorte individuali al momento dell'inventario"

#: part/models.py:3354
msgid "Total available stock at time of stocktake"
msgstr "Totale delle scorte disponibili al momento dell'inventario"

#: part/models.py:3358 part/models.py:3441 part/serializers.py:273
#: report/templates/report/inventree_test_report.html:106
msgid "Date"
msgstr "Data"

#: part/models.py:3359
msgid "Date stocktake was performed"
msgstr "Data in cui è stato effettuato l'inventario"

#: part/models.py:3367
msgid "Additional notes"
msgstr "Note aggiuntive"

#: part/models.py:3377
msgid "User who performed this stocktake"
msgstr "Utente che ha eseguito questo inventario"

#: part/models.py:3383
msgid "Minimum Stock Cost"
msgstr "Costo Minimo Scorta"

#: part/models.py:3384
msgid "Estimated minimum cost of stock on hand"
msgstr "Costo minimo stimato di magazzino a disposizione"

#: part/models.py:3390
msgid "Maximum Stock Cost"
msgstr "Costo Massimo Scorte"

#: part/models.py:3391
msgid "Estimated maximum cost of stock on hand"
msgstr "Costo massimo stimato di magazzino a disposizione"

#: part/models.py:3447
msgid "Report"
msgstr "Rapporto"

#: part/models.py:3448
msgid "Stocktake report file (generated internally)"
msgstr "File Report Inventario (generato internamente)"

#: part/models.py:3453
msgid "Part Count"
msgstr "Conteggio Articolo"

#: part/models.py:3454
msgid "Number of parts covered by stocktake"
msgstr "Numero di articoli oggetto d'inventario"

#: part/models.py:3464
msgid "User who requested this stocktake report"
msgstr "Utente che ha richiesto questo report inventario"

#: part/models.py:3474
msgid "Part Sale Price Break"
msgstr "Aggiungi Prezzo Ribassato di Vendita dell'Articolo"

#: part/models.py:3586
msgid "Part Test Template"
msgstr "Modello Prove Articolo"

#: part/models.py:3612
msgid "Invalid template name - must include at least one alphanumeric character"
msgstr "Nome modello non valido - deve includere almeno un carattere alfanumerico"

#: part/models.py:3633 part/models.py:3803
msgid "Choices must be unique"
msgstr "Le scelte devono essere uniche"

#: part/models.py:3644
msgid "Test templates can only be created for testable parts"
msgstr "Il modello di prova può essere creato solo per gli articoli testabili"

#: part/models.py:3655
msgid "Test template with the same key already exists for part"
msgstr "Il modello di test con la stessa chiave esiste già per l'articolo"

#: part/models.py:3672
msgid "Test Name"
msgstr "Nome Test"

#: part/models.py:3673
msgid "Enter a name for the test"
msgstr "Inserisci un nome per la prova"

#: part/models.py:3679
msgid "Test Key"
msgstr "Chiave Di Prova"

#: part/models.py:3680
msgid "Simplified key for the test"
msgstr "Chiave semplificata per la prova"

#: part/models.py:3687
msgid "Test Description"
msgstr "Descrizione Di Prova"

#: part/models.py:3688
msgid "Enter description for this test"
msgstr "Inserisci descrizione per questa prova"

#: part/models.py:3692 report/models.py:271
msgid "Enabled"
msgstr "Abilitato"

#: part/models.py:3692
msgid "Is this test enabled?"
msgstr ""

#: part/models.py:3697
msgid "Required"
msgstr "Richiesto"

#: part/models.py:3698
msgid "Is this test required to pass?"
msgstr "Questa prova è necessaria per passare?"

#: part/models.py:3703
msgid "Requires Value"
msgstr "Valore richiesto"

#: part/models.py:3704
msgid "Does this test require a value when adding a test result?"
msgstr "Questa prova richiede un valore quando si aggiunge un risultato di prova?"

#: part/models.py:3709
msgid "Requires Attachment"
msgstr "Allegato Richiesto"

#: part/models.py:3711
msgid "Does this test require a file attachment when adding a test result?"
msgstr "Questa prova richiede un file allegato quando si aggiunge un risultato di prova?"

#: part/models.py:3717 part/models.py:3865
msgid "Choices"
msgstr ""

#: part/models.py:3718
msgid "Valid choices for this test (comma-separated)"
msgstr ""

#: part/models.py:3751
msgid "Part Parameter Template"
msgstr ""

#: part/models.py:3778
msgid "Checkbox parameters cannot have units"
msgstr ""

#: part/models.py:3783
msgid "Checkbox parameters cannot have choices"
msgstr ""

#: part/models.py:3820
msgid "Parameter template name must be unique"
msgstr "Il nome del modello del parametro deve essere univoco"

#: part/models.py:3838
msgid "Parameter Name"
msgstr "Nome Parametro"

#: part/models.py:3845
msgid "Physical units for this parameter"
msgstr ""

#: part/models.py:3853
msgid "Parameter description"
msgstr "Descrizione del parametro"

#: part/models.py:3859
msgid "Checkbox"
msgstr ""

#: part/models.py:3860
msgid "Is this parameter a checkbox?"
msgstr ""

#: part/models.py:3866
msgid "Valid choices for this parameter (comma-separated)"
msgstr ""

#: part/models.py:3877
msgid "Selection list for this parameter"
msgstr ""

#: part/models.py:3913
msgid "Part Parameter"
msgstr ""

#: part/models.py:3939
msgid "Parameter cannot be modified - part is locked"
msgstr ""

#: part/models.py:3977
msgid "Invalid choice for parameter value"
msgstr ""

#: part/models.py:4028
msgid "Parent Part"
msgstr "Articolo principale"

#: part/models.py:4036 part/models.py:4144 part/models.py:4145
msgid "Parameter Template"
msgstr "Modello Parametro"

#: part/models.py:4042
msgid "Parameter Value"
msgstr "Valore del Parametro"

#: part/models.py:4092
msgid "Part Category Parameter Template"
msgstr ""

#: part/models.py:4151
msgid "Default Value"
msgstr "Valore Predefinito"

#: part/models.py:4152
msgid "Default Parameter Value"
msgstr "Valore Parametro Predefinito"

#: part/models.py:4283
msgid "BOM item cannot be modified - assembly is locked"
msgstr ""

#: part/models.py:4290
msgid "BOM item cannot be modified - variant assembly is locked"
msgstr ""

#: part/models.py:4300
msgid "Select parent part"
msgstr "Seleziona articolo principale"

#: part/models.py:4310
msgid "Sub part"
msgstr "Articolo subordinato"

#: part/models.py:4311
msgid "Select part to be used in BOM"
msgstr "Seleziona l'articolo da utilizzare nella Distinta Base"

#: part/models.py:4322
msgid "BOM quantity for this BOM item"
msgstr "Quantità Distinta Base per questo elemento Distinta Base"

#: part/models.py:4328
msgid "This BOM item is optional"
msgstr "Questo elemento della Distinta Base è opzionale"

#: part/models.py:4334
msgid "This BOM item is consumable (it is not tracked in build orders)"
msgstr "Questo elemento della Distinta Base è consumabile (non è tracciato negli ordini di produzione)"

#: part/models.py:4341
msgid "Overage"
msgstr "Eccedenza"

#: part/models.py:4342
msgid "Estimated build wastage quantity (absolute or percentage)"
msgstr "Quantità stimata scarti di produzione (assoluta o percentuale)"

#: part/models.py:4349
msgid "BOM item reference"
msgstr "Riferimento Elemento Distinta Base"

#: part/models.py:4357
msgid "BOM item notes"
msgstr "Note Elemento Distinta Base"

#: part/models.py:4363
msgid "Checksum"
msgstr "Codice di controllo"

#: part/models.py:4364
msgid "BOM line checksum"
msgstr "Codice di controllo Distinta Base"

#: part/models.py:4369
msgid "Validated"
msgstr "Convalidato"

#: part/models.py:4370
msgid "This BOM item has been validated"
msgstr ""

#: part/models.py:4375
msgid "Gets inherited"
msgstr ""

#: part/models.py:4376
msgid "This BOM item is inherited by BOMs for variant parts"
msgstr "Questo elemento della Distinta Base viene ereditato dalle Distinte Base per gli articoli varianti"

#: part/models.py:4382
msgid "Stock items for variant parts can be used for this BOM item"
msgstr "Gli elementi in giacenza per gli articoli varianti possono essere utilizzati per questo elemento Distinta Base"

#: part/models.py:4467 stock/models.py:848
msgid "Quantity must be integer value for trackable parts"
msgstr "La quantità deve essere un valore intero per gli articoli rintracciabili"

#: part/models.py:4477 part/models.py:4479
msgid "Sub part must be specified"
msgstr "L'articolo subordinato deve essere specificato"

#: part/models.py:4624
msgid "BOM Item Substitute"
msgstr "Elemento Distinta Base Sostituito"

#: part/models.py:4645
msgid "Substitute part cannot be the same as the master part"
msgstr "La parte sostituita non può essere la stessa dell'articolo principale"

#: part/models.py:4658
msgid "Parent BOM item"
msgstr "Elemento principale Distinta Base"

#: part/models.py:4666
msgid "Substitute part"
msgstr "Sostituisci l'Articolo"

#: part/models.py:4682
msgid "Part 1"
msgstr "Articolo 1"

#: part/models.py:4690
msgid "Part 2"
msgstr "Articolo 2"

#: part/models.py:4691
msgid "Select Related Part"
msgstr "Seleziona Prodotto Relativo"

#: part/models.py:4698
msgid "Note for this relationship"
msgstr ""

#: part/models.py:4717
msgid "Part relationship cannot be created between a part and itself"
msgstr "Non si può creare una relazione tra l'articolo e sé stesso"

#: part/models.py:4722
msgid "Duplicate relationship already exists"
msgstr "La relazione duplicata esiste già"

#: part/serializers.py:125
msgid "Parent Category"
msgstr ""

#: part/serializers.py:126
msgid "Parent part category"
msgstr ""

#: part/serializers.py:134 part/serializers.py:168
msgid "Subcategories"
msgstr "Sottocategorie"

#: part/serializers.py:207
msgid "Results"
msgstr ""

#: part/serializers.py:208
msgid "Number of results recorded against this template"
msgstr ""

#: part/serializers.py:235 part/serializers.py:253 stock/serializers.py:685
msgid "Purchase currency of this stock item"
msgstr "Valuta di acquisto di questo articolo in stock"

#: part/serializers.py:278
msgid "Speculative Quantity"
msgstr "Quantità Speculativa"

#: part/serializers.py:287
msgid "Model ID"
msgstr "ID Modello"

#: part/serializers.py:313
msgid "File is not an image"
msgstr ""

#: part/serializers.py:344
msgid "Number of parts using this template"
msgstr ""

#: part/serializers.py:489
msgid "Original Part"
msgstr "Articolo Originale"

#: part/serializers.py:490
msgid "Select original part to duplicate"
msgstr "Seleziona l'articolo originale da duplicare"

#: part/serializers.py:495
msgid "Copy Image"
msgstr "Copia immagine"

#: part/serializers.py:496
msgid "Copy image from original part"
msgstr "Copia immagine dall'articolo originale"

#: part/serializers.py:502
msgid "Copy BOM"
msgstr "Copia Distinta Base"

#: part/serializers.py:503
msgid "Copy bill of materials from original part"
msgstr "Copia fattura dei materiali dall'articolo originale"

#: part/serializers.py:509
msgid "Copy Parameters"
msgstr "Copia parametri"

#: part/serializers.py:510
msgid "Copy parameter data from original part"
msgstr "Copia i dati dei parametri dall'articolo originale"

#: part/serializers.py:516
msgid "Copy Notes"
msgstr ""

#: part/serializers.py:517
msgid "Copy notes from original part"
msgstr ""

#: part/serializers.py:535
msgid "Initial Stock Quantity"
msgstr "Quantità iniziale"

#: part/serializers.py:537
msgid "Specify initial stock quantity for this Part. If quantity is zero, no stock is added."
msgstr "Specificare la quantità iniziale disponibile per questo Articolo. Se la quantità è zero, non viene aggiunta alcuna quantità."

#: part/serializers.py:544
msgid "Initial Stock Location"
msgstr "Ubicazione Iniziale Magazzino"

#: part/serializers.py:545
msgid "Specify initial stock location for this Part"
msgstr "Specificare l'ubicazione iniziale del magazzino per questo Articolo"

#: part/serializers.py:562
msgid "Select supplier (or leave blank to skip)"
msgstr "Seleziona il fornitore (o lascia vuoto per saltare)"

#: part/serializers.py:578
msgid "Select manufacturer (or leave blank to skip)"
msgstr "Seleziona il produttore (o lascia vuoto per saltare)"

#: part/serializers.py:588
msgid "Manufacturer part number"
msgstr "Codice articolo Produttore"

#: part/serializers.py:595
msgid "Selected company is not a valid supplier"
msgstr "L'azienda selezionata non è un fornitore valido"

#: part/serializers.py:604
msgid "Selected company is not a valid manufacturer"
msgstr "L'azienda selezionata non è un produttore valido"

#: part/serializers.py:615
msgid "Manufacturer part matching this MPN already exists"
msgstr "L'articolo del produttore che corrisponde a questo MPN esiste già"

#: part/serializers.py:622
msgid "Supplier part matching this SKU already exists"
msgstr "L'articolo del fornitore che corrisponde a questo SKU esiste già"

#: part/serializers.py:911 part/stocktake.py:222
msgid "Category Name"
msgstr "Nome Categoria"

#: part/serializers.py:937
msgid "Building"
msgstr "In Costruzione"

#: part/serializers.py:952 part/stocktake.py:223 stock/serializers.py:1084
#: stock/serializers.py:1260 users/ruleset.py:36
msgid "Stock Items"
msgstr "Articoli in magazzino"

#: part/serializers.py:955
msgid "Revisions"
msgstr ""

#: part/serializers.py:958
msgid "Suppliers"
msgstr "Fornitori"

#: part/serializers.py:961 templates/email/low_stock_notification.html:16
#: templates/email/part_event_notification.html:17
msgid "Total Stock"
msgstr "Giacenze Totali"

#: part/serializers.py:967
msgid "Unallocated Stock"
msgstr ""

#: part/serializers.py:973
msgid "Variant Stock"
msgstr ""

#: part/serializers.py:1006
msgid "Duplicate Part"
msgstr "Duplica articolo"

#: part/serializers.py:1007
msgid "Copy initial data from another Part"
msgstr "Copia i dati iniziali da un altro Articolo"

#: part/serializers.py:1013
msgid "Initial Stock"
msgstr "Stock iniziale"

#: part/serializers.py:1014
msgid "Create Part with initial stock quantity"
msgstr "Crea Articolo con quantità di scorta iniziale"

#: part/serializers.py:1020
msgid "Supplier Information"
msgstr "Informazioni Fornitore"

#: part/serializers.py:1021
msgid "Add initial supplier information for this part"
msgstr "Aggiungi le informazioni iniziali del fornitore per questo articolo"

#: part/serializers.py:1029
msgid "Copy Category Parameters"
msgstr "Copia Parametri Categoria"

#: part/serializers.py:1030
msgid "Copy parameter templates from selected part category"
msgstr "Copia i parametri dai modelli della categoria articolo selezionata"

#: part/serializers.py:1035
msgid "Existing Image"
msgstr ""

#: part/serializers.py:1036
msgid "Filename of an existing part image"
msgstr ""

#: part/serializers.py:1053
msgid "Image file does not exist"
msgstr ""

#: part/serializers.py:1257
msgid "Limit stocktake report to a particular part, and any variant parts"
msgstr "Limitare il report d'inventario ad un articolo particolare e a eventuali articoli varianti"

#: part/serializers.py:1267
msgid "Limit stocktake report to a particular part category, and any child categories"
msgstr "Limita il report d'inventario ad una particolare categoria articolo, e a eventuali categorie secondarie"

#: part/serializers.py:1277
msgid "Limit stocktake report to a particular stock location, and any child locations"
msgstr "Limita il report d'inventario ad una particolare ubicazione di magazzino, e a eventuali ubicazioni secondarie"

#: part/serializers.py:1283
msgid "Exclude External Stock"
msgstr ""

#: part/serializers.py:1284
msgid "Exclude stock items in external locations"
msgstr ""

#: part/serializers.py:1289
msgid "Generate Report"
msgstr "Genera Report"

#: part/serializers.py:1290
msgid "Generate report file containing calculated stocktake data"
msgstr "Genera file di report contenente dati di inventario calcolati"

#: part/serializers.py:1295
msgid "Update Parts"
msgstr "Aggiorna Articoli"

#: part/serializers.py:1296
msgid "Update specified parts with calculated stocktake data"
msgstr "Aggiorna gli articoli specificati con i dati calcolati di inventario"

#: part/serializers.py:1304
msgid "Stocktake functionality is not enabled"
msgstr "La funzione Inventario non è abilitata"

#: part/serializers.py:1309
msgid "Background worker check failed"
msgstr "Controllo in background non riuscito"

#: part/serializers.py:1425
msgid "Minimum Price"
msgstr "Prezzo Minimo"

#: part/serializers.py:1426
msgid "Override calculated value for minimum price"
msgstr ""

#: part/serializers.py:1433
msgid "Minimum price currency"
msgstr ""

#: part/serializers.py:1440
msgid "Maximum Price"
msgstr "Prezzo Massimo"

#: part/serializers.py:1441
msgid "Override calculated value for maximum price"
msgstr ""

#: part/serializers.py:1448
msgid "Maximum price currency"
msgstr ""

#: part/serializers.py:1477
msgid "Update"
msgstr "Aggiorna"

#: part/serializers.py:1478
msgid "Update pricing for this part"
msgstr "Aggiorna i prezzi per questo articolo"

#: part/serializers.py:1501
#, python-brace-format
msgid "Could not convert from provided currencies to {default_currency}"
msgstr ""

#: part/serializers.py:1508
msgid "Minimum price must not be greater than maximum price"
msgstr ""

#: part/serializers.py:1511
msgid "Maximum price must not be less than minimum price"
msgstr ""

#: part/serializers.py:1664
msgid "Select the parent assembly"
msgstr ""

#: part/serializers.py:1678
msgid "Select the component part"
msgstr ""

#: part/serializers.py:1698
msgid "Can Build"
msgstr "Puoi produrre"

#: part/serializers.py:1925
msgid "Select part to copy BOM from"
msgstr "Seleziona l'articolo da cui copiare la distinta base"

#: part/serializers.py:1933
msgid "Remove Existing Data"
msgstr "Rimuovi Dati Esistenti"

#: part/serializers.py:1934
msgid "Remove existing BOM items before copying"
msgstr "Rimuovi elementi distinta base esistenti prima di copiare"

#: part/serializers.py:1939
msgid "Include Inherited"
msgstr "Includi Ereditato"

#: part/serializers.py:1940
msgid "Include BOM items which are inherited from templated parts"
msgstr "Includi gli elementi Distinta Base ereditati da prodotti template"

#: part/serializers.py:1945
msgid "Skip Invalid Rows"
msgstr "Salta Righe Non Valide"

#: part/serializers.py:1946
msgid "Enable this option to skip invalid rows"
msgstr "Abilita questa opzione per saltare le righe non valide"

#: part/serializers.py:1951
msgid "Copy Substitute Parts"
msgstr "Copia Articoli sostitutivi"

#: part/serializers.py:1952
msgid "Copy substitute parts when duplicate BOM items"
msgstr "Copia articoli sostitutivi quando duplichi gli elementi distinta base"

#: part/stocktake.py:218
msgid "Part ID"
msgstr "Codice Articolo"

#: part/stocktake.py:220
msgid "Part Description"
msgstr "Descrizione Articolo"

#: part/stocktake.py:221
msgid "Category ID"
msgstr "Id Categoria"

#: part/stocktake.py:224
msgid "Total Quantity"
msgstr "Quantità Totale"

#: part/stocktake.py:225
msgid "Total Cost Min"
msgstr "Costo Minimo Totale"

#: part/stocktake.py:226
msgid "Total Cost Max"
msgstr "Costo Massimo Totale"

#: part/stocktake.py:284
msgid "Stocktake Report Available"
msgstr "Report Inventario Disponibile"

#: part/stocktake.py:285
msgid "A new stocktake report is available for download"
msgstr "Un nuovo report di inventario è disponibile per il download"

#: part/tasks.py:38
msgid "Low stock notification"
msgstr "Notifica di magazzino bassa"

#: part/tasks.py:40
#, python-brace-format
msgid "The available stock for {part.name} has fallen below the configured minimum level"
msgstr "Lo stock disponibile per {part.name} è sceso sotto il livello minimo configurato"

#: plugin/api.py:78
msgid "Builtin"
msgstr ""

#: plugin/api.py:92
msgid "Mandatory"
msgstr ""

#: plugin/api.py:103
msgid "Sample"
msgstr ""

#: plugin/api.py:117 plugin/models.py:159
msgid "Installed"
msgstr "Installato"

#: plugin/api.py:184
msgid "Plugin cannot be deleted as it is currently active"
msgstr ""

#: plugin/base/action/api.py:34
msgid "No action specified"
msgstr "Nessuna azione specificata"

#: plugin/base/action/api.py:46
msgid "No matching action found"
msgstr "Nessuna azione corrispondente trovata"

#: plugin/base/barcodes/api.py:211
msgid "No match found for barcode data"
msgstr "Nessuna corrispondenza trovata per i dati del codice a barre"

#: plugin/base/barcodes/api.py:215
msgid "Match found for barcode data"
msgstr "Corrispondenza trovata per i dati del codice a barre"

#: plugin/base/barcodes/api.py:253 plugin/base/barcodes/serializers.py:77
msgid "Model is not supported"
msgstr ""

#: plugin/base/barcodes/api.py:258
msgid "Model instance not found"
msgstr "Istanza del modello non trovata"

#: plugin/base/barcodes/api.py:287
msgid "Barcode matches existing item"
msgstr "Il codice a barre corrisponde a un elemento esistente"

#: plugin/base/barcodes/api.py:418
msgid "No matching part data found"
msgstr "Nessun articolo corrispondente trovato"

#: plugin/base/barcodes/api.py:434
msgid "No matching supplier parts found"
msgstr "Nessun fornitore articolo corrispondente trovato"

#: plugin/base/barcodes/api.py:437
msgid "Multiple matching supplier parts found"
msgstr "Trovati più articoli fornitori corrispondenti"

#: plugin/base/barcodes/api.py:450 plugin/base/barcodes/api.py:677
msgid "No matching plugin found for barcode data"
msgstr "Nessuna plugin corrispondente trovato per i dati del codice a barre"

#: plugin/base/barcodes/api.py:460
msgid "Matched supplier part"
msgstr "Corrispondenza Articoli del Fornitore"

#: plugin/base/barcodes/api.py:528
msgid "Item has already been received"
msgstr "L'articolo è già stato ricevuto"

#: plugin/base/barcodes/api.py:576
msgid "No plugin match for supplier barcode"
msgstr "Nessun plugin corrisponde al codice a barre del fornitore"

#: plugin/base/barcodes/api.py:625
msgid "Multiple matching line items found"
msgstr "Trovati più articoli corrispondenti"

#: plugin/base/barcodes/api.py:628
msgid "No matching line item found"
msgstr "Nessun elemento corrispondente trovato"

#: plugin/base/barcodes/api.py:674
msgid "No sales order provided"
msgstr "Nessun ordine di vendita fornito"

#: plugin/base/barcodes/api.py:683
msgid "Barcode does not match an existing stock item"
msgstr "Il codice a barre non corrisponde a un articolo di magazzino valido"

#: plugin/base/barcodes/api.py:699
msgid "Stock item does not match line item"
msgstr "L'elemento in magazzino non corrisponde alla riga"

#: plugin/base/barcodes/api.py:729
msgid "Insufficient stock available"
msgstr "Scorte insufficienti disponibili"

#: plugin/base/barcodes/api.py:742
msgid "Stock item allocated to sales order"
msgstr "Articolo di magazzino assegnato all'ordine di vendita"

#: plugin/base/barcodes/api.py:745
msgid "Not enough information"
msgstr "Informazioni non sufficienti"

#: plugin/base/barcodes/mixins.py:308
#: plugin/builtin/barcodes/inventree_barcode.py:101
msgid "Found matching item"
msgstr "Trovato elemento corrispondente"

#: plugin/base/barcodes/mixins.py:374
msgid "Supplier part does not match line item"
msgstr "L'articolo del fornitore non corrisponde alla riga"

#: plugin/base/barcodes/mixins.py:377
msgid "Line item is already completed"
msgstr "La riga è già stata completata"

#: plugin/base/barcodes/mixins.py:414
msgid "Further information required to receive line item"
msgstr "Ulteriori informazioni richieste per ricevere la voce"

#: plugin/base/barcodes/mixins.py:422
msgid "Received purchase order line item"
msgstr "Ricevuta la linea dell'ordine d'acquisto"

#: plugin/base/barcodes/mixins.py:429
msgid "Failed to receive line item"
msgstr "Impossibile ricevere l'elemento della linea"

#: plugin/base/barcodes/serializers.py:53
msgid "Scanned barcode data"
msgstr "Codice a barre scansionato"

#: plugin/base/barcodes/serializers.py:62
msgid "Model name to generate barcode for"
msgstr "Nome del modello per generare codice a barre per"

#: plugin/base/barcodes/serializers.py:67
msgid "Primary key of model object to generate barcode for"
msgstr "Chiave primaria dell'oggetto modello per generare codice a barre per"

#: plugin/base/barcodes/serializers.py:137
msgid "Purchase Order to allocate items against"
msgstr "Ordine di acquisto per allocare oggetti contro"

#: plugin/base/barcodes/serializers.py:143
msgid "Purchase order is not open"
msgstr "L'ordine di acquisto non è aperto"

#: plugin/base/barcodes/serializers.py:161
msgid "Supplier to receive items from"
msgstr "Fornitore per ricevere articoli da"

#: plugin/base/barcodes/serializers.py:168
msgid "PurchaseOrder to receive items against"
msgstr "Ordine di acquisto per ricevere oggetti contro"

#: plugin/base/barcodes/serializers.py:174
msgid "Purchase order has not been placed"
msgstr "L'ordine di acquisto non è stato effettuato"

#: plugin/base/barcodes/serializers.py:182
msgid "Location to receive items into"
msgstr "Posizione in cui ricevere gli articoli"

#: plugin/base/barcodes/serializers.py:188
msgid "Cannot select a structural location"
msgstr "Non è possibile selezionare una posizione strutturale"

#: plugin/base/barcodes/serializers.py:196
msgid "Purchase order line item to receive items against"
msgstr ""

#: plugin/base/barcodes/serializers.py:202
msgid "Automatically allocate stock items to the purchase order"
msgstr ""

#: plugin/base/barcodes/serializers.py:215
msgid "Sales Order to allocate items against"
msgstr ""

#: plugin/base/barcodes/serializers.py:221
msgid "Sales order is not open"
msgstr ""

#: plugin/base/barcodes/serializers.py:229
msgid "Sales order line item to allocate items against"
msgstr ""

#: plugin/base/barcodes/serializers.py:236
msgid "Sales order shipment to allocate items against"
msgstr ""

#: plugin/base/barcodes/serializers.py:242
msgid "Shipment has already been delivered"
msgstr ""

#: plugin/base/barcodes/serializers.py:247
msgid "Quantity to allocate"
msgstr ""

#: plugin/base/label/label.py:39
msgid "Label printing failed"
msgstr "Stampa etichetta fallita"

#: plugin/base/label/mixins.py:55
msgid "Error rendering label to PDF"
msgstr ""

#: plugin/base/label/mixins.py:69
msgid "Error rendering label to HTML"
msgstr ""

#: plugin/base/label/mixins.py:146
msgid "No items provided to print"
msgstr ""

#: plugin/base/ui/serializers.py:30
msgid "Plugin Name"
msgstr "Nome Plugin"

#: plugin/base/ui/serializers.py:34
msgid "Feature Type"
msgstr "Tipo di funzionalità"

#: plugin/base/ui/serializers.py:39
msgid "Feature Label"
msgstr "Etichetta Funzionalità"

#: plugin/base/ui/serializers.py:44
msgid "Feature Title"
msgstr "Titolo della funzionalità"

#: plugin/base/ui/serializers.py:49
msgid "Feature Description"
msgstr "Descrizione Funzionalità"

#: plugin/base/ui/serializers.py:54
msgid "Feature Icon"
msgstr "Icona Funzionalità"

#: plugin/base/ui/serializers.py:58
msgid "Feature Options"
msgstr "Opzioni Funzionalità"

#: plugin/base/ui/serializers.py:61
msgid "Feature Context"
msgstr "Contesto Funzionalità"

#: plugin/base/ui/serializers.py:64
msgid "Feature Source (javascript)"
msgstr "Sorgente Funzionalità (javascript)"

#: plugin/builtin/barcodes/inventree_barcode.py:27
msgid "InvenTree Barcodes"
msgstr "InvenTree Codice a Barre"

#: plugin/builtin/barcodes/inventree_barcode.py:28
msgid "Provides native support for barcodes"
msgstr "Fornisce supporto nativo per codici a barre"

#: plugin/builtin/barcodes/inventree_barcode.py:30
#: plugin/builtin/exporter/bom_exporter.py:61
#: plugin/builtin/exporter/inventree_exporter.py:17
#: plugin/builtin/exporter/part_parameter_exporter.py:36
#: plugin/builtin/integration/core_notifications.py:33
#: plugin/builtin/integration/currency_exchange.py:21
#: plugin/builtin/integration/part_notifications.py:25
#: plugin/builtin/labels/inventree_label.py:26
#: plugin/builtin/labels/inventree_machine.py:64
#: plugin/builtin/labels/label_sheet.py:71
#: plugin/builtin/suppliers/digikey.py:19 plugin/builtin/suppliers/lcsc.py:21
#: plugin/builtin/suppliers/mouser.py:19 plugin/builtin/suppliers/tme.py:21
msgid "InvenTree contributors"
msgstr "Contributi d'InvenTree"

#: plugin/builtin/barcodes/inventree_barcode.py:34
msgid "Internal Barcode Format"
msgstr ""

#: plugin/builtin/barcodes/inventree_barcode.py:35
msgid "Select an internal barcode format"
msgstr ""

#: plugin/builtin/barcodes/inventree_barcode.py:37
msgid "JSON barcodes (human readable)"
msgstr ""

#: plugin/builtin/barcodes/inventree_barcode.py:38
msgid "Short barcodes (space optimized)"
msgstr ""

#: plugin/builtin/barcodes/inventree_barcode.py:43
msgid "Short Barcode Prefix"
msgstr ""

#: plugin/builtin/barcodes/inventree_barcode.py:45
msgid "Customize the prefix used for short barcodes, may be useful for environments with multiple InvenTree instances"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:17
msgid "Levels"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:18
msgid "Number of levels to export"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:23
#: plugin/builtin/exporter/part_parameter_exporter.py:17
msgid "Stock Data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:23
#: plugin/builtin/exporter/part_parameter_exporter.py:17
msgid "Include part stock data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:27
#: plugin/builtin/exporter/part_parameter_exporter.py:21
msgid "Pricing Data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:27
#: plugin/builtin/exporter/part_parameter_exporter.py:21
msgid "Include part pricing data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:31
msgid "Supplier Data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:31
msgid "Include supplier data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:36
msgid "Manufacturer Data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:37
msgid "Include manufacturer data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:42
msgid "Substitute Data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:43
msgid "Include substitute part data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:48
msgid "Parameter Data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:49
msgid "Include part parameter data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:58
msgid "Multi-Level BOM Exporter"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:59
msgid "Provides support for exporting multi-level BOMs"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:96
msgid "BOM Level"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:102
#, python-brace-format
msgid "Substitute {n}"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:108
#, python-brace-format
msgid "Supplier {n}"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:109
#, python-brace-format
msgid "Supplier {n} SKU"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:110
#, python-brace-format
msgid "Supplier {n} MPN"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:116
#, python-brace-format
msgid "Manufacturer {n}"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:117
#, python-brace-format
msgid "Manufacturer {n} MPN"
msgstr ""

#: plugin/builtin/exporter/inventree_exporter.py:14
msgid "InvenTree Generic Exporter"
msgstr ""

#: plugin/builtin/exporter/inventree_exporter.py:15
msgid "Provides support for exporting data from InvenTree"
msgstr ""

#: plugin/builtin/exporter/part_parameter_exporter.py:33
msgid "Part Parameter Exporter"
msgstr ""

#: plugin/builtin/exporter/part_parameter_exporter.py:34
msgid "Exporter for part parameter data"
msgstr ""

#: plugin/builtin/integration/core_notifications.py:32
msgid "InvenTree Notifications"
msgstr "InvenTree Notifiche"

#: plugin/builtin/integration/core_notifications.py:34
msgid "Integrated outgoing notification methods"
msgstr ""

#: plugin/builtin/integration/core_notifications.py:39
#: plugin/builtin/integration/core_notifications.py:78
msgid "Enable email notifications"
msgstr "Attiva notifiche email"

#: plugin/builtin/integration/core_notifications.py:40
#: plugin/builtin/integration/core_notifications.py:79
msgid "Allow sending of emails for event notifications"
msgstr "Consenti l'invio di email per le notifiche di eventi"

#: plugin/builtin/integration/core_notifications.py:45
msgid "Enable slack notifications"
msgstr "Abilita notifiche per rallentamenti"

#: plugin/builtin/integration/core_notifications.py:47
msgid "Allow sending of slack channel messages for event notifications"
msgstr "Consenti l'invio di messaggi di rallentamenti canale per le notifiche degli eventi"

#: plugin/builtin/integration/core_notifications.py:53
msgid "Slack incoming webhook url"
msgstr "Rallentamenti in entrata notifiche url"

#: plugin/builtin/integration/core_notifications.py:54
msgid "URL that is used to send messages to a slack channel"
msgstr "Questo URL è stato utilizzato per inviare messaggi a un canale rallentato"

#: plugin/builtin/integration/core_notifications.py:164
msgid "Open link"
msgstr "Apri collegamento"

#: plugin/builtin/integration/currency_exchange.py:22
msgid "InvenTree Currency Exchange"
msgstr ""

#: plugin/builtin/integration/currency_exchange.py:23
msgid "Default currency exchange integration"
msgstr ""

#: plugin/builtin/integration/part_notifications.py:24
msgid "Part Notifications"
msgstr ""

#: plugin/builtin/integration/part_notifications.py:26
msgid "Notify users about part changes"
msgstr ""

#: plugin/builtin/integration/part_notifications.py:31
msgid "Send notifications"
msgstr ""

#: plugin/builtin/integration/part_notifications.py:32
msgid "Send notifications for part changes to subscribed users"
msgstr ""

#: plugin/builtin/integration/part_notifications.py:49
msgid "Changed part notification"
msgstr ""

#: plugin/builtin/integration/part_notifications.py:59
#, python-brace-format
msgid "The part `{part.name}` has been triggered with a `{part_action}` event"
msgstr ""

#: plugin/builtin/labels/inventree_label.py:23
msgid "InvenTree PDF label printer"
msgstr ""

#: plugin/builtin/labels/inventree_label.py:24
msgid "Provides native support for printing PDF labels"
msgstr ""

#: plugin/builtin/labels/inventree_label.py:32
#: plugin/builtin/labels/label_sheet.py:77
msgid "Debug mode"
msgstr ""

#: plugin/builtin/labels/inventree_label.py:33
#: plugin/builtin/labels/label_sheet.py:78
msgid "Enable debug mode - returns raw HTML instead of PDF"
msgstr ""

#: plugin/builtin/labels/inventree_machine.py:61
msgid "InvenTree machine label printer"
msgstr ""

#: plugin/builtin/labels/inventree_machine.py:62
msgid "Provides support for printing using a machine"
msgstr ""

#: plugin/builtin/labels/inventree_machine.py:164
msgid "last used"
msgstr ""

#: plugin/builtin/labels/inventree_machine.py:181
msgid "Options"
msgstr ""

#: plugin/builtin/labels/label_sheet.py:30
msgid "Page size for the label sheet"
msgstr ""

#: plugin/builtin/labels/label_sheet.py:35
msgid "Skip Labels"
msgstr ""

#: plugin/builtin/labels/label_sheet.py:36
msgid "Skip this number of labels when printing label sheets"
msgstr ""

#: plugin/builtin/labels/label_sheet.py:42
msgid "Border"
msgstr ""

#: plugin/builtin/labels/label_sheet.py:43
msgid "Print a border around each label"
msgstr ""

#: plugin/builtin/labels/label_sheet.py:48 report/models.py:371
msgid "Landscape"
msgstr ""

#: plugin/builtin/labels/label_sheet.py:49
msgid "Print the label sheet in landscape mode"
msgstr ""

#: plugin/builtin/labels/label_sheet.py:54
msgid "Page Margin"
msgstr ""

#: plugin/builtin/labels/label_sheet.py:55
msgid "Margin around the page in mm"
msgstr ""

#: plugin/builtin/labels/label_sheet.py:68
msgid "InvenTree Label Sheet Printer"
msgstr ""

#: plugin/builtin/labels/label_sheet.py:69
msgid "Arrays multiple labels onto a single sheet"
msgstr ""

#: plugin/builtin/labels/label_sheet.py:121
msgid "Label is too large for page size"
msgstr ""

#: plugin/builtin/labels/label_sheet.py:160
msgid "No labels were generated"
msgstr ""

#: plugin/builtin/suppliers/digikey.py:16
msgid "Supplier Integration - DigiKey"
msgstr ""

#: plugin/builtin/suppliers/digikey.py:17
msgid "Provides support for scanning DigiKey barcodes"
msgstr ""

#: plugin/builtin/suppliers/digikey.py:26
msgid "The Supplier which acts as 'DigiKey'"
msgstr ""

#: plugin/builtin/suppliers/lcsc.py:18
msgid "Supplier Integration - LCSC"
msgstr ""

#: plugin/builtin/suppliers/lcsc.py:19
msgid "Provides support for scanning LCSC barcodes"
msgstr ""

#: plugin/builtin/suppliers/lcsc.py:27
msgid "The Supplier which acts as 'LCSC'"
msgstr ""

#: plugin/builtin/suppliers/mouser.py:16
msgid "Supplier Integration - Mouser"
msgstr ""

#: plugin/builtin/suppliers/mouser.py:17
msgid "Provides support for scanning Mouser barcodes"
msgstr ""

#: plugin/builtin/suppliers/mouser.py:25
msgid "The Supplier which acts as 'Mouser'"
msgstr ""

#: plugin/builtin/suppliers/tme.py:18
msgid "Supplier Integration - TME"
msgstr ""

#: plugin/builtin/suppliers/tme.py:19
msgid "Provides support for scanning TME barcodes"
msgstr ""

#: plugin/builtin/suppliers/tme.py:27
msgid "The Supplier which acts as 'TME'"
msgstr ""

#: plugin/installer.py:229 plugin/installer.py:307
msgid "Only staff users can administer plugins"
msgstr ""

#: plugin/installer.py:232
msgid "Plugin installation is disabled"
msgstr ""

#: plugin/installer.py:269
msgid "Installed plugin successfully"
msgstr ""

#: plugin/installer.py:274
#, python-brace-format
msgid "Installed plugin into {path}"
msgstr ""

#: plugin/installer.py:298
msgid "Plugin was not found in registry"
msgstr ""

#: plugin/installer.py:301
msgid "Plugin is not a packaged plugin"
msgstr ""

#: plugin/installer.py:304
msgid "Plugin package name not found"
msgstr ""

#: plugin/installer.py:324
msgid "Plugin uninstalling is disabled"
msgstr ""

#: plugin/installer.py:328
msgid "Plugin cannot be uninstalled as it is currently active"
msgstr ""

#: plugin/installer.py:332
msgid "Plugin is not installed"
msgstr ""

#: plugin/installer.py:348
msgid "Plugin installation not found"
msgstr ""

#: plugin/installer.py:363
msgid "Uninstalled plugin successfully"
msgstr ""

#: plugin/models.py:38
msgid "Plugin Configuration"
msgstr "Configurazione Plugin"

#: plugin/models.py:39
msgid "Plugin Configurations"
msgstr "Configurazioni Plugin"

#: plugin/models.py:46
msgid "Key of plugin"
msgstr "Key dei plugin"

#: plugin/models.py:54
msgid "PluginName of the plugin"
msgstr "PluginName del plugin"

#: plugin/models.py:61 plugin/serializers.py:119
msgid "Package Name"
msgstr "Nome Pacchetto"

#: plugin/models.py:63
msgid "Name of the installed package, if the plugin was installed via PIP"
msgstr ""

#: plugin/models.py:68
msgid "Is the plugin active"
msgstr "Il plugin è attivo"

#: plugin/models.py:168
msgid "Sample plugin"
msgstr "Plugin di esempio"

#: plugin/models.py:176
msgid "Builtin Plugin"
msgstr "Plugin Integrato"

#: plugin/models.py:184
msgid "Mandatory Plugin"
msgstr ""

#: plugin/models.py:189
msgid "Package Plugin"
msgstr ""

#: plugin/models.py:268
msgid "Plugin"
msgstr ""

#: plugin/models.py:315
msgid "Method"
msgstr "Metodo"

#: plugin/plugin.py:312
msgid "No author found"
msgstr "Nessun autore trovato"

#: plugin/registry.py:611
#, python-brace-format
msgid "Plugin '{p}' is not compatible with the current InvenTree version {v}"
msgstr ""

#: plugin/registry.py:614
#, python-brace-format
msgid "Plugin requires at least version {v}"
msgstr ""

#: plugin/registry.py:616
#, python-brace-format
msgid "Plugin requires at most version {v}"
msgstr ""

#: plugin/samples/integration/sample.py:52
msgid "Enable PO"
msgstr "Abilita PO"

#: plugin/samples/integration/sample.py:53
msgid "Enable PO functionality in InvenTree interface"
msgstr "Abilita funzionalità PO nell'interfaccia InvenTree"

#: plugin/samples/integration/sample.py:58
msgid "API Key"
msgstr ""

#: plugin/samples/integration/sample.py:59
msgid "Key required for accessing external API"
msgstr "Key richiesta per accedere alle API esterne"

#: plugin/samples/integration/sample.py:63
msgid "Numerical"
msgstr "Numerico"

#: plugin/samples/integration/sample.py:64
msgid "A numerical setting"
msgstr "Un'impostazione numerica"

#: plugin/samples/integration/sample.py:69
msgid "Choice Setting"
msgstr "Scegli l'impostazione"

#: plugin/samples/integration/sample.py:70
msgid "A setting with multiple choices"
msgstr "Un'impostazione con scelte multiple"

#: plugin/samples/integration/sample_currency_exchange.py:15
msgid "Sample currency exchange plugin"
msgstr ""

#: plugin/samples/integration/sample_currency_exchange.py:18
msgid "InvenTree Contributors"
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:27
msgid "Enable Part Panels"
msgstr "Abilita Pannelli della Parte"

#: plugin/samples/integration/user_interface_sample.py:28
msgid "Enable custom panels for Part views"
msgstr "Abilita pannelli personalizzati per le viste Parte"

#: plugin/samples/integration/user_interface_sample.py:33
msgid "Enable Purchase Order Panels"
msgstr "Abilita i Pannelli Ordine D'Acquisto"

#: plugin/samples/integration/user_interface_sample.py:34
msgid "Enable custom panels for Purchase Order views"
msgstr "Abilita pannelli personalizzati per le viste Ordine d'Acquisto"

#: plugin/samples/integration/user_interface_sample.py:39
msgid "Enable Broken Panels"
msgstr "Abilita Pannelli Interrotti"

#: plugin/samples/integration/user_interface_sample.py:40
msgid "Enable broken panels for testing"
msgstr "Abilita pannelli interrotti per testing"

#: plugin/samples/integration/user_interface_sample.py:45
msgid "Enable Dynamic Panel"
msgstr "Abilita Pannello Dinamico"

#: plugin/samples/integration/user_interface_sample.py:46
msgid "Enable dynamic panels for testing"
msgstr "Abilita pannelli dinamici per testing"

#: plugin/samples/integration/user_interface_sample.py:98
msgid "Part Panel"
msgstr "Pannello Articolo"

#: plugin/samples/integration/user_interface_sample.py:131
msgid "Broken Dashboard Item"
msgstr "Elemento Dashboard Interrotto"

#: plugin/samples/integration/user_interface_sample.py:133
msgid "This is a broken dashboard item - it will not render!"
msgstr "Questo è un elemento di dashborad interrotto - non sarà renderizzato!"

#: plugin/samples/integration/user_interface_sample.py:139
msgid "Sample Dashboard Item"
msgstr "Elemento Dashboard d'Esempio"

#: plugin/samples/integration/user_interface_sample.py:141
msgid "This is a sample dashboard item. It renders a simple string of HTML content."
msgstr "Questo è un esempio di elemento dashboard. Renderizza una semplice stringa di contenuto HTML."

#: plugin/samples/integration/user_interface_sample.py:147
msgid "Context Dashboard Item"
msgstr "Elemento Dashboard Contesto"

#: plugin/samples/integration/user_interface_sample.py:161
msgid "Admin Dashboard Item"
msgstr "Elemento Dashboard Amministratore"

#: plugin/samples/integration/user_interface_sample.py:162
msgid "This is an admin-only dashboard item."
msgstr "Questo è un elemento dashboard solo per amministratori."

#: plugin/serializers.py:86
msgid "Source File"
msgstr "File Sorgente"

#: plugin/serializers.py:87
msgid "Path to the source file for admin integration"
msgstr "Percorso del file sorgente per l'integrazione amministrazione"

#: plugin/serializers.py:94
msgid "Optional context data for the admin integration"
msgstr "Dati di contesto opzionali per l'integrazione amministrazione"

#: plugin/serializers.py:110
msgid "Source URL"
msgstr "URL di origine"

#: plugin/serializers.py:112
msgid "Source for the package - this can be a custom registry or a VCS path"
msgstr "Fonte per il pacchetto - questo può essere un registro personalizzato o un percorso VCS"

#: plugin/serializers.py:121
msgid "Name for the Plugin Package - can also contain a version indicator"
msgstr "Nome per il Pacchetto Plugin - può anche contenere un indicatore di versione"

#: plugin/serializers.py:128
msgid "Version"
msgstr "Versione"

#: plugin/serializers.py:130
msgid "Version specifier for the plugin. Leave blank for latest version."
msgstr ""

#: plugin/serializers.py:135
msgid "Confirm plugin installation"
msgstr "Conferma installazione plugin"

#: plugin/serializers.py:137
msgid "This will install this plugin now into the current instance. The instance will go into maintenance."
msgstr "Questo plugin verrà installato ora nell'istanza corrente. L'istanza andrà in manutenzione."

#: plugin/serializers.py:150
msgid "Installation not confirmed"
msgstr "Installazione non confermata"

#: plugin/serializers.py:152
msgid "Either packagename of URL must be provided"
msgstr "Deve essere fornito uno dei nomi del pacchetto URL"

#: plugin/serializers.py:188
msgid "Full reload"
msgstr ""

#: plugin/serializers.py:189
msgid "Perform a full reload of the plugin registry"
msgstr ""

#: plugin/serializers.py:195
msgid "Force reload"
msgstr ""

#: plugin/serializers.py:197
msgid "Force a reload of the plugin registry, even if it is already loaded"
msgstr ""

#: plugin/serializers.py:204
msgid "Collect plugins"
msgstr ""

#: plugin/serializers.py:205
msgid "Collect plugins and add them to the registry"
msgstr ""

#: plugin/serializers.py:232
msgid "Activate Plugin"
msgstr ""

#: plugin/serializers.py:233
msgid "Activate this plugin"
msgstr ""

#: plugin/serializers.py:253
msgid "Delete configuration"
msgstr ""

#: plugin/serializers.py:254
msgid "Delete the plugin configuration from the database"
msgstr ""

#: report/api.py:44 report/serializers.py:98 report/serializers.py:148
msgid "Items"
msgstr ""

#: report/api.py:121
msgid "Plugin not found"
msgstr ""

#: report/api.py:123
msgid "Plugin is not active"
msgstr ""

#: report/api.py:125
msgid "Plugin does not support label printing"
msgstr ""

#: report/api.py:173
msgid "Invalid label dimensions"
msgstr ""

#: report/api.py:188 report/api.py:267
msgid "No valid items provided to template"
msgstr ""

#: report/helpers.py:43
msgid "A4"
msgstr ""

#: report/helpers.py:44
msgid "A3"
msgstr ""

#: report/helpers.py:45
msgid "Legal"
msgstr ""

#: report/helpers.py:46
msgid "Letter"
msgstr ""

#: report/models.py:117
msgid "Template file with this name already exists"
msgstr ""

#: report/models.py:204
msgid "Template name"
msgstr "Nome modello"

#: report/models.py:210
msgid "Template description"
msgstr "Descrizione del template"

#: report/models.py:216
msgid "Revision number (auto-increments)"
msgstr ""

#: report/models.py:222
msgid "Attach to Model on Print"
msgstr "Allega al Modello su Stampa"

#: report/models.py:224
msgid "Save report output as an attachment against linked model instance when printing"
msgstr "Salva l'output del report come allegato contro l'istanza del modello collegato durante la stampa"

#: report/models.py:265
msgid "Filename Pattern"
msgstr "Formato del nome file"

#: report/models.py:266
msgid "Pattern for generating filenames"
msgstr ""

#: report/models.py:271
msgid "Template is enabled"
msgstr ""

#: report/models.py:278
msgid "Target model type for template"
msgstr ""

#: report/models.py:298
msgid "Filters"
msgstr "Filtri"

#: report/models.py:299
msgid "Template query filters (comma-separated list of key=value pairs)"
msgstr ""

#: report/models.py:358 report/models.py:564
msgid "Template file"
msgstr ""

#: report/models.py:366
msgid "Page size for PDF reports"
msgstr ""

#: report/models.py:372
msgid "Render report in landscape orientation"
msgstr ""

#: report/models.py:486
#, python-brace-format
msgid "Report generated from template {self.name}"
msgstr ""

#: report/models.py:511
msgid "Error generating report"
msgstr ""

#: report/models.py:570
msgid "Width [mm]"
msgstr "Larghezza [mm]"

#: report/models.py:571
msgid "Label width, specified in mm"
msgstr "Larghezza dell'etichetta, specificata in mm"

#: report/models.py:577
msgid "Height [mm]"
msgstr "Altezza [mm]"

#: report/models.py:578
msgid "Label height, specified in mm"
msgstr "Larghezza dell'etichetta, specificata in mm"

#: report/models.py:688
msgid "Error printing labels"
msgstr ""

#: report/models.py:707
msgid "Snippet"
msgstr ""

#: report/models.py:708
msgid "Report snippet file"
msgstr "Report file snippet"

#: report/models.py:715
msgid "Snippet file description"
msgstr "Descrizione file snippet"

#: report/models.py:733
msgid "Asset"
msgstr "Risorsa"

#: report/models.py:734
msgid "Report asset file"
msgstr "Report file risorsa"

#: report/models.py:741
msgid "Asset file description"
msgstr "File risorsa descrizione"

#: report/serializers.py:91
msgid "Select report template"
msgstr ""

#: report/serializers.py:99 report/serializers.py:149
msgid "List of item primary keys to include in the report"
msgstr ""

#: report/serializers.py:132
msgid "Select label template"
msgstr ""

#: report/serializers.py:140
msgid "Printing Plugin"
msgstr ""

#: report/serializers.py:141
msgid "Select plugin to use for label printing"
msgstr ""

#: report/templates/label/part_label.html:31
#: report/templates/label/stockitem_qr.html:21
#: report/templates/label/stocklocation_qr.html:20
msgid "QR Code"
msgstr ""

#: report/templates/label/part_label_code128.html:31
#: report/templates/label/stocklocation_qr_and_text.html:31
msgid "QR code"
msgstr ""

#: report/templates/report/inventree_bill_of_materials_report.html:100
msgid "Bill of Materials"
msgstr "Distinta base"

#: report/templates/report/inventree_bill_of_materials_report.html:133
msgid "Materials needed"
msgstr "Materiali necessari"

#: report/templates/report/inventree_build_order_report.html:98
#: report/templates/report/inventree_purchase_order_report.html:40
#: report/templates/report/inventree_sales_order_report.html:40
#: report/templates/report/inventree_sales_order_shipment_report.html:37
#: report/templates/report/inventree_test_report.html:84
#: report/templates/report/inventree_test_report.html:162
msgid "Part image"
msgstr ""

#: report/templates/report/inventree_build_order_report.html:121
msgid "Issued"
msgstr "Emesso"

#: report/templates/report/inventree_build_order_report.html:146
msgid "Required For"
msgstr "Richiesto Per"

#: report/templates/report/inventree_purchase_order_report.html:15
msgid "Supplier was deleted"
msgstr "Il fornitore è stato eliminato"

#: report/templates/report/inventree_purchase_order_report.html:30
#: report/templates/report/inventree_sales_order_report.html:30
msgid "Unit Price"
msgstr "Prezzo Unitario"

#: report/templates/report/inventree_purchase_order_report.html:55
#: report/templates/report/inventree_return_order_report.html:48
#: report/templates/report/inventree_sales_order_report.html:55
msgid "Extra Line Items"
msgstr ""

#: report/templates/report/inventree_purchase_order_report.html:72
#: report/templates/report/inventree_sales_order_report.html:72
msgid "Total"
msgstr "Totale"

#: report/templates/report/inventree_return_order_report.html:25
#: report/templates/report/inventree_sales_order_shipment_report.html:45
#: report/templates/report/inventree_test_report.html:88 stock/models.py:1006
#: stock/serializers.py:163
msgid "Serial Number"
msgstr "Numero Seriale"

#: report/templates/report/inventree_sales_order_shipment_report.html:23
msgid "Allocations"
msgstr "Assegnazioni"

#: report/templates/report/inventree_sales_order_shipment_report.html:47
msgid "Batch"
msgstr "Lotto"

#: report/templates/report/inventree_stock_location_report.html:97
msgid "Stock location items"
msgstr ""

#: report/templates/report/inventree_test_report.html:21
msgid "Stock Item Test Report"
msgstr "Test Report Elemento Stock"

#: report/templates/report/inventree_test_report.html:97
msgid "Test Results"
msgstr "Risultati Test"

#: report/templates/report/inventree_test_report.html:102
msgid "Test"
msgstr ""

#: report/templates/report/inventree_test_report.html:129
msgid "Pass"
msgstr "Passaggio"

#: report/templates/report/inventree_test_report.html:131
msgid "Fail"
msgstr "Fallito"

#: report/templates/report/inventree_test_report.html:138
msgid "No result (required)"
msgstr "Nessun risultato (richiesto)"

#: report/templates/report/inventree_test_report.html:140
msgid "No result"
msgstr "Nessun risultato"

#: report/templates/report/inventree_test_report.html:153
#: stock/serializers.py:668
msgid "Installed Items"
msgstr "Elementi installati"

#: report/templates/report/inventree_test_report.html:167
msgid "Serial"
msgstr "Seriale"

#: report/templatetags/report.py:144
msgid "Asset file does not exist"
msgstr ""

#: report/templatetags/report.py:201 report/templatetags/report.py:277
msgid "Image file not found"
msgstr ""

#: report/templatetags/report.py:302
msgid "part_image tag requires a Part instance"
msgstr ""

#: report/templatetags/report.py:349
msgid "company_image tag requires a Company instance"
msgstr ""

#: stock/api.py:255
msgid "Filter by location depth"
msgstr ""

#: stock/api.py:275
msgid "Filter by top-level locations"
msgstr ""

#: stock/api.py:290
msgid "Include sub-locations in filtered results"
msgstr ""

#: stock/api.py:311 stock/serializers.py:1256
msgid "Parent Location"
msgstr ""

#: stock/api.py:312
msgid "Filter by parent location"
msgstr ""

#: stock/api.py:554
msgid "Part name (case insensitive)"
msgstr "Nome della parte (maiuscole e minuscole)"

#: stock/api.py:560
msgid "Part name contains (case insensitive)"
msgstr "Il nome della parte contiene (maiuscole e minuscole)"

#: stock/api.py:566
msgid "Part name (regex)"
msgstr "Nome della parte (regex)"

#: stock/api.py:571
msgid "Part IPN (case insensitive)"
msgstr "IPN della parte (maiuscole e minuscole)"

#: stock/api.py:577
msgid "Part IPN contains (case insensitive)"
msgstr "IPN della parte contiene (maiuscole e minuscole)"

#: stock/api.py:583
msgid "Part IPN (regex)"
msgstr "IPN della parte (regex)"

#: stock/api.py:595
msgid "Minimum stock"
msgstr "Giacenza minima"

#: stock/api.py:599
msgid "Maximum stock"
msgstr "Giacenza massima"

#: stock/api.py:602
msgid "Status Code"
msgstr "Codici di stato"

#: stock/api.py:642
msgid "External Location"
msgstr "Ubicazione Esterna"

#: stock/api.py:741
msgid "Consumed by Build Order"
msgstr ""

#: stock/api.py:751
msgid "Installed in other stock item"
msgstr ""

#: stock/api.py:840
msgid "Part Tree"
msgstr ""

#: stock/api.py:862
msgid "Updated before"
msgstr ""

#: stock/api.py:866
msgid "Updated after"
msgstr ""

#: stock/api.py:870
msgid "Stocktake Before"
msgstr ""

#: stock/api.py:874
msgid "Stocktake After"
msgstr ""

#: stock/api.py:879
msgid "Expiry date before"
msgstr ""

#: stock/api.py:883
msgid "Expiry date after"
msgstr ""

#: stock/api.py:886 stock/serializers.py:673
msgid "Stale"
msgstr "Obsoleto"

#: stock/api.py:987
msgid "Quantity is required"
msgstr "La quantità è richiesta"

#: stock/api.py:992
msgid "Valid part must be supplied"
msgstr "Deve essere fornita un articolo valido"

#: stock/api.py:1019
msgid "The given supplier part does not exist"
msgstr ""

#: stock/api.py:1029
msgid "The supplier part has a pack size defined, but flag use_pack_size not set"
msgstr ""

#: stock/api.py:1056
msgid "Serial numbers cannot be supplied for a non-trackable part"
msgstr "I numeri di serie non possono essere forniti per un articolo non tracciabile"

#: stock/models.py:70
msgid "Stock Location type"
msgstr ""

#: stock/models.py:71
msgid "Stock Location types"
msgstr ""

#: stock/models.py:97
msgid "Default icon for all locations that have no icon set (optional)"
msgstr ""

#: stock/models.py:155 stock/models.py:968
msgid "Stock Location"
msgstr "Ubicazione magazzino"

#: stock/models.py:156 users/ruleset.py:35
msgid "Stock Locations"
msgstr "Posizioni magazzino"

#: stock/models.py:205 stock/models.py:1133
msgid "Owner"
msgstr "Proprietario"

#: stock/models.py:206 stock/models.py:1134
msgid "Select Owner"
msgstr "Seleziona Owner"

#: stock/models.py:214
msgid "Stock items may not be directly located into a structural stock locations, but may be located to child locations."
msgstr "Gli elementi di magazzino non possono essere direttamente situati in un magazzino strutturale, ma possono essere situati in ubicazioni secondarie."

#: stock/models.py:221 users/models.py:499
msgid "External"
msgstr "Esterno"

#: stock/models.py:222
msgid "This is an external stock location"
msgstr "Si tratta di una posizione esterna al magazzino"

#: stock/models.py:228
msgid "Location type"
msgstr ""

#: stock/models.py:232
msgid "Stock location type of this location"
msgstr ""

#: stock/models.py:304
msgid "You cannot make this stock location structural because some stock items are already located into it!"
msgstr "Non puoi rendere strutturale questa posizione di magazzino perché alcuni elementi di magazzino sono già posizionati al suo interno!"

#: stock/models.py:562
msgid "Part must be specified"
msgstr "L'articolo deve essere specificato"

#: stock/models.py:827
msgid "Stock items cannot be located into structural stock locations!"
msgstr "Gli articoli di magazzino non possono essere ubicati in posizioni di magazzino strutturali!"

#: stock/models.py:854 stock/serializers.py:516
msgid "Stock item cannot be created for virtual parts"
msgstr "Non è possibile creare un elemento di magazzino per articoli virtuali"

#: stock/models.py:871
#, python-brace-format
msgid "Part type ('{self.supplier_part.part}') must be {self.part}"
msgstr ""

#: stock/models.py:881 stock/models.py:894
msgid "Quantity must be 1 for item with a serial number"
msgstr "La quantità deve essere 1 per elementi con un numero di serie"

#: stock/models.py:884
msgid "Serial number cannot be set if quantity greater than 1"
msgstr "Il numero di serie non può essere impostato se la quantità è maggiore di 1"

#: stock/models.py:906
msgid "Item cannot belong to itself"
msgstr "L'elemento non può appartenere a se stesso"

#: stock/models.py:911
msgid "Item must have a build reference if is_building=True"
msgstr "L'elemento deve avere un riferimento di costruzione se is_building=True"

#: stock/models.py:924
msgid "Build reference does not point to the same part object"
msgstr "Il riferimento di costruzione non punta allo stesso oggetto dell'articolo"

#: stock/models.py:938
msgid "Parent Stock Item"
msgstr "Elemento di magazzino principale"

#: stock/models.py:950
msgid "Base part"
msgstr "Articolo base"

#: stock/models.py:960
msgid "Select a matching supplier part for this stock item"
msgstr "Seleziona un fornitore articolo corrispondente per questo elemento di magazzino"

#: stock/models.py:972
msgid "Where is this stock item located?"
msgstr "Dove si trova questo articolo di magazzino?"

#: stock/models.py:980 stock/serializers.py:1667
msgid "Packaging this stock item is stored in"
msgstr "Imballaggio di questo articolo di magazzino è collocato in"

#: stock/models.py:986
msgid "Installed In"
msgstr "Installato In"

#: stock/models.py:991
msgid "Is this item installed in another item?"
msgstr "Questo elemento è stato installato su un altro elemento?"

#: stock/models.py:1010
msgid "Serial number for this item"
msgstr "Numero di serie per questo elemento"

#: stock/models.py:1027 stock/serializers.py:1652
msgid "Batch code for this stock item"
msgstr "Codice lotto per questo elemento di magazzino"

#: stock/models.py:1032
msgid "Stock Quantity"
msgstr "Quantità disponibile"

#: stock/models.py:1042
msgid "Source Build"
msgstr "Genera Costruzione"

#: stock/models.py:1045
msgid "Build for this stock item"
msgstr "Costruisci per questo elemento di magazzino"

#: stock/models.py:1052
msgid "Consumed By"
msgstr ""

#: stock/models.py:1055
msgid "Build order which consumed this stock item"
msgstr ""

#: stock/models.py:1064
msgid "Source Purchase Order"
msgstr "Origina Ordine di Acquisto"

#: stock/models.py:1068
msgid "Purchase order for this stock item"
msgstr "Ordine d'acquisto per questo articolo in magazzino"

#: stock/models.py:1074
msgid "Destination Sales Order"
msgstr "Destinazione Ordine di Vendita"

#: stock/models.py:1085
msgid "Expiry date for stock item. Stock will be considered expired after this date"
msgstr "Data di scadenza per l'elemento di magazzino. Le scorte saranno considerate scadute dopo questa data"

#: stock/models.py:1103
msgid "Delete on deplete"
msgstr "Elimina al esaurimento"

#: stock/models.py:1104
msgid "Delete this Stock Item when stock is depleted"
msgstr "Cancella questo Elemento di Magazzino quando la giacenza è esaurita"

#: stock/models.py:1125
msgid "Single unit purchase price at time of purchase"
msgstr "Prezzo di acquisto unitario al momento dell’acquisto"

#: stock/models.py:1156
msgid "Converted to part"
msgstr "Convertito in articolo"

#: stock/models.py:1721
msgid "Part is not set as trackable"
msgstr "L'articolo non è impostato come tracciabile"

#: stock/models.py:1727
msgid "Quantity must be integer"
msgstr "La quantità deve essere un numero intero"

#: stock/models.py:1735
#, python-brace-format
msgid "Quantity must not exceed available stock quantity ({self.quantity})"
msgstr ""

#: stock/models.py:1741
msgid "Serial numbers must be provided as a list"
msgstr "I numeri di serie devono essere forniti come elenco"

#: stock/models.py:1746
msgid "Quantity does not match serial numbers"
msgstr "La quantità non corrisponde ai numeri di serie"

#: stock/models.py:1868 stock/models.py:2777
msgid "Test template does not exist"
msgstr ""

#: stock/models.py:1886
msgid "Stock item has been assigned to a sales order"
msgstr "L'elemento di magazzino è stato assegnato a un ordine di vendita"

#: stock/models.py:1890
msgid "Stock item is installed in another item"
msgstr "L'elemento di magazzino è installato in un altro elemento"

#: stock/models.py:1893
msgid "Stock item contains other items"
msgstr "L'elemento di magazzino contiene altri elementi"

#: stock/models.py:1896
msgid "Stock item has been assigned to a customer"
msgstr "L'elemento di magazzino è stato assegnato a un cliente"

#: stock/models.py:1899 stock/models.py:2073
msgid "Stock item is currently in production"
msgstr "L'elemento di magazzino è attualmente in produzione"

#: stock/models.py:1902
msgid "Serialized stock cannot be merged"
msgstr "Il magazzino serializzato non può essere unito"

#: stock/models.py:1909 stock/serializers.py:1545
msgid "Duplicate stock items"
msgstr "Duplica elementi di magazzino"

#: stock/models.py:1913
msgid "Stock items must refer to the same part"
msgstr "Gli elementi di magazzino devono riferirsi allo stesso articolo"

#: stock/models.py:1921
msgid "Stock items must refer to the same supplier part"
msgstr "Gli elementi di magazzino devono riferirsi allo stesso articolo fornitore"

#: stock/models.py:1926
msgid "Stock status codes must match"
msgstr "I codici di stato dello stock devono corrispondere"

#: stock/models.py:2196
msgid "StockItem cannot be moved as it is not in stock"
msgstr "Le giacenze non possono essere spostate perché non disponibili"

#: stock/models.py:2678
msgid "Stock Item Tracking"
msgstr ""

#: stock/models.py:2709
msgid "Entry notes"
msgstr "Note d'ingresso"

#: stock/models.py:2749
msgid "Stock Item Test Result"
msgstr ""

#: stock/models.py:2780
msgid "Value must be provided for this test"
msgstr "Il valore deve essere fornito per questo test"

#: stock/models.py:2784
msgid "Attachment must be uploaded for this test"
msgstr "L'allegato deve essere caricato per questo test"

#: stock/models.py:2789
msgid "Invalid value for this test"
msgstr ""

#: stock/models.py:2813
msgid "Test result"
msgstr "Risultato Test"

#: stock/models.py:2820
msgid "Test output value"
msgstr "Test valore output"

#: stock/models.py:2828 stock/serializers.py:259
msgid "Test result attachment"
msgstr "Risultato della prova allegato"

#: stock/models.py:2832
msgid "Test notes"
msgstr "Note del test"

#: stock/models.py:2840
msgid "Test station"
msgstr ""

#: stock/models.py:2841
msgid "The identifier of the test station where the test was performed"
msgstr ""

#: stock/models.py:2847
msgid "Started"
msgstr ""

#: stock/models.py:2848
msgid "The timestamp of the test start"
msgstr ""

#: stock/models.py:2854
msgid "Finished"
msgstr ""

#: stock/models.py:2855
msgid "The timestamp of the test finish"
msgstr ""

#: stock/serializers.py:84
msgid "Generated batch code"
msgstr ""

#: stock/serializers.py:93
msgid "Select build order"
msgstr ""

#: stock/serializers.py:102
msgid "Select stock item to generate batch code for"
msgstr ""

#: stock/serializers.py:111
msgid "Select location to generate batch code for"
msgstr ""

#: stock/serializers.py:120
msgid "Select part to generate batch code for"
msgstr ""

#: stock/serializers.py:129
msgid "Select purchase order"
msgstr ""

#: stock/serializers.py:136
msgid "Enter quantity for batch code"
msgstr ""

#: stock/serializers.py:162
msgid "Generated serial number"
msgstr ""

#: stock/serializers.py:172
msgid "Select part to generate serial number for"
msgstr ""

#: stock/serializers.py:180
msgid "Quantity of serial numbers to generate"
msgstr ""

#: stock/serializers.py:248
msgid "Test template for this result"
msgstr ""

#: stock/serializers.py:272
msgid "Template ID or test name must be provided"
msgstr ""

#: stock/serializers.py:304
msgid "The test finished time cannot be earlier than the test started time"
msgstr ""

#: stock/serializers.py:341
msgid "Serial number is too large"
msgstr "Il numero di serie è troppo grande"

#: stock/serializers.py:487
msgid "Parent Item"
msgstr "Elemento principale"

#: stock/serializers.py:488
msgid "Parent stock item"
msgstr ""

#: stock/serializers.py:508
msgid "Use pack size when adding: the quantity defined is the number of packs"
msgstr ""

#: stock/serializers.py:618
msgid "Supplier Part Number"
msgstr ""

#: stock/serializers.py:665 users/models.py:193
msgid "Expired"
msgstr "Scaduto"

#: stock/serializers.py:671
msgid "Child Items"
msgstr "Elementi secondari"

#: stock/serializers.py:675
msgid "Tracking Items"
msgstr ""

#: stock/serializers.py:681
msgid "Purchase price of this stock item, per unit or pack"
msgstr ""

#: stock/serializers.py:719
msgid "Enter number of stock items to serialize"
msgstr "Inserisci il numero di elementi di magazzino da serializzare"

#: stock/serializers.py:732
#, python-brace-format
msgid "Quantity must not exceed available stock quantity ({q})"
msgstr "La quantità non deve superare la quantità disponibile ({q})"

#: stock/serializers.py:739
msgid "Enter serial numbers for new items"
msgstr "Inserisci i numeri di serie per i nuovi elementi"

#: stock/serializers.py:750 stock/serializers.py:1502 stock/serializers.py:1790
msgid "Destination stock location"
msgstr "Posizione magazzino di destinazione"

#: stock/serializers.py:757
msgid "Optional note field"
msgstr "Note opzionali elemento"

#: stock/serializers.py:767
msgid "Serial numbers cannot be assigned to this part"
msgstr "Numeri di serie non possono essere assegnati a questo articolo"

#: stock/serializers.py:787
msgid "Serial numbers already exist"
msgstr "Numeri di serie già esistenti"

#: stock/serializers.py:826
msgid "Select stock item to install"
msgstr "Seleziona elementi di magazzino da installare"

#: stock/serializers.py:833
msgid "Quantity to Install"
msgstr ""

#: stock/serializers.py:834
msgid "Enter the quantity of items to install"
msgstr ""

#: stock/serializers.py:839 stock/serializers.py:919 stock/serializers.py:1049
#: stock/serializers.py:1101
msgid "Add transaction note (optional)"
msgstr "Aggiungi nota di transazione (opzionale)"

#: stock/serializers.py:847
msgid "Quantity to install must be at least 1"
msgstr ""

#: stock/serializers.py:855
msgid "Stock item is unavailable"
msgstr "Elemento di magazzino non disponibile"

#: stock/serializers.py:866
msgid "Selected part is not in the Bill of Materials"
msgstr "L'articolo selezionato non è nella Fattura dei Materiali"

#: stock/serializers.py:879
msgid "Quantity to install must not exceed available quantity"
msgstr ""

#: stock/serializers.py:914
msgid "Destination location for uninstalled item"
msgstr "Posizione di destinazione per gli elementi disinstallati"

#: stock/serializers.py:949
msgid "Select part to convert stock item into"
msgstr "Seleziona l'articolo in cui convertire l'elemento di magazzino"

#: stock/serializers.py:962
msgid "Selected part is not a valid option for conversion"
msgstr "L'articolo selezionato non è una valida opzione per la conversione"

#: stock/serializers.py:979
msgid "Cannot convert stock item with assigned SupplierPart"
msgstr ""

#: stock/serializers.py:1013
msgid "Stock item status code"
msgstr ""

#: stock/serializers.py:1042
msgid "Destination location for returned item"
msgstr "Posizione di destinazione per l'elemento restituito"

#: stock/serializers.py:1085
msgid "Select stock items to change status"
msgstr ""

#: stock/serializers.py:1091
msgid "No stock items selected"
msgstr ""

#: stock/serializers.py:1185 stock/serializers.py:1262
msgid "Sublocations"
msgstr "Sottoallocazioni"

#: stock/serializers.py:1257
msgid "Parent stock location"
msgstr ""

#: stock/serializers.py:1374
msgid "Part must be salable"
msgstr "L'articolo deve essere vendibile"

#: stock/serializers.py:1378
msgid "Item is allocated to a sales order"
msgstr "L'elemento è assegnato a un ordine di vendita"

#: stock/serializers.py:1382
msgid "Item is allocated to a build order"
msgstr "Elemento assegnato a un ordine di costruzione"

#: stock/serializers.py:1406
msgid "Customer to assign stock items"
msgstr "Cliente a cui assegnare elementi di magazzino"

#: stock/serializers.py:1412
msgid "Selected company is not a customer"
msgstr "L'azienda selezionata non è un cliente"

#: stock/serializers.py:1420
msgid "Stock assignment notes"
msgstr "Note sull'assegnazione delle scorte"

#: stock/serializers.py:1430 stock/serializers.py:1695
msgid "A list of stock items must be provided"
msgstr "Deve essere fornito un elenco degli elementi di magazzino"

#: stock/serializers.py:1509
msgid "Stock merging notes"
msgstr "Note di fusione di magazzino"

#: stock/serializers.py:1514
msgid "Allow mismatched suppliers"
msgstr "Consenti fornitori non corrispondenti"

#: stock/serializers.py:1515
msgid "Allow stock items with different supplier parts to be merged"
msgstr "Consenti di unire gli elementi di magazzino che hanno fornitori diversi"

#: stock/serializers.py:1520
msgid "Allow mismatched status"
msgstr "Consenti stato non corrispondente"

#: stock/serializers.py:1521
msgid "Allow stock items with different status codes to be merged"
msgstr "Consenti di unire gli elementi di magazzino con diversi codici di stato"

#: stock/serializers.py:1531
msgid "At least two stock items must be provided"
msgstr "Devono essere riforniti almeno due elementi in magazzino"

#: stock/serializers.py:1598
msgid "No Change"
msgstr ""

#: stock/serializers.py:1627
msgid "StockItem primary key value"
msgstr "Valore di chiave primaria StockItem"

#: stock/serializers.py:1639
msgid "Stock item is not in stock"
msgstr ""

#: stock/serializers.py:1685
msgid "Stock transaction notes"
msgstr "Note sugli spostamenti di magazzino"

#: stock/serializers.py:1831
msgid "Next Serial Number"
msgstr ""

#: stock/serializers.py:1837
msgid "Previous Serial Number"
msgstr ""

#: stock/status_codes.py:11
msgid "OK"
msgstr ""

#: stock/status_codes.py:12
msgid "Attention needed"
msgstr "Attenzione necessaria"

#: stock/status_codes.py:13
msgid "Damaged"
msgstr "Danneggiato"

#: stock/status_codes.py:14
msgid "Destroyed"
msgstr "Distrutto"

#: stock/status_codes.py:15
msgid "Rejected"
msgstr "Respinto"

#: stock/status_codes.py:19
msgid "Quarantined"
msgstr "In quarantena"

#: stock/status_codes.py:44
msgid "Legacy stock tracking entry"
msgstr "Voce di tracciamento stock preesistente"

#: stock/status_codes.py:46
msgid "Stock item created"
msgstr "Elemento stock creato"

#: stock/status_codes.py:49
msgid "Edited stock item"
msgstr "Elemento stock modificato"

#: stock/status_codes.py:50
msgid "Assigned serial number"
msgstr "Numero di serie assegnato"

#: stock/status_codes.py:53
msgid "Stock counted"
msgstr "Stock contato"

#: stock/status_codes.py:54
msgid "Stock manually added"
msgstr "Stock aggiunto manualmente"

#: stock/status_codes.py:55
msgid "Stock manually removed"
msgstr "Stock rimosso manualmente"

#: stock/status_codes.py:58
msgid "Location changed"
msgstr "Posizione cambiata"

#: stock/status_codes.py:59
msgid "Stock updated"
msgstr "Stock aggiornato"

#: stock/status_codes.py:62
msgid "Installed into assembly"
msgstr "Installato nell'assemblaggio"

#: stock/status_codes.py:63
msgid "Removed from assembly"
msgstr "Rimosso dall'assemblaggio"

#: stock/status_codes.py:65
msgid "Installed component item"
msgstr "Componente installato"

#: stock/status_codes.py:66
msgid "Removed component item"
msgstr "Elemento componente rimosso"

#: stock/status_codes.py:69
msgid "Split from parent item"
msgstr "Diviso dall'elemento genitore"

#: stock/status_codes.py:70
msgid "Split child item"
msgstr "Dividi elemento figlio"

#: stock/status_codes.py:73
msgid "Merged stock items"
msgstr "Elemento stock raggruppato"

#: stock/status_codes.py:76
msgid "Converted to variant"
msgstr "Convertito in variante"

#: stock/status_codes.py:79
msgid "Build order output created"
msgstr "Genera l'output dell'ordine creato"

#: stock/status_codes.py:80
msgid "Build order output completed"
msgstr "Build order output completato"

#: stock/status_codes.py:81
msgid "Build order output rejected"
msgstr "Ordine di costruzione rifiutato"

#: stock/status_codes.py:82
msgid "Consumed by build order"
msgstr "Impegnato dall'ordine di costruzione"

#: stock/status_codes.py:85
msgid "Shipped against Sales Order"
msgstr "Spedito contro l'ordine di vendita"

#: stock/status_codes.py:88
msgid "Received against Purchase Order"
msgstr "Ricevuto contro l'ordine di acquisto"

#: stock/status_codes.py:91
msgid "Returned against Return Order"
msgstr "Restituito contro l'ordine di ritorno"

#: stock/status_codes.py:94
msgid "Sent to customer"
msgstr "Inviato al cliente"

#: stock/status_codes.py:95
msgid "Returned from customer"
msgstr "Restituito dal cliente"

#: templates/403.html:6 templates/403.html:10 templates/403_csrf.html:7
msgid "Permission Denied"
msgstr "Permesso negato"

#: templates/403.html:11
msgid "You do not have permission to view this page."
msgstr "Non ha i permessi per visualizzare la pagina."

#: templates/403_csrf.html:11
msgid "Authentication Failure"
msgstr "Autenticazione Fallita"

#: templates/403_csrf.html:12
msgid "You have been logged out from InvenTree."
msgstr "Sei stato disconnesso da InvenTree."

#: templates/404.html:6 templates/404.html:10
msgid "Page Not Found"
msgstr "Pagina Non Trovata"

#: templates/404.html:11
msgid "The requested page does not exist"
msgstr "La pagina richiesta non esiste"

#: templates/500.html:6 templates/500.html:10
msgid "Internal Server Error"
msgstr "Errore Interno del Server"

#: templates/500.html:11
#, python-format
msgid "The %(inventree_title)s server raised an internal error"
msgstr "Il server %(inventree_title)s ha rilevato un errore interno"

#: templates/500.html:12
msgid "Refer to the error log in the admin interface for further details"
msgstr "Consultare il log degli errori nell'interfaccia di amministrazione per ulteriori dettagli"

#: templates/503.html:11 templates/503.html:15
msgid "Site is in Maintenance"
msgstr "Il sito è in manutenzione"

#: templates/503.html:17
msgid "The site is currently in maintenance and should be up again soon!"
msgstr "Il sito è attualmente in manutenzione e dovrebbe essere online presto!"

#: templates/base.html:51
msgid "Server Restart Required"
msgstr "È necessario riavviare il server"

#: templates/base.html:54
msgid "A configuration option has been changed which requires a server restart"
msgstr "È stata modificata un'impostazione che richiede un riavvio del server"

#: templates/base.html:54 templates/base.html:64
msgid "Contact your system administrator for further information"
msgstr "Contatta l'amministratore per maggiori informazioni"

#: templates/base.html:61
msgid "Pending Database Migrations"
msgstr ""

#: templates/base.html:64
msgid "There are pending database migrations which require attention"
msgstr ""

#: templates/email/build_order_completed.html:9
#: templates/email/canceled_order_assigned.html:9
#: templates/email/new_order_assigned.html:9
#: templates/email/overdue_build_order.html:9
#: templates/email/overdue_purchase_order.html:9
#: templates/email/overdue_return_order.html:9
#: templates/email/overdue_sales_order.html:9
#: templates/email/purchase_order_received.html:9
#: templates/email/return_order_received.html:9
msgid "Click on the following link to view this order"
msgstr "Clicca il seguente link per visualizzare quest'ordine"

#: templates/email/build_order_required_stock.html:7
msgid "Stock is required for the following build order"
msgstr "La giacenza è richiesta per il seguente ordine di produzione"

#: templates/email/build_order_required_stock.html:8
#, python-format
msgid "Build order %(build)s - building %(quantity)s x %(part)s"
msgstr "Ordine di produzione %(build)s - produzione %(quantity)s x %(part)s"

#: templates/email/build_order_required_stock.html:10
msgid "Click on the following link to view this build order"
msgstr "Clicca il seguente link per visualizzare quest'ordine di produzione"

#: templates/email/build_order_required_stock.html:14
msgid "The following parts are low on required stock"
msgstr "I seguenti articoli sono pochi nel magazzino richiesto"

#: templates/email/build_order_required_stock.html:18
msgid "Required Quantity"
msgstr "Quantità richiesta"

#: templates/email/build_order_required_stock.html:38
#: templates/email/low_stock_notification.html:30
msgid "You are receiving this email because you are subscribed to notifications for this part "
msgstr "Stai ricevendo questa email perché sei iscritto alle notifiche per questo articolo "

#: templates/email/low_stock_notification.html:9
#: templates/email/part_event_notification.html:9
msgid "Click on the following link to view this part"
msgstr "Clicca il seguente link per visualizzare questo articolo"

#: templates/email/low_stock_notification.html:18
#: templates/email/part_event_notification.html:19
msgid "Minimum Quantity"
msgstr "Quantità minima"

#: templates/email/part_event_notification.html:32
msgid "You are receiving this email because you are subscribed to notifications for this part or a category that it is part of "
msgstr ""

#: users/admin.py:101
msgid "Users"
msgstr "Utenti"

#: users/admin.py:102
msgid "Select which users are assigned to this group"
msgstr "Selezionare quali utenti sono assegnati a questo gruppo"

#: users/admin.py:137
msgid "Personal info"
msgstr "Informazioni personali"

#: users/admin.py:139
msgid "Permissions"
msgstr "Permessi"

#: users/admin.py:142
msgid "Important dates"
msgstr "Date Importanti"

#: users/authentication.py:30 users/models.py:157
msgid "Token has been revoked"
msgstr ""

#: users/authentication.py:33
msgid "Token has expired"
msgstr ""

#: users/models.py:100
msgid "API Token"
msgstr ""

#: users/models.py:101
msgid "API Tokens"
msgstr ""

#: users/models.py:137
msgid "Token Name"
msgstr ""

#: users/models.py:138
msgid "Custom token name"
msgstr ""

#: users/models.py:144
msgid "Token expiry date"
msgstr ""

#: users/models.py:152
msgid "Last Seen"
msgstr ""

#: users/models.py:153
msgid "Last time the token was used"
msgstr ""

#: users/models.py:157
msgid "Revoked"
msgstr ""

#: users/models.py:235
msgid "Permission set"
msgstr "Impostazione autorizzazioni"

#: users/models.py:244
msgid "Group"
msgstr "Gruppo"

#: users/models.py:248
msgid "View"
msgstr "Visualizza"

#: users/models.py:248
msgid "Permission to view items"
msgstr "Autorizzazione a visualizzare gli articoli"

#: users/models.py:252
msgid "Add"
msgstr "Aggiungi"

#: users/models.py:252
msgid "Permission to add items"
msgstr "Autorizzazione ad aggiungere elementi"

#: users/models.py:256
msgid "Change"
msgstr "Modificare"

#: users/models.py:258
msgid "Permissions to edit items"
msgstr "Permessi per modificare gli elementi"

#: users/models.py:262
msgid "Delete"
msgstr "Elimina"

#: users/models.py:264
msgid "Permission to delete items"
msgstr "Autorizzazione ad eliminare gli elementi"

#: users/models.py:497
msgid "Bot"
msgstr ""

#: users/models.py:498
msgid "Internal"
msgstr ""

#: users/models.py:500
msgid "Guest"
msgstr ""

#: users/models.py:509
msgid "Language"
msgstr ""

#: users/models.py:510
msgid "Preferred language for the user"
msgstr ""

#: users/models.py:515
msgid "Theme"
msgstr ""

#: users/models.py:516
msgid "Settings for the web UI as JSON - do not edit manually!"
msgstr ""

#: users/models.py:521
msgid "Widgets"
msgstr ""

#: users/models.py:523
msgid "Settings for the dashboard widgets as JSON - do not edit manually!"
msgstr ""

#: users/models.py:530
msgid "Display Name"
msgstr ""

#: users/models.py:531
msgid "Chosen display name for the user"
msgstr ""

#: users/models.py:537
msgid "Position"
msgstr ""

#: users/models.py:538
msgid "Main job title or position"
msgstr ""

#: users/models.py:545
msgid "User status message"
msgstr ""

#: users/models.py:552
msgid "User location information"
msgstr ""

#: users/models.py:557
msgid "User is actively using the system"
msgstr ""

#: users/models.py:564
msgid "Preferred contact information for the user"
msgstr ""

#: users/models.py:570
msgid "User Type"
msgstr ""

#: users/models.py:571
msgid "Which type of user is this?"
msgstr ""

#: users/models.py:577
msgid "Organisation"
msgstr ""

#: users/models.py:578
msgid "Users primary organisation/affiliation"
msgstr ""

#: users/models.py:586
msgid "Primary Group"
msgstr ""

#: users/models.py:587
msgid "Primary group for the user"
msgstr ""

#: users/ruleset.py:31
msgid "Admin"
msgstr "Amministratore"

#: users/ruleset.py:34
msgid "Stocktake"
msgstr "Inventario"

#: users/ruleset.py:38
msgid "Purchase Orders"
msgstr "Ordine di acquisto"

#: users/ruleset.py:39
msgid "Sales Orders"
msgstr "Ordini di Vendita"

#: users/ruleset.py:40
msgid "Return Orders"
msgstr "Ordini di reso"

#: users/serializers.py:236
msgid "Username"
msgstr "Nome utente"

#: users/serializers.py:239
msgid "First Name"
msgstr "Nome"

#: users/serializers.py:239
msgid "First name of the user"
msgstr "Nome dell'utente"

#: users/serializers.py:243
msgid "Last Name"
msgstr "Cognome"

#: users/serializers.py:243
msgid "Last name of the user"
msgstr "Cognome dell'utente"

#: users/serializers.py:247
msgid "Email address of the user"
msgstr "Indirizzo email dell'utente"

#: users/serializers.py:323
msgid "Staff"
msgstr "Staff"

#: users/serializers.py:324
msgid "Does this user have staff permissions"
msgstr "Questo utente ha i permessi dello staff"

#: users/serializers.py:329
msgid "Superuser"
msgstr "Superuser"

#: users/serializers.py:329
msgid "Is this user a superuser"
msgstr "Questo utente è un superutente"

#: users/serializers.py:333
msgid "Is this user account active"
msgstr "Questo account utente è attivo"

#: users/serializers.py:345
msgid "Only a superuser can adjust this field"
msgstr ""

#: users/serializers.py:399
msgid "Only staff users can create new users"
msgstr ""

#: users/serializers.py:404
msgid "You do not have permission to create users"
msgstr ""

#: users/serializers.py:425
msgid "Your account has been created."
msgstr "Il tuo account è stato creato."

#: users/serializers.py:427
msgid "Please use the password reset function to login"
msgstr "Si prega di utilizzare la funzione di reimpostazione password per accedere"

#: users/serializers.py:433
msgid "Welcome to InvenTree"
msgstr "Benvenuto in InvenTree"

