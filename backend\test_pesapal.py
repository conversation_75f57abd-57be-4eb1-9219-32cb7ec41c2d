#!/usr/bin/env python
"""
Test script for Pesapal integration
Run this script to test the Pesapal API integration
"""
import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'deepforest_backend.settings')
django.setup()

from payments.pesapal_client import PesapalClient
from payments.pesapal_config import PesapalConfig
from payments.pesapal_utils import generate_order_reference, validate_amount, validate_email, validate_phone
from payments.pesapal_exceptions import PesapalException


def test_config():
    """Test Pesapal configuration"""
    print("Testing Pesapal Configuration...")
    try:
        PesapalConfig.validate_config()
        print("✓ Configuration is valid")
        print(f"  - Base URL: {PesapalConfig.get_base_url()}")
        print(f"  - Sandbox Mode: {PesapalConfig.SANDBOX_MODE}")
        print(f"  - Consumer Key: {PesapalConfig.CONSUMER_KEY[:10]}...")
        return True
    except Exception as e:
        print(f"✗ Configuration error: {e}")
        return False


def test_utilities():
    """Test utility functions"""
    print("\nTesting Utility Functions...")
    
    try:
        # Test amount validation
        amount = validate_amount("1000.50")
        print(f"✓ Amount validation: {amount}")
        
        # Test email validation
        email = validate_email("<EMAIL>")
        print(f"✓ Email validation: {email}")
        
        # Test phone validation
        phone = validate_phone("0700123456")
        print(f"✓ Phone validation: {phone}")
        
        # Test order reference generation
        ref = generate_order_reference(1)
        print(f"✓ Order reference: {ref}")
        
        return True
    except Exception as e:
        print(f"✗ Utility function error: {e}")
        return False


def test_client_initialization():
    """Test Pesapal client initialization"""
    print("\nTesting Pesapal Client...")
    
    try:
        client = PesapalClient()
        print("✓ Pesapal client initialized successfully")
        return True, client
    except Exception as e:
        print(f"✗ Client initialization error: {e}")
        return False, None


def test_payment_initiation(client):
    """Test payment initiation (without actually sending request)"""
    print("\nTesting Payment Initiation...")
    
    try:
        # Test data
        order_data = {
            'amount': '1000.00',
            'description': 'Test Order',
            'reference': generate_order_reference(1),
            'first_name': 'John',
            'last_name': 'Doe',
            'email': '<EMAIL>',
            'phone': '254700123456'
        }
        
        print(f"✓ Test order data prepared:")
        for key, value in order_data.items():
            print(f"  - {key}: {value}")
        
        # Note: We're not actually calling initiate_payment to avoid making real API calls
        print("✓ Payment initiation test data is valid")
        return True
    except Exception as e:
        print(f"✗ Payment initiation error: {e}")
        return False


def main():
    """Run all tests"""
    print("=" * 50)
    print("PESAPAL INTEGRATION TEST")
    print("=" * 50)
    
    tests_passed = 0
    total_tests = 4
    
    # Test configuration
    if test_config():
        tests_passed += 1
    
    # Test utilities
    if test_utilities():
        tests_passed += 1
    
    # Test client initialization
    success, client = test_client_initialization()
    if success:
        tests_passed += 1
        
        # Test payment initiation
        if test_payment_initiation(client):
            tests_passed += 1
    
    # Summary
    print("\n" + "=" * 50)
    print(f"TEST SUMMARY: {tests_passed}/{total_tests} tests passed")
    print("=" * 50)
    
    if tests_passed == total_tests:
        print("✓ All tests passed! Pesapal integration is ready.")
        print("\nNext steps:")
        print("1. Set up your Pesapal merchant account")
        print("2. Update PESAPAL_CONSUMER_KEY and PESAPAL_CONSUMER_SECRET in .env")
        print("3. Test with real transactions in sandbox mode")
        return True
    else:
        print("✗ Some tests failed. Please check the configuration.")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
