# Generated by Django 3.2.22 on 2023-10-09 01:44

from django.db import migrations, models
import report.helpers


class Migration(migrations.Migration):

    dependencies = [
        ('report', '0020_stocklocationreport'),
    ]

    operations = [
        migrations.AddField(
            model_name='billofmaterialsreport',
            name='landscape',
            field=models.BooleanField(default=False, help_text='Render report in landscape orientation', verbose_name='Landscape'),
        ),
        migrations.AddField(
            model_name='billofmaterialsreport',
            name='page_size',
            field=models.CharField(default=report.helpers.report_page_size_default, help_text='Page size for PDF reports', max_length=20, verbose_name='Page Size'),
        ),
        migrations.AddField(
            model_name='buildreport',
            name='landscape',
            field=models.BooleanField(default=False, help_text='Render report in landscape orientation', verbose_name='Landscape'),
        ),
        migrations.AddField(
            model_name='buildreport',
            name='page_size',
            field=models.Char<PERSON>ield(default=report.helpers.report_page_size_default, help_text='Page size for PDF reports', max_length=20, verbose_name='Page Size'),
        ),
        migrations.AddField(
            model_name='purchaseorderreport',
            name='landscape',
            field=models.BooleanField(default=False, help_text='Render report in landscape orientation', verbose_name='Landscape'),
        ),
        migrations.AddField(
            model_name='purchaseorderreport',
            name='page_size',
            field=models.CharField(default=report.helpers.report_page_size_default, help_text='Page size for PDF reports', max_length=20, verbose_name='Page Size'),
        ),
        migrations.AddField(
            model_name='returnorderreport',
            name='landscape',
            field=models.BooleanField(default=False, help_text='Render report in landscape orientation', verbose_name='Landscape'),
        ),
        migrations.AddField(
            model_name='returnorderreport',
            name='page_size',
            field=models.CharField(default=report.helpers.report_page_size_default, help_text='Page size for PDF reports', max_length=20, verbose_name='Page Size'),
        ),
        migrations.AddField(
            model_name='salesorderreport',
            name='landscape',
            field=models.BooleanField(default=False, help_text='Render report in landscape orientation', verbose_name='Landscape'),
        ),
        migrations.AddField(
            model_name='salesorderreport',
            name='page_size',
            field=models.CharField(default=report.helpers.report_page_size_default, help_text='Page size for PDF reports', max_length=20, verbose_name='Page Size'),
        ),
        migrations.AddField(
            model_name='stocklocationreport',
            name='landscape',
            field=models.BooleanField(default=False, help_text='Render report in landscape orientation', verbose_name='Landscape'),
        ),
        migrations.AddField(
            model_name='stocklocationreport',
            name='page_size',
            field=models.CharField(default=report.helpers.report_page_size_default, help_text='Page size for PDF reports', max_length=20, verbose_name='Page Size'),
        ),
        migrations.AddField(
            model_name='testreport',
            name='landscape',
            field=models.BooleanField(default=False, help_text='Render report in landscape orientation', verbose_name='Landscape'),
        ),
        migrations.AddField(
            model_name='testreport',
            name='page_size',
            field=models.CharField(default=report.helpers.report_page_size_default, help_text='Page size for PDF reports', max_length=20, verbose_name='Page Size'),
        ),
    ]
