from django.contrib import admin
from .models import Category, Product, UserProfile, Cart, CartItem, DeliveryZone


@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ['name', 'slug', 'created_at']
    list_filter = ['created_at']
    search_fields = ['name', 'description']
    prepopulated_fields = {'slug': ('name',)}


@admin.register(Product)
class ProductAdmin(admin.ModelAdmin):
    list_display = ['name', 'sku', 'category', 'price_ksh', 'sale_price_ksh', 'on_sale', 'is_active', 'stock_quantity']
    list_filter = ['category', 'on_sale', 'is_active', 'created_at']
    search_fields = ['name', 'sku', 'description']
    list_editable = ['price_ksh', 'sale_price_ksh', 'on_sale', 'is_active', 'stock_quantity']
    readonly_fields = ['created_at', 'updated_at']


@admin.register(UserProfile)
class UserProfileAdmin(admin.ModelAdmin):
    list_display = ['user', 'phone', 'county', 'created_at']
    list_filter = ['county', 'created_at']
    search_fields = ['user__username', 'user__email', 'phone']


@admin.register(Cart)
class CartAdmin(admin.ModelAdmin):
    list_display = ['user', 'total_items', 'total_price_ksh', 'created_at']
    list_filter = ['created_at']
    search_fields = ['user__username']
    readonly_fields = ['total_items', 'total_price_ksh']


class CartItemInline(admin.TabularInline):
    model = CartItem
    extra = 0
    readonly_fields = ['total_price']


@admin.register(CartItem)
class CartItemAdmin(admin.ModelAdmin):
    list_display = ['cart', 'product', 'quantity', 'total_price', 'created_at']
    list_filter = ['created_at']
    search_fields = ['cart__user__username', 'product__name']


@admin.register(DeliveryZone)
class DeliveryZoneAdmin(admin.ModelAdmin):
    list_display = ['county', 'delivery_cost_ksh', 'estimated_days', 'is_active']
    list_filter = ['is_active']
    search_fields = ['county']
    list_editable = ['delivery_cost_ksh', 'estimated_days', 'is_active']
