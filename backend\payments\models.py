"""
Payment models for Pesapal integration
Extends the existing ecommerce models with payment functionality
"""
from django.db import models
from django.contrib.auth.models import User
from django.utils import timezone
from decimal import Decimal

from .pesapal_config import PAYMENT_METHOD_CHOICES, PAYMENT_STATUS_CHOICES, ORDER_STATUS_CHOICES


class Order(models.Model):
    """Order model with payment integration"""
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='orders')
    order_reference = models.CharField(max_length=50, unique=True)
    
    # Order details
    total_amount_ksh = models.DecimalField(max_digits=10, decimal_places=2)
    delivery_cost_ksh = models.DecimalField(max_digits=8, decimal_places=2, default=0)
    delivery_address = models.TextField()
    delivery_county = models.CharField(max_length=50)
    
    # Payment details
    payment_status = models.CharField(max_length=20, choices=PAYMENT_STATUS_CHOICES, default='pending')
    payment_method = models.CharField(max_length=20, choices=PAYMENT_METHOD_CHOICES, blank=True, null=True)
    pesapal_transaction_id = models.CharField(max_length=100, blank=True, null=True)
    
    # Order status
    status = models.CharField(max_length=20, choices=ORDER_STATUS_CHOICES, default='pending')
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    paid_at = models.DateTimeField(blank=True, null=True)
    
    class Meta:
        ordering = ['-created_at']
    
    def __str__(self):
        return f"Order {self.order_reference} - {self.user.username}"
    
    @property
    def total_with_delivery(self):
        """Calculate total amount including delivery"""
        return self.total_amount_ksh + self.delivery_cost_ksh
    
    def mark_as_paid(self):
        """Mark order as paid"""
        self.payment_status = 'completed'
        self.status = 'paid'
        self.paid_at = timezone.now()
        self.save()


class OrderItem(models.Model):
    """Order line items"""
    order = models.ForeignKey(Order, on_delete=models.CASCADE, related_name='items')
    product = models.ForeignKey('ecommerce.Product', on_delete=models.CASCADE)
    quantity = models.PositiveIntegerField()
    unit_price_ksh = models.DecimalField(max_digits=10, decimal_places=2)
    
    def __str__(self):
        return f"{self.quantity} x {self.product.name}"
    
    @property
    def total_price(self):
        """Calculate total price for this item"""
        return self.unit_price_ksh * self.quantity


class Payment(models.Model):
    """Payment tracking model"""
    order = models.ForeignKey(Order, on_delete=models.CASCADE, related_name='payments')
    pesapal_transaction_id = models.CharField(max_length=100, unique=True)
    amount_ksh = models.DecimalField(max_digits=10, decimal_places=2)
    payment_method = models.CharField(max_length=20, choices=PAYMENT_METHOD_CHOICES)
    status = models.CharField(max_length=20, choices=PAYMENT_STATUS_CHOICES, default='pending')
    
    # Pesapal specific fields
    pesapal_merchant_reference = models.CharField(max_length=100)
    pesapal_tracking_id = models.CharField(max_length=100, blank=True, null=True)
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    completed_at = models.DateTimeField(blank=True, null=True)
    
    class Meta:
        ordering = ['-created_at']
    
    def __str__(self):
        return f"Payment {self.pesapal_transaction_id} - {self.status}"
    
    def mark_as_completed(self):
        """Mark payment as completed"""
        self.status = 'completed'
        self.completed_at = timezone.now()
        self.save()
        
        # Update order status
        self.order.mark_as_paid()


class PaymentLog(models.Model):
    """Comprehensive payment activity logging"""
    payment = models.ForeignKey(Payment, on_delete=models.CASCADE, related_name='logs', null=True, blank=True)
    order = models.ForeignKey(Order, on_delete=models.CASCADE, related_name='payment_logs', null=True, blank=True)
    
    # Log details
    action = models.CharField(max_length=50)  # 'initiate', 'callback', 'status_check', etc.
    status = models.CharField(max_length=20)
    message = models.TextField()
    
    # API interaction data
    request_data = models.JSONField(blank=True, null=True)
    response_data = models.JSONField(blank=True, null=True)
    
    # Pesapal specific
    pesapal_transaction_id = models.CharField(max_length=100, blank=True, null=True)
    pesapal_tracking_id = models.CharField(max_length=100, blank=True, null=True)
    
    # Timestamp
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        ordering = ['-created_at']
    
    def __str__(self):
        return f"PaymentLog {self.action} - {self.status} at {self.created_at}"


class PaymentCallback(models.Model):
    """Store all payment callbacks from Pesapal"""
    pesapal_notification_type = models.CharField(max_length=50)
    pesapal_transaction_tracking_id = models.CharField(max_length=100)
    pesapal_merchant_reference = models.CharField(max_length=100)
    
    # Raw callback data
    raw_data = models.JSONField()
    
    # Processing status
    processed = models.BooleanField(default=False)
    processed_at = models.DateTimeField(blank=True, null=True)
    error_message = models.TextField(blank=True, null=True)
    
    # Timestamp
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        ordering = ['-created_at']
    
    def __str__(self):
        return f"Callback {self.pesapal_transaction_tracking_id} - {self.pesapal_notification_type}"
