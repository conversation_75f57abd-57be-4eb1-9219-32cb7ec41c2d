# InvenTree Configuration Template
# Ref: https://docs.inventree.org/en/stable/start/config/
# Note: Environment variables take precedence over values set in this file

# Secret key for backend
# Use the environment variable INVENTREE_SECRET_KEY_FILE
#secret_key_file: '/etc/inventree/secret_key.txt'
# Use the environment variable INVENTREE_OIDC_PRIVATE_KEY or INVENTREE_OIDC_PRIVATE_KEY_FILE
#oidc_private_key: '-----BEGIN PRIVATE KEY-----aaa'
#oidc_private_key_file: '/etc/inventree/oidc.key'

# Database backend selection - Configure backend database settings
# Documentation: https://docs.inventree.org/en/latest/start/config/
# Note: Database configuration options can also be specified from environmental variables,
#       with the prefix INVENTREE_DB_
#       e.g INVENTREE_DB_NAME / INVENTREE_DB_USER / INVENTREE_DB_PASSWORD
# Do not change this section if you are using the package - use `inventree config` instead
# TO MAINTAINERS: Do not change database strings
database:
  # --- Available options: ---
  # ENGINE: Database engine. Selection from:
  #         - mysql
  #         - postgresql
  #         - sqlite3
  # NAME: Database name
  # USER: Database username (if required)
  # PASSWORD: Database password (if required)
  # HOST: Database host address (if required)
  # PORT: Database host port (if required)

# Base URL for the InvenTree server (or use the environment variable INVENTREE_SITE_URL)
# site_url: 'http://localhost:8000'

# Set debug to False to run in production mode, or use the environment variable INVENTREE_DEBUG
debug: False

# Additional debug options
debug_querycount: False
debug_shell: False

# Set to False to disable the admin interface, or use the environment variable INVENTREE_ADMIN_ENABLED
#admin_enabled: True

# Set the admin URL, or use the environment variable INVENTREE_ADMIN_URL
#admin_url: 'admin'

# Configure the system logging level (or use environment variable INVENTREE_LOG_LEVEL)
# Options: DEBUG / INFO / WARNING / ERROR / CRITICAL
log_level: WARNING

# Configure if logs should be output in JSON format
# Use environment variable INVENTREE_JSON_LOG
json_log: False

# Enable database-level logging, or use the environment variable INVENTREE_DB_LOGGING
db_logging: False

# Enable writing a log file, or use the environment variable INVENTREE_WRITE_LOG
write_log: False

# Enable writing logs to the console (stdout), or use the environment variable INVENTREE_CONSOLE_LOG
console_log: True

# Select default system language , or use the environment variable INVENTREE_LANGUAGE
language: en-us

# System time-zone (default is UTC). Reference: https://en.wikipedia.org/wiki/List_of_tz_database_time_zones
timezone: UTC


# Add new user on first startup by either adding values here or from a file
#admin_user: admin
#admin_email: <EMAIL>
#admin_password: inventree
#admin_password_file: '/etc/inventree/admin_password.txt'

# Email backend configuration
# Ref: https://docs.djangoproject.com/en/dev/topics/email/
# Alternatively, these options can all be set using environment variables,
# with the INVENTREE_EMAIL_ prefix:
# e.g. INVENTREE_EMAIL_HOST / INVENTREE_EMAIL_PORT / INVENTREE_EMAIL_USERNAME
# Refer to the InvenTree documentation for more information

email:
  # backend: 'django.core.mail.backends.smtp.EmailBackend'
  host: ''
  port: 25
  username: ''
  password: ''
  sender: ''
  tls: False
  ssl: False

# Set sentry_enabled to True to report errors back to the maintainers
# Set sentry,dsn to your custom DSN if you want to use your own instance for error reporting
sentry_enabled: False
#sentry_sample_rate: 0.1
#sentry_dsn: https://<EMAIL>/custom

# OpenTelemetry tracing/metrics - disabled by default - refer to the documentation for full list of options
# This can be used to send tracing data, logs and metrics to OpenTelemtry compatible backends
tracing:
  enabled: false

# Set this variable to True to enable InvenTree Plugins, or use the environment variable INVENTREE_PLUGINS_ENABLED
plugins_enabled: False
#plugin_noinstall: True
#plugin_file: '/path/to/plugins.txt'
#plugin_dir: '/path/to/plugins/'

# Set this variable to True to enable auto-migrations, or use the environment variable INVENTREE_AUTO_UPDATE
auto_update: False

# Allowed hosts (see ALLOWED_HOSTS in Django settings documentation)
# A list of strings representing the host/domain names that this Django site can serve.
# Default behaviour is to allow all hosts (THIS IS NOT SECURE!)
allowed_hosts:
  - '*'

# Trusted origins (see CSRF_TRUSTED_ORIGINS in Django settings documentation)
# If you are running behind a proxy, you may need to add the proxy address here
# trusted_origins:
#   - 'http://localhost'
#   - 'http://*.localhost'

# Enable Proxy header passthrough
# Override with the environment variable INVENTREE_USE_X_FORWARDED_<HEADER>
# use_x_forwarded_host: true
# use_x_forwarded_port: true
# use_x_forwarded_proto: true

# Cookie settings (nominally the default settings should be fine)
cookie:
  secure: false
  samesite: false

# Cross Origin Resource Sharing (CORS) settings (see https://github.com/adamchainz/django-cors-headers)
cors:
  allow_all: true
  allow_credentials: true

  # whitelist:
  # - https://example.com
  # - https://sub.example.com

  # regex:

# MEDIA_ROOT is the local filesystem location for storing uploaded files
#media_root: '/home/<USER>/data/media'

# STATIC_ROOT is the local filesystem location for storing static files
#static_root: '/home/<USER>/data/static'

# INVENTREE_BACKUP_DIR is the local filesystem location for storing backups
#backup_dir: '/home/<USER>/data/backup'

# Background worker options
background:
  workers: 4
  timeout: 90
  max_attempts: 5

# External cache configuration (refer to the documentation for full list of options)
cache:
  enabled: false
  host: 'inventree-cache'
  port: 6379

# Login configuration
login_confirm_days: 3
login_attempts: 5
login_default_protocol: http

# Remote / proxy login
# These settings can introduce security problems if configured incorrectly. Please read
# https://docs.djangoproject.com/en/4.2/howto/auth-remote-user/ for more details
# The header name should be prefixed by `HTTP`. Please read the docs for more details
# https://docs.djangoproject.com/en/stable/ref/request-response/#django.http.HttpRequest.META
remote_login_enabled: False
remote_login_header: HTTP_REMOTE_USER

# Logout redirect configuration
# This setting may be required if using remote / proxy login to redirect requests
# during the logout process (default is 'index'). Please read the docs for more details
# https://docs.djangoproject.com/en/stable/ref/settings/#logout-redirect-url
#logout_redirect_url: 'index'

# Add SSO login-backends (see examples below)
# social_backends:
#  - 'allauth.socialaccount.providers.google'
#  - 'allauth.socialaccount.providers.github'

# Add specific settings for social account providers (if required)
# Refer to the djngo-allauth documentation for more details:
# https://docs.allauth.org/en/latest/socialaccount/provider_configuration.html
# social_providers:
#   github:
#     VERIFIED_EMAIL: true

# Add LDAP support (refer to the documentation for available options)
# Ref: https://docs.inventree.org/en/stable/start/advanced/#ldap
ldap:
  enabled: false

# Customization options
# Ref: https://docs.inventree.org/en/stable/start/config/#customization-options
# customize:
#   login_message: InvenTree demo instance - <a href='https://inventree.org/demo.html'> Click here for login details</a>
#   navbar_message: <h6>InvenTree demo mode <a href='https://inventree.org/demo.html'><span class='fas fa-info-circle'></span></a></h6>
#   hide_admin_link: true
#   hide_password_reset: true
#   logo: img/custom_logo.png
#   splash: img/custom_splash.jpg
