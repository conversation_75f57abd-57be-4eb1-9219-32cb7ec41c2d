# Generated by Django 3.0.5 on 2020-05-06 23:36

import InvenTree.models
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('stock', '0035_auto_20200502_2308'),
    ]

    operations = [
        migrations.CreateModel(
            name='StockItemAttachment',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('attachment', models.FileField(help_text='Select file to attach', upload_to='attachments')),
                ('comment', models.CharField(help_text='File comment', max_length=100)),
                ('stock_item', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='attachments', to='stock.StockItem')),
            ],
            options={
                'abstract': False,
            },
        ),
    ]
