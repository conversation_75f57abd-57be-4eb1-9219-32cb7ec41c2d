# An example configuration file for running InvenTree container behind an nginx proxy
# While suitable for a simple installation, this file will likely require some modification
# if you are running a more complex setup (e.g behind another proxy, or with HTTPS)

server {

    # Listen for connection on (internal) port 80
    # If you are exposing this server to the internet, you should use HTTPS!
    # In which case, you should also set up a redirect from HTTP to HTTPS, and listen on port 443
    # See the Nginx documentation for more details
    listen 80;

    real_ip_header proxy_protocol;

    location / {

        proxy_set_header      Host              $http_host;
        proxy_set_header      X-Forwarded-By    $server_addr:$server_port;
        proxy_set_header      X-Forwarded-For   $remote_addr;
        proxy_set_header      X-Forwarded-Proto $scheme;
        proxy_set_header      X-Real-IP         $remote_addr;
        proxy_set_header      CLIENT_IP         $remote_addr;

        proxy_pass_request_headers on;

        proxy_redirect off;

        client_max_body_size 100M;

        proxy_buffering off;
        proxy_request_buffering off;

        # Do not touch this unless you have a specific reason - this and the docker-compose need to match
        proxy_pass http://inventree-server:8000;
    }

    # Redirect any requests for static files
    location /static/ {
        alias /var/www/static/;
        autoindex on;

        # Caching settings
        expires 30d;
        add_header Pragma public;
        add_header Cache-Control "public";
    }

    # Redirect any requests for media files
    location /media/ {
        alias /var/www/media/;

        # Media files require user authentication
        auth_request /auth;

        # Content header to force download
        add_header Content-disposition "attachment";
    }

    # Use the 'user' API endpoint for auth
    location /auth {
        internal;

        proxy_pass http://inventree-server:8000/auth/;

        proxy_pass_request_body off;
        proxy_set_header Content-Length "";
        proxy_set_header X-Original-URI $request_uri;
    }

}
