# DeepForest Hardware Emporium - Django Backend

## Phase 2.1: Django Backend Foundation & Core E-commerce API

This is the Django backend foundation for DeepForest Hardware Emporium, implementing core e-commerce functionality with Django Rest Framework.

## Features Implemented

### ✅ Core Models
- **Category**: Product categories with slugs and descriptions
- **Product**: E-commerce products with KSH pricing, stock tracking, and Kenyan market features
- **UserProfile**: Extended user profiles with Kenyan-specific fields (phone, county, delivery address)
- **Cart & CartItem**: Shopping cart functionality for authenticated users
- **DeliveryZone**: Kenyan county-based delivery cost calculation

### ✅ API Endpoints
- **Categories API**: `/api/categories/` - List and manage product categories
- **Products API**: `/api/products/` - Product CRUD with filtering, search, and pagination
- **User Profile API**: `/api/user/profile/` - User profile management
- **Cart API**: `/api/cart/` - Shopping cart operations
- **Authentication API**: `/api/auth/` - JWT-based authentication
- **Delivery Zones API**: `/api/delivery-zones/` - County delivery information

### ✅ Kenyan Market Features
- All prices stored and returned in KSH (Kenyan Shillings)
- Weight measurements in KG (Kilograms)
- Kenyan county-based delivery zones
- Phone number fields for local contact preferences

### ✅ Admin Interface
- Customized Django admin for all models
- Bulk editing capabilities for products
- User management and permissions

## Setup Instructions

### 1. Environment Setup
```bash
# Create virtual environment
python -m venv venv

# Activate virtual environment (Windows)
.\venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt
```

### 2. Database Setup
```bash
# Run migrations
python manage.py migrate

# Populate initial data
python manage.py populate_initial_data

# Create superuser (optional)
python manage.py createsuperuser
```

### 3. Run Development Server
```bash
python manage.py runserver
```

The API will be available at: `http://127.0.0.1:8000/api/`
Django Admin will be available at: `http://127.0.0.1:8000/admin/`

### 4. Test API Endpoints
```bash
# Install requests for testing
pip install requests

# Run API tests
python test_api.py
```

## API Documentation

### Categories
- `GET /api/categories/` - List all categories
- `POST /api/categories/` - Create new category
- `GET /api/categories/{slug}/` - Get category by slug

### Products
- `GET /api/products/` - List products with filtering
  - Query parameters: `category`, `search`, `min_price`, `max_price`, `in_stock`
- `POST /api/products/` - Create new product
- `GET /api/products/{id}/` - Get product details

### Authentication
- `POST /api/auth/register/` - Register new user
- `POST /api/auth/login/` - Login (get JWT tokens)
- `POST /api/auth/refresh/` - Refresh JWT token
- `GET /api/auth/user/` - Get current user info

### Cart Operations
- `GET /api/cart/` - Get user's cart
- `POST /api/cart/add/` - Add item to cart
- `PUT /api/cart/items/{id}/` - Update cart item quantity
- `DELETE /api/cart/items/{id}/` - Remove item from cart
- `DELETE /api/cart/clear/` - Clear entire cart

### User Profile
- `GET /api/user/profile/` - Get user profile
- `PUT /api/user/profile/` - Update user profile

### Delivery Zones
- `GET /api/delivery-zones/` - List all delivery zones

## Configuration

### Environment Variables (.env)
```
SECRET_KEY=your-secret-key-here
DEBUG=True
ALLOWED_HOSTS=localhost,127.0.0.1

# Database (SQLite for development)
DB_ENGINE=django.db.backends.sqlite3
DB_NAME=db.sqlite3

# CORS for React frontend
CORS_ALLOWED_ORIGINS=http://localhost:3000,http://127.0.0.1:3000,http://localhost:5173

# JWT Configuration
JWT_ACCESS_TOKEN_LIFETIME=60
JWT_REFRESH_TOKEN_LIFETIME=1440
```

## Database Schema

### Key Relationships
- `Product` belongs to `Category` (ForeignKey)
- `UserProfile` extends `User` (OneToOne)
- `Cart` belongs to `User` (OneToOne)
- `CartItem` links `Cart` and `Product` (ForeignKey)

### Sample Data
The system comes pre-populated with:
- 6 product categories (Doors & Windows, Paints, Cement, Hardware, Roofing, Electrical)
- 15 sample products with realistic KSH pricing
- 10 Kenyan delivery zones with cost estimates

## Next Steps (Phase 2.2)

This foundation is ready for Phase 2.2 implementation:
- Pesapal payment integration
- Order processing with payment tracking
- Enhanced checkout flow
- Payment webhook handling

## Technology Stack

- **Django 5.2.3** - Web framework
- **Django Rest Framework 3.16.0** - API framework
- **django-cors-headers** - CORS handling for React frontend
- **djangorestframework-simplejwt** - JWT authentication
- **django-filter** - API filtering capabilities
- **SQLite** - Development database (PostgreSQL ready for production)

## File Structure
```
backend/
├── deepforest_backend/     # Main Django project
├── ecommerce/              # E-commerce app
├── accounts/               # Authentication app
├── requirements.txt        # Python dependencies
├── test_api.py            # API testing script
└── README.md              # This file
```
