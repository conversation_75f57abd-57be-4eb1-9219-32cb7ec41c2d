---
title: InvenTree API Schema
---

The InvenTree API is implemented using the [Django REST framework](https://www.django-rest-framework.org).
The API schema as documented below is generated using the [drf-spectactular](https://github.com/tfranzel/drf-spectacular/) extension.

## API Version

This documentation is for API version: `339`

!!! tip "API Schema History"
    We track API schema changes, and provide a snapshot of each API schema version in the [API schema repository](https://github.com/inventree/schema/).

## API Schema File

The API schema file is available for download, and can be used for generating client libraries, or for testing API endpoints.

## API Schema Documentation

API schema documentation is split into the following categories:

| Category | Description |
| --- | --- |
| [Authorization and Authentication](./schema/auth.md) | Authorization and Authentication |
| [Background Task Management](./schema/background-task.md) | Background Task Management |
| [Barcode Scanning](./schema/barcode.md) | Barcode Scanning |
| [Bill of Materials](./schema/bom.md) | Bill of Materials |
| [Build Order Management](./schema/build.md) | Build Order Management |
| [Company Management](./schema/company.md) | Company Management |
| [Label Printing](./schema/label.md) | Label Printing |
| [External Machine Management](./schema/machine.md) | External Machine Management |
| [External Order Management](./schema/order.md) | External Order Management |
| [Parts and Part Categories](./schema/part.md) | Parts and Part Categories |
| [Plugin Functionality](./schema/plugins.md) | Plugin Functionality |
| [Report Generation](./schema/report.md) | Report Generation |
| [Settings Management](./schema/settings.md) | Settings Management |
| [Stock and Stock Locations](./schema/stock.md) | Stock and Stock Locations |
| [User Management](./schema/user.md) | User Management |
| [General](./schema/general.md) | General API endpoints |
