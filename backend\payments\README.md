# Pesapal Payment Integration

This module provides complete Pesapal payment integration for the DeepForest Hardware Emporium e-commerce platform, supporting M-Pesa, Visa, Mastercard, and other Kenyan payment methods.

## Features

- **Complete Pesapal API Integration**: OAuth 1.0 authentication and payment processing
- **Multiple Payment Methods**: M-Pesa, Visa, Mastercard, Airtel Money, Equitel, Bank Transfer
- **Kenyan Market Focus**: KSH currency, Kenyan phone number validation, county-based delivery
- **Real-time Payment Tracking**: IPN callbacks and status monitoring
- **Comprehensive Logging**: Full audit trail of all payment activities
- **Security**: Secure handling of payment data and API credentials

## File Structure

```
payments/
├── __init__.py
├── apps.py
├── models.py                    # Payment, Order, and logging models
├── admin.py                     # Django admin interface
├── urls.py                      # API endpoints
├── views.py                     # DRF API views
├── pesapal_client.py           # Main Pesapal API client
├── pesapal_config.py           # Configuration management
├── pesapal_utils.py            # Utility functions
├── pesapal_webhooks.py         # IPN handlers
├── pesapal_exceptions.py       # Custom exceptions
├── pesapal_serializers.py     # DRF serializers
└── README.md                   # This file
```

## API Endpoints

### Payment Endpoints
- `POST /api/payments/initiate/` - Initiate payment
- `GET/POST /api/payments/callback/` - Payment callback handler
- `GET/POST /api/payments/ipn/` - Pesapal IPN endpoint
- `GET /api/payments/status/<transaction_id>/` - Check payment status

### Order Endpoints
- `GET /api/payments/orders/` - Get user orders
- `GET /api/payments/orders/<order_id>/` - Get order details
- `GET /api/payments/checkout/summary/` - Get checkout summary

## Models

### Order
- Stores order information with payment details
- Links to user and order items
- Tracks payment status and order status

### OrderItem
- Individual items within an order
- Links to products with quantity and pricing

### Payment
- Tracks payment transactions
- Links to orders with Pesapal transaction details

### PaymentLog
- Comprehensive logging of all payment activities
- Stores API requests and responses

### PaymentCallback
- Stores all IPN callbacks from Pesapal
- Tracks processing status

## Configuration

### Environment Variables

```bash
# Pesapal Configuration
PESAPAL_SANDBOX_MODE=True
PESAPAL_CONSUMER_KEY=your_pesapal_consumer_key
PESAPAL_CONSUMER_SECRET=your_pesapal_consumer_secret
PESAPAL_CALLBACK_URL=http://localhost:3000/payment-success
PESAPAL_IPN_URL=http://localhost:8000/api/payments/ipn/
```

### Django Settings

Add to `INSTALLED_APPS`:
```python
INSTALLED_APPS = [
    # ... other apps
    'payments',
]
```

Add to URL configuration:
```python
urlpatterns = [
    # ... other URLs
    path('api/payments/', include('payments.urls')),
]
```

## Usage

### 1. Payment Initiation

```python
# Frontend (React)
const paymentData = {
    delivery_address: "123 Main St, Nairobi",
    delivery_county: "nairobi",
    payment_method: "mpesa",
    phone: "254700123456"
};

const response = await fetch('/api/payments/initiate/', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
    },
    body: JSON.stringify(paymentData),
});

const data = await response.json();
if (data.success) {
    // Redirect to Pesapal payment page
    window.location.href = data.payment_url;
}
```

### 2. Payment Status Checking

```python
# Backend
from payments.pesapal_webhooks import PesapalWebhookHandler

webhook_handler = PesapalWebhookHandler()
result = webhook_handler.manual_status_check('DF000001123456789')
```

### 3. IPN Callback Handling

The system automatically handles Pesapal IPN callbacks at `/api/payments/ipn/`. When a payment status changes, Pesapal sends a notification to this endpoint, which:

1. Validates the callback data
2. Queries Pesapal for the current payment status
3. Updates the order and payment records
4. Logs all activities

## Payment Flow

1. **Order Creation**: User completes checkout form with delivery information
2. **Payment Initiation**: System creates order and generates Pesapal payment URL
3. **Payment Processing**: User is redirected to Pesapal for payment
4. **Callback Handling**: Pesapal sends IPN notifications for status changes
5. **Order Completion**: System updates order status based on payment result

## Testing

Run the test script to verify integration:

```bash
cd backend
python test_pesapal.py
```

This will test:
- Configuration validation
- Utility functions
- Client initialization
- Payment data preparation

## Security Considerations

1. **API Credentials**: Store Pesapal credentials securely in environment variables
2. **HTTPS**: Use HTTPS for all payment-related endpoints in production
3. **Callback Verification**: All IPN callbacks are validated before processing
4. **Data Encryption**: Sensitive payment data is handled securely
5. **Audit Trail**: Complete logging of all payment activities

## Error Handling

The system includes comprehensive error handling:

- **PesapalConfigurationError**: Invalid or missing configuration
- **PesapalAuthenticationError**: OAuth authentication failures
- **PesapalAPIError**: API communication errors
- **PesapalValidationError**: Data validation failures
- **PesapalPaymentError**: Payment processing errors

## Monitoring and Logging

All payment activities are logged in the `PaymentLog` model:
- Payment initiations
- Status checks
- IPN callbacks
- Error conditions

Access logs through Django admin or API endpoints for monitoring and debugging.

## Production Deployment

1. Set `PESAPAL_SANDBOX_MODE=False`
2. Update to live Pesapal credentials
3. Configure proper callback URLs
4. Set up monitoring and alerting
5. Test thoroughly with small transactions

## Support

For issues with the payment integration:
1. Check the PaymentLog model for detailed error information
2. Verify Pesapal configuration and credentials
3. Test with the provided test script
4. Contact Pesapal support for API-related issues
