msgid ""
msgstr ""
"Project-Id-Version: inventree\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-09 19:12+0000\n"
"PO-Revision-Date: 2025-05-09 19:15\n"
"Last-Translator: \n"
"Language-Team: Spanish\n"
"Language: es_ES\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Crowdin-Project: inventree\n"
"X-Crowdin-Project-ID: 452300\n"
"X-Crowdin-Language: es-ES\n"
"X-Crowdin-File: /src/backend/InvenTree/locale/en/LC_MESSAGES/django.po\n"
"X-Crowdin-File-ID: 250\n"

#: InvenTree/AllUserRequire2FAMiddleware.py:42
msgid "You must enable two-factor authentication before doing anything else."
msgstr "Debe habilitar la autenticación de doble factor antes de continuar."

#: InvenTree/api.py:355
msgid "API endpoint not found"
msgstr "endpoint API no encontrado"

#: InvenTree/api.py:432
msgid "List of items or filters must be provided for bulk operation"
msgstr "Lista de artículos o filtros deben ser proporcionados para la operación en bloque"

#: InvenTree/api.py:439
msgid "Items must be provided as a list"
msgstr "Los artículos deben ser proporcionados como una lista"

#: InvenTree/api.py:447
msgid "Invalid items list provided"
msgstr "Lista de artículos no válida"

#: InvenTree/api.py:453
msgid "Filters must be provided as a dict"
msgstr ""

#: InvenTree/api.py:460
msgid "Invalid filters provided"
msgstr "Filtros proporcionados inválidos"

#: InvenTree/api.py:465
msgid "All filter must only be used with true"
msgstr ""

#: InvenTree/api.py:470
msgid "No items match the provided criteria"
msgstr "Ningún artículo coincide con el criterio proporcionado"

#: InvenTree/api.py:713
msgid "User does not have permission to view this model"
msgstr "El usuario no tiene permiso para ver este modelo"

#: InvenTree/auth_overrides.py:57
msgid "Email (again)"
msgstr "Email (de nuevo)"

#: InvenTree/auth_overrides.py:61
msgid "Email address confirmation"
msgstr "Confirmación de dirección de email"

#: InvenTree/auth_overrides.py:84
msgid "You must type the same email each time."
msgstr "Debe escribir el mismo correo electrónico cada vez."

#: InvenTree/auth_overrides.py:126 InvenTree/auth_overrides.py:134
msgid "The provided primary email address is not valid."
msgstr "La dirección de correo electrónico principal proporcionada no es válida."

#: InvenTree/auth_overrides.py:140
msgid "The provided email domain is not approved."
msgstr "El dominio de correo electrónico proporcionado no está aprobado."

#: InvenTree/conversion.py:162
#, python-brace-format
msgid "Invalid unit provided ({unit})"
msgstr "Unidad proporcionada no válida ({unit})"

#: InvenTree/conversion.py:179
msgid "No value provided"
msgstr "Ningún valor proporcionado"

#: InvenTree/conversion.py:206
#, python-brace-format
msgid "Could not convert {original} to {unit}"
msgstr "No se pudo convertir {original} a {unit}"

#: InvenTree/conversion.py:208 InvenTree/conversion.py:222
#: InvenTree/helpers.py:548 order/models.py:707 order/models.py:975
msgid "Invalid quantity provided"
msgstr "Cantidad proporcionada no válida"

#: InvenTree/exceptions.py:109
msgid "Error details can be found in the admin panel"
msgstr "Detalles del error pueden encontrarse en el panel de administración"

#: InvenTree/fields.py:138
msgid "Enter date"
msgstr "Ingrese la fecha"

#: InvenTree/fields.py:161
msgid "Invalid decimal value"
msgstr "Número decimal no válido"

#: InvenTree/fields.py:210 InvenTree/models.py:887 build/serializers.py:526
#: build/serializers.py:597 company/models.py:834 order/models.py:1579
#: part/models.py:3366
#: report/templates/report/inventree_build_order_report.html:172
#: stock/models.py:2708 stock/models.py:2832 stock/serializers.py:756
#: stock/serializers.py:918 stock/serializers.py:1048 stock/serializers.py:1100
#: stock/serializers.py:1419 stock/serializers.py:1508
#: stock/serializers.py:1684
msgid "Notes"
msgstr "Notas"

#: InvenTree/format.py:166
#, python-brace-format
msgid "Value '{name}' does not appear in pattern format"
msgstr "El valor '{name}' no aparece en formato de patrón"

#: InvenTree/format.py:177
msgid "Provided value does not match required pattern: "
msgstr "El valor proporcionado no coincide con el patrón requerido: "

#: InvenTree/helpers.py:552
msgid "Cannot serialize more than 1000 items at once"
msgstr "No se puede serializar más de 1000 elementos a la vez"

#: InvenTree/helpers.py:558
msgid "Empty serial number string"
msgstr "No se ha proporcionado un número de serie"

#: InvenTree/helpers.py:587
msgid "Duplicate serial"
msgstr "Serie duplicada"

#: InvenTree/helpers.py:619 InvenTree/helpers.py:662 InvenTree/helpers.py:680
#: InvenTree/helpers.py:687 InvenTree/helpers.py:706
#, python-brace-format
msgid "Invalid group: {group}"
msgstr "Grupo no válido {group}"

#: InvenTree/helpers.py:650
#, python-brace-format
msgid "Group range {group} exceeds allowed quantity ({expected_quantity})"
msgstr "Rango del grupo {group} supera la cantidad permitida ({expected_quantity})"

#: InvenTree/helpers.py:716
msgid "No serial numbers found"
msgstr "Numeros de serie no encontrados"

#: InvenTree/helpers.py:721
msgid "Number of unique serial numbers ({len(serials)}) must match quantity ({expected_quantity})"
msgstr "Los números de serie únicos ({len(serials)}) debe coincidir con la cantidad ({expected_quantity})"

#: InvenTree/helpers.py:840
msgid "Remove HTML tags from this value"
msgstr "Eliminar etiquetas HTML de este valor"

#: InvenTree/helpers.py:919
msgid "Data contains prohibited markdown content"
msgstr "Los datos contienen contenido de marcado prohibido"

#: InvenTree/helpers_model.py:131
msgid "Connection error"
msgstr "Error de conexión"

#: InvenTree/helpers_model.py:136 InvenTree/helpers_model.py:143
msgid "Server responded with invalid status code"
msgstr "El servidor respondió con código de estado no válido"

#: InvenTree/helpers_model.py:139
msgid "Exception occurred"
msgstr "Se ha producido una excepción"

#: InvenTree/helpers_model.py:149
msgid "Server responded with invalid Content-Length value"
msgstr "El servidor respondió con un valor de longitud de contenido inválido"

#: InvenTree/helpers_model.py:152
msgid "Image size is too large"
msgstr "El tamaño de la imagen es demasiado grande"

#: InvenTree/helpers_model.py:164
msgid "Image download exceeded maximum size"
msgstr "La descarga de imagen excedió el tamaño máximo"

#: InvenTree/helpers_model.py:169
msgid "Remote server returned empty response"
msgstr "El servidor remoto devolvió una respuesta vacía"

#: InvenTree/helpers_model.py:177
msgid "Supplied URL is not a valid image file"
msgstr "La URL proporcionada no es un archivo de imagen válido"

#: InvenTree/locales.py:20
msgid "Arabic"
msgstr "Árabe"

#: InvenTree/locales.py:21
msgid "Bulgarian"
msgstr "Búlgaro"

#: InvenTree/locales.py:22
msgid "Czech"
msgstr "Checo"

#: InvenTree/locales.py:23
msgid "Danish"
msgstr "Danés"

#: InvenTree/locales.py:24
msgid "German"
msgstr "Alemán"

#: InvenTree/locales.py:25
msgid "Greek"
msgstr "Griego"

#: InvenTree/locales.py:26
msgid "English"
msgstr "Inglés"

#: InvenTree/locales.py:27
msgid "Spanish"
msgstr "Español"

#: InvenTree/locales.py:28
msgid "Spanish (Mexican)"
msgstr "Español (México)"

#: InvenTree/locales.py:29
msgid "Estonian"
msgstr "Estoniano"

#: InvenTree/locales.py:30
msgid "Farsi / Persian"
msgstr "Farsi / Persa"

#: InvenTree/locales.py:31
msgid "Finnish"
msgstr "Finlandés"

#: InvenTree/locales.py:32
msgid "French"
msgstr "Francés"

#: InvenTree/locales.py:33
msgid "Hebrew"
msgstr "Hebreo"

#: InvenTree/locales.py:34
msgid "Hindi"
msgstr "Hindú"

#: InvenTree/locales.py:35
msgid "Hungarian"
msgstr "Húngaro"

#: InvenTree/locales.py:36
msgid "Italian"
msgstr "Italiano"

#: InvenTree/locales.py:37
msgid "Japanese"
msgstr "Japonés"

#: InvenTree/locales.py:38
msgid "Korean"
msgstr "Coreano"

#: InvenTree/locales.py:39
msgid "Lithuanian"
msgstr "Lituano"

#: InvenTree/locales.py:40
msgid "Latvian"
msgstr "Letón"

#: InvenTree/locales.py:41
msgid "Dutch"
msgstr "Holandés"

#: InvenTree/locales.py:42
msgid "Norwegian"
msgstr "Noruego"

#: InvenTree/locales.py:43
msgid "Polish"
msgstr "Polaco"

#: InvenTree/locales.py:44
msgid "Portuguese"
msgstr "Portugués"

#: InvenTree/locales.py:45
msgid "Portuguese (Brazilian)"
msgstr "Portugués (Brasileño)"

#: InvenTree/locales.py:46
msgid "Romanian"
msgstr "Rumano"

#: InvenTree/locales.py:47
msgid "Russian"
msgstr "Ruso"

#: InvenTree/locales.py:48
msgid "Slovak"
msgstr "Eslovaco"

#: InvenTree/locales.py:49
msgid "Slovenian"
msgstr "Esloveno"

#: InvenTree/locales.py:50
msgid "Serbian"
msgstr "Serbio"

#: InvenTree/locales.py:51
msgid "Swedish"
msgstr "Sueco"

#: InvenTree/locales.py:52
msgid "Thai"
msgstr "Tailandés"

#: InvenTree/locales.py:53
msgid "Turkish"
msgstr "Turco"

#: InvenTree/locales.py:54
msgid "Ukrainian"
msgstr "Ucraniano"

#: InvenTree/locales.py:55
msgid "Vietnamese"
msgstr "Vietnamita"

#: InvenTree/locales.py:56
msgid "Chinese (Simplified)"
msgstr "Chino (Simplificado)"

#: InvenTree/locales.py:57
msgid "Chinese (Traditional)"
msgstr "Chino (Tradicional)"

#: InvenTree/magic_login.py:31
msgid "Log in to the app"
msgstr "Iniciar sesión en la aplicación"

#: InvenTree/magic_login.py:41 company/models.py:136 users/serializers.py:247
msgid "Email"
msgstr "Correo electrónico"

#: InvenTree/models.py:107
msgid "Error running plugin validation"
msgstr "Error al ejecutar la validación del plug-in"

#: InvenTree/models.py:184
msgid "Metadata must be a python dict object"
msgstr "Los metadatos deben ser un objeto diccionario de python"

#: InvenTree/models.py:190
msgid "Plugin Metadata"
msgstr "Metadatos del complemento"

#: InvenTree/models.py:191
msgid "JSON metadata field, for use by external plugins"
msgstr "Campo de metadatos JSON, para uso por complementos externos"

#: InvenTree/models.py:374
msgid "Improperly formatted pattern"
msgstr "Patrón con formato incorrecto"

#: InvenTree/models.py:381
msgid "Unknown format key specified"
msgstr "Clave de formato especificado desconocida"

#: InvenTree/models.py:387
msgid "Missing required format key"
msgstr "Falta la clave de formato necesaria"

#: InvenTree/models.py:398
msgid "Reference field cannot be empty"
msgstr "El campo de servidor no puede estar vacío"

#: InvenTree/models.py:406
msgid "Reference must match required pattern"
msgstr "La referencia debe coincidir con la expresión regular {pattern}"

#: InvenTree/models.py:437
msgid "Reference number is too large"
msgstr "El número de referencia es demasiado grande"

#: InvenTree/models.py:688
msgid "Duplicate names cannot exist under the same parent"
msgstr "Los nombres duplicados no pueden existir bajo el mismo padre"

#: InvenTree/models.py:705
msgid "Invalid choice"
msgstr "Selección no válida"

#: InvenTree/models.py:734 common/models.py:1325 common/models.py:1752
#: common/models.py:2011 common/models.py:2136 common/serializers.py:488
#: company/models.py:593 generic/states/serializers.py:20 machine/models.py:24
#: part/models.py:1039 part/models.py:3837 plugin/models.py:53
#: report/models.py:203 stock/models.py:83
msgid "Name"
msgstr "Nombre"

#: InvenTree/models.py:740 build/models.py:242 common/models.py:123
#: common/models.py:2143 common/models.py:2256 company/models.py:521
#: company/models.py:825 order/models.py:429 order/models.py:1615
#: part/models.py:1062 part/models.py:3852 report/models.py:209
#: report/models.py:714 report/models.py:740
#: report/templates/report/inventree_build_order_report.html:117
#: stock/models.py:89
msgid "Description"
msgstr "Descripción"

#: InvenTree/models.py:741 stock/models.py:90
msgid "Description (optional)"
msgstr "Descripción (opcional)"

#: InvenTree/models.py:756 common/models.py:2309
msgid "Path"
msgstr "Ruta"

#: InvenTree/models.py:887
msgid "Markdown notes (optional)"
msgstr "Notas de Markdown (opcional)"

#: InvenTree/models.py:918
msgid "Barcode Data"
msgstr "Datos de código de barras"

#: InvenTree/models.py:919
msgid "Third party barcode data"
msgstr "Datos de código de barras de terceros"

#: InvenTree/models.py:925
msgid "Barcode Hash"
msgstr "Hash del Código de barras"

#: InvenTree/models.py:926
msgid "Unique hash of barcode data"
msgstr "Hash único de datos de código de barras"

#: InvenTree/models.py:1003
msgid "Existing barcode found"
msgstr "Código de barras existente encontrado"

#: InvenTree/models.py:1084
msgid "Task Failure"
msgstr "Fallo en la tarea"

#: InvenTree/models.py:1085
#, python-brace-format
msgid "Background worker task '{f}' failed after {n} attempts"
msgstr "La tarea en segundo plano '{f}' falló después de {n} intentos"

#: InvenTree/models.py:1112
msgid "Server Error"
msgstr "Error de servidor"

#: InvenTree/models.py:1113
msgid "An error has been logged by the server."
msgstr "Se ha registrado un error por el servidor."

#: InvenTree/serializers.py:69 part/models.py:4455
msgid "Must be a valid number"
msgstr "Debe ser un número válido"

#: InvenTree/serializers.py:111 company/models.py:187 part/models.py:3184
msgid "Currency"
msgstr "Moneda"

#: InvenTree/serializers.py:114 part/serializers.py:1335
msgid "Select currency from available options"
msgstr "Seleccionar moneda de las opciones disponibles"

#: InvenTree/serializers.py:466
msgid "Invalid value"
msgstr "Valor inválido"

#: InvenTree/serializers.py:503
msgid "Remote Image"
msgstr "Imagen remota"

#: InvenTree/serializers.py:504
msgid "URL of remote image file"
msgstr "URL de imagen remota"

#: InvenTree/serializers.py:522
msgid "Downloading images from remote URL is not enabled"
msgstr "La descarga de imágenes desde la URL remota no está habilitada"

#: InvenTree/serializers.py:529
msgid "Failed to download image from remote URL"
msgstr "Error al descargar la imagen desde la URL remota"

#: InvenTree/validators.py:30
msgid "Invalid physical unit"
msgstr "Unidad física inválida"

#: InvenTree/validators.py:36
msgid "Not a valid currency code"
msgstr "No es un código de moneda válido"

#: InvenTree/validators.py:116 InvenTree/validators.py:132
msgid "Overage value must not be negative"
msgstr "El valor excedente no debe ser negativo"

#: InvenTree/validators.py:134
msgid "Overage must not exceed 100%"
msgstr "El excedente no debe superar el 100%"

#: InvenTree/validators.py:140
msgid "Invalid value for overage"
msgstr "Valor no válido para sobrecarga"

#: build/api.py:39 order/api.py:104 order/api.py:263 order/serializers.py:128
msgid "Order Status"
msgstr "Estado del pedido"

#: build/api.py:65 build/models.py:254
msgid "Parent Build"
msgstr "Construcción o Armado Superior"

#: build/api.py:69 build/api.py:768 order/api.py:512 order/api.py:732
#: order/api.py:1106 order/api.py:1334 stock/api.py:522
msgid "Include Variants"
msgstr "Incluye Variantes"

#: build/api.py:85 build/api.py:470 build/api.py:782 build/models.py:260
#: build/serializers.py:1254 build/serializers.py:1376
#: build/serializers.py:1427 company/models.py:1044 company/serializers.py:432
#: order/api.py:291 order/api.py:295 order/api.py:880 order/api.py:1119
#: order/api.py:1122 order/models.py:1704 order/models.py:1863
#: order/models.py:1864 part/api.py:1448 part/api.py:1451 part/api.py:1517
#: part/api.py:1827 part/models.py:457 part/models.py:3195 part/models.py:3339
#: part/models.py:3487 part/models.py:3508 part/models.py:3530
#: part/models.py:3666 part/models.py:4027 part/models.py:4299
#: part/models.py:4665 part/serializers.py:1255 part/serializers.py:1924
#: report/templates/report/inventree_bill_of_materials_report.html:110
#: report/templates/report/inventree_bill_of_materials_report.html:137
#: report/templates/report/inventree_build_order_report.html:109
#: report/templates/report/inventree_purchase_order_report.html:27
#: report/templates/report/inventree_return_order_report.html:24
#: report/templates/report/inventree_sales_order_report.html:27
#: report/templates/report/inventree_sales_order_shipment_report.html:28
#: report/templates/report/inventree_stock_location_report.html:102
#: stock/api.py:535 stock/serializers.py:119 stock/serializers.py:171
#: stock/serializers.py:481 stock/serializers.py:643 stock/serializers.py:948
#: templates/email/build_order_completed.html:17
#: templates/email/build_order_required_stock.html:17
#: templates/email/low_stock_notification.html:15
#: templates/email/overdue_build_order.html:16
#: templates/email/part_event_notification.html:15
msgid "Part"
msgstr "Parte"

#: build/api.py:105 build/api.py:108 part/api.py:1531 part/models.py:1080
#: part/models.py:3558 part/models.py:4136 part/serializers.py:1265
#: stock/api.py:818
msgid "Category"
msgstr "Categoría"

#: build/api.py:116 build/api.py:120
msgid "Ancestor Build"
msgstr ""

#: build/api.py:137 order/api.py:122
msgid "Assigned to me"
msgstr "Asignado a mí"

#: build/api.py:152
#: report/templates/report/inventree_build_order_report.html:152
msgid "Issued By"
msgstr "Emitido por"

#: build/api.py:167
msgid "Assigned To"
msgstr "Asignadas a"

#: build/api.py:202
msgid "Created before"
msgstr "Creado antes"

#: build/api.py:206
msgid "Created after"
msgstr "Creado despues"

#: build/api.py:210
msgid "Has start date"
msgstr "Tiene fecha inicial"

#: build/api.py:218
msgid "Start date before"
msgstr "Fecha de inicio anterior"

#: build/api.py:222
msgid "Start date after"
msgstr "Fecha de inicio después"

#: build/api.py:226
msgid "Has target date"
msgstr "Tiene fecha límite"

#: build/api.py:234
msgid "Target date before"
msgstr "Fecha objetivo antes"

#: build/api.py:238
msgid "Target date after"
msgstr "Fecha objetivo después"

#: build/api.py:242
msgid "Completed before"
msgstr "Completado antes"

#: build/api.py:246
msgid "Completed after"
msgstr "Completado después"

#: build/api.py:249 order/api.py:219
msgid "Min Date"
msgstr "Fecha Mínima"

#: build/api.py:272 order/api.py:238
msgid "Max Date"
msgstr "Fecha Máxima"

#: build/api.py:297 build/api.py:300 part/api.py:224
msgid "Exclude Tree"
msgstr "Excluir Árbol"

#: build/api.py:409
msgid "Build must be cancelled before it can be deleted"
msgstr "La compilación debe cancelarse antes de poder ser eliminada"

#: build/api.py:453 build/serializers.py:1392 part/models.py:4333
msgid "Consumable"
msgstr "Consumible"

#: build/api.py:456 build/serializers.py:1395 part/models.py:4327
msgid "Optional"
msgstr "Opcional"

#: build/api.py:459 common/setting/system.py:429 part/models.py:1211
#: part/serializers.py:1663 part/serializers.py:1672 stock/api.py:588
msgid "Assembly"
msgstr "Montaje"

#: build/api.py:462
msgid "Tracked"
msgstr "Rastreado"

#: build/api.py:465 build/serializers.py:1398 part/models.py:1229
msgid "Testable"
msgstr "Comprobable"

#: build/api.py:475 order/api.py:925
msgid "Order Outstanding"
msgstr "Pedido pendiente"

#: build/api.py:485 order/api.py:884
msgid "Allocated"
msgstr "Asignadas"

#: build/api.py:495 company/models.py:889 company/serializers.py:427
#: templates/email/build_order_required_stock.html:19
#: templates/email/low_stock_notification.html:17
#: templates/email/part_event_notification.html:18
msgid "Available"
msgstr "Disponible"

#: build/api.py:805 build/models.py:116
#: report/templates/report/inventree_build_order_report.html:105
#: stock/serializers.py:92 templates/email/build_order_completed.html:16
#: templates/email/overdue_build_order.html:15
msgid "Build Order"
msgstr "Construir órden"

#: build/models.py:117 users/ruleset.py:37
msgid "Build Orders"
msgstr "Construir órdenes"

#: build/models.py:162
msgid "Assembly BOM has not been validated"
msgstr "BOM de ensamblado no ha sido validado"

#: build/models.py:169
msgid "Build order cannot be created for an inactive part"
msgstr "La orden de construcción no puede ser creado para una parte inactiva"

#: build/models.py:176
msgid "Build order cannot be created for an unlocked part"
msgstr "La orden de construcción no puede ser creada para una parte desbloqueada"

#: build/models.py:188
msgid "Invalid choice for parent build"
msgstr "Opción no válida para la construcción padre"

#: build/models.py:197 order/models.py:363
msgid "Responsible user or group must be specified"
msgstr "Se debe especificar un usuario o grupo responsable"

#: build/models.py:202
msgid "Build order part cannot be changed"
msgstr "La parte del pedido de construcción no puede ser modificada"

#: build/models.py:207 order/models.py:376
msgid "Target date must be after start date"
msgstr "La fecha límite debe ser posterior a la fecha de inicio"

#: build/models.py:235
msgid "Build Order Reference"
msgstr "Número de orden de construcción o armado"

#: build/models.py:236 build/serializers.py:1389 order/models.py:601
#: order/models.py:1140 order/models.py:1572 order/models.py:2403
#: part/models.py:4348
#: report/templates/report/inventree_bill_of_materials_report.html:139
#: report/templates/report/inventree_purchase_order_report.html:28
#: report/templates/report/inventree_return_order_report.html:26
#: report/templates/report/inventree_sales_order_report.html:28
msgid "Reference"
msgstr "Referencia"

#: build/models.py:245
msgid "Brief description of the build (optional)"
msgstr "Breve descripción de la construcción (opcional)"

#: build/models.py:255
msgid "BuildOrder to which this build is allocated"
msgstr "Orden de Construcción o Armado a la que se asigna"

#: build/models.py:264
msgid "Select part to build"
msgstr "Seleccionar parte a construir o armar"

#: build/models.py:269
msgid "Sales Order Reference"
msgstr "Referencia de orden de venta"

#: build/models.py:274
msgid "SalesOrder to which this build is allocated"
msgstr "Orden de Venta a la que se asigna"

#: build/models.py:279 build/serializers.py:1104
msgid "Source Location"
msgstr "Ubicación de la fuente"

#: build/models.py:285
msgid "Select location to take stock from for this build (leave blank to take from any stock location)"
msgstr "Seleccione la ubicación de donde tomar stock para esta construcción o armado (deje en blanco para tomar desde cualquier ubicación)"

#: build/models.py:291
msgid "Destination Location"
msgstr "Ubicación de destino"

#: build/models.py:296
msgid "Select location where the completed items will be stored"
msgstr "Seleccione la ubicación donde se almacenarán los artículos completados"

#: build/models.py:300
msgid "Build Quantity"
msgstr "Cantidad a crear"

#: build/models.py:303
msgid "Number of stock items to build"
msgstr "Número de objetos existentes a construir"

#: build/models.py:307
msgid "Completed items"
msgstr "Elementos completados"

#: build/models.py:309
msgid "Number of stock items which have been completed"
msgstr "Número de productos en stock que se han completado"

#: build/models.py:313
msgid "Build Status"
msgstr "Estado de la construcción"

#: build/models.py:318
msgid "Build status code"
msgstr "Código de estado de construcción"

#: build/models.py:327 build/serializers.py:376 order/serializers.py:785
#: stock/models.py:1023 stock/serializers.py:84 stock/serializers.py:1651
msgid "Batch Code"
msgstr "Numero de lote"

#: build/models.py:331 build/serializers.py:377
msgid "Batch code for this build output"
msgstr "Número de lote de este producto final"

#: build/models.py:335 order/models.py:466 order/serializers.py:166
#: part/models.py:1286
msgid "Creation Date"
msgstr "Fecha de Creación"

#: build/models.py:341
msgid "Build start date"
msgstr "Crear fecha de inicio"

#: build/models.py:342
msgid "Scheduled start date for this build order"
msgstr "Fecha de inicio programada para este pedido"

#: build/models.py:348
msgid "Target completion date"
msgstr "Fecha límite de finalización"

#: build/models.py:350
msgid "Target date for build completion. Build will be overdue after this date."
msgstr "Fecha límite para la finalización de la construcción. La construcción estará vencida después de esta fecha."

#: build/models.py:355 order/models.py:654 order/models.py:2442
msgid "Completion Date"
msgstr "Fecha de finalización"

#: build/models.py:363
msgid "completed by"
msgstr "terminado por"

#: build/models.py:372
msgid "Issued by"
msgstr "Emitido por"

#: build/models.py:373
msgid "User who issued this build order"
msgstr "El usuario que emitió esta orden"

#: build/models.py:382 common/models.py:132 order/api.py:172
#: order/models.py:491 part/models.py:1303
#: report/templates/report/inventree_build_order_report.html:158
msgid "Responsible"
msgstr "Responsable"

#: build/models.py:383
msgid "User or group responsible for this build order"
msgstr "Usuario o grupo responsable de esta orden de construcción"

#: build/models.py:388 stock/models.py:1016
msgid "External Link"
msgstr "Link externo"

#: build/models.py:390 common/models.py:1899 part/models.py:1114
#: stock/models.py:1018
msgid "Link to external URL"
msgstr "Enlace a URL externa"

#: build/models.py:395
msgid "Build Priority"
msgstr "Prioridad de construcción"

#: build/models.py:398
msgid "Priority of this build order"
msgstr "Prioridad de esta orden de construcción"

#: build/models.py:406 common/models.py:102 common/models.py:116
#: order/api.py:158 order/models.py:438
msgid "Project Code"
msgstr "Código del proyecto"

#: build/models.py:407
msgid "Project code for this build order"
msgstr "Código de proyecto para esta orden de ensamble"

#: build/models.py:680 build/models.py:803
msgid "Failed to offload task to complete build allocations"
msgstr "No se pudo descargar la tarea para completar las asignaciones de construcción"

#: build/models.py:703
#, python-brace-format
msgid "Build order {build} has been completed"
msgstr "El pedido {build} ha sido procesado"

#: build/models.py:709
msgid "A build order has been completed"
msgstr "Pedido #[order] ha sido procesado"

#: build/models.py:881 build/serializers.py:424
msgid "Serial numbers must be provided for trackable parts"
msgstr "Los números de serie deben ser proporcionados para las partes rastreables"

#: build/models.py:991 build/models.py:1076
msgid "No build output specified"
msgstr "No se ha especificado salida de construcción"

#: build/models.py:994
msgid "Build output is already completed"
msgstr "La construcción de la salida ya está completa"

#: build/models.py:997
msgid "Build output does not match Build Order"
msgstr "La salida de la construcción no coincide con el orden de construcción"

#: build/models.py:1079 build/serializers.py:303 build/serializers.py:352
#: build/serializers.py:972 order/models.py:704 order/serializers.py:601
#: order/serializers.py:780 part/serializers.py:1657 stock/models.py:863
#: stock/models.py:1730 stock/serializers.py:727
msgid "Quantity must be greater than zero"
msgstr "La cantidad debe ser mayor que cero"

#: build/models.py:1083 build/serializers.py:307
msgid "Quantity cannot be greater than the output quantity"
msgstr "La cantidad no puede ser mayor que la cantidad de salida"

#: build/models.py:1148 build/serializers.py:615
#, python-brace-format
msgid "Build output {serial} has not passed all required tests"
msgstr "La construcción {serial} no ha pasado todas las pruebas requeridas"

#: build/models.py:1534
msgid "Build Order Line Item"
msgstr "Construir línea de pedido"

#: build/models.py:1558
msgid "Build object"
msgstr "Ensamblar equipo"

#: build/models.py:1570 build/models.py:1829 build/serializers.py:291
#: build/serializers.py:337 build/serializers.py:1410 common/models.py:1255
#: order/models.py:1555 order/models.py:2288 order/serializers.py:1670
#: order/serializers.py:2131 part/models.py:3353 part/models.py:4321
#: part/serializers.py:275
#: report/templates/report/inventree_bill_of_materials_report.html:138
#: report/templates/report/inventree_build_order_report.html:113
#: report/templates/report/inventree_purchase_order_report.html:29
#: report/templates/report/inventree_sales_order_report.html:29
#: report/templates/report/inventree_sales_order_shipment_report.html:47
#: report/templates/report/inventree_sales_order_shipment_report.html:49
#: report/templates/report/inventree_stock_location_report.html:104
#: report/templates/report/inventree_test_report.html:90
#: report/templates/report/inventree_test_report.html:169
#: stock/serializers.py:135 stock/serializers.py:179 stock/serializers.py:718
#: templates/email/build_order_completed.html:18
msgid "Quantity"
msgstr "Cantidad"

#: build/models.py:1571
msgid "Required quantity for build order"
msgstr "Cantidad requerida para orden de ensamble"

#: build/models.py:1655
msgid "Build item must specify a build output, as master part is marked as trackable"
msgstr "Item de construcción o armado debe especificar un resultado o salida, ya que la parte maestra está marcada como rastreable"

#: build/models.py:1666
#, python-brace-format
msgid "Allocated quantity ({q}) must not exceed available stock quantity ({a})"
msgstr "Cantidad asignada ({q}) no debe exceder la cantidad disponible de stock ({a})"

#: build/models.py:1687 order/models.py:2237
msgid "Stock item is over-allocated"
msgstr "Artículo de stock sobreasignado"

#: build/models.py:1692 order/models.py:2240
msgid "Allocation quantity must be greater than zero"
msgstr "Cantidad asignada debe ser mayor que cero"

#: build/models.py:1698
msgid "Quantity must be 1 for serialized stock"
msgstr "La cantidad debe ser 1 para el stock serializado"

#: build/models.py:1758
msgid "Selected stock item does not match BOM line"
msgstr "El artículo de almacén selelccionado no coincide con la línea BOM"

#: build/models.py:1819 build/serializers.py:955 build/serializers.py:1263
#: order/serializers.py:1507 order/serializers.py:1528
#: report/templates/report/inventree_sales_order_shipment_report.html:29
#: stock/models.py:452 stock/serializers.py:101 stock/serializers.py:825
#: stock/serializers.py:1357 stock/serializers.py:1469
msgid "Stock Item"
msgstr "Artículo de stock"

#: build/models.py:1820
msgid "Source stock item"
msgstr "Producto original de stock"

#: build/models.py:1830
msgid "Stock quantity to allocate to build"
msgstr "Cantidad de stock a asignar para construir"

#: build/models.py:1839
msgid "Install into"
msgstr "Instalar en"

#: build/models.py:1840
msgid "Destination stock item"
msgstr "Artículo de stock de destino"

#: build/serializers.py:116
msgid "Build Level"
msgstr "Nivel de construcción"

#: build/serializers.py:125 part/stocktake.py:219
msgid "Part Name"
msgstr "Nombre de parte"

#: build/serializers.py:143
msgid "Project Code Label"
msgstr "Etiqueta del código del proyecto"

#: build/serializers.py:155
msgid "Create Child Builds"
msgstr "Crear construcciones hijas"

#: build/serializers.py:156
msgid "Automatically generate child build orders"
msgstr "Generar automáticamente órdenes de construcción hijas"

#: build/serializers.py:239 build/serializers.py:981
msgid "Build Output"
msgstr "Resultado de la construcción o armado"

#: build/serializers.py:251
msgid "Build output does not match the parent build"
msgstr "La salida de construcción no coincide con la construcción padre"

#: build/serializers.py:255
msgid "Output part does not match BuildOrder part"
msgstr "La parte de salida no coincide con la parte de la Orden de Construcción"

#: build/serializers.py:259
msgid "This build output has already been completed"
msgstr "Esta salida de construcción ya ha sido completada"

#: build/serializers.py:273
msgid "This build output is not fully allocated"
msgstr "Esta salida de construcción no está completamente asignada"

#: build/serializers.py:292 build/serializers.py:338
msgid "Enter quantity for build output"
msgstr "Ingrese la cantidad para la producción de la construcción"

#: build/serializers.py:360
msgid "Integer quantity required for trackable parts"
msgstr "Cantidad entera requerida para partes rastreables"

#: build/serializers.py:366
msgid "Integer quantity required, as the bill of materials contains trackable parts"
msgstr "Cantidad entera requerida, ya que la factura de materiales contiene partes rastreables"

#: build/serializers.py:383 order/serializers.py:801 order/serializers.py:1674
#: stock/serializers.py:738
msgid "Serial Numbers"
msgstr "Números de serie"

#: build/serializers.py:384
msgid "Enter serial numbers for build outputs"
msgstr "Introduzca los números de serie de salidas de construcción"

#: build/serializers.py:389 build/serializers.py:514 build/serializers.py:584
#: build/serializers.py:1267 build/serializers.py:1271 order/serializers.py:769
#: order/serializers.py:916 order/serializers.py:2022 part/serializers.py:1275
#: stock/serializers.py:110 stock/serializers.py:647 stock/serializers.py:749
#: stock/serializers.py:913 stock/serializers.py:1041 stock/serializers.py:1501
#: stock/serializers.py:1789 users/models.py:551
msgid "Location"
msgstr "Ubicación"

#: build/serializers.py:390
msgid "Stock location for build output"
msgstr "Ubicación de stock para objetos construidos"

#: build/serializers.py:405
msgid "Auto Allocate Serial Numbers"
msgstr "Autoasignar Números de Serie"

#: build/serializers.py:407
msgid "Automatically allocate required items with matching serial numbers"
msgstr "Asignar automáticamente los artículos requeridos con números de serie coincidentes"

#: build/serializers.py:440 order/serializers.py:894 stock/api.py:1077
#: stock/models.py:1753
msgid "The following serial numbers already exist or are invalid"
msgstr "Los siguientes números seriales ya existen o son inválidos"

#: build/serializers.py:482 build/serializers.py:538 build/serializers.py:622
msgid "A list of build outputs must be provided"
msgstr "Debe proporcionarse una lista de salidas de construcción"

#: build/serializers.py:515
msgid "Stock location for scrapped outputs"
msgstr "Ubicación de almacén para salidas descartadas"

#: build/serializers.py:521
msgid "Discard Allocations"
msgstr "Descartar asignaciones"

#: build/serializers.py:522
msgid "Discard any stock allocations for scrapped outputs"
msgstr "Descartar cualquier asignación de existencias para las salidas descartadas"

#: build/serializers.py:527
msgid "Reason for scrapping build output(s)"
msgstr "Razón para descartar la salida de ensamble(s)"

#: build/serializers.py:585
msgid "Location for completed build outputs"
msgstr "Ubicación para las salidas de construcción completadas"

#: build/serializers.py:593
msgid "Accept Incomplete Allocation"
msgstr "Aceptar Asignación Incompleta"

#: build/serializers.py:594
msgid "Complete outputs if stock has not been fully allocated"
msgstr "Completar salidas si el inventario no se ha asignado completamente"

#: build/serializers.py:709
msgid "Consume Allocated Stock"
msgstr "Consumir Stock Asignado"

#: build/serializers.py:710
msgid "Consume any stock which has already been allocated to this build"
msgstr "Consume cualquier stock que ya ha sido asignado a esta construcción"

#: build/serializers.py:716
msgid "Remove Incomplete Outputs"
msgstr "Eliminar salidas incompletas"

#: build/serializers.py:717
msgid "Delete any build outputs which have not been completed"
msgstr "Eliminar cualquier salida de construcción que no se haya completado"

#: build/serializers.py:744
msgid "Not permitted"
msgstr "No permitido"

#: build/serializers.py:745
msgid "Accept as consumed by this build order"
msgstr "Aceptar como consumido por este pedido de construcción"

#: build/serializers.py:746
msgid "Deallocate before completing this build order"
msgstr "Liberar antes de completar esta orden de construcción"

#: build/serializers.py:773
msgid "Overallocated Stock"
msgstr "Stock sobreasignado"

#: build/serializers.py:776
msgid "How do you want to handle extra stock items assigned to the build order"
msgstr "Cómo quieres manejar los artículos extra de inventario asignados a la orden de construcción"

#: build/serializers.py:787
msgid "Some stock items have been overallocated"
msgstr "Algunos artículos de inventario han sido sobreasignados"

#: build/serializers.py:792
msgid "Accept Unallocated"
msgstr "Aceptar no asignado"

#: build/serializers.py:794
msgid "Accept that stock items have not been fully allocated to this build order"
msgstr "Aceptar que los artículos de stock no se han asignado completamente a este pedido de construcción"

#: build/serializers.py:805
msgid "Required stock has not been fully allocated"
msgstr "El stock requerido no ha sido completamente asignado"

#: build/serializers.py:810 order/serializers.py:444 order/serializers.py:1575
msgid "Accept Incomplete"
msgstr "Aceptar incompleto"

#: build/serializers.py:812
msgid "Accept that the required number of build outputs have not been completed"
msgstr "Aceptar que el número requerido de salidas de construcción no se han completado"

#: build/serializers.py:823
msgid "Required build quantity has not been completed"
msgstr "La cantidad de construcción requerida aún no se ha completado"

#: build/serializers.py:835
msgid "Build order has open child build orders"
msgstr "La orden de construcción tiene órdenes hijas de construcción abiertas"

#: build/serializers.py:838
msgid "Build order must be in production state"
msgstr "Orden de construcción debe estar en estado de producción"

#: build/serializers.py:841
msgid "Build order has incomplete outputs"
msgstr "El orden de construcción tiene salidas incompletas"

#: build/serializers.py:880
msgid "Build Line"
msgstr "Linea de ensamble"

#: build/serializers.py:888
msgid "Build output"
msgstr "Resultado de la construcción o armado"

#: build/serializers.py:896
msgid "Build output must point to the same build"
msgstr "La salida de la construcción debe apuntar a la misma construcción"

#: build/serializers.py:927
msgid "Build Line Item"
msgstr "Crear partida"

#: build/serializers.py:945
msgid "bom_item.part must point to the same part as the build order"
msgstr "bom_item.part debe apuntar a la misma parte que la orden de construcción"

#: build/serializers.py:961 stock/serializers.py:1370
msgid "Item must be in stock"
msgstr "El artículo debe estar en stock"

#: build/serializers.py:1004 order/serializers.py:1561
#, python-brace-format
msgid "Available quantity ({q}) exceeded"
msgstr "Cantidad disponible ({q}) excedida"

#: build/serializers.py:1010
msgid "Build output must be specified for allocation of tracked parts"
msgstr "La salida de la construcción debe especificarse para la asignación de partes rastreadas"

#: build/serializers.py:1018
msgid "Build output cannot be specified for allocation of untracked parts"
msgstr "La salida de construcción no se puede especificar para la asignación de partes no rastreadas"

#: build/serializers.py:1042 order/serializers.py:1834
msgid "Allocation items must be provided"
msgstr "Debe proporcionarse la adjudicación de artículos"

#: build/serializers.py:1106
msgid "Stock location where parts are to be sourced (leave blank to take from any location)"
msgstr "Ubicación de inventario donde las partes deben ser obtenidas (dejar en blanco para tomar de cualquier ubicación)"

#: build/serializers.py:1115
msgid "Exclude Location"
msgstr "Excluir ubicación"

#: build/serializers.py:1116
msgid "Exclude stock items from this selected location"
msgstr "Excluir artículos de stock de esta ubicación seleccionada"

#: build/serializers.py:1121
msgid "Interchangeable Stock"
msgstr "Stock intercambiable"

#: build/serializers.py:1122
msgid "Stock items in multiple locations can be used interchangeably"
msgstr "Los artículos de inventario en múltiples ubicaciones se pueden utilizar de forma intercambiable"

#: build/serializers.py:1127
msgid "Substitute Stock"
msgstr "Sustituir stock"

#: build/serializers.py:1128
msgid "Allow allocation of substitute parts"
msgstr "Permitir la asignación de partes sustitutas"

#: build/serializers.py:1133
msgid "Optional Items"
msgstr "Elementos opcionales"

#: build/serializers.py:1134
msgid "Allocate optional BOM items to build order"
msgstr "Asignar artículos de la BOM opcionales para construir la orden"

#: build/serializers.py:1156
msgid "Failed to start auto-allocation task"
msgstr "Error al iniciar la tarea de asignación automática"

#: build/serializers.py:1230
msgid "BOM Reference"
msgstr "Referencia BOM"

#: build/serializers.py:1236
msgid "BOM Part ID"
msgstr "ID de la parte BOM"

#: build/serializers.py:1243
msgid "BOM Part Name"
msgstr "Nombre de parte la BOM"

#: build/serializers.py:1278 build/serializers.py:1434
msgid "Build"
msgstr ""

#: build/serializers.py:1286 company/models.py:662 order/api.py:304
#: order/api.py:309 order/api.py:508 order/serializers.py:593
#: stock/models.py:959 stock/serializers.py:631
msgid "Supplier Part"
msgstr "Parte del proveedor"

#: build/serializers.py:1294 stock/serializers.py:662
msgid "Allocated Quantity"
msgstr "Cantidad Asignada"

#: build/serializers.py:1371
msgid "Build Reference"
msgstr "Referencia de orden de Ensamblado"

#: build/serializers.py:1381
msgid "Part Category Name"
msgstr "Nombre de la categoría por pieza"

#: build/serializers.py:1401 common/setting/system.py:453 part/models.py:1223
msgid "Trackable"
msgstr "Rastreable"

#: build/serializers.py:1404
msgid "Inherited"
msgstr "Heredado"

#: build/serializers.py:1407 part/models.py:4381
msgid "Allow Variants"
msgstr "Permitir variantes"

#: build/serializers.py:1412 build/serializers.py:1416 part/models.py:4179
#: part/models.py:4657 stock/api.py:831
msgid "BOM Item"
msgstr "Item de Lista de Materiales"

#: build/serializers.py:1445
msgid "Allocated Stock"
msgstr "Stock Asignado"

#: build/serializers.py:1447 company/serializers.py:424
#: order/serializers.py:1271 part/serializers.py:943 part/serializers.py:1690
msgid "On Order"
msgstr "En pedido"

#: build/serializers.py:1449 order/serializers.py:1272 part/serializers.py:1694
msgid "In Production"
msgstr "En producción"

#: build/serializers.py:1451 part/serializers.py:964
msgid "External Stock"
msgstr "Stock externo"

#: build/serializers.py:1452 part/serializers.py:1723
msgid "Available Stock"
msgstr "Stock Disponible"

#: build/serializers.py:1454
msgid "Available Substitute Stock"
msgstr "Stock sustituto disponible"

#: build/serializers.py:1457
msgid "Available Variant Stock"
msgstr "Stock variable disponible"

#: build/status_codes.py:11 generic/states/tests.py:21
#: generic/states/tests.py:131 order/status_codes.py:12
#: order/status_codes.py:44 order/status_codes.py:76 order/status_codes.py:102
msgid "Pending"
msgstr "Pendiente"

#: build/status_codes.py:12
msgid "Production"
msgstr "Producción"

#: build/status_codes.py:13 order/status_codes.py:14 order/status_codes.py:51
#: order/status_codes.py:81
msgid "On Hold"
msgstr "En espera"

#: build/status_codes.py:14 order/status_codes.py:16 order/status_codes.py:53
#: order/status_codes.py:84
msgid "Cancelled"
msgstr "Cancelado"

#: build/status_codes.py:15 generic/states/tests.py:23 importer/models.py:529
#: importer/status_codes.py:27 order/status_codes.py:15
#: order/status_codes.py:52 order/status_codes.py:83
msgid "Complete"
msgstr "Terminado"

#: build/tasks.py:193
msgid "Stock required for build order"
msgstr "Stock requerido para la orden de construcción"

#: build/tasks.py:267
msgid "Overdue Build Order"
msgstr "Orden de construcción atrasada"

#: build/tasks.py:272
#, python-brace-format
msgid "Build order {bo} is now overdue"
msgstr "El pedido de construcción {bo} está atrasado"

#: common/api.py:710
msgid "Is Link"
msgstr "¿Es enlace?"

#: common/api.py:718
msgid "Is File"
msgstr "¿Es archivo?"

#: common/api.py:761
msgid "User does not have permission to delete these attachments"
msgstr "El usuario no tiene permiso para eliminar estos adjuntos"

#: common/api.py:778
msgid "User does not have permission to delete this attachment"
msgstr "El usuario no tiene permiso para eliminar este adjunto"

#: common/currency.py:120
msgid "Invalid currency code"
msgstr "Código de divisa inválido"

#: common/currency.py:122
msgid "Duplicate currency code"
msgstr "Código de divisa duplicado"

#: common/currency.py:127
msgid "No valid currency codes provided"
msgstr "No se han proporcionado códigos de divisa válidos"

#: common/currency.py:144
msgid "No plugin"
msgstr "Sin plugin"

#: common/models.py:89
msgid "Updated"
msgstr "Actualizado"

#: common/models.py:90
msgid "Timestamp of last update"
msgstr "Fecha y hora de la última actualización"

#: common/models.py:117
msgid "Unique project code"
msgstr "Código único del proyecto"

#: common/models.py:124
msgid "Project description"
msgstr "Descripción del proyecto"

#: common/models.py:133
msgid "User or group responsible for this project"
msgstr "Usuario o grupo responsable de este projecto"

#: common/models.py:721 common/models.py:1187 common/models.py:1225
msgid "Settings key"
msgstr "Tecla de ajustes"

#: common/models.py:725
msgid "Settings value"
msgstr "Valor de ajuste"

#: common/models.py:780
msgid "Chosen value is not a valid option"
msgstr "El valor elegido no es una opción válida"

#: common/models.py:796
msgid "Value must be a boolean value"
msgstr "El valor debe ser un valor booleano"

#: common/models.py:804
msgid "Value must be an integer value"
msgstr "El valor debe ser un entero"

#: common/models.py:812
msgid "Value must be a valid number"
msgstr "El valor debe ser un número válido"

#: common/models.py:837
msgid "Value does not pass validation checks"
msgstr "El valor no pasa las comprobaciones de validación"

#: common/models.py:859
msgid "Key string must be unique"
msgstr "Cadena de clave debe ser única"

#: common/models.py:1233 common/models.py:1234 common/models.py:1338
#: common/models.py:1339 common/models.py:1584 common/models.py:1585
#: common/models.py:1915 common/models.py:1916 common/models.py:2297
#: importer/models.py:97 part/models.py:3376 part/models.py:3463
#: part/models.py:3537 part/models.py:3565 plugin/models.py:322
#: plugin/models.py:323 report/templates/report/inventree_test_report.html:105
#: users/models.py:130 users/models.py:503
msgid "User"
msgstr "Usuario"

#: common/models.py:1256
msgid "Price break quantity"
msgstr "Cantidad de salto de precio"

#: common/models.py:1263 company/serializers.py:567 order/models.py:1632
#: order/models.py:2734
msgid "Price"
msgstr "Precio"

#: common/models.py:1264
msgid "Unit price at specified quantity"
msgstr "Precio unitario a la cantidad especificada"

#: common/models.py:1315 common/models.py:1500
msgid "Endpoint"
msgstr "Endpoint"

#: common/models.py:1316
msgid "Endpoint at which this webhook is received"
msgstr "Punto final en el que se recibe este webhook"

#: common/models.py:1326
msgid "Name for this webhook"
msgstr "Nombre para este webhook"

#: common/models.py:1330 common/models.py:2156 common/models.py:2263
#: company/models.py:164 company/models.py:799 machine/models.py:39
#: part/models.py:1246 plugin/models.py:68 stock/api.py:591 users/models.py:201
#: users/models.py:556 users/serializers.py:333
msgid "Active"
msgstr "Activo"

#: common/models.py:1330
msgid "Is this webhook active"
msgstr "Está activo este webhook"

#: common/models.py:1346 users/models.py:178
msgid "Token"
msgstr "Token"

#: common/models.py:1347
msgid "Token for access"
msgstr "Token para el acceso"

#: common/models.py:1355
msgid "Secret"
msgstr "Clave"

#: common/models.py:1356
msgid "Shared secret for HMAC"
msgstr "Secreto compartido para HMAC"

#: common/models.py:1464
msgid "Message ID"
msgstr "ID de mensaje"

#: common/models.py:1465
msgid "Unique identifier for this message"
msgstr "Identificador único para este mensaje"

#: common/models.py:1473
msgid "Host"
msgstr "Servidor"

#: common/models.py:1474
msgid "Host from which this message was received"
msgstr "Servidor desde el cual se recibió este mensaje"

#: common/models.py:1482
msgid "Header"
msgstr "Encabezado"

#: common/models.py:1483
msgid "Header of this message"
msgstr "Encabezado del mensaje"

#: common/models.py:1490
msgid "Body"
msgstr "Cuerpo"

#: common/models.py:1491
msgid "Body of this message"
msgstr "Cuerpo de este mensaje"

#: common/models.py:1501
msgid "Endpoint on which this message was received"
msgstr "Endpoint en el que se recibió este mensaje"

#: common/models.py:1506
msgid "Worked on"
msgstr "Trabajado en"

#: common/models.py:1507
msgid "Was the work on this message finished?"
msgstr "¿El trabajo en este mensaje ha terminado?"

#: common/models.py:1633
msgid "Id"
msgstr "Id"

#: common/models.py:1635 part/serializers.py:281
msgid "Title"
msgstr "Título"

#: common/models.py:1637 common/models.py:1898 company/models.py:149
#: company/models.py:445 company/models.py:512 company/models.py:816
#: order/models.py:444 order/models.py:1585 order/models.py:2056
#: part/models.py:1113
#: report/templates/report/inventree_build_order_report.html:164
msgid "Link"
msgstr "Enlace"

#: common/models.py:1639
msgid "Published"
msgstr "Publicado"

#: common/models.py:1641
msgid "Author"
msgstr "Autor"

#: common/models.py:1643
msgid "Summary"
msgstr "Resumen"

#: common/models.py:1646
msgid "Read"
msgstr "Leer"

#: common/models.py:1646
msgid "Was this news item read?"
msgstr "¿Esta noticia ya fue leída?"

#: common/models.py:1663 company/models.py:160 part/models.py:1124
#: report/templates/report/inventree_bill_of_materials_report.html:126
#: report/templates/report/inventree_bill_of_materials_report.html:148
#: report/templates/report/inventree_return_order_report.html:35
msgid "Image"
msgstr "Imágen"

#: common/models.py:1663
msgid "Image file"
msgstr "Archivo de imagen"

#: common/models.py:1675
msgid "Target model type for this image"
msgstr ""

#: common/models.py:1679
msgid "Target model ID for this image"
msgstr ""

#: common/models.py:1701
msgid "Custom Unit"
msgstr "Unidad personalizada"

#: common/models.py:1719
msgid "Unit symbol must be unique"
msgstr "El símbolo de la unidad debe ser único"

#: common/models.py:1734
msgid "Unit name must be a valid identifier"
msgstr "Nombre de unidad debe ser un identificador válido"

#: common/models.py:1753
msgid "Unit name"
msgstr "Nombre de unidad"

#: common/models.py:1760
msgid "Symbol"
msgstr "Símbolo"

#: common/models.py:1761
msgid "Optional unit symbol"
msgstr "Símbolo de unidad opcional"

#: common/models.py:1767
msgid "Definition"
msgstr "Definición"

#: common/models.py:1768
msgid "Unit definition"
msgstr "Definición de unidad"

#: common/models.py:1826 common/models.py:1889 stock/models.py:2827
#: stock/serializers.py:258
msgid "Attachment"
msgstr "Archivo adjunto"

#: common/models.py:1843
msgid "Missing file"
msgstr "Archivo no encontrado"

#: common/models.py:1844
msgid "Missing external link"
msgstr "Falta enlace externo"

#: common/models.py:1881
msgid "Model type"
msgstr ""

#: common/models.py:1882
msgid "Target model type for image"
msgstr ""

#: common/models.py:1890
msgid "Select file to attach"
msgstr "Seleccionar archivo para adjuntar"

#: common/models.py:1906
msgid "Comment"
msgstr "Comentario"

#: common/models.py:1907
msgid "Attachment comment"
msgstr "Comentario de archivo adjunto"

#: common/models.py:1923
msgid "Upload date"
msgstr "Fecha de carga"

#: common/models.py:1924
msgid "Date the file was uploaded"
msgstr "Fecha de carga del archivo"

#: common/models.py:1928
msgid "File size"
msgstr "Tamaño del archivo"

#: common/models.py:1928
msgid "File size in bytes"
msgstr "Tamaño del archivo en bytes"

#: common/models.py:1966 common/serializers.py:637
msgid "Invalid model type specified for attachment"
msgstr "Tipo de modelo no válido especificado para el archivo adjunto"

#: common/models.py:1987
msgid "Custom State"
msgstr "Estado personalizado"

#: common/models.py:1988
msgid "Custom States"
msgstr "Estados personalizados"

#: common/models.py:1993
msgid "Reference Status Set"
msgstr ""

#: common/models.py:1994
msgid "Status set that is extended with this custom state"
msgstr ""

#: common/models.py:1998 generic/states/serializers.py:18
msgid "Logical Key"
msgstr "Llave lógica"

#: common/models.py:2000
msgid "State logical key that is equal to this custom state in business logic"
msgstr ""

#: common/models.py:2005 common/models.py:2244 company/models.py:600
#: report/templates/report/inventree_test_report.html:104 stock/models.py:2819
msgid "Value"
msgstr "Valor"

#: common/models.py:2006
msgid "Numerical value that will be saved in the models database"
msgstr ""

#: common/models.py:2012
msgid "Name of the state"
msgstr "Nombre del estado"

#: common/models.py:2021 common/models.py:2250 generic/states/serializers.py:22
#: part/serializers.py:283
msgid "Label"
msgstr "Etiqueta"

#: common/models.py:2022
msgid "Label that will be displayed in the frontend"
msgstr "Etiqueta que se mostrará en el frontend"

#: common/models.py:2029 generic/states/serializers.py:24
msgid "Color"
msgstr "Color"

#: common/models.py:2030
msgid "Color that will be displayed in the frontend"
msgstr "Color que se mostrará en el frontend"

#: common/models.py:2038 part/serializers.py:285
msgid "Model"
msgstr "Modelo"

#: common/models.py:2039
msgid "Model this state is associated with"
msgstr ""

#: common/models.py:2054
msgid "Model must be selected"
msgstr "El modelo debe ser seleccionado"

#: common/models.py:2057
msgid "Key must be selected"
msgstr "La clave debe ser seleccionada"

#: common/models.py:2060
msgid "Logical key must be selected"
msgstr "La clave lógica debe ser seleccionada"

#: common/models.py:2064
msgid "Key must be different from logical key"
msgstr ""

#: common/models.py:2071
msgid "Valid reference status class must be provided"
msgstr ""

#: common/models.py:2077
msgid "Key must be different from the logical keys of the reference status"
msgstr ""

#: common/models.py:2084
msgid "Logical key must be in the logical keys of the reference status"
msgstr ""

#: common/models.py:2091
msgid "Name must be different from the names of the reference status"
msgstr ""

#: common/models.py:2131 common/models.py:2238 part/models.py:3876
msgid "Selection List"
msgstr "Lista de selección"

#: common/models.py:2132
msgid "Selection Lists"
msgstr "Listas de Selección"

#: common/models.py:2137
msgid "Name of the selection list"
msgstr "Nombre de la lista de selección"

#: common/models.py:2144
msgid "Description of the selection list"
msgstr "Descripción de la lista de selección"

#: common/models.py:2150 part/models.py:1251
msgid "Locked"
msgstr "Bloqueado"

#: common/models.py:2151
msgid "Is this selection list locked?"
msgstr "¿Está bloqueada esta lista de selección?"

#: common/models.py:2157
msgid "Can this selection list be used?"
msgstr "¿Se puede utilizar esta lista de selección?"

#: common/models.py:2165
msgid "Source Plugin"
msgstr "Complemento de origen"

#: common/models.py:2166
msgid "Plugin which provides the selection list"
msgstr "Complemento que proporciona la lista de selección"

#: common/models.py:2171
msgid "Source String"
msgstr "Cadena de origen"

#: common/models.py:2172
msgid "Optional string identifying the source used for this list"
msgstr "Cadena opcional que identifica la fuente usada para esta lista"

#: common/models.py:2181
msgid "Default Entry"
msgstr "Entrada por defecto"

#: common/models.py:2182
msgid "Default entry for this selection list"
msgstr "Entrada predeterminada para esta lista de selección"

#: common/models.py:2187
msgid "Created"
msgstr "Creado"

#: common/models.py:2188
msgid "Date and time that the selection list was created"
msgstr "Fecha y hora en la que se creó la lista de selección"

#: common/models.py:2193
msgid "Last Updated"
msgstr "Última actualización"

#: common/models.py:2194
msgid "Date and time that the selection list was last updated"
msgstr "Fecha y hora en que la lista de selección fue actualizada por última vez"

#: common/models.py:2228
msgid "Selection List Entry"
msgstr "Entrada de lista de selección"

#: common/models.py:2229
msgid "Selection List Entries"
msgstr "Entradas de la lista de selección"

#: common/models.py:2239
msgid "Selection list to which this entry belongs"
msgstr "Lista de selección a la que pertenece esta entrada"

#: common/models.py:2245
msgid "Value of the selection list entry"
msgstr "Valor del elemento de la lista de selección"

#: common/models.py:2251
msgid "Label for the selection list entry"
msgstr "Etiqueta para la entrada de lista de selección"

#: common/models.py:2257
msgid "Description of the selection list entry"
msgstr "Descripción de la entrada de lista de selección"

#: common/models.py:2264
msgid "Is this selection list entry active?"
msgstr "¿Está activa esta entrada de la lista de selección?"

#: common/models.py:2282
msgid "Barcode Scan"
msgstr "Escanear código de barras"

#: common/models.py:2286 importer/models.py:523 part/models.py:4041
msgid "Data"
msgstr "Datos"

#: common/models.py:2287
msgid "Barcode data"
msgstr "Datos de código de barras"

#: common/models.py:2298
msgid "User who scanned the barcode"
msgstr "Usuario que escaneó el código de barras"

#: common/models.py:2303 importer/models.py:66
msgid "Timestamp"
msgstr ""

#: common/models.py:2304
msgid "Date and time of the barcode scan"
msgstr "Fecha y hora del escaneo de código de barras"

#: common/models.py:2310
msgid "URL endpoint which processed the barcode"
msgstr "Dispositivo URL que procesó el código de barras"

#: common/models.py:2317 order/models.py:1622 plugin/serializers.py:93
msgid "Context"
msgstr "Contexto"

#: common/models.py:2318
msgid "Context data for the barcode scan"
msgstr "Datos de contexto para el escaneo de código de barras"

#: common/models.py:2325
msgid "Response"
msgstr "Respuesta"

#: common/models.py:2326
msgid "Response data from the barcode scan"
msgstr "Respuesta de datos del escaneo de código de barras"

#: common/models.py:2332 report/templates/report/inventree_test_report.html:103
#: stock/models.py:2813
msgid "Result"
msgstr "Resultado"

#: common/models.py:2333
msgid "Was the barcode scan successful?"
msgstr "¿El escaneo de código de barras fue exitoso?"

#: common/notifications.py:332
#, python-brace-format
msgid "New {verbose_name}"
msgstr "Nuevo {verbose_name}"

#: common/notifications.py:334
msgid "A new order has been created and assigned to you"
msgstr "Se ha creado un nuevo pedido y se le ha asignado"

#: common/notifications.py:340
#, python-brace-format
msgid "{verbose_name} canceled"
msgstr "{verbose_name} cancelado"

#: common/notifications.py:342
msgid "A order that is assigned to you was canceled"
msgstr ""

#: common/notifications.py:348 common/notifications.py:355 order/api.py:559
msgid "Items Received"
msgstr "Artículos Recibidos"

#: common/notifications.py:350
msgid "Items have been received against a purchase order"
msgstr "Los artículos han sido recibidos contra una orden de compra"

#: common/notifications.py:357
msgid "Items have been received against a return order"
msgstr "Los artículos han sido recibidos contra una orden de devolución"

#: common/notifications.py:505
msgid "Error raised by plugin"
msgstr "Error generado por el complemento"

#: common/serializers.py:451
msgid "Is Running"
msgstr "Está en ejecución"

#: common/serializers.py:457
msgid "Pending Tasks"
msgstr "Tareas pendientes"

#: common/serializers.py:463
msgid "Scheduled Tasks"
msgstr "Tareas Programadas"

#: common/serializers.py:469
msgid "Failed Tasks"
msgstr "Tareas fallidas"

#: common/serializers.py:484
msgid "Task ID"
msgstr "Identificación de Tarea"

#: common/serializers.py:484
msgid "Unique task ID"
msgstr "Identificación de tarea única"

#: common/serializers.py:486
msgid "Lock"
msgstr "Bloquear"

#: common/serializers.py:486
msgid "Lock time"
msgstr "Bloquear hora"

#: common/serializers.py:488
msgid "Task name"
msgstr "Nombre de la tarea"

#: common/serializers.py:490
msgid "Function"
msgstr "Función"

#: common/serializers.py:490
msgid "Function name"
msgstr "Nombre de la Función"

#: common/serializers.py:492
msgid "Arguments"
msgstr "Argumentos"

#: common/serializers.py:492
msgid "Task arguments"
msgstr "Argumentos de la tarea"

#: common/serializers.py:495
msgid "Keyword Arguments"
msgstr "Argumentos de palabra clave"

#: common/serializers.py:495
msgid "Task keyword arguments"
msgstr "Argumentos de palabra clave de tarea"

#: common/serializers.py:605
msgid "Filename"
msgstr "Nombre de Archivo"

#: common/serializers.py:612 importer/models.py:86 report/api.py:41
#: report/models.py:277 report/serializers.py:53
msgid "Model Type"
msgstr ""

#: common/serializers.py:640
msgid "User does not have permission to create or edit attachments for this model"
msgstr "El usuario no tiene permiso para crear o editar archivos adjuntos para este modelo"

#: common/serializers.py:684 common/serializers.py:787
msgid "Selection list is locked"
msgstr "Lista de selección bloqueada"

#: common/setting/system.py:97
msgid "No group"
msgstr "Sin grupo"

#: common/setting/system.py:156
msgid "Site URL is locked by configuration"
msgstr "La URL del sitio está bloqueada por su configuración"

#: common/setting/system.py:167
msgid "Restart required"
msgstr "Reinicio requerido"

#: common/setting/system.py:168
msgid "A setting has been changed which requires a server restart"
msgstr "Se ha cambiado una configuración que requiere un reinicio del servidor"

#: common/setting/system.py:174
msgid "Pending migrations"
msgstr "Migraciones pendientes"

#: common/setting/system.py:175
msgid "Number of pending database migrations"
msgstr "Número de migraciones de base de datos pendientes"

#: common/setting/system.py:180
msgid "Instance ID"
msgstr ""

#: common/setting/system.py:181
msgid "Unique identifier for this InvenTree instance"
msgstr "Identificador único para esta instancia de InvenTree"

#: common/setting/system.py:186
msgid "Announce ID"
msgstr ""

#: common/setting/system.py:188
msgid "Announce the instance ID of the server in the server status info (unauthenticated)"
msgstr ""

#: common/setting/system.py:194
msgid "Server Instance Name"
msgstr "Nombre de la instancia del servidor"

#: common/setting/system.py:196
msgid "String descriptor for the server instance"
msgstr "Descriptor de cadena para la instancia del servidor"

#: common/setting/system.py:200
msgid "Use instance name"
msgstr "Usar nombre de instancia"

#: common/setting/system.py:201
msgid "Use the instance name in the title-bar"
msgstr "Utilice el nombre de la instancia en la barra de título"

#: common/setting/system.py:206
msgid "Restrict showing `about`"
msgstr "Restringir mostrar 'acerca de'"

#: common/setting/system.py:207
msgid "Show the `about` modal only to superusers"
msgstr "Mostrar la modal `about` solo para superusuarios"

#: common/setting/system.py:212 company/models.py:108 company/models.py:109
msgid "Company name"
msgstr "Nombre de empresa"

#: common/setting/system.py:213
msgid "Internal company name"
msgstr "Nombre interno de empresa"

#: common/setting/system.py:217
msgid "Base URL"
msgstr "URL Base"

#: common/setting/system.py:218
msgid "Base URL for server instance"
msgstr "URL base para la instancia del servidor"

#: common/setting/system.py:224
msgid "Default Currency"
msgstr "Moneda predeterminada"

#: common/setting/system.py:225
msgid "Select base currency for pricing calculations"
msgstr "Seleccione la moneda base para los cálculos de precios"

#: common/setting/system.py:231
msgid "Supported Currencies"
msgstr "Monedas admitidas"

#: common/setting/system.py:232
msgid "List of supported currency codes"
msgstr "Listado de códigos de divisa soportados"

#: common/setting/system.py:238
msgid "Currency Update Interval"
msgstr "Intervalo de actualización de moneda"

#: common/setting/system.py:239
msgid "How often to update exchange rates (set to zero to disable)"
msgstr "Con qué frecuencia actualizar los tipos de cambio (establecer a cero para desactivar)"

#: common/setting/system.py:241 common/setting/system.py:281
#: common/setting/system.py:294 common/setting/system.py:302
#: common/setting/system.py:309 common/setting/system.py:318
#: common/setting/system.py:567 common/setting/system.py:587
#: common/setting/system.py:684 common/setting/system.py:1068
msgid "days"
msgstr "días"

#: common/setting/system.py:245
msgid "Currency Update Plugin"
msgstr "Plugin de Actualización de Moneda"

#: common/setting/system.py:246
msgid "Currency update plugin to use"
msgstr "Plugin de actualización de moneda a usar"

#: common/setting/system.py:251
msgid "Download from URL"
msgstr "Descargar desde URL"

#: common/setting/system.py:252
msgid "Allow download of remote images and files from external URL"
msgstr "Permitir la descarga de imágenes y archivos remotos desde la URL externa"

#: common/setting/system.py:257
msgid "Download Size Limit"
msgstr "Límite de tamaño de descarga"

#: common/setting/system.py:258
msgid "Maximum allowable download size for remote image"
msgstr "Tamaño máximo de descarga permitido para la imagen remota"

#: common/setting/system.py:264
msgid "User-agent used to download from URL"
msgstr "Agente de usuario usado para descargar desde la URL"

#: common/setting/system.py:266
msgid "Allow to override the user-agent used to download images and files from external URL (leave blank for the default)"
msgstr "Permitir reemplazar el agente de usuario utilizado para descargar imágenes y archivos desde URL externa (dejar en blanco para el valor predeterminado)"

#: common/setting/system.py:271
msgid "Strict URL Validation"
msgstr "Validación estricta de URL"

#: common/setting/system.py:272
msgid "Require schema specification when validating URLs"
msgstr "Requerir especificación de esquema al validar URLs"

#: common/setting/system.py:277
msgid "Update Check Interval"
msgstr "Actualizar intervalo de actualización"

#: common/setting/system.py:278
msgid "How often to check for updates (set to zero to disable)"
msgstr "Con qué frecuencia comprobar actualizaciones (establecer a cero para desactivar)"

#: common/setting/system.py:284
msgid "Automatic Backup"
msgstr "Copia de seguridad automática"

#: common/setting/system.py:285
msgid "Enable automatic backup of database and media files"
msgstr "Activar copia de seguridad automática de los archivos de base de datos y medios"

#: common/setting/system.py:290
msgid "Auto Backup Interval"
msgstr "Intervalo de respaldo automático"

#: common/setting/system.py:291
msgid "Specify number of days between automated backup events"
msgstr "Especificar número de días entre eventos automatizados de copia de seguridad"

#: common/setting/system.py:297
msgid "Task Deletion Interval"
msgstr "Intervalo de eliminación de tareas"

#: common/setting/system.py:299
msgid "Background task results will be deleted after specified number of days"
msgstr "Los resultados de las tareas en segundo plano se eliminarán después del número especificado de días"

#: common/setting/system.py:306
msgid "Error Log Deletion Interval"
msgstr "Intervalo de eliminación de registro de errores"

#: common/setting/system.py:307
msgid "Error logs will be deleted after specified number of days"
msgstr "Los registros de errores se eliminarán después del número especificado de días"

#: common/setting/system.py:313
msgid "Notification Deletion Interval"
msgstr "Intervalo de eliminación de notificaciones"

#: common/setting/system.py:315
msgid "User notifications will be deleted after specified number of days"
msgstr "Las notificaciones de usuario se eliminarán después del número especificado de días"

#: common/setting/system.py:322
msgid "Barcode Support"
msgstr "Soporte de código de barras"

#: common/setting/system.py:323
msgid "Enable barcode scanner support in the web interface"
msgstr "Habilitar el soporte para escáner de códigos de barras en la interfaz web"

#: common/setting/system.py:328
msgid "Store Barcode Results"
msgstr "Guardar resultados de código de barras"

#: common/setting/system.py:329
msgid "Store barcode scan results in the database"
msgstr "Guardar resultados de código de barras en la base de datos"

#: common/setting/system.py:334
msgid "Barcode Scans Maximum Count"
msgstr "Número máximo de escaneos de código de barras"

#: common/setting/system.py:335
msgid "Maximum number of barcode scan results to store"
msgstr "Número máximo de resultados de escaneo de código de barras para almacenar"

#: common/setting/system.py:340
msgid "Barcode Input Delay"
msgstr "Retraso de entrada de código de barras"

#: common/setting/system.py:341
msgid "Barcode input processing delay time"
msgstr "Tiempo de retraso en la lectura de códigos de barras"

#: common/setting/system.py:347
msgid "Barcode Webcam Support"
msgstr "Soporte para Webcam de código de barras"

#: common/setting/system.py:348
msgid "Allow barcode scanning via webcam in browser"
msgstr "Permitir escaneo de código de barras a través de webcam en el navegador"

#: common/setting/system.py:353
msgid "Barcode Show Data"
msgstr ""

#: common/setting/system.py:354
msgid "Display barcode data in browser as text"
msgstr "Mostrar datos del código de barra como texto en el navegador"

#: common/setting/system.py:359
msgid "Barcode Generation Plugin"
msgstr "Complemento para generar códigos de barra"

#: common/setting/system.py:360
msgid "Plugin to use for internal barcode data generation"
msgstr "Complemento a usar para la generación de datos de códigos de barra internos"

#: common/setting/system.py:365
msgid "Part Revisions"
msgstr "Revisiones de partes"

#: common/setting/system.py:366
msgid "Enable revision field for Part"
msgstr "Habilitar campo de revisión para parte"

#: common/setting/system.py:371
msgid "Assembly Revision Only"
msgstr ""

#: common/setting/system.py:372
msgid "Only allow revisions for assembly parts"
msgstr ""

#: common/setting/system.py:377
msgid "Allow Deletion from Assembly"
msgstr ""

#: common/setting/system.py:378
msgid "Allow deletion of parts which are used in an assembly"
msgstr ""

#: common/setting/system.py:383
msgid "IPN Regex"
msgstr "Regex IPN"

#: common/setting/system.py:384
msgid "Regular expression pattern for matching Part IPN"
msgstr "Patrón de expresión regular para IPN de la parte coincidente"

#: common/setting/system.py:387
msgid "Allow Duplicate IPN"
msgstr "Permitir IPN duplicado"

#: common/setting/system.py:388
msgid "Allow multiple parts to share the same IPN"
msgstr "Permitir que varias partes compartan el mismo IPN"

#: common/setting/system.py:393
msgid "Allow Editing IPN"
msgstr "Permitir editar IPN"

#: common/setting/system.py:394
msgid "Allow changing the IPN value while editing a part"
msgstr "Permite cambiar el valor de IPN mientras se edita una parte"

#: common/setting/system.py:399
msgid "Copy Part BOM Data"
msgstr "Copiar parte de datos BOM"

#: common/setting/system.py:400
msgid "Copy BOM data by default when duplicating a part"
msgstr "Copiar datos BOM por defecto al duplicar una parte"

#: common/setting/system.py:405
msgid "Copy Part Parameter Data"
msgstr "Copiar parámetros de parte"

#: common/setting/system.py:406
msgid "Copy parameter data by default when duplicating a part"
msgstr "Copiar datos de parámetro por defecto al duplicar una parte"

#: common/setting/system.py:411
msgid "Copy Part Test Data"
msgstr "Copiar parte de datos de prueba"

#: common/setting/system.py:412
msgid "Copy test data by default when duplicating a part"
msgstr "Copiar datos de parámetro por defecto al duplicar una parte"

#: common/setting/system.py:417
msgid "Copy Category Parameter Templates"
msgstr "Copiar plantillas de parámetros de categoría"

#: common/setting/system.py:418
msgid "Copy category parameter templates when creating a part"
msgstr "Copiar plantillas de parámetros de categoría al crear una parte"

#: common/setting/system.py:423 part/models.py:4035 report/models.py:357
#: report/models.py:563 report/serializers.py:90 report/serializers.py:131
#: stock/serializers.py:247
msgid "Template"
msgstr "Plantilla"

#: common/setting/system.py:424
msgid "Parts are templates by default"
msgstr "Las partes son plantillas por defecto"

#: common/setting/system.py:430
msgid "Parts can be assembled from other components by default"
msgstr "Las partes pueden ser ensambladas desde otros componentes por defecto"

#: common/setting/system.py:435 part/models.py:1217 part/serializers.py:1677
#: part/serializers.py:1683
msgid "Component"
msgstr "Componente"

#: common/setting/system.py:436
msgid "Parts can be used as sub-components by default"
msgstr "Las partes pueden ser usadas como subcomponentes por defecto"

#: common/setting/system.py:441 part/models.py:1235
msgid "Purchaseable"
msgstr "Comprable"

#: common/setting/system.py:442
msgid "Parts are purchaseable by default"
msgstr "Las partes son comprables por defecto"

#: common/setting/system.py:447 part/models.py:1241 stock/api.py:592
msgid "Salable"
msgstr "Vendible"

#: common/setting/system.py:448
msgid "Parts are salable by default"
msgstr "Las partes se pueden vender por defecto"

#: common/setting/system.py:454
msgid "Parts are trackable by default"
msgstr "Las partes son rastreables por defecto"

#: common/setting/system.py:459 part/models.py:1257
msgid "Virtual"
msgstr "Virtual"

#: common/setting/system.py:460
msgid "Parts are virtual by default"
msgstr "Las partes son virtuales por defecto"

#: common/setting/system.py:465
msgid "Show Import in Views"
msgstr "Mostrar importación en vistas"

#: common/setting/system.py:466
msgid "Display the import wizard in some part views"
msgstr "Mostrar el asistente de importación en algunas vistas de partes"

#: common/setting/system.py:471
msgid "Show related parts"
msgstr "Mostrar partes relacionadas"

#: common/setting/system.py:472
msgid "Display related parts for a part"
msgstr "Mostrar partes relacionadas para una parte"

#: common/setting/system.py:477
msgid "Initial Stock Data"
msgstr "Datos iniciales de existencias"

#: common/setting/system.py:478
msgid "Allow creation of initial stock when adding a new part"
msgstr "Permitir la creación del stock inicial al añadir una nueva parte"

#: common/setting/system.py:483
msgid "Initial Supplier Data"
msgstr "Datos iniciales del proveedor"

#: common/setting/system.py:485
msgid "Allow creation of initial supplier data when adding a new part"
msgstr "Permitir la creación de datos iniciales del proveedor al agregar una nueva parte"

#: common/setting/system.py:491
msgid "Part Name Display Format"
msgstr "Formato de visualización de Nombre de Parte"

#: common/setting/system.py:492
msgid "Format to display the part name"
msgstr "Formato para mostrar el nombre de la parte"

#: common/setting/system.py:498
msgid "Part Category Default Icon"
msgstr "Icono por defecto de la categoría de parte"

#: common/setting/system.py:499
msgid "Part category default icon (empty means no icon)"
msgstr "Icono por defecto de la categoría de parte (vacío significa que no hay icono)"

#: common/setting/system.py:504
msgid "Enforce Parameter Units"
msgstr "Forzar unidades de parámetro"

#: common/setting/system.py:506
msgid "If units are provided, parameter values must match the specified units"
msgstr "Si se proporcionan unidades, los valores de parámetro deben coincidir con las unidades especificadas"

#: common/setting/system.py:512
msgid "Minimum Pricing Decimal Places"
msgstr "Mínimo de lugares decimales en el precio"

#: common/setting/system.py:514
msgid "Minimum number of decimal places to display when rendering pricing data"
msgstr "Número mínimo de decimales a mostrar al procesar los datos de precios"

#: common/setting/system.py:525
msgid "Maximum Pricing Decimal Places"
msgstr "Máximo de lugares decimales en el precio"

#: common/setting/system.py:527
msgid "Maximum number of decimal places to display when rendering pricing data"
msgstr "Número máximo de decimales a mostrar al procesar los datos de precios"

#: common/setting/system.py:538
msgid "Use Supplier Pricing"
msgstr "Usar precios de proveedor"

#: common/setting/system.py:540
msgid "Include supplier price breaks in overall pricing calculations"
msgstr "Incluir descuentos de precios del proveedor en los cálculos generales de precios"

#: common/setting/system.py:546
msgid "Purchase History Override"
msgstr "Anulación del historial de compra"

#: common/setting/system.py:548
msgid "Historical purchase order pricing overrides supplier price breaks"
msgstr "El precio histórico de compra anula los descuentos de precios del proveedor"

#: common/setting/system.py:554
msgid "Use Stock Item Pricing"
msgstr "Usar precio del artículo de almacén"

#: common/setting/system.py:556
msgid "Use pricing from manually entered stock data for pricing calculations"
msgstr "Usar los precios de los datos de inventario introducidos manualmente para los cálculos de precios"

#: common/setting/system.py:562
msgid "Stock Item Pricing Age"
msgstr "Edad del precio del artículo de almacén"

#: common/setting/system.py:564
msgid "Exclude stock items older than this number of days from pricing calculations"
msgstr "Excluir artículos de almacén anteriores a este número de días de los cálculos de precios"

#: common/setting/system.py:571
msgid "Use Variant Pricing"
msgstr "Usar precios variantes"

#: common/setting/system.py:572
msgid "Include variant pricing in overall pricing calculations"
msgstr "Incluir variantes de precios en los cálculos generales de precios"

#: common/setting/system.py:577
msgid "Active Variants Only"
msgstr "Solo variantes activas"

#: common/setting/system.py:579
msgid "Only use active variant parts for calculating variant pricing"
msgstr "Usar solo partes de variantes activas para calcular los precios de variantes"

#: common/setting/system.py:585
msgid "Pricing Rebuild Interval"
msgstr "Intervalo de reconstrucción de precios"

#: common/setting/system.py:586
msgid "Number of days before part pricing is automatically updated"
msgstr "Número de días antes de que el precio de la parte se actualice automáticamente"

#: common/setting/system.py:592
msgid "Internal Prices"
msgstr "Precios internos"

#: common/setting/system.py:593
msgid "Enable internal prices for parts"
msgstr "Habilitar precios internos para partes"

#: common/setting/system.py:598
msgid "Internal Price Override"
msgstr "Anulación del precio interno"

#: common/setting/system.py:600
msgid "If available, internal prices override price range calculations"
msgstr "Si está disponible, los precios internos anulan los cálculos del rango de precios"

#: common/setting/system.py:606
msgid "Enable label printing"
msgstr "Habilitar impresión de etiquetas"

#: common/setting/system.py:607
msgid "Enable label printing from the web interface"
msgstr "Habilitar impresión de etiquetas desde la interfaz web"

#: common/setting/system.py:612
msgid "Label Image DPI"
msgstr "PPP de la imagen de etiqueta"

#: common/setting/system.py:614
msgid "DPI resolution when generating image files to supply to label printing plugins"
msgstr "Resolución DPI al generar archivos de imagen que suministrar para etiquetar complementos de impresión"

#: common/setting/system.py:620
msgid "Enable Reports"
msgstr "Habilitar informes"

#: common/setting/system.py:621
msgid "Enable generation of reports"
msgstr "Habilitar generación de informes"

#: common/setting/system.py:626
msgid "Debug Mode"
msgstr "Modo de depuración"

#: common/setting/system.py:627
msgid "Generate reports in debug mode (HTML output)"
msgstr "Generar informes en modo de depuración (salida HTML)"

#: common/setting/system.py:632
msgid "Log Report Errors"
msgstr "Registrar errores de reportes"

#: common/setting/system.py:633
msgid "Log errors which occur when generating reports"
msgstr "Registrar errores ocurridos al generar reportes"

#: common/setting/system.py:638 plugin/builtin/labels/label_sheet.py:29
#: report/models.py:365
msgid "Page Size"
msgstr "Tamaño de página"

#: common/setting/system.py:639
msgid "Default page size for PDF reports"
msgstr "Tamaño de página predeterminado para informes PDF"

#: common/setting/system.py:644
msgid "Globally Unique Serials"
msgstr "Seriales únicos globalmente"

#: common/setting/system.py:645
msgid "Serial numbers for stock items must be globally unique"
msgstr "Los números de serie para los artículos de inventario deben ser únicos globalmente"

#: common/setting/system.py:650
msgid "Autofill Serial Numbers"
msgstr "Autollenar números de serie"

#: common/setting/system.py:651
msgid "Autofill serial numbers in forms"
msgstr "Autorellenar números de serie en formularios"

#: common/setting/system.py:656
msgid "Delete Depleted Stock"
msgstr "Eliminar existencias agotadas"

#: common/setting/system.py:657
msgid "Determines default behavior when a stock item is depleted"
msgstr "Determina el comportamiento por defecto al agotarse un artículo del inventario"

#: common/setting/system.py:662
msgid "Batch Code Template"
msgstr "Plantilla de código de lote"

#: common/setting/system.py:663
msgid "Template for generating default batch codes for stock items"
msgstr "Plantilla para generar códigos de lote por defecto para artículos de almacén"

#: common/setting/system.py:667
msgid "Stock Expiry"
msgstr "Expiración de stock"

#: common/setting/system.py:668
msgid "Enable stock expiry functionality"
msgstr "Habilitar la funcionalidad de expiración de stock"

#: common/setting/system.py:673
msgid "Sell Expired Stock"
msgstr "Vender existencias caducadas"

#: common/setting/system.py:674
msgid "Allow sale of expired stock"
msgstr "Permitir venta de existencias caducadas"

#: common/setting/system.py:679
msgid "Stock Stale Time"
msgstr "Tiempo histórico de Stock"

#: common/setting/system.py:681
msgid "Number of days stock items are considered stale before expiring"
msgstr "Número de días de artículos de stock se consideran obsoletos antes de caducar"

#: common/setting/system.py:688
msgid "Build Expired Stock"
msgstr "Crear Stock Caducado"

#: common/setting/system.py:689
msgid "Allow building with expired stock"
msgstr "Permitir crear con stock caducado"

#: common/setting/system.py:694
msgid "Stock Ownership Control"
msgstr "Control de Stock"

#: common/setting/system.py:695
msgid "Enable ownership control over stock locations and items"
msgstr "Habilitar control de propiedad sobre ubicaciones de stock y artículos"

#: common/setting/system.py:700
msgid "Stock Location Default Icon"
msgstr "Icono por defecto de ubicación de almacén"

#: common/setting/system.py:701
msgid "Stock location default icon (empty means no icon)"
msgstr "Icono por defecto de ubicación de almacén (vacío significa que no hay icono)"

#: common/setting/system.py:706
msgid "Show Installed Stock Items"
msgstr "Mostrar Articulos de Stock Instalados"

#: common/setting/system.py:707
msgid "Display installed stock items in stock tables"
msgstr "Mostrar los artículos de stock instalados en las tablas de stock"

#: common/setting/system.py:712
msgid "Check BOM when installing items"
msgstr ""

#: common/setting/system.py:714
msgid "Installed stock items must exist in the BOM for the parent part"
msgstr ""

#: common/setting/system.py:720
msgid "Allow Out of Stock Transfer"
msgstr "Permitir transferencia Sin Existencias"

#: common/setting/system.py:722
msgid "Allow stock items which are not in stock to be transferred between stock locations"
msgstr "Permitir que artículos del inventario sin existencias puedan ser transferidos entre ubicaciones de inventario"

#: common/setting/system.py:728
msgid "Build Order Reference Pattern"
msgstr "Patrón de Referencia de Ordenes de Armado"

#: common/setting/system.py:729
msgid "Required pattern for generating Build Order reference field"
msgstr "Patrón requerido para generar el campo de referencia de la Orden de Ensamblado"

#: common/setting/system.py:734 common/setting/system.py:788
#: common/setting/system.py:808 common/setting/system.py:844
msgid "Require Responsible Owner"
msgstr "Requerir Dueño Responsable"

#: common/setting/system.py:735 common/setting/system.py:789
#: common/setting/system.py:809 common/setting/system.py:845
msgid "A responsible owner must be assigned to each order"
msgstr "Se debe asignar un dueño responsable a cada orden"

#: common/setting/system.py:740
msgid "Require Active Part"
msgstr "Requerir Parte Activa"

#: common/setting/system.py:741
msgid "Prevent build order creation for inactive parts"
msgstr "Impedir la creación de órdenes de fabricación para partes inactivas"

#: common/setting/system.py:746
msgid "Require Locked Part"
msgstr "Requerir Parte Bloqueada"

#: common/setting/system.py:747
msgid "Prevent build order creation for unlocked parts"
msgstr "Impedir la creación de órdenes de fabricación para partes bloqueadas"

#: common/setting/system.py:752
msgid "Require Valid BOM"
msgstr ""

#: common/setting/system.py:753
msgid "Prevent build order creation unless BOM has been validated"
msgstr "Impedir la creación de órdenes de fabricación a menos que se haya validado la lista de materiales"

#: common/setting/system.py:758
msgid "Require Closed Child Orders"
msgstr ""

#: common/setting/system.py:760
msgid "Prevent build order completion until all child orders are closed"
msgstr "Prevenir la finalización de la orden de construcción hasta que todas las órdenes hijas estén cerradas"

#: common/setting/system.py:766
msgid "Block Until Tests Pass"
msgstr "Bloquear hasta que los Tests pasen"

#: common/setting/system.py:768
msgid "Prevent build outputs from being completed until all required tests pass"
msgstr "Evitar que las construcciones sean completadas hasta que todas las pruebas requeridas pasen"

#: common/setting/system.py:774
msgid "Enable Return Orders"
msgstr "Habilitar órdenes de devolución"

#: common/setting/system.py:775
msgid "Enable return order functionality in the user interface"
msgstr "Habilitar la funcionalidad de orden de devolución en la interfaz de usuario"

#: common/setting/system.py:780
msgid "Return Order Reference Pattern"
msgstr "Patrón de referencia de orden de devolución"

#: common/setting/system.py:782
msgid "Required pattern for generating Return Order reference field"
msgstr "Patrón requerido para generar el campo de referencia de devolución de la orden"

#: common/setting/system.py:794
msgid "Edit Completed Return Orders"
msgstr "Editar ordenes de devolución completadas"

#: common/setting/system.py:796
msgid "Allow editing of return orders after they have been completed"
msgstr "Permitir la edición de ordenes de devolución después de que hayan sido completados"

#: common/setting/system.py:802
msgid "Sales Order Reference Pattern"
msgstr "Patrón de Referencia de Ordenes de Venta"

#: common/setting/system.py:803
msgid "Required pattern for generating Sales Order reference field"
msgstr "Patrón requerido para generar el campo de referencia de la orden de venta"

#: common/setting/system.py:814
msgid "Sales Order Default Shipment"
msgstr "Envío Predeterminado de Ordenes de Venta"

#: common/setting/system.py:815
msgid "Enable creation of default shipment with sales orders"
msgstr "Habilitar la creación de envío predeterminado con ordenes de entrega"

#: common/setting/system.py:820
msgid "Edit Completed Sales Orders"
msgstr "Editar Ordenes de Venta Completados"

#: common/setting/system.py:822
msgid "Allow editing of sales orders after they have been shipped or completed"
msgstr "Permitir la edición de ordenes de venta después de que hayan sido enviados o completados"

#: common/setting/system.py:828
msgid "Mark Shipped Orders as Complete"
msgstr "Marcar pedidos enviados como completados"

#: common/setting/system.py:830
msgid "Sales orders marked as shipped will automatically be completed, bypassing the \"shipped\" status"
msgstr "Los pedidos marcados como enviados se completarán automáticamente, evitando el estado de \"envío\""

#: common/setting/system.py:836
msgid "Purchase Order Reference Pattern"
msgstr "Patrón de Referencia de Orden de Compra"

#: common/setting/system.py:838
msgid "Required pattern for generating Purchase Order reference field"
msgstr "Patrón requerido para generar el campo de referencia de la Orden de Compra"

#: common/setting/system.py:850
msgid "Edit Completed Purchase Orders"
msgstr "Editar Ordenes de Compra Completados"

#: common/setting/system.py:852
msgid "Allow editing of purchase orders after they have been shipped or completed"
msgstr "Permitir la edición de órdenes de venta después de que hayan sido enviados o completados"

#: common/setting/system.py:858
msgid "Convert Currency"
msgstr "Convertir moneda"

#: common/setting/system.py:859
msgid "Convert item value to base currency when receiving stock"
msgstr ""

#: common/setting/system.py:864
msgid "Auto Complete Purchase Orders"
msgstr "Autocompletar Ordenes de compra"

#: common/setting/system.py:866
msgid "Automatically mark purchase orders as complete when all line items are received"
msgstr "Marcar automáticamente las órdenes de compra como completas cuando se reciben todos los artículos de línea"

#: common/setting/system.py:873
msgid "Enable password forgot"
msgstr "Habilitar función de contraseña olvidada"

#: common/setting/system.py:874
msgid "Enable password forgot function on the login pages"
msgstr "Activar la función olvido de contraseña en las páginas de inicio de sesión"

#: common/setting/system.py:879
msgid "Enable registration"
msgstr "Habilitar registro"

#: common/setting/system.py:880
msgid "Enable self-registration for users on the login pages"
msgstr "Activar auto-registro para usuarios en las páginas de inicio de sesión"

#: common/setting/system.py:885
msgid "Enable SSO"
msgstr "Habilitar SSO"

#: common/setting/system.py:886
msgid "Enable SSO on the login pages"
msgstr "Habilitar SSO en las páginas de inicio de sesión"

#: common/setting/system.py:891
msgid "Enable SSO registration"
msgstr "Habilitar registro SSO"

#: common/setting/system.py:893
msgid "Enable self-registration via SSO for users on the login pages"
msgstr "Activar autoregistro a través de SSO para usuarios en las páginas de inicio de sesión"

#: common/setting/system.py:899
msgid "Enable SSO group sync"
msgstr "Habilitar sincronización de grupo SSO"

#: common/setting/system.py:901
msgid "Enable synchronizing InvenTree groups with groups provided by the IdP"
msgstr "Habilitar la sincronización de grupos de Inventree con grupos proporcionados por el IdP"

#: common/setting/system.py:907
msgid "SSO group key"
msgstr "Clave de grupo SSO"

#: common/setting/system.py:908
msgid "The name of the groups claim attribute provided by the IdP"
msgstr "El nombre del atributo reclamado por el grupo proporcionado por el IdP"

#: common/setting/system.py:913
msgid "SSO group map"
msgstr "Mapa del grupo SSO"

#: common/setting/system.py:915
msgid "A mapping from SSO groups to local InvenTree groups. If the local group does not exist, it will be created."
msgstr "Un mapeo de grupos SSO a grupos de Inventree locales. Si el grupo local no existe, se creará."

#: common/setting/system.py:921
msgid "Remove groups outside of SSO"
msgstr "Eliminar grupos fuera de SSO"

#: common/setting/system.py:923
msgid "Whether groups assigned to the user should be removed if they are not backend by the IdP. Disabling this setting might cause security issues"
msgstr ""

#: common/setting/system.py:929
msgid "Email required"
msgstr "Email requerido"

#: common/setting/system.py:930
msgid "Require user to supply mail on signup"
msgstr "Requiere usuario para suministrar correo al registrarse"

#: common/setting/system.py:935
msgid "Auto-fill SSO users"
msgstr "Auto-rellenar usuarios SSO"

#: common/setting/system.py:936
msgid "Automatically fill out user-details from SSO account-data"
msgstr "Rellenar automáticamente los datos de usuario de la cuenta SSO"

#: common/setting/system.py:941
msgid "Mail twice"
msgstr "Correo dos veces"

#: common/setting/system.py:942
msgid "On signup ask users twice for their mail"
msgstr "Al registrarse pregunte dos veces a los usuarios por su correo"

#: common/setting/system.py:947
msgid "Password twice"
msgstr "Contraseña dos veces"

#: common/setting/system.py:948
msgid "On signup ask users twice for their password"
msgstr "Al registrarse, preguntar dos veces a los usuarios por su contraseña"

#: common/setting/system.py:953
msgid "Allowed domains"
msgstr "Dominios permitidos"

#: common/setting/system.py:955
msgid "Restrict signup to certain domains (comma-separated, starting with @)"
msgstr "Restringir el registro a ciertos dominios (separados por comas, comenzando por @)"

#: common/setting/system.py:961
msgid "Group on signup"
msgstr "Grupo al registrarse"

#: common/setting/system.py:963
msgid "Group to which new users are assigned on registration. If SSO group sync is enabled, this group is only set if no group can be assigned from the IdP."
msgstr "Grupo al que se asignan nuevos usuarios al registrarse. Si la sincronización de grupo SSO está activada, este grupo sólo se establece si no se puede asignar ningún grupo desde el IdP."

#: common/setting/system.py:969
msgid "Enforce MFA"
msgstr "Forzar MFA"

#: common/setting/system.py:970
msgid "Users must use multifactor security."
msgstr "Los usuarios deben utilizar seguridad multifactor."

#: common/setting/system.py:975
msgid "Check plugins on startup"
msgstr "Comprobar complementos al iniciar"

#: common/setting/system.py:977
msgid "Check that all plugins are installed on startup - enable in container environments"
msgstr "Comprobar que todos los complementos están instalados en el arranque - habilitar en entornos de contenedores"

#: common/setting/system.py:984
msgid "Check for plugin updates"
msgstr "Revisar actualizaciones del plugin"

#: common/setting/system.py:985
msgid "Enable periodic checks for updates to installed plugins"
msgstr "Habilitar comprobaciones periódicas para actualizaciones de plugins instalados"

#: common/setting/system.py:991
msgid "Enable URL integration"
msgstr "Habilitar integración de URL"

#: common/setting/system.py:992
msgid "Enable plugins to add URL routes"
msgstr "Habilitar plugins para añadir rutas de URL"

#: common/setting/system.py:998
msgid "Enable navigation integration"
msgstr "Habilitar integración de navegación"

#: common/setting/system.py:999
msgid "Enable plugins to integrate into navigation"
msgstr "Habilitar plugins para integrar en la navegación"

#: common/setting/system.py:1005
msgid "Enable app integration"
msgstr "Habilitar integración de la aplicación"

#: common/setting/system.py:1006
msgid "Enable plugins to add apps"
msgstr "Habilitar plugins para añadir aplicaciones"

#: common/setting/system.py:1012
msgid "Enable schedule integration"
msgstr "Habilitar integración de programación"

#: common/setting/system.py:1013
msgid "Enable plugins to run scheduled tasks"
msgstr "Habilitar plugins para ejecutar tareas programadas"

#: common/setting/system.py:1019
msgid "Enable event integration"
msgstr "Habilitar integración de eventos"

#: common/setting/system.py:1020
msgid "Enable plugins to respond to internal events"
msgstr "Habilitar plugins para responder a eventos internos"

#: common/setting/system.py:1026
msgid "Enable interface integration"
msgstr "Habilitar integración de interfaz"

#: common/setting/system.py:1027
msgid "Enable plugins to integrate into the user interface"
msgstr "Habilitar complementos para integrar en la interfaz de usuario"

#: common/setting/system.py:1033
msgid "Enable project codes"
msgstr ""

#: common/setting/system.py:1034
msgid "Enable project codes for tracking projects"
msgstr "Habilitar códigos de proyecto para rastrear proyectos"

#: common/setting/system.py:1039
msgid "Stocktake Functionality"
msgstr "Funcionalidad de inventario"

#: common/setting/system.py:1041
msgid "Enable stocktake functionality for recording stock levels and calculating stock value"
msgstr "Habilite la funcionalidad de inventario para registrar los niveles de existencias y calcular el valor de las existencias"

#: common/setting/system.py:1047
msgid "Exclude External Locations"
msgstr "Excluir Ubicaciones Externas"

#: common/setting/system.py:1049
msgid "Exclude stock items in external locations from stocktake calculations"
msgstr "Excluir artículos en existencia en ubicaciones externas de los cálculos de inventario"

#: common/setting/system.py:1055
msgid "Automatic Stocktake Period"
msgstr "Periodo de inventario automático"

#: common/setting/system.py:1057
msgid "Number of days between automatic stocktake recording (set to zero to disable)"
msgstr "Número de días entre el registro automático del inventario (establecer en cero para desactivarlo)"

#: common/setting/system.py:1063
msgid "Report Deletion Interval"
msgstr "Intervalo de borrado de informe"

#: common/setting/system.py:1065
msgid "Stocktake reports will be deleted after specified number of days"
msgstr "Los informes de inventario se eliminarán después de un número de días especificado"

#: common/setting/system.py:1072
msgid "Display Users full names"
msgstr "Mostrar nombres completos de los usuarios"

#: common/setting/system.py:1073
msgid "Display Users full names instead of usernames"
msgstr "Mostrar nombres completos de usuarios en lugar de nombres de usuario"

#: common/setting/system.py:1078
msgid "Display User Profiles"
msgstr ""

#: common/setting/system.py:1079
msgid "Display Users Profiles on their profile page"
msgstr ""

#: common/setting/system.py:1084
msgid "Enable Test Station Data"
msgstr "Habilitar datos de estación de prueba"

#: common/setting/system.py:1085
msgid "Enable test station data collection for test results"
msgstr "Habilitar la recolección de datos de estaciones de prueba para resultados de prueba"

#: common/setting/system.py:1090
msgid "Create Template on Upload"
msgstr "Crear plantilla al subir"

#: common/setting/system.py:1092
msgid "Create a new test template when uploading test data which does not match an existing template"
msgstr "Crear una nueva plantilla de prueba al subir datos de prueba que no coincidan con una plantilla existente"

#: common/setting/user.py:23
msgid "Inline label display"
msgstr "Mostrar etiqueta interior"

#: common/setting/user.py:25
msgid "Display PDF labels in the browser, instead of downloading as a file"
msgstr "Mostrar etiquetas PDF en el navegador, en lugar de descargar como un archivo"

#: common/setting/user.py:31
msgid "Default label printer"
msgstr "Impresora predeterminada"

#: common/setting/user.py:32
msgid "Configure which label printer should be selected by default"
msgstr "Configure qué etiqueta de impresión debería ser seleccionada por defecto"

#: common/setting/user.py:37
msgid "Inline report display"
msgstr "Mostrar informe en línea"

#: common/setting/user.py:39
msgid "Display PDF reports in the browser, instead of downloading as a file"
msgstr "Mostrar informes PDF en el navegador, en lugar de descargar como un archivo"

#: common/setting/user.py:45
msgid "Search Parts"
msgstr "Buscar partes"

#: common/setting/user.py:46
msgid "Display parts in search preview window"
msgstr "Mostrar partes en la ventana de vista previa de búsqueda"

#: common/setting/user.py:51
msgid "Search Supplier Parts"
msgstr "Buscar partes de proveedor"

#: common/setting/user.py:52
msgid "Display supplier parts in search preview window"
msgstr "Mostrar partes de proveedores en la ventana de vista previa de búsqueda"

#: common/setting/user.py:57
msgid "Search Manufacturer Parts"
msgstr "Buscar Partes del Fabricante"

#: common/setting/user.py:58
msgid "Display manufacturer parts in search preview window"
msgstr "Mostrar partes de productores en la ventana de vista previa de búsqueda"

#: common/setting/user.py:63
msgid "Hide Inactive Parts"
msgstr "Ocultar Partes Inactivas"

#: common/setting/user.py:64
msgid "Excluded inactive parts from search preview window"
msgstr "Excluir las partes inactivas de la ventana de previsualización de búsqueda"

#: common/setting/user.py:69
msgid "Search Categories"
msgstr "Buscar categorías"

#: common/setting/user.py:70
msgid "Display part categories in search preview window"
msgstr "Mostrar categorias de la parte en la ventana de previsualización de búsqueda"

#: common/setting/user.py:75
msgid "Search Stock"
msgstr "Buscar inventario"

#: common/setting/user.py:76
msgid "Display stock items in search preview window"
msgstr "Mostrar artículos del stock en la ventana de previsualización de búsqueda"

#: common/setting/user.py:81
msgid "Hide Unavailable Stock Items"
msgstr "Ocultar Artículos del Stock Agotados"

#: common/setting/user.py:83
msgid "Exclude stock items which are not available from the search preview window"
msgstr "Excluir artículos de stock que no están disponibles en la ventana de previsualización de búsqueda"

#: common/setting/user.py:89
msgid "Search Locations"
msgstr "Buscar ubicaciones"

#: common/setting/user.py:90
msgid "Display stock locations in search preview window"
msgstr "Mostrar ubicaciones de almacén en la ventana de vista previa de búsqueda"

#: common/setting/user.py:95
msgid "Search Companies"
msgstr "Buscar empresas"

#: common/setting/user.py:96
msgid "Display companies in search preview window"
msgstr "Mostrar empresas en la ventana de vista previa de búsqueda"

#: common/setting/user.py:101
msgid "Search Build Orders"
msgstr "Buscar Pedidos de Construcción"

#: common/setting/user.py:102
msgid "Display build orders in search preview window"
msgstr "Mostrar órdenes de fabricación en la ventana de vista previa de búsqueda"

#: common/setting/user.py:107
msgid "Search Purchase Orders"
msgstr "Buscar órdenes de compra"

#: common/setting/user.py:108
msgid "Display purchase orders in search preview window"
msgstr "Mostrar órdenes de compra en la ventana de vista previa de búsqueda"

#: common/setting/user.py:113
msgid "Exclude Inactive Purchase Orders"
msgstr "Excluir pedidos de compra inactivos"

#: common/setting/user.py:114
msgid "Exclude inactive purchase orders from search preview window"
msgstr "Excluir órdenes de compra inactivas de la ventana de vista previa de búsqueda"

#: common/setting/user.py:119
msgid "Search Sales Orders"
msgstr "Buscar órdenes de venta"

#: common/setting/user.py:120
msgid "Display sales orders in search preview window"
msgstr "Mostrar órdenes de venta en la ventana de vista previa de búsqueda"

#: common/setting/user.py:125
msgid "Exclude Inactive Sales Orders"
msgstr "Excluir órdenes de venta inactivas"

#: common/setting/user.py:126
msgid "Exclude inactive sales orders from search preview window"
msgstr "Excluir órdenes de venta inactivas de la ventana de vista previa de búsqueda"

#: common/setting/user.py:131
msgid "Search Sales Order Shipments"
msgstr ""

#: common/setting/user.py:132
msgid "Display sales order shipments in search preview window"
msgstr ""

#: common/setting/user.py:137
msgid "Search Return Orders"
msgstr "Buscar órdenes de devolución"

#: common/setting/user.py:138
msgid "Display return orders in search preview window"
msgstr "Mostrar órdenes de devolución en la ventana de vista previa de búsqueda"

#: common/setting/user.py:143
msgid "Exclude Inactive Return Orders"
msgstr ""

#: common/setting/user.py:144
msgid "Exclude inactive return orders from search preview window"
msgstr ""

#: common/setting/user.py:149
msgid "Search Preview Results"
msgstr "Resultados de la vista previa"

#: common/setting/user.py:151
msgid "Number of results to show in each section of the search preview window"
msgstr "Número de resultados a mostrar en cada sección de la ventana de vista previa"

#: common/setting/user.py:157
msgid "Regex Search"
msgstr "Búsqueda usando una expresión regular"

#: common/setting/user.py:158
msgid "Enable regular expressions in search queries"
msgstr "Habilitar expresiones regulares en las consultas de búsqueda"

#: common/setting/user.py:163
msgid "Whole Word Search"
msgstr "Búsqueda por palabra completa"

#: common/setting/user.py:164
msgid "Search queries return results for whole word matches"
msgstr "Las consultas de búsqueda devuelven resultados para palabras enteras coincidentes"

#: common/setting/user.py:169
msgid "Search Notes"
msgstr ""

#: common/setting/user.py:171
msgid "Search queries return results for matches from the item's notes"
msgstr ""

#: common/setting/user.py:177
msgid "Show Quantity in Forms"
msgstr "Mostrar cantidad en formularios"

#: common/setting/user.py:178
msgid "Display available part quantity in some forms"
msgstr "Mostrar la cantidad de partes disponibles en algunos formularios"

#: common/setting/user.py:183
msgid "Escape Key Closes Forms"
msgstr "Formularios de cierre de teclas de escape"

#: common/setting/user.py:184
msgid "Use the escape key to close modal forms"
msgstr "Usa la clave de escape para cerrar formularios modales"

#: common/setting/user.py:189
msgid "Fixed Navbar"
msgstr "Barra de navegación fija"

#: common/setting/user.py:190
msgid "The navbar position is fixed to the top of the screen"
msgstr "La posición de la barra de navegación se fija en la parte superior de la pantalla"

#: common/setting/user.py:195
msgid "Navigation Icons"
msgstr ""

#: common/setting/user.py:196
msgid "Display icons in the navigation bar"
msgstr ""

#: common/setting/user.py:201
msgid "Date Format"
msgstr "Formato de Fecha"

#: common/setting/user.py:202
msgid "Preferred format for displaying dates"
msgstr "Formato preferido para mostrar fechas"

#: common/setting/user.py:215
msgid "Part Scheduling"
msgstr "Planificación de partes"

#: common/setting/user.py:216
msgid "Display part scheduling information"
msgstr ""

#: common/setting/user.py:221
msgid "Part Stocktake"
msgstr ""

#: common/setting/user.py:223
msgid "Display part stocktake information (if stocktake functionality is enabled)"
msgstr ""

#: common/setting/user.py:229
msgid "Table String Length"
msgstr "Longitud del texto en las tablas"

#: common/setting/user.py:230
msgid "Maximum length limit for strings displayed in table views"
msgstr "Longitud máxima para textos en tablas"

#: common/setting/user.py:235
msgid "Show Last Breadcrumb"
msgstr ""

#: common/setting/user.py:236
msgid "Show the current page in breadcrumbs"
msgstr ""

#: common/setting/user.py:241
msgid "Receive error reports"
msgstr "Recibir reportes de error"

#: common/setting/user.py:242
msgid "Receive notifications for system errors"
msgstr "Recibir notificación de errores del sistema"

#: common/setting/user.py:247
msgid "Last used printing machines"
msgstr "Últimas impresoras usadas"

#: common/setting/user.py:248
msgid "Save the last used printing machines for a user"
msgstr ""

#: common/validators.py:35
msgid "No attachment model type provided"
msgstr ""

#: common/validators.py:41
msgid "Invalid attachment model type"
msgstr ""

#: common/validators.py:82
msgid "Minimum places cannot be greater than maximum places"
msgstr ""

#: common/validators.py:94
msgid "Maximum places cannot be less than minimum places"
msgstr ""

#: common/validators.py:105
msgid "An empty domain is not allowed."
msgstr "Un dominio vacío no está permitido."

#: common/validators.py:107
#, python-brace-format
msgid "Invalid domain name: {domain}"
msgstr "Nombre de dominio inválido: {domain}"

#: common/validators.py:123
msgid "Value must be uppercase"
msgstr ""

#: common/validators.py:129
msgid "Value must be a valid variable identifier"
msgstr ""

#: company/api.py:141
msgid "Part is Active"
msgstr "La parte está activa"

#: company/api.py:145
msgid "Manufacturer is Active"
msgstr "El fabricante está activo"

#: company/api.py:278
msgid "Supplier Part is Active"
msgstr ""

#: company/api.py:282
msgid "Internal Part is Active"
msgstr ""

#: company/api.py:287
msgid "Supplier is Active"
msgstr ""

#: company/api.py:299 company/models.py:498 company/serializers.py:444
#: part/serializers.py:577
msgid "Manufacturer"
msgstr "Fabricante"

#: company/api.py:306 company/models.py:97 company/models.py:370
#: stock/api.py:849
msgid "Company"
msgstr "Empresa"

#: company/api.py:316
msgid "Has Stock"
msgstr "Tiene Stock"

#: company/models.py:98
msgid "Companies"
msgstr "Empresas"

#: company/models.py:114
msgid "Company description"
msgstr "Descripción de la empresa"

#: company/models.py:115
msgid "Description of the company"
msgstr "Descripción de la empresa"

#: company/models.py:121
msgid "Website"
msgstr "Página web"

#: company/models.py:122
msgid "Company website URL"
msgstr "URL del sitio web de la empresa"

#: company/models.py:128
msgid "Phone number"
msgstr "Teléfono"

#: company/models.py:130
msgid "Contact phone number"
msgstr "Teléfono de contacto"

#: company/models.py:137
msgid "Contact email address"
msgstr "Correo electrónico de contacto"

#: company/models.py:142 company/models.py:274 order/models.py:500
#: users/models.py:563
msgid "Contact"
msgstr "Contacto"

#: company/models.py:144
msgid "Point of contact"
msgstr "Punto de contacto"

#: company/models.py:150
msgid "Link to external company information"
msgstr "Enlace a información externa de la empresa"

#: company/models.py:164
msgid "Is this company active?"
msgstr "¿Esta empresa está activa?"

#: company/models.py:169
msgid "Is customer"
msgstr "¿Es cliente?"

#: company/models.py:170
msgid "Do you sell items to this company?"
msgstr "¿Vendes artículos a esta empresa?"

#: company/models.py:175
msgid "Is supplier"
msgstr "¿Es proveedor?"

#: company/models.py:176
msgid "Do you purchase items from this company?"
msgstr "¿Compras artículos de esta empresa?"

#: company/models.py:181
msgid "Is manufacturer"
msgstr "¿Es productor?"

#: company/models.py:182
msgid "Does this company manufacture parts?"
msgstr "¿Esta empresa fabrica partes?"

#: company/models.py:190
msgid "Default currency used for this company"
msgstr "Moneda predeterminada utilizada para esta empresa"

#: company/models.py:313 order/models.py:510
msgid "Address"
msgstr "Dirección"

#: company/models.py:314
msgid "Addresses"
msgstr "Direcciones"

#: company/models.py:371
msgid "Select company"
msgstr "Seleccionar empresa"

#: company/models.py:376
msgid "Address title"
msgstr "Título de dirección"

#: company/models.py:377
msgid "Title describing the address entry"
msgstr "Título que describe la entrada de dirección"

#: company/models.py:383
msgid "Primary address"
msgstr "Dirección principal"

#: company/models.py:384
msgid "Set as primary address"
msgstr "Establecer como dirección principal"

#: company/models.py:389
msgid "Line 1"
msgstr "Línea 1"

#: company/models.py:390
msgid "Address line 1"
msgstr "Dirección línea 1"

#: company/models.py:396
msgid "Line 2"
msgstr "Línea 2"

#: company/models.py:397
msgid "Address line 2"
msgstr "Dirección línea 2"

#: company/models.py:403 company/models.py:404
msgid "Postal code"
msgstr "Código postal"

#: company/models.py:410
msgid "City/Region"
msgstr "Ciudad/región"

#: company/models.py:411
msgid "Postal code city/region"
msgstr "Código postal de ciudad/región"

#: company/models.py:417
msgid "State/Province"
msgstr "Estado/provincia"

#: company/models.py:418
msgid "State or province"
msgstr "Estado o provincia"

#: company/models.py:424
msgid "Country"
msgstr "País"

#: company/models.py:425
msgid "Address country"
msgstr "Dirección de país"

#: company/models.py:431
msgid "Courier shipping notes"
msgstr "Notas de envío de mensajería"

#: company/models.py:432
msgid "Notes for shipping courier"
msgstr "Notas para el mensajero de envío"

#: company/models.py:438
msgid "Internal shipping notes"
msgstr "Notas de envío internas"

#: company/models.py:439
msgid "Shipping notes for internal use"
msgstr "Notas de envío para uso interno"

#: company/models.py:446
msgid "Link to address information (external)"
msgstr "Enlace a información de dirección (externa)"

#: company/models.py:470 company/models.py:587 company/models.py:809
#: company/serializers.py:458
msgid "Manufacturer Part"
msgstr "Parte del fabricante"

#: company/models.py:487 company/models.py:777 stock/models.py:948
#: stock/serializers.py:480
msgid "Base Part"
msgstr "Parte base"

#: company/models.py:489 company/models.py:779
msgid "Select part"
msgstr "Seleccionar parte"

#: company/models.py:499
msgid "Select manufacturer"
msgstr "Seleccionar fabricante"

#: company/models.py:505 company/serializers.py:466 order/serializers.py:665
#: part/serializers.py:587
msgid "MPN"
msgstr ""

#: company/models.py:506 stock/serializers.py:625
msgid "Manufacturer Part Number"
msgstr "Número de parte de fabricante"

#: company/models.py:513
msgid "URL for external manufacturer part link"
msgstr "URL para el enlace de parte del fabricante externo"

#: company/models.py:522
msgid "Manufacturer part description"
msgstr "Descripción de la parte del fabricante"

#: company/models.py:575
msgid "Manufacturer Part Parameter"
msgstr ""

#: company/models.py:594
msgid "Parameter name"
msgstr "Nombre del parámetro"

#: company/models.py:601
msgid "Parameter value"
msgstr "Valor del parámetro"

#: company/models.py:608 part/models.py:1204 part/models.py:3844
msgid "Units"
msgstr "Unidades"

#: company/models.py:609
msgid "Parameter units"
msgstr "Unidades de parámetro"

#: company/models.py:717
msgid "Pack units must be compatible with the base part units"
msgstr "Las unidades de paquete deben ser compatibles con las unidades de partes de base"

#: company/models.py:724
msgid "Pack units must be greater than zero"
msgstr "Las unidades de paquete deben ser mayor que cero"

#: company/models.py:738
msgid "Linked manufacturer part must reference the same base part"
msgstr "La parte vinculada del fabricante debe hacer referencia a la misma parte base"

#: company/models.py:787 company/serializers.py:436 company/serializers.py:454
#: order/models.py:626 part/serializers.py:561
#: plugin/builtin/suppliers/digikey.py:25 plugin/builtin/suppliers/lcsc.py:26
#: plugin/builtin/suppliers/mouser.py:24 plugin/builtin/suppliers/tme.py:26
#: stock/api.py:516 templates/email/overdue_purchase_order.html:16
msgid "Supplier"
msgstr "Proveedor"

#: company/models.py:788
msgid "Select supplier"
msgstr "Seleccionar proveedor"

#: company/models.py:794 part/serializers.py:572
msgid "Supplier stock keeping unit"
msgstr "Unidad de mantenimiento de stock de proveedores"

#: company/models.py:800
msgid "Is this supplier part active?"
msgstr ""

#: company/models.py:810
msgid "Select manufacturer part"
msgstr "Seleccionar parte del fabricante"

#: company/models.py:817
msgid "URL for external supplier part link"
msgstr "URL del enlace de parte del proveedor externo"

#: company/models.py:826
msgid "Supplier part description"
msgstr "Descripción de la parte del proveedor"

#: company/models.py:833 order/serializers.py:819 order/serializers.py:2027
#: part/models.py:4356 part/models.py:4697
#: report/templates/report/inventree_bill_of_materials_report.html:140
#: report/templates/report/inventree_purchase_order_report.html:32
#: report/templates/report/inventree_return_order_report.html:27
#: report/templates/report/inventree_sales_order_report.html:32
#: report/templates/report/inventree_stock_location_report.html:105
#: stock/serializers.py:838
msgid "Note"
msgstr "Nota"

#: company/models.py:842 part/models.py:2182
msgid "base cost"
msgstr "costo base"

#: company/models.py:843 part/models.py:2183
msgid "Minimum charge (e.g. stocking fee)"
msgstr "Cargo mínimo (p. ej., cuota de almacenamiento)"

#: company/models.py:850 order/serializers.py:811 stock/models.py:979
#: stock/serializers.py:1666
msgid "Packaging"
msgstr "Paquetes"

#: company/models.py:851
msgid "Part packaging"
msgstr "Embalaje de partes"

#: company/models.py:856
msgid "Pack Quantity"
msgstr "Cantidad de paquete"

#: company/models.py:858
msgid "Total quantity supplied in a single pack. Leave empty for single items."
msgstr "Cantidad total suministrada en un solo paquete. Dejar vacío para artículos individuales."

#: company/models.py:877 part/models.py:2189
msgid "multiple"
msgstr "múltiple"

#: company/models.py:878
msgid "Order multiple"
msgstr "Pedido múltiple"

#: company/models.py:890
msgid "Quantity available from supplier"
msgstr "Cantidad disponible del proveedor"

#: company/models.py:896
msgid "Availability Updated"
msgstr "Disponibilidad actualizada"

#: company/models.py:897
msgid "Date of last update of availability data"
msgstr "Fecha de última actualización de los datos de disponibilidad"

#: company/models.py:1025
msgid "Supplier Price Break"
msgstr ""

#: company/serializers.py:166
msgid "Return the string representation for the primary address. This property exists for backwards compatibility."
msgstr ""

#: company/serializers.py:185
msgid "Default currency used for this supplier"
msgstr "Moneda predeterminada utilizada para este proveedor"

#: company/serializers.py:221
msgid "Company Name"
msgstr "Nombre de la empresa"

#: company/serializers.py:420 part/serializers.py:940 stock/serializers.py:498
msgid "In Stock"
msgstr "En Stock"

#: data_exporter/mixins.py:324 data_exporter/mixins.py:393
msgid "Error occurred during data export"
msgstr ""

#: data_exporter/mixins.py:375
msgid "Data export plugin returned incorrect data format"
msgstr ""

#: data_exporter/serializers.py:74
msgid "Export Format"
msgstr ""

#: data_exporter/serializers.py:75
msgid "Select export file format"
msgstr ""

#: data_exporter/serializers.py:82
msgid "Export Plugin"
msgstr ""

#: data_exporter/serializers.py:83
msgid "Select export plugin"
msgstr ""

#: generic/states/fields.py:146
msgid "Additional status information for this item"
msgstr "Información adicional de estado para este artículo"

#: generic/states/fields.py:160
msgid "Custom status key"
msgstr "Clave personalizada de estado"

#: generic/states/serializers.py:16 plugin/models.py:45 users/models.py:119
msgid "Key"
msgstr "Clave"

#: generic/states/serializers.py:26
msgid "Custom"
msgstr ""

#: generic/states/serializers.py:37
msgid "Class"
msgstr ""

#: generic/states/serializers.py:40
msgid "Values"
msgstr "Valores"

#: generic/states/tests.py:22 order/status_codes.py:13
msgid "Placed"
msgstr "Colocado"

#: generic/states/validators.py:21
msgid "Invalid status code"
msgstr "Código de estado no válido"

#: importer/models.py:70
msgid "Data File"
msgstr "Archivo de datos"

#: importer/models.py:71
msgid "Data file to import"
msgstr "Archivo de datos a importar"

#: importer/models.py:80
msgid "Columns"
msgstr "Columnas"

#: importer/models.py:87
msgid "Target model type for this import session"
msgstr ""

#: importer/models.py:93
msgid "Import status"
msgstr ""

#: importer/models.py:103
msgid "Field Defaults"
msgstr "Valores predeterminados del campo"

#: importer/models.py:110
msgid "Field Overrides"
msgstr ""

#: importer/models.py:117
msgid "Field Filters"
msgstr "Filtros del campo"

#: importer/models.py:250
msgid "Some required fields have not been mapped"
msgstr "Algunos campos requeridos no han sido mapeados"

#: importer/models.py:407
msgid "Column is already mapped to a database field"
msgstr "La columna ya fue mapeada a un campo de la base de datos"

#: importer/models.py:412
msgid "Field is already mapped to a data column"
msgstr ""

#: importer/models.py:421
msgid "Column mapping must be linked to a valid import session"
msgstr ""

#: importer/models.py:426
msgid "Column does not exist in the data file"
msgstr "La columna no existe en el archivo de datos"

#: importer/models.py:433
msgid "Field does not exist in the target model"
msgstr "El campo no existe en el modelo destino"

#: importer/models.py:437
msgid "Selected field is read-only"
msgstr "El campo seleccionado es de solo lectura"

#: importer/models.py:442 importer/models.py:513
msgid "Import Session"
msgstr "Sesión de importación"

#: importer/models.py:446
msgid "Field"
msgstr "Campo"

#: importer/models.py:448
msgid "Column"
msgstr "Columna"

#: importer/models.py:517
msgid "Row Index"
msgstr "Número de fila"

#: importer/models.py:520
msgid "Original row data"
msgstr "Datos de la fila original"

#: importer/models.py:525 machine/models.py:110
msgid "Errors"
msgstr "Errores"

#: importer/models.py:527 part/api.py:864
msgid "Valid"
msgstr "Válido"

#: importer/operations.py:28 importer/operations.py:49
msgid "Unsupported data file format"
msgstr "Formato de archivo de datos no soportado"

#: importer/operations.py:40
msgid "Failed to open data file"
msgstr "Error al abrir el archivo de datos"

#: importer/operations.py:51
msgid "Invalid data file dimensions"
msgstr "Dimensiones del archivo de datos inválidas"

#: importer/serializers.py:91
msgid "Invalid field defaults"
msgstr "Valores predeterminados del campo inválidos"

#: importer/serializers.py:104
msgid "Invalid field overrides"
msgstr ""

#: importer/serializers.py:117
msgid "Invalid field filters"
msgstr "Filtros del campo inválidos"

#: importer/serializers.py:178
msgid "Rows"
msgstr "Filas"

#: importer/serializers.py:179
msgid "List of row IDs to accept"
msgstr "Listado de IDs de fila a aceptar"

#: importer/serializers.py:192
msgid "No rows provided"
msgstr "No hay filas"

#: importer/serializers.py:196
msgid "Row does not belong to this session"
msgstr "La fila no pertenece a esta sesión"

#: importer/serializers.py:199
msgid "Row contains invalid data"
msgstr "La fila contiene datos inválidos"

#: importer/serializers.py:202
msgid "Row has already been completed"
msgstr "La fila ya se ha completado"

#: importer/status_codes.py:13
msgid "Initializing"
msgstr "Inicializando"

#: importer/status_codes.py:18
msgid "Mapping Columns"
msgstr "Mapeando columnas"

#: importer/status_codes.py:21
msgid "Importing Data"
msgstr "Importando datos"

#: importer/status_codes.py:24
msgid "Processing Data"
msgstr "Procesando datos"

#: importer/validators.py:21
msgid "Data file exceeds maximum size limit"
msgstr "El archivo de datos excede el tamaño máximo"

#: importer/validators.py:26
msgid "Data file contains no headers"
msgstr "El archivo de datos no tiene cabeceras"

#: importer/validators.py:29
msgid "Data file contains too many columns"
msgstr "El archivo de datos tiene demasiadas columnas"

#: importer/validators.py:32
msgid "Data file contains too many rows"
msgstr "El archivo de datos tiene demasiadas filas"

#: importer/validators.py:53
msgid "Value must be a valid dictionary object"
msgstr ""

#: machine/machine_types/label_printer.py:212
msgid "Copies"
msgstr "Copias"

#: machine/machine_types/label_printer.py:213
msgid "Number of copies to print for each label"
msgstr "Número de copias a imprimir para cada etiqueta"

#: machine/machine_types/label_printer.py:228
msgid "Connected"
msgstr "Conectado"

#: machine/machine_types/label_printer.py:229 order/api.py:1679
msgid "Unknown"
msgstr "Desconocido"

#: machine/machine_types/label_printer.py:230
msgid "Printing"
msgstr "Impresión"

#: machine/machine_types/label_printer.py:231
msgid "No media"
msgstr "Sin archivos multimedia"

#: machine/machine_types/label_printer.py:232
msgid "Paper jam"
msgstr "Atascamiento de papel"

#: machine/machine_types/label_printer.py:233
msgid "Disconnected"
msgstr "Desconectado"

#: machine/machine_types/label_printer.py:240
msgid "Label Printer"
msgstr "Impresora de Etiquetas"

#: machine/machine_types/label_printer.py:241
msgid "Directly print labels for various items."
msgstr "Imprime directamente etiquetas para varios artículos."

#: machine/machine_types/label_printer.py:247
msgid "Printer Location"
msgstr "Ubicación de la Impresora"

#: machine/machine_types/label_printer.py:248
msgid "Scope the printer to a specific location"
msgstr ""

#: machine/models.py:25
msgid "Name of machine"
msgstr "Nombre de la máquina"

#: machine/models.py:29
msgid "Machine Type"
msgstr "Tipo de Máquina"

#: machine/models.py:29
msgid "Type of machine"
msgstr "Tipo de máquina"

#: machine/models.py:34 machine/models.py:146
msgid "Driver"
msgstr "Controlador"

#: machine/models.py:35
msgid "Driver used for the machine"
msgstr "Controlador usado para la máquina"

#: machine/models.py:39
msgid "Machines can be disabled"
msgstr "Las máquinas pueden ser desactivadas"

#: machine/models.py:95
msgid "Driver available"
msgstr "Controlador disponible"

#: machine/models.py:100
msgid "No errors"
msgstr "Sin errores"

#: machine/models.py:105
msgid "Initialized"
msgstr "Inicializado"

#: machine/models.py:117
msgid "Machine status"
msgstr "Estado de máquina"

#: machine/models.py:145
msgid "Machine"
msgstr "Máquina"

#: machine/models.py:151
msgid "Machine Config"
msgstr ""

#: machine/models.py:156
msgid "Config type"
msgstr "Tipo de configuración"

#: order/api.py:118
msgid "Order Reference"
msgstr "Referencia del pedido"

#: order/api.py:146 order/api.py:1139
msgid "Outstanding"
msgstr "Destacado"

#: order/api.py:162
msgid "Has Project Code"
msgstr "Tiene Código de Proyecto"

#: order/api.py:176 order/models.py:475
msgid "Created By"
msgstr "Creado por"

#: order/api.py:180
msgid "Created Before"
msgstr "Creado antes de"

#: order/api.py:184
msgid "Created After"
msgstr "Creado después de"

#: order/api.py:188
msgid "Has Start Date"
msgstr "Tiene fecha inicial"

#: order/api.py:196
msgid "Start Date Before"
msgstr "Fecha de inicio anterior"

#: order/api.py:200
msgid "Start Date After"
msgstr "Fecha de inicio después"

#: order/api.py:204
msgid "Has Target Date"
msgstr "Tiene fecha límite"

#: order/api.py:212
msgid "Target Date Before"
msgstr "Fecha objetivo antes"

#: order/api.py:216
msgid "Target Date After"
msgstr "Fecha objetivo después"

#: order/api.py:267
msgid "Has Pricing"
msgstr "Tiene Precio"

#: order/api.py:320 order/api.py:774 order/api.py:1375
msgid "Completed Before"
msgstr "Completado antes de"

#: order/api.py:324 order/api.py:778 order/api.py:1379
msgid "Completed After"
msgstr "Completado después de"

#: order/api.py:491 order/api.py:876 order/api.py:1102 order/models.py:1685
#: order/models.py:1803 order/models.py:1854 order/models.py:2002
#: order/models.py:2168 order/models.py:2690 order/models.py:2756
msgid "Order"
msgstr "Orden"

#: order/api.py:495 order/api.py:914
msgid "Order Complete"
msgstr "Orden completada"

#: order/api.py:527 order/api.py:531 order/serializers.py:676
msgid "Internal Part"
msgstr "Componente interno"

#: order/api.py:549
msgid "Order Pending"
msgstr "Orden pendiente"

#: order/api.py:899
msgid "Completed"
msgstr "Completados"

#: order/api.py:1155
msgid "Has Shipment"
msgstr "Tiene envío"

#: order/api.py:1673 order/models.py:539 order/models.py:1686
#: order/models.py:1804
#: report/templates/report/inventree_purchase_order_report.html:14
#: stock/serializers.py:128 templates/email/overdue_purchase_order.html:15
msgid "Purchase Order"
msgstr "Orden de compra"

#: order/api.py:1675 order/models.py:1080 order/models.py:1855
#: order/models.py:2003 order/models.py:2169
#: report/templates/report/inventree_build_order_report.html:135
#: report/templates/report/inventree_sales_order_report.html:14
#: report/templates/report/inventree_sales_order_shipment_report.html:15
#: templates/email/overdue_sales_order.html:15
msgid "Sales Order"
msgstr "Orden de Venta"

#: order/api.py:1677 order/models.py:2340 order/models.py:2691
#: order/models.py:2757
#: report/templates/report/inventree_return_order_report.html:13
#: templates/email/overdue_return_order.html:15
msgid "Return Order"
msgstr "Orden de devolución"

#: order/models.py:89
#: report/templates/report/inventree_purchase_order_report.html:31
#: report/templates/report/inventree_sales_order_report.html:31
msgid "Total Price"
msgstr "Precio Total"

#: order/models.py:90
msgid "Total price for this order"
msgstr "Precio total para este pedido"

#: order/models.py:95 order/serializers.py:75
msgid "Order Currency"
msgstr "Moneda de pedido"

#: order/models.py:98 order/serializers.py:76
msgid "Currency for this order (leave blank to use company default)"
msgstr "Moneda para este pedido (dejar en blanco para utilizar el valor predeterminado de la empresa)"

#: order/models.py:323
msgid "This order is locked and cannot be modified"
msgstr "Este pedido está bloqueado y no puede ser modificado"

#: order/models.py:370
msgid "Contact does not match selected company"
msgstr "El contacto no coincide con la empresa seleccionada"

#: order/models.py:377
msgid "Start date must be before target date"
msgstr "La fecha de inicio debe ser anterior a la fecha de límite"

#: order/models.py:430
msgid "Order description (optional)"
msgstr "Descripción del pedido (opcional)"

#: order/models.py:439
msgid "Select project code for this order"
msgstr "Seleccione el código del proyecto para este pedido"

#: order/models.py:445 order/models.py:1586 order/models.py:2057
msgid "Link to external page"
msgstr "Enlace a Url externa"

#: order/models.py:452
msgid "Start date"
msgstr "Fecha de inicio"

#: order/models.py:453
msgid "Scheduled start date for this order"
msgstr "Fecha de inicio programada para este pedido"

#: order/models.py:459 order/models.py:1593 order/serializers.py:269
#: report/templates/report/inventree_build_order_report.html:125
msgid "Target Date"
msgstr "Fecha objetivo"

#: order/models.py:461
msgid "Expected date for order delivery. Order will be overdue after this date."
msgstr "Fecha esperada para la entrega del pedido. El pedido se retrasará después de esta fecha."

#: order/models.py:481
msgid "Issue Date"
msgstr "Fecha de emisión"

#: order/models.py:482
msgid "Date order was issued"
msgstr "Fecha de expedición del pedido"

#: order/models.py:490
msgid "User or group responsible for this order"
msgstr "Usuario o grupo responsable de este pedido"

#: order/models.py:501
msgid "Point of contact for this order"
msgstr "Punto de contacto para este pedido"

#: order/models.py:511
msgid "Company address for this order"
msgstr "Dirección de la empresa para este pedido"

#: order/models.py:602 order/models.py:1141
msgid "Order reference"
msgstr "Referencia del pedido"

#: order/models.py:611 order/models.py:1165 order/models.py:2428
#: stock/serializers.py:612 stock/serializers.py:1010 users/models.py:544
msgid "Status"
msgstr "Estado"

#: order/models.py:612
msgid "Purchase order status"
msgstr "Estado de la orden de compra"

#: order/models.py:627
msgid "Company from which the items are being ordered"
msgstr "Empresa de la cual se están encargando los artículos"

#: order/models.py:638
msgid "Supplier Reference"
msgstr "Referencia del proveedor"

#: order/models.py:639
msgid "Supplier order reference code"
msgstr "Código de referencia de pedido del proveedor"

#: order/models.py:648
msgid "received by"
msgstr "recibido por"

#: order/models.py:655 order/models.py:2443
msgid "Date order was completed"
msgstr "La fecha de pedido fue completada"

#: order/models.py:664 order/models.py:1733
msgid "Destination"
msgstr "Destinación"

#: order/models.py:665 order/models.py:1737
msgid "Destination for received items"
msgstr "Destino para los artículos recibidos"

#: order/models.py:711
msgid "Part supplier must match PO supplier"
msgstr "El proveedor de la parte debe coincidir con el proveedor de PO"

#: order/models.py:971
msgid "Quantity must be a positive number"
msgstr "La cantidad debe ser un número positivo"

#: order/models.py:1152 order/models.py:2415 stock/models.py:1001
#: stock/models.py:1002 stock/serializers.py:1405
#: templates/email/overdue_return_order.html:16
#: templates/email/overdue_sales_order.html:16
msgid "Customer"
msgstr "Cliente"

#: order/models.py:1153
msgid "Company to which the items are being sold"
msgstr "Empresa a la que se venden los artículos"

#: order/models.py:1166
msgid "Sales order status"
msgstr "Estado de la orden de venta"

#: order/models.py:1177 order/models.py:2435
msgid "Customer Reference "
msgstr "Referencia del cliente "

#: order/models.py:1178 order/models.py:2436
msgid "Customer order reference code"
msgstr "Código de referencia de pedido del cliente"

#: order/models.py:1182 order/models.py:2009
msgid "Shipment Date"
msgstr "Fecha de envío"

#: order/models.py:1191
msgid "shipped by"
msgstr "enviado por"

#: order/models.py:1230
msgid "Order is already complete"
msgstr "La orden ya fue completada"

#: order/models.py:1233
msgid "Order is already cancelled"
msgstr "La orden ya fue cancelada"

#: order/models.py:1237
msgid "Only an open order can be marked as complete"
msgstr "Sólo una orden abierta puede ser marcada como completa"

#: order/models.py:1241
msgid "Order cannot be completed as there are incomplete shipments"
msgstr "El pedido no se puede completar porque hay envíos incompletos"

#: order/models.py:1246
msgid "Order cannot be completed as there are incomplete allocations"
msgstr "El pedido no se puede completar ya que hay asignaciones incompletas"

#: order/models.py:1251
msgid "Order cannot be completed as there are incomplete line items"
msgstr "El pedido no se puede completar porque hay partidas incompletas"

#: order/models.py:1535 order/models.py:1548
msgid "The order is locked and cannot be modified"
msgstr "Este pedido está bloqueado y no puede ser modificado"

#: order/models.py:1556
msgid "Item quantity"
msgstr "Cantidad del artículo"

#: order/models.py:1573
msgid "Line item reference"
msgstr "Referencia de partida"

#: order/models.py:1580
msgid "Line item notes"
msgstr "Notas de partida"

#: order/models.py:1595
msgid "Target date for this line item (leave blank to use the target date from the order)"
msgstr "Fecha objetivo para esta partida (dejar en blanco para usar la fecha de destino de la orden)"

#: order/models.py:1616
msgid "Line item description (optional)"
msgstr "Descripción de partida (opcional)"

#: order/models.py:1623
msgid "Additional context for this line"
msgstr "Contexto adicional para esta línea"

#: order/models.py:1633
msgid "Unit price"
msgstr "Precio unitario"

#: order/models.py:1647
msgid "Purchase Order Line Item"
msgstr ""

#: order/models.py:1671
msgid "Supplier part must match supplier"
msgstr "La parte del proveedor debe coincidir con el proveedor"

#: order/models.py:1705
msgid "Supplier part"
msgstr "Parte del proveedor"

#: order/models.py:1712
msgid "Received"
msgstr "Recibido"

#: order/models.py:1713
msgid "Number of items received"
msgstr "Número de artículos recibidos"

#: order/models.py:1721 stock/models.py:1124 stock/serializers.py:679
msgid "Purchase Price"
msgstr "Precio de Compra"

#: order/models.py:1722
msgid "Unit purchase price"
msgstr "Precio de compra unitario"

#: order/models.py:1792
msgid "Purchase Order Extra Line"
msgstr ""

#: order/models.py:1821
msgid "Sales Order Line Item"
msgstr ""

#: order/models.py:1842
msgid "Virtual part cannot be assigned to a sales order"
msgstr "Una parte virtual no puede ser asignada a un pedido de venta"

#: order/models.py:1847
msgid "Only salable parts can be assigned to a sales order"
msgstr "Sólo las partes vendibles pueden ser asignadas a un pedido de venta"

#: order/models.py:1873
msgid "Sale Price"
msgstr "Precio de Venta"

#: order/models.py:1874
msgid "Unit sale price"
msgstr "Precio de venta unitario"

#: order/models.py:1883 order/status_codes.py:50
msgid "Shipped"
msgstr "Enviado"

#: order/models.py:1884
msgid "Shipped quantity"
msgstr "Cantidad enviada"

#: order/models.py:1978
msgid "Sales Order Shipment"
msgstr ""

#: order/models.py:2010
msgid "Date of shipment"
msgstr "Fecha del envío"

#: order/models.py:2016
msgid "Delivery Date"
msgstr "Fecha de entrega"

#: order/models.py:2017
msgid "Date of delivery of shipment"
msgstr "Fecha de entrega del envío"

#: order/models.py:2025
msgid "Checked By"
msgstr "Revisado por"

#: order/models.py:2026
msgid "User who checked this shipment"
msgstr "Usuario que revisó este envío"

#: order/models.py:2033 order/models.py:2265 order/serializers.py:1685
#: order/serializers.py:1809
#: report/templates/report/inventree_sales_order_shipment_report.html:14
msgid "Shipment"
msgstr "Envío"

#: order/models.py:2034
msgid "Shipment number"
msgstr "Número de envío"

#: order/models.py:2042
msgid "Tracking Number"
msgstr "Número de Seguimiento"

#: order/models.py:2043
msgid "Shipment tracking information"
msgstr "Información de seguimiento del envío"

#: order/models.py:2050
msgid "Invoice Number"
msgstr "Número de factura"

#: order/models.py:2051
msgid "Reference number for associated invoice"
msgstr "Número de referencia para la factura asociada"

#: order/models.py:2074
msgid "Shipment has already been sent"
msgstr "El envío ya ha sido enviado"

#: order/models.py:2077
msgid "Shipment has no allocated stock items"
msgstr "El envío no tiene artículos de stock asignados"

#: order/models.py:2157
msgid "Sales Order Extra Line"
msgstr ""

#: order/models.py:2186
msgid "Sales Order Allocation"
msgstr ""

#: order/models.py:2209 order/models.py:2211
msgid "Stock item has not been assigned"
msgstr "El artículo de stock no ha sido asignado"

#: order/models.py:2218
msgid "Cannot allocate stock item to a line with a different part"
msgstr "No se puede asignar el artículo de stock a una línea con una parte diferente"

#: order/models.py:2221
msgid "Cannot allocate stock to a line without a part"
msgstr "No se puede asignar stock a una línea sin una parte"

#: order/models.py:2224
msgid "Allocation quantity cannot exceed stock quantity"
msgstr "La cantidad de asignación no puede exceder la cantidad de stock"

#: order/models.py:2243 order/serializers.py:1555
msgid "Quantity must be 1 for serialized stock item"
msgstr "La cantidad debe ser 1 para el stock serializado"

#: order/models.py:2246
msgid "Sales order does not match shipment"
msgstr "La orden de venta no coincide con el envío"

#: order/models.py:2247 plugin/base/barcodes/api.py:642
msgid "Shipment does not match sales order"
msgstr "El envío no coincide con el pedido de venta"

#: order/models.py:2255
msgid "Line"
msgstr "Línea"

#: order/models.py:2266
msgid "Sales order shipment reference"
msgstr "Referencia del envío del pedido de venta"

#: order/models.py:2279 order/models.py:2698
msgid "Item"
msgstr "Ítem"

#: order/models.py:2280
msgid "Select stock item to allocate"
msgstr "Seleccionar artículo de stock para asignar"

#: order/models.py:2289
msgid "Enter stock allocation quantity"
msgstr "Especificar la cantidad de asignación de stock"

#: order/models.py:2404
msgid "Return Order reference"
msgstr "Referencia de la orden de devolución"

#: order/models.py:2416
msgid "Company from which items are being returned"
msgstr "Empresa de la cual se están devolviendo los artículos"

#: order/models.py:2429
msgid "Return order status"
msgstr "Estado de la orden de devolución"

#: order/models.py:2656
msgid "Return Order Line Item"
msgstr ""

#: order/models.py:2669
msgid "Stock item must be specified"
msgstr ""

#: order/models.py:2673
msgid "Return quantity exceeds stock quantity"
msgstr ""

#: order/models.py:2678
msgid "Return quantity must be greater than zero"
msgstr ""

#: order/models.py:2683
msgid "Invalid quantity for serialized stock item"
msgstr ""

#: order/models.py:2699
msgid "Select item to return from customer"
msgstr "Seleccionar el artículo a devolver del cliente"

#: order/models.py:2714
msgid "Received Date"
msgstr "Fecha de recepción"

#: order/models.py:2715
msgid "The date this this return item was received"
msgstr "La fecha en la que se recibió este artículo de devolución"

#: order/models.py:2727
msgid "Outcome"
msgstr "Resultado"

#: order/models.py:2728
msgid "Outcome for this line item"
msgstr "Salida para esta partida"

#: order/models.py:2735
msgid "Cost associated with return or repair for this line item"
msgstr "Costo asociado con la devolución o reparación para esta partida"

#: order/models.py:2745
msgid "Return Order Extra Line"
msgstr ""

#: order/serializers.py:89
msgid "Order ID"
msgstr "ID del Pedido"

#: order/serializers.py:89
msgid "ID of the order to duplicate"
msgstr "ID del pedido a duplicar"

#: order/serializers.py:95
msgid "Copy Lines"
msgstr "Copiar líneas"

#: order/serializers.py:96
msgid "Copy line items from the original order"
msgstr "Copiar elementos de línea del pedido original"

#: order/serializers.py:102
msgid "Copy Extra Lines"
msgstr "Copiar líneas adicionales"

#: order/serializers.py:103
msgid "Copy extra line items from the original order"
msgstr "Copiar elementos extra de la línea del pedido original"

#: order/serializers.py:116
#: report/templates/report/inventree_purchase_order_report.html:22
#: report/templates/report/inventree_return_order_report.html:19
#: report/templates/report/inventree_sales_order_report.html:22
msgid "Line Items"
msgstr "Partidas"

#: order/serializers.py:121
msgid "Completed Lines"
msgstr "Líneas completadas"

#: order/serializers.py:172
msgid "Duplicate Order"
msgstr "Duplicar pedido"

#: order/serializers.py:173
msgid "Specify options for duplicating this order"
msgstr "Especificar opciones para duplicar este pedido"

#: order/serializers.py:249
msgid "Invalid order ID"
msgstr "ID de pedido no válido"

#: order/serializers.py:388
msgid "Supplier Name"
msgstr "Nombre del proveedor"

#: order/serializers.py:430
msgid "Order cannot be cancelled"
msgstr "El pedido no puede ser cancelado"

#: order/serializers.py:445 order/serializers.py:1576
msgid "Allow order to be closed with incomplete line items"
msgstr "Permitir cerrar el pedido con partidas incompletas"

#: order/serializers.py:455 order/serializers.py:1586
msgid "Order has incomplete line items"
msgstr "El pedido tiene partidas incompletas"

#: order/serializers.py:608
msgid "Order is not open"
msgstr "El pedido no está abierto"

#: order/serializers.py:629
msgid "Auto Pricing"
msgstr "Precio automático"

#: order/serializers.py:631
msgid "Automatically calculate purchase price based on supplier part data"
msgstr "Calcular precio de compra automáticamente con base en los datos del proveedor"

#: order/serializers.py:641
msgid "Purchase price currency"
msgstr "Moneda del precio de compra"

#: order/serializers.py:649
msgid "Merge Items"
msgstr "Combinar artículos"

#: order/serializers.py:651
msgid "Merge items with the same part, destination and target date into one line item"
msgstr ""

#: order/serializers.py:658 part/serializers.py:571
msgid "SKU"
msgstr "SKU"

#: order/serializers.py:672 part/models.py:1089 part/serializers.py:412
msgid "Internal Part Number"
msgstr "Número de parte interna"

#: order/serializers.py:680
msgid "Internal Part Name"
msgstr "Nombre interno de parte"

#: order/serializers.py:696
msgid "Supplier part must be specified"
msgstr "Debe especificar la parte del proveedor"

#: order/serializers.py:699
msgid "Purchase order must be specified"
msgstr "La orden de compra debe especificarse"

#: order/serializers.py:707
msgid "Supplier must match purchase order"
msgstr "El proveedor debe coincidir con la orden de compra"

#: order/serializers.py:708
msgid "Purchase order must match supplier"
msgstr "La orden de compra debe coincidir con el proveedor"

#: order/serializers.py:754 order/serializers.py:1656
msgid "Line Item"
msgstr "Partida"

#: order/serializers.py:760
msgid "Line item does not match purchase order"
msgstr "La partida no coincide con la orden de compra"

#: order/serializers.py:770 order/serializers.py:917 order/serializers.py:2023
msgid "Select destination location for received items"
msgstr "Seleccione la ubicación de destino para los artículos recibidos"

#: order/serializers.py:786
msgid "Enter batch code for incoming stock items"
msgstr "Introduzca el código de lote para los artículos de almacén entrantes"

#: order/serializers.py:793 stock/models.py:1083 users/models.py:143
msgid "Expiry Date"
msgstr "Fecha de Expiración"

#: order/serializers.py:794
msgid "Enter expiry date for incoming stock items"
msgstr ""

#: order/serializers.py:802
msgid "Enter serial numbers for incoming stock items"
msgstr "Introduzca números de serie para artículos de almacén entrantes"

#: order/serializers.py:812
msgid "Override packaging information for incoming stock items"
msgstr ""

#: order/serializers.py:820 order/serializers.py:2028
msgid "Additional note for incoming stock items"
msgstr ""

#: order/serializers.py:827
msgid "Barcode"
msgstr "Código de barras"

#: order/serializers.py:828
msgid "Scanned barcode"
msgstr "Código de barras escaneado"

#: order/serializers.py:844
msgid "Barcode is already in use"
msgstr "Código de barras en uso"

#: order/serializers.py:867
msgid "An integer quantity must be provided for trackable parts"
msgstr "Debe proporcionarse una cantidad entera para las partes rastreables"

#: order/serializers.py:934 order/serializers.py:2047
msgid "Line items must be provided"
msgstr "Se deben proporcionar las partidas"

#: order/serializers.py:950
msgid "Destination location must be specified"
msgstr "Se requiere ubicación de destino"

#: order/serializers.py:961
msgid "Supplied barcode values must be unique"
msgstr "Los valores del código de barras deben ser únicos"

#: order/serializers.py:1092
msgid "Shipments"
msgstr "Envíos"

#: order/serializers.py:1096
msgid "Completed Shipments"
msgstr "Envíos completados"

#: order/serializers.py:1283
msgid "Sale price currency"
msgstr "Moneda del precio de venta"

#: order/serializers.py:1331
msgid "Allocated Items"
msgstr "Elementos asignados"

#: order/serializers.py:1458
msgid "No shipment details provided"
msgstr "No se proporcionaron detalles de envío"

#: order/serializers.py:1519 order/serializers.py:1665
msgid "Line item is not associated with this order"
msgstr "La partida no está asociada con este pedido"

#: order/serializers.py:1538
msgid "Quantity must be positive"
msgstr "La cantidad debe ser positiva"

#: order/serializers.py:1675
msgid "Enter serial numbers to allocate"
msgstr "Introduzca números de serie para asignar"

#: order/serializers.py:1697 order/serializers.py:1817
msgid "Shipment has already been shipped"
msgstr "El envío ya ha sido enviado"

#: order/serializers.py:1700 order/serializers.py:1820
msgid "Shipment is not associated with this order"
msgstr "El envío no está asociado con este pedido"

#: order/serializers.py:1755
msgid "No match found for the following serial numbers"
msgstr "No se han encontrado coincidencias para los siguientes números de serie"

#: order/serializers.py:1762
msgid "The following serial numbers are unavailable"
msgstr "Los siguientes números de serie no están disponibles"

#: order/serializers.py:1989
msgid "Return order line item"
msgstr "Partida de orden de devolución"

#: order/serializers.py:1999
msgid "Line item does not match return order"
msgstr "La partida no coincide con la orden de devolución"

#: order/serializers.py:2002
msgid "Line item has already been received"
msgstr "La partida ya ha sido recibida"

#: order/serializers.py:2039
msgid "Items can only be received against orders which are in progress"
msgstr "Los artículos sólo pueden ser recibidos contra pedidos en curso"

#: order/serializers.py:2131
msgid "Quantity to return"
msgstr "Cantidad a devolver"

#: order/serializers.py:2143
msgid "Line price currency"
msgstr "Moneda de precio de línea"

#: order/status_codes.py:17 order/status_codes.py:54 stock/status_codes.py:16
msgid "Lost"
msgstr "Perdida"

#: order/status_codes.py:18 order/status_codes.py:55 stock/status_codes.py:24
msgid "Returned"
msgstr "Devuelto"

#: order/status_codes.py:47 order/status_codes.py:79
msgid "In Progress"
msgstr "En progreso"

#: order/status_codes.py:105
msgid "Return"
msgstr "Devolución"

#: order/status_codes.py:108
msgid "Repair"
msgstr "Reparación"

#: order/status_codes.py:111
msgid "Replace"
msgstr "Reemplazo"

#: order/status_codes.py:114
msgid "Refund"
msgstr "Reembolso"

#: order/status_codes.py:117
msgid "Reject"
msgstr "Rechazo"

#: order/tasks.py:44
msgid "Overdue Purchase Order"
msgstr "Orden de compra atrasada"

#: order/tasks.py:49
#, python-brace-format
msgid "Purchase order {po} is now overdue"
msgstr "La orden de compra {po} está atrasada"

#: order/tasks.py:112
msgid "Overdue Sales Order"
msgstr "Orden de venta atrasada"

#: order/tasks.py:117
#, python-brace-format
msgid "Sales order {so} is now overdue"
msgstr "La orden de venta {so} está atrasada"

#: order/tasks.py:177
msgid "Overdue Return Order"
msgstr ""

#: order/tasks.py:182
#, python-brace-format
msgid "Return order {ro} is now overdue"
msgstr ""

#: part/api.py:115
msgid "Starred"
msgstr "Favoritos"

#: part/api.py:117
msgid "Filter by starred categories"
msgstr ""

#: part/api.py:134 stock/api.py:255
msgid "Depth"
msgstr "Profundidad"

#: part/api.py:134
msgid "Filter by category depth"
msgstr "Filtrar por profundidad de categoría"

#: part/api.py:152 stock/api.py:273
msgid "Top Level"
msgstr "Nivel superior"

#: part/api.py:154
msgid "Filter by top-level categories"
msgstr "Filtrar por categorías de nivel superior"

#: part/api.py:167 stock/api.py:288
msgid "Cascade"
msgstr "En cascada"

#: part/api.py:169
msgid "Include sub-categories in filtered results"
msgstr ""

#: part/api.py:189
msgid "Parent"
msgstr ""

#: part/api.py:191
msgid "Filter by parent category"
msgstr ""

#: part/api.py:226
msgid "Exclude sub-categories under the specified category"
msgstr ""

#: part/api.py:438
msgid "Has Results"
msgstr "Tiene resultados"

#: part/api.py:611
msgid "Incoming Purchase Order"
msgstr "Orden de compra entrante"

#: part/api.py:625
msgid "Outgoing Sales Order"
msgstr "Orden de venta saliente"

#: part/api.py:637
msgid "Stock produced by Build Order"
msgstr ""

#: part/api.py:719
msgid "Stock required for Build Order"
msgstr ""

#: part/api.py:865
msgid "Validate entire Bill of Materials"
msgstr "Validación de Lista de Materiales"

#: part/api.py:871
msgid "This option must be selected"
msgstr "Esta opción debe ser seleccionada"

#: part/api.py:907
msgid "Is Variant"
msgstr ""

#: part/api.py:915
msgid "Is Revision"
msgstr ""

#: part/api.py:925
msgid "Has Revisions"
msgstr ""

#: part/api.py:1116
msgid "BOM Valid"
msgstr ""

#: part/api.py:1775
msgid "Assembly part is testable"
msgstr ""

#: part/api.py:1784
msgid "Component part is testable"
msgstr ""

#: part/api.py:1835
msgid "Uses"
msgstr ""

#: part/models.py:90 part/models.py:4137
#: templates/email/part_event_notification.html:16
msgid "Part Category"
msgstr "Categoría de parte"

#: part/models.py:91 users/ruleset.py:32
msgid "Part Categories"
msgstr "Categorías de parte"

#: part/models.py:109 part/models.py:1134
msgid "Default Location"
msgstr "Ubicación Predeterminada"

#: part/models.py:110
msgid "Default location for parts in this category"
msgstr "Ubicación predeterminada para partes de esta categoría"

#: part/models.py:115 stock/models.py:212
msgid "Structural"
msgstr "Estructural"

#: part/models.py:117
msgid "Parts may not be directly assigned to a structural category, but may be assigned to child categories."
msgstr "Las partes no pueden asignarse directamente a una categoría estructural, pero pueden asignarse a categorías hijas."

#: part/models.py:126
msgid "Default keywords"
msgstr "Palabras clave predeterminadas"

#: part/models.py:127
msgid "Default keywords for parts in this category"
msgstr "Palabras clave por defecto para partes en esta categoría"

#: part/models.py:134 stock/models.py:96 stock/models.py:194
msgid "Icon"
msgstr "Icono"

#: part/models.py:135 part/serializers.py:152 part/serializers.py:171
#: stock/models.py:195
msgid "Icon (optional)"
msgstr "Icono (opcional)"

#: part/models.py:179
msgid "You cannot make this part category structural because some parts are already assigned to it!"
msgstr "¡No puedes hacer que esta categoría de partes sea estructural porque algunas partes ya están asignadas!"

#: part/models.py:458 part/serializers.py:130 part/serializers.py:343
#: users/ruleset.py:33
msgid "Parts"
msgstr "Partes"

#: part/models.py:510
msgid "Cannot delete this part as it is locked"
msgstr ""

#: part/models.py:513
msgid "Cannot delete this part as it is still active"
msgstr ""

#: part/models.py:518
msgid "Cannot delete this part as it is used in an assembly"
msgstr ""

#: part/models.py:556
msgid "Invalid choice for parent part"
msgstr "Opción no válida para la parte principal"

#: part/models.py:604 part/models.py:611
#, python-brace-format
msgid "Part '{self}' cannot be used in BOM for '{parent}' (recursive)"
msgstr ""

#: part/models.py:623
#, python-brace-format
msgid "Part '{parent}' is  used in BOM for '{self}' (recursive)"
msgstr ""

#: part/models.py:690
#, python-brace-format
msgid "IPN must match regex pattern {pattern}"
msgstr ""

#: part/models.py:698
msgid "Part cannot be a revision of itself"
msgstr ""

#: part/models.py:705
msgid "Cannot make a revision of a part which is already a revision"
msgstr ""

#: part/models.py:712
msgid "Revision code must be specified"
msgstr ""

#: part/models.py:719
msgid "Revisions are only allowed for assembly parts"
msgstr ""

#: part/models.py:726
msgid "Cannot make a revision of a template part"
msgstr ""

#: part/models.py:732
msgid "Parent part must point to the same template"
msgstr ""

#: part/models.py:829
msgid "Stock item with this serial number already exists"
msgstr "Ya existe un artículo de almacén con este número de serie"

#: part/models.py:971
msgid "Duplicate IPN not allowed in part settings"
msgstr "IPN duplicado no permitido en la configuración de partes"

#: part/models.py:983
msgid "Duplicate part revision already exists."
msgstr "La revisión de parte duplicada ya existe."

#: part/models.py:992
msgid "Part with this Name, IPN and Revision already exists."
msgstr "Parte con este nombre, IPN y revisión ya existe."

#: part/models.py:1007
msgid "Parts cannot be assigned to structural part categories!"
msgstr "¡No se pueden asignar partes a las categorías de partes estructurales!"

#: part/models.py:1039
msgid "Part name"
msgstr "Nombre de la parte"

#: part/models.py:1044
msgid "Is Template"
msgstr "Es plantilla"

#: part/models.py:1045
msgid "Is this part a template part?"
msgstr "¿Es esta parte una parte de la plantilla?"

#: part/models.py:1055
msgid "Is this part a variant of another part?"
msgstr "¿Es esta parte una variante de otra parte?"

#: part/models.py:1056
msgid "Variant Of"
msgstr "Variante de"

#: part/models.py:1063
msgid "Part description (optional)"
msgstr "Descripción de parte (opcional)"

#: part/models.py:1070
msgid "Keywords"
msgstr "Palabras claves"

#: part/models.py:1071
msgid "Part keywords to improve visibility in search results"
msgstr "Palabras clave para mejorar la visibilidad en los resultados de búsqueda"

#: part/models.py:1081
msgid "Part category"
msgstr "Categoría de parte"

#: part/models.py:1088 part/serializers.py:926
#: report/templates/report/inventree_stock_location_report.html:103
msgid "IPN"
msgstr "IPN"

#: part/models.py:1096
msgid "Part revision or version number"
msgstr "Revisión de parte o número de versión"

#: part/models.py:1097 report/models.py:215
msgid "Revision"
msgstr "Revisión"

#: part/models.py:1106
msgid "Is this part a revision of another part?"
msgstr "¿Es esta parte una variante de otra parte?"

#: part/models.py:1107
msgid "Revision Of"
msgstr "Variante de"

#: part/models.py:1132
msgid "Where is this item normally stored?"
msgstr "¿Dónde se almacena este artículo normalmente?"

#: part/models.py:1178
msgid "Default Supplier"
msgstr "Proveedor por defecto"

#: part/models.py:1179
msgid "Default supplier part"
msgstr "Parte de proveedor predeterminada"

#: part/models.py:1186
msgid "Default Expiry"
msgstr "Expiración por defecto"

#: part/models.py:1187
msgid "Expiry time (in days) for stock items of this part"
msgstr "Tiempo de expiración (en días) para los artículos de stock de esta parte"

#: part/models.py:1195 part/serializers.py:977
msgid "Minimum Stock"
msgstr "Stock mínimo"

#: part/models.py:1196
msgid "Minimum allowed stock level"
msgstr "Nivel mínimo de stock permitido"

#: part/models.py:1205
msgid "Units of measure for this part"
msgstr "Unidades de medida para esta parte"

#: part/models.py:1212
msgid "Can this part be built from other parts?"
msgstr "¿Se puede construir esta parte a partir de otras partes?"

#: part/models.py:1218
msgid "Can this part be used to build other parts?"
msgstr "¿Se puede utilizar esta parte para construir otras partes?"

#: part/models.py:1224
msgid "Does this part have tracking for unique items?"
msgstr "¿Esta parte tiene seguimiento de objetos únicos?"

#: part/models.py:1230
msgid "Can this part have test results recorded against it?"
msgstr ""

#: part/models.py:1236
msgid "Can this part be purchased from external suppliers?"
msgstr "¿Se puede comprar esta parte a proveedores externos?"

#: part/models.py:1242
msgid "Can this part be sold to customers?"
msgstr "¿Se puede vender esta parte a los clientes?"

#: part/models.py:1246
msgid "Is this part active?"
msgstr "¿Está activa esta parte?"

#: part/models.py:1252
msgid "Locked parts cannot be edited"
msgstr "Las partes bloqueadas no pueden ser editadas"

#: part/models.py:1258
msgid "Is this a virtual part, such as a software product or license?"
msgstr "¿Es ésta una parte virtual, como un producto de software o una licencia?"

#: part/models.py:1264
msgid "BOM checksum"
msgstr "Suma de verificación de BOM"

#: part/models.py:1265
msgid "Stored BOM checksum"
msgstr "Suma de verificación de BOM almacenada"

#: part/models.py:1273
msgid "BOM checked by"
msgstr "BOM comprobado por"

#: part/models.py:1278
msgid "BOM checked date"
msgstr "Fecha BOM comprobada"

#: part/models.py:1294
msgid "Creation User"
msgstr "Creación de Usuario"

#: part/models.py:1304
msgid "Owner responsible for this part"
msgstr "Dueño responsable de esta parte"

#: part/models.py:1309
msgid "Last Stocktake"
msgstr "Último inventario"

#: part/models.py:2190
msgid "Sell multiple"
msgstr "Vender múltiples"

#: part/models.py:3185
msgid "Currency used to cache pricing calculations"
msgstr "Moneda utilizada para almacenar en caché los cálculos de precios"

#: part/models.py:3201
msgid "Minimum BOM Cost"
msgstr "Costo mínimo de BOM"

#: part/models.py:3202
msgid "Minimum cost of component parts"
msgstr "Costo mínimo de partes de componentes"

#: part/models.py:3208
msgid "Maximum BOM Cost"
msgstr "Costo máximo de BOM"

#: part/models.py:3209
msgid "Maximum cost of component parts"
msgstr "Costo máximo de partes de componentes"

#: part/models.py:3215
msgid "Minimum Purchase Cost"
msgstr "Costo mínimo de compra"

#: part/models.py:3216
msgid "Minimum historical purchase cost"
msgstr "Costo histórico mínimo de compra"

#: part/models.py:3222
msgid "Maximum Purchase Cost"
msgstr "Costo máximo de compra"

#: part/models.py:3223
msgid "Maximum historical purchase cost"
msgstr "Costo histórico máximo de compra"

#: part/models.py:3229
msgid "Minimum Internal Price"
msgstr "Precio interno mínimo"

#: part/models.py:3230
msgid "Minimum cost based on internal price breaks"
msgstr "Costo mínimo basado en precios reducidos internos"

#: part/models.py:3236
msgid "Maximum Internal Price"
msgstr "Precio interno máximo"

#: part/models.py:3237
msgid "Maximum cost based on internal price breaks"
msgstr "Costo máximo basado en precios reducidos internos"

#: part/models.py:3243
msgid "Minimum Supplier Price"
msgstr "Precio mínimo de proveedor"

#: part/models.py:3244
msgid "Minimum price of part from external suppliers"
msgstr "Precio mínimo de la parte de proveedores externos"

#: part/models.py:3250
msgid "Maximum Supplier Price"
msgstr "Precio máximo de proveedor"

#: part/models.py:3251
msgid "Maximum price of part from external suppliers"
msgstr "Precio máximo de la parte de proveedores externos"

#: part/models.py:3257
msgid "Minimum Variant Cost"
msgstr "Costo mínimo de variante"

#: part/models.py:3258
msgid "Calculated minimum cost of variant parts"
msgstr "Costo mínimo calculado de las partes variantes"

#: part/models.py:3264
msgid "Maximum Variant Cost"
msgstr "Costo máximo de variante"

#: part/models.py:3265
msgid "Calculated maximum cost of variant parts"
msgstr "Costo máximo calculado de las partes variantes"

#: part/models.py:3271 part/models.py:3285
msgid "Minimum Cost"
msgstr "Costo mínimo"

#: part/models.py:3272
msgid "Override minimum cost"
msgstr "Anular el costo mínimo"

#: part/models.py:3278 part/models.py:3292
msgid "Maximum Cost"
msgstr "Costo máximo"

#: part/models.py:3279
msgid "Override maximum cost"
msgstr "Reemplazar coste máximo"

#: part/models.py:3286
msgid "Calculated overall minimum cost"
msgstr "Costo mínimo general calculado"

#: part/models.py:3293
msgid "Calculated overall maximum cost"
msgstr ""

#: part/models.py:3299
msgid "Minimum Sale Price"
msgstr "Precio de venta mínimo"

#: part/models.py:3300
msgid "Minimum sale price based on price breaks"
msgstr "Precio de venta mínimo basado en precios reducidos"

#: part/models.py:3306
msgid "Maximum Sale Price"
msgstr "Precio de venta máximo"

#: part/models.py:3307
msgid "Maximum sale price based on price breaks"
msgstr "Precio de venta máximo basado en precios reducidos"

#: part/models.py:3313
msgid "Minimum Sale Cost"
msgstr "Costo de venta mínimo"

#: part/models.py:3314
msgid "Minimum historical sale price"
msgstr "Precio de venta mínimo histórico"

#: part/models.py:3320
msgid "Maximum Sale Cost"
msgstr "Costo de Venta Máximo"

#: part/models.py:3321
msgid "Maximum historical sale price"
msgstr "Precio de venta máximo histórico"

#: part/models.py:3340
msgid "Part for stocktake"
msgstr ""

#: part/models.py:3345
msgid "Item Count"
msgstr "Número de artículos"

#: part/models.py:3346
msgid "Number of individual stock entries at time of stocktake"
msgstr ""

#: part/models.py:3354
msgid "Total available stock at time of stocktake"
msgstr ""

#: part/models.py:3358 part/models.py:3441 part/serializers.py:273
#: report/templates/report/inventree_test_report.html:106
msgid "Date"
msgstr "Fecha"

#: part/models.py:3359
msgid "Date stocktake was performed"
msgstr ""

#: part/models.py:3367
msgid "Additional notes"
msgstr "Notas adicionales"

#: part/models.py:3377
msgid "User who performed this stocktake"
msgstr ""

#: part/models.py:3383
msgid "Minimum Stock Cost"
msgstr "Costo de Stock Mínimo"

#: part/models.py:3384
msgid "Estimated minimum cost of stock on hand"
msgstr "Costo mínimo estimado del stock disponible"

#: part/models.py:3390
msgid "Maximum Stock Cost"
msgstr ""

#: part/models.py:3391
msgid "Estimated maximum cost of stock on hand"
msgstr ""

#: part/models.py:3447
msgid "Report"
msgstr "Informe"

#: part/models.py:3448
msgid "Stocktake report file (generated internally)"
msgstr ""

#: part/models.py:3453
msgid "Part Count"
msgstr "Número de partes"

#: part/models.py:3454
msgid "Number of parts covered by stocktake"
msgstr ""

#: part/models.py:3464
msgid "User who requested this stocktake report"
msgstr ""

#: part/models.py:3474
msgid "Part Sale Price Break"
msgstr ""

#: part/models.py:3586
msgid "Part Test Template"
msgstr ""

#: part/models.py:3612
msgid "Invalid template name - must include at least one alphanumeric character"
msgstr ""

#: part/models.py:3633 part/models.py:3803
msgid "Choices must be unique"
msgstr ""

#: part/models.py:3644
msgid "Test templates can only be created for testable parts"
msgstr "Las plantillas de prueba solo pueden ser creadas para partes de prueba"

#: part/models.py:3655
msgid "Test template with the same key already exists for part"
msgstr ""

#: part/models.py:3672
msgid "Test Name"
msgstr "Nombre de prueba"

#: part/models.py:3673
msgid "Enter a name for the test"
msgstr "Introduzca un nombre para la prueba"

#: part/models.py:3679
msgid "Test Key"
msgstr ""

#: part/models.py:3680
msgid "Simplified key for the test"
msgstr ""

#: part/models.py:3687
msgid "Test Description"
msgstr "Descripción de prueba"

#: part/models.py:3688
msgid "Enter description for this test"
msgstr "Introduce la descripción para esta prueba"

#: part/models.py:3692 report/models.py:271
msgid "Enabled"
msgstr "Habilitado"

#: part/models.py:3692
msgid "Is this test enabled?"
msgstr ""

#: part/models.py:3697
msgid "Required"
msgstr "Requerido"

#: part/models.py:3698
msgid "Is this test required to pass?"
msgstr "¿Es necesario pasar esta prueba?"

#: part/models.py:3703
msgid "Requires Value"
msgstr "Requiere valor"

#: part/models.py:3704
msgid "Does this test require a value when adding a test result?"
msgstr "¿Esta prueba requiere un valor al agregar un resultado de la prueba?"

#: part/models.py:3709
msgid "Requires Attachment"
msgstr "Adjunto obligatorio"

#: part/models.py:3711
msgid "Does this test require a file attachment when adding a test result?"
msgstr "¿Esta prueba requiere un archivo adjunto al agregar un resultado de la prueba?"

#: part/models.py:3717 part/models.py:3865
msgid "Choices"
msgstr "Opciones"

#: part/models.py:3718
msgid "Valid choices for this test (comma-separated)"
msgstr ""

#: part/models.py:3751
msgid "Part Parameter Template"
msgstr ""

#: part/models.py:3778
msgid "Checkbox parameters cannot have units"
msgstr ""

#: part/models.py:3783
msgid "Checkbox parameters cannot have choices"
msgstr ""

#: part/models.py:3820
msgid "Parameter template name must be unique"
msgstr "El nombre de parámetro en la plantilla tiene que ser único"

#: part/models.py:3838
msgid "Parameter Name"
msgstr "Nombre de Parámetro"

#: part/models.py:3845
msgid "Physical units for this parameter"
msgstr ""

#: part/models.py:3853
msgid "Parameter description"
msgstr ""

#: part/models.py:3859
msgid "Checkbox"
msgstr "Casilla de verificación"

#: part/models.py:3860
msgid "Is this parameter a checkbox?"
msgstr "¿Es este parámetro una casilla de verificación?"

#: part/models.py:3866
msgid "Valid choices for this parameter (comma-separated)"
msgstr "Opciones válidas para este parámetro (separados por comas)"

#: part/models.py:3877
msgid "Selection list for this parameter"
msgstr "Lista de selección para este parámetro"

#: part/models.py:3913
msgid "Part Parameter"
msgstr ""

#: part/models.py:3939
msgid "Parameter cannot be modified - part is locked"
msgstr ""

#: part/models.py:3977
msgid "Invalid choice for parameter value"
msgstr "Opción inválida para el valor del parámetro"

#: part/models.py:4028
msgid "Parent Part"
msgstr "Parte principal"

#: part/models.py:4036 part/models.py:4144 part/models.py:4145
msgid "Parameter Template"
msgstr "Plantilla de parámetro"

#: part/models.py:4042
msgid "Parameter Value"
msgstr "Valor del parámetro"

#: part/models.py:4092
msgid "Part Category Parameter Template"
msgstr ""

#: part/models.py:4151
msgid "Default Value"
msgstr "Valor predeterminado"

#: part/models.py:4152
msgid "Default Parameter Value"
msgstr "Valor de parámetro por defecto"

#: part/models.py:4283
msgid "BOM item cannot be modified - assembly is locked"
msgstr ""

#: part/models.py:4290
msgid "BOM item cannot be modified - variant assembly is locked"
msgstr ""

#: part/models.py:4300
msgid "Select parent part"
msgstr "Seleccionar parte principal"

#: part/models.py:4310
msgid "Sub part"
msgstr "Sub parte"

#: part/models.py:4311
msgid "Select part to be used in BOM"
msgstr "Seleccionar parte a utilizar en BOM"

#: part/models.py:4322
msgid "BOM quantity for this BOM item"
msgstr "Cantidad del artículo en BOM"

#: part/models.py:4328
msgid "This BOM item is optional"
msgstr "Este artículo BOM es opcional"

#: part/models.py:4334
msgid "This BOM item is consumable (it is not tracked in build orders)"
msgstr "Este artículo de BOM es consumible (no está rastreado en órdenes de construcción)"

#: part/models.py:4341
msgid "Overage"
msgstr "Exceso"

#: part/models.py:4342
msgid "Estimated build wastage quantity (absolute or percentage)"
msgstr "Cantidad estimada de desperdicio de construcción (absoluta o porcentaje)"

#: part/models.py:4349
msgid "BOM item reference"
msgstr "Referencia de artículo de BOM"

#: part/models.py:4357
msgid "BOM item notes"
msgstr "Notas del artículo de BOM"

#: part/models.py:4363
msgid "Checksum"
msgstr "Suma de verificación"

#: part/models.py:4364
msgid "BOM line checksum"
msgstr "Suma de verificación de línea de BOM"

#: part/models.py:4369
msgid "Validated"
msgstr "Validado"

#: part/models.py:4370
msgid "This BOM item has been validated"
msgstr "Este artículo de BOM ha sido validado"

#: part/models.py:4375
msgid "Gets inherited"
msgstr ""

#: part/models.py:4376
msgid "This BOM item is inherited by BOMs for variant parts"
msgstr "Este artículo BOM es heredado por BOMs para partes variantes"

#: part/models.py:4382
msgid "Stock items for variant parts can be used for this BOM item"
msgstr "Artículos de stock para partes variantes pueden ser usados para este artículo BOM"

#: part/models.py:4467 stock/models.py:848
msgid "Quantity must be integer value for trackable parts"
msgstr "La cantidad debe ser un valor entero para las partes rastreables"

#: part/models.py:4477 part/models.py:4479
msgid "Sub part must be specified"
msgstr "Debe especificar la subparte"

#: part/models.py:4624
msgid "BOM Item Substitute"
msgstr "Ítem de BOM sustituto"

#: part/models.py:4645
msgid "Substitute part cannot be the same as the master part"
msgstr "La parte sustituta no puede ser la misma que la parte principal"

#: part/models.py:4658
msgid "Parent BOM item"
msgstr "Artículo BOM superior"

#: part/models.py:4666
msgid "Substitute part"
msgstr "Sustituir parte"

#: part/models.py:4682
msgid "Part 1"
msgstr "Parte 1"

#: part/models.py:4690
msgid "Part 2"
msgstr "Parte 2"

#: part/models.py:4691
msgid "Select Related Part"
msgstr "Seleccionar parte relacionada"

#: part/models.py:4698
msgid "Note for this relationship"
msgstr "Nota para esta relación"

#: part/models.py:4717
msgid "Part relationship cannot be created between a part and itself"
msgstr ""

#: part/models.py:4722
msgid "Duplicate relationship already exists"
msgstr ""

#: part/serializers.py:125
msgid "Parent Category"
msgstr ""

#: part/serializers.py:126
msgid "Parent part category"
msgstr "Categoría principal de parte"

#: part/serializers.py:134 part/serializers.py:168
msgid "Subcategories"
msgstr "Subcategorías"

#: part/serializers.py:207
msgid "Results"
msgstr ""

#: part/serializers.py:208
msgid "Number of results recorded against this template"
msgstr ""

#: part/serializers.py:235 part/serializers.py:253 stock/serializers.py:685
msgid "Purchase currency of this stock item"
msgstr "Moneda de compra de ítem de stock"

#: part/serializers.py:278
msgid "Speculative Quantity"
msgstr "Cantidad especulativa"

#: part/serializers.py:287
msgid "Model ID"
msgstr "ID del modelo"

#: part/serializers.py:313
msgid "File is not an image"
msgstr ""

#: part/serializers.py:344
msgid "Number of parts using this template"
msgstr ""

#: part/serializers.py:489
msgid "Original Part"
msgstr "Parte original"

#: part/serializers.py:490
msgid "Select original part to duplicate"
msgstr "Seleccione la parte original a duplicar"

#: part/serializers.py:495
msgid "Copy Image"
msgstr "Copiar Imagen"

#: part/serializers.py:496
msgid "Copy image from original part"
msgstr "Copiar imagen desde la parte original"

#: part/serializers.py:502
msgid "Copy BOM"
msgstr "Copiar BOM"

#: part/serializers.py:503
msgid "Copy bill of materials from original part"
msgstr "Copiar la factura de materiales de la parte original"

#: part/serializers.py:509
msgid "Copy Parameters"
msgstr "Copiar Parámetros"

#: part/serializers.py:510
msgid "Copy parameter data from original part"
msgstr "Copiar datos del parámetro de la parte original"

#: part/serializers.py:516
msgid "Copy Notes"
msgstr "Copiar Notas"

#: part/serializers.py:517
msgid "Copy notes from original part"
msgstr ""

#: part/serializers.py:535
msgid "Initial Stock Quantity"
msgstr "Cantidad Inicial de Stock"

#: part/serializers.py:537
msgid "Specify initial stock quantity for this Part. If quantity is zero, no stock is added."
msgstr ""

#: part/serializers.py:544
msgid "Initial Stock Location"
msgstr ""

#: part/serializers.py:545
msgid "Specify initial stock location for this Part"
msgstr ""

#: part/serializers.py:562
msgid "Select supplier (or leave blank to skip)"
msgstr "Seleccione proveedor (o déjelo en blanco para saltar)"

#: part/serializers.py:578
msgid "Select manufacturer (or leave blank to skip)"
msgstr "Seleccionar fabricante (o dejar en blanco para saltar)"

#: part/serializers.py:588
msgid "Manufacturer part number"
msgstr "Número de parte del fabricante"

#: part/serializers.py:595
msgid "Selected company is not a valid supplier"
msgstr "La empresa seleccionada no es un proveedor válido"

#: part/serializers.py:604
msgid "Selected company is not a valid manufacturer"
msgstr "La empresa seleccionada no es un fabricante válido"

#: part/serializers.py:615
msgid "Manufacturer part matching this MPN already exists"
msgstr ""

#: part/serializers.py:622
msgid "Supplier part matching this SKU already exists"
msgstr ""

#: part/serializers.py:911 part/stocktake.py:222
msgid "Category Name"
msgstr "Nombre de categoría"

#: part/serializers.py:937
msgid "Building"
msgstr "En construcción"

#: part/serializers.py:952 part/stocktake.py:223 stock/serializers.py:1084
#: stock/serializers.py:1260 users/ruleset.py:36
msgid "Stock Items"
msgstr "Elementos de stock"

#: part/serializers.py:955
msgid "Revisions"
msgstr ""

#: part/serializers.py:958
msgid "Suppliers"
msgstr "Proveedores"

#: part/serializers.py:961 templates/email/low_stock_notification.html:16
#: templates/email/part_event_notification.html:17
msgid "Total Stock"
msgstr "Inventario Total"

#: part/serializers.py:967
msgid "Unallocated Stock"
msgstr ""

#: part/serializers.py:973
msgid "Variant Stock"
msgstr ""

#: part/serializers.py:1006
msgid "Duplicate Part"
msgstr "Duplicar Parte"

#: part/serializers.py:1007
msgid "Copy initial data from another Part"
msgstr ""

#: part/serializers.py:1013
msgid "Initial Stock"
msgstr "Stock Inicial"

#: part/serializers.py:1014
msgid "Create Part with initial stock quantity"
msgstr "Crear Parte con cantidad inicial de stock"

#: part/serializers.py:1020
msgid "Supplier Information"
msgstr "Información del proveedor"

#: part/serializers.py:1021
msgid "Add initial supplier information for this part"
msgstr "Añadir información inicial del proveedor para esta parte"

#: part/serializers.py:1029
msgid "Copy Category Parameters"
msgstr "Copiar Parámetros de Categoría"

#: part/serializers.py:1030
msgid "Copy parameter templates from selected part category"
msgstr "Copiar plantillas de parámetro de la categoría de partes seleccionada"

#: part/serializers.py:1035
msgid "Existing Image"
msgstr "Imagen Existente"

#: part/serializers.py:1036
msgid "Filename of an existing part image"
msgstr ""

#: part/serializers.py:1053
msgid "Image file does not exist"
msgstr "El archivo de imagen no existe"

#: part/serializers.py:1257
msgid "Limit stocktake report to a particular part, and any variant parts"
msgstr ""

#: part/serializers.py:1267
msgid "Limit stocktake report to a particular part category, and any child categories"
msgstr ""

#: part/serializers.py:1277
msgid "Limit stocktake report to a particular stock location, and any child locations"
msgstr ""

#: part/serializers.py:1283
msgid "Exclude External Stock"
msgstr ""

#: part/serializers.py:1284
msgid "Exclude stock items in external locations"
msgstr ""

#: part/serializers.py:1289
msgid "Generate Report"
msgstr "Generar informe"

#: part/serializers.py:1290
msgid "Generate report file containing calculated stocktake data"
msgstr ""

#: part/serializers.py:1295
msgid "Update Parts"
msgstr "Actualizar partes"

#: part/serializers.py:1296
msgid "Update specified parts with calculated stocktake data"
msgstr ""

#: part/serializers.py:1304
msgid "Stocktake functionality is not enabled"
msgstr ""

#: part/serializers.py:1309
msgid "Background worker check failed"
msgstr "Falló la comprobación en segundo plano del worker"

#: part/serializers.py:1425
msgid "Minimum Price"
msgstr "Precio mínimo"

#: part/serializers.py:1426
msgid "Override calculated value for minimum price"
msgstr "Anular el valor calculado para precio mínimo"

#: part/serializers.py:1433
msgid "Minimum price currency"
msgstr "Precio mínimo de moneda"

#: part/serializers.py:1440
msgid "Maximum Price"
msgstr "Precio máximo"

#: part/serializers.py:1441
msgid "Override calculated value for maximum price"
msgstr ""

#: part/serializers.py:1448
msgid "Maximum price currency"
msgstr "Precio máximo de moneda"

#: part/serializers.py:1477
msgid "Update"
msgstr "Actualizar"

#: part/serializers.py:1478
msgid "Update pricing for this part"
msgstr ""

#: part/serializers.py:1501
#, python-brace-format
msgid "Could not convert from provided currencies to {default_currency}"
msgstr ""

#: part/serializers.py:1508
msgid "Minimum price must not be greater than maximum price"
msgstr "El precio mínimo no debe ser mayor que el precio máximo"

#: part/serializers.py:1511
msgid "Maximum price must not be less than minimum price"
msgstr "El precio máximo no debe ser inferior al precio mínimo"

#: part/serializers.py:1664
msgid "Select the parent assembly"
msgstr ""

#: part/serializers.py:1678
msgid "Select the component part"
msgstr ""

#: part/serializers.py:1698
msgid "Can Build"
msgstr "Puede construir"

#: part/serializers.py:1925
msgid "Select part to copy BOM from"
msgstr "Seleccionar parte de la que copiar BOM"

#: part/serializers.py:1933
msgid "Remove Existing Data"
msgstr "Eliminar Datos Existentes"

#: part/serializers.py:1934
msgid "Remove existing BOM items before copying"
msgstr "Eliminar artículos BOM existentes antes de copiar"

#: part/serializers.py:1939
msgid "Include Inherited"
msgstr "Incluye Heredado"

#: part/serializers.py:1940
msgid "Include BOM items which are inherited from templated parts"
msgstr "Incluye artículos BOM que son heredados de partes con plantillas"

#: part/serializers.py:1945
msgid "Skip Invalid Rows"
msgstr "Omitir filas no válidas"

#: part/serializers.py:1946
msgid "Enable this option to skip invalid rows"
msgstr "Activar esta opción para omitir filas inválidas"

#: part/serializers.py:1951
msgid "Copy Substitute Parts"
msgstr "Copiar partes sustitutas"

#: part/serializers.py:1952
msgid "Copy substitute parts when duplicate BOM items"
msgstr ""

#: part/stocktake.py:218
msgid "Part ID"
msgstr "ID de Parte"

#: part/stocktake.py:220
msgid "Part Description"
msgstr "Descripción de parte"

#: part/stocktake.py:221
msgid "Category ID"
msgstr "ID de Categoría"

#: part/stocktake.py:224
msgid "Total Quantity"
msgstr "Cantidad Total"

#: part/stocktake.py:225
msgid "Total Cost Min"
msgstr "Costo total mínimo"

#: part/stocktake.py:226
msgid "Total Cost Max"
msgstr "Costo total máximo"

#: part/stocktake.py:284
msgid "Stocktake Report Available"
msgstr ""

#: part/stocktake.py:285
msgid "A new stocktake report is available for download"
msgstr ""

#: part/tasks.py:38
msgid "Low stock notification"
msgstr "Notificación por bajo stock"

#: part/tasks.py:40
#, python-brace-format
msgid "The available stock for {part.name} has fallen below the configured minimum level"
msgstr "El stock disponible para {part.name} ha caído por debajo del nivel mínimo configurado"

#: plugin/api.py:78
msgid "Builtin"
msgstr ""

#: plugin/api.py:92
msgid "Mandatory"
msgstr ""

#: plugin/api.py:103
msgid "Sample"
msgstr ""

#: plugin/api.py:117 plugin/models.py:159
msgid "Installed"
msgstr "Instalado"

#: plugin/api.py:184
msgid "Plugin cannot be deleted as it is currently active"
msgstr ""

#: plugin/base/action/api.py:34
msgid "No action specified"
msgstr "No se especificó ninguna acción"

#: plugin/base/action/api.py:46
msgid "No matching action found"
msgstr "No se encontró ninguna acción coincidente"

#: plugin/base/barcodes/api.py:211
msgid "No match found for barcode data"
msgstr "No se encontró ninguna coincidencia para los datos del código de barras"

#: plugin/base/barcodes/api.py:215
msgid "Match found for barcode data"
msgstr "Coincidencia encontrada para datos de códigos de barras"

#: plugin/base/barcodes/api.py:253 plugin/base/barcodes/serializers.py:77
msgid "Model is not supported"
msgstr ""

#: plugin/base/barcodes/api.py:258
msgid "Model instance not found"
msgstr ""

#: plugin/base/barcodes/api.py:287
msgid "Barcode matches existing item"
msgstr "El código de barras coincide con artículo existente"

#: plugin/base/barcodes/api.py:418
msgid "No matching part data found"
msgstr ""

#: plugin/base/barcodes/api.py:434
msgid "No matching supplier parts found"
msgstr ""

#: plugin/base/barcodes/api.py:437
msgid "Multiple matching supplier parts found"
msgstr ""

#: plugin/base/barcodes/api.py:450 plugin/base/barcodes/api.py:677
msgid "No matching plugin found for barcode data"
msgstr "No se ha encontrado ningún complemento para datos de código de barras"

#: plugin/base/barcodes/api.py:460
msgid "Matched supplier part"
msgstr ""

#: plugin/base/barcodes/api.py:528
msgid "Item has already been received"
msgstr ""

#: plugin/base/barcodes/api.py:576
msgid "No plugin match for supplier barcode"
msgstr ""

#: plugin/base/barcodes/api.py:625
msgid "Multiple matching line items found"
msgstr ""

#: plugin/base/barcodes/api.py:628
msgid "No matching line item found"
msgstr ""

#: plugin/base/barcodes/api.py:674
msgid "No sales order provided"
msgstr "Ningún pedido de venta proporcionado"

#: plugin/base/barcodes/api.py:683
msgid "Barcode does not match an existing stock item"
msgstr ""

#: plugin/base/barcodes/api.py:699
msgid "Stock item does not match line item"
msgstr ""

#: plugin/base/barcodes/api.py:729
msgid "Insufficient stock available"
msgstr ""

#: plugin/base/barcodes/api.py:742
msgid "Stock item allocated to sales order"
msgstr ""

#: plugin/base/barcodes/api.py:745
msgid "Not enough information"
msgstr ""

#: plugin/base/barcodes/mixins.py:308
#: plugin/builtin/barcodes/inventree_barcode.py:101
msgid "Found matching item"
msgstr ""

#: plugin/base/barcodes/mixins.py:374
msgid "Supplier part does not match line item"
msgstr ""

#: plugin/base/barcodes/mixins.py:377
msgid "Line item is already completed"
msgstr ""

#: plugin/base/barcodes/mixins.py:414
msgid "Further information required to receive line item"
msgstr ""

#: plugin/base/barcodes/mixins.py:422
msgid "Received purchase order line item"
msgstr ""

#: plugin/base/barcodes/mixins.py:429
msgid "Failed to receive line item"
msgstr ""

#: plugin/base/barcodes/serializers.py:53
msgid "Scanned barcode data"
msgstr ""

#: plugin/base/barcodes/serializers.py:62
msgid "Model name to generate barcode for"
msgstr ""

#: plugin/base/barcodes/serializers.py:67
msgid "Primary key of model object to generate barcode for"
msgstr ""

#: plugin/base/barcodes/serializers.py:137
msgid "Purchase Order to allocate items against"
msgstr ""

#: plugin/base/barcodes/serializers.py:143
msgid "Purchase order is not open"
msgstr ""

#: plugin/base/barcodes/serializers.py:161
msgid "Supplier to receive items from"
msgstr ""

#: plugin/base/barcodes/serializers.py:168
msgid "PurchaseOrder to receive items against"
msgstr ""

#: plugin/base/barcodes/serializers.py:174
msgid "Purchase order has not been placed"
msgstr ""

#: plugin/base/barcodes/serializers.py:182
msgid "Location to receive items into"
msgstr ""

#: plugin/base/barcodes/serializers.py:188
msgid "Cannot select a structural location"
msgstr ""

#: plugin/base/barcodes/serializers.py:196
msgid "Purchase order line item to receive items against"
msgstr ""

#: plugin/base/barcodes/serializers.py:202
msgid "Automatically allocate stock items to the purchase order"
msgstr ""

#: plugin/base/barcodes/serializers.py:215
msgid "Sales Order to allocate items against"
msgstr ""

#: plugin/base/barcodes/serializers.py:221
msgid "Sales order is not open"
msgstr ""

#: plugin/base/barcodes/serializers.py:229
msgid "Sales order line item to allocate items against"
msgstr ""

#: plugin/base/barcodes/serializers.py:236
msgid "Sales order shipment to allocate items against"
msgstr ""

#: plugin/base/barcodes/serializers.py:242
msgid "Shipment has already been delivered"
msgstr ""

#: plugin/base/barcodes/serializers.py:247
msgid "Quantity to allocate"
msgstr ""

#: plugin/base/label/label.py:39
msgid "Label printing failed"
msgstr "Impresión de etiquetas fallida"

#: plugin/base/label/mixins.py:55
msgid "Error rendering label to PDF"
msgstr ""

#: plugin/base/label/mixins.py:69
msgid "Error rendering label to HTML"
msgstr ""

#: plugin/base/label/mixins.py:146
msgid "No items provided to print"
msgstr ""

#: plugin/base/ui/serializers.py:30
msgid "Plugin Name"
msgstr "Nombre del complemento"

#: plugin/base/ui/serializers.py:34
msgid "Feature Type"
msgstr "Tipo de Característica"

#: plugin/base/ui/serializers.py:39
msgid "Feature Label"
msgstr "Etiqueta de características"

#: plugin/base/ui/serializers.py:44
msgid "Feature Title"
msgstr "Título de la característica"

#: plugin/base/ui/serializers.py:49
msgid "Feature Description"
msgstr "Descripción de la característica"

#: plugin/base/ui/serializers.py:54
msgid "Feature Icon"
msgstr "Icono de característica"

#: plugin/base/ui/serializers.py:58
msgid "Feature Options"
msgstr "Opciones de características"

#: plugin/base/ui/serializers.py:61
msgid "Feature Context"
msgstr "Contexto de característica"

#: plugin/base/ui/serializers.py:64
msgid "Feature Source (javascript)"
msgstr "Fuente de característica (javascript)"

#: plugin/builtin/barcodes/inventree_barcode.py:27
msgid "InvenTree Barcodes"
msgstr "Códigos de barras de InvenTree"

#: plugin/builtin/barcodes/inventree_barcode.py:28
msgid "Provides native support for barcodes"
msgstr "Proporciona soporte nativo para códigos de barras"

#: plugin/builtin/barcodes/inventree_barcode.py:30
#: plugin/builtin/exporter/bom_exporter.py:61
#: plugin/builtin/exporter/inventree_exporter.py:17
#: plugin/builtin/exporter/part_parameter_exporter.py:36
#: plugin/builtin/integration/core_notifications.py:33
#: plugin/builtin/integration/currency_exchange.py:21
#: plugin/builtin/integration/part_notifications.py:25
#: plugin/builtin/labels/inventree_label.py:26
#: plugin/builtin/labels/inventree_machine.py:64
#: plugin/builtin/labels/label_sheet.py:71
#: plugin/builtin/suppliers/digikey.py:19 plugin/builtin/suppliers/lcsc.py:21
#: plugin/builtin/suppliers/mouser.py:19 plugin/builtin/suppliers/tme.py:21
msgid "InvenTree contributors"
msgstr "Contribuidores de InvenTree"

#: plugin/builtin/barcodes/inventree_barcode.py:34
msgid "Internal Barcode Format"
msgstr ""

#: plugin/builtin/barcodes/inventree_barcode.py:35
msgid "Select an internal barcode format"
msgstr ""

#: plugin/builtin/barcodes/inventree_barcode.py:37
msgid "JSON barcodes (human readable)"
msgstr ""

#: plugin/builtin/barcodes/inventree_barcode.py:38
msgid "Short barcodes (space optimized)"
msgstr ""

#: plugin/builtin/barcodes/inventree_barcode.py:43
msgid "Short Barcode Prefix"
msgstr ""

#: plugin/builtin/barcodes/inventree_barcode.py:45
msgid "Customize the prefix used for short barcodes, may be useful for environments with multiple InvenTree instances"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:17
msgid "Levels"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:18
msgid "Number of levels to export"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:23
#: plugin/builtin/exporter/part_parameter_exporter.py:17
msgid "Stock Data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:23
#: plugin/builtin/exporter/part_parameter_exporter.py:17
msgid "Include part stock data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:27
#: plugin/builtin/exporter/part_parameter_exporter.py:21
msgid "Pricing Data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:27
#: plugin/builtin/exporter/part_parameter_exporter.py:21
msgid "Include part pricing data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:31
msgid "Supplier Data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:31
msgid "Include supplier data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:36
msgid "Manufacturer Data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:37
msgid "Include manufacturer data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:42
msgid "Substitute Data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:43
msgid "Include substitute part data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:48
msgid "Parameter Data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:49
msgid "Include part parameter data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:58
msgid "Multi-Level BOM Exporter"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:59
msgid "Provides support for exporting multi-level BOMs"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:96
msgid "BOM Level"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:102
#, python-brace-format
msgid "Substitute {n}"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:108
#, python-brace-format
msgid "Supplier {n}"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:109
#, python-brace-format
msgid "Supplier {n} SKU"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:110
#, python-brace-format
msgid "Supplier {n} MPN"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:116
#, python-brace-format
msgid "Manufacturer {n}"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:117
#, python-brace-format
msgid "Manufacturer {n} MPN"
msgstr ""

#: plugin/builtin/exporter/inventree_exporter.py:14
msgid "InvenTree Generic Exporter"
msgstr ""

#: plugin/builtin/exporter/inventree_exporter.py:15
msgid "Provides support for exporting data from InvenTree"
msgstr ""

#: plugin/builtin/exporter/part_parameter_exporter.py:33
msgid "Part Parameter Exporter"
msgstr ""

#: plugin/builtin/exporter/part_parameter_exporter.py:34
msgid "Exporter for part parameter data"
msgstr ""

#: plugin/builtin/integration/core_notifications.py:32
msgid "InvenTree Notifications"
msgstr "Notificaciones de InvenTree"

#: plugin/builtin/integration/core_notifications.py:34
msgid "Integrated outgoing notification methods"
msgstr "Métodos de notificaciones salientes integrados"

#: plugin/builtin/integration/core_notifications.py:39
#: plugin/builtin/integration/core_notifications.py:78
msgid "Enable email notifications"
msgstr "Habilitar notificaciones por correo electrónico"

#: plugin/builtin/integration/core_notifications.py:40
#: plugin/builtin/integration/core_notifications.py:79
msgid "Allow sending of emails for event notifications"
msgstr "Permitir el envío de correos electrónicos para notificaciones de eventos"

#: plugin/builtin/integration/core_notifications.py:45
msgid "Enable slack notifications"
msgstr "Activar notificaciones de slack"

#: plugin/builtin/integration/core_notifications.py:47
msgid "Allow sending of slack channel messages for event notifications"
msgstr "Permitir el envío de mensajes por canal de slack para notificaciones de eventos"

#: plugin/builtin/integration/core_notifications.py:53
msgid "Slack incoming webhook url"
msgstr "URL de webhook entrante de Slack"

#: plugin/builtin/integration/core_notifications.py:54
msgid "URL that is used to send messages to a slack channel"
msgstr "URL que se utiliza para enviar mensajes a un canal de slack"

#: plugin/builtin/integration/core_notifications.py:164
msgid "Open link"
msgstr "Abrir enlace"

#: plugin/builtin/integration/currency_exchange.py:22
msgid "InvenTree Currency Exchange"
msgstr ""

#: plugin/builtin/integration/currency_exchange.py:23
msgid "Default currency exchange integration"
msgstr ""

#: plugin/builtin/integration/part_notifications.py:24
msgid "Part Notifications"
msgstr ""

#: plugin/builtin/integration/part_notifications.py:26
msgid "Notify users about part changes"
msgstr ""

#: plugin/builtin/integration/part_notifications.py:31
msgid "Send notifications"
msgstr ""

#: plugin/builtin/integration/part_notifications.py:32
msgid "Send notifications for part changes to subscribed users"
msgstr ""

#: plugin/builtin/integration/part_notifications.py:49
msgid "Changed part notification"
msgstr ""

#: plugin/builtin/integration/part_notifications.py:59
#, python-brace-format
msgid "The part `{part.name}` has been triggered with a `{part_action}` event"
msgstr ""

#: plugin/builtin/labels/inventree_label.py:23
msgid "InvenTree PDF label printer"
msgstr "Impresora de etiquetas PDF de InvenTree"

#: plugin/builtin/labels/inventree_label.py:24
msgid "Provides native support for printing PDF labels"
msgstr "Proporciona soporte nativo para imprimir etiquetas PDF"

#: plugin/builtin/labels/inventree_label.py:32
#: plugin/builtin/labels/label_sheet.py:77
msgid "Debug mode"
msgstr "Modo de depuración"

#: plugin/builtin/labels/inventree_label.py:33
#: plugin/builtin/labels/label_sheet.py:78
msgid "Enable debug mode - returns raw HTML instead of PDF"
msgstr "Activar modo de depuración - devuelve código HTML en lugar de PDF"

#: plugin/builtin/labels/inventree_machine.py:61
msgid "InvenTree machine label printer"
msgstr ""

#: plugin/builtin/labels/inventree_machine.py:62
msgid "Provides support for printing using a machine"
msgstr ""

#: plugin/builtin/labels/inventree_machine.py:164
msgid "last used"
msgstr ""

#: plugin/builtin/labels/inventree_machine.py:181
msgid "Options"
msgstr ""

#: plugin/builtin/labels/label_sheet.py:30
msgid "Page size for the label sheet"
msgstr ""

#: plugin/builtin/labels/label_sheet.py:35
msgid "Skip Labels"
msgstr ""

#: plugin/builtin/labels/label_sheet.py:36
msgid "Skip this number of labels when printing label sheets"
msgstr ""

#: plugin/builtin/labels/label_sheet.py:42
msgid "Border"
msgstr ""

#: plugin/builtin/labels/label_sheet.py:43
msgid "Print a border around each label"
msgstr ""

#: plugin/builtin/labels/label_sheet.py:48 report/models.py:371
msgid "Landscape"
msgstr ""

#: plugin/builtin/labels/label_sheet.py:49
msgid "Print the label sheet in landscape mode"
msgstr ""

#: plugin/builtin/labels/label_sheet.py:54
msgid "Page Margin"
msgstr ""

#: plugin/builtin/labels/label_sheet.py:55
msgid "Margin around the page in mm"
msgstr ""

#: plugin/builtin/labels/label_sheet.py:68
msgid "InvenTree Label Sheet Printer"
msgstr ""

#: plugin/builtin/labels/label_sheet.py:69
msgid "Arrays multiple labels onto a single sheet"
msgstr ""

#: plugin/builtin/labels/label_sheet.py:121
msgid "Label is too large for page size"
msgstr ""

#: plugin/builtin/labels/label_sheet.py:160
msgid "No labels were generated"
msgstr ""

#: plugin/builtin/suppliers/digikey.py:16
msgid "Supplier Integration - DigiKey"
msgstr ""

#: plugin/builtin/suppliers/digikey.py:17
msgid "Provides support for scanning DigiKey barcodes"
msgstr ""

#: plugin/builtin/suppliers/digikey.py:26
msgid "The Supplier which acts as 'DigiKey'"
msgstr ""

#: plugin/builtin/suppliers/lcsc.py:18
msgid "Supplier Integration - LCSC"
msgstr ""

#: plugin/builtin/suppliers/lcsc.py:19
msgid "Provides support for scanning LCSC barcodes"
msgstr ""

#: plugin/builtin/suppliers/lcsc.py:27
msgid "The Supplier which acts as 'LCSC'"
msgstr ""

#: plugin/builtin/suppliers/mouser.py:16
msgid "Supplier Integration - Mouser"
msgstr ""

#: plugin/builtin/suppliers/mouser.py:17
msgid "Provides support for scanning Mouser barcodes"
msgstr ""

#: plugin/builtin/suppliers/mouser.py:25
msgid "The Supplier which acts as 'Mouser'"
msgstr ""

#: plugin/builtin/suppliers/tme.py:18
msgid "Supplier Integration - TME"
msgstr ""

#: plugin/builtin/suppliers/tme.py:19
msgid "Provides support for scanning TME barcodes"
msgstr ""

#: plugin/builtin/suppliers/tme.py:27
msgid "The Supplier which acts as 'TME'"
msgstr ""

#: plugin/installer.py:229 plugin/installer.py:307
msgid "Only staff users can administer plugins"
msgstr ""

#: plugin/installer.py:232
msgid "Plugin installation is disabled"
msgstr ""

#: plugin/installer.py:269
msgid "Installed plugin successfully"
msgstr ""

#: plugin/installer.py:274
#, python-brace-format
msgid "Installed plugin into {path}"
msgstr ""

#: plugin/installer.py:298
msgid "Plugin was not found in registry"
msgstr ""

#: plugin/installer.py:301
msgid "Plugin is not a packaged plugin"
msgstr ""

#: plugin/installer.py:304
msgid "Plugin package name not found"
msgstr ""

#: plugin/installer.py:324
msgid "Plugin uninstalling is disabled"
msgstr ""

#: plugin/installer.py:328
msgid "Plugin cannot be uninstalled as it is currently active"
msgstr ""

#: plugin/installer.py:332
msgid "Plugin is not installed"
msgstr "El complemento no está instalado"

#: plugin/installer.py:348
msgid "Plugin installation not found"
msgstr "Instalación del complemento no encontrada"

#: plugin/installer.py:363
msgid "Uninstalled plugin successfully"
msgstr ""

#: plugin/models.py:38
msgid "Plugin Configuration"
msgstr "Configuración del complemento"

#: plugin/models.py:39
msgid "Plugin Configurations"
msgstr "Configuraciones del Plug-in"

#: plugin/models.py:46
msgid "Key of plugin"
msgstr "Clave del complemento"

#: plugin/models.py:54
msgid "PluginName of the plugin"
msgstr "Nombre del complemento"

#: plugin/models.py:61 plugin/serializers.py:119
msgid "Package Name"
msgstr "Nombre de Paquete"

#: plugin/models.py:63
msgid "Name of the installed package, if the plugin was installed via PIP"
msgstr ""

#: plugin/models.py:68
msgid "Is the plugin active"
msgstr "Está activo el complemento"

#: plugin/models.py:168
msgid "Sample plugin"
msgstr "Complemento de ejemplo"

#: plugin/models.py:176
msgid "Builtin Plugin"
msgstr "Complemento integrado"

#: plugin/models.py:184
msgid "Mandatory Plugin"
msgstr ""

#: plugin/models.py:189
msgid "Package Plugin"
msgstr ""

#: plugin/models.py:268
msgid "Plugin"
msgstr "Complemento"

#: plugin/models.py:315
msgid "Method"
msgstr "Método"

#: plugin/plugin.py:312
msgid "No author found"
msgstr "No se encontró autor"

#: plugin/registry.py:611
#, python-brace-format
msgid "Plugin '{p}' is not compatible with the current InvenTree version {v}"
msgstr "El complemento '{p}' no es compatible con la versión actual de InvenTree {v}"

#: plugin/registry.py:614
#, python-brace-format
msgid "Plugin requires at least version {v}"
msgstr "El complemento requiere al menos la versión {v}"

#: plugin/registry.py:616
#, python-brace-format
msgid "Plugin requires at most version {v}"
msgstr "El complemento requiere como máximo la versión {v}"

#: plugin/samples/integration/sample.py:52
msgid "Enable PO"
msgstr "Habilitar PO"

#: plugin/samples/integration/sample.py:53
msgid "Enable PO functionality in InvenTree interface"
msgstr "Habilitar la funcionalidad PO en la interfaz de InvenTree"

#: plugin/samples/integration/sample.py:58
msgid "API Key"
msgstr "Clave API"

#: plugin/samples/integration/sample.py:59
msgid "Key required for accessing external API"
msgstr "Clave necesaria para acceder a la API externa"

#: plugin/samples/integration/sample.py:63
msgid "Numerical"
msgstr "Numérico"

#: plugin/samples/integration/sample.py:64
msgid "A numerical setting"
msgstr "Una configuración numérica"

#: plugin/samples/integration/sample.py:69
msgid "Choice Setting"
msgstr "Configuración de Elección"

#: plugin/samples/integration/sample.py:70
msgid "A setting with multiple choices"
msgstr "Un ajuste con múltiples opciones"

#: plugin/samples/integration/sample_currency_exchange.py:15
msgid "Sample currency exchange plugin"
msgstr ""

#: plugin/samples/integration/sample_currency_exchange.py:18
msgid "InvenTree Contributors"
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:27
msgid "Enable Part Panels"
msgstr "Habilitar paneles de piezas"

#: plugin/samples/integration/user_interface_sample.py:28
msgid "Enable custom panels for Part views"
msgstr "Habilitar paneles personalizados para vistas de piezas"

#: plugin/samples/integration/user_interface_sample.py:33
msgid "Enable Purchase Order Panels"
msgstr "Habilitar paneles de orden de compra"

#: plugin/samples/integration/user_interface_sample.py:34
msgid "Enable custom panels for Purchase Order views"
msgstr "Habilitar paneles personalizados para las vistas de orden de compra"

#: plugin/samples/integration/user_interface_sample.py:39
msgid "Enable Broken Panels"
msgstr "Habilitar paneles rotos"

#: plugin/samples/integration/user_interface_sample.py:40
msgid "Enable broken panels for testing"
msgstr "Habilitar paneles rotos para la prueba"

#: plugin/samples/integration/user_interface_sample.py:45
msgid "Enable Dynamic Panel"
msgstr "Habilitar Panel Dinámico"

#: plugin/samples/integration/user_interface_sample.py:46
msgid "Enable dynamic panels for testing"
msgstr "Habilitar paneles dinámicos para pruebas"

#: plugin/samples/integration/user_interface_sample.py:98
msgid "Part Panel"
msgstr "Panel de pieza"

#: plugin/samples/integration/user_interface_sample.py:131
msgid "Broken Dashboard Item"
msgstr "Artículo de panel roto"

#: plugin/samples/integration/user_interface_sample.py:133
msgid "This is a broken dashboard item - it will not render!"
msgstr "Este es panel roto - ¡no se renderizará!"

#: plugin/samples/integration/user_interface_sample.py:139
msgid "Sample Dashboard Item"
msgstr "Panel de control de ejemplo"

#: plugin/samples/integration/user_interface_sample.py:141
msgid "This is a sample dashboard item. It renders a simple string of HTML content."
msgstr "Este es un panel de control de ejemplo. Muestra una cadena simple de contenido HTML."

#: plugin/samples/integration/user_interface_sample.py:147
msgid "Context Dashboard Item"
msgstr "Elemento de panel de control de contexto"

#: plugin/samples/integration/user_interface_sample.py:161
msgid "Admin Dashboard Item"
msgstr "Elemento de panel de control de administración"

#: plugin/samples/integration/user_interface_sample.py:162
msgid "This is an admin-only dashboard item."
msgstr "Este es un elemento de panel de administración solamente."

#: plugin/serializers.py:86
msgid "Source File"
msgstr "Archivo de origen"

#: plugin/serializers.py:87
msgid "Path to the source file for admin integration"
msgstr "Ruta al archivo de origen para la integración del administrador"

#: plugin/serializers.py:94
msgid "Optional context data for the admin integration"
msgstr "Datos de contexto opcionales para la integración del administrador"

#: plugin/serializers.py:110
msgid "Source URL"
msgstr "URL de origen"

#: plugin/serializers.py:112
msgid "Source for the package - this can be a custom registry or a VCS path"
msgstr "Fuente del paquete - puede ser un registro personalizado o una ruta VCS"

#: plugin/serializers.py:121
msgid "Name for the Plugin Package - can also contain a version indicator"
msgstr "Nombre del paquete Plug-in - también puede contener un indicador de versión"

#: plugin/serializers.py:128
msgid "Version"
msgstr "Versión"

#: plugin/serializers.py:130
msgid "Version specifier for the plugin. Leave blank for latest version."
msgstr ""

#: plugin/serializers.py:135
msgid "Confirm plugin installation"
msgstr "Confirmar instalación del complemento"

#: plugin/serializers.py:137
msgid "This will install this plugin now into the current instance. The instance will go into maintenance."
msgstr "Esto instalará este plug-in en la instancia actual. La instancia entrará en mantenimiento."

#: plugin/serializers.py:150
msgid "Installation not confirmed"
msgstr "Instalación no confirmada"

#: plugin/serializers.py:152
msgid "Either packagename of URL must be provided"
msgstr "Debe proporcionar cualquier nombre de paquete de la URL"

#: plugin/serializers.py:188
msgid "Full reload"
msgstr ""

#: plugin/serializers.py:189
msgid "Perform a full reload of the plugin registry"
msgstr ""

#: plugin/serializers.py:195
msgid "Force reload"
msgstr ""

#: plugin/serializers.py:197
msgid "Force a reload of the plugin registry, even if it is already loaded"
msgstr ""

#: plugin/serializers.py:204
msgid "Collect plugins"
msgstr ""

#: plugin/serializers.py:205
msgid "Collect plugins and add them to the registry"
msgstr ""

#: plugin/serializers.py:232
msgid "Activate Plugin"
msgstr "Activar complemento"

#: plugin/serializers.py:233
msgid "Activate this plugin"
msgstr "Activar este complemento"

#: plugin/serializers.py:253
msgid "Delete configuration"
msgstr ""

#: plugin/serializers.py:254
msgid "Delete the plugin configuration from the database"
msgstr ""

#: report/api.py:44 report/serializers.py:98 report/serializers.py:148
msgid "Items"
msgstr ""

#: report/api.py:121
msgid "Plugin not found"
msgstr ""

#: report/api.py:123
msgid "Plugin is not active"
msgstr ""

#: report/api.py:125
msgid "Plugin does not support label printing"
msgstr ""

#: report/api.py:173
msgid "Invalid label dimensions"
msgstr "Dimensiones de etiqueta inválidas"

#: report/api.py:188 report/api.py:267
msgid "No valid items provided to template"
msgstr ""

#: report/helpers.py:43
msgid "A4"
msgstr "A4"

#: report/helpers.py:44
msgid "A3"
msgstr "A3"

#: report/helpers.py:45
msgid "Legal"
msgstr "Legal"

#: report/helpers.py:46
msgid "Letter"
msgstr "Carta"

#: report/models.py:117
msgid "Template file with this name already exists"
msgstr ""

#: report/models.py:204
msgid "Template name"
msgstr "Nombre de la plantilla"

#: report/models.py:210
msgid "Template description"
msgstr ""

#: report/models.py:216
msgid "Revision number (auto-increments)"
msgstr ""

#: report/models.py:222
msgid "Attach to Model on Print"
msgstr "Adjuntar al modelo al imprimir"

#: report/models.py:224
msgid "Save report output as an attachment against linked model instance when printing"
msgstr "Guardar la salida del informe como un archivo adjunto contra la instancia del modelo enlazado al imprimir"

#: report/models.py:265
msgid "Filename Pattern"
msgstr "Patrón de Nombre de archivo"

#: report/models.py:266
msgid "Pattern for generating filenames"
msgstr ""

#: report/models.py:271
msgid "Template is enabled"
msgstr ""

#: report/models.py:278
msgid "Target model type for template"
msgstr ""

#: report/models.py:298
msgid "Filters"
msgstr "Filtros"

#: report/models.py:299
msgid "Template query filters (comma-separated list of key=value pairs)"
msgstr ""

#: report/models.py:358 report/models.py:564
msgid "Template file"
msgstr ""

#: report/models.py:366
msgid "Page size for PDF reports"
msgstr "Tamaño de página para reportes PDF"

#: report/models.py:372
msgid "Render report in landscape orientation"
msgstr ""

#: report/models.py:486
#, python-brace-format
msgid "Report generated from template {self.name}"
msgstr ""

#: report/models.py:511
msgid "Error generating report"
msgstr ""

#: report/models.py:570
msgid "Width [mm]"
msgstr "Ancho [mm]"

#: report/models.py:571
msgid "Label width, specified in mm"
msgstr "Ancho de la etiqueta, especificado en mm"

#: report/models.py:577
msgid "Height [mm]"
msgstr "Altura [mm]"

#: report/models.py:578
msgid "Label height, specified in mm"
msgstr "Altura de la etiqueta, especificada en mm"

#: report/models.py:688
msgid "Error printing labels"
msgstr ""

#: report/models.py:707
msgid "Snippet"
msgstr "Fragmento"

#: report/models.py:708
msgid "Report snippet file"
msgstr "Archivo fragmento de informe"

#: report/models.py:715
msgid "Snippet file description"
msgstr "Descripción de archivo de fragmento"

#: report/models.py:733
msgid "Asset"
msgstr "Activo"

#: report/models.py:734
msgid "Report asset file"
msgstr "Reportar archivo de activos"

#: report/models.py:741
msgid "Asset file description"
msgstr "Descripción del archivo de activos"

#: report/serializers.py:91
msgid "Select report template"
msgstr ""

#: report/serializers.py:99 report/serializers.py:149
msgid "List of item primary keys to include in the report"
msgstr ""

#: report/serializers.py:132
msgid "Select label template"
msgstr "Selecciona la plantilla de etiqueta"

#: report/serializers.py:140
msgid "Printing Plugin"
msgstr "Complemento de impresión"

#: report/serializers.py:141
msgid "Select plugin to use for label printing"
msgstr "Selecciona el complemento a usar para imprimir etiquetas"

#: report/templates/label/part_label.html:31
#: report/templates/label/stockitem_qr.html:21
#: report/templates/label/stocklocation_qr.html:20
msgid "QR Code"
msgstr "Código QR"

#: report/templates/label/part_label_code128.html:31
#: report/templates/label/stocklocation_qr_and_text.html:31
msgid "QR code"
msgstr "Código QR"

#: report/templates/report/inventree_bill_of_materials_report.html:100
msgid "Bill of Materials"
msgstr "Lista de Materiales"

#: report/templates/report/inventree_bill_of_materials_report.html:133
msgid "Materials needed"
msgstr "Materiales necesarios"

#: report/templates/report/inventree_build_order_report.html:98
#: report/templates/report/inventree_purchase_order_report.html:40
#: report/templates/report/inventree_sales_order_report.html:40
#: report/templates/report/inventree_sales_order_shipment_report.html:37
#: report/templates/report/inventree_test_report.html:84
#: report/templates/report/inventree_test_report.html:162
msgid "Part image"
msgstr "Imagen de parte"

#: report/templates/report/inventree_build_order_report.html:121
msgid "Issued"
msgstr "Emitido"

#: report/templates/report/inventree_build_order_report.html:146
msgid "Required For"
msgstr "Requerido para"

#: report/templates/report/inventree_purchase_order_report.html:15
msgid "Supplier was deleted"
msgstr "El proveedor ha sido eliminado"

#: report/templates/report/inventree_purchase_order_report.html:30
#: report/templates/report/inventree_sales_order_report.html:30
msgid "Unit Price"
msgstr "Precio Unitario"

#: report/templates/report/inventree_purchase_order_report.html:55
#: report/templates/report/inventree_return_order_report.html:48
#: report/templates/report/inventree_sales_order_report.html:55
msgid "Extra Line Items"
msgstr "Partida extra"

#: report/templates/report/inventree_purchase_order_report.html:72
#: report/templates/report/inventree_sales_order_report.html:72
msgid "Total"
msgstr "Total"

#: report/templates/report/inventree_return_order_report.html:25
#: report/templates/report/inventree_sales_order_shipment_report.html:45
#: report/templates/report/inventree_test_report.html:88 stock/models.py:1006
#: stock/serializers.py:163
msgid "Serial Number"
msgstr "Número de serie"

#: report/templates/report/inventree_sales_order_shipment_report.html:23
msgid "Allocations"
msgstr "Asignaciones"

#: report/templates/report/inventree_sales_order_shipment_report.html:47
msgid "Batch"
msgstr "Lote"

#: report/templates/report/inventree_stock_location_report.html:97
msgid "Stock location items"
msgstr ""

#: report/templates/report/inventree_test_report.html:21
msgid "Stock Item Test Report"
msgstr "Artículo Stock Informe de prueba"

#: report/templates/report/inventree_test_report.html:97
msgid "Test Results"
msgstr "Resultados de la Prueba"

#: report/templates/report/inventree_test_report.html:102
msgid "Test"
msgstr "Prueba"

#: report/templates/report/inventree_test_report.html:129
msgid "Pass"
msgstr "Pasada"

#: report/templates/report/inventree_test_report.html:131
msgid "Fail"
msgstr "Fallo"

#: report/templates/report/inventree_test_report.html:138
msgid "No result (required)"
msgstr "Ningún resultado (requerido)"

#: report/templates/report/inventree_test_report.html:140
msgid "No result"
msgstr "Sin resultados"

#: report/templates/report/inventree_test_report.html:153
#: stock/serializers.py:668
msgid "Installed Items"
msgstr "Elementos instalados"

#: report/templates/report/inventree_test_report.html:167
msgid "Serial"
msgstr "Número de serie"

#: report/templatetags/report.py:144
msgid "Asset file does not exist"
msgstr ""

#: report/templatetags/report.py:201 report/templatetags/report.py:277
msgid "Image file not found"
msgstr ""

#: report/templatetags/report.py:302
msgid "part_image tag requires a Part instance"
msgstr ""

#: report/templatetags/report.py:349
msgid "company_image tag requires a Company instance"
msgstr ""

#: stock/api.py:255
msgid "Filter by location depth"
msgstr ""

#: stock/api.py:275
msgid "Filter by top-level locations"
msgstr ""

#: stock/api.py:290
msgid "Include sub-locations in filtered results"
msgstr ""

#: stock/api.py:311 stock/serializers.py:1256
msgid "Parent Location"
msgstr "Ubicación principal"

#: stock/api.py:312
msgid "Filter by parent location"
msgstr "Filtrar por ubicación principal"

#: stock/api.py:554
msgid "Part name (case insensitive)"
msgstr "Nombre de pieza (insensible a mayúsculas y minúsculas)"

#: stock/api.py:560
msgid "Part name contains (case insensitive)"
msgstr "El nombre de la pieza (insensible a mayúsculas y minúsculas)"

#: stock/api.py:566
msgid "Part name (regex)"
msgstr "Nombre de la pieza (expresión regular)"

#: stock/api.py:571
msgid "Part IPN (case insensitive)"
msgstr "Pieza IPN (insensible a mayúsculas y minúsculas)"

#: stock/api.py:577
msgid "Part IPN contains (case insensitive)"
msgstr "Pieza IPN (insensible a mayúsculas y minúsculas)"

#: stock/api.py:583
msgid "Part IPN (regex)"
msgstr "Pieza IPN (expresión regular)"

#: stock/api.py:595
msgid "Minimum stock"
msgstr "Stock mínimo"

#: stock/api.py:599
msgid "Maximum stock"
msgstr "Stock máximo"

#: stock/api.py:602
msgid "Status Code"
msgstr "Código de estado"

#: stock/api.py:642
msgid "External Location"
msgstr "Ubicación externa"

#: stock/api.py:741
msgid "Consumed by Build Order"
msgstr ""

#: stock/api.py:751
msgid "Installed in other stock item"
msgstr "Instalado en otro artículo de existencias"

#: stock/api.py:840
msgid "Part Tree"
msgstr "Árbol de piezas"

#: stock/api.py:862
msgid "Updated before"
msgstr "Actualizado antes"

#: stock/api.py:866
msgid "Updated after"
msgstr "Actualizado después"

#: stock/api.py:870
msgid "Stocktake Before"
msgstr "Inventario antes"

#: stock/api.py:874
msgid "Stocktake After"
msgstr "Inventario después"

#: stock/api.py:879
msgid "Expiry date before"
msgstr ""

#: stock/api.py:883
msgid "Expiry date after"
msgstr ""

#: stock/api.py:886 stock/serializers.py:673
msgid "Stale"
msgstr "Desactualizado"

#: stock/api.py:987
msgid "Quantity is required"
msgstr "Cantidad requerida"

#: stock/api.py:992
msgid "Valid part must be supplied"
msgstr "Debe suministrarse una parte válida"

#: stock/api.py:1019
msgid "The given supplier part does not exist"
msgstr ""

#: stock/api.py:1029
msgid "The supplier part has a pack size defined, but flag use_pack_size not set"
msgstr ""

#: stock/api.py:1056
msgid "Serial numbers cannot be supplied for a non-trackable part"
msgstr ""

#: stock/models.py:70
msgid "Stock Location type"
msgstr ""

#: stock/models.py:71
msgid "Stock Location types"
msgstr ""

#: stock/models.py:97
msgid "Default icon for all locations that have no icon set (optional)"
msgstr ""

#: stock/models.py:155 stock/models.py:968
msgid "Stock Location"
msgstr "Ubicación de Stock"

#: stock/models.py:156 users/ruleset.py:35
msgid "Stock Locations"
msgstr "Ubicaciones de Stock"

#: stock/models.py:205 stock/models.py:1133
msgid "Owner"
msgstr "Propietario"

#: stock/models.py:206 stock/models.py:1134
msgid "Select Owner"
msgstr "Seleccionar Propietario"

#: stock/models.py:214
msgid "Stock items may not be directly located into a structural stock locations, but may be located to child locations."
msgstr ""

#: stock/models.py:221 users/models.py:499
msgid "External"
msgstr "Externo"

#: stock/models.py:222
msgid "This is an external stock location"
msgstr ""

#: stock/models.py:228
msgid "Location type"
msgstr ""

#: stock/models.py:232
msgid "Stock location type of this location"
msgstr ""

#: stock/models.py:304
msgid "You cannot make this stock location structural because some stock items are already located into it!"
msgstr ""

#: stock/models.py:562
msgid "Part must be specified"
msgstr "Se debe especificar la pieza"

#: stock/models.py:827
msgid "Stock items cannot be located into structural stock locations!"
msgstr ""

#: stock/models.py:854 stock/serializers.py:516
msgid "Stock item cannot be created for virtual parts"
msgstr ""

#: stock/models.py:871
#, python-brace-format
msgid "Part type ('{self.supplier_part.part}') must be {self.part}"
msgstr ""

#: stock/models.py:881 stock/models.py:894
msgid "Quantity must be 1 for item with a serial number"
msgstr "La cantidad debe ser 1 para el artículo con un número de serie"

#: stock/models.py:884
msgid "Serial number cannot be set if quantity greater than 1"
msgstr "Número de serie no se puede establecer si la cantidad es mayor que 1"

#: stock/models.py:906
msgid "Item cannot belong to itself"
msgstr "El objeto no puede pertenecer a sí mismo"

#: stock/models.py:911
msgid "Item must have a build reference if is_building=True"
msgstr "El artículo debe tener una referencia de construcción si is_building=True"

#: stock/models.py:924
msgid "Build reference does not point to the same part object"
msgstr "La referencia de la construcción no apunta al mismo objeto de parte"

#: stock/models.py:938
msgid "Parent Stock Item"
msgstr "Artículo de stock padre"

#: stock/models.py:950
msgid "Base part"
msgstr "Parte base"

#: stock/models.py:960
msgid "Select a matching supplier part for this stock item"
msgstr "Seleccione una parte del proveedor correspondiente para este artículo de stock"

#: stock/models.py:972
msgid "Where is this stock item located?"
msgstr "¿Dónde se encuentra este artículo de stock?"

#: stock/models.py:980 stock/serializers.py:1667
msgid "Packaging this stock item is stored in"
msgstr "Empaquetar este artículo de stock se almacena en"

#: stock/models.py:986
msgid "Installed In"
msgstr "Instalado en"

#: stock/models.py:991
msgid "Is this item installed in another item?"
msgstr "¿Está este artículo instalado en otro artículo?"

#: stock/models.py:1010
msgid "Serial number for this item"
msgstr "Número de serie para este artículo"

#: stock/models.py:1027 stock/serializers.py:1652
msgid "Batch code for this stock item"
msgstr "Código de lote para este artículo de stock"

#: stock/models.py:1032
msgid "Stock Quantity"
msgstr "Cantidad de Stock"

#: stock/models.py:1042
msgid "Source Build"
msgstr "Build de origen"

#: stock/models.py:1045
msgid "Build for this stock item"
msgstr "Build para este item de stock"

#: stock/models.py:1052
msgid "Consumed By"
msgstr "Consumido por"

#: stock/models.py:1055
msgid "Build order which consumed this stock item"
msgstr ""

#: stock/models.py:1064
msgid "Source Purchase Order"
msgstr "Orden de compra de origen"

#: stock/models.py:1068
msgid "Purchase order for this stock item"
msgstr "Orden de compra para este artículo de stock"

#: stock/models.py:1074
msgid "Destination Sales Order"
msgstr "Orden de venta de destino"

#: stock/models.py:1085
msgid "Expiry date for stock item. Stock will be considered expired after this date"
msgstr "Fecha de caducidad del artículo de stock. El stock se considerará caducado después de esta fecha"

#: stock/models.py:1103
msgid "Delete on deplete"
msgstr "Eliminar al agotar"

#: stock/models.py:1104
msgid "Delete this Stock Item when stock is depleted"
msgstr "Eliminar este artículo de stock cuando se agoten las existencias"

#: stock/models.py:1125
msgid "Single unit purchase price at time of purchase"
msgstr "Precio de compra único en el momento de la compra"

#: stock/models.py:1156
msgid "Converted to part"
msgstr "Convertido a parte"

#: stock/models.py:1721
msgid "Part is not set as trackable"
msgstr "La parte no está establecida como rastreable"

#: stock/models.py:1727
msgid "Quantity must be integer"
msgstr "Cantidad debe ser un entero"

#: stock/models.py:1735
#, python-brace-format
msgid "Quantity must not exceed available stock quantity ({self.quantity})"
msgstr ""

#: stock/models.py:1741
msgid "Serial numbers must be provided as a list"
msgstr "Los números de serie deben ser proporcionados como una lista"

#: stock/models.py:1746
msgid "Quantity does not match serial numbers"
msgstr "La cantidad no coincide con los números de serie"

#: stock/models.py:1868 stock/models.py:2777
msgid "Test template does not exist"
msgstr ""

#: stock/models.py:1886
msgid "Stock item has been assigned to a sales order"
msgstr "Artículo de stock ha sido asignado a un pedido de venta"

#: stock/models.py:1890
msgid "Stock item is installed in another item"
msgstr "Artículo de stock está instalado en otro artículo"

#: stock/models.py:1893
msgid "Stock item contains other items"
msgstr "Artículo de stock contiene otros artículos"

#: stock/models.py:1896
msgid "Stock item has been assigned to a customer"
msgstr "Artículo de stock ha sido asignado a un cliente"

#: stock/models.py:1899 stock/models.py:2073
msgid "Stock item is currently in production"
msgstr "El artículo de stock está en producción"

#: stock/models.py:1902
msgid "Serialized stock cannot be merged"
msgstr "Stock serializado no puede ser combinado"

#: stock/models.py:1909 stock/serializers.py:1545
msgid "Duplicate stock items"
msgstr "Artículos de Stock Duplicados"

#: stock/models.py:1913
msgid "Stock items must refer to the same part"
msgstr "Los artículos de stock deben referirse a la misma parte"

#: stock/models.py:1921
msgid "Stock items must refer to the same supplier part"
msgstr "Los artículos de stock deben referirse a la misma parte del proveedor"

#: stock/models.py:1926
msgid "Stock status codes must match"
msgstr "Los códigos de estado del stock deben coincidir"

#: stock/models.py:2196
msgid "StockItem cannot be moved as it is not in stock"
msgstr "Stock no se puede mover porque no está en stock"

#: stock/models.py:2678
msgid "Stock Item Tracking"
msgstr ""

#: stock/models.py:2709
msgid "Entry notes"
msgstr "Notas de entrada"

#: stock/models.py:2749
msgid "Stock Item Test Result"
msgstr ""

#: stock/models.py:2780
msgid "Value must be provided for this test"
msgstr "Debe proporcionarse un valor para esta prueba"

#: stock/models.py:2784
msgid "Attachment must be uploaded for this test"
msgstr "El archivo adjunto debe ser subido para esta prueba"

#: stock/models.py:2789
msgid "Invalid value for this test"
msgstr ""

#: stock/models.py:2813
msgid "Test result"
msgstr "Resultado de la prueba"

#: stock/models.py:2820
msgid "Test output value"
msgstr "Valor de salida de prueba"

#: stock/models.py:2828 stock/serializers.py:259
msgid "Test result attachment"
msgstr "Adjunto de resultados de prueba"

#: stock/models.py:2832
msgid "Test notes"
msgstr "Notas de prueba"

#: stock/models.py:2840
msgid "Test station"
msgstr ""

#: stock/models.py:2841
msgid "The identifier of the test station where the test was performed"
msgstr ""

#: stock/models.py:2847
msgid "Started"
msgstr ""

#: stock/models.py:2848
msgid "The timestamp of the test start"
msgstr ""

#: stock/models.py:2854
msgid "Finished"
msgstr "Finalizó"

#: stock/models.py:2855
msgid "The timestamp of the test finish"
msgstr ""

#: stock/serializers.py:84
msgid "Generated batch code"
msgstr ""

#: stock/serializers.py:93
msgid "Select build order"
msgstr ""

#: stock/serializers.py:102
msgid "Select stock item to generate batch code for"
msgstr ""

#: stock/serializers.py:111
msgid "Select location to generate batch code for"
msgstr ""

#: stock/serializers.py:120
msgid "Select part to generate batch code for"
msgstr ""

#: stock/serializers.py:129
msgid "Select purchase order"
msgstr ""

#: stock/serializers.py:136
msgid "Enter quantity for batch code"
msgstr ""

#: stock/serializers.py:162
msgid "Generated serial number"
msgstr ""

#: stock/serializers.py:172
msgid "Select part to generate serial number for"
msgstr ""

#: stock/serializers.py:180
msgid "Quantity of serial numbers to generate"
msgstr ""

#: stock/serializers.py:248
msgid "Test template for this result"
msgstr ""

#: stock/serializers.py:272
msgid "Template ID or test name must be provided"
msgstr ""

#: stock/serializers.py:304
msgid "The test finished time cannot be earlier than the test started time"
msgstr ""

#: stock/serializers.py:341
msgid "Serial number is too large"
msgstr "El número de serie es demasiado grande"

#: stock/serializers.py:487
msgid "Parent Item"
msgstr "Elemento padre"

#: stock/serializers.py:488
msgid "Parent stock item"
msgstr ""

#: stock/serializers.py:508
msgid "Use pack size when adding: the quantity defined is the number of packs"
msgstr ""

#: stock/serializers.py:618
msgid "Supplier Part Number"
msgstr "Número de pieza del proveedor"

#: stock/serializers.py:665 users/models.py:193
msgid "Expired"
msgstr "Expirado"

#: stock/serializers.py:671
msgid "Child Items"
msgstr "Elementos secundarios"

#: stock/serializers.py:675
msgid "Tracking Items"
msgstr ""

#: stock/serializers.py:681
msgid "Purchase price of this stock item, per unit or pack"
msgstr ""

#: stock/serializers.py:719
msgid "Enter number of stock items to serialize"
msgstr "Introduzca el número de artículos de stock para serializar"

#: stock/serializers.py:732
#, python-brace-format
msgid "Quantity must not exceed available stock quantity ({q})"
msgstr "La cantidad no debe exceder la cantidad disponible de stock ({q})"

#: stock/serializers.py:739
msgid "Enter serial numbers for new items"
msgstr "Introduzca números de serie para nuevos artículos"

#: stock/serializers.py:750 stock/serializers.py:1502 stock/serializers.py:1790
msgid "Destination stock location"
msgstr "Ubicación de stock de destino"

#: stock/serializers.py:757
msgid "Optional note field"
msgstr "Campo de nota opcional"

#: stock/serializers.py:767
msgid "Serial numbers cannot be assigned to this part"
msgstr "Los números de serie no se pueden asignar a esta parte"

#: stock/serializers.py:787
msgid "Serial numbers already exist"
msgstr "Números de serie ya existen"

#: stock/serializers.py:826
msgid "Select stock item to install"
msgstr ""

#: stock/serializers.py:833
msgid "Quantity to Install"
msgstr ""

#: stock/serializers.py:834
msgid "Enter the quantity of items to install"
msgstr ""

#: stock/serializers.py:839 stock/serializers.py:919 stock/serializers.py:1049
#: stock/serializers.py:1101
msgid "Add transaction note (optional)"
msgstr "Añadir nota de transacción (opcional)"

#: stock/serializers.py:847
msgid "Quantity to install must be at least 1"
msgstr ""

#: stock/serializers.py:855
msgid "Stock item is unavailable"
msgstr ""

#: stock/serializers.py:866
msgid "Selected part is not in the Bill of Materials"
msgstr ""

#: stock/serializers.py:879
msgid "Quantity to install must not exceed available quantity"
msgstr ""

#: stock/serializers.py:914
msgid "Destination location for uninstalled item"
msgstr ""

#: stock/serializers.py:949
msgid "Select part to convert stock item into"
msgstr ""

#: stock/serializers.py:962
msgid "Selected part is not a valid option for conversion"
msgstr ""

#: stock/serializers.py:979
msgid "Cannot convert stock item with assigned SupplierPart"
msgstr ""

#: stock/serializers.py:1013
msgid "Stock item status code"
msgstr ""

#: stock/serializers.py:1042
msgid "Destination location for returned item"
msgstr ""

#: stock/serializers.py:1085
msgid "Select stock items to change status"
msgstr ""

#: stock/serializers.py:1091
msgid "No stock items selected"
msgstr ""

#: stock/serializers.py:1185 stock/serializers.py:1262
msgid "Sublocations"
msgstr "Sub-ubicación"

#: stock/serializers.py:1257
msgid "Parent stock location"
msgstr ""

#: stock/serializers.py:1374
msgid "Part must be salable"
msgstr "La parte debe ser vendible"

#: stock/serializers.py:1378
msgid "Item is allocated to a sales order"
msgstr "El artículo está asignado a una orden de venta"

#: stock/serializers.py:1382
msgid "Item is allocated to a build order"
msgstr "El artículo está asignado a una orden de creación"

#: stock/serializers.py:1406
msgid "Customer to assign stock items"
msgstr "Cliente para asignar artículos de stock"

#: stock/serializers.py:1412
msgid "Selected company is not a customer"
msgstr "La empresa seleccionada no es un cliente"

#: stock/serializers.py:1420
msgid "Stock assignment notes"
msgstr "Notas de asignación de stock"

#: stock/serializers.py:1430 stock/serializers.py:1695
msgid "A list of stock items must be provided"
msgstr "Debe proporcionarse una lista de artículos de stock"

#: stock/serializers.py:1509
msgid "Stock merging notes"
msgstr "Notas de fusión de stock"

#: stock/serializers.py:1514
msgid "Allow mismatched suppliers"
msgstr "Permitir proveedores no coincidentes"

#: stock/serializers.py:1515
msgid "Allow stock items with different supplier parts to be merged"
msgstr "Permitir fusionar artículos de stock con diferentes partes de proveedor"

#: stock/serializers.py:1520
msgid "Allow mismatched status"
msgstr "Permitir estado no coincidente"

#: stock/serializers.py:1521
msgid "Allow stock items with different status codes to be merged"
msgstr "Permitir fusionar artículos de stock con diferentes códigos de estado"

#: stock/serializers.py:1531
msgid "At least two stock items must be provided"
msgstr "Debe proporcionar al menos dos artículos de stock"

#: stock/serializers.py:1598
msgid "No Change"
msgstr "Sin cambios"

#: stock/serializers.py:1627
msgid "StockItem primary key value"
msgstr "Valor de clave primaria de Stock"

#: stock/serializers.py:1639
msgid "Stock item is not in stock"
msgstr "No hay existencias del artículo"

#: stock/serializers.py:1685
msgid "Stock transaction notes"
msgstr "Notas de transacción de stock"

#: stock/serializers.py:1831
msgid "Next Serial Number"
msgstr ""

#: stock/serializers.py:1837
msgid "Previous Serial Number"
msgstr ""

#: stock/status_codes.py:11
msgid "OK"
msgstr "OK"

#: stock/status_codes.py:12
msgid "Attention needed"
msgstr "Atención necesaria"

#: stock/status_codes.py:13
msgid "Damaged"
msgstr "Dañado"

#: stock/status_codes.py:14
msgid "Destroyed"
msgstr "Destruido"

#: stock/status_codes.py:15
msgid "Rejected"
msgstr "Rechazado"

#: stock/status_codes.py:19
msgid "Quarantined"
msgstr "En cuarentena"

#: stock/status_codes.py:44
msgid "Legacy stock tracking entry"
msgstr "Entrada antigua de rastreo de stock"

#: stock/status_codes.py:46
msgid "Stock item created"
msgstr "Artículo de stock creado"

#: stock/status_codes.py:49
msgid "Edited stock item"
msgstr "Artículo de almacén editado"

#: stock/status_codes.py:50
msgid "Assigned serial number"
msgstr "Número de serie asignado"

#: stock/status_codes.py:53
msgid "Stock counted"
msgstr "Stock contado"

#: stock/status_codes.py:54
msgid "Stock manually added"
msgstr "Stock añadido manualmente"

#: stock/status_codes.py:55
msgid "Stock manually removed"
msgstr "Stock eliminado manualmente"

#: stock/status_codes.py:58
msgid "Location changed"
msgstr "Ubicación cambiada"

#: stock/status_codes.py:59
msgid "Stock updated"
msgstr "Existencia actualizada"

#: stock/status_codes.py:62
msgid "Installed into assembly"
msgstr "Instalado en el ensamblaje"

#: stock/status_codes.py:63
msgid "Removed from assembly"
msgstr "Retirado del ensamblaje"

#: stock/status_codes.py:65
msgid "Installed component item"
msgstr "Artículo del componente instalado"

#: stock/status_codes.py:66
msgid "Removed component item"
msgstr "Elemento de componente eliminado"

#: stock/status_codes.py:69
msgid "Split from parent item"
msgstr "Separar del artículo principal"

#: stock/status_codes.py:70
msgid "Split child item"
msgstr "Dividir artículo secundario"

#: stock/status_codes.py:73
msgid "Merged stock items"
msgstr "Artículos de stock combinados"

#: stock/status_codes.py:76
msgid "Converted to variant"
msgstr "Convertir a variante"

#: stock/status_codes.py:79
msgid "Build order output created"
msgstr "Trabajo de ensamblaje creado"

#: stock/status_codes.py:80
msgid "Build order output completed"
msgstr "Construir orden de salida completado"

#: stock/status_codes.py:81
msgid "Build order output rejected"
msgstr "Orden de ensamble rechazada"

#: stock/status_codes.py:82
msgid "Consumed by build order"
msgstr "Consumido por orden de construcción"

#: stock/status_codes.py:85
msgid "Shipped against Sales Order"
msgstr "Enviado contra orden de venta"

#: stock/status_codes.py:88
msgid "Received against Purchase Order"
msgstr "Recibido contra la orden de compra"

#: stock/status_codes.py:91
msgid "Returned against Return Order"
msgstr "Devuelto contra orden de devolución"

#: stock/status_codes.py:94
msgid "Sent to customer"
msgstr "Enviar al cliente"

#: stock/status_codes.py:95
msgid "Returned from customer"
msgstr "Devolución del cliente"

#: templates/403.html:6 templates/403.html:10 templates/403_csrf.html:7
msgid "Permission Denied"
msgstr "Permiso Denegado"

#: templates/403.html:11
msgid "You do not have permission to view this page."
msgstr "No tiene permisos para ver esta página."

#: templates/403_csrf.html:11
msgid "Authentication Failure"
msgstr "Falla de autenticación"

#: templates/403_csrf.html:12
msgid "You have been logged out from InvenTree."
msgstr "Has cerrado sesión en InvenTree."

#: templates/404.html:6 templates/404.html:10
msgid "Page Not Found"
msgstr "Página No Encontrada"

#: templates/404.html:11
msgid "The requested page does not exist"
msgstr "La página solicitada no existe"

#: templates/500.html:6 templates/500.html:10
msgid "Internal Server Error"
msgstr "Error Interno Del Servidor"

#: templates/500.html:11
#, python-format
msgid "The %(inventree_title)s server raised an internal error"
msgstr ""

#: templates/500.html:12
msgid "Refer to the error log in the admin interface for further details"
msgstr "Consulte el registro de errores en la interfaz de administración para más detalles"

#: templates/503.html:11 templates/503.html:15
msgid "Site is in Maintenance"
msgstr "El Sitio está en Mantenimiento"

#: templates/503.html:17
msgid "The site is currently in maintenance and should be up again soon!"
msgstr "El sitio está actualmente en mantenimiento y debería estar listo pronto!"

#: templates/base.html:51
msgid "Server Restart Required"
msgstr "Reinicio del Servidor Requerido"

#: templates/base.html:54
msgid "A configuration option has been changed which requires a server restart"
msgstr "Se ha cambiado una opción de configuración que requiere reiniciar el servidor"

#: templates/base.html:54 templates/base.html:64
msgid "Contact your system administrator for further information"
msgstr "Póngase en contacto con su administrador para más información"

#: templates/base.html:61
msgid "Pending Database Migrations"
msgstr ""

#: templates/base.html:64
msgid "There are pending database migrations which require attention"
msgstr ""

#: templates/email/build_order_completed.html:9
#: templates/email/canceled_order_assigned.html:9
#: templates/email/new_order_assigned.html:9
#: templates/email/overdue_build_order.html:9
#: templates/email/overdue_purchase_order.html:9
#: templates/email/overdue_return_order.html:9
#: templates/email/overdue_sales_order.html:9
#: templates/email/purchase_order_received.html:9
#: templates/email/return_order_received.html:9
msgid "Click on the following link to view this order"
msgstr ""

#: templates/email/build_order_required_stock.html:7
msgid "Stock is required for the following build order"
msgstr "Se requiere stock para el siguiente orden de trabajo"

#: templates/email/build_order_required_stock.html:8
#, python-format
msgid "Build order %(build)s - building %(quantity)s x %(part)s"
msgstr "Orden de trabajo %(build)s - creando %(quantity)s x %(part)s"

#: templates/email/build_order_required_stock.html:10
msgid "Click on the following link to view this build order"
msgstr "Haga clic en el siguiente enlace para ver esta orden de trabajo"

#: templates/email/build_order_required_stock.html:14
msgid "The following parts are low on required stock"
msgstr "Las siguientes partes están bajas en stock requerido"

#: templates/email/build_order_required_stock.html:18
msgid "Required Quantity"
msgstr "Cantidad requerida"

#: templates/email/build_order_required_stock.html:38
#: templates/email/low_stock_notification.html:30
msgid "You are receiving this email because you are subscribed to notifications for this part "
msgstr "Estás recibiendo este correo electrónico porque estás suscrito a las notificaciones de esta parte "

#: templates/email/low_stock_notification.html:9
#: templates/email/part_event_notification.html:9
msgid "Click on the following link to view this part"
msgstr "Haga clic en el siguiente enlace para ver esta parte"

#: templates/email/low_stock_notification.html:18
#: templates/email/part_event_notification.html:19
msgid "Minimum Quantity"
msgstr "Cantidad Mínima"

#: templates/email/part_event_notification.html:32
msgid "You are receiving this email because you are subscribed to notifications for this part or a category that it is part of "
msgstr ""

#: users/admin.py:101
msgid "Users"
msgstr "Usuarios"

#: users/admin.py:102
msgid "Select which users are assigned to this group"
msgstr "Seleccione qué usuarios están asignados a este grupo"

#: users/admin.py:137
msgid "Personal info"
msgstr "Información personal"

#: users/admin.py:139
msgid "Permissions"
msgstr "Permisos"

#: users/admin.py:142
msgid "Important dates"
msgstr "Fechas importantes"

#: users/authentication.py:30 users/models.py:157
msgid "Token has been revoked"
msgstr "Token revocado"

#: users/authentication.py:33
msgid "Token has expired"
msgstr "Token expirado"

#: users/models.py:100
msgid "API Token"
msgstr "Token del API"

#: users/models.py:101
msgid "API Tokens"
msgstr "Tokens del API"

#: users/models.py:137
msgid "Token Name"
msgstr "Nombre del token"

#: users/models.py:138
msgid "Custom token name"
msgstr "Nombre personalizado del token"

#: users/models.py:144
msgid "Token expiry date"
msgstr "Fecha de expiración del token"

#: users/models.py:152
msgid "Last Seen"
msgstr "Última conexión"

#: users/models.py:153
msgid "Last time the token was used"
msgstr "Última vez que se usó el token"

#: users/models.py:157
msgid "Revoked"
msgstr "Revocado"

#: users/models.py:235
msgid "Permission set"
msgstr "Permiso establecido"

#: users/models.py:244
msgid "Group"
msgstr "Grupo"

#: users/models.py:248
msgid "View"
msgstr "Vista"

#: users/models.py:248
msgid "Permission to view items"
msgstr "Permiso para ver artículos"

#: users/models.py:252
msgid "Add"
msgstr "Añadir"

#: users/models.py:252
msgid "Permission to add items"
msgstr "Permiso para añadir artículos"

#: users/models.py:256
msgid "Change"
msgstr "Cambiar"

#: users/models.py:258
msgid "Permissions to edit items"
msgstr "Permisos para editar artículos"

#: users/models.py:262
msgid "Delete"
msgstr "Eliminar"

#: users/models.py:264
msgid "Permission to delete items"
msgstr "Permiso para eliminar artículos"

#: users/models.py:497
msgid "Bot"
msgstr ""

#: users/models.py:498
msgid "Internal"
msgstr ""

#: users/models.py:500
msgid "Guest"
msgstr ""

#: users/models.py:509
msgid "Language"
msgstr ""

#: users/models.py:510
msgid "Preferred language for the user"
msgstr ""

#: users/models.py:515
msgid "Theme"
msgstr ""

#: users/models.py:516
msgid "Settings for the web UI as JSON - do not edit manually!"
msgstr ""

#: users/models.py:521
msgid "Widgets"
msgstr ""

#: users/models.py:523
msgid "Settings for the dashboard widgets as JSON - do not edit manually!"
msgstr ""

#: users/models.py:530
msgid "Display Name"
msgstr ""

#: users/models.py:531
msgid "Chosen display name for the user"
msgstr ""

#: users/models.py:537
msgid "Position"
msgstr ""

#: users/models.py:538
msgid "Main job title or position"
msgstr ""

#: users/models.py:545
msgid "User status message"
msgstr ""

#: users/models.py:552
msgid "User location information"
msgstr ""

#: users/models.py:557
msgid "User is actively using the system"
msgstr ""

#: users/models.py:564
msgid "Preferred contact information for the user"
msgstr ""

#: users/models.py:570
msgid "User Type"
msgstr ""

#: users/models.py:571
msgid "Which type of user is this?"
msgstr ""

#: users/models.py:577
msgid "Organisation"
msgstr ""

#: users/models.py:578
msgid "Users primary organisation/affiliation"
msgstr ""

#: users/models.py:586
msgid "Primary Group"
msgstr ""

#: users/models.py:587
msgid "Primary group for the user"
msgstr ""

#: users/ruleset.py:31
msgid "Admin"
msgstr ""

#: users/ruleset.py:34
msgid "Stocktake"
msgstr "Verificación de Inventario"

#: users/ruleset.py:38
msgid "Purchase Orders"
msgstr "Ordenes de compra"

#: users/ruleset.py:39
msgid "Sales Orders"
msgstr "Órdenes de venta"

#: users/ruleset.py:40
msgid "Return Orders"
msgstr "Ordenes de devolución"

#: users/serializers.py:236
msgid "Username"
msgstr "Nombre de usuario"

#: users/serializers.py:239
msgid "First Name"
msgstr "Nombre"

#: users/serializers.py:239
msgid "First name of the user"
msgstr "Nombre del usuario"

#: users/serializers.py:243
msgid "Last Name"
msgstr "Apellido"

#: users/serializers.py:243
msgid "Last name of the user"
msgstr "Apellido del usuario"

#: users/serializers.py:247
msgid "Email address of the user"
msgstr "Dirección de correo del usuario"

#: users/serializers.py:323
msgid "Staff"
msgstr "Personal"

#: users/serializers.py:324
msgid "Does this user have staff permissions"
msgstr "Tiene este usuario permisos de personal"

#: users/serializers.py:329
msgid "Superuser"
msgstr "Superusuario"

#: users/serializers.py:329
msgid "Is this user a superuser"
msgstr "Es este usuario un superusuario"

#: users/serializers.py:333
msgid "Is this user account active"
msgstr "Esta cuenta de usuario está activa"

#: users/serializers.py:345
msgid "Only a superuser can adjust this field"
msgstr ""

#: users/serializers.py:399
msgid "Only staff users can create new users"
msgstr ""

#: users/serializers.py:404
msgid "You do not have permission to create users"
msgstr ""

#: users/serializers.py:425
msgid "Your account has been created."
msgstr "Su cuenta ha sido creada."

#: users/serializers.py:427
msgid "Please use the password reset function to login"
msgstr "Por favor, utilice la función de restablecer la contraseña para iniciar sesión"

#: users/serializers.py:433
msgid "Welcome to InvenTree"
msgstr "Bienvenido a InvenTree"

