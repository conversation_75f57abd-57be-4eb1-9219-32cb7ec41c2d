# Generated by Django 4.2.12 on 2024-05-23 16:40

from importlib import import_module

from django.conf import settings
from django.db import migrations


def clear_sessions(apps, schema_editor):  # pragma: no cover
    """Clear all user sessions."""

    # Ignore in test mode
    if settings.TESTING:
        return

    try:
        engine = import_module(settings.SESSION_ENGINE)
        engine.SessionStore.clear_expired()
        print('\nCleared all user sessions to deal with GHSA-2crp-q9pc-457j')
    except Exception:
        # Database may not be ready yet, so this does not matter anyhow
        pass

class Migration(migrations.Migration):

    atomic = False

    dependencies = [
        ("users", "0010_alter_apitoken_key"),
    ]

    operations = [
        migrations.RunPython(
            clear_sessions, reverse_code=migrations.RunPython.noop,
        )
    ]
