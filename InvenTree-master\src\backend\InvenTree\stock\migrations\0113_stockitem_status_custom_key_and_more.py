# Generated by Django 4.2.14 on 2024-08-07 22:40

import django.core.validators
from django.db import migrations

import generic.states
import generic.states.fields
import generic.states.validators
import InvenTree.status_codes


class Migration(migrations.Migration):

    dependencies = [
        ("stock", "0112_alter_stocklocation_custom_icon_and_more"),
    ]

    operations = [
        migrations.AddField(
            model_name="stockitem",
            name="status_custom_key",
            field=generic.states.fields.ExtraInvenTreeCustomStatusModelField(
                blank=True,
                default=None,
                help_text="Additional status information for this item",
                null=True,
                verbose_name="Custom status key",
                validators=[
                    generic.states.validators.CustomStatusCodeValidator(
                        status_class=InvenTree.status_codes.StockStatus
                    ),
                ],
            ),
        ),
        migrations.AlterField(
            model_name="stockitem",
            name="status",
            field=generic.states.fields.InvenTreeCustomStatusModelField(
                choices=InvenTree.status_codes.StockStatus.items(),
                default=10,
                validators=[
                    django.core.validators.MinValueValidator(0),
                    generic.states.validators.CustomStatusCodeValidator(
                        status_class=InvenTree.status_codes.StockStatus
                    ),
                ],
            ),
        ),
    ]
