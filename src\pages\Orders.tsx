import React, { useEffect, useState } from 'react';
import { Package, Clock, CheckCircle, XCircle, Eye } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import Header from '@/components/Header';
import CurrencyFormatter from '@/components/ui/currency-formatter';
import { useNavigate } from 'react-router-dom';

interface Order {
  id: number;
  order_reference: string;
  total_amount_ksh: number;
  delivery_cost_ksh: number;
  total_with_delivery: number;
  delivery_address: string;
  delivery_county: string;
  payment_status: string;
  payment_method: string;
  status: string;
  created_at: string;
  paid_at?: string;
  items: Array<{
    id: number;
    product_name: string;
    quantity: number;
    unit_price_ksh: number;
    total_price: number;
  }>;
}

const Orders = () => {
  const navigate = useNavigate();
  const [orders, setOrders] = useState<Order[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchOrders();
  }, []);

  const fetchOrders = async () => {
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        navigate('/login');
        return;
      }

      const response = await fetch('http://localhost:8000/api/payments/orders/', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setOrders(data.orders || []);
      } else {
        setError('Failed to fetch orders');
      }
    } catch (error) {
      console.error('Error fetching orders:', error);
      setError('Failed to fetch orders');
    } finally {
      setIsLoading(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'pending':
        return <Clock className="w-4 h-4 text-yellow-500" />;
      case 'failed':
      case 'cancelled':
        return <XCircle className="w-4 h-4 text-red-500" />;
      default:
        return <Clock className="w-4 h-4 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
      case 'paid':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'processing':
        return 'bg-blue-100 text-blue-800';
      case 'shipped':
        return 'bg-purple-100 text-purple-800';
      case 'delivered':
        return 'bg-green-100 text-green-800';
      case 'failed':
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-forest-grey-50">
        <Header />
        <div className="container mx-auto px-4 py-8">
          <div className="flex justify-center items-center min-h-[400px]">
            <div className="text-center">
              <Package className="w-16 h-16 text-forest-green-600 mx-auto mb-4 animate-pulse" />
              <p className="text-lg text-forest-grey-600">Loading your orders...</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-forest-grey-50">
        <Header />
        <div className="container mx-auto px-4 py-8">
          <div className="text-center">
            <XCircle className="w-16 h-16 text-red-500 mx-auto mb-4" />
            <h1 className="text-2xl font-bold text-forest-grey-800 mb-2">Error Loading Orders</h1>
            <p className="text-forest-grey-600 mb-4">{error}</p>
            <Button onClick={fetchOrders} className="bg-forest-green-600 hover:bg-forest-green-700">
              Try Again
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-forest-grey-50">
      <Header />
      
      <div className="container mx-auto px-4 py-8">
        <div className="mb-6">
          <h1 className="text-3xl font-bold text-forest-grey-800 mb-2">My Orders</h1>
          <p className="text-forest-grey-600">Track and manage your orders</p>
        </div>

        {orders.length === 0 ? (
          <Card>
            <CardContent className="pt-6">
              <div className="text-center py-8">
                <Package className="w-16 h-16 text-forest-grey-400 mx-auto mb-4" />
                <h2 className="text-xl font-semibold text-forest-grey-800 mb-2">No Orders Yet</h2>
                <p className="text-forest-grey-600 mb-4">You haven't placed any orders yet.</p>
                <Button 
                  onClick={() => navigate('/products')}
                  className="bg-forest-green-600 hover:bg-forest-green-700"
                >
                  Start Shopping
                </Button>
              </div>
            </CardContent>
          </Card>
        ) : (
          <div className="space-y-6">
            {orders.map((order) => (
              <Card key={order.id}>
                <CardHeader>
                  <div className="flex justify-between items-start">
                    <div>
                      <CardTitle className="text-forest-grey-800">
                        Order #{order.order_reference}
                      </CardTitle>
                      <p className="text-sm text-forest-grey-600 mt-1">
                        Placed on {new Date(order.created_at).toLocaleDateString()}
                      </p>
                    </div>
                    <div className="flex flex-col items-end space-y-2">
                      <Badge className={getStatusColor(order.payment_status)}>
                        <div className="flex items-center space-x-1">
                          {getStatusIcon(order.payment_status)}
                          <span className="capitalize">{order.payment_status}</span>
                        </div>
                      </Badge>
                      <Badge variant="outline" className="capitalize">
                        {order.status}
                      </Badge>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {/* Order Items */}
                    <div>
                      <h4 className="font-medium text-forest-grey-800 mb-2">Items</h4>
                      <div className="space-y-2">
                        {order.items.map((item) => (
                          <div key={item.id} className="flex justify-between items-center">
                            <div className="flex-1">
                              <p className="font-medium text-forest-grey-800">{item.product_name}</p>
                              <p className="text-sm text-forest-grey-600">Qty: {item.quantity}</p>
                            </div>
                            <p className="font-medium text-forest-green-600">
                              <CurrencyFormatter amount={item.total_price} />
                            </p>
                          </div>
                        ))}
                      </div>
                    </div>

                    <Separator />

                    {/* Order Summary */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <h4 className="font-medium text-forest-grey-800 mb-2">Delivery Information</h4>
                        <p className="text-sm text-forest-grey-600">
                          <strong>County:</strong> {order.delivery_county}
                        </p>
                        <p className="text-sm text-forest-grey-600">
                          <strong>Address:</strong> {order.delivery_address}
                        </p>
                        {order.payment_method && (
                          <p className="text-sm text-forest-grey-600">
                            <strong>Payment Method:</strong> {order.payment_method.toUpperCase()}
                          </p>
                        )}
                      </div>
                      
                      <div>
                        <h4 className="font-medium text-forest-grey-800 mb-2">Order Total</h4>
                        <div className="space-y-1 text-sm">
                          <div className="flex justify-between">
                            <span className="text-forest-grey-600">Subtotal:</span>
                            <span><CurrencyFormatter amount={order.total_amount_ksh} /></span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-forest-grey-600">Delivery:</span>
                            <span><CurrencyFormatter amount={order.delivery_cost_ksh} /></span>
                          </div>
                          <Separator />
                          <div className="flex justify-between font-medium">
                            <span>Total:</span>
                            <span className="text-forest-green-600">
                              <CurrencyFormatter amount={order.total_with_delivery} />
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>

                    <Separator />

                    {/* Actions */}
                    <div className="flex justify-between items-center">
                      <div className="text-sm text-forest-grey-600">
                        {order.paid_at && (
                          <span>Paid on {new Date(order.paid_at).toLocaleDateString()}</span>
                        )}
                      </div>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => navigate(`/orders/${order.id}`)}
                        className="flex items-center space-x-1"
                      >
                        <Eye className="w-4 h-4" />
                        <span>View Details</span>
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default Orders;
