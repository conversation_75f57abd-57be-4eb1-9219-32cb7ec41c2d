# InvenTree environment variables for a development setup
# These variables will be used by the docker-compose.yml file

INVENTREE_SITE_URL=http://localhost:8000

# Set DEBUG to True for a development setup
INVENTREE_DEBUG=True
INVENTREE_LOG_LEVEL=WARNING
INVENTREE_DB_LOGGING=False

# Database configuration options
# Note: The example setup is for a PostgreSQL database (change as required)
INVENTREE_DB_ENGINE=postgresql
INVENTREE_DB_NAME=inventree
INVENTREE_DB_HOST=inventree-dev-db
INVENTREE_DB_PORT=5432
INVENTREE_DB_USER=pguser
INVENTREE_DB_PASSWORD=pgpassword

# Enable custom plugins?
INVENTREE_PLUGINS_ENABLED=True

# Auto run migrations?
INVENTREE_AUTO_UPDATE=True
