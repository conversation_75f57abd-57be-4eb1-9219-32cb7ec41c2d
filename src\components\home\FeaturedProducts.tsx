
import React from 'react';
import { Link } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import ProductCard from './ProductCard';
import { featuredProducts } from '@/data/products';

const FeaturedProducts = () => {

  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="flex justify-between items-center mb-12">
          <div>
            <h2 className="text-4xl font-bold text-forest-grey-800 mb-4">Featured Products</h2>
            <p className="text-lg text-forest-grey-600">Top picks from our extensive inventory</p>
          </div>
          <Link to="/products">
            <Button variant="outline" className="border-forest-green-500 text-forest-green-600 hover:bg-forest-green-50">
              View All Products
            </Button>
          </Link>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {featuredProducts.map((product) => (
            <ProductCard key={product.id} product={product} />
          ))}
        </div>
      </div>
    </section>
  );
};

export default FeaturedProducts;
