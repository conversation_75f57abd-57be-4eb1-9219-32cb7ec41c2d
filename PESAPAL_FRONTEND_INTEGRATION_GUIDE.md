# Pesapal Payment Integration - Frontend Guide

## Overview

This guide provides complete instructions for integrating the Pesapal payment system with your React frontend. The backend API endpoints are already implemented and ready to use.

## Backend API Endpoints

### 1. Payment Initiation
**Endpoint:** `POST /api/payments/initiate/`
**Authentication:** Required (JWT <PERSON>)

**Request Body:**
```json
{
  "delivery_address": "123 Main Street, Nairobi",
  "delivery_county": "nairobi",
  "payment_method": "mpesa",
  "phone": "************"
}
```

**Response:**
```json
{
  "success": true,
  "order": {
    "id": 1,
    "order_reference": "DF00000112345",
    "total_amount_ksh": "1000.00",
    "delivery_cost_ksh": "200.00",
    "total_with_delivery": "1200.00",
    "status": "pending",
    "payment_status": "pending"
  },
  "payment_url": "http://demo.pesapal.com/api/PostPesapalDirectOrderV4?...",
  "message": "Payment initiated successfully"
}
```

### 2. Payment Status Check
**Endpoint:** `GET /api/payments/status/<order_reference>/`
**Authentication:** Required (JWT Token)

**Response:**
```json
{
  "success": true,
  "order": {
    "id": 1,
    "order_reference": "DF00000112345",
    "payment_status": "completed",
    "status": "paid"
  },
  "payment_status": "completed"
}
```

### 3. Checkout Summary
**Endpoint:** `GET /api/payments/checkout/summary/?delivery_county=nairobi`
**Authentication:** Required (JWT Token)

**Response:**
```json
{
  "success": true,
  "summary": {
    "cart_items": [
      {
        "product_name": "Hammer",
        "quantity": 2,
        "unit_price_ksh": "500.00",
        "total_price_ksh": "1000.00"
      }
    ],
    "subtotal_ksh": "1000.00",
    "delivery_cost_ksh": "200.00",
    "total_ksh": "1200.00"
  }
}
```

## React Frontend Implementation

### 1. Checkout Page Component

```jsx
// src/pages/CheckoutPage.jsx
import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';

const CheckoutPage = () => {
  const [checkoutData, setCheckoutData] = useState(null);
  const [formData, setFormData] = useState({
    delivery_address: '',
    delivery_county: 'nairobi',
    payment_method: 'mpesa',
    phone: ''
  });
  const [loading, setLoading] = useState(false);
  const { token } = useAuth();
  const navigate = useNavigate();

  // County options for Kenya
  const counties = [
    { value: 'nairobi', label: 'Nairobi' },
    { value: 'mombasa', label: 'Mombasa' },
    { value: 'kisumu', label: 'Kisumu' },
    { value: 'nakuru', label: 'Nakuru' },
    { value: 'eldoret', label: 'Eldoret' }
  ];

  // Payment method options
  const paymentMethods = [
    { value: 'mpesa', label: 'M-Pesa' },
    { value: 'visa', label: 'Visa Card' },
    { value: 'mastercard', label: 'Mastercard' },
    { value: 'airtel', label: 'Airtel Money' }
  ];

  // Fetch checkout summary
  useEffect(() => {
    fetchCheckoutSummary();
  }, [formData.delivery_county]);

  const fetchCheckoutSummary = async () => {
    try {
      const response = await fetch(
        `/api/payments/checkout/summary/?delivery_county=${formData.delivery_county}`,
        {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        }
      );
      const data = await response.json();
      if (data.success) {
        setCheckoutData(data.summary);
      }
    } catch (error) {
      console.error('Error fetching checkout summary:', error);
    }
  };

  const handleInputChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);

    try {
      const response = await fetch('/api/payments/initiate/', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(formData)
      });

      const data = await response.json();
      
      if (data.success) {
        // Redirect to payment confirmation page with payment URL
        navigate(`/checkout/confirmation/${data.order.order_reference}`, {
          state: { paymentUrl: data.payment_url }
        });
      } else {
        alert('Payment initiation failed: ' + (data.error || 'Unknown error'));
      }
    } catch (error) {
      console.error('Error initiating payment:', error);
      alert('Payment initiation failed');
    } finally {
      setLoading(false);
    }
  };

  if (!checkoutData) {
    return <div>Loading checkout summary...</div>;
  }

  return (
    <div className="checkout-page">
      <h1>Checkout</h1>
      
      {/* Order Summary */}
      <div className="order-summary">
        <h2>Order Summary</h2>
        {checkoutData.cart_items.map((item, index) => (
          <div key={index} className="cart-item">
            <span>{item.product_name}</span>
            <span>Qty: {item.quantity}</span>
            <span>KSH {item.total_price_ksh}</span>
          </div>
        ))}
        <div className="totals">
          <div>Subtotal: KSH {checkoutData.subtotal_ksh}</div>
          <div>Delivery: KSH {checkoutData.delivery_cost_ksh}</div>
          <div className="total">Total: KSH {checkoutData.total_ksh}</div>
        </div>
      </div>

      {/* Checkout Form */}
      <form onSubmit={handleSubmit} className="checkout-form">
        <div className="form-group">
          <label>Delivery County:</label>
          <select
            name="delivery_county"
            value={formData.delivery_county}
            onChange={handleInputChange}
            required
          >
            {counties.map(county => (
              <option key={county.value} value={county.value}>
                {county.label}
              </option>
            ))}
          </select>
        </div>

        <div className="form-group">
          <label>Delivery Address:</label>
          <textarea
            name="delivery_address"
            value={formData.delivery_address}
            onChange={handleInputChange}
            placeholder="Enter your full delivery address"
            required
          />
        </div>

        <div className="form-group">
          <label>Payment Method:</label>
          <select
            name="payment_method"
            value={formData.payment_method}
            onChange={handleInputChange}
            required
          >
            {paymentMethods.map(method => (
              <option key={method.value} value={method.value}>
                {method.label}
              </option>
            ))}
          </select>
        </div>

        <div className="form-group">
          <label>Phone Number (Optional):</label>
          <input
            type="tel"
            name="phone"
            value={formData.phone}
            onChange={handleInputChange}
            placeholder="************"
          />
        </div>

        <button type="submit" disabled={loading} className="pay-button">
          {loading ? 'Processing...' : `Pay KSH ${checkoutData.total_ksh}`}
        </button>
      </form>
    </div>
  );
};

export default CheckoutPage;
```

### 2. Payment Confirmation Page

```jsx
// src/pages/PaymentConfirmationPage.jsx
import React, { useState, useEffect } from 'react';
import { useParams, useLocation, useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';

const PaymentConfirmationPage = () => {
  const { orderReference } = useParams();
  const location = useLocation();
  const navigate = useNavigate();
  const { token } = useAuth();
  
  const [paymentStatus, setPaymentStatus] = useState('pending');
  const [orderData, setOrderData] = useState(null);
  const [loading, setLoading] = useState(true);
  const paymentUrl = location.state?.paymentUrl;

  useEffect(() => {
    // Start polling for payment status
    const pollInterval = setInterval(checkPaymentStatus, 3000);
    
    // Initial check
    checkPaymentStatus();

    // Cleanup interval on unmount
    return () => clearInterval(pollInterval);
  }, [orderReference]);

  const checkPaymentStatus = async () => {
    try {
      const response = await fetch(`/api/payments/status/${orderReference}/`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });
      
      const data = await response.json();
      
      if (data.success) {
        setOrderData(data.order);
        setPaymentStatus(data.payment_status);
        setLoading(false);
        
        // Stop polling if payment is completed or failed
        if (data.payment_status !== 'pending') {
          // Clear the interval by returning true
          return true;
        }
      }
    } catch (error) {
      console.error('Error checking payment status:', error);
      setLoading(false);
    }
  };

  const handleReturnToShop = () => {
    navigate('/products');
  };

  const handleViewOrder = () => {
    navigate(`/orders/${orderData.id}`);
  };

  if (loading) {
    return (
      <div className="payment-confirmation">
        <h1>Processing Payment...</h1>
        <div className="loading-spinner">⏳</div>
        <p>Please wait while we confirm your payment.</p>
      </div>
    );
  }

  return (
    <div className="payment-confirmation">
      <div className="payment-iframe-container">
        {paymentUrl && paymentStatus === 'pending' && (
          <iframe
            src={paymentUrl}
            width="100%"
            height="600"
            frameBorder="0"
            title="Pesapal Payment"
          />
        )}
      </div>

      <div className="payment-status">
        {paymentStatus === 'completed' && (
          <div className="success">
            <h1>✅ Payment Successful!</h1>
            <p>Your order has been confirmed and will be processed shortly.</p>
            <div className="order-details">
              <h3>Order Details:</h3>
              <p>Order Reference: {orderData.order_reference}</p>
              <p>Total Amount: KSH {orderData.total_with_delivery}</p>
              <p>Status: {orderData.status}</p>
            </div>
            <div className="actions">
              <button onClick={handleViewOrder} className="btn-primary">
                View Order Details
              </button>
              <button onClick={handleReturnToShop} className="btn-secondary">
                Continue Shopping
              </button>
            </div>
          </div>
        )}

        {paymentStatus === 'failed' && (
          <div className="error">
            <h1>❌ Payment Failed</h1>
            <p>Your payment could not be processed. Please try again.</p>
            <button onClick={() => navigate('/checkout')} className="btn-primary">
              Try Again
            </button>
          </div>
        )}

        {paymentStatus === 'pending' && (
          <div className="pending">
            <h1>⏳ Payment Pending</h1>
            <p>Complete your payment in the form above.</p>
            <p>This page will automatically update when payment is confirmed.</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default PaymentConfirmationPage;
```

## CSS Styling

```css
/* src/styles/checkout.css */
.checkout-page {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.order-summary {
  background: #f5f5f5;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 30px;
}

.cart-item {
  display: flex;
  justify-content: space-between;
  padding: 10px 0;
  border-bottom: 1px solid #ddd;
}

.totals {
  margin-top: 15px;
  padding-top: 15px;
  border-top: 2px solid #ddd;
}

.total {
  font-weight: bold;
  font-size: 1.2em;
  color: #2c5530;
}

.checkout-form {
  background: white;
  padding: 30px;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 16px;
}

.pay-button {
  width: 100%;
  padding: 15px;
  background: #2c5530;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 18px;
  font-weight: bold;
  cursor: pointer;
}

.pay-button:hover {
  background: #1e3a21;
}

.pay-button:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.payment-confirmation {
  max-width: 1000px;
  margin: 0 auto;
  padding: 20px;
}

.payment-iframe-container {
  margin-bottom: 30px;
  border: 1px solid #ddd;
  border-radius: 8px;
  overflow: hidden;
}

.payment-status {
  text-align: center;
  padding: 30px;
}

.success {
  color: #2c5530;
}

.error {
  color: #d32f2f;
}

.pending {
  color: #f57c00;
}

.order-details {
  background: #f5f5f5;
  padding: 20px;
  border-radius: 8px;
  margin: 20px 0;
  text-align: left;
}

.actions {
  margin-top: 30px;
}

.btn-primary,
.btn-secondary {
  padding: 12px 24px;
  margin: 0 10px;
  border: none;
  border-radius: 4px;
  font-size: 16px;
  cursor: pointer;
}

.btn-primary {
  background: #2c5530;
  color: white;
}

.btn-secondary {
  background: #f5f5f5;
  color: #333;
  border: 1px solid #ddd;
}

.loading-spinner {
  font-size: 48px;
  margin: 20px 0;
}
```

## Router Configuration

```jsx
// Add these routes to your React Router configuration
import CheckoutPage from './pages/CheckoutPage';
import PaymentConfirmationPage from './pages/PaymentConfirmationPage';

// In your router setup:
<Route path="/checkout" element={<CheckoutPage />} />
<Route path="/checkout/confirmation/:orderReference" element={<PaymentConfirmationPage />} />
```

## Environment Variables

Make sure your React app can communicate with the Django backend. Update your `.env` file:

```env
REACT_APP_API_BASE_URL=http://localhost:8000
```

## Next Steps

1. **Test the Integration**: Use the test credentials to verify the payment flow
2. **Production Setup**: Replace sandbox URLs and credentials with production values
3. **Error Handling**: Add comprehensive error handling for network issues
4. **Loading States**: Implement proper loading indicators
5. **Mobile Optimization**: Ensure the payment iframe works well on mobile devices

## Security Notes

- Always validate payment status on the backend
- Never trust frontend-only payment confirmations
- Use HTTPS in production
- Implement proper CORS settings
- Validate all user inputs on the backend
