
import React from 'react';
import { Link } from 'react-router-dom';
import { ArrowDown, Truck, ShoppingCart } from 'lucide-react';
import { Button } from '@/components/ui/button';

const HeroSection = () => {
  return (
    <section className="relative bg-gradient-to-r from-forest-steel-700 to-forest-rust-600 text-white py-20">
      <div className="container mx-auto px-4">
        <div className="max-w-3xl">
          <h1 className="text-5xl md:text-6xl font-bold mb-6 animate-fade-in">
            Your Premier Destination for Building Materials
          </h1>
          <p className="text-xl mb-8 text-forest-steel-100">
            From doors and windows to cement and paint, we provide quality materials for all your construction and renovation projects.
          </p>
          <div className="flex flex-col sm:flex-row gap-4">
            <Link to="/products">
              <Button size="lg" className="hardware-btn px-8 py-6 text-lg bg-forest-rust-500 hover:bg-forest-rust-600 text-white">
                <ShoppingCart className="w-5 h-5" />
                <span>Shop Now</span>
              </Button>
            </Link>
            <Link to="/delivery">
              <Button size="lg" variant="outline" className="hardware-btn text-white border-white hover:bg-white hover:text-forest-rust-600 px-8 py-6 text-lg">
                <Truck className="w-5 h-5" />
                <span>Free Delivery Available</span>
              </Button>
            </Link>
          </div>
        </div>
      </div>
      <div className="absolute bottom-6 left-1/2 transform -translate-x-1/2 animate-bounce">
        <ArrowDown className="w-6 h-6" />
      </div>
    </section>
  );
};

export default HeroSection;
