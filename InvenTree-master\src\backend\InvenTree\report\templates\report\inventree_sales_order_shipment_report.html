{% extends "report/inventree_order_report_base.html" %}

{% load i18n %}
{% load report %}
{% load barcode %}
{% load inventree_extras %}
{% load markdownify %}

{% block header_content %}

    <img class='logo' src='{% company_image order.customer %}' alt="{{ order.customer }}" width='150'>

    <div class='header-right'>
        <h3>{% trans "Shipment" %} {{ prefix }}{{ reference }}</h3>
        <i>{% trans "Sales Order" %} {{ order.reference }}</i><br/>
        {{ order.customer.name }}
    </div>

{% endblock header_content %}

{% block page_content %}

<h3>{% trans "Allocations" %}</h3>

<table class='table table-striped table-condensed'>
    <thead>
        <tr>
            <th>{% trans "Part" %}</th>
            <th>{% trans "Stock Item" %}</th>
        </tr>
    </thead>
    <tbody>
        {% for allocation in allocations.all %}
        <tr>
            <td>
                <div class='thumb-container'>
                    <img src='{% part_image allocation.line.part height=240 %}' alt='{% trans "Part image" %}' class='part-thumb'>
                </div>
                <div class='part-text'>
                    {{ allocation.line.part.full_name }}
                </div>
            </td>

            {% if allocation.item and allocation.item.serial and allocation.quantity == 1 %}
                <td>{% trans "Serial Number" %}: {{ allocation.item.serial }}</td>
            {% elif allocation.item and allocation.item.batch %}
                <td>{% trans "Quantity" %}: {% decimal allocation.quantity %} - <i>{% trans "Batch" %}: {{ allocation.item.batch }}</i></td>
            {% else %}
                <td>{% trans "Quantity" %}: {% decimal allocation.quantity %}</td>
            {% endif %}
        </tr>
        {% endfor %}
    </tbody>
</table>

{% endblock page_content %}
