# Generated by Django 4.2.12 on 2024-06-08 12:37

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import taggit.managers

import common.models
import common.validators
import InvenTree.fields
import InvenTree.models


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('contenttypes', '0002_remove_content_type_name'),
        ('common', '0024_notesimage_model_id_notesimage_model_type'),
    ]

    operations = [
        migrations.CreateModel(
            name='Attachment',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('model_id', models.PositiveIntegerField()),
                ('attachment', models.FileField(blank=True, help_text='Select file to attach', null=True, upload_to=common.models.rename_attachment, verbose_name='Attachment')),
                ('link', InvenTree.fields.InvenTreeURLField(blank=True, help_text='Link to external URL', null=True, verbose_name='Link')),
                ('comment', models.CharField(blank=True, help_text='Attachment comment', max_length=250, verbose_name='Comment')),
                ('upload_date', models.DateField(auto_now_add=True, help_text='Date the file was uploaded', null=True, verbose_name='Upload date')),
                ('file_size', models.PositiveIntegerField(default=0, help_text='File size in bytes', verbose_name='File size')),
                ('model_type', models.CharField(help_text='Target model type for this image', max_length=100, validators=[common.validators.validate_attachment_model_type])),
                ('upload_user', models.ForeignKey(blank=True, help_text='User', null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='User')),
                ('metadata', models.JSONField(blank=True, help_text='JSON metadata field, for use by external plugins', null=True, verbose_name='Plugin Metadata')),
                ('tags', taggit.managers.TaggableManager(blank=True, help_text='A comma-separated list of tags.', through='taggit.TaggedItem', to='taggit.Tag', verbose_name='Tags'))
            ],
            bases=(InvenTree.models.PluginValidationMixin, models.Model),
            options={
                'verbose_name': 'Attachment',
            }
        ),
    ]
