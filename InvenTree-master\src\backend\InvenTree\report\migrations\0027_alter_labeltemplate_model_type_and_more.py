# Generated by Django 4.2.11 on 2024-04-30 09:50

from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
import report.models
import report.validators


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('report', '0026_auto_20240422_1301'),
    ]

    operations = [
        migrations.AlterField(
            model_name='labeltemplate',
            name='model_type',
            field=models.CharField(help_text='Target model type for template', max_length=100, validators=[report.validators.validate_report_model_type]),
        ),
        migrations.AlterField(
            model_name='labeltemplate',
            name='template',
            field=models.FileField(help_text='Template file', upload_to=report.models.rename_template, validators=[django.core.validators.FileExtensionValidator(allowed_extensions=['html', 'htm'])], verbose_name='Template'),
        ),
        migrations.<PERSON><PERSON><PERSON><PERSON>(
            model_name='reportasset',
            name='asset',
            field=models.FileField(help_text='Report asset file', upload_to=report.models.rename_template, verbose_name='Asset'),
        ),
        migrations.AlterField(
            model_name='reportsnippet',
            name='snippet',
            field=models.FileField(help_text='Report snippet file', upload_to=report.models.rename_template, validators=[django.core.validators.FileExtensionValidator(allowed_extensions=['html', 'htm'])], verbose_name='Snippet'),
        ),
        migrations.AlterField(
            model_name='reporttemplate',
            name='template',
            field=models.FileField(help_text='Template file', upload_to=report.models.rename_template, validators=[django.core.validators.FileExtensionValidator(allowed_extensions=['html', 'htm'])], verbose_name='Template'),
        ),
        migrations.CreateModel(
            name='ReportOutput',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', models.DateField(auto_now_add=True)),
                ('items', models.PositiveIntegerField(default=0, help_text='Number of items to process', verbose_name='Items')),
                ('complete', models.BooleanField(default=False, help_text='Report generation is complete', verbose_name='Complete')),
                ('progress', models.PositiveIntegerField(default=0, help_text='Report generation progress', verbose_name='Progress')),
                ('output', models.FileField(blank=True, help_text='Generated output file', null=True, upload_to='report/output', verbose_name='Output File')),
                ('template', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='report.reporttemplate', verbose_name='Report Template')),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='LabelOutput',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', models.DateField(auto_now_add=True)),
                ('items', models.PositiveIntegerField(default=0, help_text='Number of items to process', verbose_name='Items')),
                ('complete', models.BooleanField(default=False, help_text='Report generation is complete', verbose_name='Complete')),
                ('progress', models.PositiveIntegerField(default=0, help_text='Report generation progress', verbose_name='Progress')),
                ('output', models.FileField(blank=True, help_text='Generated output file', null=True, upload_to='label/output', verbose_name='Output File')),
                ('plugin', models.CharField(blank=True, help_text='Label output plugin', max_length=100, verbose_name='Plugin')),
                ('template', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='report.labeltemplate', verbose_name='Label Template')),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
    ]
