#!/usr/bin/env python
"""
Complete Payment Flow Test
Tests the entire payment process from cart to completion
"""
import os
import sys
import django
from decimal import Decimal
import json

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'deepforest_backend.settings')
django.setup()

from django.contrib.auth.models import User
from django.test import Client
from django.urls import reverse
from rest_framework.test import APIClient
from rest_framework_simplejwt.tokens import RefreshToken

from payments.models import Order, OrderItem, Payment, PaymentLog
from payments.pesapal_webhooks import PesapalWebhookHandler
from ecommerce.models import Product, Category, Cart, CartItem, DeliveryZone


def setup_test_data():
    """Setup test data for complete flow testing"""
    print("Setting up test data...")

    # Create test user
    import time
    username = f'flowtest{int(time.time())}'
    user = User.objects.create_user(
        username=username,
        email=f'{username}@example.com',
        password='testpass123',
        first_name='Flow',
        last_name='Test'
    )
    
    # Create test category and products
    category, _ = Category.objects.get_or_create(
        name='Test Hardware',
        defaults={'description': 'Test hardware category'}
    )

    product1, _ = Product.objects.get_or_create(
        sku='HAMMER001',
        defaults={
            'name': 'Test Hammer',
            'description': 'Professional hammer for testing',
            'price_ksh': Decimal('500.00'),
            'category': category,
            'stock_quantity': 10
        }
    )

    product2, _ = Product.objects.get_or_create(
        sku='SCREW001',
        defaults={
            'name': 'Test Screwdriver',
            'description': 'Quality screwdriver for testing',
            'price_ksh': Decimal('300.00'),
            'category': category,
            'stock_quantity': 15
        }
    )

    # Create delivery zone
    delivery_zone, _ = DeliveryZone.objects.get_or_create(
        county='nairobi',
        defaults={
            'delivery_cost_ksh': Decimal('200.00'),
            'estimated_days': 2
        }
    )
    
    # Create cart with items
    cart = Cart.objects.create(user=user)
    CartItem.objects.create(cart=cart, product=product1, quantity=2)
    CartItem.objects.create(cart=cart, product=product2, quantity=1)
    
    return user, [product1, product2], delivery_zone


def get_auth_token(user):
    """Get JWT token for user"""
    refresh = RefreshToken.for_user(user)
    return str(refresh.access_token)


def test_checkout_summary():
    """Test checkout summary endpoint"""
    print("\n=== Testing Checkout Summary ===")
    
    user, products, delivery_zone = setup_test_data()
    client = APIClient()
    token = get_auth_token(user)
    client.credentials(HTTP_AUTHORIZATION=f'Bearer {token}')
    
    # Test checkout summary
    response = client.get('/api/payments/checkout/summary/?delivery_county=nairobi')
    
    if response.status_code == 200:
        data = response.json()
        if data['success']:
            summary = data['summary']
            print(f"✓ Checkout summary retrieved successfully")
            print(f"  Cart items: {len(summary['cart_items'])}")
            print(f"  Subtotal: KSH {summary['subtotal_ksh']}")
            print(f"  Delivery: KSH {summary['delivery_cost_ksh']}")
            print(f"  Total: KSH {summary['total_ksh']}")
            return summary
        else:
            print(f"✗ Checkout summary failed: {data}")
    else:
        print(f"✗ Checkout summary failed with status {response.status_code}")
    
    return None


def test_payment_initiation():
    """Test payment initiation endpoint"""
    print("\n=== Testing Payment Initiation ===")
    
    user, products, delivery_zone = setup_test_data()
    client = APIClient()
    token = get_auth_token(user)
    client.credentials(HTTP_AUTHORIZATION=f'Bearer {token}')
    
    # Payment initiation data
    payment_data = {
        'delivery_address': '123 Test Street, Nairobi, Kenya',
        'delivery_county': 'nairobi',
        'payment_method': 'mpesa',
        'phone': '254700000000'
    }
    
    response = client.post('/api/payments/initiate/', payment_data, format='json')
    
    if response.status_code == 201:
        data = response.json()
        if data['success']:
            order = data['order']
            print(f"✓ Payment initiated successfully")
            print(f"  Order Reference: {order['order_reference']}")
            print(f"  Total Amount: KSH {order['total_with_delivery']}")
            print(f"  Payment URL: {data['payment_url'][:100]}...")
            return order, data['payment_url']
        else:
            print(f"✗ Payment initiation failed: {data}")
    else:
        print(f"✗ Payment initiation failed with status {response.status_code}")
        print(f"  Response: {response.json()}")
    
    return None, None


def test_payment_status_check():
    """Test payment status checking"""
    print("\n=== Testing Payment Status Check ===")
    
    # First create an order
    user, products, delivery_zone = setup_test_data()
    client = APIClient()
    token = get_auth_token(user)
    client.credentials(HTTP_AUTHORIZATION=f'Bearer {token}')
    
    # Create order via payment initiation
    payment_data = {
        'delivery_address': '123 Test Street, Nairobi, Kenya',
        'delivery_county': 'nairobi',
        'payment_method': 'mpesa',
        'phone': '254700000000'
    }
    
    init_response = client.post('/api/payments/initiate/', payment_data, format='json')
    
    if init_response.status_code == 201:
        order_data = init_response.json()['order']
        order_reference = order_data['order_reference']
        
        # Check payment status
        status_response = client.get(f'/api/payments/status/{order_reference}/')
        
        if status_response.status_code == 200:
            data = status_response.json()
            if data['success']:
                print(f"✓ Payment status check successful")
                print(f"  Order Reference: {order_reference}")
                print(f"  Payment Status: {data['payment_status']}")
                print(f"  Order Status: {data['order']['status']}")
                return data
            else:
                print(f"✗ Payment status check failed: {data}")
        else:
            print(f"✗ Payment status check failed with status {status_response.status_code}")
    
    return None


def test_webhook_simulation():
    """Test webhook processing with simulated callback"""
    print("\n=== Testing Webhook Simulation ===")
    
    # Create an order first
    user, products, delivery_zone = setup_test_data()
    
    # Create order manually for webhook testing
    order = Order.objects.create(
        user=user,
        order_reference='WEBHOOK_TEST_001',
        total_amount_ksh=Decimal('1300.00'),
        delivery_cost_ksh=Decimal('200.00'),
        delivery_address='123 Webhook Test Street',
        delivery_county='nairobi'
    )
    
    # Create order items
    OrderItem.objects.create(
        order=order,
        product=products[0],
        quantity=2,
        unit_price_ksh=products[0].price_ksh
    )
    OrderItem.objects.create(
        order=order,
        product=products[1],
        quantity=1,
        unit_price_ksh=products[1].price_ksh
    )
    
    print(f"Created test order: {order.order_reference}")
    
    # Simulate webhook callback
    webhook_handler = PesapalWebhookHandler()
    
    # Test successful payment callback
    callback_data = {
        'pesapal_notification_type': 'CHANGE',
        'pesapal_transaction_tracking_id': 'TEST_TRACKING_123',
        'pesapal_merchant_reference': order.order_reference
    }
    
    # Mock the status query to return completed
    original_query_method = webhook_handler.client.query_payment_status
    
    def mock_query_status(merchant_ref, tracking_id):
        return {'status': 'COMPLETED'}
    
    webhook_handler.client.query_payment_status = mock_query_status
    
    try:
        result = webhook_handler.process_ipn_callback(callback_data)
        
        if result:
            # Refresh order from database
            order.refresh_from_db()
            print(f"✓ Webhook processing successful")
            print(f"  Order Status: {order.status}")
            print(f"  Payment Status: {order.payment_status}")
            print(f"  Paid At: {order.paid_at}")
            
            # Check if payment record was created
            payment = Payment.objects.filter(order=order).first()
            if payment:
                print(f"  Payment Record: {payment.status}")
            
            return True
        else:
            print(f"✗ Webhook processing failed")
    
    except Exception as e:
        print(f"✗ Webhook processing error: {str(e)}")
    
    finally:
        # Restore original method
        webhook_handler.client.query_payment_status = original_query_method
    
    return False


def test_order_retrieval():
    """Test order retrieval endpoints"""
    print("\n=== Testing Order Retrieval ===")
    
    user, products, delivery_zone = setup_test_data()
    client = APIClient()
    token = get_auth_token(user)
    client.credentials(HTTP_AUTHORIZATION=f'Bearer {token}')
    
    # Create an order first
    payment_data = {
        'delivery_address': '123 Test Street, Nairobi, Kenya',
        'delivery_county': 'nairobi',
        'payment_method': 'mpesa',
        'phone': '254700000000'
    }
    
    init_response = client.post('/api/payments/initiate/', payment_data, format='json')
    
    if init_response.status_code == 201:
        order_data = init_response.json()['order']
        order_id = order_data['id']
        
        # Test user orders list
        orders_response = client.get('/api/payments/orders/')
        if orders_response.status_code == 200:
            orders_data = orders_response.json()
            if orders_data['success']:
                print(f"✓ User orders retrieved: {len(orders_data['orders'])} orders")
            else:
                print(f"✗ User orders retrieval failed: {orders_data}")
        
        # Test order detail
        detail_response = client.get(f'/api/payments/orders/{order_id}/')
        if detail_response.status_code == 200:
            detail_data = detail_response.json()
            if detail_data['success']:
                print(f"✓ Order detail retrieved")
                print(f"  Order ID: {order_id}")
                print(f"  Payment Logs: {len(detail_data['payment_logs'])}")
                return True
            else:
                print(f"✗ Order detail retrieval failed: {detail_data}")
        
    return False


def main():
    """Run complete payment flow tests"""
    print("🚀 Starting Complete Payment Flow Tests")
    print("=" * 60)
    
    try:
        # Test 1: Checkout Summary
        summary = test_checkout_summary()
        
        # Test 2: Payment Initiation
        order, payment_url = test_payment_initiation()
        
        # Test 3: Payment Status Check
        status_data = test_payment_status_check()
        
        # Test 4: Webhook Simulation
        webhook_success = test_webhook_simulation()
        
        # Test 5: Order Retrieval
        retrieval_success = test_order_retrieval()
        
        print("\n" + "=" * 60)
        print("✅ Complete Payment Flow Tests Finished")
        
        # Summary
        print(f"\n📊 Test Results Summary:")
        print(f"   Checkout Summary: {'✓' if summary else '✗'}")
        print(f"   Payment Initiation: {'✓' if order else '✗'}")
        print(f"   Status Check: {'✓' if status_data else '✗'}")
        print(f"   Webhook Processing: {'✓' if webhook_success else '✗'}")
        print(f"   Order Retrieval: {'✓' if retrieval_success else '✗'}")
        
        if order and payment_url:
            print(f"\n🔗 Sample Payment URL for Manual Testing:")
            print(f"   {payment_url}")
        
        print(f"\n💡 Next Steps:")
        print(f"   1. Configure real Pesapal credentials in .env")
        print(f"   2. Test with actual payment methods")
        print(f"   3. Set up production webhook endpoints")
        print(f"   4. Implement frontend integration")
        
    except Exception as e:
        print(f"\n❌ Test execution failed: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()
