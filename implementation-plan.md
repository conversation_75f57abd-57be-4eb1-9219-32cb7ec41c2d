# DeepForest Hardware Emporium - Comprehensive Implementation Plan

## Project Overview
**Objective:** Fully integrate InvenTree inventory management system into the existing DeepForest Hardware Emporium e-commerce website, creating a unified platform with seamless CRUD operations, shared database, and unified admin dashboard.

**Current Status:** ~60% complete with React/TypeScript frontend and basic e-commerce functionality

**Technology Stack:**
- **Frontend:** React, TypeScript, shadcn/ui, TanStack Query, React Router
- **Backend:** Django, Django Rest Framework, PostgreSQL
- **Integration:** InvenTree Django apps embedded within main backend
- **Deployment:** Single unified Django application

## Three-Phase Development Approach

### **Phase 1: Frontend Stabilization & Critical UI/UX Fixes**

**Duration:** 1-2 weeks  
**Persona:** Frontend Developer specializing in React, TypeScript, and shadcn/ui

#### **Objective**
Address all known UI bugs, broken navigation, and functional gaps in the existing client-side application. Make the current static frontend "feature-perfect" and ready for backend integration.

#### **Key Tasks & Requirements**

**1. Fix UI Visibility and Interaction Bugs**
- **Homepage (`src/pages/Index.tsx`):** Ensure all buttons including "Free Delivery" are visible by default, not just on hover
- **Sales Page (`src/pages/Sales.tsx`):** Fix "View All Products" button visibility
- **Delivery Page (`src/pages/Delivery.tsx`):** Fix "Contact Delivery Team" button visibility

**2. Repair Broken Navigation and Actions**
- **Homepage:** Update "Browse Collection" buttons to navigate to `/products` with category filters (e.g., `/products?category=doors`)
- **Sales Page:** Wire "Contact for Bulk Pricing" button to navigate to `/contact` page
- **Delivery Page:** Wire "Contact Delivery Team" button to navigate to `/contact` page
- **Header (`src/components/Header.tsx`):** Implement functional search bar with client-side filtering

**3. Fix Cart Functionality Issues**
- **Cart Page (`src/pages/Cart.tsx`):** Fix the missing `Link` import error on line 189
- **Add to Cart:** Implement working "Add to Cart" buttons throughout the site using React Context or localStorage
- **Cart State Management:** Ensure cart persists across page navigation and quantity updates work correctly

**4. Localization for Kenyan Market**
- **Currency:** Implement and use a `CurrencyFormatter` component to display all prices in Kenyan Shillings (KSH)
- **Units:** Update all weight references from pounds (lbs) to kilograms (KG)
- **Delivery Calculator:** Build functional delivery cost calculator on delivery page with KSH output

**5. Enhanced Product and Category Pages**
- **Products Page (`src/pages/Products.tsx`):** Ensure category filtering works from URL parameters
- **Categories Page (`src/pages/Categories.tsx`):** Make category cards clickable and navigate to filtered products
- **Search Functionality:** Implement client-side product search filtering

**6. Payment Flow Placeholder**
- Create a placeholder checkout/payment page (`/checkout/payment`) with non-functional credit card form UI
- Ensure payment method selection on cart page navigates correctly

#### **Acceptance Criteria**
- All buttons are visible and functional
- Navigation works correctly between all pages
- Cart functionality is fully operational on client-side
- All data displays in KSH and KG
- Search and filtering work with hardcoded data
- Zero console errors
- Ready for Django backend integration

---

### **Phase 2.1: Django Backend Foundation & Core E-commerce API**

**Duration:** 1-2 weeks
**Persona:** Full-Stack Developer with expertise in Django, Django Rest Framework, and React integration

#### **Objective**
Build the foundational Django backend infrastructure for DeepForest Hardware Emporium. Create core models, authentication, and basic CRUD operations that will serve as the foundation for both payment integration and InvenTree integration.

#### **Key Tasks & Requirements**

**1. Django Project Setup**
- **New Django Project:** Create a new Django project in a `backend/` directory
- **Database:** Configure PostgreSQL as the database
- **API Framework:** Set up Django Rest Framework (DRF)
- **CORS:** Configure `django-cors-headers` for React frontend communication
- **Environment:** Set up proper environment variable management

**2. Core Database Models (`backend/models.py`)**
- **Category Model:** name, description, image_url, slug, created_at, updated_at
- **Product Model:** name, description, price_ksh, sku, category (FK), image_url, is_active, on_sale, sale_price_ksh, stock_quantity (temporary field), created_at, updated_at
- **User Profile:** Extend Django User with Kenyan-specific fields (phone, county, delivery_address)
- **Cart & CartItem:** User-linked shopping cart functionality
- **Data Migration:** Script to populate initial product/category data from frontend

**3. Core DRF API Endpoints**
- **Products API:** `/api/products/` with filtering (category, search, price range), pagination
- **Categories API:** `/api/categories/` for category listing
- **Authentication:** JWT-based auth with `/api/auth/` endpoints (register, login, logout)
- **Cart API:** Protected `/api/cart/` endpoints for authenticated users
- **User API:** `/api/user/profile/` for user profile management

**4. Kenyan Market Foundation**
- **Currency Handling:** All prices stored and returned in KSH
- **Delivery Zones:** Model for Kenyan counties and delivery costs

**5. Frontend Integration**
- **Remove Static Data:** Replace all hardcoded arrays in React components
- **TanStack Query:** Implement React Query for all API calls with proper loading/error states
- **Authentication Flow:** Build login/register components with JWT handling
- **Dynamic Cart:** Connect cart UI to backend API

**6. Admin Interface**
- **Django Admin:** Customize admin interface for product/category management
- **Permissions:** Set up proper user roles and permissions
- **Data Management:** Tools for bulk product import/export

#### **Acceptance Criteria**
- Fully functional Django backend with PostgreSQL
- Core REST API with proper authentication
- React frontend is 100% dynamic (no hardcoded data)
- User registration, login, and profile management works
- Cart persists in database for logged-in users
- Admin interface allows product/category management
- All prices and weights use Kenyan standards (KSH, KG)
- Foundation ready for payment integration

---

### **Phase 2.2: Payment Integration & Order Processing with Pesapal API**

**Duration:** 1-2 weeks
**Persona:** Full-Stack Developer with expertise in Django, payment gateway integration, and Kenyan payment systems

#### **Objective**
Implement complete payment processing functionality using Pesapal API for Kenyan market, including M-Pesa, card payments, and order management. Build robust order processing workflow with payment verification and status tracking.

#### **Key Tasks & Requirements**

**1. Pesapal API Integration & File Structure**
- **Pesapal Setup:** Configure Pesapal merchant account and API credentials
- **Payment Models:** Create models for payment tracking, transaction logs, and payment methods
- **Pesapal API Files:** Create dedicated Pesapal integration files:
  - `backend/payments/pesapal_client.py` - Main Pesapal API client class
  - `backend/payments/pesapal_config.py` - Configuration and credentials management
  - `backend/payments/pesapal_utils.py` - Utility functions for payment processing
  - `backend/payments/pesapal_webhooks.py` - IPN (Instant Payment Notification) handlers
  - `backend/payments/pesapal_exceptions.py` - Custom exception classes for payment errors
- **Pesapal SDK:** Integrate Pesapal Python SDK or direct API calls using the above files
- **Payment Methods:** Support M-Pesa, Visa, Mastercard, and other Pesapal-supported methods

**2. Order & Payment Models**
- **Order Model:** Extend with payment_status, payment_method, pesapal_transaction_id, total_amount_ksh
- **Payment Model:** pesapal_transaction_id, order (FK), amount_ksh, status, payment_method, created_at, completed_at
- **PaymentLog Model:** Track all payment attempts and responses from Pesapal

**3. Payment API Endpoints**
- **Payment Initiation:** `/api/payments/initiate/` - Create Pesapal payment request
- **Payment Callback:** `/api/payments/callback/` - Handle Pesapal IPN (Instant Payment Notification)
- **Payment Status:** `/api/payments/status/<transaction_id>/` - Check payment status
- **Orders API:** `/api/orders/` for order creation with payment processing

**4. Frontend Payment Flow**
- **Checkout Process:** Multi-step checkout with payment method selection
- **Pesapal Integration:** Redirect to Pesapal payment page and handle return
- **Payment Status:** Real-time payment status updates and order confirmation
- **Order History:** Display payment status and transaction details

**5. Kenyan Payment Features**
- **M-Pesa Integration:** Seamless M-Pesa payment flow through Pesapal
- **Mobile-First:** Optimized payment flow for mobile devices
- **KSH Currency:** All payment amounts in Kenyan Shillings
- **Local Payment Methods:** Support for local banks and mobile money

**6. Security & Compliance**
- **Payment Security:** Secure handling of payment data and API keys
- **Transaction Logging:** Comprehensive audit trail of all payment activities
- **Error Handling:** Robust error handling for failed payments and network issues
- **PCI Compliance:** Follow best practices for payment data handling

#### **Acceptance Criteria**
- Complete Pesapal API integration with all supported payment methods
- Functional M-Pesa payment flow through Pesapal
- Orders are created and payment status tracked in database
- Payment callback handling works reliably
- Frontend checkout flow is complete and user-friendly
- All payment amounts processed in KSH
- Comprehensive payment logging and error handling
- Ready for InvenTree integration in Phase 3

---

### **Phase 3: Deep InvenTree Integration for Unified Inventory Management**

**Duration:** 3-4 weeks  
**Persona:** Senior Django Developer with expertise in complex system integration and inventory management

#### **Objective**
Fully integrate the InvenTree Django application from `InvenTree-master/src/backend/InvenTree/` into the DeepForest backend, creating a unified system where the website's admin dashboard becomes the single interface for both e-commerce and inventory management.

#### **Key Tasks & Requirements**

**1. Strategic Codebase Integration**
- **App Migration:** Move essential InvenTree Django apps (`part`, `stock`, `company`, `order`) into the main `backend/` directory
- **Dependency Resolution:** Merge InvenTree's `requirements.txt` with the existing backend requirements
- **Settings Integration:** Merge InvenTree configurations into main `settings.py`
- **URL Integration:** Include InvenTree URLs under `/admin/inventory/` and `/api/inventory/`

**2. Database Schema Unification**
- **Single Database:** Configure InvenTree apps to use the same PostgreSQL database
- **Model Relationships:** Create One-to-One relationship between e-commerce `Product` and InvenTree `Part`
- **Stock Integration:** Replace `Product.stock_quantity` with a `@property` that queries InvenTree `StockItem` in real-time
- **Automated Part Creation:** Use Django signals to auto-create InvenTree `Part` when e-commerce `Product` is created

**3. React Admin Dashboard Enhancement**
- **Admin Routes:** Create protected `/admin` route group in React
- **Inventory Dashboard:** Build `/admin/inventory` page using shadcn/ui components
- **Stock Management UI:** Components for viewing/editing stock levels, adding stock, stock history
- **Product-Part Linking:** Interface to manage relationships between e-commerce products and inventory parts
- **Real-time Updates:** Display live stock levels throughout the admin interface

**4. Business Logic Integration**
- **Order Processing:** Modify order creation to automatically consume InvenTree stock
- **Stock Validation:** Prevent orders when insufficient stock available
- **Inventory Alerts:** Notifications for low stock levels
- **Stock Movements:** Track all stock changes through InvenTree's system

**5. Public-Facing Integration**
- **Real-time Stock Display:** Show accurate stock levels on product pages
- **Out of Stock Handling:** Disable "Add to Cart" for zero-stock items
- **Stock-based Filtering:** Allow filtering products by availability
- **Delivery Estimation:** Use stock location data for delivery time estimates

**6. Admin Workflow Optimization**
- **Unified Dashboard:** Single interface for both e-commerce and inventory management
- **Role-based Access:** Different permission levels for different admin functions
- **Reporting Integration:** Combine sales and inventory reports
- **Bulk Operations:** Tools for bulk stock updates and product management

**7. Kenyan Market Specific Adaptations**
- **Currency Integration:** Ensure all InvenTree pricing uses KSH
- **Local Suppliers:** Adapt supplier management for Kenyan vendors
- **Import/Export:** Tools for managing international vs local stock

#### **Acceptance Criteria**
- InvenTree is fully embedded within the main Django project (not separate service)
- Admin can manage entire business through unified React dashboard at `/admin`
- Creating e-commerce products automatically creates corresponding inventory parts
- Customer orders automatically update inventory levels
- Public product pages show real-time stock availability
- All inventory operations (receiving, adjustments, transfers) work through admin interface
- System is deployable as single Django application with React frontend
- Full audit trail of all inventory movements
- Integration maintains InvenTree's powerful inventory features while providing e-commerce-friendly interface

## Technical Architecture

### **Final System Architecture**
```
┌─────────────────────────────────────────────────────────────┐
│                    React Frontend                           │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │   Public Site   │  │  Admin Dashboard │  │ Auth System  │ │
│  │  (e-commerce)   │  │   (inventory)    │  │   (JWT)      │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────────────────────────────────────────────┘
                              │
                              │ REST API
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                   Django Backend                            │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │  E-commerce     │  │   InvenTree     │  │  Unified     │ │
│  │    Models       │  │    Models       │  │   Admin      │ │
│  │                 │  │                 │  │              │ │
│  │  - Product ──────────→ Part          │  │              │ │
│  │  - Category     │  │  - StockItem    │  │              │ │
│  │  - Order        │  │  - StockLocation│  │              │ │
│  │  - Cart         │  │  - Company      │  │              │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
                    ┌─────────────────┐
                    │   PostgreSQL    │
                    │    Database     │
                    │   (Unified)     │
                    └─────────────────┘
```

### **Key Integration Points**
1. **Product ↔ Part Relationship:** One-to-One mapping between e-commerce products and inventory parts
2. **Real-time Stock:** Product stock levels pulled from InvenTree StockItem queries
3. **Order Processing:** Sales orders automatically create inventory movements
4. **Unified Admin:** Single React dashboard for both e-commerce and inventory management
5. **Shared Authentication:** Single user system across all functionality

## Project Deliverables

### **Technical Deliverables**
- [ ] **Integrated Inventory Module:** Fully embedded InvenTree functionality within website
- [ ] **Unified Admin Dashboard:** Complete inventory management through website's admin interface
- [ ] **Internal APIs:** Service layer for inventory operations and cross-module communication
- [ ] **Database Integration:** Unified schema with proper migrations and data consistency

### **Documentation Deliverables**
- [ ] **Integration Documentation:** Detailed technical documentation of the integration process
- [ ] **Admin User Guide:** Comprehensive guide for managing inventory through the admin dashboard
- [ ] **API Documentation:** Internal API specifications and usage examples
- [ ] **Deployment Guide:** Step-by-step deployment and maintenance instructions
- [ ] **Example Workflows:** Demonstration of inventory CRUD operations via admin interface

### **Quality Assurance**
- [ ] **Scalability:** Ensure the integrated system can handle growth and increased load
- [ ] **Maintainability:** Clear code structure and documentation for future enhancements
- [ ] **Security:** Proper authentication, authorization, and data protection
- [ ] **Performance:** Optimized database queries and efficient inventory operations

## Success Metrics

### **Phase 1 Success Metrics**
- All UI bugs resolved and buttons functional
- Cart functionality works completely on client-side
- All data displays in Kenyan standards (KSH, KG)
- Zero console errors

### **Phase 2 Success Metrics**
- Backend API handles all frontend data needs
- User authentication and cart persistence work
- Admin can manage products through Django admin
- Frontend is completely dynamic

### **Phase 3 Success Metrics**
- Real-time inventory levels displayed on product pages
- Orders automatically update stock levels
- Admin dashboard provides complete business management
- Single deployment for entire system

## Risk Mitigation

### **Technical Risks**
- **InvenTree Compatibility:** Test integration early and maintain InvenTree's core functionality
- **Database Conflicts:** Careful schema planning and migration testing
- **Performance:** Monitor query performance with real-time stock lookups

### **Business Risks**
- **Data Loss:** Comprehensive backup strategy during integration
- **Downtime:** Staged deployment with rollback capabilities
- **User Training:** Clear documentation and training materials for admin users

## Timeline Summary

| Phase | Duration | Key Milestone |
|-------|----------|---------------|
| Phase 1 | 1-2 weeks | Frontend bugs fixed, ready for backend |
| Phase 2 | 2-3 weeks | Dynamic e-commerce site with Django API |
| Phase 3 | 3-4 weeks | Fully integrated inventory management |
| **Total** | **6-9 weeks** | **Complete unified platform** |

## Next Steps

1. **Immediate:** Begin Phase 1 frontend stabilization
2. **Week 2:** Start Django backend development
3. **Week 5:** Begin InvenTree integration planning
4. **Week 8:** System testing and deployment preparation
5. **Week 9:** Go-live and monitoring

This implementation plan provides a clear roadmap for transforming DeepForest Hardware Emporium into a fully integrated e-commerce and inventory management platform.

