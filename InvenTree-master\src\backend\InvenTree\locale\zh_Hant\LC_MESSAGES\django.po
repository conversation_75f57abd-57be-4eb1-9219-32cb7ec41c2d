msgid ""
msgstr ""
"Project-Id-Version: inventree\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-09 19:12+0000\n"
"PO-Revision-Date: 2025-05-09 19:14\n"
"Last-Translator: \n"
"Language-Team: Chinese Traditional\n"
"Language: zh_TW\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"X-Crowdin-Project: inventree\n"
"X-Crowdin-Project-ID: 452300\n"
"X-Crowdin-Language: zh-TW\n"
"X-Crowdin-File: /src/backend/InvenTree/locale/en/LC_MESSAGES/django.po\n"
"X-Crowdin-File-ID: 250\n"

#: InvenTree/AllUserRequire2FAMiddleware.py:42
msgid "You must enable two-factor authentication before doing anything else."
msgstr ""

#: InvenTree/api.py:355
msgid "API endpoint not found"
msgstr "未找到 API 端點"

#: InvenTree/api.py:432
msgid "List of items or filters must be provided for bulk operation"
msgstr ""

#: InvenTree/api.py:439
msgid "Items must be provided as a list"
msgstr ""

#: InvenTree/api.py:447
msgid "Invalid items list provided"
msgstr "提供了無效的單位"

#: InvenTree/api.py:453
msgid "Filters must be provided as a dict"
msgstr ""

#: InvenTree/api.py:460
msgid "Invalid filters provided"
msgstr "提供了無效的過濾器"

#: InvenTree/api.py:465
msgid "All filter must only be used with true"
msgstr ""

#: InvenTree/api.py:470
msgid "No items match the provided criteria"
msgstr ""

#: InvenTree/api.py:713
msgid "User does not have permission to view this model"
msgstr "用户沒有權限查閲當前模型。"

#: InvenTree/auth_overrides.py:57
msgid "Email (again)"
msgstr "電子郵件 (重複)"

#: InvenTree/auth_overrides.py:61
msgid "Email address confirmation"
msgstr "郵箱地址已確認"

#: InvenTree/auth_overrides.py:84
msgid "You must type the same email each time."
msgstr "您必須每次輸入相同的電子郵件。"

#: InvenTree/auth_overrides.py:126 InvenTree/auth_overrides.py:134
msgid "The provided primary email address is not valid."
msgstr "提供的主電子郵件地址無效。"

#: InvenTree/auth_overrides.py:140
msgid "The provided email domain is not approved."
msgstr "提供的郵箱域名未被批准。"

#: InvenTree/conversion.py:162
#, python-brace-format
msgid "Invalid unit provided ({unit})"
msgstr "提供了無效的單位 ({unit})"

#: InvenTree/conversion.py:179
msgid "No value provided"
msgstr "沒有提供數值"

#: InvenTree/conversion.py:206
#, python-brace-format
msgid "Could not convert {original} to {unit}"
msgstr "不能將 {original} 轉換到 {unit}"

#: InvenTree/conversion.py:208 InvenTree/conversion.py:222
#: InvenTree/helpers.py:548 order/models.py:707 order/models.py:975
msgid "Invalid quantity provided"
msgstr "提供的數量無效"

#: InvenTree/exceptions.py:109
msgid "Error details can be found in the admin panel"
msgstr "在管理面板中可以找到錯誤詳細信息"

#: InvenTree/fields.py:138
msgid "Enter date"
msgstr "輸入日期"

#: InvenTree/fields.py:161
msgid "Invalid decimal value"
msgstr ""

#: InvenTree/fields.py:210 InvenTree/models.py:887 build/serializers.py:526
#: build/serializers.py:597 company/models.py:834 order/models.py:1579
#: part/models.py:3366
#: report/templates/report/inventree_build_order_report.html:172
#: stock/models.py:2708 stock/models.py:2832 stock/serializers.py:756
#: stock/serializers.py:918 stock/serializers.py:1048 stock/serializers.py:1100
#: stock/serializers.py:1419 stock/serializers.py:1508
#: stock/serializers.py:1684
msgid "Notes"
msgstr "備註"

#: InvenTree/format.py:166
#, python-brace-format
msgid "Value '{name}' does not appear in pattern format"
msgstr "值' {name}' 未出現在模式格式中"

#: InvenTree/format.py:177
msgid "Provided value does not match required pattern: "
msgstr "提供的值與所需模式不匹配："

#: InvenTree/helpers.py:552
msgid "Cannot serialize more than 1000 items at once"
msgstr ""

#: InvenTree/helpers.py:558
msgid "Empty serial number string"
msgstr "序號為空白"

#: InvenTree/helpers.py:587
msgid "Duplicate serial"
msgstr "複製序列號"

#: InvenTree/helpers.py:619 InvenTree/helpers.py:662 InvenTree/helpers.py:680
#: InvenTree/helpers.py:687 InvenTree/helpers.py:706
#, python-brace-format
msgid "Invalid group: {group}"
msgstr ""

#: InvenTree/helpers.py:650
#, python-brace-format
msgid "Group range {group} exceeds allowed quantity ({expected_quantity})"
msgstr "組範圍 {group} 超出了允許的數量 ({expected_quantity})"

#: InvenTree/helpers.py:716
msgid "No serial numbers found"
msgstr "未找到序列號"

#: InvenTree/helpers.py:721
msgid "Number of unique serial numbers ({len(serials)}) must match quantity ({expected_quantity})"
msgstr "唯一序列號的數量 ({len(serials)}) 必須與數量匹配 ({expected_quantity})"

#: InvenTree/helpers.py:840
msgid "Remove HTML tags from this value"
msgstr "從這個值中刪除 HTML 標籤"

#: InvenTree/helpers.py:919
msgid "Data contains prohibited markdown content"
msgstr ""

#: InvenTree/helpers_model.py:131
msgid "Connection error"
msgstr "連接錯誤"

#: InvenTree/helpers_model.py:136 InvenTree/helpers_model.py:143
msgid "Server responded with invalid status code"
msgstr "服務器響應狀態碼無效"

#: InvenTree/helpers_model.py:139
msgid "Exception occurred"
msgstr "發生異常"

#: InvenTree/helpers_model.py:149
msgid "Server responded with invalid Content-Length value"
msgstr "服務器響應的內容長度值無效"

#: InvenTree/helpers_model.py:152
msgid "Image size is too large"
msgstr "圖片尺寸過大"

#: InvenTree/helpers_model.py:164
msgid "Image download exceeded maximum size"
msgstr "圖片下載超出最大尺寸"

#: InvenTree/helpers_model.py:169
msgid "Remote server returned empty response"
msgstr "遠程服務器返回了空響應"

#: InvenTree/helpers_model.py:177
msgid "Supplied URL is not a valid image file"
msgstr "提供的 URL 不是一個有效的圖片文件"

#: InvenTree/locales.py:20
msgid "Arabic"
msgstr "阿拉伯語"

#: InvenTree/locales.py:21
msgid "Bulgarian"
msgstr "Bulgarian"

#: InvenTree/locales.py:22
msgid "Czech"
msgstr "Czech"

#: InvenTree/locales.py:23
msgid "Danish"
msgstr "Danish"

#: InvenTree/locales.py:24
msgid "German"
msgstr "German"

#: InvenTree/locales.py:25
msgid "Greek"
msgstr "Greek"

#: InvenTree/locales.py:26
msgid "English"
msgstr "English"

#: InvenTree/locales.py:27
msgid "Spanish"
msgstr "Spanish"

#: InvenTree/locales.py:28
msgid "Spanish (Mexican)"
msgstr "Spanish (Mexican)"

#: InvenTree/locales.py:29
msgid "Estonian"
msgstr "愛沙尼亞語"

#: InvenTree/locales.py:30
msgid "Farsi / Persian"
msgstr "Farsi / Persian"

#: InvenTree/locales.py:31
msgid "Finnish"
msgstr "Finnish"

#: InvenTree/locales.py:32
msgid "French"
msgstr "French"

#: InvenTree/locales.py:33
msgid "Hebrew"
msgstr "Hebrew"

#: InvenTree/locales.py:34
msgid "Hindi"
msgstr "Hindi"

#: InvenTree/locales.py:35
msgid "Hungarian"
msgstr "Hungarian"

#: InvenTree/locales.py:36
msgid "Italian"
msgstr "Italian"

#: InvenTree/locales.py:37
msgid "Japanese"
msgstr "Japanese"

#: InvenTree/locales.py:38
msgid "Korean"
msgstr "Korean"

#: InvenTree/locales.py:39
msgid "Lithuanian"
msgstr "立陶宛語"

#: InvenTree/locales.py:40
msgid "Latvian"
msgstr "Latvian"

#: InvenTree/locales.py:41
msgid "Dutch"
msgstr "Dutch"

#: InvenTree/locales.py:42
msgid "Norwegian"
msgstr "Norwegian"

#: InvenTree/locales.py:43
msgid "Polish"
msgstr "Polish"

#: InvenTree/locales.py:44
msgid "Portuguese"
msgstr "Portuguese"

#: InvenTree/locales.py:45
msgid "Portuguese (Brazilian)"
msgstr "Portuguese (Brazilian)"

#: InvenTree/locales.py:46
msgid "Romanian"
msgstr "羅馬尼亞語"

#: InvenTree/locales.py:47
msgid "Russian"
msgstr "Russian"

#: InvenTree/locales.py:48
msgid "Slovak"
msgstr "Slovak"

#: InvenTree/locales.py:49
msgid "Slovenian"
msgstr "Slovenian"

#: InvenTree/locales.py:50
msgid "Serbian"
msgstr "Serbian"

#: InvenTree/locales.py:51
msgid "Swedish"
msgstr "Swedish"

#: InvenTree/locales.py:52
msgid "Thai"
msgstr "Thai"

#: InvenTree/locales.py:53
msgid "Turkish"
msgstr "Turkish"

#: InvenTree/locales.py:54
msgid "Ukrainian"
msgstr "烏克蘭語"

#: InvenTree/locales.py:55
msgid "Vietnamese"
msgstr "Vietnamese"

#: InvenTree/locales.py:56
msgid "Chinese (Simplified)"
msgstr "中文 (簡體)"

#: InvenTree/locales.py:57
msgid "Chinese (Traditional)"
msgstr "中文 (繁體)"

#: InvenTree/magic_login.py:31
msgid "Log in to the app"
msgstr ""

#: InvenTree/magic_login.py:41 company/models.py:136 users/serializers.py:247
msgid "Email"
msgstr "電子郵件"

#: InvenTree/models.py:107
msgid "Error running plugin validation"
msgstr "驗證外掛程式時發生錯誤"

#: InvenTree/models.py:184
msgid "Metadata must be a python dict object"
msgstr "Metadata必須是一個Python Dictionary物件"

#: InvenTree/models.py:190
msgid "Plugin Metadata"
msgstr "外掛程式Metadata"

#: InvenTree/models.py:191
msgid "JSON metadata field, for use by external plugins"
msgstr "外掛程式使用的JSON Metadata欄位"

#: InvenTree/models.py:374
msgid "Improperly formatted pattern"
msgstr "格式錯誤"

#: InvenTree/models.py:381
msgid "Unknown format key specified"
msgstr "指定了不明的格式鍵值"

#: InvenTree/models.py:387
msgid "Missing required format key"
msgstr "缺少必須的格式鍵值"

#: InvenTree/models.py:398
msgid "Reference field cannot be empty"
msgstr "參考欄位不能空白"

#: InvenTree/models.py:406
msgid "Reference must match required pattern"
msgstr "參考欄位並須符合格式"

#: InvenTree/models.py:437
msgid "Reference number is too large"
msgstr "參考編號過大"

#: InvenTree/models.py:688
msgid "Duplicate names cannot exist under the same parent"
msgstr "同一個上層元件下不能有重複的名字"

#: InvenTree/models.py:705
msgid "Invalid choice"
msgstr "無效的選項"

#: InvenTree/models.py:734 common/models.py:1325 common/models.py:1752
#: common/models.py:2011 common/models.py:2136 common/serializers.py:488
#: company/models.py:593 generic/states/serializers.py:20 machine/models.py:24
#: part/models.py:1039 part/models.py:3837 plugin/models.py:53
#: report/models.py:203 stock/models.py:83
msgid "Name"
msgstr "名稱"

#: InvenTree/models.py:740 build/models.py:242 common/models.py:123
#: common/models.py:2143 common/models.py:2256 company/models.py:521
#: company/models.py:825 order/models.py:429 order/models.py:1615
#: part/models.py:1062 part/models.py:3852 report/models.py:209
#: report/models.py:714 report/models.py:740
#: report/templates/report/inventree_build_order_report.html:117
#: stock/models.py:89
msgid "Description"
msgstr "描述"

#: InvenTree/models.py:741 stock/models.py:90
msgid "Description (optional)"
msgstr "描述（選填）"

#: InvenTree/models.py:756 common/models.py:2309
msgid "Path"
msgstr "路徑"

#: InvenTree/models.py:887
msgid "Markdown notes (optional)"
msgstr "Markdown 註記（選填）"

#: InvenTree/models.py:918
msgid "Barcode Data"
msgstr "條碼資料"

#: InvenTree/models.py:919
msgid "Third party barcode data"
msgstr "第三方條碼資料"

#: InvenTree/models.py:925
msgid "Barcode Hash"
msgstr "條碼雜湊值"

#: InvenTree/models.py:926
msgid "Unique hash of barcode data"
msgstr "條碼資料的唯一雜湊值"

#: InvenTree/models.py:1003
msgid "Existing barcode found"
msgstr "發現現有條碼"

#: InvenTree/models.py:1084
msgid "Task Failure"
msgstr ""

#: InvenTree/models.py:1085
#, python-brace-format
msgid "Background worker task '{f}' failed after {n} attempts"
msgstr ""

#: InvenTree/models.py:1112
msgid "Server Error"
msgstr "伺服器錯誤"

#: InvenTree/models.py:1113
msgid "An error has been logged by the server."
msgstr "伺服器紀錄了一個錯誤。"

#: InvenTree/serializers.py:69 part/models.py:4455
msgid "Must be a valid number"
msgstr "必須是有效的數字"

#: InvenTree/serializers.py:111 company/models.py:187 part/models.py:3184
msgid "Currency"
msgstr "貨幣"

#: InvenTree/serializers.py:114 part/serializers.py:1335
msgid "Select currency from available options"
msgstr "從可用選項中選擇貨幣"

#: InvenTree/serializers.py:466
msgid "Invalid value"
msgstr "無效值"

#: InvenTree/serializers.py:503
msgid "Remote Image"
msgstr "遠程圖片"

#: InvenTree/serializers.py:504
msgid "URL of remote image file"
msgstr "遠程圖片文件的 URL"

#: InvenTree/serializers.py:522
msgid "Downloading images from remote URL is not enabled"
msgstr "未啓用從遠程 URL下載圖片"

#: InvenTree/serializers.py:529
msgid "Failed to download image from remote URL"
msgstr "從遠程URL下載圖像失敗"

#: InvenTree/validators.py:30
msgid "Invalid physical unit"
msgstr "無效的物理單位"

#: InvenTree/validators.py:36
msgid "Not a valid currency code"
msgstr "無效的貨幣代碼"

#: InvenTree/validators.py:116 InvenTree/validators.py:132
msgid "Overage value must not be negative"
msgstr "損失值不能為負"

#: InvenTree/validators.py:134
msgid "Overage must not exceed 100%"
msgstr "損失率不能超過100%"

#: InvenTree/validators.py:140
msgid "Invalid value for overage"
msgstr "無效的損失值"

#: build/api.py:39 order/api.py:104 order/api.py:263 order/serializers.py:128
msgid "Order Status"
msgstr "訂單狀態"

#: build/api.py:65 build/models.py:254
msgid "Parent Build"
msgstr "上層生產工單"

#: build/api.py:69 build/api.py:768 order/api.py:512 order/api.py:732
#: order/api.py:1106 order/api.py:1334 stock/api.py:522
msgid "Include Variants"
msgstr "包含變體"

#: build/api.py:85 build/api.py:470 build/api.py:782 build/models.py:260
#: build/serializers.py:1254 build/serializers.py:1376
#: build/serializers.py:1427 company/models.py:1044 company/serializers.py:432
#: order/api.py:291 order/api.py:295 order/api.py:880 order/api.py:1119
#: order/api.py:1122 order/models.py:1704 order/models.py:1863
#: order/models.py:1864 part/api.py:1448 part/api.py:1451 part/api.py:1517
#: part/api.py:1827 part/models.py:457 part/models.py:3195 part/models.py:3339
#: part/models.py:3487 part/models.py:3508 part/models.py:3530
#: part/models.py:3666 part/models.py:4027 part/models.py:4299
#: part/models.py:4665 part/serializers.py:1255 part/serializers.py:1924
#: report/templates/report/inventree_bill_of_materials_report.html:110
#: report/templates/report/inventree_bill_of_materials_report.html:137
#: report/templates/report/inventree_build_order_report.html:109
#: report/templates/report/inventree_purchase_order_report.html:27
#: report/templates/report/inventree_return_order_report.html:24
#: report/templates/report/inventree_sales_order_report.html:27
#: report/templates/report/inventree_sales_order_shipment_report.html:28
#: report/templates/report/inventree_stock_location_report.html:102
#: stock/api.py:535 stock/serializers.py:119 stock/serializers.py:171
#: stock/serializers.py:481 stock/serializers.py:643 stock/serializers.py:948
#: templates/email/build_order_completed.html:17
#: templates/email/build_order_required_stock.html:17
#: templates/email/low_stock_notification.html:15
#: templates/email/overdue_build_order.html:16
#: templates/email/part_event_notification.html:15
msgid "Part"
msgstr "零件"

#: build/api.py:105 build/api.py:108 part/api.py:1531 part/models.py:1080
#: part/models.py:3558 part/models.py:4136 part/serializers.py:1265
#: stock/api.py:818
msgid "Category"
msgstr "類別"

#: build/api.py:116 build/api.py:120
msgid "Ancestor Build"
msgstr "可測試部分"

#: build/api.py:137 order/api.py:122
msgid "Assigned to me"
msgstr "分配給我"

#: build/api.py:152
#: report/templates/report/inventree_build_order_report.html:152
msgid "Issued By"
msgstr "發佈者"

#: build/api.py:167
msgid "Assigned To"
msgstr "負責人"

#: build/api.py:202
msgid "Created before"
msgstr ""

#: build/api.py:206
msgid "Created after"
msgstr ""

#: build/api.py:210
msgid "Has start date"
msgstr ""

#: build/api.py:218
msgid "Start date before"
msgstr ""

#: build/api.py:222
msgid "Start date after"
msgstr ""

#: build/api.py:226
msgid "Has target date"
msgstr ""

#: build/api.py:234
msgid "Target date before"
msgstr ""

#: build/api.py:238
msgid "Target date after"
msgstr ""

#: build/api.py:242
msgid "Completed before"
msgstr ""

#: build/api.py:246
msgid "Completed after"
msgstr ""

#: build/api.py:249 order/api.py:219
msgid "Min Date"
msgstr ""

#: build/api.py:272 order/api.py:238
msgid "Max Date"
msgstr ""

#: build/api.py:297 build/api.py:300 part/api.py:224
msgid "Exclude Tree"
msgstr "排除樹"

#: build/api.py:409
msgid "Build must be cancelled before it can be deleted"
msgstr "工單必須被取消才能被刪除"

#: build/api.py:453 build/serializers.py:1392 part/models.py:4333
msgid "Consumable"
msgstr "耗材"

#: build/api.py:456 build/serializers.py:1395 part/models.py:4327
msgid "Optional"
msgstr "非必須項目"

#: build/api.py:459 common/setting/system.py:429 part/models.py:1211
#: part/serializers.py:1663 part/serializers.py:1672 stock/api.py:588
msgid "Assembly"
msgstr "裝配"

#: build/api.py:462
msgid "Tracked"
msgstr "追蹤中"

#: build/api.py:465 build/serializers.py:1398 part/models.py:1229
msgid "Testable"
msgstr "可測試"

#: build/api.py:475 order/api.py:925
msgid "Order Outstanding"
msgstr ""

#: build/api.py:485 order/api.py:884
msgid "Allocated"
msgstr "已分配"

#: build/api.py:495 company/models.py:889 company/serializers.py:427
#: templates/email/build_order_required_stock.html:19
#: templates/email/low_stock_notification.html:17
#: templates/email/part_event_notification.html:18
msgid "Available"
msgstr "可用數量"

#: build/api.py:805 build/models.py:116
#: report/templates/report/inventree_build_order_report.html:105
#: stock/serializers.py:92 templates/email/build_order_completed.html:16
#: templates/email/overdue_build_order.html:15
msgid "Build Order"
msgstr "生產工單"

#: build/models.py:117 users/ruleset.py:37
msgid "Build Orders"
msgstr "生產工單"

#: build/models.py:162
msgid "Assembly BOM has not been validated"
msgstr "裝配物料清單尚未驗證"

#: build/models.py:169
msgid "Build order cannot be created for an inactive part"
msgstr "無法為未激活的零件創建生產訂單"

#: build/models.py:176
msgid "Build order cannot be created for an unlocked part"
msgstr "無法為已解鎖的零件創建生產訂單"

#: build/models.py:188
msgid "Invalid choice for parent build"
msgstr "無效的上層生產工單選擇"

#: build/models.py:197 order/models.py:363
msgid "Responsible user or group must be specified"
msgstr "必須指定負責的用户或組"

#: build/models.py:202
msgid "Build order part cannot be changed"
msgstr "無法更改生產工單"

#: build/models.py:207 order/models.py:376
msgid "Target date must be after start date"
msgstr ""

#: build/models.py:235
msgid "Build Order Reference"
msgstr "生產工單代號"

#: build/models.py:236 build/serializers.py:1389 order/models.py:601
#: order/models.py:1140 order/models.py:1572 order/models.py:2403
#: part/models.py:4348
#: report/templates/report/inventree_bill_of_materials_report.html:139
#: report/templates/report/inventree_purchase_order_report.html:28
#: report/templates/report/inventree_return_order_report.html:26
#: report/templates/report/inventree_sales_order_report.html:28
msgid "Reference"
msgstr "參考代號"

#: build/models.py:245
msgid "Brief description of the build (optional)"
msgstr "關於生產工單的簡單説明（選填）"

#: build/models.py:255
msgid "BuildOrder to which this build is allocated"
msgstr "這張生產工單對應的上層生產工單"

#: build/models.py:264
msgid "Select part to build"
msgstr "選擇要生產的零件"

#: build/models.py:269
msgid "Sales Order Reference"
msgstr "銷售訂單代號"

#: build/models.py:274
msgid "SalesOrder to which this build is allocated"
msgstr "這張生產工單對應的銷售訂單"

#: build/models.py:279 build/serializers.py:1104
msgid "Source Location"
msgstr "來源倉儲地點"

#: build/models.py:285
msgid "Select location to take stock from for this build (leave blank to take from any stock location)"
msgstr "選擇領取料件的倉儲地點（留白表示可以從任何地點領取）"

#: build/models.py:291
msgid "Destination Location"
msgstr "目標倉儲地點"

#: build/models.py:296
msgid "Select location where the completed items will be stored"
msgstr "選擇已完成項目庫存地點"

#: build/models.py:300
msgid "Build Quantity"
msgstr "生產數量"

#: build/models.py:303
msgid "Number of stock items to build"
msgstr "要生產的項目數量"

#: build/models.py:307
msgid "Completed items"
msgstr "已完成項目"

#: build/models.py:309
msgid "Number of stock items which have been completed"
msgstr "已經完成的庫存品數量"

#: build/models.py:313
msgid "Build Status"
msgstr "生產狀態"

#: build/models.py:318
msgid "Build status code"
msgstr "生產狀態代碼"

#: build/models.py:327 build/serializers.py:376 order/serializers.py:785
#: stock/models.py:1023 stock/serializers.py:84 stock/serializers.py:1651
msgid "Batch Code"
msgstr "批號"

#: build/models.py:331 build/serializers.py:377
msgid "Batch code for this build output"
msgstr "此產出的批號"

#: build/models.py:335 order/models.py:466 order/serializers.py:166
#: part/models.py:1286
msgid "Creation Date"
msgstr "建立日期"

#: build/models.py:341
msgid "Build start date"
msgstr ""

#: build/models.py:342
msgid "Scheduled start date for this build order"
msgstr ""

#: build/models.py:348
msgid "Target completion date"
msgstr "目標完成日期"

#: build/models.py:350
msgid "Target date for build completion. Build will be overdue after this date."
msgstr "生產的預計完成日期。若超過此日期則工單會逾期。"

#: build/models.py:355 order/models.py:654 order/models.py:2442
msgid "Completion Date"
msgstr "完成日期"

#: build/models.py:363
msgid "completed by"
msgstr "完成者"

#: build/models.py:372
msgid "Issued by"
msgstr "發布者"

#: build/models.py:373
msgid "User who issued this build order"
msgstr "發布此生產工單的使用者"

#: build/models.py:382 common/models.py:132 order/api.py:172
#: order/models.py:491 part/models.py:1303
#: report/templates/report/inventree_build_order_report.html:158
msgid "Responsible"
msgstr "負責人"

#: build/models.py:383
msgid "User or group responsible for this build order"
msgstr "負責此生產工單的使用者或羣組"

#: build/models.py:388 stock/models.py:1016
msgid "External Link"
msgstr "外部連結"

#: build/models.py:390 common/models.py:1899 part/models.py:1114
#: stock/models.py:1018
msgid "Link to external URL"
msgstr "外部URL連結"

#: build/models.py:395
msgid "Build Priority"
msgstr "製造優先度"

#: build/models.py:398
msgid "Priority of this build order"
msgstr "此生產工單的優先程度"

#: build/models.py:406 common/models.py:102 common/models.py:116
#: order/api.py:158 order/models.py:438
msgid "Project Code"
msgstr "專案代碼"

#: build/models.py:407
msgid "Project code for this build order"
msgstr "此生產工單隸屬的專案代碼"

#: build/models.py:680 build/models.py:803
msgid "Failed to offload task to complete build allocations"
msgstr "未能卸載任務以完成生產分配"

#: build/models.py:703
#, python-brace-format
msgid "Build order {build} has been completed"
msgstr "生產工單 {build} 已經完成"

#: build/models.py:709
msgid "A build order has been completed"
msgstr "一張生產工單已經完成"

#: build/models.py:881 build/serializers.py:424
msgid "Serial numbers must be provided for trackable parts"
msgstr "對於可跟蹤的零件，必須提供序列號"

#: build/models.py:991 build/models.py:1076
msgid "No build output specified"
msgstr "未指定產出"

#: build/models.py:994
msgid "Build output is already completed"
msgstr "產出已完成"

#: build/models.py:997
msgid "Build output does not match Build Order"
msgstr "產出與生產訂單不匹配"

#: build/models.py:1079 build/serializers.py:303 build/serializers.py:352
#: build/serializers.py:972 order/models.py:704 order/serializers.py:601
#: order/serializers.py:780 part/serializers.py:1657 stock/models.py:863
#: stock/models.py:1730 stock/serializers.py:727
msgid "Quantity must be greater than zero"
msgstr "數量必須大於零"

#: build/models.py:1083 build/serializers.py:307
msgid "Quantity cannot be greater than the output quantity"
msgstr "數量不能大於輸出數量"

#: build/models.py:1148 build/serializers.py:615
#, python-brace-format
msgid "Build output {serial} has not passed all required tests"
msgstr "產出 {serial} 未通過所有必要測試"

#: build/models.py:1534
msgid "Build Order Line Item"
msgstr "生產訂單行項目"

#: build/models.py:1558
msgid "Build object"
msgstr "生產對象"

#: build/models.py:1570 build/models.py:1829 build/serializers.py:291
#: build/serializers.py:337 build/serializers.py:1410 common/models.py:1255
#: order/models.py:1555 order/models.py:2288 order/serializers.py:1670
#: order/serializers.py:2131 part/models.py:3353 part/models.py:4321
#: part/serializers.py:275
#: report/templates/report/inventree_bill_of_materials_report.html:138
#: report/templates/report/inventree_build_order_report.html:113
#: report/templates/report/inventree_purchase_order_report.html:29
#: report/templates/report/inventree_sales_order_report.html:29
#: report/templates/report/inventree_sales_order_shipment_report.html:47
#: report/templates/report/inventree_sales_order_shipment_report.html:49
#: report/templates/report/inventree_stock_location_report.html:104
#: report/templates/report/inventree_test_report.html:90
#: report/templates/report/inventree_test_report.html:169
#: stock/serializers.py:135 stock/serializers.py:179 stock/serializers.py:718
#: templates/email/build_order_completed.html:18
msgid "Quantity"
msgstr "數量"

#: build/models.py:1571
msgid "Required quantity for build order"
msgstr "生產工單所需數量"

#: build/models.py:1655
msgid "Build item must specify a build output, as master part is marked as trackable"
msgstr "生產項必須指定產出，因為主零件已經被標記為可追蹤的"

#: build/models.py:1666
#, python-brace-format
msgid "Allocated quantity ({q}) must not exceed available stock quantity ({a})"
msgstr "分配的數量（{q}）不能超過可用的庫存數量（{a}）"

#: build/models.py:1687 order/models.py:2237
msgid "Stock item is over-allocated"
msgstr "庫存品項超額分配"

#: build/models.py:1692 order/models.py:2240
msgid "Allocation quantity must be greater than zero"
msgstr "分配的數量必須大於零"

#: build/models.py:1698
msgid "Quantity must be 1 for serialized stock"
msgstr "有序號的品項數量必須為1"

#: build/models.py:1758
msgid "Selected stock item does not match BOM line"
msgstr "選擇的庫存品項和BOM的項目不符"

#: build/models.py:1819 build/serializers.py:955 build/serializers.py:1263
#: order/serializers.py:1507 order/serializers.py:1528
#: report/templates/report/inventree_sales_order_shipment_report.html:29
#: stock/models.py:452 stock/serializers.py:101 stock/serializers.py:825
#: stock/serializers.py:1357 stock/serializers.py:1469
msgid "Stock Item"
msgstr "庫存品項"

#: build/models.py:1820
msgid "Source stock item"
msgstr "來源庫存項目"

#: build/models.py:1830
msgid "Stock quantity to allocate to build"
msgstr "要分配的庫存數量"

#: build/models.py:1839
msgid "Install into"
msgstr "安裝到"

#: build/models.py:1840
msgid "Destination stock item"
msgstr "目的庫存品項"

#: build/serializers.py:116
msgid "Build Level"
msgstr "構建等級"

#: build/serializers.py:125 part/stocktake.py:219
msgid "Part Name"
msgstr "零件名稱"

#: build/serializers.py:143
msgid "Project Code Label"
msgstr "項目編碼標籤"

#: build/serializers.py:155
msgid "Create Child Builds"
msgstr "新建子生產項目"

#: build/serializers.py:156
msgid "Automatically generate child build orders"
msgstr "自動生成子生成工單"

#: build/serializers.py:239 build/serializers.py:981
msgid "Build Output"
msgstr "產出"

#: build/serializers.py:251
msgid "Build output does not match the parent build"
msgstr "產出與之前的生產不匹配"

#: build/serializers.py:255
msgid "Output part does not match BuildOrder part"
msgstr "產出零件與生產訂單零件不匹配"

#: build/serializers.py:259
msgid "This build output has already been completed"
msgstr "此產出已經完成"

#: build/serializers.py:273
msgid "This build output is not fully allocated"
msgstr "此產出尚未完全分配"

#: build/serializers.py:292 build/serializers.py:338
msgid "Enter quantity for build output"
msgstr "輸入產出數量"

#: build/serializers.py:360
msgid "Integer quantity required for trackable parts"
msgstr "可追蹤的零件數量必須為整數"

#: build/serializers.py:366
msgid "Integer quantity required, as the bill of materials contains trackable parts"
msgstr "因為BOM包含可追蹤的零件，所以數量必須為整數"

#: build/serializers.py:383 order/serializers.py:801 order/serializers.py:1674
#: stock/serializers.py:738
msgid "Serial Numbers"
msgstr "序號"

#: build/serializers.py:384
msgid "Enter serial numbers for build outputs"
msgstr "輸出產出的序列號"

#: build/serializers.py:389 build/serializers.py:514 build/serializers.py:584
#: build/serializers.py:1267 build/serializers.py:1271 order/serializers.py:769
#: order/serializers.py:916 order/serializers.py:2022 part/serializers.py:1275
#: stock/serializers.py:110 stock/serializers.py:647 stock/serializers.py:749
#: stock/serializers.py:913 stock/serializers.py:1041 stock/serializers.py:1501
#: stock/serializers.py:1789 users/models.py:551
msgid "Location"
msgstr "地點"

#: build/serializers.py:390
msgid "Stock location for build output"
msgstr "生產輸出的庫存地點"

#: build/serializers.py:405
msgid "Auto Allocate Serial Numbers"
msgstr "自動分配序號"

#: build/serializers.py:407
msgid "Automatically allocate required items with matching serial numbers"
msgstr "自動為需要項目分配對應的序號"

#: build/serializers.py:440 order/serializers.py:894 stock/api.py:1077
#: stock/models.py:1753
msgid "The following serial numbers already exist or are invalid"
msgstr "序號已存在或無效"

#: build/serializers.py:482 build/serializers.py:538 build/serializers.py:622
msgid "A list of build outputs must be provided"
msgstr "必須提供產出清單"

#: build/serializers.py:515
msgid "Stock location for scrapped outputs"
msgstr "廢品產出的庫存位置"

#: build/serializers.py:521
msgid "Discard Allocations"
msgstr "放棄分配"

#: build/serializers.py:522
msgid "Discard any stock allocations for scrapped outputs"
msgstr "取消對廢品產出的任何庫存分配"

#: build/serializers.py:527
msgid "Reason for scrapping build output(s)"
msgstr "廢品產出的原因"

#: build/serializers.py:585
msgid "Location for completed build outputs"
msgstr "已完成刪除的庫存地點"

#: build/serializers.py:593
msgid "Accept Incomplete Allocation"
msgstr "接受不完整的分配"

#: build/serializers.py:594
msgid "Complete outputs if stock has not been fully allocated"
msgstr "如果庫存尚未全部分配，則完成產出"

#: build/serializers.py:709
msgid "Consume Allocated Stock"
msgstr "消費已分配的庫存"

#: build/serializers.py:710
msgid "Consume any stock which has already been allocated to this build"
msgstr "消耗已分配給此生產的任何庫存"

#: build/serializers.py:716
msgid "Remove Incomplete Outputs"
msgstr "移除未完成的產出"

#: build/serializers.py:717
msgid "Delete any build outputs which have not been completed"
msgstr "刪除所有未完成的產出"

#: build/serializers.py:744
msgid "Not permitted"
msgstr "不允許"

#: build/serializers.py:745
msgid "Accept as consumed by this build order"
msgstr "接受作為此生產訂單的消費"

#: build/serializers.py:746
msgid "Deallocate before completing this build order"
msgstr "完成此生產訂單前取消分配"

#: build/serializers.py:773
msgid "Overallocated Stock"
msgstr "超出分配的庫存"

#: build/serializers.py:776
msgid "How do you want to handle extra stock items assigned to the build order"
msgstr "如何處理分配給生產訂單的額外庫存項"

#: build/serializers.py:787
msgid "Some stock items have been overallocated"
msgstr "有庫存項目已被過度分配"

#: build/serializers.py:792
msgid "Accept Unallocated"
msgstr "接受未分配"

#: build/serializers.py:794
msgid "Accept that stock items have not been fully allocated to this build order"
msgstr "接受庫存項未被完全分配至生產訂單"

#: build/serializers.py:805
msgid "Required stock has not been fully allocated"
msgstr "所需庫存尚未完全分配"

#: build/serializers.py:810 order/serializers.py:444 order/serializers.py:1575
msgid "Accept Incomplete"
msgstr "接受不完整"

#: build/serializers.py:812
msgid "Accept that the required number of build outputs have not been completed"
msgstr "允許所需數量的產出未完成"

#: build/serializers.py:823
msgid "Required build quantity has not been completed"
msgstr "未完成所需生產數量"

#: build/serializers.py:835
msgid "Build order has open child build orders"
msgstr "生產訂單有打開的子生產訂單"

#: build/serializers.py:838
msgid "Build order must be in production state"
msgstr "生產訂單必須處於生產狀態"

#: build/serializers.py:841
msgid "Build order has incomplete outputs"
msgstr "生產訂單有未完成的產出"

#: build/serializers.py:880
msgid "Build Line"
msgstr "生產行"

#: build/serializers.py:888
msgid "Build output"
msgstr "產出"

#: build/serializers.py:896
msgid "Build output must point to the same build"
msgstr "生產產出必須指向相同的生產"

#: build/serializers.py:927
msgid "Build Line Item"
msgstr "生產行項目"

#: build/serializers.py:945
msgid "bom_item.part must point to the same part as the build order"
msgstr "bom_item.part 必須與生產訂單零件相同"

#: build/serializers.py:961 stock/serializers.py:1370
msgid "Item must be in stock"
msgstr "商品必須有庫存"

#: build/serializers.py:1004 order/serializers.py:1561
#, python-brace-format
msgid "Available quantity ({q}) exceeded"
msgstr "可用量 ({q}) 超出限制"

#: build/serializers.py:1010
msgid "Build output must be specified for allocation of tracked parts"
msgstr "對於被追蹤的零件的分配，必須指定生產產出"

#: build/serializers.py:1018
msgid "Build output cannot be specified for allocation of untracked parts"
msgstr "對於未被追蹤的零件，無法指定生產產出"

#: build/serializers.py:1042 order/serializers.py:1834
msgid "Allocation items must be provided"
msgstr "必須提供分配項目"

#: build/serializers.py:1106
msgid "Stock location where parts are to be sourced (leave blank to take from any location)"
msgstr "零件來源的庫存地點(留空則可來源於任何庫存地點)"

#: build/serializers.py:1115
msgid "Exclude Location"
msgstr "排除位置"

#: build/serializers.py:1116
msgid "Exclude stock items from this selected location"
msgstr "從該選定的庫存地點排除庫存項"

#: build/serializers.py:1121
msgid "Interchangeable Stock"
msgstr "可互換庫存"

#: build/serializers.py:1122
msgid "Stock items in multiple locations can be used interchangeably"
msgstr "在多個位置的庫存項目可以互換使用"

#: build/serializers.py:1127
msgid "Substitute Stock"
msgstr "替代品庫存"

#: build/serializers.py:1128
msgid "Allow allocation of substitute parts"
msgstr "允許分配可替換的零件"

#: build/serializers.py:1133
msgid "Optional Items"
msgstr "可選項目"

#: build/serializers.py:1134
msgid "Allocate optional BOM items to build order"
msgstr "分配可選的物料清單給生產訂單"

#: build/serializers.py:1156
msgid "Failed to start auto-allocation task"
msgstr "啓動自動分配任務失敗"

#: build/serializers.py:1230
msgid "BOM Reference"
msgstr "物料清單參考"

#: build/serializers.py:1236
msgid "BOM Part ID"
msgstr "物料清單零件識別號碼"

#: build/serializers.py:1243
msgid "BOM Part Name"
msgstr "物料清單零件名稱"

#: build/serializers.py:1278 build/serializers.py:1434
msgid "Build"
msgstr ""

#: build/serializers.py:1286 company/models.py:662 order/api.py:304
#: order/api.py:309 order/api.py:508 order/serializers.py:593
#: stock/models.py:959 stock/serializers.py:631
msgid "Supplier Part"
msgstr "供應商零件"

#: build/serializers.py:1294 stock/serializers.py:662
msgid "Allocated Quantity"
msgstr "已分配數量"

#: build/serializers.py:1371
msgid "Build Reference"
msgstr "構建參考"

#: build/serializers.py:1381
msgid "Part Category Name"
msgstr "零件類別名稱"

#: build/serializers.py:1401 common/setting/system.py:453 part/models.py:1223
msgid "Trackable"
msgstr "可追蹤"

#: build/serializers.py:1404
msgid "Inherited"
msgstr "已繼承的"

#: build/serializers.py:1407 part/models.py:4381
msgid "Allow Variants"
msgstr "允許變體"

#: build/serializers.py:1412 build/serializers.py:1416 part/models.py:4179
#: part/models.py:4657 stock/api.py:831
msgid "BOM Item"
msgstr "物料清單項"

#: build/serializers.py:1445
msgid "Allocated Stock"
msgstr "分配庫存"

#: build/serializers.py:1447 company/serializers.py:424
#: order/serializers.py:1271 part/serializers.py:943 part/serializers.py:1690
msgid "On Order"
msgstr "已訂購"

#: build/serializers.py:1449 order/serializers.py:1272 part/serializers.py:1694
msgid "In Production"
msgstr "生產中"

#: build/serializers.py:1451 part/serializers.py:964
msgid "External Stock"
msgstr "外部庫存"

#: build/serializers.py:1452 part/serializers.py:1723
msgid "Available Stock"
msgstr "可用庫存"

#: build/serializers.py:1454
msgid "Available Substitute Stock"
msgstr "可用的替代品庫存"

#: build/serializers.py:1457
msgid "Available Variant Stock"
msgstr "可用的變體庫存"

#: build/status_codes.py:11 generic/states/tests.py:21
#: generic/states/tests.py:131 order/status_codes.py:12
#: order/status_codes.py:44 order/status_codes.py:76 order/status_codes.py:102
msgid "Pending"
msgstr "待定"

#: build/status_codes.py:12
msgid "Production"
msgstr "生產"

#: build/status_codes.py:13 order/status_codes.py:14 order/status_codes.py:51
#: order/status_codes.py:81
msgid "On Hold"
msgstr "被掛起"

#: build/status_codes.py:14 order/status_codes.py:16 order/status_codes.py:53
#: order/status_codes.py:84
msgid "Cancelled"
msgstr "已取消"

#: build/status_codes.py:15 generic/states/tests.py:23 importer/models.py:529
#: importer/status_codes.py:27 order/status_codes.py:15
#: order/status_codes.py:52 order/status_codes.py:83
msgid "Complete"
msgstr "完成"

#: build/tasks.py:193
msgid "Stock required for build order"
msgstr "生產訂單所需庫存"

#: build/tasks.py:267
msgid "Overdue Build Order"
msgstr "逾期的生產訂單"

#: build/tasks.py:272
#, python-brace-format
msgid "Build order {bo} is now overdue"
msgstr "生產訂單 {bo} 現已逾期"

#: common/api.py:710
msgid "Is Link"
msgstr "是否鏈接"

#: common/api.py:718
msgid "Is File"
msgstr "是否為文件"

#: common/api.py:761
msgid "User does not have permission to delete these attachments"
msgstr "用户沒有權限刪除此附件"

#: common/api.py:778
msgid "User does not have permission to delete this attachment"
msgstr "用户沒有權限刪除此附件"

#: common/currency.py:120
msgid "Invalid currency code"
msgstr "無效的貨幣代碼"

#: common/currency.py:122
msgid "Duplicate currency code"
msgstr "重複的貨幣代碼"

#: common/currency.py:127
msgid "No valid currency codes provided"
msgstr "未提供有效的貨幣代碼"

#: common/currency.py:144
msgid "No plugin"
msgstr "暫無插件"

#: common/models.py:89
msgid "Updated"
msgstr "已是最新"

#: common/models.py:90
msgid "Timestamp of last update"
msgstr "最後更新時間戳"

#: common/models.py:117
msgid "Unique project code"
msgstr "唯一項目編碼"

#: common/models.py:124
msgid "Project description"
msgstr "項目描述"

#: common/models.py:133
msgid "User or group responsible for this project"
msgstr "負責此項目的用户或羣組"

#: common/models.py:721 common/models.py:1187 common/models.py:1225
msgid "Settings key"
msgstr ""

#: common/models.py:725
msgid "Settings value"
msgstr "設定值"

#: common/models.py:780
msgid "Chosen value is not a valid option"
msgstr "所選值不是一個有效的選項"

#: common/models.py:796
msgid "Value must be a boolean value"
msgstr "該值必須是布爾值"

#: common/models.py:804
msgid "Value must be an integer value"
msgstr "該值必須為整數"

#: common/models.py:812
msgid "Value must be a valid number"
msgstr ""

#: common/models.py:837
msgid "Value does not pass validation checks"
msgstr ""

#: common/models.py:859
msgid "Key string must be unique"
msgstr "鍵字符串必須是唯一的"

#: common/models.py:1233 common/models.py:1234 common/models.py:1338
#: common/models.py:1339 common/models.py:1584 common/models.py:1585
#: common/models.py:1915 common/models.py:1916 common/models.py:2297
#: importer/models.py:97 part/models.py:3376 part/models.py:3463
#: part/models.py:3537 part/models.py:3565 plugin/models.py:322
#: plugin/models.py:323 report/templates/report/inventree_test_report.html:105
#: users/models.py:130 users/models.py:503
msgid "User"
msgstr "使用者"

#: common/models.py:1256
msgid "Price break quantity"
msgstr "批發價數量"

#: common/models.py:1263 company/serializers.py:567 order/models.py:1632
#: order/models.py:2734
msgid "Price"
msgstr "價格"

#: common/models.py:1264
msgid "Unit price at specified quantity"
msgstr "指定數量的單位價格"

#: common/models.py:1315 common/models.py:1500
msgid "Endpoint"
msgstr "端點"

#: common/models.py:1316
msgid "Endpoint at which this webhook is received"
msgstr "接收此網絡鈎子的端點"

#: common/models.py:1326
msgid "Name for this webhook"
msgstr "此網絡鈎子的名稱"

#: common/models.py:1330 common/models.py:2156 common/models.py:2263
#: company/models.py:164 company/models.py:799 machine/models.py:39
#: part/models.py:1246 plugin/models.py:68 stock/api.py:591 users/models.py:201
#: users/models.py:556 users/serializers.py:333
msgid "Active"
msgstr "激活"

#: common/models.py:1330
msgid "Is this webhook active"
msgstr "網絡鈎子是否已啓用"

#: common/models.py:1346 users/models.py:178
msgid "Token"
msgstr "令牌"

#: common/models.py:1347
msgid "Token for access"
msgstr "訪問令牌"

#: common/models.py:1355
msgid "Secret"
msgstr "密鑰"

#: common/models.py:1356
msgid "Shared secret for HMAC"
msgstr "HMAC共享密鑰"

#: common/models.py:1464
msgid "Message ID"
msgstr "消息ID"

#: common/models.py:1465
msgid "Unique identifier for this message"
msgstr "此郵件的唯一標識符"

#: common/models.py:1473
msgid "Host"
msgstr "主機"

#: common/models.py:1474
msgid "Host from which this message was received"
msgstr "接收此消息的主機"

#: common/models.py:1482
msgid "Header"
msgstr "標題"

#: common/models.py:1483
msgid "Header of this message"
msgstr "此消息的標題"

#: common/models.py:1490
msgid "Body"
msgstr "正文"

#: common/models.py:1491
msgid "Body of this message"
msgstr "此消息的正文"

#: common/models.py:1501
msgid "Endpoint on which this message was received"
msgstr "接收此消息的終點"

#: common/models.py:1506
msgid "Worked on"
msgstr "工作於"

#: common/models.py:1507
msgid "Was the work on this message finished?"
msgstr "這條消息的工作完成了嗎？"

#: common/models.py:1633
msgid "Id"
msgstr "標識"

#: common/models.py:1635 part/serializers.py:281
msgid "Title"
msgstr "標題"

#: common/models.py:1637 common/models.py:1898 company/models.py:149
#: company/models.py:445 company/models.py:512 company/models.py:816
#: order/models.py:444 order/models.py:1585 order/models.py:2056
#: part/models.py:1113
#: report/templates/report/inventree_build_order_report.html:164
msgid "Link"
msgstr "連結"

#: common/models.py:1639
msgid "Published"
msgstr "已發佈"

#: common/models.py:1641
msgid "Author"
msgstr "作者"

#: common/models.py:1643
msgid "Summary"
msgstr "摘要"

#: common/models.py:1646
msgid "Read"
msgstr "閲讀"

#: common/models.py:1646
msgid "Was this news item read?"
msgstr "這條新聞被閲讀了嗎？"

#: common/models.py:1663 company/models.py:160 part/models.py:1124
#: report/templates/report/inventree_bill_of_materials_report.html:126
#: report/templates/report/inventree_bill_of_materials_report.html:148
#: report/templates/report/inventree_return_order_report.html:35
msgid "Image"
msgstr "圖像"

#: common/models.py:1663
msgid "Image file"
msgstr "圖像文件"

#: common/models.py:1675
msgid "Target model type for this image"
msgstr "此圖像的目標模型類型"

#: common/models.py:1679
msgid "Target model ID for this image"
msgstr "此圖像的目標型號ID"

#: common/models.py:1701
msgid "Custom Unit"
msgstr "自定義單位"

#: common/models.py:1719
msgid "Unit symbol must be unique"
msgstr "單位符號必須唯一"

#: common/models.py:1734
msgid "Unit name must be a valid identifier"
msgstr "單位名稱必須是有效的標識符"

#: common/models.py:1753
msgid "Unit name"
msgstr "單位名稱"

#: common/models.py:1760
msgid "Symbol"
msgstr "符號"

#: common/models.py:1761
msgid "Optional unit symbol"
msgstr "可選單位符號"

#: common/models.py:1767
msgid "Definition"
msgstr "定義"

#: common/models.py:1768
msgid "Unit definition"
msgstr "單位定義"

#: common/models.py:1826 common/models.py:1889 stock/models.py:2827
#: stock/serializers.py:258
msgid "Attachment"
msgstr "附件"

#: common/models.py:1843
msgid "Missing file"
msgstr "缺少檔案"

#: common/models.py:1844
msgid "Missing external link"
msgstr "缺少外部連結"

#: common/models.py:1881
msgid "Model type"
msgstr ""

#: common/models.py:1882
msgid "Target model type for image"
msgstr ""

#: common/models.py:1890
msgid "Select file to attach"
msgstr "選擇附件"

#: common/models.py:1906
msgid "Comment"
msgstr "註解"

#: common/models.py:1907
msgid "Attachment comment"
msgstr "附件評論"

#: common/models.py:1923
msgid "Upload date"
msgstr "上傳日期"

#: common/models.py:1924
msgid "Date the file was uploaded"
msgstr "上傳文件的日期"

#: common/models.py:1928
msgid "File size"
msgstr "文件大小"

#: common/models.py:1928
msgid "File size in bytes"
msgstr "文件大小，以字節為單位"

#: common/models.py:1966 common/serializers.py:637
msgid "Invalid model type specified for attachment"
msgstr "為附件指定的模型類型無效"

#: common/models.py:1987
msgid "Custom State"
msgstr "自定狀態"

#: common/models.py:1988
msgid "Custom States"
msgstr "定製狀態"

#: common/models.py:1993
msgid "Reference Status Set"
msgstr "參考狀態設定"

#: common/models.py:1994
msgid "Status set that is extended with this custom state"
msgstr "使用此自定義狀態擴展狀態的狀態集"

#: common/models.py:1998 generic/states/serializers.py:18
msgid "Logical Key"
msgstr "邏輯密鑰"

#: common/models.py:2000
msgid "State logical key that is equal to this custom state in business logic"
msgstr "等同於商業邏輯中自定義狀態的狀態邏輯鍵"

#: common/models.py:2005 common/models.py:2244 company/models.py:600
#: report/templates/report/inventree_test_report.html:104 stock/models.py:2819
msgid "Value"
msgstr "值"

#: common/models.py:2006
msgid "Numerical value that will be saved in the models database"
msgstr ""

#: common/models.py:2012
msgid "Name of the state"
msgstr "狀態名"

#: common/models.py:2021 common/models.py:2250 generic/states/serializers.py:22
#: part/serializers.py:283
msgid "Label"
msgstr "標籤"

#: common/models.py:2022
msgid "Label that will be displayed in the frontend"
msgstr "在前端顯示的標籤"

#: common/models.py:2029 generic/states/serializers.py:24
msgid "Color"
msgstr "顏色"

#: common/models.py:2030
msgid "Color that will be displayed in the frontend"
msgstr "將在前端顯示顏色"

#: common/models.py:2038 part/serializers.py:285
msgid "Model"
msgstr "模式"

#: common/models.py:2039
msgid "Model this state is associated with"
msgstr "該狀態關聯的模型"

#: common/models.py:2054
msgid "Model must be selected"
msgstr "必須選定模型"

#: common/models.py:2057
msgid "Key must be selected"
msgstr "必須選取密鑰"

#: common/models.py:2060
msgid "Logical key must be selected"
msgstr "必須選中邏輯密鑰"

#: common/models.py:2064
msgid "Key must be different from logical key"
msgstr "密鑰必須不同於邏輯密鑰"

#: common/models.py:2071
msgid "Valid reference status class must be provided"
msgstr ""

#: common/models.py:2077
msgid "Key must be different from the logical keys of the reference status"
msgstr "密鑰必須不同於參考狀態的邏輯密鑰"

#: common/models.py:2084
msgid "Logical key must be in the logical keys of the reference status"
msgstr "邏輯密鑰必須在參考狀態的邏輯鍵中"

#: common/models.py:2091
msgid "Name must be different from the names of the reference status"
msgstr ""

#: common/models.py:2131 common/models.py:2238 part/models.py:3876
msgid "Selection List"
msgstr ""

#: common/models.py:2132
msgid "Selection Lists"
msgstr ""

#: common/models.py:2137
msgid "Name of the selection list"
msgstr ""

#: common/models.py:2144
msgid "Description of the selection list"
msgstr ""

#: common/models.py:2150 part/models.py:1251
msgid "Locked"
msgstr "已鎖定"

#: common/models.py:2151
msgid "Is this selection list locked?"
msgstr ""

#: common/models.py:2157
msgid "Can this selection list be used?"
msgstr ""

#: common/models.py:2165
msgid "Source Plugin"
msgstr ""

#: common/models.py:2166
msgid "Plugin which provides the selection list"
msgstr ""

#: common/models.py:2171
msgid "Source String"
msgstr ""

#: common/models.py:2172
msgid "Optional string identifying the source used for this list"
msgstr ""

#: common/models.py:2181
msgid "Default Entry"
msgstr ""

#: common/models.py:2182
msgid "Default entry for this selection list"
msgstr ""

#: common/models.py:2187
msgid "Created"
msgstr "已創建"

#: common/models.py:2188
msgid "Date and time that the selection list was created"
msgstr ""

#: common/models.py:2193
msgid "Last Updated"
msgstr "最近更新"

#: common/models.py:2194
msgid "Date and time that the selection list was last updated"
msgstr ""

#: common/models.py:2228
msgid "Selection List Entry"
msgstr ""

#: common/models.py:2229
msgid "Selection List Entries"
msgstr ""

#: common/models.py:2239
msgid "Selection list to which this entry belongs"
msgstr ""

#: common/models.py:2245
msgid "Value of the selection list entry"
msgstr ""

#: common/models.py:2251
msgid "Label for the selection list entry"
msgstr ""

#: common/models.py:2257
msgid "Description of the selection list entry"
msgstr ""

#: common/models.py:2264
msgid "Is this selection list entry active?"
msgstr ""

#: common/models.py:2282
msgid "Barcode Scan"
msgstr "掃描條碼"

#: common/models.py:2286 importer/models.py:523 part/models.py:4041
msgid "Data"
msgstr "數據"

#: common/models.py:2287
msgid "Barcode data"
msgstr "條碼數據"

#: common/models.py:2298
msgid "User who scanned the barcode"
msgstr "掃描條碼"

#: common/models.py:2303 importer/models.py:66
msgid "Timestamp"
msgstr "時間戳"

#: common/models.py:2304
msgid "Date and time of the barcode scan"
msgstr "掃描條碼的日期和時間"

#: common/models.py:2310
msgid "URL endpoint which processed the barcode"
msgstr "處理條碼的 URL 終點"

#: common/models.py:2317 order/models.py:1622 plugin/serializers.py:93
msgid "Context"
msgstr "上下文"

#: common/models.py:2318
msgid "Context data for the barcode scan"
msgstr "掃描條碼的上下文數據"

#: common/models.py:2325
msgid "Response"
msgstr "響應"

#: common/models.py:2326
msgid "Response data from the barcode scan"
msgstr "掃描條碼的響應數據"

#: common/models.py:2332 report/templates/report/inventree_test_report.html:103
#: stock/models.py:2813
msgid "Result"
msgstr "結果"

#: common/models.py:2333
msgid "Was the barcode scan successful?"
msgstr "條碼掃描成功嗎？"

#: common/notifications.py:332
#, python-brace-format
msgid "New {verbose_name}"
msgstr "新建{verbose_name}"

#: common/notifications.py:334
msgid "A new order has been created and assigned to you"
msgstr "新訂單已創建並分配給您"

#: common/notifications.py:340
#, python-brace-format
msgid "{verbose_name} canceled"
msgstr "{verbose_name} 已取消"

#: common/notifications.py:342
msgid "A order that is assigned to you was canceled"
msgstr "分配給您的訂單已取消"

#: common/notifications.py:348 common/notifications.py:355 order/api.py:559
msgid "Items Received"
msgstr "收到的物品"

#: common/notifications.py:350
msgid "Items have been received against a purchase order"
msgstr "已根據採購訂單收到物品"

#: common/notifications.py:357
msgid "Items have been received against a return order"
msgstr "已收到退貨訂單中的物品"

#: common/notifications.py:505
msgid "Error raised by plugin"
msgstr "插件引發的錯誤"

#: common/serializers.py:451
msgid "Is Running"
msgstr "正在運行"

#: common/serializers.py:457
msgid "Pending Tasks"
msgstr "等待完成的任務"

#: common/serializers.py:463
msgid "Scheduled Tasks"
msgstr "預定的任務"

#: common/serializers.py:469
msgid "Failed Tasks"
msgstr "失敗的任務"

#: common/serializers.py:484
msgid "Task ID"
msgstr "任務ID"

#: common/serializers.py:484
msgid "Unique task ID"
msgstr "唯一任務ID"

#: common/serializers.py:486
msgid "Lock"
msgstr "鎖定"

#: common/serializers.py:486
msgid "Lock time"
msgstr "鎖定時間"

#: common/serializers.py:488
msgid "Task name"
msgstr "任務名稱"

#: common/serializers.py:490
msgid "Function"
msgstr "功能"

#: common/serializers.py:490
msgid "Function name"
msgstr "功能名稱"

#: common/serializers.py:492
msgid "Arguments"
msgstr "參數"

#: common/serializers.py:492
msgid "Task arguments"
msgstr "任務參數"

#: common/serializers.py:495
msgid "Keyword Arguments"
msgstr "關鍵字參數"

#: common/serializers.py:495
msgid "Task keyword arguments"
msgstr "任務關鍵詞參數"

#: common/serializers.py:605
msgid "Filename"
msgstr "檔案名稱"

#: common/serializers.py:612 importer/models.py:86 report/api.py:41
#: report/models.py:277 report/serializers.py:53
msgid "Model Type"
msgstr "模型類型"

#: common/serializers.py:640
msgid "User does not have permission to create or edit attachments for this model"
msgstr "用户無權為此模式創建或編輯附件"

#: common/serializers.py:684 common/serializers.py:787
msgid "Selection list is locked"
msgstr ""

#: common/setting/system.py:97
msgid "No group"
msgstr "無分組"

#: common/setting/system.py:156
msgid "Site URL is locked by configuration"
msgstr "網站 URL 已配置為鎖定"

#: common/setting/system.py:167
msgid "Restart required"
msgstr "需要重啓"

#: common/setting/system.py:168
msgid "A setting has been changed which requires a server restart"
msgstr "設置已更改，需要服務器重啓"

#: common/setting/system.py:174
msgid "Pending migrations"
msgstr "等待遷移"

#: common/setting/system.py:175
msgid "Number of pending database migrations"
msgstr "待處理的數據庫遷移數"

#: common/setting/system.py:180
msgid "Instance ID"
msgstr ""

#: common/setting/system.py:181
msgid "Unique identifier for this InvenTree instance"
msgstr ""

#: common/setting/system.py:186
msgid "Announce ID"
msgstr ""

#: common/setting/system.py:188
msgid "Announce the instance ID of the server in the server status info (unauthenticated)"
msgstr ""

#: common/setting/system.py:194
msgid "Server Instance Name"
msgstr "服務器實例名稱"

#: common/setting/system.py:196
msgid "String descriptor for the server instance"
msgstr "服務器實例的字符串描述符"

#: common/setting/system.py:200
msgid "Use instance name"
msgstr "使用實例名稱"

#: common/setting/system.py:201
msgid "Use the instance name in the title-bar"
msgstr "在標題欄中使用實例名稱"

#: common/setting/system.py:206
msgid "Restrict showing `about`"
msgstr "限制顯示 `關於` 信息"

#: common/setting/system.py:207
msgid "Show the `about` modal only to superusers"
msgstr "只向超級管理員顯示關於信息"

#: common/setting/system.py:212 company/models.py:108 company/models.py:109
msgid "Company name"
msgstr "公司名稱"

#: common/setting/system.py:213
msgid "Internal company name"
msgstr "內部公司名稱"

#: common/setting/system.py:217
msgid "Base URL"
msgstr "基本 URL"

#: common/setting/system.py:218
msgid "Base URL for server instance"
msgstr "服務器實例的基準 URL"

#: common/setting/system.py:224
msgid "Default Currency"
msgstr "默認貨幣單位"

#: common/setting/system.py:225
msgid "Select base currency for pricing calculations"
msgstr "選擇價格計算的默認貨幣"

#: common/setting/system.py:231
msgid "Supported Currencies"
msgstr "支持幣種"

#: common/setting/system.py:232
msgid "List of supported currency codes"
msgstr "支持的貨幣代碼列表"

#: common/setting/system.py:238
msgid "Currency Update Interval"
msgstr "貨幣更新間隔時間"

#: common/setting/system.py:239
msgid "How often to update exchange rates (set to zero to disable)"
msgstr "檢查更新的頻率(設置為零以禁用)"

#: common/setting/system.py:241 common/setting/system.py:281
#: common/setting/system.py:294 common/setting/system.py:302
#: common/setting/system.py:309 common/setting/system.py:318
#: common/setting/system.py:567 common/setting/system.py:587
#: common/setting/system.py:684 common/setting/system.py:1068
msgid "days"
msgstr "天"

#: common/setting/system.py:245
msgid "Currency Update Plugin"
msgstr "幣種更新插件"

#: common/setting/system.py:246
msgid "Currency update plugin to use"
msgstr "使用貨幣更新插件"

#: common/setting/system.py:251
msgid "Download from URL"
msgstr "從URL下載"

#: common/setting/system.py:252
msgid "Allow download of remote images and files from external URL"
msgstr "允許從外部 URL 下載遠程圖片和文件"

#: common/setting/system.py:257
msgid "Download Size Limit"
msgstr "下載大小限制"

#: common/setting/system.py:258
msgid "Maximum allowable download size for remote image"
msgstr "遠程圖片的最大允許下載大小"

#: common/setting/system.py:264
msgid "User-agent used to download from URL"
msgstr "用於從 URL 下載的 User-agent"

#: common/setting/system.py:266
msgid "Allow to override the user-agent used to download images and files from external URL (leave blank for the default)"
msgstr "允許覆蓋用於從外部 URL 下載圖片和文件的 user-agent(留空為默認值)"

#: common/setting/system.py:271
msgid "Strict URL Validation"
msgstr "嚴格的 URL 驗證"

#: common/setting/system.py:272
msgid "Require schema specification when validating URLs"
msgstr "驗證 URL 時需要 schema 規範"

#: common/setting/system.py:277
msgid "Update Check Interval"
msgstr "更新檢查間隔"

#: common/setting/system.py:278
msgid "How often to check for updates (set to zero to disable)"
msgstr "檢查更新的頻率(設置為零以禁用)"

#: common/setting/system.py:284
msgid "Automatic Backup"
msgstr "自動備份"

#: common/setting/system.py:285
msgid "Enable automatic backup of database and media files"
msgstr "啟動資料庫和媒體文件自動備份"

#: common/setting/system.py:290
msgid "Auto Backup Interval"
msgstr "自動備份間隔"

#: common/setting/system.py:291
msgid "Specify number of days between automated backup events"
msgstr "指定自動備份之間的間隔天數"

#: common/setting/system.py:297
msgid "Task Deletion Interval"
msgstr "任務刪除間隔"

#: common/setting/system.py:299
msgid "Background task results will be deleted after specified number of days"
msgstr "後台任務結果將在指定天數後刪除"

#: common/setting/system.py:306
msgid "Error Log Deletion Interval"
msgstr "錯誤日誌刪除間隔"

#: common/setting/system.py:307
msgid "Error logs will be deleted after specified number of days"
msgstr "錯誤日誌將在指定天數後被刪除"

#: common/setting/system.py:313
msgid "Notification Deletion Interval"
msgstr "通知刪除間隔"

#: common/setting/system.py:315
msgid "User notifications will be deleted after specified number of days"
msgstr "用户通知將在指定天數後被刪除"

#: common/setting/system.py:322
msgid "Barcode Support"
msgstr "條形碼支持"

#: common/setting/system.py:323
msgid "Enable barcode scanner support in the web interface"
msgstr "在網頁界面啓用條形碼掃描器支持"

#: common/setting/system.py:328
msgid "Store Barcode Results"
msgstr "存儲條碼結果"

#: common/setting/system.py:329
msgid "Store barcode scan results in the database"
msgstr "存儲條碼掃描結果"

#: common/setting/system.py:334
msgid "Barcode Scans Maximum Count"
msgstr "條碼掃描最大計數"

#: common/setting/system.py:335
msgid "Maximum number of barcode scan results to store"
msgstr "存儲條碼掃描結果的最大數量"

#: common/setting/system.py:340
msgid "Barcode Input Delay"
msgstr "條形碼掃描延遲設置"

#: common/setting/system.py:341
msgid "Barcode input processing delay time"
msgstr "條形碼輸入處理延遲時間"

#: common/setting/system.py:347
msgid "Barcode Webcam Support"
msgstr "條碼攝像頭支持"

#: common/setting/system.py:348
msgid "Allow barcode scanning via webcam in browser"
msgstr "允許通過網絡攝像頭掃描條形碼"

#: common/setting/system.py:353
msgid "Barcode Show Data"
msgstr "條形碼顯示數據"

#: common/setting/system.py:354
msgid "Display barcode data in browser as text"
msgstr "在瀏覽器中將條形碼數據顯示為文本"

#: common/setting/system.py:359
msgid "Barcode Generation Plugin"
msgstr "條形碼生成插件"

#: common/setting/system.py:360
msgid "Plugin to use for internal barcode data generation"
msgstr "用於內部條形碼數據生成的插件"

#: common/setting/system.py:365
msgid "Part Revisions"
msgstr "零件修訂"

#: common/setting/system.py:366
msgid "Enable revision field for Part"
msgstr "啓用零件修訂字段"

#: common/setting/system.py:371
msgid "Assembly Revision Only"
msgstr "僅限裝配修訂版本"

#: common/setting/system.py:372
msgid "Only allow revisions for assembly parts"
msgstr "僅允許對裝配零件進行修訂"

#: common/setting/system.py:377
msgid "Allow Deletion from Assembly"
msgstr "允許從裝配中刪除"

#: common/setting/system.py:378
msgid "Allow deletion of parts which are used in an assembly"
msgstr "允許刪除已在裝配中使用的零件"

#: common/setting/system.py:383
msgid "IPN Regex"
msgstr "IPN 內部零件號"

#: common/setting/system.py:384
msgid "Regular expression pattern for matching Part IPN"
msgstr "匹配零件 IPN（內部零件號）的正則表達式模式"

#: common/setting/system.py:387
msgid "Allow Duplicate IPN"
msgstr "允許重複的 IPN（內部零件號）"

#: common/setting/system.py:388
msgid "Allow multiple parts to share the same IPN"
msgstr "允許多個零件共享相同的 IPN（內部零件號）"

#: common/setting/system.py:393
msgid "Allow Editing IPN"
msgstr "允許編輯 IPN（內部零件號）"

#: common/setting/system.py:394
msgid "Allow changing the IPN value while editing a part"
msgstr "允許編輯零件時更改內部零件號"

#: common/setting/system.py:399
msgid "Copy Part BOM Data"
msgstr "複製零件物料清單數據"

#: common/setting/system.py:400
msgid "Copy BOM data by default when duplicating a part"
msgstr "複製零件時默認複製物料清單數據"

#: common/setting/system.py:405
msgid "Copy Part Parameter Data"
msgstr "複製零件參數數據"

#: common/setting/system.py:406
msgid "Copy parameter data by default when duplicating a part"
msgstr "複製零件時默認複製參數數據"

#: common/setting/system.py:411
msgid "Copy Part Test Data"
msgstr "複製零件測試數據"

#: common/setting/system.py:412
msgid "Copy test data by default when duplicating a part"
msgstr "複製零件時默認複製測試數據"

#: common/setting/system.py:417
msgid "Copy Category Parameter Templates"
msgstr "複製類別參數模板"

#: common/setting/system.py:418
msgid "Copy category parameter templates when creating a part"
msgstr "創建零件時複製類別參數模板"

#: common/setting/system.py:423 part/models.py:4035 report/models.py:357
#: report/models.py:563 report/serializers.py:90 report/serializers.py:131
#: stock/serializers.py:247
msgid "Template"
msgstr "模板"

#: common/setting/system.py:424
msgid "Parts are templates by default"
msgstr "零件默認為模板"

#: common/setting/system.py:430
msgid "Parts can be assembled from other components by default"
msgstr "默認情況下，元件可由其他零件組裝而成"

#: common/setting/system.py:435 part/models.py:1217 part/serializers.py:1677
#: part/serializers.py:1683
msgid "Component"
msgstr "組件"

#: common/setting/system.py:436
msgid "Parts can be used as sub-components by default"
msgstr "默認情況下，零件可用作子部件"

#: common/setting/system.py:441 part/models.py:1235
msgid "Purchaseable"
msgstr "可購買"

#: common/setting/system.py:442
msgid "Parts are purchaseable by default"
msgstr "默認情況下可購買零件"

#: common/setting/system.py:447 part/models.py:1241 stock/api.py:592
msgid "Salable"
msgstr "可銷售"

#: common/setting/system.py:448
msgid "Parts are salable by default"
msgstr "零件默認為可銷售"

#: common/setting/system.py:454
msgid "Parts are trackable by default"
msgstr "默認情況下可跟蹤零件"

#: common/setting/system.py:459 part/models.py:1257
msgid "Virtual"
msgstr "虛擬的"

#: common/setting/system.py:460
msgid "Parts are virtual by default"
msgstr "默認情況下，零件是虛擬的"

#: common/setting/system.py:465
msgid "Show Import in Views"
msgstr "在視圖中顯示導入"

#: common/setting/system.py:466
msgid "Display the import wizard in some part views"
msgstr "在某些零件視圖中顯示導入嚮導"

#: common/setting/system.py:471
msgid "Show related parts"
msgstr "顯示相關零件"

#: common/setting/system.py:472
msgid "Display related parts for a part"
msgstr "顯示零件的相關零件"

#: common/setting/system.py:477
msgid "Initial Stock Data"
msgstr "初始庫存數據"

#: common/setting/system.py:478
msgid "Allow creation of initial stock when adding a new part"
msgstr "允許在添加新零件時創建初始庫存"

#: common/setting/system.py:483
msgid "Initial Supplier Data"
msgstr "初始供應商數據"

#: common/setting/system.py:485
msgid "Allow creation of initial supplier data when adding a new part"
msgstr "允許在添加新零件時創建初始供應商數據"

#: common/setting/system.py:491
msgid "Part Name Display Format"
msgstr "零件名稱顯示格式"

#: common/setting/system.py:492
msgid "Format to display the part name"
msgstr "顯示零件名稱的格式"

#: common/setting/system.py:498
msgid "Part Category Default Icon"
msgstr "零件類別默認圖標"

#: common/setting/system.py:499
msgid "Part category default icon (empty means no icon)"
msgstr "零件類別默認圖標 (空表示沒有圖標)"

#: common/setting/system.py:504
msgid "Enforce Parameter Units"
msgstr "強制參數單位"

#: common/setting/system.py:506
msgid "If units are provided, parameter values must match the specified units"
msgstr "如果提供了單位，參數值必須與指定的單位匹配"

#: common/setting/system.py:512
msgid "Minimum Pricing Decimal Places"
msgstr "最小定價小數位數"

#: common/setting/system.py:514
msgid "Minimum number of decimal places to display when rendering pricing data"
msgstr "呈現定價數據時顯示的最小小數位數"

#: common/setting/system.py:525
msgid "Maximum Pricing Decimal Places"
msgstr "最大定價小數位數"

#: common/setting/system.py:527
msgid "Maximum number of decimal places to display when rendering pricing data"
msgstr "呈現定價數據時顯示的最大小數位數"

#: common/setting/system.py:538
msgid "Use Supplier Pricing"
msgstr "使用供應商定價"

#: common/setting/system.py:540
msgid "Include supplier price breaks in overall pricing calculations"
msgstr "將供應商的價批發價納入總體定價計算中"

#: common/setting/system.py:546
msgid "Purchase History Override"
msgstr "購買歷史記錄覆蓋"

#: common/setting/system.py:548
msgid "Historical purchase order pricing overrides supplier price breaks"
msgstr "歷史採購訂單定價優先於供應商批發價"

#: common/setting/system.py:554
msgid "Use Stock Item Pricing"
msgstr "使用庫存項定價"

#: common/setting/system.py:556
msgid "Use pricing from manually entered stock data for pricing calculations"
msgstr "使用手動輸入的庫存數據進行定價計算"

#: common/setting/system.py:562
msgid "Stock Item Pricing Age"
msgstr "庫存項目定價時間"

#: common/setting/system.py:564
msgid "Exclude stock items older than this number of days from pricing calculations"
msgstr "從定價計算中排除超過此天數的庫存項目"

#: common/setting/system.py:571
msgid "Use Variant Pricing"
msgstr "使用變體定價"

#: common/setting/system.py:572
msgid "Include variant pricing in overall pricing calculations"
msgstr "在整體定價計算中包括變體定價"

#: common/setting/system.py:577
msgid "Active Variants Only"
msgstr "僅限活躍變體"

#: common/setting/system.py:579
msgid "Only use active variant parts for calculating variant pricing"
msgstr "僅使用活躍變體零件計算變體價格"

#: common/setting/system.py:585
msgid "Pricing Rebuild Interval"
msgstr "價格重建間隔"

#: common/setting/system.py:586
msgid "Number of days before part pricing is automatically updated"
msgstr "零件價格自動更新前的天數"

#: common/setting/system.py:592
msgid "Internal Prices"
msgstr "內部價格"

#: common/setting/system.py:593
msgid "Enable internal prices for parts"
msgstr "啓用內部零件價格"

#: common/setting/system.py:598
msgid "Internal Price Override"
msgstr "覆蓋內部價格"

#: common/setting/system.py:600
msgid "If available, internal prices override price range calculations"
msgstr "如果有內部價格，內部價格將覆蓋價格範圍計算"

#: common/setting/system.py:606
msgid "Enable label printing"
msgstr "啓用標籤打印功能"

#: common/setting/system.py:607
msgid "Enable label printing from the web interface"
msgstr "啓用從網絡界面打印標籤"

#: common/setting/system.py:612
msgid "Label Image DPI"
msgstr "標籤圖片 DPI"

#: common/setting/system.py:614
msgid "DPI resolution when generating image files to supply to label printing plugins"
msgstr "生成圖像文件以供標籤打印插件使用時的 DPI 分辨率"

#: common/setting/system.py:620
msgid "Enable Reports"
msgstr "啓用報告"

#: common/setting/system.py:621
msgid "Enable generation of reports"
msgstr "啓用報告生成"

#: common/setting/system.py:626
msgid "Debug Mode"
msgstr "調試模式"

#: common/setting/system.py:627
msgid "Generate reports in debug mode (HTML output)"
msgstr "以調試模式生成報告（HTML 輸出）"

#: common/setting/system.py:632
msgid "Log Report Errors"
msgstr "日誌錯誤報告"

#: common/setting/system.py:633
msgid "Log errors which occur when generating reports"
msgstr "記錄生成報告時出現的錯誤"

#: common/setting/system.py:638 plugin/builtin/labels/label_sheet.py:29
#: report/models.py:365
msgid "Page Size"
msgstr "頁面大小"

#: common/setting/system.py:639
msgid "Default page size for PDF reports"
msgstr "PDF 報告默認頁面大小"

#: common/setting/system.py:644
msgid "Globally Unique Serials"
msgstr "全局唯一序列號"

#: common/setting/system.py:645
msgid "Serial numbers for stock items must be globally unique"
msgstr "庫存項的序列號必須全局唯一"

#: common/setting/system.py:650
msgid "Autofill Serial Numbers"
msgstr "自動填充序列號"

#: common/setting/system.py:651
msgid "Autofill serial numbers in forms"
msgstr "在表格中自動填充序列號"

#: common/setting/system.py:656
msgid "Delete Depleted Stock"
msgstr "刪除已耗盡的庫存"

#: common/setting/system.py:657
msgid "Determines default behavior when a stock item is depleted"
msgstr "設置庫存耗盡時的默認行為"

#: common/setting/system.py:662
msgid "Batch Code Template"
msgstr "批號模板"

#: common/setting/system.py:663
msgid "Template for generating default batch codes for stock items"
msgstr "為庫存項生成默認批號的模板"

#: common/setting/system.py:667
msgid "Stock Expiry"
msgstr "庫存過期"

#: common/setting/system.py:668
msgid "Enable stock expiry functionality"
msgstr "啓用庫存過期功能"

#: common/setting/system.py:673
msgid "Sell Expired Stock"
msgstr "銷售過期庫存"

#: common/setting/system.py:674
msgid "Allow sale of expired stock"
msgstr "允許銷售過期庫存"

#: common/setting/system.py:679
msgid "Stock Stale Time"
msgstr "庫存過期時間"

#: common/setting/system.py:681
msgid "Number of days stock items are considered stale before expiring"
msgstr "庫存項在到期前被視為過期的天數"

#: common/setting/system.py:688
msgid "Build Expired Stock"
msgstr "生產過期庫存"

#: common/setting/system.py:689
msgid "Allow building with expired stock"
msgstr "允許用過期的庫存生產"

#: common/setting/system.py:694
msgid "Stock Ownership Control"
msgstr "庫存所有權控制"

#: common/setting/system.py:695
msgid "Enable ownership control over stock locations and items"
msgstr "啓用庫存地點和項目的所有權控制"

#: common/setting/system.py:700
msgid "Stock Location Default Icon"
msgstr "庫存地點默認圖標"

#: common/setting/system.py:701
msgid "Stock location default icon (empty means no icon)"
msgstr "庫存地點默認圖標 (空表示沒有圖標)"

#: common/setting/system.py:706
msgid "Show Installed Stock Items"
msgstr "顯示已安裝的庫存項"

#: common/setting/system.py:707
msgid "Display installed stock items in stock tables"
msgstr "在庫存表中顯示已安裝的庫存項"

#: common/setting/system.py:712
msgid "Check BOM when installing items"
msgstr "在安裝項目時檢查物料清單"

#: common/setting/system.py:714
msgid "Installed stock items must exist in the BOM for the parent part"
msgstr "已安裝的庫存項目必須存在於上級零件的物料清單中"

#: common/setting/system.py:720
msgid "Allow Out of Stock Transfer"
msgstr "允許超出庫存轉移"

#: common/setting/system.py:722
msgid "Allow stock items which are not in stock to be transferred between stock locations"
msgstr "允許非庫存的庫存項目在庫存位置之間轉移"

#: common/setting/system.py:728
msgid "Build Order Reference Pattern"
msgstr "生產訂單參考模式"

#: common/setting/system.py:729
msgid "Required pattern for generating Build Order reference field"
msgstr "生成生產訂單參考字段所需的模式"

#: common/setting/system.py:734 common/setting/system.py:788
#: common/setting/system.py:808 common/setting/system.py:844
msgid "Require Responsible Owner"
msgstr "要求負責人"

#: common/setting/system.py:735 common/setting/system.py:789
#: common/setting/system.py:809 common/setting/system.py:845
msgid "A responsible owner must be assigned to each order"
msgstr "必須為每個訂單分配一個負責人"

#: common/setting/system.py:740
msgid "Require Active Part"
msgstr "需要活動零件"

#: common/setting/system.py:741
msgid "Prevent build order creation for inactive parts"
msgstr "防止為非活動零件創建生產訂單"

#: common/setting/system.py:746
msgid "Require Locked Part"
msgstr "需要鎖定零件"

#: common/setting/system.py:747
msgid "Prevent build order creation for unlocked parts"
msgstr "防止為未鎖定的零件創建生產訂單"

#: common/setting/system.py:752
msgid "Require Valid BOM"
msgstr "需要有效的物料清單"

#: common/setting/system.py:753
msgid "Prevent build order creation unless BOM has been validated"
msgstr "除非物料清單已驗證，否則禁止創建生產訂單"

#: common/setting/system.py:758
msgid "Require Closed Child Orders"
msgstr "需要關閉子訂單"

#: common/setting/system.py:760
msgid "Prevent build order completion until all child orders are closed"
msgstr "在所有子訂單關閉之前，阻止生產訂單的完成"

#: common/setting/system.py:766
msgid "Block Until Tests Pass"
msgstr "阻止直到測試通過"

#: common/setting/system.py:768
msgid "Prevent build outputs from being completed until all required tests pass"
msgstr "在所有必要的測試通過之前，阻止產出完成"

#: common/setting/system.py:774
msgid "Enable Return Orders"
msgstr "啓用訂單退貨"

#: common/setting/system.py:775
msgid "Enable return order functionality in the user interface"
msgstr "在用户界面中啓用訂單退貨功能"

#: common/setting/system.py:780
msgid "Return Order Reference Pattern"
msgstr "退貨訂單參考模式"

#: common/setting/system.py:782
msgid "Required pattern for generating Return Order reference field"
msgstr "生成退貨訂單參考字段所需的模式"

#: common/setting/system.py:794
msgid "Edit Completed Return Orders"
msgstr "編輯已完成的退貨訂單"

#: common/setting/system.py:796
msgid "Allow editing of return orders after they have been completed"
msgstr "允許編輯已完成的退貨訂單"

#: common/setting/system.py:802
msgid "Sales Order Reference Pattern"
msgstr "銷售訂單參考模式"

#: common/setting/system.py:803
msgid "Required pattern for generating Sales Order reference field"
msgstr "生成銷售訂單參考字段所需參照模式"

#: common/setting/system.py:814
msgid "Sales Order Default Shipment"
msgstr "銷售訂單默認配送方式"

#: common/setting/system.py:815
msgid "Enable creation of default shipment with sales orders"
msgstr "啓用創建銷售訂單的默認配送功能"

#: common/setting/system.py:820
msgid "Edit Completed Sales Orders"
msgstr "編輯已完成的銷售訂單"

#: common/setting/system.py:822
msgid "Allow editing of sales orders after they have been shipped or completed"
msgstr "允許在訂單配送或完成後編輯銷售訂單"

#: common/setting/system.py:828
msgid "Mark Shipped Orders as Complete"
msgstr "標記該訂單為已完成？"

#: common/setting/system.py:830
msgid "Sales orders marked as shipped will automatically be completed, bypassing the \"shipped\" status"
msgstr "標記為已發貨的銷售訂單將自動完成，繞過“已發貨”狀態"

#: common/setting/system.py:836
msgid "Purchase Order Reference Pattern"
msgstr "採購訂單參考模式"

#: common/setting/system.py:838
msgid "Required pattern for generating Purchase Order reference field"
msgstr "生成採購訂單參考字段所需的模式"

#: common/setting/system.py:850
msgid "Edit Completed Purchase Orders"
msgstr "編輯已完成的採購訂單"

#: common/setting/system.py:852
msgid "Allow editing of purchase orders after they have been shipped or completed"
msgstr "允許在採購訂單已配送或完成後編輯訂單"

#: common/setting/system.py:858
msgid "Convert Currency"
msgstr ""

#: common/setting/system.py:859
msgid "Convert item value to base currency when receiving stock"
msgstr ""

#: common/setting/system.py:864
msgid "Auto Complete Purchase Orders"
msgstr "自動完成採購訂單"

#: common/setting/system.py:866
msgid "Automatically mark purchase orders as complete when all line items are received"
msgstr "當收到所有行項目時，自動將採購訂單標記為完成"

#: common/setting/system.py:873
msgid "Enable password forgot"
msgstr "忘記啓用密碼"

#: common/setting/system.py:874
msgid "Enable password forgot function on the login pages"
msgstr "在登錄頁面上啓用忘記密碼功能"

#: common/setting/system.py:879
msgid "Enable registration"
msgstr "啓用註冊"

#: common/setting/system.py:880
msgid "Enable self-registration for users on the login pages"
msgstr "在登錄頁面為用户啓用自行註冊功能"

#: common/setting/system.py:885
msgid "Enable SSO"
msgstr "啓用單點登錄"

#: common/setting/system.py:886
msgid "Enable SSO on the login pages"
msgstr "在登錄界面啓用單點登錄"

#: common/setting/system.py:891
msgid "Enable SSO registration"
msgstr "啓用單點登錄註冊"

#: common/setting/system.py:893
msgid "Enable self-registration via SSO for users on the login pages"
msgstr "允許登錄頁面上的用户通過 SSO 進行自我註冊"

#: common/setting/system.py:899
msgid "Enable SSO group sync"
msgstr "啓用單點登錄羣組同步"

#: common/setting/system.py:901
msgid "Enable synchronizing InvenTree groups with groups provided by the IdP"
msgstr "啓用庫存管理系統組和由身份提供者提供的組的同步功能"

#: common/setting/system.py:907
msgid "SSO group key"
msgstr "單點登錄系統組密鑰"

#: common/setting/system.py:908
msgid "The name of the groups claim attribute provided by the IdP"
msgstr "由身份提供者提供的組聲明屬性名稱"

#: common/setting/system.py:913
msgid "SSO group map"
msgstr "單點登錄系統組地圖"

#: common/setting/system.py:915
msgid "A mapping from SSO groups to local InvenTree groups. If the local group does not exist, it will be created."
msgstr "從單點登錄系統組組到本地庫存管理系統組的映射。如果本地組不存在，它將被創建。"

#: common/setting/system.py:921
msgid "Remove groups outside of SSO"
msgstr "移除單點登錄系統以外的羣組"

#: common/setting/system.py:923
msgid "Whether groups assigned to the user should be removed if they are not backend by the IdP. Disabling this setting might cause security issues"
msgstr "如果分配給用户的組不是身份提供者的後端，是否應該刪除它們。禁用此設置可能會造成安全問題"

#: common/setting/system.py:929
msgid "Email required"
msgstr "需要郵箱地址"

#: common/setting/system.py:930
msgid "Require user to supply mail on signup"
msgstr "要求用户在註冊時提供郵件"

#: common/setting/system.py:935
msgid "Auto-fill SSO users"
msgstr "自動填充單點登錄系統用户"

#: common/setting/system.py:936
msgid "Automatically fill out user-details from SSO account-data"
msgstr "自動使用單點登錄系統賬户的數據填寫用户詳細信息"

#: common/setting/system.py:941
msgid "Mail twice"
msgstr "發兩次郵件"

#: common/setting/system.py:942
msgid "On signup ask users twice for their mail"
msgstr "註冊時詢問用户他們的電子郵件兩次"

#: common/setting/system.py:947
msgid "Password twice"
msgstr "兩次輸入密碼"

#: common/setting/system.py:948
msgid "On signup ask users twice for their password"
msgstr "當註冊時請用户輸入密碼兩次"

#: common/setting/system.py:953
msgid "Allowed domains"
msgstr "域名白名單"

#: common/setting/system.py:955
msgid "Restrict signup to certain domains (comma-separated, starting with @)"
msgstr "限制註冊到某些域名 (逗號分隔，以 @ 開頭)"

#: common/setting/system.py:961
msgid "Group on signup"
msgstr "註冊羣組"

#: common/setting/system.py:963
msgid "Group to which new users are assigned on registration. If SSO group sync is enabled, this group is only set if no group can be assigned from the IdP."
msgstr "註冊時分配給新用户的組。 如果啓用了單點登錄系統羣組同步，此羣組僅在無法從 IdP 分配任何羣組的情況下才被設置。"

#: common/setting/system.py:969
msgid "Enforce MFA"
msgstr "強制啓用多因素安全認證"

#: common/setting/system.py:970
msgid "Users must use multifactor security."
msgstr "用户必須使用多因素安全認證。"

#: common/setting/system.py:975
msgid "Check plugins on startup"
msgstr "啓動時檢查插件"

#: common/setting/system.py:977
msgid "Check that all plugins are installed on startup - enable in container environments"
msgstr "啓動時檢查全部插件是否已安裝 - 在容器環境中啓用"

#: common/setting/system.py:984
msgid "Check for plugin updates"
msgstr "檢查插件更新"

#: common/setting/system.py:985
msgid "Enable periodic checks for updates to installed plugins"
msgstr "啓用定期檢查已安裝插件的更新"

#: common/setting/system.py:991
msgid "Enable URL integration"
msgstr "啓用統一資源定位符集成"

#: common/setting/system.py:992
msgid "Enable plugins to add URL routes"
msgstr "啓用插件以添加統一資源定位符路由"

#: common/setting/system.py:998
msgid "Enable navigation integration"
msgstr "啓用導航集成"

#: common/setting/system.py:999
msgid "Enable plugins to integrate into navigation"
msgstr "啓用插件以集成到導航中"

#: common/setting/system.py:1005
msgid "Enable app integration"
msgstr "啓用應用集成"

#: common/setting/system.py:1006
msgid "Enable plugins to add apps"
msgstr "啓用插件添加應用"

#: common/setting/system.py:1012
msgid "Enable schedule integration"
msgstr "啓用調度集成"

#: common/setting/system.py:1013
msgid "Enable plugins to run scheduled tasks"
msgstr "啓用插件來運行預定任務"

#: common/setting/system.py:1019
msgid "Enable event integration"
msgstr "啓用事件集成"

#: common/setting/system.py:1020
msgid "Enable plugins to respond to internal events"
msgstr "啓用插件響應內部事件"

#: common/setting/system.py:1026
msgid "Enable interface integration"
msgstr "啓用界面集成"

#: common/setting/system.py:1027
msgid "Enable plugins to integrate into the user interface"
msgstr "啓用插件集成到用户界面"

#: common/setting/system.py:1033
msgid "Enable project codes"
msgstr ""

#: common/setting/system.py:1034
msgid "Enable project codes for tracking projects"
msgstr ""

#: common/setting/system.py:1039
msgid "Stocktake Functionality"
msgstr "盤點功能"

#: common/setting/system.py:1041
msgid "Enable stocktake functionality for recording stock levels and calculating stock value"
msgstr "啓用盤點功能以記錄庫存水平和計算庫存值"

#: common/setting/system.py:1047
msgid "Exclude External Locations"
msgstr "排除外部地點"

#: common/setting/system.py:1049
msgid "Exclude stock items in external locations from stocktake calculations"
msgstr "從盤點計算中排除外部地點的庫存項"

#: common/setting/system.py:1055
msgid "Automatic Stocktake Period"
msgstr "自動盤點週期"

#: common/setting/system.py:1057
msgid "Number of days between automatic stocktake recording (set to zero to disable)"
msgstr "自動盤點記錄之間的天數 (設置為零以禁用)"

#: common/setting/system.py:1063
msgid "Report Deletion Interval"
msgstr "報告刪除間隔"

#: common/setting/system.py:1065
msgid "Stocktake reports will be deleted after specified number of days"
msgstr "盤點報告將在指定天數後刪除"

#: common/setting/system.py:1072
msgid "Display Users full names"
msgstr "顯示用户全名"

#: common/setting/system.py:1073
msgid "Display Users full names instead of usernames"
msgstr "顯示用户全名而不是用户名"

#: common/setting/system.py:1078
msgid "Display User Profiles"
msgstr ""

#: common/setting/system.py:1079
msgid "Display Users Profiles on their profile page"
msgstr ""

#: common/setting/system.py:1084
msgid "Enable Test Station Data"
msgstr "啓用測試站數據"

#: common/setting/system.py:1085
msgid "Enable test station data collection for test results"
msgstr "啓用測試站數據收集以獲取測試結果"

#: common/setting/system.py:1090
msgid "Create Template on Upload"
msgstr "上傳時創建模板"

#: common/setting/system.py:1092
msgid "Create a new test template when uploading test data which does not match an existing template"
msgstr "上傳測試數據與現有模板不匹配時創建一個新的測試模板"

#: common/setting/user.py:23
msgid "Inline label display"
msgstr "內聯標籤顯示"

#: common/setting/user.py:25
msgid "Display PDF labels in the browser, instead of downloading as a file"
msgstr "在瀏覽器中顯示PDF標籤，而不是作為文件下載"

#: common/setting/user.py:31
msgid "Default label printer"
msgstr "默認標籤打印機"

#: common/setting/user.py:32
msgid "Configure which label printer should be selected by default"
msgstr "配置默認情況下應選擇哪個標籤打印機"

#: common/setting/user.py:37
msgid "Inline report display"
msgstr "內聯報告顯示"

#: common/setting/user.py:39
msgid "Display PDF reports in the browser, instead of downloading as a file"
msgstr "在瀏覽器中顯示PDF報告，而不是作為文件下載"

#: common/setting/user.py:45
msgid "Search Parts"
msgstr "搜索零件"

#: common/setting/user.py:46
msgid "Display parts in search preview window"
msgstr "在搜索預覽窗口中顯示零件"

#: common/setting/user.py:51
msgid "Search Supplier Parts"
msgstr "搜索供應商零件"

#: common/setting/user.py:52
msgid "Display supplier parts in search preview window"
msgstr "在搜索預覽窗口中顯示供應商零件"

#: common/setting/user.py:57
msgid "Search Manufacturer Parts"
msgstr "搜索製造商零件"

#: common/setting/user.py:58
msgid "Display manufacturer parts in search preview window"
msgstr "在搜索預覽窗口中顯示製造商零件"

#: common/setting/user.py:63
msgid "Hide Inactive Parts"
msgstr "隱藏非活動零件"

#: common/setting/user.py:64
msgid "Excluded inactive parts from search preview window"
msgstr "從搜索預覽窗口中排除非活動零件"

#: common/setting/user.py:69
msgid "Search Categories"
msgstr "搜索分類"

#: common/setting/user.py:70
msgid "Display part categories in search preview window"
msgstr "在搜索預覽窗口中顯示零件類別"

#: common/setting/user.py:75
msgid "Search Stock"
msgstr "搜索庫存"

#: common/setting/user.py:76
msgid "Display stock items in search preview window"
msgstr "在搜索預覽窗口中顯示庫存項目"

#: common/setting/user.py:81
msgid "Hide Unavailable Stock Items"
msgstr "隱藏不可用的庫存項目"

#: common/setting/user.py:83
msgid "Exclude stock items which are not available from the search preview window"
msgstr "排除搜索預覽窗口中不可用的庫存項目"

#: common/setting/user.py:89
msgid "Search Locations"
msgstr "搜索地點"

#: common/setting/user.py:90
msgid "Display stock locations in search preview window"
msgstr "在搜索預覽窗口中顯示庫存位置"

#: common/setting/user.py:95
msgid "Search Companies"
msgstr "搜索公司"

#: common/setting/user.py:96
msgid "Display companies in search preview window"
msgstr "在搜索預覽窗口中顯示公司"

#: common/setting/user.py:101
msgid "Search Build Orders"
msgstr "搜索生產訂單"

#: common/setting/user.py:102
msgid "Display build orders in search preview window"
msgstr "在搜索預覽窗口中顯示生產訂單"

#: common/setting/user.py:107
msgid "Search Purchase Orders"
msgstr "搜索採購訂單"

#: common/setting/user.py:108
msgid "Display purchase orders in search preview window"
msgstr "在搜索預覽窗口中顯示採購訂單"

#: common/setting/user.py:113
msgid "Exclude Inactive Purchase Orders"
msgstr "排除未激活的採購訂單"

#: common/setting/user.py:114
msgid "Exclude inactive purchase orders from search preview window"
msgstr "從搜索預覽窗口中排除不活動的採購訂單"

#: common/setting/user.py:119
msgid "Search Sales Orders"
msgstr "搜索銷售訂單"

#: common/setting/user.py:120
msgid "Display sales orders in search preview window"
msgstr "在搜索預覽窗口中顯示銷售訂單"

#: common/setting/user.py:125
msgid "Exclude Inactive Sales Orders"
msgstr "排除未激活的銷售訂單"

#: common/setting/user.py:126
msgid "Exclude inactive sales orders from search preview window"
msgstr "從搜索預覽窗口中排除不活動的銷售訂單"

#: common/setting/user.py:131
msgid "Search Sales Order Shipments"
msgstr ""

#: common/setting/user.py:132
msgid "Display sales order shipments in search preview window"
msgstr ""

#: common/setting/user.py:137
msgid "Search Return Orders"
msgstr "搜索退貨訂單"

#: common/setting/user.py:138
msgid "Display return orders in search preview window"
msgstr "在搜索預覽窗口中顯示退貨訂單"

#: common/setting/user.py:143
msgid "Exclude Inactive Return Orders"
msgstr "排除未激活的退貨訂單"

#: common/setting/user.py:144
msgid "Exclude inactive return orders from search preview window"
msgstr "從搜索預覽窗口中排除不活動的退貨訂單"

#: common/setting/user.py:149
msgid "Search Preview Results"
msgstr "搜索預覽結果"

#: common/setting/user.py:151
msgid "Number of results to show in each section of the search preview window"
msgstr "在搜索預覽窗口的每個部分中顯示的結果數"

#: common/setting/user.py:157
msgid "Regex Search"
msgstr "正則表達式搜索"

#: common/setting/user.py:158
msgid "Enable regular expressions in search queries"
msgstr "在搜索查詢中啓用正則表達式"

#: common/setting/user.py:163
msgid "Whole Word Search"
msgstr "整詞搜索"

#: common/setting/user.py:164
msgid "Search queries return results for whole word matches"
msgstr "搜索查詢返回整詞匹配的結果"

#: common/setting/user.py:169
msgid "Search Notes"
msgstr ""

#: common/setting/user.py:171
msgid "Search queries return results for matches from the item's notes"
msgstr ""

#: common/setting/user.py:177
msgid "Show Quantity in Forms"
msgstr "在表格中顯示數量"

#: common/setting/user.py:178
msgid "Display available part quantity in some forms"
msgstr "以某些形式顯示可用零件數量"

#: common/setting/user.py:183
msgid "Escape Key Closes Forms"
msgstr "Esc鍵關閉窗體"

#: common/setting/user.py:184
msgid "Use the escape key to close modal forms"
msgstr "使用ESC鍵關閉模態窗體"

#: common/setting/user.py:189
msgid "Fixed Navbar"
msgstr "固定導航欄"

#: common/setting/user.py:190
msgid "The navbar position is fixed to the top of the screen"
msgstr "導航欄位置固定在屏幕頂部"

#: common/setting/user.py:195
msgid "Navigation Icons"
msgstr ""

#: common/setting/user.py:196
msgid "Display icons in the navigation bar"
msgstr ""

#: common/setting/user.py:201
msgid "Date Format"
msgstr "時間格式"

#: common/setting/user.py:202
msgid "Preferred format for displaying dates"
msgstr "顯示時間的首選格式"

#: common/setting/user.py:215
msgid "Part Scheduling"
msgstr "零件調度"

#: common/setting/user.py:216
msgid "Display part scheduling information"
msgstr "顯示零件排程信息"

#: common/setting/user.py:221
msgid "Part Stocktake"
msgstr "零件盤點"

#: common/setting/user.py:223
msgid "Display part stocktake information (if stocktake functionality is enabled)"
msgstr "顯示零件盤點信息 (如果啓用了盤點功能)"

#: common/setting/user.py:229
msgid "Table String Length"
msgstr "表字符串長度"

#: common/setting/user.py:230
msgid "Maximum length limit for strings displayed in table views"
msgstr "表視圖中顯示的字符串的最大長度限制"

#: common/setting/user.py:235
msgid "Show Last Breadcrumb"
msgstr ""

#: common/setting/user.py:236
msgid "Show the current page in breadcrumbs"
msgstr ""

#: common/setting/user.py:241
msgid "Receive error reports"
msgstr "接收錯誤報告"

#: common/setting/user.py:242
msgid "Receive notifications for system errors"
msgstr "接收系統錯誤通知"

#: common/setting/user.py:247
msgid "Last used printing machines"
msgstr "上次使用的打印設備"

#: common/setting/user.py:248
msgid "Save the last used printing machines for a user"
msgstr "為用户保存上次使用的打印設備"

#: common/validators.py:35
msgid "No attachment model type provided"
msgstr "未提供附件型號"

#: common/validators.py:41
msgid "Invalid attachment model type"
msgstr "附件模型類型無效"

#: common/validators.py:82
msgid "Minimum places cannot be greater than maximum places"
msgstr "最小位置不能大於最大位置"

#: common/validators.py:94
msgid "Maximum places cannot be less than minimum places"
msgstr "最大名額不能小於最小名額"

#: common/validators.py:105
msgid "An empty domain is not allowed."
msgstr "不允許空域。"

#: common/validators.py:107
#, python-brace-format
msgid "Invalid domain name: {domain}"
msgstr "無效的域名: {domain}"

#: common/validators.py:123
msgid "Value must be uppercase"
msgstr ""

#: common/validators.py:129
msgid "Value must be a valid variable identifier"
msgstr ""

#: company/api.py:141
msgid "Part is Active"
msgstr "零件已激活"

#: company/api.py:145
msgid "Manufacturer is Active"
msgstr "製造商處於活動狀態"

#: company/api.py:278
msgid "Supplier Part is Active"
msgstr "供應商零件處於激活狀態"

#: company/api.py:282
msgid "Internal Part is Active"
msgstr "內部零件已激活"

#: company/api.py:287
msgid "Supplier is Active"
msgstr "供應商已激活"

#: company/api.py:299 company/models.py:498 company/serializers.py:444
#: part/serializers.py:577
msgid "Manufacturer"
msgstr "製造商"

#: company/api.py:306 company/models.py:97 company/models.py:370
#: stock/api.py:849
msgid "Company"
msgstr "公司"

#: company/api.py:316
msgid "Has Stock"
msgstr ""

#: company/models.py:98
msgid "Companies"
msgstr "公司"

#: company/models.py:114
msgid "Company description"
msgstr "公司簡介"

#: company/models.py:115
msgid "Description of the company"
msgstr "公司簡介"

#: company/models.py:121
msgid "Website"
msgstr "網站"

#: company/models.py:122
msgid "Company website URL"
msgstr "公司網站"

#: company/models.py:128
msgid "Phone number"
msgstr "電話號碼"

#: company/models.py:130
msgid "Contact phone number"
msgstr "聯繫電話"

#: company/models.py:137
msgid "Contact email address"
msgstr "聯繫人電子郵箱地址"

#: company/models.py:142 company/models.py:274 order/models.py:500
#: users/models.py:563
msgid "Contact"
msgstr "聯繫人"

#: company/models.py:144
msgid "Point of contact"
msgstr "聯絡點"

#: company/models.py:150
msgid "Link to external company information"
msgstr "外部公司信息鏈接"

#: company/models.py:164
msgid "Is this company active?"
msgstr "這家公司是否激活？"

#: company/models.py:169
msgid "Is customer"
msgstr "是客户"

#: company/models.py:170
msgid "Do you sell items to this company?"
msgstr "你是否向該公司出售商品？"

#: company/models.py:175
msgid "Is supplier"
msgstr "是否為供應商"

#: company/models.py:176
msgid "Do you purchase items from this company?"
msgstr "你從這家公司買東西嗎？"

#: company/models.py:181
msgid "Is manufacturer"
msgstr "是製造商嗎"

#: company/models.py:182
msgid "Does this company manufacture parts?"
msgstr "這家公司生產零件嗎？"

#: company/models.py:190
msgid "Default currency used for this company"
msgstr "此公司使用的默認貨幣"

#: company/models.py:313 order/models.py:510
msgid "Address"
msgstr "地址"

#: company/models.py:314
msgid "Addresses"
msgstr "地址"

#: company/models.py:371
msgid "Select company"
msgstr "選擇公司"

#: company/models.py:376
msgid "Address title"
msgstr "地址標題"

#: company/models.py:377
msgid "Title describing the address entry"
msgstr "描述地址條目的標題"

#: company/models.py:383
msgid "Primary address"
msgstr "主要地址"

#: company/models.py:384
msgid "Set as primary address"
msgstr "設置主要地址"

#: company/models.py:389
msgid "Line 1"
msgstr "第1行"

#: company/models.py:390
msgid "Address line 1"
msgstr "地址行1"

#: company/models.py:396
msgid "Line 2"
msgstr "第2行"

#: company/models.py:397
msgid "Address line 2"
msgstr "地址行2"

#: company/models.py:403 company/models.py:404
msgid "Postal code"
msgstr "郵政編碼"

#: company/models.py:410
msgid "City/Region"
msgstr "城市/地區"

#: company/models.py:411
msgid "Postal code city/region"
msgstr "郵政編碼城市/地區"

#: company/models.py:417
msgid "State/Province"
msgstr "省/市/自治區"

#: company/models.py:418
msgid "State or province"
msgstr "省、自治區或直轄市"

#: company/models.py:424
msgid "Country"
msgstr "國家/地區"

#: company/models.py:425
msgid "Address country"
msgstr "地址所在國家"

#: company/models.py:431
msgid "Courier shipping notes"
msgstr "快遞運單"

#: company/models.py:432
msgid "Notes for shipping courier"
msgstr "運輸快遞注意事項"

#: company/models.py:438
msgid "Internal shipping notes"
msgstr "內部裝運通知單"

#: company/models.py:439
msgid "Shipping notes for internal use"
msgstr "內部使用的裝運通知單"

#: company/models.py:446
msgid "Link to address information (external)"
msgstr "鏈接地址信息 (外部)"

#: company/models.py:470 company/models.py:587 company/models.py:809
#: company/serializers.py:458
msgid "Manufacturer Part"
msgstr "製造商零件"

#: company/models.py:487 company/models.py:777 stock/models.py:948
#: stock/serializers.py:480
msgid "Base Part"
msgstr "基礎零件"

#: company/models.py:489 company/models.py:779
msgid "Select part"
msgstr "選擇零件"

#: company/models.py:499
msgid "Select manufacturer"
msgstr "選擇製造商"

#: company/models.py:505 company/serializers.py:466 order/serializers.py:665
#: part/serializers.py:587
msgid "MPN"
msgstr "製造商零件編號"

#: company/models.py:506 stock/serializers.py:625
msgid "Manufacturer Part Number"
msgstr "製造商零件編號"

#: company/models.py:513
msgid "URL for external manufacturer part link"
msgstr "外部製造商零件鏈接的URL"

#: company/models.py:522
msgid "Manufacturer part description"
msgstr "製造商零件説明"

#: company/models.py:575
msgid "Manufacturer Part Parameter"
msgstr "製造商零件參數"

#: company/models.py:594
msgid "Parameter name"
msgstr "參數名稱"

#: company/models.py:601
msgid "Parameter value"
msgstr "參數值"

#: company/models.py:608 part/models.py:1204 part/models.py:3844
msgid "Units"
msgstr "單位"

#: company/models.py:609
msgid "Parameter units"
msgstr "參數單位"

#: company/models.py:717
msgid "Pack units must be compatible with the base part units"
msgstr "包裝單位必須與基礎零件單位兼容"

#: company/models.py:724
msgid "Pack units must be greater than zero"
msgstr "包裝單位必須大於零"

#: company/models.py:738
msgid "Linked manufacturer part must reference the same base part"
msgstr "鏈接的製造商零件必須引用相同的基礎零件"

#: company/models.py:787 company/serializers.py:436 company/serializers.py:454
#: order/models.py:626 part/serializers.py:561
#: plugin/builtin/suppliers/digikey.py:25 plugin/builtin/suppliers/lcsc.py:26
#: plugin/builtin/suppliers/mouser.py:24 plugin/builtin/suppliers/tme.py:26
#: stock/api.py:516 templates/email/overdue_purchase_order.html:16
msgid "Supplier"
msgstr "供應商"

#: company/models.py:788
msgid "Select supplier"
msgstr "選擇供應商"

#: company/models.py:794 part/serializers.py:572
msgid "Supplier stock keeping unit"
msgstr "供應商庫存管理單位"

#: company/models.py:800
msgid "Is this supplier part active?"
msgstr "此供應商零件是否處於活動狀態？"

#: company/models.py:810
msgid "Select manufacturer part"
msgstr "選擇製造商零件"

#: company/models.py:817
msgid "URL for external supplier part link"
msgstr "外部供應商零件鏈接的URL"

#: company/models.py:826
msgid "Supplier part description"
msgstr "供應商零件説明"

#: company/models.py:833 order/serializers.py:819 order/serializers.py:2027
#: part/models.py:4356 part/models.py:4697
#: report/templates/report/inventree_bill_of_materials_report.html:140
#: report/templates/report/inventree_purchase_order_report.html:32
#: report/templates/report/inventree_return_order_report.html:27
#: report/templates/report/inventree_sales_order_report.html:32
#: report/templates/report/inventree_stock_location_report.html:105
#: stock/serializers.py:838
msgid "Note"
msgstr "備註"

#: company/models.py:842 part/models.py:2182
msgid "base cost"
msgstr "基本費用"

#: company/models.py:843 part/models.py:2183
msgid "Minimum charge (e.g. stocking fee)"
msgstr "最低費用(例如庫存費)"

#: company/models.py:850 order/serializers.py:811 stock/models.py:979
#: stock/serializers.py:1666
msgid "Packaging"
msgstr "打包"

#: company/models.py:851
msgid "Part packaging"
msgstr "零件打包"

#: company/models.py:856
msgid "Pack Quantity"
msgstr "包裝數量"

#: company/models.py:858
msgid "Total quantity supplied in a single pack. Leave empty for single items."
msgstr "單包供應的總數量。為單個項目留空。"

#: company/models.py:877 part/models.py:2189
msgid "multiple"
msgstr "多個"

#: company/models.py:878
msgid "Order multiple"
msgstr "訂購多個"

#: company/models.py:890
msgid "Quantity available from supplier"
msgstr "供應商提供的數量"

#: company/models.py:896
msgid "Availability Updated"
msgstr "可用性已更新"

#: company/models.py:897
msgid "Date of last update of availability data"
msgstr "上次更新可用性數據的日期"

#: company/models.py:1025
msgid "Supplier Price Break"
msgstr "供應商批發價"

#: company/serializers.py:166
msgid "Return the string representation for the primary address. This property exists for backwards compatibility."
msgstr ""

#: company/serializers.py:185
msgid "Default currency used for this supplier"
msgstr "此供應商使用的默認貨幣"

#: company/serializers.py:221
msgid "Company Name"
msgstr "公司名稱"

#: company/serializers.py:420 part/serializers.py:940 stock/serializers.py:498
msgid "In Stock"
msgstr "有庫存"

#: data_exporter/mixins.py:324 data_exporter/mixins.py:393
msgid "Error occurred during data export"
msgstr ""

#: data_exporter/mixins.py:375
msgid "Data export plugin returned incorrect data format"
msgstr ""

#: data_exporter/serializers.py:74
msgid "Export Format"
msgstr ""

#: data_exporter/serializers.py:75
msgid "Select export file format"
msgstr ""

#: data_exporter/serializers.py:82
msgid "Export Plugin"
msgstr ""

#: data_exporter/serializers.py:83
msgid "Select export plugin"
msgstr ""

#: generic/states/fields.py:146
msgid "Additional status information for this item"
msgstr "此項目的附加狀態信息"

#: generic/states/fields.py:160
msgid "Custom status key"
msgstr "自定義狀態密鑰"

#: generic/states/serializers.py:16 plugin/models.py:45 users/models.py:119
msgid "Key"
msgstr "鍵"

#: generic/states/serializers.py:26
msgid "Custom"
msgstr ""

#: generic/states/serializers.py:37
msgid "Class"
msgstr ""

#: generic/states/serializers.py:40
msgid "Values"
msgstr ""

#: generic/states/tests.py:22 order/status_codes.py:13
msgid "Placed"
msgstr "放置"

#: generic/states/validators.py:21
msgid "Invalid status code"
msgstr ""

#: importer/models.py:70
msgid "Data File"
msgstr "數據文件"

#: importer/models.py:71
msgid "Data file to import"
msgstr "要導入的數據文件"

#: importer/models.py:80
msgid "Columns"
msgstr "列"

#: importer/models.py:87
msgid "Target model type for this import session"
msgstr ""

#: importer/models.py:93
msgid "Import status"
msgstr "導入狀態"

#: importer/models.py:103
msgid "Field Defaults"
msgstr "字段默認值"

#: importer/models.py:110
msgid "Field Overrides"
msgstr "字段覆蓋"

#: importer/models.py:117
msgid "Field Filters"
msgstr "字段篩選器"

#: importer/models.py:250
msgid "Some required fields have not been mapped"
msgstr "某些必填字段尚未映射"

#: importer/models.py:407
msgid "Column is already mapped to a database field"
msgstr "列已映射到數據庫字段"

#: importer/models.py:412
msgid "Field is already mapped to a data column"
msgstr "字段已映射到數據列"

#: importer/models.py:421
msgid "Column mapping must be linked to a valid import session"
msgstr "列映射必須鏈接到有效的導入會話"

#: importer/models.py:426
msgid "Column does not exist in the data file"
msgstr "數據文件中不存在列"

#: importer/models.py:433
msgid "Field does not exist in the target model"
msgstr "目標模型中不存在字段"

#: importer/models.py:437
msgid "Selected field is read-only"
msgstr "所選字段為只讀"

#: importer/models.py:442 importer/models.py:513
msgid "Import Session"
msgstr "導入會話"

#: importer/models.py:446
msgid "Field"
msgstr "字段"

#: importer/models.py:448
msgid "Column"
msgstr "列"

#: importer/models.py:517
msgid "Row Index"
msgstr "行索引"

#: importer/models.py:520
msgid "Original row data"
msgstr "原始行數據"

#: importer/models.py:525 machine/models.py:110
msgid "Errors"
msgstr "錯誤"

#: importer/models.py:527 part/api.py:864
msgid "Valid"
msgstr "有效"

#: importer/operations.py:28 importer/operations.py:49
msgid "Unsupported data file format"
msgstr "不支持的數據文件格式"

#: importer/operations.py:40
msgid "Failed to open data file"
msgstr "打開數據文件失敗"

#: importer/operations.py:51
msgid "Invalid data file dimensions"
msgstr "數據文件維度無效"

#: importer/serializers.py:91
msgid "Invalid field defaults"
msgstr "字段默認值無效"

#: importer/serializers.py:104
msgid "Invalid field overrides"
msgstr "無效的字段覆蓋"

#: importer/serializers.py:117
msgid "Invalid field filters"
msgstr "字段篩選器無效"

#: importer/serializers.py:178
msgid "Rows"
msgstr "行"

#: importer/serializers.py:179
msgid "List of row IDs to accept"
msgstr "要接受的行ID列表"

#: importer/serializers.py:192
msgid "No rows provided"
msgstr "未提供行"

#: importer/serializers.py:196
msgid "Row does not belong to this session"
msgstr "行不屬於此會話"

#: importer/serializers.py:199
msgid "Row contains invalid data"
msgstr "行包含無效數據"

#: importer/serializers.py:202
msgid "Row has already been completed"
msgstr "行已完成"

#: importer/status_codes.py:13
msgid "Initializing"
msgstr "正在初始化"

#: importer/status_codes.py:18
msgid "Mapping Columns"
msgstr "映射列"

#: importer/status_codes.py:21
msgid "Importing Data"
msgstr "導入數據"

#: importer/status_codes.py:24
msgid "Processing Data"
msgstr "處理數據中"

#: importer/validators.py:21
msgid "Data file exceeds maximum size limit"
msgstr "數據文件超出最大大小限制"

#: importer/validators.py:26
msgid "Data file contains no headers"
msgstr "數據文件不包含標頭"

#: importer/validators.py:29
msgid "Data file contains too many columns"
msgstr "數據文件包含的列太多"

#: importer/validators.py:32
msgid "Data file contains too many rows"
msgstr "數據文件包含的行太多"

#: importer/validators.py:53
msgid "Value must be a valid dictionary object"
msgstr "值必須是有效的字典對象"

#: machine/machine_types/label_printer.py:212
msgid "Copies"
msgstr "拷貝"

#: machine/machine_types/label_printer.py:213
msgid "Number of copies to print for each label"
msgstr "每個標籤要打印的份數"

#: machine/machine_types/label_printer.py:228
msgid "Connected"
msgstr "已連接"

#: machine/machine_types/label_printer.py:229 order/api.py:1679
msgid "Unknown"
msgstr "未知"

#: machine/machine_types/label_printer.py:230
msgid "Printing"
msgstr "正在打印"

#: machine/machine_types/label_printer.py:231
msgid "No media"
msgstr "無媒體"

#: machine/machine_types/label_printer.py:232
msgid "Paper jam"
msgstr "卡紙"

#: machine/machine_types/label_printer.py:233
msgid "Disconnected"
msgstr "已斷開連接"

#: machine/machine_types/label_printer.py:240
msgid "Label Printer"
msgstr "標籤打印機"

#: machine/machine_types/label_printer.py:241
msgid "Directly print labels for various items."
msgstr "直接打印各種物品的標籤。"

#: machine/machine_types/label_printer.py:247
msgid "Printer Location"
msgstr "打印機位置"

#: machine/machine_types/label_printer.py:248
msgid "Scope the printer to a specific location"
msgstr "將打印機定位到特定位置"

#: machine/models.py:25
msgid "Name of machine"
msgstr "設備名稱"

#: machine/models.py:29
msgid "Machine Type"
msgstr "設備類型"

#: machine/models.py:29
msgid "Type of machine"
msgstr "設備類型"

#: machine/models.py:34 machine/models.py:146
msgid "Driver"
msgstr "驅動"

#: machine/models.py:35
msgid "Driver used for the machine"
msgstr "設備使用的驅動器"

#: machine/models.py:39
msgid "Machines can be disabled"
msgstr "可以禁用設備"

#: machine/models.py:95
msgid "Driver available"
msgstr "可用驅動"

#: machine/models.py:100
msgid "No errors"
msgstr "無錯誤"

#: machine/models.py:105
msgid "Initialized"
msgstr "已初始化"

#: machine/models.py:117
msgid "Machine status"
msgstr "設備狀態"

#: machine/models.py:145
msgid "Machine"
msgstr "設備"

#: machine/models.py:151
msgid "Machine Config"
msgstr "設備配置"

#: machine/models.py:156
msgid "Config type"
msgstr "配置類型"

#: order/api.py:118
msgid "Order Reference"
msgstr "訂單參考"

#: order/api.py:146 order/api.py:1139
msgid "Outstanding"
msgstr "未完成"

#: order/api.py:162
msgid "Has Project Code"
msgstr "有項目編碼"

#: order/api.py:176 order/models.py:475
msgid "Created By"
msgstr "創建人"

#: order/api.py:180
msgid "Created Before"
msgstr ""

#: order/api.py:184
msgid "Created After"
msgstr ""

#: order/api.py:188
msgid "Has Start Date"
msgstr ""

#: order/api.py:196
msgid "Start Date Before"
msgstr ""

#: order/api.py:200
msgid "Start Date After"
msgstr ""

#: order/api.py:204
msgid "Has Target Date"
msgstr ""

#: order/api.py:212
msgid "Target Date Before"
msgstr ""

#: order/api.py:216
msgid "Target Date After"
msgstr ""

#: order/api.py:267
msgid "Has Pricing"
msgstr "有定價"

#: order/api.py:320 order/api.py:774 order/api.py:1375
msgid "Completed Before"
msgstr ""

#: order/api.py:324 order/api.py:778 order/api.py:1379
msgid "Completed After"
msgstr ""

#: order/api.py:491 order/api.py:876 order/api.py:1102 order/models.py:1685
#: order/models.py:1803 order/models.py:1854 order/models.py:2002
#: order/models.py:2168 order/models.py:2690 order/models.py:2756
msgid "Order"
msgstr "訂單"

#: order/api.py:495 order/api.py:914
msgid "Order Complete"
msgstr "訂單完成"

#: order/api.py:527 order/api.py:531 order/serializers.py:676
msgid "Internal Part"
msgstr "內部零件"

#: order/api.py:549
msgid "Order Pending"
msgstr "訂單待定"

#: order/api.py:899
msgid "Completed"
msgstr "已完成"

#: order/api.py:1155
msgid "Has Shipment"
msgstr ""

#: order/api.py:1673 order/models.py:539 order/models.py:1686
#: order/models.py:1804
#: report/templates/report/inventree_purchase_order_report.html:14
#: stock/serializers.py:128 templates/email/overdue_purchase_order.html:15
msgid "Purchase Order"
msgstr "採購訂單"

#: order/api.py:1675 order/models.py:1080 order/models.py:1855
#: order/models.py:2003 order/models.py:2169
#: report/templates/report/inventree_build_order_report.html:135
#: report/templates/report/inventree_sales_order_report.html:14
#: report/templates/report/inventree_sales_order_shipment_report.html:15
#: templates/email/overdue_sales_order.html:15
msgid "Sales Order"
msgstr "銷售訂單"

#: order/api.py:1677 order/models.py:2340 order/models.py:2691
#: order/models.py:2757
#: report/templates/report/inventree_return_order_report.html:13
#: templates/email/overdue_return_order.html:15
msgid "Return Order"
msgstr "退貨訂單"

#: order/models.py:89
#: report/templates/report/inventree_purchase_order_report.html:31
#: report/templates/report/inventree_sales_order_report.html:31
msgid "Total Price"
msgstr "總價格"

#: order/models.py:90
msgid "Total price for this order"
msgstr "此訂單的總價"

#: order/models.py:95 order/serializers.py:75
msgid "Order Currency"
msgstr "訂單貨幣"

#: order/models.py:98 order/serializers.py:76
msgid "Currency for this order (leave blank to use company default)"
msgstr "此訂單的貨幣 (留空以使用公司默認值)"

#: order/models.py:323
msgid "This order is locked and cannot be modified"
msgstr ""

#: order/models.py:370
msgid "Contact does not match selected company"
msgstr "聯繫人與所選公司不匹配"

#: order/models.py:377
msgid "Start date must be before target date"
msgstr ""

#: order/models.py:430
msgid "Order description (optional)"
msgstr "訂單描述 (可選)"

#: order/models.py:439
msgid "Select project code for this order"
msgstr "為此訂單選擇項目編碼"

#: order/models.py:445 order/models.py:1586 order/models.py:2057
msgid "Link to external page"
msgstr "鏈接到外部頁面"

#: order/models.py:452
msgid "Start date"
msgstr ""

#: order/models.py:453
msgid "Scheduled start date for this order"
msgstr ""

#: order/models.py:459 order/models.py:1593 order/serializers.py:269
#: report/templates/report/inventree_build_order_report.html:125
msgid "Target Date"
msgstr "預計日期"

#: order/models.py:461
msgid "Expected date for order delivery. Order will be overdue after this date."
msgstr "訂單交付的預期日期。訂單將在此日期後過期。"

#: order/models.py:481
msgid "Issue Date"
msgstr "簽發日期"

#: order/models.py:482
msgid "Date order was issued"
msgstr "訂單發出日期"

#: order/models.py:490
msgid "User or group responsible for this order"
msgstr "負責此訂單的用户或組"

#: order/models.py:501
msgid "Point of contact for this order"
msgstr "此訂單的聯繫人"

#: order/models.py:511
msgid "Company address for this order"
msgstr "此訂單的公司地址"

#: order/models.py:602 order/models.py:1141
msgid "Order reference"
msgstr "訂單參考"

#: order/models.py:611 order/models.py:1165 order/models.py:2428
#: stock/serializers.py:612 stock/serializers.py:1010 users/models.py:544
msgid "Status"
msgstr "狀態"

#: order/models.py:612
msgid "Purchase order status"
msgstr "採購訂單狀態"

#: order/models.py:627
msgid "Company from which the items are being ordered"
msgstr "訂購物品的公司"

#: order/models.py:638
msgid "Supplier Reference"
msgstr "供應商參考"

#: order/models.py:639
msgid "Supplier order reference code"
msgstr "供應商訂單參考代碼"

#: order/models.py:648
msgid "received by"
msgstr "接收人"

#: order/models.py:655 order/models.py:2443
msgid "Date order was completed"
msgstr "訂單完成日期"

#: order/models.py:664 order/models.py:1733
msgid "Destination"
msgstr "目的地"

#: order/models.py:665 order/models.py:1737
msgid "Destination for received items"
msgstr ""

#: order/models.py:711
msgid "Part supplier must match PO supplier"
msgstr "零件供應商必須與採購訂單供應商匹配"

#: order/models.py:971
msgid "Quantity must be a positive number"
msgstr "數量必須是正數"

#: order/models.py:1152 order/models.py:2415 stock/models.py:1001
#: stock/models.py:1002 stock/serializers.py:1405
#: templates/email/overdue_return_order.html:16
#: templates/email/overdue_sales_order.html:16
msgid "Customer"
msgstr "客户"

#: order/models.py:1153
msgid "Company to which the items are being sold"
msgstr "出售物品的公司"

#: order/models.py:1166
msgid "Sales order status"
msgstr "銷售訂單狀態"

#: order/models.py:1177 order/models.py:2435
msgid "Customer Reference "
msgstr "客户參考 "

#: order/models.py:1178 order/models.py:2436
msgid "Customer order reference code"
msgstr "客户訂單參考代碼"

#: order/models.py:1182 order/models.py:2009
msgid "Shipment Date"
msgstr "發貨日期"

#: order/models.py:1191
msgid "shipped by"
msgstr "發貨人"

#: order/models.py:1230
msgid "Order is already complete"
msgstr "訂單已完成"

#: order/models.py:1233
msgid "Order is already cancelled"
msgstr "訂單已取消"

#: order/models.py:1237
msgid "Only an open order can be marked as complete"
msgstr "只有未結訂單才能標記為已完成"

#: order/models.py:1241
msgid "Order cannot be completed as there are incomplete shipments"
msgstr "由於發貨不完整，訂單無法完成"

#: order/models.py:1246
msgid "Order cannot be completed as there are incomplete allocations"
msgstr ""

#: order/models.py:1251
msgid "Order cannot be completed as there are incomplete line items"
msgstr "訂單無法完成，因為行項目不完整"

#: order/models.py:1535 order/models.py:1548
msgid "The order is locked and cannot be modified"
msgstr ""

#: order/models.py:1556
msgid "Item quantity"
msgstr "項目數量"

#: order/models.py:1573
msgid "Line item reference"
msgstr "行項目參考"

#: order/models.py:1580
msgid "Line item notes"
msgstr "行項目註釋"

#: order/models.py:1595
msgid "Target date for this line item (leave blank to use the target date from the order)"
msgstr "此行項目的目標日期 (留空以使用訂單中的目標日期)"

#: order/models.py:1616
msgid "Line item description (optional)"
msgstr "行項目描述 (可選)"

#: order/models.py:1623
msgid "Additional context for this line"
msgstr "此行的附加上下文"

#: order/models.py:1633
msgid "Unit price"
msgstr "單位價格"

#: order/models.py:1647
msgid "Purchase Order Line Item"
msgstr "採購訂單行項目"

#: order/models.py:1671
msgid "Supplier part must match supplier"
msgstr "供應商零件必須與供應商匹配"

#: order/models.py:1705
msgid "Supplier part"
msgstr "供應商零件"

#: order/models.py:1712
msgid "Received"
msgstr "已接收"

#: order/models.py:1713
msgid "Number of items received"
msgstr "收到的物品數量"

#: order/models.py:1721 stock/models.py:1124 stock/serializers.py:679
msgid "Purchase Price"
msgstr "採購價格"

#: order/models.py:1722
msgid "Unit purchase price"
msgstr "每單位的採購價格"

#: order/models.py:1792
msgid "Purchase Order Extra Line"
msgstr "採購訂單附加行"

#: order/models.py:1821
msgid "Sales Order Line Item"
msgstr "銷售訂單行項目"

#: order/models.py:1842
msgid "Virtual part cannot be assigned to a sales order"
msgstr "虛擬零件不能分配給銷售訂單"

#: order/models.py:1847
msgid "Only salable parts can be assigned to a sales order"
msgstr "只有可銷售的零件才能分配給銷售訂單"

#: order/models.py:1873
msgid "Sale Price"
msgstr "售出價格"

#: order/models.py:1874
msgid "Unit sale price"
msgstr "單位售出價格"

#: order/models.py:1883 order/status_codes.py:50
msgid "Shipped"
msgstr "已配送"

#: order/models.py:1884
msgid "Shipped quantity"
msgstr "發貨數量"

#: order/models.py:1978
msgid "Sales Order Shipment"
msgstr "銷售訂單發貨"

#: order/models.py:2010
msgid "Date of shipment"
msgstr "發貨日期"

#: order/models.py:2016
msgid "Delivery Date"
msgstr "送達日期"

#: order/models.py:2017
msgid "Date of delivery of shipment"
msgstr "裝運交貨日期"

#: order/models.py:2025
msgid "Checked By"
msgstr "審核人"

#: order/models.py:2026
msgid "User who checked this shipment"
msgstr "檢查此裝運的用户"

#: order/models.py:2033 order/models.py:2265 order/serializers.py:1685
#: order/serializers.py:1809
#: report/templates/report/inventree_sales_order_shipment_report.html:14
msgid "Shipment"
msgstr "配送"

#: order/models.py:2034
msgid "Shipment number"
msgstr "配送單號"

#: order/models.py:2042
msgid "Tracking Number"
msgstr "跟蹤單號"

#: order/models.py:2043
msgid "Shipment tracking information"
msgstr "配送跟蹤信息"

#: order/models.py:2050
msgid "Invoice Number"
msgstr "發票編號"

#: order/models.py:2051
msgid "Reference number for associated invoice"
msgstr "相關發票的參考號"

#: order/models.py:2074
msgid "Shipment has already been sent"
msgstr "貨物已發出"

#: order/models.py:2077
msgid "Shipment has no allocated stock items"
msgstr "發貨沒有分配庫存項目"

#: order/models.py:2157
msgid "Sales Order Extra Line"
msgstr "銷售訂單加行"

#: order/models.py:2186
msgid "Sales Order Allocation"
msgstr "銷售訂單分配"

#: order/models.py:2209 order/models.py:2211
msgid "Stock item has not been assigned"
msgstr "庫存項目尚未分配"

#: order/models.py:2218
msgid "Cannot allocate stock item to a line with a different part"
msgstr "無法將庫存項目分配給具有不同零件的行"

#: order/models.py:2221
msgid "Cannot allocate stock to a line without a part"
msgstr "無法將庫存分配給沒有零件的生產線"

#: order/models.py:2224
msgid "Allocation quantity cannot exceed stock quantity"
msgstr "分配數量不能超過庫存數量"

#: order/models.py:2243 order/serializers.py:1555
msgid "Quantity must be 1 for serialized stock item"
msgstr "序列化庫存項目的數量必須為1"

#: order/models.py:2246
msgid "Sales order does not match shipment"
msgstr "銷售訂單與發貨不匹配"

#: order/models.py:2247 plugin/base/barcodes/api.py:642
msgid "Shipment does not match sales order"
msgstr "發貨與銷售訂單不匹配"

#: order/models.py:2255
msgid "Line"
msgstr "行"

#: order/models.py:2266
msgid "Sales order shipment reference"
msgstr "銷售訂單發貨參考"

#: order/models.py:2279 order/models.py:2698
msgid "Item"
msgstr "項目"

#: order/models.py:2280
msgid "Select stock item to allocate"
msgstr "選擇要分配的庫存項目"

#: order/models.py:2289
msgid "Enter stock allocation quantity"
msgstr "輸入庫存分配數量"

#: order/models.py:2404
msgid "Return Order reference"
msgstr "退貨訂單參考"

#: order/models.py:2416
msgid "Company from which items are being returned"
msgstr "退回物品的公司"

#: order/models.py:2429
msgid "Return order status"
msgstr "退貨訂單狀態"

#: order/models.py:2656
msgid "Return Order Line Item"
msgstr "退貨訂單行項目"

#: order/models.py:2669
msgid "Stock item must be specified"
msgstr ""

#: order/models.py:2673
msgid "Return quantity exceeds stock quantity"
msgstr ""

#: order/models.py:2678
msgid "Return quantity must be greater than zero"
msgstr ""

#: order/models.py:2683
msgid "Invalid quantity for serialized stock item"
msgstr ""

#: order/models.py:2699
msgid "Select item to return from customer"
msgstr "選擇要從客户處退回的商品"

#: order/models.py:2714
msgid "Received Date"
msgstr "接收日期"

#: order/models.py:2715
msgid "The date this this return item was received"
msgstr "收到此退貨的日期"

#: order/models.py:2727
msgid "Outcome"
msgstr "結果"

#: order/models.py:2728
msgid "Outcome for this line item"
msgstr "該行項目的結果"

#: order/models.py:2735
msgid "Cost associated with return or repair for this line item"
msgstr "與此行項目的退貨或維修相關的成本"

#: order/models.py:2745
msgid "Return Order Extra Line"
msgstr "退貨訂單附加行"

#: order/serializers.py:89
msgid "Order ID"
msgstr "訂單ID"

#: order/serializers.py:89
msgid "ID of the order to duplicate"
msgstr "要複製的訂單ID"

#: order/serializers.py:95
msgid "Copy Lines"
msgstr "複製行"

#: order/serializers.py:96
msgid "Copy line items from the original order"
msgstr "從原始訂單複製行項目"

#: order/serializers.py:102
msgid "Copy Extra Lines"
msgstr "複製額外行"

#: order/serializers.py:103
msgid "Copy extra line items from the original order"
msgstr "從原始訂單複製額外的行項目"

#: order/serializers.py:116
#: report/templates/report/inventree_purchase_order_report.html:22
#: report/templates/report/inventree_return_order_report.html:19
#: report/templates/report/inventree_sales_order_report.html:22
msgid "Line Items"
msgstr "行項目"

#: order/serializers.py:121
msgid "Completed Lines"
msgstr "已完成行項目"

#: order/serializers.py:172
msgid "Duplicate Order"
msgstr "複製訂單"

#: order/serializers.py:173
msgid "Specify options for duplicating this order"
msgstr "指定複製此訂單的選項"

#: order/serializers.py:249
msgid "Invalid order ID"
msgstr "訂單ID不正確"

#: order/serializers.py:388
msgid "Supplier Name"
msgstr "供應商名稱"

#: order/serializers.py:430
msgid "Order cannot be cancelled"
msgstr "訂單不能取消"

#: order/serializers.py:445 order/serializers.py:1576
msgid "Allow order to be closed with incomplete line items"
msgstr "允許關閉行項目不完整的訂單"

#: order/serializers.py:455 order/serializers.py:1586
msgid "Order has incomplete line items"
msgstr "訂單中的行項目不完整"

#: order/serializers.py:608
msgid "Order is not open"
msgstr "訂單未打開"

#: order/serializers.py:629
msgid "Auto Pricing"
msgstr "自動定價"

#: order/serializers.py:631
msgid "Automatically calculate purchase price based on supplier part data"
msgstr "根據供應商零件數據自動計算採購價格"

#: order/serializers.py:641
msgid "Purchase price currency"
msgstr "購買價格貨幣"

#: order/serializers.py:649
msgid "Merge Items"
msgstr "合併項目"

#: order/serializers.py:651
msgid "Merge items with the same part, destination and target date into one line item"
msgstr "將具有相同零件、目的地和目標日期的項目合併到一個行項目中"

#: order/serializers.py:658 part/serializers.py:571
msgid "SKU"
msgstr "庫存量單位"

#: order/serializers.py:672 part/models.py:1089 part/serializers.py:412
msgid "Internal Part Number"
msgstr "內部零件編號"

#: order/serializers.py:680
msgid "Internal Part Name"
msgstr "內部零件名稱"

#: order/serializers.py:696
msgid "Supplier part must be specified"
msgstr "必須指定供應商零件"

#: order/serializers.py:699
msgid "Purchase order must be specified"
msgstr "必須指定採購訂單"

#: order/serializers.py:707
msgid "Supplier must match purchase order"
msgstr "供應商必須匹配採購訂單"

#: order/serializers.py:708
msgid "Purchase order must match supplier"
msgstr "採購訂單必須與供應商匹配"

#: order/serializers.py:754 order/serializers.py:1656
msgid "Line Item"
msgstr "行項目"

#: order/serializers.py:760
msgid "Line item does not match purchase order"
msgstr "行項目與採購訂單不匹配"

#: order/serializers.py:770 order/serializers.py:917 order/serializers.py:2023
msgid "Select destination location for received items"
msgstr "為收到的物品選擇目的地位置"

#: order/serializers.py:786
msgid "Enter batch code for incoming stock items"
msgstr "輸入入庫項目的批號"

#: order/serializers.py:793 stock/models.py:1083 users/models.py:143
msgid "Expiry Date"
msgstr "有效期至"

#: order/serializers.py:794
msgid "Enter expiry date for incoming stock items"
msgstr ""

#: order/serializers.py:802
msgid "Enter serial numbers for incoming stock items"
msgstr "輸入入庫庫存項目的序列號"

#: order/serializers.py:812
msgid "Override packaging information for incoming stock items"
msgstr "覆蓋傳入庫存項目的包裝資料"

#: order/serializers.py:820 order/serializers.py:2028
msgid "Additional note for incoming stock items"
msgstr "傳入庫存項目的附加説明"

#: order/serializers.py:827
msgid "Barcode"
msgstr "條形碼"

#: order/serializers.py:828
msgid "Scanned barcode"
msgstr "掃描條形碼"

#: order/serializers.py:844
msgid "Barcode is already in use"
msgstr "條形碼已被使用"

#: order/serializers.py:867
msgid "An integer quantity must be provided for trackable parts"
msgstr "必須為可跟蹤零件提供整數數量"

#: order/serializers.py:934 order/serializers.py:2047
msgid "Line items must be provided"
msgstr "必須提供行項目"

#: order/serializers.py:950
msgid "Destination location must be specified"
msgstr "必須指定目標位置"

#: order/serializers.py:961
msgid "Supplied barcode values must be unique"
msgstr "提供的條形碼值必須是唯一的"

#: order/serializers.py:1092
msgid "Shipments"
msgstr ""

#: order/serializers.py:1096
msgid "Completed Shipments"
msgstr "完成配送"

#: order/serializers.py:1283
msgid "Sale price currency"
msgstr "售出價格貨幣"

#: order/serializers.py:1331
msgid "Allocated Items"
msgstr ""

#: order/serializers.py:1458
msgid "No shipment details provided"
msgstr "未提供裝運詳細信息"

#: order/serializers.py:1519 order/serializers.py:1665
msgid "Line item is not associated with this order"
msgstr "行項目與此訂單不關聯"

#: order/serializers.py:1538
msgid "Quantity must be positive"
msgstr "數量必須為正"

#: order/serializers.py:1675
msgid "Enter serial numbers to allocate"
msgstr "輸入要分配的序列號"

#: order/serializers.py:1697 order/serializers.py:1817
msgid "Shipment has already been shipped"
msgstr "貨物已發出"

#: order/serializers.py:1700 order/serializers.py:1820
msgid "Shipment is not associated with this order"
msgstr "發貨與此訂單無關"

#: order/serializers.py:1755
msgid "No match found for the following serial numbers"
msgstr "未找到以下序列號的匹配項"

#: order/serializers.py:1762
msgid "The following serial numbers are unavailable"
msgstr "以下序列號不可用"

#: order/serializers.py:1989
msgid "Return order line item"
msgstr "退貨訂單行項目"

#: order/serializers.py:1999
msgid "Line item does not match return order"
msgstr "行項目與退貨訂單不匹配"

#: order/serializers.py:2002
msgid "Line item has already been received"
msgstr "行項目已收到"

#: order/serializers.py:2039
msgid "Items can only be received against orders which are in progress"
msgstr "只能根據正在進行的訂單接收物品"

#: order/serializers.py:2131
msgid "Quantity to return"
msgstr ""

#: order/serializers.py:2143
msgid "Line price currency"
msgstr "行價格貨幣"

#: order/status_codes.py:17 order/status_codes.py:54 stock/status_codes.py:16
msgid "Lost"
msgstr "丟失"

#: order/status_codes.py:18 order/status_codes.py:55 stock/status_codes.py:24
msgid "Returned"
msgstr "已退回"

#: order/status_codes.py:47 order/status_codes.py:79
msgid "In Progress"
msgstr "正在進行"

#: order/status_codes.py:105
msgid "Return"
msgstr "退回"

#: order/status_codes.py:108
msgid "Repair"
msgstr "維修"

#: order/status_codes.py:111
msgid "Replace"
msgstr "替換"

#: order/status_codes.py:114
msgid "Refund"
msgstr "退款"

#: order/status_codes.py:117
msgid "Reject"
msgstr "拒絕"

#: order/tasks.py:44
msgid "Overdue Purchase Order"
msgstr "逾期採購訂單"

#: order/tasks.py:49
#, python-brace-format
msgid "Purchase order {po} is now overdue"
msgstr "採購訂單 {po} 已逾期"

#: order/tasks.py:112
msgid "Overdue Sales Order"
msgstr "逾期銷售訂單"

#: order/tasks.py:117
#, python-brace-format
msgid "Sales order {so} is now overdue"
msgstr "銷售訂單 {so} 已逾期"

#: order/tasks.py:177
msgid "Overdue Return Order"
msgstr ""

#: order/tasks.py:182
#, python-brace-format
msgid "Return order {ro} is now overdue"
msgstr ""

#: part/api.py:115
msgid "Starred"
msgstr "已加星標"

#: part/api.py:117
msgid "Filter by starred categories"
msgstr "按星標類別篩選"

#: part/api.py:134 stock/api.py:255
msgid "Depth"
msgstr "深度"

#: part/api.py:134
msgid "Filter by category depth"
msgstr "按類別深度篩選"

#: part/api.py:152 stock/api.py:273
msgid "Top Level"
msgstr "頂級"

#: part/api.py:154
msgid "Filter by top-level categories"
msgstr "按頂級類別篩選"

#: part/api.py:167 stock/api.py:288
msgid "Cascade"
msgstr "級聯"

#: part/api.py:169
msgid "Include sub-categories in filtered results"
msgstr "在篩選結果中包含子類別"

#: part/api.py:189
msgid "Parent"
msgstr "父類"

#: part/api.py:191
msgid "Filter by parent category"
msgstr "按父類別篩選"

#: part/api.py:226
msgid "Exclude sub-categories under the specified category"
msgstr "排除指定類別下的子類別"

#: part/api.py:438
msgid "Has Results"
msgstr "有結果"

#: part/api.py:611
msgid "Incoming Purchase Order"
msgstr "收到的採購訂單"

#: part/api.py:625
msgid "Outgoing Sales Order"
msgstr "外發銷售訂單"

#: part/api.py:637
msgid "Stock produced by Build Order"
msgstr "建造生產訂單產生的庫存"

#: part/api.py:719
msgid "Stock required for Build Order"
msgstr "生產訂單所需的庫存"

#: part/api.py:865
msgid "Validate entire Bill of Materials"
msgstr "驗證整個物料清單"

#: part/api.py:871
msgid "This option must be selected"
msgstr "必須選擇此項"

#: part/api.py:907
msgid "Is Variant"
msgstr ""

#: part/api.py:915
msgid "Is Revision"
msgstr "是修訂版本"

#: part/api.py:925
msgid "Has Revisions"
msgstr "有修訂版本"

#: part/api.py:1116
msgid "BOM Valid"
msgstr "物料清單合規"

#: part/api.py:1775
msgid "Assembly part is testable"
msgstr "裝配部份是可測試的"

#: part/api.py:1784
msgid "Component part is testable"
msgstr "組件部份是可測試的"

#: part/api.py:1835
msgid "Uses"
msgstr "使用"

#: part/models.py:90 part/models.py:4137
#: templates/email/part_event_notification.html:16
msgid "Part Category"
msgstr "零件類別"

#: part/models.py:91 users/ruleset.py:32
msgid "Part Categories"
msgstr "零件類別"

#: part/models.py:109 part/models.py:1134
msgid "Default Location"
msgstr "默認位置"

#: part/models.py:110
msgid "Default location for parts in this category"
msgstr "此類別零件的默認庫存地點"

#: part/models.py:115 stock/models.py:212
msgid "Structural"
msgstr "結構性"

#: part/models.py:117
msgid "Parts may not be directly assigned to a structural category, but may be assigned to child categories."
msgstr "零件可能無法直接分配到結構類別，但可以分配到子類別。"

#: part/models.py:126
msgid "Default keywords"
msgstr "默認關鍵字"

#: part/models.py:127
msgid "Default keywords for parts in this category"
msgstr "此類別零件的默認關鍵字"

#: part/models.py:134 stock/models.py:96 stock/models.py:194
msgid "Icon"
msgstr "圖標"

#: part/models.py:135 part/serializers.py:152 part/serializers.py:171
#: stock/models.py:195
msgid "Icon (optional)"
msgstr "圖標(可選)"

#: part/models.py:179
msgid "You cannot make this part category structural because some parts are already assigned to it!"
msgstr "您不能使這個零件類別結構化，因為有些零件已經分配給了它！"

#: part/models.py:458 part/serializers.py:130 part/serializers.py:343
#: users/ruleset.py:33
msgid "Parts"
msgstr "零件"

#: part/models.py:510
msgid "Cannot delete this part as it is locked"
msgstr "無法刪除這個零件，因為它已被鎖定"

#: part/models.py:513
msgid "Cannot delete this part as it is still active"
msgstr "無法刪除這個零件，因為它仍然處於活動狀態"

#: part/models.py:518
msgid "Cannot delete this part as it is used in an assembly"
msgstr "無法刪除這個零件，因為它被使用在了裝配中"

#: part/models.py:556
msgid "Invalid choice for parent part"
msgstr "無效的上級零件選擇"

#: part/models.py:604 part/models.py:611
#, python-brace-format
msgid "Part '{self}' cannot be used in BOM for '{parent}' (recursive)"
msgstr "零件 \"{self}\" 不能用在 \"{parent}\" 的物料清單 (遞歸)"

#: part/models.py:623
#, python-brace-format
msgid "Part '{parent}' is  used in BOM for '{self}' (recursive)"
msgstr "零件 \"{parent}\" 被使用在了 \"{self}\" 的物料清單 (遞歸)"

#: part/models.py:690
#, python-brace-format
msgid "IPN must match regex pattern {pattern}"
msgstr "內部零件號必須匹配正則表達式 {pattern}"

#: part/models.py:698
msgid "Part cannot be a revision of itself"
msgstr "零件不能是對自身的修訂"

#: part/models.py:705
msgid "Cannot make a revision of a part which is already a revision"
msgstr "無法對已經是修訂版本的零件進行修訂"

#: part/models.py:712
msgid "Revision code must be specified"
msgstr "必須指定修訂代碼"

#: part/models.py:719
msgid "Revisions are only allowed for assembly parts"
msgstr "修訂僅對裝配零件允許"

#: part/models.py:726
msgid "Cannot make a revision of a template part"
msgstr "無法對模版零件進行修訂"

#: part/models.py:732
msgid "Parent part must point to the same template"
msgstr "上級零件必須指向相同的模版"

#: part/models.py:829
msgid "Stock item with this serial number already exists"
msgstr "該序列號庫存項己存在"

#: part/models.py:971
msgid "Duplicate IPN not allowed in part settings"
msgstr "在零件設置中不允許重複的內部零件號"

#: part/models.py:983
msgid "Duplicate part revision already exists."
msgstr "重複的零件修訂版本已經存在。"

#: part/models.py:992
msgid "Part with this Name, IPN and Revision already exists."
msgstr "有這個名字，內部零件號，和修訂版本的零件已經存在"

#: part/models.py:1007
msgid "Parts cannot be assigned to structural part categories!"
msgstr "零件不能分配到結構性零件類別！"

#: part/models.py:1039
msgid "Part name"
msgstr "零件名稱"

#: part/models.py:1044
msgid "Is Template"
msgstr "是模板"

#: part/models.py:1045
msgid "Is this part a template part?"
msgstr "這個零件是一個模版零件嗎?"

#: part/models.py:1055
msgid "Is this part a variant of another part?"
msgstr "這個零件是另一零件的變體嗎？"

#: part/models.py:1056
msgid "Variant Of"
msgstr "變體"

#: part/models.py:1063
msgid "Part description (optional)"
msgstr "零件描述(可選)"

#: part/models.py:1070
msgid "Keywords"
msgstr "關鍵詞"

#: part/models.py:1071
msgid "Part keywords to improve visibility in search results"
msgstr "提高搜索結果可見性的零件關鍵字"

#: part/models.py:1081
msgid "Part category"
msgstr "零件類別"

#: part/models.py:1088 part/serializers.py:926
#: report/templates/report/inventree_stock_location_report.html:103
msgid "IPN"
msgstr "內部零件號 IPN"

#: part/models.py:1096
msgid "Part revision or version number"
msgstr "零件修訂版本或版本號"

#: part/models.py:1097 report/models.py:215
msgid "Revision"
msgstr "版本"

#: part/models.py:1106
msgid "Is this part a revision of another part?"
msgstr "這零件是另一零件的修訂版本嗎？"

#: part/models.py:1107
msgid "Revision Of"
msgstr "修訂版本"

#: part/models.py:1132
msgid "Where is this item normally stored?"
msgstr "該物品通常存放在哪裏？"

#: part/models.py:1178
msgid "Default Supplier"
msgstr "默認供應商"

#: part/models.py:1179
msgid "Default supplier part"
msgstr "默認供應商零件"

#: part/models.py:1186
msgid "Default Expiry"
msgstr "默認到期"

#: part/models.py:1187
msgid "Expiry time (in days) for stock items of this part"
msgstr "此零件庫存項的過期時間 (天)"

#: part/models.py:1195 part/serializers.py:977
msgid "Minimum Stock"
msgstr "最低庫存"

#: part/models.py:1196
msgid "Minimum allowed stock level"
msgstr "允許的最小庫存量"

#: part/models.py:1205
msgid "Units of measure for this part"
msgstr "此零件的計量單位"

#: part/models.py:1212
msgid "Can this part be built from other parts?"
msgstr "這個零件可由其他零件加工而成嗎？"

#: part/models.py:1218
msgid "Can this part be used to build other parts?"
msgstr "這個零件可用於創建其他零件嗎？"

#: part/models.py:1224
msgid "Does this part have tracking for unique items?"
msgstr "此零件是否有唯一物品的追蹤功能"

#: part/models.py:1230
msgid "Can this part have test results recorded against it?"
msgstr "這一部分能否記錄到測試結果？"

#: part/models.py:1236
msgid "Can this part be purchased from external suppliers?"
msgstr "這個零件可從外部供應商購買嗎？"

#: part/models.py:1242
msgid "Can this part be sold to customers?"
msgstr "此零件可以銷售給客户嗎?"

#: part/models.py:1246
msgid "Is this part active?"
msgstr "這個零件是否已激活？"

#: part/models.py:1252
msgid "Locked parts cannot be edited"
msgstr "無法編輯鎖定的零件"

#: part/models.py:1258
msgid "Is this a virtual part, such as a software product or license?"
msgstr "這是一個虛擬零件，例如一個軟件產品或許可證嗎？"

#: part/models.py:1264
msgid "BOM checksum"
msgstr "物料清單校驗和"

#: part/models.py:1265
msgid "Stored BOM checksum"
msgstr "保存的物料清單校驗和"

#: part/models.py:1273
msgid "BOM checked by"
msgstr "物料清單檢查人"

#: part/models.py:1278
msgid "BOM checked date"
msgstr "物料清單檢查日期"

#: part/models.py:1294
msgid "Creation User"
msgstr "新建用户"

#: part/models.py:1304
msgid "Owner responsible for this part"
msgstr "此零件的負責人"

#: part/models.py:1309
msgid "Last Stocktake"
msgstr "最近庫存盤點"

#: part/models.py:2190
msgid "Sell multiple"
msgstr "出售多個"

#: part/models.py:3185
msgid "Currency used to cache pricing calculations"
msgstr "用於緩存定價計算的貨幣"

#: part/models.py:3201
msgid "Minimum BOM Cost"
msgstr "最低物料清單成本"

#: part/models.py:3202
msgid "Minimum cost of component parts"
msgstr "元件的最低成本"

#: part/models.py:3208
msgid "Maximum BOM Cost"
msgstr "物料清單的最高成本"

#: part/models.py:3209
msgid "Maximum cost of component parts"
msgstr "元件的最高成本"

#: part/models.py:3215
msgid "Minimum Purchase Cost"
msgstr "最低購買成本"

#: part/models.py:3216
msgid "Minimum historical purchase cost"
msgstr "最高歷史購買成本"

#: part/models.py:3222
msgid "Maximum Purchase Cost"
msgstr "最大購買成本"

#: part/models.py:3223
msgid "Maximum historical purchase cost"
msgstr "最高歷史購買成本"

#: part/models.py:3229
msgid "Minimum Internal Price"
msgstr "最低內部價格"

#: part/models.py:3230
msgid "Minimum cost based on internal price breaks"
msgstr "基於內部批發價的最低成本"

#: part/models.py:3236
msgid "Maximum Internal Price"
msgstr "最大內部價格"

#: part/models.py:3237
msgid "Maximum cost based on internal price breaks"
msgstr "基於內部批發價的最高成本"

#: part/models.py:3243
msgid "Minimum Supplier Price"
msgstr "供應商最低價格"

#: part/models.py:3244
msgid "Minimum price of part from external suppliers"
msgstr "外部供應商零件的最低價格"

#: part/models.py:3250
msgid "Maximum Supplier Price"
msgstr "供應商最高價格"

#: part/models.py:3251
msgid "Maximum price of part from external suppliers"
msgstr "來自外部供應商的商零件的最高價格"

#: part/models.py:3257
msgid "Minimum Variant Cost"
msgstr "最小變體成本"

#: part/models.py:3258
msgid "Calculated minimum cost of variant parts"
msgstr "計算出的變體零件的最低成本"

#: part/models.py:3264
msgid "Maximum Variant Cost"
msgstr "最大變體成本"

#: part/models.py:3265
msgid "Calculated maximum cost of variant parts"
msgstr "計算出的變體零件的最大成本"

#: part/models.py:3271 part/models.py:3285
msgid "Minimum Cost"
msgstr "最低成本"

#: part/models.py:3272
msgid "Override minimum cost"
msgstr "覆蓋最低成本"

#: part/models.py:3278 part/models.py:3292
msgid "Maximum Cost"
msgstr "最高成本"

#: part/models.py:3279
msgid "Override maximum cost"
msgstr "覆蓋最大成本"

#: part/models.py:3286
msgid "Calculated overall minimum cost"
msgstr "計算總最低成本"

#: part/models.py:3293
msgid "Calculated overall maximum cost"
msgstr "計算總最大成本"

#: part/models.py:3299
msgid "Minimum Sale Price"
msgstr "最低售出價格"

#: part/models.py:3300
msgid "Minimum sale price based on price breaks"
msgstr "基於批發價的最低售出價格"

#: part/models.py:3306
msgid "Maximum Sale Price"
msgstr "最高售出價格"

#: part/models.py:3307
msgid "Maximum sale price based on price breaks"
msgstr "基於批發價的最大售出價格"

#: part/models.py:3313
msgid "Minimum Sale Cost"
msgstr "最低銷售成本"

#: part/models.py:3314
msgid "Minimum historical sale price"
msgstr "歷史最低售出價格"

#: part/models.py:3320
msgid "Maximum Sale Cost"
msgstr "最高銷售成本"

#: part/models.py:3321
msgid "Maximum historical sale price"
msgstr "歷史最高售出價格"

#: part/models.py:3340
msgid "Part for stocktake"
msgstr "用於盤點的零件"

#: part/models.py:3345
msgid "Item Count"
msgstr "物品數量"

#: part/models.py:3346
msgid "Number of individual stock entries at time of stocktake"
msgstr "盤點時的個別庫存條目數"

#: part/models.py:3354
msgid "Total available stock at time of stocktake"
msgstr "盤點時可用庫存總額"

#: part/models.py:3358 part/models.py:3441 part/serializers.py:273
#: report/templates/report/inventree_test_report.html:106
msgid "Date"
msgstr "日期"

#: part/models.py:3359
msgid "Date stocktake was performed"
msgstr "進行盤點的日期"

#: part/models.py:3367
msgid "Additional notes"
msgstr "附加註釋"

#: part/models.py:3377
msgid "User who performed this stocktake"
msgstr "進行此盤點的用户"

#: part/models.py:3383
msgid "Minimum Stock Cost"
msgstr "最低庫存成本"

#: part/models.py:3384
msgid "Estimated minimum cost of stock on hand"
msgstr "現有存庫存最低成本估算"

#: part/models.py:3390
msgid "Maximum Stock Cost"
msgstr "最高庫存成本"

#: part/models.py:3391
msgid "Estimated maximum cost of stock on hand"
msgstr "目前庫存最高成本估算"

#: part/models.py:3447
msgid "Report"
msgstr "報告"

#: part/models.py:3448
msgid "Stocktake report file (generated internally)"
msgstr "盤點報告文件(內部生成)"

#: part/models.py:3453
msgid "Part Count"
msgstr "零件計數"

#: part/models.py:3454
msgid "Number of parts covered by stocktake"
msgstr "盤點涵蓋的零件數量"

#: part/models.py:3464
msgid "User who requested this stocktake report"
msgstr "請求此盤點報告的用户"

#: part/models.py:3474
msgid "Part Sale Price Break"
msgstr "零件售出價格折扣"

#: part/models.py:3586
msgid "Part Test Template"
msgstr "零件測試模板"

#: part/models.py:3612
msgid "Invalid template name - must include at least one alphanumeric character"
msgstr "模板名稱無效 - 必須包含至少一個字母或者數字"

#: part/models.py:3633 part/models.py:3803
msgid "Choices must be unique"
msgstr "選擇必須是唯一的"

#: part/models.py:3644
msgid "Test templates can only be created for testable parts"
msgstr "測試模板只能為可拆分的部件創建"

#: part/models.py:3655
msgid "Test template with the same key already exists for part"
msgstr "零件已存在具有相同主鍵的測試模板"

#: part/models.py:3672
msgid "Test Name"
msgstr "測試名"

#: part/models.py:3673
msgid "Enter a name for the test"
msgstr "輸入測試的名稱"

#: part/models.py:3679
msgid "Test Key"
msgstr "測試主鍵"

#: part/models.py:3680
msgid "Simplified key for the test"
msgstr "簡化測試主鍵"

#: part/models.py:3687
msgid "Test Description"
msgstr "測試説明"

#: part/models.py:3688
msgid "Enter description for this test"
msgstr "輸入測試的描述"

#: part/models.py:3692 report/models.py:271
msgid "Enabled"
msgstr "已啓用"

#: part/models.py:3692
msgid "Is this test enabled?"
msgstr "此測試是否已啓用？"

#: part/models.py:3697
msgid "Required"
msgstr "必須的"

#: part/models.py:3698
msgid "Is this test required to pass?"
msgstr "需要此測試才能通過嗎？"

#: part/models.py:3703
msgid "Requires Value"
msgstr "需要值"

#: part/models.py:3704
msgid "Does this test require a value when adding a test result?"
msgstr "添加測試結果時是否需要一個值？"

#: part/models.py:3709
msgid "Requires Attachment"
msgstr "需要附件"

#: part/models.py:3711
msgid "Does this test require a file attachment when adding a test result?"
msgstr "添加測試結果時是否需要文件附件？"

#: part/models.py:3717 part/models.py:3865
msgid "Choices"
msgstr "選項"

#: part/models.py:3718
msgid "Valid choices for this test (comma-separated)"
msgstr "此測試的有效選擇 (逗號分隔)"

#: part/models.py:3751
msgid "Part Parameter Template"
msgstr "零件參數模板"

#: part/models.py:3778
msgid "Checkbox parameters cannot have units"
msgstr "勾選框參數不能有單位"

#: part/models.py:3783
msgid "Checkbox parameters cannot have choices"
msgstr "複選框參數不能有選項"

#: part/models.py:3820
msgid "Parameter template name must be unique"
msgstr "參數模板名稱必須是唯一的"

#: part/models.py:3838
msgid "Parameter Name"
msgstr "參數名稱"

#: part/models.py:3845
msgid "Physical units for this parameter"
msgstr "此參數的物理單位"

#: part/models.py:3853
msgid "Parameter description"
msgstr "參數説明"

#: part/models.py:3859
msgid "Checkbox"
msgstr "勾選框"

#: part/models.py:3860
msgid "Is this parameter a checkbox?"
msgstr "此參數是否為勾選框？"

#: part/models.py:3866
msgid "Valid choices for this parameter (comma-separated)"
msgstr "此參數的有效選擇 (逗號分隔)"

#: part/models.py:3877
msgid "Selection list for this parameter"
msgstr ""

#: part/models.py:3913
msgid "Part Parameter"
msgstr "零件參數"

#: part/models.py:3939
msgid "Parameter cannot be modified - part is locked"
msgstr "參數不能被修改 - 零件被鎖定"

#: part/models.py:3977
msgid "Invalid choice for parameter value"
msgstr "無效的參數值選擇"

#: part/models.py:4028
msgid "Parent Part"
msgstr "父零件"

#: part/models.py:4036 part/models.py:4144 part/models.py:4145
msgid "Parameter Template"
msgstr "參數模板"

#: part/models.py:4042
msgid "Parameter Value"
msgstr "參數值"

#: part/models.py:4092
msgid "Part Category Parameter Template"
msgstr "零件類別參數模板"

#: part/models.py:4151
msgid "Default Value"
msgstr "默認值"

#: part/models.py:4152
msgid "Default Parameter Value"
msgstr "默認參數值"

#: part/models.py:4283
msgid "BOM item cannot be modified - assembly is locked"
msgstr "物料清單項目不能被修改 - 裝配已鎖定"

#: part/models.py:4290
msgid "BOM item cannot be modified - variant assembly is locked"
msgstr "物料清單項目不能修改 - 變體裝配已鎖定"

#: part/models.py:4300
msgid "Select parent part"
msgstr "選擇父零件"

#: part/models.py:4310
msgid "Sub part"
msgstr "子零件"

#: part/models.py:4311
msgid "Select part to be used in BOM"
msgstr "選擇要用於物料清單的零件"

#: part/models.py:4322
msgid "BOM quantity for this BOM item"
msgstr "此物料清單項目的數量"

#: part/models.py:4328
msgid "This BOM item is optional"
msgstr "此物料清單項目是可選的"

#: part/models.py:4334
msgid "This BOM item is consumable (it is not tracked in build orders)"
msgstr "這個物料清單項目是耗材 (它沒有在生產訂單中被追蹤)"

#: part/models.py:4341
msgid "Overage"
msgstr "超量"

#: part/models.py:4342
msgid "Estimated build wastage quantity (absolute or percentage)"
msgstr "估計生產物浪費量(絕對值或百分比)"

#: part/models.py:4349
msgid "BOM item reference"
msgstr "物料清單項目引用"

#: part/models.py:4357
msgid "BOM item notes"
msgstr "物料清單項目註釋"

#: part/models.py:4363
msgid "Checksum"
msgstr "校驗和"

#: part/models.py:4364
msgid "BOM line checksum"
msgstr "物料清單行校驗和"

#: part/models.py:4369
msgid "Validated"
msgstr "已驗證"

#: part/models.py:4370
msgid "This BOM item has been validated"
msgstr "此物料清單項目已驗證"

#: part/models.py:4375
msgid "Gets inherited"
msgstr "獲取繼承的"

#: part/models.py:4376
msgid "This BOM item is inherited by BOMs for variant parts"
msgstr "此物料清單項目是由物料清單繼承的變體零件"

#: part/models.py:4382
msgid "Stock items for variant parts can be used for this BOM item"
msgstr "變體零件的庫存項可以用於此物料清單項目"

#: part/models.py:4467 stock/models.py:848
msgid "Quantity must be integer value for trackable parts"
msgstr "可追蹤零件的數量必須是整數"

#: part/models.py:4477 part/models.py:4479
msgid "Sub part must be specified"
msgstr "必須指定子零件"

#: part/models.py:4624
msgid "BOM Item Substitute"
msgstr "物料清單項目替代品"

#: part/models.py:4645
msgid "Substitute part cannot be the same as the master part"
msgstr "替代品零件不能與主零件相同"

#: part/models.py:4658
msgid "Parent BOM item"
msgstr "上級物料清單項目"

#: part/models.py:4666
msgid "Substitute part"
msgstr "替代品零件"

#: part/models.py:4682
msgid "Part 1"
msgstr "零件 1"

#: part/models.py:4690
msgid "Part 2"
msgstr "零件2"

#: part/models.py:4691
msgid "Select Related Part"
msgstr "選擇相關的零件"

#: part/models.py:4698
msgid "Note for this relationship"
msgstr ""

#: part/models.py:4717
msgid "Part relationship cannot be created between a part and itself"
msgstr "零件關係不能在零件和自身之間創建"

#: part/models.py:4722
msgid "Duplicate relationship already exists"
msgstr "複製關係已經存在"

#: part/serializers.py:125
msgid "Parent Category"
msgstr "上級類別"

#: part/serializers.py:126
msgid "Parent part category"
msgstr "上級零件類別"

#: part/serializers.py:134 part/serializers.py:168
msgid "Subcategories"
msgstr "子類別"

#: part/serializers.py:207
msgid "Results"
msgstr "結果"

#: part/serializers.py:208
msgid "Number of results recorded against this template"
msgstr "根據該模板記錄的結果數量"

#: part/serializers.py:235 part/serializers.py:253 stock/serializers.py:685
msgid "Purchase currency of this stock item"
msgstr "購買此庫存項的貨幣"

#: part/serializers.py:278
msgid "Speculative Quantity"
msgstr "投機數量"

#: part/serializers.py:287
msgid "Model ID"
msgstr "型號ID"

#: part/serializers.py:313
msgid "File is not an image"
msgstr ""

#: part/serializers.py:344
msgid "Number of parts using this template"
msgstr "使用此模板的零件數"

#: part/serializers.py:489
msgid "Original Part"
msgstr "原始零件"

#: part/serializers.py:490
msgid "Select original part to duplicate"
msgstr "選擇要複製的原始零件"

#: part/serializers.py:495
msgid "Copy Image"
msgstr "複製圖片"

#: part/serializers.py:496
msgid "Copy image from original part"
msgstr "從原零件複製圖片"

#: part/serializers.py:502
msgid "Copy BOM"
msgstr "複製物料清單"

#: part/serializers.py:503
msgid "Copy bill of materials from original part"
msgstr "從原始零件複製材料清單"

#: part/serializers.py:509
msgid "Copy Parameters"
msgstr "複製參數"

#: part/serializers.py:510
msgid "Copy parameter data from original part"
msgstr "從原始零件複製參數數據"

#: part/serializers.py:516
msgid "Copy Notes"
msgstr "複製備註"

#: part/serializers.py:517
msgid "Copy notes from original part"
msgstr "從原始零件複製備註"

#: part/serializers.py:535
msgid "Initial Stock Quantity"
msgstr "初始化庫存數量"

#: part/serializers.py:537
msgid "Specify initial stock quantity for this Part. If quantity is zero, no stock is added."
msgstr "指定此零件的初始庫存數量。如果數量為零，則不添加任何庫存。"

#: part/serializers.py:544
msgid "Initial Stock Location"
msgstr "初始化庫存地點"

#: part/serializers.py:545
msgid "Specify initial stock location for this Part"
msgstr "初始化指定此零件的庫存地點"

#: part/serializers.py:562
msgid "Select supplier (or leave blank to skip)"
msgstr "選擇供應商(或為空以跳過)"

#: part/serializers.py:578
msgid "Select manufacturer (or leave blank to skip)"
msgstr "選擇製造商(或為空)"

#: part/serializers.py:588
msgid "Manufacturer part number"
msgstr "製造商零件號"

#: part/serializers.py:595
msgid "Selected company is not a valid supplier"
msgstr "所選公司不是一個有效的供應商"

#: part/serializers.py:604
msgid "Selected company is not a valid manufacturer"
msgstr "所選公司不是一個有效的製造商"

#: part/serializers.py:615
msgid "Manufacturer part matching this MPN already exists"
msgstr "與此製造商零件編號 (MPN) 的相匹配的製造商零件已存在"

#: part/serializers.py:622
msgid "Supplier part matching this SKU already exists"
msgstr "匹配此庫存單位 (SKU) 的供應商零件已存在"

#: part/serializers.py:911 part/stocktake.py:222
msgid "Category Name"
msgstr "類別名稱"

#: part/serializers.py:937
msgid "Building"
msgstr "正在生產"

#: part/serializers.py:952 part/stocktake.py:223 stock/serializers.py:1084
#: stock/serializers.py:1260 users/ruleset.py:36
msgid "Stock Items"
msgstr "庫存項"

#: part/serializers.py:955
msgid "Revisions"
msgstr "修訂"

#: part/serializers.py:958
msgid "Suppliers"
msgstr "供應商"

#: part/serializers.py:961 templates/email/low_stock_notification.html:16
#: templates/email/part_event_notification.html:17
msgid "Total Stock"
msgstr "庫存總量"

#: part/serializers.py:967
msgid "Unallocated Stock"
msgstr "未分配的庫存"

#: part/serializers.py:973
msgid "Variant Stock"
msgstr "變體庫存"

#: part/serializers.py:1006
msgid "Duplicate Part"
msgstr "重複零件"

#: part/serializers.py:1007
msgid "Copy initial data from another Part"
msgstr "從另一個零件複製初始數據"

#: part/serializers.py:1013
msgid "Initial Stock"
msgstr "初始庫存"

#: part/serializers.py:1014
msgid "Create Part with initial stock quantity"
msgstr "創建具有初始庫存數量的零件"

#: part/serializers.py:1020
msgid "Supplier Information"
msgstr "供應商信息"

#: part/serializers.py:1021
msgid "Add initial supplier information for this part"
msgstr "添加此零件的初始供應商信息"

#: part/serializers.py:1029
msgid "Copy Category Parameters"
msgstr "複製類別參數"

#: part/serializers.py:1030
msgid "Copy parameter templates from selected part category"
msgstr "從選擇的零件複製參數模版"

#: part/serializers.py:1035
msgid "Existing Image"
msgstr "現有的圖片"

#: part/serializers.py:1036
msgid "Filename of an existing part image"
msgstr "現有零件圖片的文件名"

#: part/serializers.py:1053
msgid "Image file does not exist"
msgstr "圖片不存在"

#: part/serializers.py:1257
msgid "Limit stocktake report to a particular part, and any variant parts"
msgstr "限制盤點報告到某個特定零件以及任何變體零件"

#: part/serializers.py:1267
msgid "Limit stocktake report to a particular part category, and any child categories"
msgstr "限制盤點報告到某個特定零件類別以及任何子類別"

#: part/serializers.py:1277
msgid "Limit stocktake report to a particular stock location, and any child locations"
msgstr "限制盤點報告到某個特定零件庫存地點以及任何子位置"

#: part/serializers.py:1283
msgid "Exclude External Stock"
msgstr "排除外部庫存"

#: part/serializers.py:1284
msgid "Exclude stock items in external locations"
msgstr "排除外部位置的庫存項"

#: part/serializers.py:1289
msgid "Generate Report"
msgstr "生成報告"

#: part/serializers.py:1290
msgid "Generate report file containing calculated stocktake data"
msgstr "生成包含計算出來的盤點數據的報告文件"

#: part/serializers.py:1295
msgid "Update Parts"
msgstr "更新零件"

#: part/serializers.py:1296
msgid "Update specified parts with calculated stocktake data"
msgstr "使用計算出的盤點數據更新指定零件"

#: part/serializers.py:1304
msgid "Stocktake functionality is not enabled"
msgstr "盤點功能未啓用"

#: part/serializers.py:1309
msgid "Background worker check failed"
msgstr "後台執行器檢查失敗"

#: part/serializers.py:1425
msgid "Minimum Price"
msgstr "最低價格"

#: part/serializers.py:1426
msgid "Override calculated value for minimum price"
msgstr "覆蓋已計算的最低價格值"

#: part/serializers.py:1433
msgid "Minimum price currency"
msgstr "最低價格貨幣"

#: part/serializers.py:1440
msgid "Maximum Price"
msgstr "最高價格"

#: part/serializers.py:1441
msgid "Override calculated value for maximum price"
msgstr "覆蓋已計算的最高價格值"

#: part/serializers.py:1448
msgid "Maximum price currency"
msgstr "最高價格貨幣"

#: part/serializers.py:1477
msgid "Update"
msgstr "更新"

#: part/serializers.py:1478
msgid "Update pricing for this part"
msgstr "更新這個零件的價格"

#: part/serializers.py:1501
#, python-brace-format
msgid "Could not convert from provided currencies to {default_currency}"
msgstr "無法將所提供的貨幣轉換為 {default_currency}"

#: part/serializers.py:1508
msgid "Minimum price must not be greater than maximum price"
msgstr "最低價格不能高於最高價格。"

#: part/serializers.py:1511
msgid "Maximum price must not be less than minimum price"
msgstr "最高價格不能低於最低價格"

#: part/serializers.py:1664
msgid "Select the parent assembly"
msgstr "選擇父裝配"

#: part/serializers.py:1678
msgid "Select the component part"
msgstr "選擇零部件"

#: part/serializers.py:1698
msgid "Can Build"
msgstr "可以創建"

#: part/serializers.py:1925
msgid "Select part to copy BOM from"
msgstr "選擇要複製物料清單的零件"

#: part/serializers.py:1933
msgid "Remove Existing Data"
msgstr "移除現有數據"

#: part/serializers.py:1934
msgid "Remove existing BOM items before copying"
msgstr "複製前刪除現有的物料清單項目"

#: part/serializers.py:1939
msgid "Include Inherited"
msgstr "包含繼承的"

#: part/serializers.py:1940
msgid "Include BOM items which are inherited from templated parts"
msgstr "包含從模板零件繼承的物料清單項目"

#: part/serializers.py:1945
msgid "Skip Invalid Rows"
msgstr "跳過無效行"

#: part/serializers.py:1946
msgid "Enable this option to skip invalid rows"
msgstr "啓用此選項以跳過無效行"

#: part/serializers.py:1951
msgid "Copy Substitute Parts"
msgstr "複製替代品零件"

#: part/serializers.py:1952
msgid "Copy substitute parts when duplicate BOM items"
msgstr "複製物料清單項目時複製替代品零件"

#: part/stocktake.py:218
msgid "Part ID"
msgstr "零件編號"

#: part/stocktake.py:220
msgid "Part Description"
msgstr "零件描述"

#: part/stocktake.py:221
msgid "Category ID"
msgstr "類別 ID"

#: part/stocktake.py:224
msgid "Total Quantity"
msgstr "總數量"

#: part/stocktake.py:225
msgid "Total Cost Min"
msgstr "總費用最小值"

#: part/stocktake.py:226
msgid "Total Cost Max"
msgstr "總費用最大值"

#: part/stocktake.py:284
msgid "Stocktake Report Available"
msgstr "庫存盤點報告可用"

#: part/stocktake.py:285
msgid "A new stocktake report is available for download"
msgstr "有新的庫存盤點報告可供下載"

#: part/tasks.py:38
msgid "Low stock notification"
msgstr "低庫存通知"

#: part/tasks.py:40
#, python-brace-format
msgid "The available stock for {part.name} has fallen below the configured minimum level"
msgstr "可用的 {part.name}庫存已經跌到設置的最低值"

#: plugin/api.py:78
msgid "Builtin"
msgstr ""

#: plugin/api.py:92
msgid "Mandatory"
msgstr ""

#: plugin/api.py:103
msgid "Sample"
msgstr ""

#: plugin/api.py:117 plugin/models.py:159
msgid "Installed"
msgstr "已安裝"

#: plugin/api.py:184
msgid "Plugin cannot be deleted as it is currently active"
msgstr "插件不能被刪除，因為它當前處於激活狀態"

#: plugin/base/action/api.py:34
msgid "No action specified"
msgstr "未指定操作"

#: plugin/base/action/api.py:46
msgid "No matching action found"
msgstr "未找到指定操作"

#: plugin/base/barcodes/api.py:211
msgid "No match found for barcode data"
msgstr "未找到匹配條形碼數據"

#: plugin/base/barcodes/api.py:215
msgid "Match found for barcode data"
msgstr "找到匹配條形碼數據"

#: plugin/base/barcodes/api.py:253 plugin/base/barcodes/serializers.py:77
msgid "Model is not supported"
msgstr "不支持模型"

#: plugin/base/barcodes/api.py:258
msgid "Model instance not found"
msgstr "找不到模型實例"

#: plugin/base/barcodes/api.py:287
msgid "Barcode matches existing item"
msgstr "條形碼匹配現有項目"

#: plugin/base/barcodes/api.py:418
msgid "No matching part data found"
msgstr "沒有找到匹配的零件數據"

#: plugin/base/barcodes/api.py:434
msgid "No matching supplier parts found"
msgstr "沒有找到匹配的供應商零件"

#: plugin/base/barcodes/api.py:437
msgid "Multiple matching supplier parts found"
msgstr "找到多個匹配的供應商零件"

#: plugin/base/barcodes/api.py:450 plugin/base/barcodes/api.py:677
msgid "No matching plugin found for barcode data"
msgstr "沒有找到匹配條碼數據的插件"

#: plugin/base/barcodes/api.py:460
msgid "Matched supplier part"
msgstr "匹配的供應商零件"

#: plugin/base/barcodes/api.py:528
msgid "Item has already been received"
msgstr "項目已被接收"

#: plugin/base/barcodes/api.py:576
msgid "No plugin match for supplier barcode"
msgstr ""

#: plugin/base/barcodes/api.py:625
msgid "Multiple matching line items found"
msgstr "找到多個匹配的行項目"

#: plugin/base/barcodes/api.py:628
msgid "No matching line item found"
msgstr "未找到匹配的行項目"

#: plugin/base/barcodes/api.py:674
msgid "No sales order provided"
msgstr "未提供銷售訂單"

#: plugin/base/barcodes/api.py:683
msgid "Barcode does not match an existing stock item"
msgstr "條形碼與現有的庫存項不匹配"

#: plugin/base/barcodes/api.py:699
msgid "Stock item does not match line item"
msgstr "庫存項與行項目不匹配"

#: plugin/base/barcodes/api.py:729
msgid "Insufficient stock available"
msgstr "可用庫存不足"

#: plugin/base/barcodes/api.py:742
msgid "Stock item allocated to sales order"
msgstr "庫存項已分配到銷售訂單"

#: plugin/base/barcodes/api.py:745
msgid "Not enough information"
msgstr "沒有足夠的信息"

#: plugin/base/barcodes/mixins.py:308
#: plugin/builtin/barcodes/inventree_barcode.py:101
msgid "Found matching item"
msgstr ""

#: plugin/base/barcodes/mixins.py:374
msgid "Supplier part does not match line item"
msgstr ""

#: plugin/base/barcodes/mixins.py:377
msgid "Line item is already completed"
msgstr ""

#: plugin/base/barcodes/mixins.py:414
msgid "Further information required to receive line item"
msgstr "需要更多信息以接收行項目"

#: plugin/base/barcodes/mixins.py:422
msgid "Received purchase order line item"
msgstr "已收到採購訂單行項目"

#: plugin/base/barcodes/mixins.py:429
msgid "Failed to receive line item"
msgstr ""

#: plugin/base/barcodes/serializers.py:53
msgid "Scanned barcode data"
msgstr "已掃描的條形碼數據"

#: plugin/base/barcodes/serializers.py:62
msgid "Model name to generate barcode for"
msgstr "要生成條形碼的模型名稱"

#: plugin/base/barcodes/serializers.py:67
msgid "Primary key of model object to generate barcode for"
msgstr "要生成條形碼的模型對象的主鍵"

#: plugin/base/barcodes/serializers.py:137
msgid "Purchase Order to allocate items against"
msgstr "根據採購訂單以分配項目"

#: plugin/base/barcodes/serializers.py:143
msgid "Purchase order is not open"
msgstr ""

#: plugin/base/barcodes/serializers.py:161
msgid "Supplier to receive items from"
msgstr ""

#: plugin/base/barcodes/serializers.py:168
msgid "PurchaseOrder to receive items against"
msgstr "根據採購訂單以接收項目"

#: plugin/base/barcodes/serializers.py:174
msgid "Purchase order has not been placed"
msgstr "採購訂單尚未提交"

#: plugin/base/barcodes/serializers.py:182
msgid "Location to receive items into"
msgstr "項目接收地點"

#: plugin/base/barcodes/serializers.py:188
msgid "Cannot select a structural location"
msgstr "無法選擇一個結構性位置"

#: plugin/base/barcodes/serializers.py:196
msgid "Purchase order line item to receive items against"
msgstr ""

#: plugin/base/barcodes/serializers.py:202
msgid "Automatically allocate stock items to the purchase order"
msgstr ""

#: plugin/base/barcodes/serializers.py:215
msgid "Sales Order to allocate items against"
msgstr "根據銷售訂單以分配項目"

#: plugin/base/barcodes/serializers.py:221
msgid "Sales order is not open"
msgstr ""

#: plugin/base/barcodes/serializers.py:229
msgid "Sales order line item to allocate items against"
msgstr "根據銷售訂單行項目分配項目"

#: plugin/base/barcodes/serializers.py:236
msgid "Sales order shipment to allocate items against"
msgstr "根據銷售訂單配送分配項目"

#: plugin/base/barcodes/serializers.py:242
msgid "Shipment has already been delivered"
msgstr "已交付"

#: plugin/base/barcodes/serializers.py:247
msgid "Quantity to allocate"
msgstr "待分配數"

#: plugin/base/label/label.py:39
msgid "Label printing failed"
msgstr "標籤打印失敗"

#: plugin/base/label/mixins.py:55
msgid "Error rendering label to PDF"
msgstr "渲染標籤到 PDF 時出錯"

#: plugin/base/label/mixins.py:69
msgid "Error rendering label to HTML"
msgstr "渲染標籤到 HTML 時出錯"

#: plugin/base/label/mixins.py:146
msgid "No items provided to print"
msgstr "沒有要打印的項目"

#: plugin/base/ui/serializers.py:30
msgid "Plugin Name"
msgstr ""

#: plugin/base/ui/serializers.py:34
msgid "Feature Type"
msgstr "功能類別"

#: plugin/base/ui/serializers.py:39
msgid "Feature Label"
msgstr ""

#: plugin/base/ui/serializers.py:44
msgid "Feature Title"
msgstr ""

#: plugin/base/ui/serializers.py:49
msgid "Feature Description"
msgstr ""

#: plugin/base/ui/serializers.py:54
msgid "Feature Icon"
msgstr ""

#: plugin/base/ui/serializers.py:58
msgid "Feature Options"
msgstr "特色選項"

#: plugin/base/ui/serializers.py:61
msgid "Feature Context"
msgstr ""

#: plugin/base/ui/serializers.py:64
msgid "Feature Source (javascript)"
msgstr "功能源 (javascript)"

#: plugin/builtin/barcodes/inventree_barcode.py:27
msgid "InvenTree Barcodes"
msgstr "InvenTree 條形碼"

#: plugin/builtin/barcodes/inventree_barcode.py:28
msgid "Provides native support for barcodes"
msgstr "提供條形碼本地支持"

#: plugin/builtin/barcodes/inventree_barcode.py:30
#: plugin/builtin/exporter/bom_exporter.py:61
#: plugin/builtin/exporter/inventree_exporter.py:17
#: plugin/builtin/exporter/part_parameter_exporter.py:36
#: plugin/builtin/integration/core_notifications.py:33
#: plugin/builtin/integration/currency_exchange.py:21
#: plugin/builtin/integration/part_notifications.py:25
#: plugin/builtin/labels/inventree_label.py:26
#: plugin/builtin/labels/inventree_machine.py:64
#: plugin/builtin/labels/label_sheet.py:71
#: plugin/builtin/suppliers/digikey.py:19 plugin/builtin/suppliers/lcsc.py:21
#: plugin/builtin/suppliers/mouser.py:19 plugin/builtin/suppliers/tme.py:21
msgid "InvenTree contributors"
msgstr "InvenTree 貢獻者"

#: plugin/builtin/barcodes/inventree_barcode.py:34
msgid "Internal Barcode Format"
msgstr "條形碼內部格式"

#: plugin/builtin/barcodes/inventree_barcode.py:35
msgid "Select an internal barcode format"
msgstr "選擇內部條形碼格式"

#: plugin/builtin/barcodes/inventree_barcode.py:37
msgid "JSON barcodes (human readable)"
msgstr "JSON 條形碼 (人類可讀)"

#: plugin/builtin/barcodes/inventree_barcode.py:38
msgid "Short barcodes (space optimized)"
msgstr "短條形碼 (空間優化)"

#: plugin/builtin/barcodes/inventree_barcode.py:43
msgid "Short Barcode Prefix"
msgstr "短條形碼前綴"

#: plugin/builtin/barcodes/inventree_barcode.py:45
msgid "Customize the prefix used for short barcodes, may be useful for environments with multiple InvenTree instances"
msgstr "自定義用於短條形碼的前綴，可能對有多個InvenTree實例的環境有用。"

#: plugin/builtin/exporter/bom_exporter.py:17
msgid "Levels"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:18
msgid "Number of levels to export"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:23
#: plugin/builtin/exporter/part_parameter_exporter.py:17
msgid "Stock Data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:23
#: plugin/builtin/exporter/part_parameter_exporter.py:17
msgid "Include part stock data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:27
#: plugin/builtin/exporter/part_parameter_exporter.py:21
msgid "Pricing Data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:27
#: plugin/builtin/exporter/part_parameter_exporter.py:21
msgid "Include part pricing data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:31
msgid "Supplier Data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:31
msgid "Include supplier data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:36
msgid "Manufacturer Data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:37
msgid "Include manufacturer data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:42
msgid "Substitute Data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:43
msgid "Include substitute part data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:48
msgid "Parameter Data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:49
msgid "Include part parameter data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:58
msgid "Multi-Level BOM Exporter"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:59
msgid "Provides support for exporting multi-level BOMs"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:96
msgid "BOM Level"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:102
#, python-brace-format
msgid "Substitute {n}"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:108
#, python-brace-format
msgid "Supplier {n}"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:109
#, python-brace-format
msgid "Supplier {n} SKU"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:110
#, python-brace-format
msgid "Supplier {n} MPN"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:116
#, python-brace-format
msgid "Manufacturer {n}"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:117
#, python-brace-format
msgid "Manufacturer {n} MPN"
msgstr ""

#: plugin/builtin/exporter/inventree_exporter.py:14
msgid "InvenTree Generic Exporter"
msgstr ""

#: plugin/builtin/exporter/inventree_exporter.py:15
msgid "Provides support for exporting data from InvenTree"
msgstr ""

#: plugin/builtin/exporter/part_parameter_exporter.py:33
msgid "Part Parameter Exporter"
msgstr ""

#: plugin/builtin/exporter/part_parameter_exporter.py:34
msgid "Exporter for part parameter data"
msgstr ""

#: plugin/builtin/integration/core_notifications.py:32
msgid "InvenTree Notifications"
msgstr "Inventree 通知"

#: plugin/builtin/integration/core_notifications.py:34
msgid "Integrated outgoing notification methods"
msgstr "集成的輸出通知方法"

#: plugin/builtin/integration/core_notifications.py:39
#: plugin/builtin/integration/core_notifications.py:78
msgid "Enable email notifications"
msgstr "啓用電子郵件通知"

#: plugin/builtin/integration/core_notifications.py:40
#: plugin/builtin/integration/core_notifications.py:79
msgid "Allow sending of emails for event notifications"
msgstr "允許發送事件通知郵件"

#: plugin/builtin/integration/core_notifications.py:45
msgid "Enable slack notifications"
msgstr "啓用slack通知"

#: plugin/builtin/integration/core_notifications.py:47
msgid "Allow sending of slack channel messages for event notifications"
msgstr "允許發送事件通知的 slack 頻道消息"

#: plugin/builtin/integration/core_notifications.py:53
msgid "Slack incoming webhook url"
msgstr "Slack傳入Webhook url"

#: plugin/builtin/integration/core_notifications.py:54
msgid "URL that is used to send messages to a slack channel"
msgstr "用於發送消息到slack頻道的 URL"

#: plugin/builtin/integration/core_notifications.py:164
msgid "Open link"
msgstr "打開鏈接"

#: plugin/builtin/integration/currency_exchange.py:22
msgid "InvenTree Currency Exchange"
msgstr "InvenTree 貨幣兑換"

#: plugin/builtin/integration/currency_exchange.py:23
msgid "Default currency exchange integration"
msgstr "默認貨幣兑換集成"

#: plugin/builtin/integration/part_notifications.py:24
msgid "Part Notifications"
msgstr ""

#: plugin/builtin/integration/part_notifications.py:26
msgid "Notify users about part changes"
msgstr ""

#: plugin/builtin/integration/part_notifications.py:31
msgid "Send notifications"
msgstr ""

#: plugin/builtin/integration/part_notifications.py:32
msgid "Send notifications for part changes to subscribed users"
msgstr ""

#: plugin/builtin/integration/part_notifications.py:49
msgid "Changed part notification"
msgstr ""

#: plugin/builtin/integration/part_notifications.py:59
#, python-brace-format
msgid "The part `{part.name}` has been triggered with a `{part_action}` event"
msgstr ""

#: plugin/builtin/labels/inventree_label.py:23
msgid "InvenTree PDF label printer"
msgstr "InvenTree PDF 標籤打印機"

#: plugin/builtin/labels/inventree_label.py:24
msgid "Provides native support for printing PDF labels"
msgstr "為打印 PDF 標籤提供本機支持"

#: plugin/builtin/labels/inventree_label.py:32
#: plugin/builtin/labels/label_sheet.py:77
msgid "Debug mode"
msgstr "Debug模式"

#: plugin/builtin/labels/inventree_label.py:33
#: plugin/builtin/labels/label_sheet.py:78
msgid "Enable debug mode - returns raw HTML instead of PDF"
msgstr "啓用Debug模式 - 返回原始的 HTML 而不是 PDF"

#: plugin/builtin/labels/inventree_machine.py:61
msgid "InvenTree machine label printer"
msgstr "InvenTree 設備標籤打印機"

#: plugin/builtin/labels/inventree_machine.py:62
msgid "Provides support for printing using a machine"
msgstr "提供使用設備打印的支持"

#: plugin/builtin/labels/inventree_machine.py:164
msgid "last used"
msgstr "最近使用"

#: plugin/builtin/labels/inventree_machine.py:181
msgid "Options"
msgstr "選項"

#: plugin/builtin/labels/label_sheet.py:30
msgid "Page size for the label sheet"
msgstr "標籤頁大小"

#: plugin/builtin/labels/label_sheet.py:35
msgid "Skip Labels"
msgstr "跳過標籤"

#: plugin/builtin/labels/label_sheet.py:36
msgid "Skip this number of labels when printing label sheets"
msgstr "打印標籤頁時跳過標籤的數量"

#: plugin/builtin/labels/label_sheet.py:42
msgid "Border"
msgstr "邊框"

#: plugin/builtin/labels/label_sheet.py:43
msgid "Print a border around each label"
msgstr "打印每個標籤的邊框"

#: plugin/builtin/labels/label_sheet.py:48 report/models.py:371
msgid "Landscape"
msgstr "橫屏模式"

#: plugin/builtin/labels/label_sheet.py:49
msgid "Print the label sheet in landscape mode"
msgstr "在橫屏模式下打印標籤表"

#: plugin/builtin/labels/label_sheet.py:54
msgid "Page Margin"
msgstr ""

#: plugin/builtin/labels/label_sheet.py:55
msgid "Margin around the page in mm"
msgstr ""

#: plugin/builtin/labels/label_sheet.py:68
msgid "InvenTree Label Sheet Printer"
msgstr "庫存樹標籤工作表"

#: plugin/builtin/labels/label_sheet.py:69
msgid "Arrays multiple labels onto a single sheet"
msgstr "單張紙上的組合多個標籤"

#: plugin/builtin/labels/label_sheet.py:121
msgid "Label is too large for page size"
msgstr "標籤大過頁面"

#: plugin/builtin/labels/label_sheet.py:160
msgid "No labels were generated"
msgstr "沒有生成標籤"

#: plugin/builtin/suppliers/digikey.py:16
msgid "Supplier Integration - DigiKey"
msgstr "供應商集成 - DigiKey"

#: plugin/builtin/suppliers/digikey.py:17
msgid "Provides support for scanning DigiKey barcodes"
msgstr "為掃描 DigiKey 條形碼提供支持"

#: plugin/builtin/suppliers/digikey.py:26
msgid "The Supplier which acts as 'DigiKey'"
msgstr "作為“DigiKey”的供應商。"

#: plugin/builtin/suppliers/lcsc.py:18
msgid "Supplier Integration - LCSC"
msgstr "供應商集成 - LCSC"

#: plugin/builtin/suppliers/lcsc.py:19
msgid "Provides support for scanning LCSC barcodes"
msgstr "為掃描 LCSC 條形碼提供支持"

#: plugin/builtin/suppliers/lcsc.py:27
msgid "The Supplier which acts as 'LCSC'"
msgstr "作為“LCSC”的供應商。"

#: plugin/builtin/suppliers/mouser.py:16
msgid "Supplier Integration - Mouser"
msgstr "供應商集成 - Mouser"

#: plugin/builtin/suppliers/mouser.py:17
msgid "Provides support for scanning Mouser barcodes"
msgstr "為掃描 Mouser條形碼提供支持"

#: plugin/builtin/suppliers/mouser.py:25
msgid "The Supplier which acts as 'Mouser'"
msgstr "作為“Mouser”的供應商。"

#: plugin/builtin/suppliers/tme.py:18
msgid "Supplier Integration - TME"
msgstr "供應商集成 - TME"

#: plugin/builtin/suppliers/tme.py:19
msgid "Provides support for scanning TME barcodes"
msgstr "為掃描 TME 條形碼提供支持"

#: plugin/builtin/suppliers/tme.py:27
msgid "The Supplier which acts as 'TME'"
msgstr "作為‘TME’的供應商"

#: plugin/installer.py:229 plugin/installer.py:307
msgid "Only staff users can administer plugins"
msgstr "只有員工用户可以管理插件"

#: plugin/installer.py:232
msgid "Plugin installation is disabled"
msgstr "插件安裝已禁用"

#: plugin/installer.py:269
msgid "Installed plugin successfully"
msgstr "插件安裝成功"

#: plugin/installer.py:274
#, python-brace-format
msgid "Installed plugin into {path}"
msgstr "插件安裝到 {path}"

#: plugin/installer.py:298
msgid "Plugin was not found in registry"
msgstr "在插件倉庫中找不到插件"

#: plugin/installer.py:301
msgid "Plugin is not a packaged plugin"
msgstr "插件不是一個打包的插件"

#: plugin/installer.py:304
msgid "Plugin package name not found"
msgstr "找不到插件包名稱"

#: plugin/installer.py:324
msgid "Plugin uninstalling is disabled"
msgstr "插件卸載已禁用"

#: plugin/installer.py:328
msgid "Plugin cannot be uninstalled as it is currently active"
msgstr "插件無法卸載，因為它目前處於激活狀態"

#: plugin/installer.py:332
msgid "Plugin is not installed"
msgstr ""

#: plugin/installer.py:348
msgid "Plugin installation not found"
msgstr ""

#: plugin/installer.py:363
msgid "Uninstalled plugin successfully"
msgstr "插件卸載成功"

#: plugin/models.py:38
msgid "Plugin Configuration"
msgstr "插件配置"

#: plugin/models.py:39
msgid "Plugin Configurations"
msgstr "插件配置"

#: plugin/models.py:46
msgid "Key of plugin"
msgstr "插件的鍵"

#: plugin/models.py:54
msgid "PluginName of the plugin"
msgstr "插件名稱"

#: plugin/models.py:61 plugin/serializers.py:119
msgid "Package Name"
msgstr "軟件包名"

#: plugin/models.py:63
msgid "Name of the installed package, if the plugin was installed via PIP"
msgstr "已安裝的軟件包名字，如果插件是通過 PIP 安裝的"

#: plugin/models.py:68
msgid "Is the plugin active"
msgstr "插件是否激活"

#: plugin/models.py:168
msgid "Sample plugin"
msgstr "示例插件"

#: plugin/models.py:176
msgid "Builtin Plugin"
msgstr "內置插件"

#: plugin/models.py:184
msgid "Mandatory Plugin"
msgstr ""

#: plugin/models.py:189
msgid "Package Plugin"
msgstr "軟件包插件"

#: plugin/models.py:268
msgid "Plugin"
msgstr "插件"

#: plugin/models.py:315
msgid "Method"
msgstr "方法"

#: plugin/plugin.py:312
msgid "No author found"
msgstr "未找到作者"

#: plugin/registry.py:611
#, python-brace-format
msgid "Plugin '{p}' is not compatible with the current InvenTree version {v}"
msgstr "插件 '{p}' 與當前 InvenTree 版本{v} 不兼容"

#: plugin/registry.py:614
#, python-brace-format
msgid "Plugin requires at least version {v}"
msgstr "插件所需最低版本 {v}"

#: plugin/registry.py:616
#, python-brace-format
msgid "Plugin requires at most version {v}"
msgstr "插件所需最高版本 {v}"

#: plugin/samples/integration/sample.py:52
msgid "Enable PO"
msgstr "啓用 採購功能"

#: plugin/samples/integration/sample.py:53
msgid "Enable PO functionality in InvenTree interface"
msgstr "在 InvenTree 界面中啓用採購功能"

#: plugin/samples/integration/sample.py:58
msgid "API Key"
msgstr "API密鑰"

#: plugin/samples/integration/sample.py:59
msgid "Key required for accessing external API"
msgstr "訪問外部 API 所需的密鑰"

#: plugin/samples/integration/sample.py:63
msgid "Numerical"
msgstr "數字化"

#: plugin/samples/integration/sample.py:64
msgid "A numerical setting"
msgstr "數值設置"

#: plugin/samples/integration/sample.py:69
msgid "Choice Setting"
msgstr "選擇設置"

#: plugin/samples/integration/sample.py:70
msgid "A setting with multiple choices"
msgstr "帶有多個選項的設置"

#: plugin/samples/integration/sample_currency_exchange.py:15
msgid "Sample currency exchange plugin"
msgstr "貨幣兑換插件示例"

#: plugin/samples/integration/sample_currency_exchange.py:18
msgid "InvenTree Contributors"
msgstr "InvenTree 貢獻者"

#: plugin/samples/integration/user_interface_sample.py:27
msgid "Enable Part Panels"
msgstr "啓用零件面板"

#: plugin/samples/integration/user_interface_sample.py:28
msgid "Enable custom panels for Part views"
msgstr "啓用自定義面板來查看部件"

#: plugin/samples/integration/user_interface_sample.py:33
msgid "Enable Purchase Order Panels"
msgstr "啓用採購訂單面板"

#: plugin/samples/integration/user_interface_sample.py:34
msgid "Enable custom panels for Purchase Order views"
msgstr "啓用自定義面板以查看購買訂單"

#: plugin/samples/integration/user_interface_sample.py:39
msgid "Enable Broken Panels"
msgstr "啓用破損面板"

#: plugin/samples/integration/user_interface_sample.py:40
msgid "Enable broken panels for testing"
msgstr "啓用損壞的面板來測試"

#: plugin/samples/integration/user_interface_sample.py:45
msgid "Enable Dynamic Panel"
msgstr "啓用動態面板"

#: plugin/samples/integration/user_interface_sample.py:46
msgid "Enable dynamic panels for testing"
msgstr "啓用動態面板來測試"

#: plugin/samples/integration/user_interface_sample.py:98
msgid "Part Panel"
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:131
msgid "Broken Dashboard Item"
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:133
msgid "This is a broken dashboard item - it will not render!"
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:139
msgid "Sample Dashboard Item"
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:141
msgid "This is a sample dashboard item. It renders a simple string of HTML content."
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:147
msgid "Context Dashboard Item"
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:161
msgid "Admin Dashboard Item"
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:162
msgid "This is an admin-only dashboard item."
msgstr ""

#: plugin/serializers.py:86
msgid "Source File"
msgstr ""

#: plugin/serializers.py:87
msgid "Path to the source file for admin integration"
msgstr ""

#: plugin/serializers.py:94
msgid "Optional context data for the admin integration"
msgstr ""

#: plugin/serializers.py:110
msgid "Source URL"
msgstr "源URL"

#: plugin/serializers.py:112
msgid "Source for the package - this can be a custom registry or a VCS path"
msgstr "軟件包的來源 - 這可以是自定義註冊表或 VCS 路徑"

#: plugin/serializers.py:121
msgid "Name for the Plugin Package - can also contain a version indicator"
msgstr "插件包名稱 - 也可以包含版本指示器"

#: plugin/serializers.py:128
msgid "Version"
msgstr "版本"

#: plugin/serializers.py:130
msgid "Version specifier for the plugin. Leave blank for latest version."
msgstr "插件版本説明。新版請留白。"

#: plugin/serializers.py:135
msgid "Confirm plugin installation"
msgstr "確認插件安裝"

#: plugin/serializers.py:137
msgid "This will install this plugin now into the current instance. The instance will go into maintenance."
msgstr "這將把這個插件安裝到當前實例中。這個實例將進行維護。"

#: plugin/serializers.py:150
msgid "Installation not confirmed"
msgstr "安裝尚未確認"

#: plugin/serializers.py:152
msgid "Either packagename of URL must be provided"
msgstr "必須提供軟件包名稱或者URL"

#: plugin/serializers.py:188
msgid "Full reload"
msgstr "完全重載"

#: plugin/serializers.py:189
msgid "Perform a full reload of the plugin registry"
msgstr "執行插件庫的完整重載"

#: plugin/serializers.py:195
msgid "Force reload"
msgstr "強制重載"

#: plugin/serializers.py:197
msgid "Force a reload of the plugin registry, even if it is already loaded"
msgstr "強制重載插件庫，即使已經加載"

#: plugin/serializers.py:204
msgid "Collect plugins"
msgstr "收集插件"

#: plugin/serializers.py:205
msgid "Collect plugins and add them to the registry"
msgstr "收集插件並添加到註冊表中"

#: plugin/serializers.py:232
msgid "Activate Plugin"
msgstr "激活插件"

#: plugin/serializers.py:233
msgid "Activate this plugin"
msgstr "激活此插件"

#: plugin/serializers.py:253
msgid "Delete configuration"
msgstr "刪除配置"

#: plugin/serializers.py:254
msgid "Delete the plugin configuration from the database"
msgstr "從數據庫中刪除插件配置"

#: report/api.py:44 report/serializers.py:98 report/serializers.py:148
msgid "Items"
msgstr "項目"

#: report/api.py:121
msgid "Plugin not found"
msgstr "插件未找到"

#: report/api.py:123
msgid "Plugin is not active"
msgstr "插件未激活"

#: report/api.py:125
msgid "Plugin does not support label printing"
msgstr "插件不支持標籤打印"

#: report/api.py:173
msgid "Invalid label dimensions"
msgstr "無效的標籤尺寸"

#: report/api.py:188 report/api.py:267
msgid "No valid items provided to template"
msgstr "沒有有效的項目提供到模板"

#: report/helpers.py:43
msgid "A4"
msgstr "A4"

#: report/helpers.py:44
msgid "A3"
msgstr "A3"

#: report/helpers.py:45
msgid "Legal"
msgstr "法律"

#: report/helpers.py:46
msgid "Letter"
msgstr "字母"

#: report/models.py:117
msgid "Template file with this name already exists"
msgstr "已存在具有此名稱的模板"

#: report/models.py:204
msgid "Template name"
msgstr "模版名稱"

#: report/models.py:210
msgid "Template description"
msgstr "模板説明"

#: report/models.py:216
msgid "Revision number (auto-increments)"
msgstr "修訂編號 (自動增量)"

#: report/models.py:222
msgid "Attach to Model on Print"
msgstr "打印時附加到模型"

#: report/models.py:224
msgid "Save report output as an attachment against linked model instance when printing"
msgstr "打印時將報告輸出保存為附件與鏈接模型實例"

#: report/models.py:265
msgid "Filename Pattern"
msgstr "文件名樣式"

#: report/models.py:266
msgid "Pattern for generating filenames"
msgstr "生成文件名模式"

#: report/models.py:271
msgid "Template is enabled"
msgstr "模板已啓用"

#: report/models.py:278
msgid "Target model type for template"
msgstr "模版的目標模型類型"

#: report/models.py:298
msgid "Filters"
msgstr "篩選器"

#: report/models.py:299
msgid "Template query filters (comma-separated list of key=value pairs)"
msgstr "模版查詢篩選器 (逗號分隔的鍵值對列表)"

#: report/models.py:358 report/models.py:564
msgid "Template file"
msgstr "模板包文件"

#: report/models.py:366
msgid "Page size for PDF reports"
msgstr "PDF 報告的頁面大小"

#: report/models.py:372
msgid "Render report in landscape orientation"
msgstr "橫向渲染報告"

#: report/models.py:486
#, python-brace-format
msgid "Report generated from template {self.name}"
msgstr ""

#: report/models.py:511
msgid "Error generating report"
msgstr ""

#: report/models.py:570
msgid "Width [mm]"
msgstr "寬度 [mm]"

#: report/models.py:571
msgid "Label width, specified in mm"
msgstr "標籤寬度，以毫米為單位。"

#: report/models.py:577
msgid "Height [mm]"
msgstr "高度 [mm]"

#: report/models.py:578
msgid "Label height, specified in mm"
msgstr "標籤高度，以毫米為單位。"

#: report/models.py:688
msgid "Error printing labels"
msgstr ""

#: report/models.py:707
msgid "Snippet"
msgstr "代碼片段"

#: report/models.py:708
msgid "Report snippet file"
msgstr "報告代碼片段文件"

#: report/models.py:715
msgid "Snippet file description"
msgstr "代碼片段文件描述"

#: report/models.py:733
msgid "Asset"
msgstr "資產"

#: report/models.py:734
msgid "Report asset file"
msgstr "報告資產文件"

#: report/models.py:741
msgid "Asset file description"
msgstr "資產文件描述"

#: report/serializers.py:91
msgid "Select report template"
msgstr "選擇報表模板"

#: report/serializers.py:99 report/serializers.py:149
msgid "List of item primary keys to include in the report"
msgstr "要包含在報告中的項目主鍵列表"

#: report/serializers.py:132
msgid "Select label template"
msgstr "選擇標籤模板"

#: report/serializers.py:140
msgid "Printing Plugin"
msgstr "打印插件"

#: report/serializers.py:141
msgid "Select plugin to use for label printing"
msgstr "選擇用於標籤打印的插件"

#: report/templates/label/part_label.html:31
#: report/templates/label/stockitem_qr.html:21
#: report/templates/label/stocklocation_qr.html:20
msgid "QR Code"
msgstr "二維碼"

#: report/templates/label/part_label_code128.html:31
#: report/templates/label/stocklocation_qr_and_text.html:31
msgid "QR code"
msgstr "二維碼"

#: report/templates/report/inventree_bill_of_materials_report.html:100
msgid "Bill of Materials"
msgstr "物料清單"

#: report/templates/report/inventree_bill_of_materials_report.html:133
msgid "Materials needed"
msgstr "所需材料"

#: report/templates/report/inventree_build_order_report.html:98
#: report/templates/report/inventree_purchase_order_report.html:40
#: report/templates/report/inventree_sales_order_report.html:40
#: report/templates/report/inventree_sales_order_shipment_report.html:37
#: report/templates/report/inventree_test_report.html:84
#: report/templates/report/inventree_test_report.html:162
msgid "Part image"
msgstr "零件圖像"

#: report/templates/report/inventree_build_order_report.html:121
msgid "Issued"
msgstr "已派發"

#: report/templates/report/inventree_build_order_report.html:146
msgid "Required For"
msgstr "需要給"

#: report/templates/report/inventree_purchase_order_report.html:15
msgid "Supplier was deleted"
msgstr "供應商已刪除"

#: report/templates/report/inventree_purchase_order_report.html:30
#: report/templates/report/inventree_sales_order_report.html:30
msgid "Unit Price"
msgstr "單位價格"

#: report/templates/report/inventree_purchase_order_report.html:55
#: report/templates/report/inventree_return_order_report.html:48
#: report/templates/report/inventree_sales_order_report.html:55
msgid "Extra Line Items"
msgstr "額外行項目"

#: report/templates/report/inventree_purchase_order_report.html:72
#: report/templates/report/inventree_sales_order_report.html:72
msgid "Total"
msgstr "總計"

#: report/templates/report/inventree_return_order_report.html:25
#: report/templates/report/inventree_sales_order_shipment_report.html:45
#: report/templates/report/inventree_test_report.html:88 stock/models.py:1006
#: stock/serializers.py:163
msgid "Serial Number"
msgstr "序列號"

#: report/templates/report/inventree_sales_order_shipment_report.html:23
msgid "Allocations"
msgstr "分配"

#: report/templates/report/inventree_sales_order_shipment_report.html:47
msgid "Batch"
msgstr "隊列"

#: report/templates/report/inventree_stock_location_report.html:97
msgid "Stock location items"
msgstr "庫存地點項目"

#: report/templates/report/inventree_test_report.html:21
msgid "Stock Item Test Report"
msgstr "庫存項測試報告"

#: report/templates/report/inventree_test_report.html:97
msgid "Test Results"
msgstr "測試結果"

#: report/templates/report/inventree_test_report.html:102
msgid "Test"
msgstr "測試"

#: report/templates/report/inventree_test_report.html:129
msgid "Pass"
msgstr "通過"

#: report/templates/report/inventree_test_report.html:131
msgid "Fail"
msgstr "失敗"

#: report/templates/report/inventree_test_report.html:138
msgid "No result (required)"
msgstr "無結果 (必填)"

#: report/templates/report/inventree_test_report.html:140
msgid "No result"
msgstr "沒有結果"

#: report/templates/report/inventree_test_report.html:153
#: stock/serializers.py:668
msgid "Installed Items"
msgstr "已安裝的項目"

#: report/templates/report/inventree_test_report.html:167
msgid "Serial"
msgstr "系列"

#: report/templatetags/report.py:144
msgid "Asset file does not exist"
msgstr "資產文件不存在"

#: report/templatetags/report.py:201 report/templatetags/report.py:277
msgid "Image file not found"
msgstr "找不到圖片文件"

#: report/templatetags/report.py:302
msgid "part_image tag requires a Part instance"
msgstr "parpart_image 標籤需要一個零件實例"

#: report/templatetags/report.py:349
msgid "company_image tag requires a Company instance"
msgstr "公司_圖片標籤需要一個公司實例"

#: stock/api.py:255
msgid "Filter by location depth"
msgstr "按位置深度篩選"

#: stock/api.py:275
msgid "Filter by top-level locations"
msgstr "按頂級位置篩選"

#: stock/api.py:290
msgid "Include sub-locations in filtered results"
msgstr "在篩選結果中包含子地點"

#: stock/api.py:311 stock/serializers.py:1256
msgid "Parent Location"
msgstr "上級地點"

#: stock/api.py:312
msgid "Filter by parent location"
msgstr "按上級位置篩選"

#: stock/api.py:554
msgid "Part name (case insensitive)"
msgstr ""

#: stock/api.py:560
msgid "Part name contains (case insensitive)"
msgstr ""

#: stock/api.py:566
msgid "Part name (regex)"
msgstr ""

#: stock/api.py:571
msgid "Part IPN (case insensitive)"
msgstr ""

#: stock/api.py:577
msgid "Part IPN contains (case insensitive)"
msgstr ""

#: stock/api.py:583
msgid "Part IPN (regex)"
msgstr ""

#: stock/api.py:595
msgid "Minimum stock"
msgstr ""

#: stock/api.py:599
msgid "Maximum stock"
msgstr ""

#: stock/api.py:602
msgid "Status Code"
msgstr "狀態代碼"

#: stock/api.py:642
msgid "External Location"
msgstr "外部地點"

#: stock/api.py:741
msgid "Consumed by Build Order"
msgstr ""

#: stock/api.py:751
msgid "Installed in other stock item"
msgstr ""

#: stock/api.py:840
msgid "Part Tree"
msgstr "零件樹"

#: stock/api.py:862
msgid "Updated before"
msgstr ""

#: stock/api.py:866
msgid "Updated after"
msgstr ""

#: stock/api.py:870
msgid "Stocktake Before"
msgstr ""

#: stock/api.py:874
msgid "Stocktake After"
msgstr ""

#: stock/api.py:879
msgid "Expiry date before"
msgstr "過期日期前"

#: stock/api.py:883
msgid "Expiry date after"
msgstr "過期日期後"

#: stock/api.py:886 stock/serializers.py:673
msgid "Stale"
msgstr "過期"

#: stock/api.py:987
msgid "Quantity is required"
msgstr "請先輸入數量"

#: stock/api.py:992
msgid "Valid part must be supplied"
msgstr "必須提供有效的零件"

#: stock/api.py:1019
msgid "The given supplier part does not exist"
msgstr "給定的供應商零件不存在"

#: stock/api.py:1029
msgid "The supplier part has a pack size defined, but flag use_pack_size not set"
msgstr "供應商零件有定義的包裝大小，但 use_pack_size 標誌未設置"

#: stock/api.py:1056
msgid "Serial numbers cannot be supplied for a non-trackable part"
msgstr "不能為不可跟蹤的零件提供序列號"

#: stock/models.py:70
msgid "Stock Location type"
msgstr "庫存地點類型"

#: stock/models.py:71
msgid "Stock Location types"
msgstr "庫存地點類型"

#: stock/models.py:97
msgid "Default icon for all locations that have no icon set (optional)"
msgstr "為所有沒有圖標的位置設置默認圖標(可選)"

#: stock/models.py:155 stock/models.py:968
msgid "Stock Location"
msgstr "庫存地點"

#: stock/models.py:156 users/ruleset.py:35
msgid "Stock Locations"
msgstr "庫存地點"

#: stock/models.py:205 stock/models.py:1133
msgid "Owner"
msgstr "所有者"

#: stock/models.py:206 stock/models.py:1134
msgid "Select Owner"
msgstr "選擇所有者"

#: stock/models.py:214
msgid "Stock items may not be directly located into a structural stock locations, but may be located to child locations."
msgstr "庫存項可能不直接位於結構庫存地點，但可能位於其子地點。"

#: stock/models.py:221 users/models.py:499
msgid "External"
msgstr "外部"

#: stock/models.py:222
msgid "This is an external stock location"
msgstr "這是一個外部庫存地點"

#: stock/models.py:228
msgid "Location type"
msgstr "位置類型"

#: stock/models.py:232
msgid "Stock location type of this location"
msgstr "該位置的庫存地點類型"

#: stock/models.py:304
msgid "You cannot make this stock location structural because some stock items are already located into it!"
msgstr "您不能將此庫存地點設置為結構性，因為某些庫存項已經位於它！"

#: stock/models.py:562
msgid "Part must be specified"
msgstr ""

#: stock/models.py:827
msgid "Stock items cannot be located into structural stock locations!"
msgstr "庫存項不能存放在結構性庫存地點！"

#: stock/models.py:854 stock/serializers.py:516
msgid "Stock item cannot be created for virtual parts"
msgstr "無法為虛擬零件創建庫存項"

#: stock/models.py:871
#, python-brace-format
msgid "Part type ('{self.supplier_part.part}') must be {self.part}"
msgstr "零件類型 ('{self.supplier_part.part}') 必須為 {self.part}"

#: stock/models.py:881 stock/models.py:894
msgid "Quantity must be 1 for item with a serial number"
msgstr "有序列號的項目的數量必須是1"

#: stock/models.py:884
msgid "Serial number cannot be set if quantity greater than 1"
msgstr "如果數量大於1，則不能設置序列號"

#: stock/models.py:906
msgid "Item cannot belong to itself"
msgstr "項目不能屬於其自身"

#: stock/models.py:911
msgid "Item must have a build reference if is_building=True"
msgstr "如果is_building=True，則項必須具有構建引用"

#: stock/models.py:924
msgid "Build reference does not point to the same part object"
msgstr "構建引用未指向同一零件對象"

#: stock/models.py:938
msgid "Parent Stock Item"
msgstr "母庫存項目"

#: stock/models.py:950
msgid "Base part"
msgstr "基礎零件"

#: stock/models.py:960
msgid "Select a matching supplier part for this stock item"
msgstr "為此庫存項目選擇匹配的供應商零件"

#: stock/models.py:972
msgid "Where is this stock item located?"
msgstr "這個庫存物品在哪裏？"

#: stock/models.py:980 stock/serializers.py:1667
msgid "Packaging this stock item is stored in"
msgstr "包裝此庫存物品存儲在"

#: stock/models.py:986
msgid "Installed In"
msgstr "安裝於"

#: stock/models.py:991
msgid "Is this item installed in another item?"
msgstr "此項目是否安裝在另一個項目中？"

#: stock/models.py:1010
msgid "Serial number for this item"
msgstr "此項目的序列號"

#: stock/models.py:1027 stock/serializers.py:1652
msgid "Batch code for this stock item"
msgstr "此庫存項的批號"

#: stock/models.py:1032
msgid "Stock Quantity"
msgstr "庫存數量"

#: stock/models.py:1042
msgid "Source Build"
msgstr "源代碼構建"

#: stock/models.py:1045
msgid "Build for this stock item"
msgstr "為此庫存項目構建"

#: stock/models.py:1052
msgid "Consumed By"
msgstr "消費者"

#: stock/models.py:1055
msgid "Build order which consumed this stock item"
msgstr "構建消耗此庫存項的生產訂單"

#: stock/models.py:1064
msgid "Source Purchase Order"
msgstr "採購訂單來源"

#: stock/models.py:1068
msgid "Purchase order for this stock item"
msgstr "此庫存商品的採購訂單"

#: stock/models.py:1074
msgid "Destination Sales Order"
msgstr "目的地銷售訂單"

#: stock/models.py:1085
msgid "Expiry date for stock item. Stock will be considered expired after this date"
msgstr "庫存物品的到期日。在此日期之後，庫存將被視為過期"

#: stock/models.py:1103
msgid "Delete on deplete"
msgstr "耗盡時刪除"

#: stock/models.py:1104
msgid "Delete this Stock Item when stock is depleted"
msgstr "當庫存耗盡時刪除此庫存項"

#: stock/models.py:1125
msgid "Single unit purchase price at time of purchase"
msgstr "購買時一個單位的價格"

#: stock/models.py:1156
msgid "Converted to part"
msgstr "轉換為零件"

#: stock/models.py:1721
msgid "Part is not set as trackable"
msgstr "零件未設置為可跟蹤"

#: stock/models.py:1727
msgid "Quantity must be integer"
msgstr "數量必須是整數"

#: stock/models.py:1735
#, python-brace-format
msgid "Quantity must not exceed available stock quantity ({self.quantity})"
msgstr "數量不得超過現有庫存量 ({self.quantity})"

#: stock/models.py:1741
msgid "Serial numbers must be provided as a list"
msgstr ""

#: stock/models.py:1746
msgid "Quantity does not match serial numbers"
msgstr "數量不匹配序列號"

#: stock/models.py:1868 stock/models.py:2777
msgid "Test template does not exist"
msgstr "測試模板不存在"

#: stock/models.py:1886
msgid "Stock item has been assigned to a sales order"
msgstr "庫存項已分配到銷售訂單"

#: stock/models.py:1890
msgid "Stock item is installed in another item"
msgstr "庫存項已安裝在另一個項目中"

#: stock/models.py:1893
msgid "Stock item contains other items"
msgstr "庫存項包含其他項目"

#: stock/models.py:1896
msgid "Stock item has been assigned to a customer"
msgstr "庫存項已分配給客户"

#: stock/models.py:1899 stock/models.py:2073
msgid "Stock item is currently in production"
msgstr "庫存項目前正在生產"

#: stock/models.py:1902
msgid "Serialized stock cannot be merged"
msgstr "序列化的庫存不能合併"

#: stock/models.py:1909 stock/serializers.py:1545
msgid "Duplicate stock items"
msgstr "複製庫存項"

#: stock/models.py:1913
msgid "Stock items must refer to the same part"
msgstr "庫存項必須指相同零件"

#: stock/models.py:1921
msgid "Stock items must refer to the same supplier part"
msgstr "庫存項必須是同一供應商的零件"

#: stock/models.py:1926
msgid "Stock status codes must match"
msgstr "庫存狀態碼必須匹配"

#: stock/models.py:2196
msgid "StockItem cannot be moved as it is not in stock"
msgstr "庫存項不能移動，因為它沒有庫存"

#: stock/models.py:2678
msgid "Stock Item Tracking"
msgstr "庫存項跟蹤"

#: stock/models.py:2709
msgid "Entry notes"
msgstr "條目註釋"

#: stock/models.py:2749
msgid "Stock Item Test Result"
msgstr "庫存項測試結果"

#: stock/models.py:2780
msgid "Value must be provided for this test"
msgstr "必須為此測試提供值"

#: stock/models.py:2784
msgid "Attachment must be uploaded for this test"
msgstr "測試附件必須上傳"

#: stock/models.py:2789
msgid "Invalid value for this test"
msgstr "此測試的值無效"

#: stock/models.py:2813
msgid "Test result"
msgstr "測試結果"

#: stock/models.py:2820
msgid "Test output value"
msgstr "測試輸出值"

#: stock/models.py:2828 stock/serializers.py:259
msgid "Test result attachment"
msgstr "測驗結果附件"

#: stock/models.py:2832
msgid "Test notes"
msgstr "測試備註"

#: stock/models.py:2840
msgid "Test station"
msgstr "測試站"

#: stock/models.py:2841
msgid "The identifier of the test station where the test was performed"
msgstr "進行測試的測試站的標識符"

#: stock/models.py:2847
msgid "Started"
msgstr "已開始"

#: stock/models.py:2848
msgid "The timestamp of the test start"
msgstr "測試開始的時間戳"

#: stock/models.py:2854
msgid "Finished"
msgstr "已完成"

#: stock/models.py:2855
msgid "The timestamp of the test finish"
msgstr "測試結束的時間戳"

#: stock/serializers.py:84
msgid "Generated batch code"
msgstr "生成批量代碼"

#: stock/serializers.py:93
msgid "Select build order"
msgstr "選擇生產訂單"

#: stock/serializers.py:102
msgid "Select stock item to generate batch code for"
msgstr "選擇要生成批量代碼的庫存項"

#: stock/serializers.py:111
msgid "Select location to generate batch code for"
msgstr "選擇要生成批量代碼的位置"

#: stock/serializers.py:120
msgid "Select part to generate batch code for"
msgstr "選擇要生成批量代碼的零件"

#: stock/serializers.py:129
msgid "Select purchase order"
msgstr "選擇採購訂單"

#: stock/serializers.py:136
msgid "Enter quantity for batch code"
msgstr "輸入批量代碼的數量"

#: stock/serializers.py:162
msgid "Generated serial number"
msgstr "生成的序列號"

#: stock/serializers.py:172
msgid "Select part to generate serial number for"
msgstr "選擇要生成序列號的零件"

#: stock/serializers.py:180
msgid "Quantity of serial numbers to generate"
msgstr "要生成的序列號的數量"

#: stock/serializers.py:248
msgid "Test template for this result"
msgstr "此結果的測試模板"

#: stock/serializers.py:272
msgid "Template ID or test name must be provided"
msgstr "必須提供模板 ID 或測試名稱"

#: stock/serializers.py:304
msgid "The test finished time cannot be earlier than the test started time"
msgstr "測試完成時間不能早於測試開始時間"

#: stock/serializers.py:341
msgid "Serial number is too large"
msgstr "序列號太大"

#: stock/serializers.py:487
msgid "Parent Item"
msgstr "父項"

#: stock/serializers.py:488
msgid "Parent stock item"
msgstr "父庫存項"

#: stock/serializers.py:508
msgid "Use pack size when adding: the quantity defined is the number of packs"
msgstr "添加時使用包裝尺寸：定義的數量是包裝的數量"

#: stock/serializers.py:618
msgid "Supplier Part Number"
msgstr "供應商零件編號"

#: stock/serializers.py:665 users/models.py:193
msgid "Expired"
msgstr "已過期"

#: stock/serializers.py:671
msgid "Child Items"
msgstr "子項目"

#: stock/serializers.py:675
msgid "Tracking Items"
msgstr "跟蹤項目"

#: stock/serializers.py:681
msgid "Purchase price of this stock item, per unit or pack"
msgstr "此庫存商品的購買價格，單位或包裝"

#: stock/serializers.py:719
msgid "Enter number of stock items to serialize"
msgstr "輸入要序列化的庫存項目數量"

#: stock/serializers.py:732
#, python-brace-format
msgid "Quantity must not exceed available stock quantity ({q})"
msgstr "數量不得超過現有庫存量 ({q})"

#: stock/serializers.py:739
msgid "Enter serial numbers for new items"
msgstr "輸入新項目的序列號"

#: stock/serializers.py:750 stock/serializers.py:1502 stock/serializers.py:1790
msgid "Destination stock location"
msgstr "目標庫存位置"

#: stock/serializers.py:757
msgid "Optional note field"
msgstr "可選註釋字段"

#: stock/serializers.py:767
msgid "Serial numbers cannot be assigned to this part"
msgstr "此零件不能分配序列號"

#: stock/serializers.py:787
msgid "Serial numbers already exist"
msgstr "序列號已存在"

#: stock/serializers.py:826
msgid "Select stock item to install"
msgstr "選擇要安裝的庫存項目"

#: stock/serializers.py:833
msgid "Quantity to Install"
msgstr "安裝數量"

#: stock/serializers.py:834
msgid "Enter the quantity of items to install"
msgstr "輸入要安裝的項目數量"

#: stock/serializers.py:839 stock/serializers.py:919 stock/serializers.py:1049
#: stock/serializers.py:1101
msgid "Add transaction note (optional)"
msgstr "添加交易記錄 (可選)"

#: stock/serializers.py:847
msgid "Quantity to install must be at least 1"
msgstr "安裝數量必須至少為1"

#: stock/serializers.py:855
msgid "Stock item is unavailable"
msgstr "庫存項不可用"

#: stock/serializers.py:866
msgid "Selected part is not in the Bill of Materials"
msgstr "所選零件不在物料清單中"

#: stock/serializers.py:879
msgid "Quantity to install must not exceed available quantity"
msgstr "安裝數量不得超過可用數量"

#: stock/serializers.py:914
msgid "Destination location for uninstalled item"
msgstr "已卸載項目的目標位置"

#: stock/serializers.py:949
msgid "Select part to convert stock item into"
msgstr "選擇要將庫存項目轉換為的零件"

#: stock/serializers.py:962
msgid "Selected part is not a valid option for conversion"
msgstr "所選零件不是有效的轉換選項"

#: stock/serializers.py:979
msgid "Cannot convert stock item with assigned SupplierPart"
msgstr "無法轉換已分配供應商零件的庫存項"

#: stock/serializers.py:1013
msgid "Stock item status code"
msgstr "庫存項狀態代碼"

#: stock/serializers.py:1042
msgid "Destination location for returned item"
msgstr "退回物品的目的地位置"

#: stock/serializers.py:1085
msgid "Select stock items to change status"
msgstr "選擇要更改狀態的庫存項目"

#: stock/serializers.py:1091
msgid "No stock items selected"
msgstr "未選擇庫存商品"

#: stock/serializers.py:1185 stock/serializers.py:1262
msgid "Sublocations"
msgstr "轉租"

#: stock/serializers.py:1257
msgid "Parent stock location"
msgstr "上級庫存地點"

#: stock/serializers.py:1374
msgid "Part must be salable"
msgstr "零件必須可銷售"

#: stock/serializers.py:1378
msgid "Item is allocated to a sales order"
msgstr "物料已分配到銷售訂單"

#: stock/serializers.py:1382
msgid "Item is allocated to a build order"
msgstr "項目被分配到生產訂單中"

#: stock/serializers.py:1406
msgid "Customer to assign stock items"
msgstr "客户分配庫存項目"

#: stock/serializers.py:1412
msgid "Selected company is not a customer"
msgstr "所選公司不是客户"

#: stock/serializers.py:1420
msgid "Stock assignment notes"
msgstr "庫存分配説明"

#: stock/serializers.py:1430 stock/serializers.py:1695
msgid "A list of stock items must be provided"
msgstr "必須提供庫存物品清單"

#: stock/serializers.py:1509
msgid "Stock merging notes"
msgstr "庫存合併説明"

#: stock/serializers.py:1514
msgid "Allow mismatched suppliers"
msgstr "允許不匹配的供應商"

#: stock/serializers.py:1515
msgid "Allow stock items with different supplier parts to be merged"
msgstr "允許合併具有不同供應商零件的庫存項目"

#: stock/serializers.py:1520
msgid "Allow mismatched status"
msgstr "允許不匹配的狀態"

#: stock/serializers.py:1521
msgid "Allow stock items with different status codes to be merged"
msgstr "允許合併具有不同狀態代碼的庫存項目"

#: stock/serializers.py:1531
msgid "At least two stock items must be provided"
msgstr "必須提供至少兩件庫存物品"

#: stock/serializers.py:1598
msgid "No Change"
msgstr "無更改"

#: stock/serializers.py:1627
msgid "StockItem primary key value"
msgstr "庫存項主鍵值"

#: stock/serializers.py:1639
msgid "Stock item is not in stock"
msgstr ""

#: stock/serializers.py:1685
msgid "Stock transaction notes"
msgstr "庫存交易記錄"

#: stock/serializers.py:1831
msgid "Next Serial Number"
msgstr ""

#: stock/serializers.py:1837
msgid "Previous Serial Number"
msgstr ""

#: stock/status_codes.py:11
msgid "OK"
msgstr "OK"

#: stock/status_codes.py:12
msgid "Attention needed"
msgstr "需要關注"

#: stock/status_codes.py:13
msgid "Damaged"
msgstr "破損"

#: stock/status_codes.py:14
msgid "Destroyed"
msgstr "銷燬"

#: stock/status_codes.py:15
msgid "Rejected"
msgstr "拒絕"

#: stock/status_codes.py:19
msgid "Quarantined"
msgstr "隔離"

#: stock/status_codes.py:44
msgid "Legacy stock tracking entry"
msgstr "舊庫存跟蹤條目"

#: stock/status_codes.py:46
msgid "Stock item created"
msgstr "庫存項已創建"

#: stock/status_codes.py:49
msgid "Edited stock item"
msgstr "已編輯庫存項"

#: stock/status_codes.py:50
msgid "Assigned serial number"
msgstr "已分配序列號"

#: stock/status_codes.py:53
msgid "Stock counted"
msgstr "庫存計數"

#: stock/status_codes.py:54
msgid "Stock manually added"
msgstr "已手動添加庫存"

#: stock/status_codes.py:55
msgid "Stock manually removed"
msgstr "已手動刪除庫存"

#: stock/status_codes.py:58
msgid "Location changed"
msgstr "地點已更改"

#: stock/status_codes.py:59
msgid "Stock updated"
msgstr "庫存已更新"

#: stock/status_codes.py:62
msgid "Installed into assembly"
msgstr "已安裝到裝配中"

#: stock/status_codes.py:63
msgid "Removed from assembly"
msgstr "已從裝配中刪除"

#: stock/status_codes.py:65
msgid "Installed component item"
msgstr "已安裝組件項"

#: stock/status_codes.py:66
msgid "Removed component item"
msgstr "已刪除組件項"

#: stock/status_codes.py:69
msgid "Split from parent item"
msgstr "從上級項拆分"

#: stock/status_codes.py:70
msgid "Split child item"
msgstr "拆分子項"

#: stock/status_codes.py:73
msgid "Merged stock items"
msgstr "合併的庫存項"

#: stock/status_codes.py:76
msgid "Converted to variant"
msgstr "轉換為變體"

#: stock/status_codes.py:79
msgid "Build order output created"
msgstr "已創建生產訂單產出"

#: stock/status_codes.py:80
msgid "Build order output completed"
msgstr "生產訂單已出產"

#: stock/status_codes.py:81
msgid "Build order output rejected"
msgstr "生產訂單產出被拒絕"

#: stock/status_codes.py:82
msgid "Consumed by build order"
msgstr "被工單消耗的"

#: stock/status_codes.py:85
msgid "Shipped against Sales Order"
msgstr "按銷售訂單出貨"

#: stock/status_codes.py:88
msgid "Received against Purchase Order"
msgstr "按採購訂單接收"

#: stock/status_codes.py:91
msgid "Returned against Return Order"
msgstr "按退貨訂單退回"

#: stock/status_codes.py:94
msgid "Sent to customer"
msgstr "寄送給客户"

#: stock/status_codes.py:95
msgid "Returned from customer"
msgstr "從客户端退回"

#: templates/403.html:6 templates/403.html:10 templates/403_csrf.html:7
msgid "Permission Denied"
msgstr "權限受限"

#: templates/403.html:11
msgid "You do not have permission to view this page."
msgstr "您沒有查看此頁面的權限。"

#: templates/403_csrf.html:11
msgid "Authentication Failure"
msgstr "認證失敗"

#: templates/403_csrf.html:12
msgid "You have been logged out from InvenTree."
msgstr "您已從InvenTree註銷。"

#: templates/404.html:6 templates/404.html:10
msgid "Page Not Found"
msgstr "找不到頁面"

#: templates/404.html:11
msgid "The requested page does not exist"
msgstr "請求的頁面不存在"

#: templates/500.html:6 templates/500.html:10
msgid "Internal Server Error"
msgstr "服務器內部錯誤"

#: templates/500.html:11
#, python-format
msgid "The %(inventree_title)s server raised an internal error"
msgstr "%(inventree_title)s 服務器引起一個內部錯誤"

#: templates/500.html:12
msgid "Refer to the error log in the admin interface for further details"
msgstr "有關更多詳細信息，請參閲管理界面中的錯誤日誌"

#: templates/503.html:11 templates/503.html:15
msgid "Site is in Maintenance"
msgstr "網站正在維護中"

#: templates/503.html:17
msgid "The site is currently in maintenance and should be up again soon!"
msgstr "該網站目前正在維護中，應該很快就會重新上線！"

#: templates/base.html:51
msgid "Server Restart Required"
msgstr "需要重新啓動服務器"

#: templates/base.html:54
msgid "A configuration option has been changed which requires a server restart"
msgstr "配置選項已更改，需要重新啓動服務器"

#: templates/base.html:54 templates/base.html:64
msgid "Contact your system administrator for further information"
msgstr "有關詳細信息，請與系統管理員聯繫"

#: templates/base.html:61
msgid "Pending Database Migrations"
msgstr "待處理的數據庫遷移"

#: templates/base.html:64
msgid "There are pending database migrations which require attention"
msgstr "有一些待處理的數據庫遷移需要注意"

#: templates/email/build_order_completed.html:9
#: templates/email/canceled_order_assigned.html:9
#: templates/email/new_order_assigned.html:9
#: templates/email/overdue_build_order.html:9
#: templates/email/overdue_purchase_order.html:9
#: templates/email/overdue_return_order.html:9
#: templates/email/overdue_sales_order.html:9
#: templates/email/purchase_order_received.html:9
#: templates/email/return_order_received.html:9
msgid "Click on the following link to view this order"
msgstr "點擊以下鏈接查看此訂單"

#: templates/email/build_order_required_stock.html:7
msgid "Stock is required for the following build order"
msgstr "以下生產訂單需要庫存"

#: templates/email/build_order_required_stock.html:8
#, python-format
msgid "Build order %(build)s - building %(quantity)s x %(part)s"
msgstr "生產訂單 %(build)s - 生產… %(quantity)s x %(part)s"

#: templates/email/build_order_required_stock.html:10
msgid "Click on the following link to view this build order"
msgstr "點擊以下鏈接查看此生產訂單"

#: templates/email/build_order_required_stock.html:14
msgid "The following parts are low on required stock"
msgstr "以下零件所需庫存不足"

#: templates/email/build_order_required_stock.html:18
msgid "Required Quantity"
msgstr "所需數量"

#: templates/email/build_order_required_stock.html:38
#: templates/email/low_stock_notification.html:30
msgid "You are receiving this email because you are subscribed to notifications for this part "
msgstr "您收到此郵件是因為您訂閲了此零件的通知 "

#: templates/email/low_stock_notification.html:9
#: templates/email/part_event_notification.html:9
msgid "Click on the following link to view this part"
msgstr "點擊以下鏈接查看此零件"

#: templates/email/low_stock_notification.html:18
#: templates/email/part_event_notification.html:19
msgid "Minimum Quantity"
msgstr "最小數量"

#: templates/email/part_event_notification.html:32
msgid "You are receiving this email because you are subscribed to notifications for this part or a category that it is part of "
msgstr ""

#: users/admin.py:101
msgid "Users"
msgstr "用户"

#: users/admin.py:102
msgid "Select which users are assigned to this group"
msgstr "選擇分配給此組的用户"

#: users/admin.py:137
msgid "Personal info"
msgstr "個人信息"

#: users/admin.py:139
msgid "Permissions"
msgstr "權限"

#: users/admin.py:142
msgid "Important dates"
msgstr "重要日期"

#: users/authentication.py:30 users/models.py:157
msgid "Token has been revoked"
msgstr "令牌已被撤銷"

#: users/authentication.py:33
msgid "Token has expired"
msgstr "令牌已過期"

#: users/models.py:100
msgid "API Token"
msgstr "API 令牌"

#: users/models.py:101
msgid "API Tokens"
msgstr "API 令牌"

#: users/models.py:137
msgid "Token Name"
msgstr "令牌名稱"

#: users/models.py:138
msgid "Custom token name"
msgstr "自定義令牌名稱"

#: users/models.py:144
msgid "Token expiry date"
msgstr "令牌過期日期"

#: users/models.py:152
msgid "Last Seen"
msgstr "最近一次在線"

#: users/models.py:153
msgid "Last time the token was used"
msgstr "最近使用令牌的時間"

#: users/models.py:157
msgid "Revoked"
msgstr "撤銷"

#: users/models.py:235
msgid "Permission set"
msgstr "權限設置"

#: users/models.py:244
msgid "Group"
msgstr "組"

#: users/models.py:248
msgid "View"
msgstr "查看"

#: users/models.py:248
msgid "Permission to view items"
msgstr "查看項目的權限"

#: users/models.py:252
msgid "Add"
msgstr "添加"

#: users/models.py:252
msgid "Permission to add items"
msgstr "添加項目的權限"

#: users/models.py:256
msgid "Change"
msgstr "更改"

#: users/models.py:258
msgid "Permissions to edit items"
msgstr "編輯項目的權限"

#: users/models.py:262
msgid "Delete"
msgstr "刪除"

#: users/models.py:264
msgid "Permission to delete items"
msgstr "刪除項目的權限"

#: users/models.py:497
msgid "Bot"
msgstr ""

#: users/models.py:498
msgid "Internal"
msgstr ""

#: users/models.py:500
msgid "Guest"
msgstr ""

#: users/models.py:509
msgid "Language"
msgstr ""

#: users/models.py:510
msgid "Preferred language for the user"
msgstr ""

#: users/models.py:515
msgid "Theme"
msgstr ""

#: users/models.py:516
msgid "Settings for the web UI as JSON - do not edit manually!"
msgstr ""

#: users/models.py:521
msgid "Widgets"
msgstr ""

#: users/models.py:523
msgid "Settings for the dashboard widgets as JSON - do not edit manually!"
msgstr ""

#: users/models.py:530
msgid "Display Name"
msgstr ""

#: users/models.py:531
msgid "Chosen display name for the user"
msgstr ""

#: users/models.py:537
msgid "Position"
msgstr ""

#: users/models.py:538
msgid "Main job title or position"
msgstr ""

#: users/models.py:545
msgid "User status message"
msgstr ""

#: users/models.py:552
msgid "User location information"
msgstr ""

#: users/models.py:557
msgid "User is actively using the system"
msgstr ""

#: users/models.py:564
msgid "Preferred contact information for the user"
msgstr ""

#: users/models.py:570
msgid "User Type"
msgstr ""

#: users/models.py:571
msgid "Which type of user is this?"
msgstr ""

#: users/models.py:577
msgid "Organisation"
msgstr ""

#: users/models.py:578
msgid "Users primary organisation/affiliation"
msgstr ""

#: users/models.py:586
msgid "Primary Group"
msgstr ""

#: users/models.py:587
msgid "Primary group for the user"
msgstr ""

#: users/ruleset.py:31
msgid "Admin"
msgstr "管理員"

#: users/ruleset.py:34
msgid "Stocktake"
msgstr "庫存盤點"

#: users/ruleset.py:38
msgid "Purchase Orders"
msgstr "採購訂單"

#: users/ruleset.py:39
msgid "Sales Orders"
msgstr "銷售訂單"

#: users/ruleset.py:40
msgid "Return Orders"
msgstr "退貨訂單"

#: users/serializers.py:236
msgid "Username"
msgstr "用户名"

#: users/serializers.py:239
msgid "First Name"
msgstr "名"

#: users/serializers.py:239
msgid "First name of the user"
msgstr "用户的名字（不包括姓氏）"

#: users/serializers.py:243
msgid "Last Name"
msgstr "姓"

#: users/serializers.py:243
msgid "Last name of the user"
msgstr "用户的姓氏"

#: users/serializers.py:247
msgid "Email address of the user"
msgstr "用户的電子郵件地址"

#: users/serializers.py:323
msgid "Staff"
msgstr "職員"

#: users/serializers.py:324
msgid "Does this user have staff permissions"
msgstr "此用户是否擁有員工權限"

#: users/serializers.py:329
msgid "Superuser"
msgstr "超級用户"

#: users/serializers.py:329
msgid "Is this user a superuser"
msgstr "此用户是否為超級用户"

#: users/serializers.py:333
msgid "Is this user account active"
msgstr "此用户帳户是否已激活"

#: users/serializers.py:345
msgid "Only a superuser can adjust this field"
msgstr ""

#: users/serializers.py:399
msgid "Only staff users can create new users"
msgstr ""

#: users/serializers.py:404
msgid "You do not have permission to create users"
msgstr ""

#: users/serializers.py:425
msgid "Your account has been created."
msgstr "您的帳號已經建立完成。"

#: users/serializers.py:427
msgid "Please use the password reset function to login"
msgstr "請使用重設密碼功能來登入"

#: users/serializers.py:433
msgid "Welcome to InvenTree"
msgstr "歡迎使用 InvenTree"

