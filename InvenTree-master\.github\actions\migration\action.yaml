name: 'Migration test'
description: 'Run migration test sequence'
author: 'InvenTree'

runs:
    using: 'composite'
    steps:
      - name: Data Import Export
        shell: bash
        run: |
          invoke migrate
          invoke dev.import-fixtures
          invoke export-records -f data.json
          python3 ./src/backend/InvenTree/manage.py flush --noinput
          invoke migrate
          invoke import-records -c -f data.json
          invoke import-records -c -f data.json
