# Payment System Setup Guide

## Quick Start Checklist

### ✅ 1. Backend Setup (Already Complete)
- [x] Django payments app created and configured
- [x] Database migrations applied
- [x] Pesapal integration files implemented
- [x] API endpoints configured

### ✅ 2. Frontend Setup (Already Complete)
- [x] CheckoutPayment component updated
- [x] PaymentSuccess page created
- [x] Orders page created
- [x] Payment flow integrated

### 🔧 3. Configuration Required

#### Environment Variables
Create/update `backend/.env` file:
```bash
# Copy example file
cp backend/.env.example backend/.env
```

Edit the `.env` file with your Pesapal credentials:
```bash
# Pesapal Configuration
PESAPAL_SANDBOX_MODE=True
PESAPAL_CONSUMER_KEY=your_pesapal_consumer_key_here
PESAPAL_CONSUMER_SECRET=your_pesapal_consumer_secret_here
PESAPAL_CALLBACK_URL=http://localhost:3000/payment-success
PESAPAL_IPN_URL=http://localhost:8000/api/payments/ipn/
```

#### Get Pesapal Credentials
1. **Sandbox Testing**:
   - Visit [demo.pesapal.com](http://demo.pesapal.com)
   - Register for a merchant account
   - Get your Consumer Key and Consumer Secret

2. **Production**:
   - Visit [pesapal.com](https://www.pesapal.com)
   - Complete merchant registration
   - Get live credentials

### 🧪 4. Testing the Integration

#### Run Test Script
```bash
cd backend
.\venv\Scripts\Activate.ps1  # Windows
python test_pesapal.py
```

Expected output with credentials:
```
==================================================
PESAPAL INTEGRATION TEST
==================================================
Testing Pesapal Configuration...
✓ Configuration is valid
  - Base URL: http://demo.pesapal.com
  - Sandbox Mode: True
  - Consumer Key: abcd123456...

Testing Utility Functions...
✓ Amount validation: 1000.50
✓ Email validation: <EMAIL>
✓ Phone validation: ************
✓ Order reference: ******************

Testing Pesapal Client...
✓ Pesapal client initialized successfully

Testing Payment Initiation...
✓ Test order data prepared:
  - amount: 1000.00
  - description: Test Order
  - reference: ******************
  - first_name: John
  - last_name: Doe
  - email: <EMAIL>
  - phone: ************
✓ Payment initiation test data is valid

==================================================
TEST SUMMARY: 4/4 tests passed
==================================================
✓ All tests passed! Pesapal integration is ready.
```

### 🚀 5. Start the Application

#### Backend
```bash
cd backend
.\venv\Scripts\Activate.ps1
python manage.py runserver
```

#### Frontend
```bash
npm run dev
```

### 🛒 6. Test Payment Flow

1. **Add items to cart** on the frontend
2. **Go to checkout** (`/checkout/payment`)
3. **Fill delivery information**:
   - Delivery address
   - Select county (affects delivery cost)
   - Phone number
4. **Select payment method** (M-Pesa, Visa, Mastercard)
5. **Click "Pay"** - redirects to Pesapal
6. **Complete payment** on Pesapal gateway
7. **Return to success page** with order details

### 📊 7. Monitor Payments

#### Django Admin
- Visit `http://localhost:8000/admin/`
- Login with superuser credentials
- Navigate to "Payments" section to see:
  - Orders
  - Payments
  - Payment Logs
  - Payment Callbacks

#### API Endpoints for Testing
```bash
# Get user orders
GET http://localhost:8000/api/payments/orders/
Authorization: Bearer <your_jwt_token>

# Check payment status
GET http://localhost:8000/api/payments/status/<transaction_id>/
Authorization: Bearer <your_jwt_token>

# Get checkout summary
GET http://localhost:8000/api/payments/checkout/summary/?delivery_county=nairobi
Authorization: Bearer <your_jwt_token>
```

## 🔧 Troubleshooting

### Common Issues

#### 1. "PESAPAL_CONSUMER_KEY is required"
- **Solution**: Add Pesapal credentials to `.env` file
- **Check**: Ensure `.env` file exists in `backend/` directory

#### 2. "Module 'requests' not found"
- **Solution**: Install requests in virtual environment
```bash
cd backend
.\venv\Scripts\Activate.ps1
pip install requests
```

#### 3. Payment callback not working
- **Check**: IPN URL is accessible from internet (use ngrok for local testing)
- **Verify**: Callback URL matches frontend route

#### 4. Orders not appearing
- **Check**: User is authenticated (JWT token valid)
- **Verify**: Database migrations applied
- **Debug**: Check Django admin for order records

### Development Tips

#### Use ngrok for IPN Testing
```bash
# Install ngrok
# Start backend on port 8000
python manage.py runserver

# In another terminal
ngrok http 8000

# Update .env with ngrok URL
PESAPAL_IPN_URL=https://your-ngrok-url.ngrok.io/api/payments/ipn/
```

#### Debug Payment Logs
Check `PaymentLog` model in Django admin for detailed API interactions:
- Request data sent to Pesapal
- Response data received
- Error messages
- Timestamps

## 🎯 Production Deployment

### Before Going Live

1. **Update Environment**:
   ```bash
   PESAPAL_SANDBOX_MODE=False
   PESAPAL_CONSUMER_KEY=live_consumer_key
   PESAPAL_CONSUMER_SECRET=live_consumer_secret
   ```

2. **Update URLs**:
   - Use production domain for callback URLs
   - Ensure HTTPS for all payment endpoints

3. **Test Thoroughly**:
   - Small test transactions first
   - Verify IPN callbacks work
   - Test all payment methods

4. **Monitor**:
   - Set up logging alerts
   - Monitor payment success rates
   - Track failed transactions

## 📞 Support

- **Pesapal Documentation**: [developer.pesapal.com](https://developer.pesapal.com)
- **Integration Issues**: Check `PaymentLog` model for detailed error information
- **API Testing**: Use provided test script and Django admin interface

**The payment system is now ready for production use with proper Pesapal credentials!**
