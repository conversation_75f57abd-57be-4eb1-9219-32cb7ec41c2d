# Generated by Django 3.2.16 on 2022-11-11 01:53

import InvenTree.fields
from django.db import migrations
import djmoney.models.validators


class Migration(migrations.Migration):

    dependencies = [
        ('stock', '0088_remove_stockitem_infinite'),
    ]

    operations = [
        migrations.AlterField(
            model_name='stockitem',
            name='purchase_price',
            field=InvenTree.fields.InvenTreeModelMoneyField(blank=True, currency_choices=[], decimal_places=6, default_currency='', help_text='Single unit purchase price at time of purchase', max_digits=19, null=True, validators=[djmoney.models.validators.MinMoneyValidator(0)], verbose_name='Purchase Price'),
        ),
    ]
