# Prompt 2.1: Django Backend Foundation & Core E-commerce API

**Persona:** You are a Full-Stack Developer with expertise in Django, Django Rest Framework, and React integration.

**Objective:** Build the foundational Django backend infrastructure for DeepForest Hardware Emporium. Create core models, authentication, and basic CRUD operations that will serve as the foundation for payment integration and InvenTree integration.

**Context:** The frontend is now polished and functional. This phase builds the core Django backend engine that will later integrate with Pesapal payments and InvenTree. The project is at `d:\PRJs\WORK ACTION\DeepForest\deepforest-hardware-emporium` and must use Django + DRF to match InvenTree's technology stack.

## Key Tasks & Requirements:

### 1. Django Project Setup:
- **New Django Project:** Create a new Django project in a `backend/` directory
- **Database:** Configure PostgreSQL as the database
- **API Framework:** Set up Django Rest Framework (DRF)
- **CORS:** Configure `django-cors-headers` for React frontend communication
- **Environment:** Set up proper environment variable management

### 2. Core Database Models (`backend/models.py`):
- **Category Model:** name, description, image_url, slug, created_at, updated_at
- **Product Model:** name, description, price_ksh, sku, category (FK), image_url, is_active, on_sale, sale_price_ksh, stock_quantity (temporary field), created_at, updated_at
- **User Profile:** Extend Django User with Kenyan-specific fields (phone, county, delivery_address)
- **Cart & CartItem:** User-linked shopping cart functionality
- **Data Migration:** Script to populate initial product/category data from frontend

### 3. Core DRF API Endpoints:
- **Products API:** `/api/products/` with filtering (category, search, price range), pagination
- **Categories API:** `/api/categories/` for category listing
- **Authentication:** JWT-based auth with `/api/auth/` endpoints (register, login, logout)
- **Cart API:** Protected `/api/cart/` endpoints for authenticated users
- **User API:** `/api/user/profile/` for user profile management

### 4. Kenyan Market Foundation:
- **Currency Handling:** All prices stored and returned in KSH
- **Delivery Zones:** Model for Kenyan counties and delivery costs

### 5. Frontend Integration:
- **Remove Static Data:** Replace all hardcoded arrays in React components
- **TanStack Query:** Implement React Query for all API calls with proper loading/error states
- **Authentication Flow:** Build login/register components with JWT handling
- **Dynamic Cart:** Connect cart UI to backend API

### 6. Admin Interface:
- **Django Admin:** Customize admin interface for product/category management
- **Permissions:** Set up proper user roles and permissions
- **Data Management:** Tools for bulk product import/export

## Acceptance Criteria:
- Fully functional Django backend with PostgreSQL
- Core REST API with proper authentication
- React frontend is 100% dynamic (no hardcoded data)
- User registration, login, and profile management works
- Cart persists in database for logged-in users
- Admin interface allows product/category management
- All prices and weights use Kenyan standards (KSH, KG)
- Foundation ready for Pesapal payment integration in Phase 2.2
