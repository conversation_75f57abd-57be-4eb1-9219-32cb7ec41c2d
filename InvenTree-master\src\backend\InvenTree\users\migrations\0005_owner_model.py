# Generated by Django 3.0.7 on 2021-01-11 18:54

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('contenttypes', '0002_remove_content_type_name'),
        ('users', '0004_auto_20210113_1909'),
    ]

    operations = [
        migrations.CreateModel(
            name='Owner',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('owner_id', models.PositiveIntegerField(blank=True, null=True)),
                ('owner_type', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='contenttypes.ContentType')),
            ],
        ),
        migrations.AddConstraint(
            model_name='owner',
            constraint=models.UniqueConstraint(fields=('owner_type', 'owner_id'), name='unique_owner'),
        ),
    ]
