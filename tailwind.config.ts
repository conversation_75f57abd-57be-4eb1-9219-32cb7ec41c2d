
import type { Config } from "tailwindcss";

export default {
	darkMode: ["class"],
	content: [
		"./pages/**/*.{ts,tsx}",
		"./components/**/*.{ts,tsx}",
		"./app/**/*.{ts,tsx}",
		"./src/**/*.{ts,tsx}",
	],
	prefix: "",
	theme: {
		container: {
			center: true,
			padding: '2rem',
			screens: {
				'2xl': '1400px'
			}
		},
		extend: {
			colors: {
				border: 'hsl(var(--border))',
				input: 'hsl(var(--input))',
				ring: 'hsl(var(--ring))',
				background: 'hsl(var(--background))',
				foreground: 'hsl(var(--foreground))',
				primary: {
					DEFAULT: 'hsl(var(--primary))',
					foreground: 'hsl(var(--primary-foreground))'
				},
				secondary: {
					DEFAULT: 'hsl(var(--secondary))',
					foreground: 'hsl(var(--secondary-foreground))'
				},
				destructive: {
					DEFAULT: 'hsl(var(--destructive))',
					foreground: 'hsl(var(--destructive-foreground))'
				},
				muted: {
					DEFAULT: 'hsl(var(--muted))',
					foreground: 'hsl(var(--muted-foreground))'
				},
				accent: {
					DEFAULT: 'hsl(var(--accent))',
					foreground: 'hsl(var(--accent-foreground))'
				},
				popover: {
					DEFAULT: 'hsl(var(--popover))',
					foreground: 'hsl(var(--popover-foreground))'
				},
				card: {
					DEFAULT: 'hsl(var(--card))',
					foreground: 'hsl(var(--card-foreground))'
				},
				sidebar: {
					DEFAULT: 'hsl(var(--sidebar-background))',
					foreground: 'hsl(var(--sidebar-foreground))',
					primary: 'hsl(var(--sidebar-primary))',
					'primary-foreground': 'hsl(var(--sidebar-primary-foreground))',
					accent: 'hsl(var(--sidebar-accent))',
					'accent-foreground': 'hsl(var(--sidebar-accent-foreground))',
					border: 'hsl(var(--sidebar-border))',
					ring: 'hsl(var(--sidebar-ring))'
				},
				// DeepForest Hardware theme colors - updated for hardware store
				forest: {
					green: {
						50: '#f0f9f4',
						100: '#dcf2e3',
						200: '#bbe5ca',
						300: '#8dd1a5',
						400: '#57b578',
						500: '#2f7a3c', // Main green
						600: '#28653e',
						700: '#245138',
						800: '#1f4030',
						900: '#1b3428'
					},
					brown: {
						50: '#faf8f5',
						100: '#f2ede5',
						200: '#e5d7c8',
						300: '#d4bca2',
						400: '#c0997a',
						500: '#a8835d', // Main brown
						600: '#8b6d49',
						700: '#73593d',
						800: '#5e4a34',
						900: '#4d3d2c'
					},
					grey: {
						50: '#f9f9f9',
						100: '#f1f1f1',
						200: '#e4e4e4',
						300: '#d1d1d1',
						400: '#b4b4b4',
						500: '#9a9a9a',
						600: '#818181',
						700: '#6a6a6a',
						800: '#5a5a5a',
						900: '#4a4a4a'
					},
					rust: {
						50: '#fbf7f4',
						100: '#f5eae3',
						200: '#e9d2c3',
						300: '#dbb59e',
						400: '#ca8f6d',
						500: '#b76e47', // Rust - new industrial color
						600: '#9c5a36',
						700: '#824a2f',
						800: '#6c3e2a',
						900: '#5a3424'
					},
					steel: {
						50: '#f6f7f9',
						100: '#ebedf2',
						200: '#d5d9e2',
						300: '#b3bac9',
						400: '#8c95aa',
						500: '#69758f', // Steel - new industrial color
						600: '#556076',
						700: '#454e60',
						800: '#3a4151',
						900: '#333845'
					}
				}
			},
			borderRadius: {
				lg: 'var(--radius)',
				md: 'calc(var(--radius) - 2px)',
				sm: 'calc(var(--radius) - 4px)'
			},
			keyframes: {
				'accordion-down': {
					from: {
						height: '0'
					},
					to: {
						height: 'var(--radix-accordion-content-height)'
					}
				},
				'accordion-up': {
					from: {
						height: 'var(--radix-accordion-content-height)'
					},
					to: {
						height: '0'
					}
				},
				'fade-in': {
					'0%': {
						opacity: '0',
						transform: 'translateY(10px)'
					},
					'100%': {
						opacity: '1',
						transform: 'translateY(0)'
					}
				}
			},
			animation: {
				'accordion-down': 'accordion-down 0.2s ease-out',
				'accordion-up': 'accordion-up 0.2s ease-out',
				'fade-in': 'fade-in 0.6s ease-out'
			}
		}
	},
	plugins: [require("tailwindcss-animate")],
} satisfies Config;
