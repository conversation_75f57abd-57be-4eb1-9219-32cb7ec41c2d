- model: stock.stockitemtestresult
  pk: 1
  fields:
    stock_item: 105
    template: 10
    value: "0xA1B2C3D4"
    result: True
    date: 2020-02-02

- model: stock.stockitemtestresult
  pk: 2
  fields:
    stock_item: 105
    template: 9
    value: "0xAABBCCDD"
    result: True
    date: 2020-02-02

- model: stock.stockitemtestresult
  pk: 3
  fields:
    stock_item: 105
    template: 8
    result: False
    date: 2020-05-16
    notes: 'Got too hot or something'

- model: stock.stockitemtestresult
  pk: 4
  fields:
    stock_item: 105
    template: 8
    result: True
    date: 2020-05-17
    notes: 'Passed temperature test by making it cooler'

- model: stock.stockitemtestresult
  pk: 5
  fields:
    stock_item: 522
    template: 2
    result: True
    date: 2020-05-17

- model: stock.stockitemtestresult
  pk: 6
  fields:
    stock_item: 522
    template: 2
    result: False
    date: 2020-05-18

- model: stock.stockitemtestresult
  pk: 7
  fields:
    stock_item: 522
    template: 4
    result: True
    date: 2020-05-17

- model: stock.stockitemtestresult
  pk: 8
  fields:
    stock_item: 522
    template: 3
    result: True
    date: 2024-02-15
