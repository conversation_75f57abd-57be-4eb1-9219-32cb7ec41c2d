---
title: Release 0.2.3
---

## Release 0.2.3

[Release 0.2.3](https://github.com/inventree/InvenTree/releases/tag/0.2.3) provides a number of major new features and improvements, as well as some crucial bug fixes:

## New Features

### Part Tile Display

[#1622](https://github.com/inventree/InvenTree/pull/1622) introduces a "tile" display for part data.

### BOM Line Variants

[#1626](https://github.com/inventree/InvenTree/pull/1626) adds functionality which allows variants of parts to be optionally substituted in Bills of Material. This option is individually configurable per BOM line item.

### Docker Improvements

[#1664](https://github.com/inventree/InvenTree/pull/1664) provides major improvements to the InvenTree docker images. In particular, running in "production" mode (`INVENTREE_DEBUG=false`) now correctly handles serving of static and media files using the nginx reverse proxy.

## Major Bug Fixes

| PR | Description |
| --- | --- |
| [#1632](https://github.com/inventree/InvenTree/pull/1632) | Fixes navbar icon rendering issues |
| [#1633](https://github.com/inventree/InvenTree/pull/1633) | Fixes currency rendering issues |
| [#1644](https://github.com/inventree/InvenTree/pull/1644) | Fixes rendering issue for stock test result table |
| [#1647](https://github.com/inventree/InvenTree/pull/1647) | Fixes exception when image does not have a rendered thumbnail |
