"""
Payment views and API endpoints for Pesapal integration
"""
import logging
from rest_framework import status, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.views import APIView
from django.http import HttpResponse
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
from django.shortcuts import get_object_or_404
from django.db import models

from .models import Order, Payment, PaymentLog
from .pesapal_client import PesapalClient
from .pesapal_webhooks import PesapalWebhookHandler
from .pesapal_serializers import (
    OrderSerializer, PaymentInitiationSerializer, PaymentStatusSerializer,
    PaymentCallbackSerializer, PaymentLogSerializer, CheckoutSummarySerializer
)
from .pesapal_exceptions import PesapalException
from .pesapal_utils import generate_order_reference

logger = logging.getLogger(__name__)


class PaymentInitiationView(APIView):
    """Initiate payment with Pesapal"""
    permission_classes = [permissions.IsAuthenticated]
    
    def post(self, request):
        """Create order and initiate payment"""
        try:
            serializer = PaymentInitiationSerializer(data=request.data)
            if not serializer.is_valid():
                return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
            
            # Create order from cart
            order = serializer.create_order_from_cart(request.user)
            
            # Prepare payment data
            payment_data = {
                'amount': str(order.total_with_delivery),
                'description': f"DeepForest Hardware Order {order.order_reference}",
                'type': 'MERCHANT',
                'reference': order.order_reference,
                'first_name': request.user.first_name or request.user.username,
                'last_name': request.user.last_name or '',
                'email': request.user.email,
                'phone': serializer.validated_data.get('phone', '')
            }
            
            # Initialize Pesapal client and create payment
            client = PesapalClient()
            iframe_url = client.initiate_payment(payment_data)
            
            # Log payment initiation
            PaymentLog.objects.create(
                order=order,
                action='initiate_payment',
                status='pending',
                message=f"Payment initiated for order {order.order_reference}",
                request_data=payment_data,
                pesapal_transaction_id=order.order_reference
            )
            
            # Return response
            order_serializer = OrderSerializer(order)
            return Response({
                'success': True,
                'order': order_serializer.data,
                'payment_url': iframe_url,
                'message': 'Payment initiated successfully'
            }, status=status.HTTP_201_CREATED)
            
        except PesapalException as e:
            logger.error(f"Pesapal error during payment initiation: {str(e)}")
            return Response({
                'success': False,
                'error': str(e)
            }, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            logger.error(f"Error during payment initiation: {str(e)}")
            return Response({
                'success': False,
                'error': 'Payment initiation failed'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class PaymentCallbackView(APIView):
    """Handle Pesapal payment callbacks"""
    permission_classes = []  # No authentication required for callbacks
    
    @method_decorator(csrf_exempt)
    def dispatch(self, *args, **kwargs):
        return super().dispatch(*args, **kwargs)
    
    def get(self, request):
        """Handle GET callback from Pesapal"""
        return self._handle_callback(request.GET.dict())
    
    def post(self, request):
        """Handle POST callback from Pesapal"""
        return self._handle_callback(request.data)
    
    def _handle_callback(self, callback_data):
        """Process callback data"""
        try:
            serializer = PaymentCallbackSerializer(data=callback_data)
            if not serializer.is_valid():
                logger.warning(f"Invalid callback data: {callback_data}")
                return Response({'status': 'invalid'}, status=status.HTTP_400_BAD_REQUEST)
            
            # Process callback
            webhook_handler = PesapalWebhookHandler()
            success = webhook_handler.process_ipn_callback(callback_data)
            
            if success:
                return Response({'status': 'success'}, status=status.HTTP_200_OK)
            else:
                return Response({'status': 'failed'}, status=status.HTTP_400_BAD_REQUEST)
                
        except Exception as e:
            logger.error(f"Error processing callback: {str(e)}")
            return Response({'status': 'error'}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def payment_status(request, transaction_id):
    """Check payment status"""
    try:
        # Find order by transaction ID or order reference
        order = get_object_or_404(
            Order, 
            models.Q(pesapal_transaction_id=transaction_id) | 
            models.Q(order_reference=transaction_id),
            user=request.user
        )
        
        # Manual status check if needed
        if order.payment_status == 'pending':
            webhook_handler = PesapalWebhookHandler()
            result = webhook_handler.manual_status_check(order.order_reference)
            
            if not result['success']:
                logger.warning(f"Manual status check failed: {result.get('error')}")
        
        # Return current status
        order_serializer = OrderSerializer(order)
        return Response({
            'success': True,
            'order': order_serializer.data,
            'payment_status': order.payment_status
        })
        
    except Exception as e:
        logger.error(f"Error checking payment status: {str(e)}")
        return Response({
            'success': False,
            'error': 'Failed to check payment status'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def checkout_summary(request):
    """Get checkout summary with cart items and delivery costs"""
    try:
        delivery_county = request.GET.get('delivery_county', '')
        
        serializer = CheckoutSummarySerializer(
            data={'delivery_county': delivery_county},
            user=request.user
        )
        
        if serializer.is_valid():
            return Response({
                'success': True,
                'summary': serializer.data
            })
        else:
            return Response({
                'success': False,
                'errors': serializer.errors
            }, status=status.HTTP_400_BAD_REQUEST)
            
    except Exception as e:
        logger.error(f"Error getting checkout summary: {str(e)}")
        return Response({
            'success': False,
            'error': 'Failed to get checkout summary'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def user_orders(request):
    """Get user's orders"""
    try:
        orders = Order.objects.filter(user=request.user)
        serializer = OrderSerializer(orders, many=True)
        
        return Response({
            'success': True,
            'orders': serializer.data
        })
        
    except Exception as e:
        logger.error(f"Error getting user orders: {str(e)}")
        return Response({
            'success': False,
            'error': 'Failed to get orders'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def order_detail(request, order_id):
    """Get order details"""
    try:
        order = get_object_or_404(Order, id=order_id, user=request.user)
        serializer = OrderSerializer(order)
        
        # Get payment logs
        logs = PaymentLog.objects.filter(order=order)
        log_serializer = PaymentLogSerializer(logs, many=True)
        
        return Response({
            'success': True,
            'order': serializer.data,
            'payment_logs': log_serializer.data
        })
        
    except Exception as e:
        logger.error(f"Error getting order detail: {str(e)}")
        return Response({
            'success': False,
            'error': 'Failed to get order details'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# IPN endpoint for Pesapal webhooks
@csrf_exempt
def pesapal_ipn(request):
    """
    Pesapal IPN endpoint
    Based on pesapal-ipn-listener.php implementation
    """
    try:
        if request.method == 'GET':
            callback_data = request.GET.dict()
        else:
            callback_data = request.POST.dict()
        
        logger.info(f"Received IPN: {callback_data}")
        
        # Process IPN
        webhook_handler = PesapalWebhookHandler()
        success = webhook_handler.process_ipn_callback(callback_data)
        
        if success:
            # Return the required response format for Pesapal
            response_data = (
                f"pesapal_notification_type={callback_data.get('pesapal_notification_type', '')}&"
                f"pesapal_transaction_tracking_id={callback_data.get('pesapal_transaction_tracking_id', '')}&"
                f"pesapal_merchant_reference={callback_data.get('pesapal_merchant_reference', '')}"
            )
            return HttpResponse(response_data, content_type='text/plain')
        else:
            return HttpResponse('FAILED', status=400)
            
    except Exception as e:
        logger.error(f"Error in IPN handler: {str(e)}")
        return HttpResponse('ERROR', status=500)
