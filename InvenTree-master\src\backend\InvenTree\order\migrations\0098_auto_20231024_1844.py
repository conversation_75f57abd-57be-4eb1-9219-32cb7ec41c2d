# Generated by Django 3.2.22 on 2023-10-24 16:44

from django.db import migrations
import djmoney.models.fields


class Migration(migrations.Migration):

    dependencies = [
        ('order', '0097_auto_20230529_0107'),
    ]

    operations = [
        migrations.AlterField(
            model_name='purchaseorder',
            name='total_price_currency',
            field=djmoney.models.fields.CurrencyField(choices=[], default='', editable=False, max_length=3, null=True),
        ),
        migrations.AlterField(
            model_name='purchaseorderextraline',
            name='price_currency',
            field=djmoney.models.fields.CurrencyField(choices=[], default='', editable=False, max_length=3, null=True),
        ),
        migrations.AlterField(
            model_name='purchaseorderlineitem',
            name='purchase_price_currency',
            field=djmoney.models.fields.CurrencyField(choices=[], default='', editable=False, max_length=3, null=True),
        ),
        migrations.AlterField(
            model_name='returnorder',
            name='total_price_currency',
            field=djmoney.models.fields.CurrencyField(choices=[], default='', editable=False, max_length=3, null=True),
        ),
        migrations.AlterField(
            model_name='returnorderextraline',
            name='price_currency',
            field=djmoney.models.fields.CurrencyField(choices=[], default='', editable=False, max_length=3, null=True),
        ),
        migrations.AlterField(
            model_name='returnorderlineitem',
            name='price_currency',
            field=djmoney.models.fields.CurrencyField(choices=[], default='', editable=False, max_length=3, null=True),
        ),
        migrations.AlterField(
            model_name='salesorder',
            name='total_price_currency',
            field=djmoney.models.fields.CurrencyField(choices=[], default='', editable=False, max_length=3, null=True),
        ),
        migrations.AlterField(
            model_name='salesorderextraline',
            name='price_currency',
            field=djmoney.models.fields.CurrencyField(choices=[], default='', editable=False, max_length=3, null=True),
        ),
        migrations.AlterField(
            model_name='salesorderlineitem',
            name='sale_price_currency',
            field=djmoney.models.fields.CurrencyField(choices=[], default='', editable=False, max_length=3, null=True),
        ),
    ]
