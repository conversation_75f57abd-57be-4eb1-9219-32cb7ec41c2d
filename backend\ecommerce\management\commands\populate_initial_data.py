from django.core.management.base import BaseCommand
from ecommerce.models import Category, Product, DeliveryZone


class Command(BaseCommand):
    help = 'Populate initial data from frontend data structure'

    def handle(self, *args, **options):
        self.stdout.write('Populating initial data...')

        # Create categories
        categories_data = [
            {
                'name': 'Doors & Windows',
                'description': 'Interior and exterior doors, windows, frames, and hardware. Find the perfect entrance solutions for your project.',
                'image_url': 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=500',
                'slug': 'doors'
            },
            {
                'name': 'Paints & Coatings',
                'description': 'Professional grade paints, primers, and protective coatings for all surfaces and applications.',
                'image_url': 'https://images.unsplash.com/photo-1562259949-e8e7689d7828?w=500',
                'slug': 'paint'
            },
            {
                'name': 'Cement & Concrete',
                'description': 'High-quality cement, concrete mixes, and masonry supplies for strong, durable construction.',
                'image_url': 'https://images.unsplash.com/photo-1504307651254-35680f356dfd?w=500',
                'slug': 'cement'
            },
            {
                'name': 'Hardware & Tools',
                'description': 'Essential tools, fasteners, and hardware for construction and maintenance projects.',
                'image_url': 'https://images.unsplash.com/photo-1416879595882-3373a0480b5b?w=500',
                'slug': 'hardware'
            },
            {
                'name': 'Roofing Materials',
                'description': 'Complete roofing solutions including tiles, sheets, gutters, and accessories.',
                'image_url': 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=500',
                'slug': 'roofing'
            },
            {
                'name': 'Electrical Supplies',
                'description': 'Wiring, fixtures, switches, and electrical components for residential and commercial use.',
                'image_url': 'https://images.unsplash.com/photo-1621905251189-08b45d6a269e?w=500',
                'slug': 'electrical'
            }
        ]

        for cat_data in categories_data:
            category, created = Category.objects.get_or_create(
                slug=cat_data['slug'],
                defaults=cat_data
            )
            if created:
                self.stdout.write(f'Created category: {category.name}')

        # Create products
        products_data = [
            {
                'name': 'Solid Wood Interior Door',
                'description': 'Premium mahogany interior door with elegant finish',
                'price_ksh': 15000,
                'sale_price_ksh': 12000,
                'sku': 'DW-001',
                'category_slug': 'doors',
                'image_url': 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400',
                'on_sale': True,
                'stock_quantity': 25,
                'weight_kg': 25
            },
            {
                'name': 'Steel Security Door',
                'description': 'Heavy-duty steel security door with multiple locks',
                'price_ksh': 25000,
                'sku': 'DW-002',
                'category_slug': 'doors',
                'image_url': 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400',
                'stock_quantity': 15,
                'weight_kg': 45
            },
            {
                'name': 'Aluminum Sliding Window',
                'description': 'Double-glazed aluminum sliding window',
                'price_ksh': 12000,
                'sku': 'DW-003',
                'category_slug': 'doors',
                'image_url': 'https://images.unsplash.com/photo-1545558014-8692077e9b5c?w=400',
                'stock_quantity': 30,
                'weight_kg': 18
            },
            {
                'name': 'Premium Interior Paint - White',
                'description': 'High-quality latex paint for interior walls',
                'price_ksh': 4000,
                'sale_price_ksh': 3500,
                'sku': 'PT-001',
                'category_slug': 'paint',
                'image_url': 'https://images.unsplash.com/photo-1562259949-e8e7689d7828?w=400',
                'on_sale': True,
                'stock_quantity': 50,
                'weight_kg': 4
            },
            {
                'name': 'Exterior Weather Shield Paint',
                'description': 'Weather-resistant exterior paint with UV protection',
                'price_ksh': 4200,
                'sku': 'PT-002',
                'category_slug': 'paint',
                'image_url': 'https://images.unsplash.com/photo-1589939705384-5185137a7f0f?w=400',
                'stock_quantity': 40,
                'weight_kg': 4.5
            },
            {
                'name': 'Wood Stain - Mahogany',
                'description': 'Premium wood stain for furniture and decking',
                'price_ksh': 2800,
                'sku': 'PT-003',
                'category_slug': 'paint',
                'image_url': 'https://images.unsplash.com/photo-1604709177225-055f99402ea3?w=400',
                'stock_quantity': 0,
                'weight_kg': 2.5
            },
            {
                'name': 'Portland Cement - 50kg',
                'description': 'High-grade Portland cement for construction',
                'price_ksh': 800,
                'sku': 'CM-001',
                'category_slug': 'cement',
                'image_url': 'https://images.unsplash.com/photo-1504307651254-35680f356dfd?w=400',
                'stock_quantity': 100,
                'weight_kg': 50
            },
            {
                'name': 'Ready Mix Concrete',
                'description': 'Pre-mixed concrete for quick projects',
                'price_ksh': 1400,
                'sale_price_ksh': 1200,
                'sku': 'CM-002',
                'category_slug': 'cement',
                'image_url': 'https://images.unsplash.com/photo-1581094794329-c8112a89af12?w=400',
                'on_sale': True,
                'stock_quantity': 75,
                'weight_kg': 40
            },
            {
                'name': 'Professional Hammer Set',
                'description': 'Set of 3 professional hammers for construction',
                'price_ksh': 2500,
                'sku': 'HW-001',
                'category_slug': 'hardware',
                'image_url': 'https://images.unsplash.com/photo-1416879595882-3373a0480b5b?w=400',
                'stock_quantity': 20,
                'weight_kg': 2.2
            },
            {
                'name': 'Electric Drill Kit',
                'description': 'Cordless electric drill with bits and case',
                'price_ksh': 10000,
                'sale_price_ksh': 8500,
                'sku': 'HW-002',
                'category_slug': 'hardware',
                'image_url': 'https://images.unsplash.com/photo-1572981779307-38b8cabb2407?w=400',
                'on_sale': True,
                'stock_quantity': 12,
                'weight_kg': 1.8
            },
            {
                'name': 'Stainless Steel Screws - Assorted',
                'description': 'Assorted pack of stainless steel screws',
                'price_ksh': 1200,
                'sku': 'HW-003',
                'category_slug': 'hardware',
                'image_url': 'https://images.unsplash.com/photo-1609205807107-e8ec2120f9de?w=400',
                'stock_quantity': 60,
                'weight_kg': 0.5
            },
            {
                'name': 'Clay Roofing Tiles',
                'description': 'Traditional clay tiles for durable roofing',
                'price_ksh': 150,
                'sku': 'RF-001',
                'category_slug': 'roofing',
                'image_url': 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400',
                'stock_quantity': 500,
                'weight_kg': 2.5
            },
            {
                'name': 'Galvanized Iron Sheets',
                'description': 'Corrugated galvanized iron roofing sheets',
                'price_ksh': 2000,
                'sale_price_ksh': 1800,
                'sku': 'RF-002',
                'category_slug': 'roofing',
                'image_url': 'https://images.unsplash.com/photo-1590736969955-71cc94901144?w=400',
                'on_sale': True,
                'stock_quantity': 80,
                'weight_kg': 8
            },
            {
                'name': 'Copper Electrical Wire - 100m',
                'description': 'High-quality copper wire for electrical installations',
                'price_ksh': 3200,
                'sku': 'EL-001',
                'category_slug': 'electrical',
                'image_url': 'https://images.unsplash.com/photo-1621905251189-08b45d6a269e?w=400',
                'stock_quantity': 35,
                'weight_kg': 12
            },
            {
                'name': 'LED Light Fixtures Set',
                'description': 'Energy-efficient LED light fixtures for home',
                'price_ksh': 5200,
                'sale_price_ksh': 4500,
                'sku': 'EL-002',
                'category_slug': 'electrical',
                'image_url': 'https://images.unsplash.com/photo-1524484485831-a92ffc0de03f?w=400',
                'on_sale': True,
                'stock_quantity': 25,
                'weight_kg': 3.2
            }
        ]

        for prod_data in products_data:
            category_slug = prod_data.pop('category_slug')
            category = Category.objects.get(slug=category_slug)
            
            product, created = Product.objects.get_or_create(
                sku=prod_data['sku'],
                defaults={**prod_data, 'category': category}
            )
            if created:
                self.stdout.write(f'Created product: {product.name}')

        # Create delivery zones
        delivery_zones = [
            {'county': 'Nairobi', 'delivery_cost_ksh': 200, 'estimated_days': 1},
            {'county': 'Mombasa', 'delivery_cost_ksh': 500, 'estimated_days': 2},
            {'county': 'Kisumu', 'delivery_cost_ksh': 400, 'estimated_days': 2},
            {'county': 'Nakuru', 'delivery_cost_ksh': 300, 'estimated_days': 2},
            {'county': 'Eldoret', 'delivery_cost_ksh': 450, 'estimated_days': 3},
            {'county': 'Thika', 'delivery_cost_ksh': 250, 'estimated_days': 1},
            {'county': 'Malindi', 'delivery_cost_ksh': 600, 'estimated_days': 3},
            {'county': 'Kitale', 'delivery_cost_ksh': 500, 'estimated_days': 3},
            {'county': 'Garissa', 'delivery_cost_ksh': 700, 'estimated_days': 4},
            {'county': 'Kakamega', 'delivery_cost_ksh': 450, 'estimated_days': 3},
        ]

        for zone_data in delivery_zones:
            zone, created = DeliveryZone.objects.get_or_create(
                county=zone_data['county'],
                defaults=zone_data
            )
            if created:
                self.stdout.write(f'Created delivery zone: {zone.county}')

        self.stdout.write(self.style.SUCCESS('Successfully populated initial data!'))
