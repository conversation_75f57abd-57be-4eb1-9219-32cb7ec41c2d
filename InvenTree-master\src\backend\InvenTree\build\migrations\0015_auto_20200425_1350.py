# Generated by Django 3.0.5 on 2020-04-25 13:50

import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
import mptt.fields


class Migration(migrations.Migration):

    dependencies = [
        ('build', '0014_auto_20200425_1243'),
    ]

    operations = [
        migrations.AlterField(
            model_name='build',
            name='parent',
            field=mptt.fields.TreeForeignKey(blank=True, help_text='Parent build to which this build is allocated', null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='children', to='build.Build', verbose_name='Parent Build'),
        ),
        migrations.AlterField(
            model_name='builditem',
            name='quantity',
            field=models.DecimalField(decimal_places=5, default=1, help_text='Stock quantity to allocate to build', max_digits=15, validators=[django.core.validators.MinValueValidator(0)]),
        ),
    ]
