{% extends "label/label_base.html" %}

{% load l10n i18n barcode %}

{% block style %}

.qr {
    position: absolute;
    left: 0mm;
    top: 0mm;
    {% localize off %}
    height: {{ height }}mm;
    width: {{ height }}mm;
    {% endlocalize %}
}

.loc {
    font-family: Arial, Helvetica, sans-serif;
    display: flex;
    position: absolute;
    {% localize off %}
    left: {{ height }}mm;
    {% endlocalize %}
    top: 2mm;
}

{% endblock style %}

{% block content %}

<img class='qr' alt="{% trans 'QR code' %}" src='{% qrcode qr_data %}'>

<div class='loc'>
    {{ location.name }}
</div>

{% endblock content %}
