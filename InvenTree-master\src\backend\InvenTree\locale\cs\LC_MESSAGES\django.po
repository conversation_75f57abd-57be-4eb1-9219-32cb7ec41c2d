msgid ""
msgstr ""
"Project-Id-Version: inventree\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-09 19:12+0000\n"
"PO-Revision-Date: 2025-05-09 19:14\n"
"Last-Translator: \n"
"Language-Team: Czech\n"
"Language: cs_CZ\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=4; plural=(n==1) ? 0 : (n>=2 && n<=4) ? 1 : 3;\n"
"X-Crowdin-Project: inventree\n"
"X-Crowdin-Project-ID: 452300\n"
"X-Crowdin-Language: cs\n"
"X-Crowdin-File: /src/backend/InvenTree/locale/en/LC_MESSAGES/django.po\n"
"X-Crowdin-File-ID: 250\n"

#: InvenTree/AllUserRequire2FAMiddleware.py:42
msgid "You must enable two-factor authentication before doing anything else."
msgstr "<PERSON><PERSON><PERSON>, než budete dělat co<PERSON>li <PERSON>, mus<PERSON><PERSON> zap<PERSON> dvoufaktorové ověřování."

#: InvenTree/api.py:355
msgid "API endpoint not found"
msgstr "API endpoint nebyl nalezen"

#: InvenTree/api.py:432
msgid "List of items or filters must be provided for bulk operation"
msgstr "Seznam položek nebo filtrů musí být k dispozici pro hromadnou operaci"

#: InvenTree/api.py:439
msgid "Items must be provided as a list"
msgstr "Položky musí být uvedeny jako seznam"

#: InvenTree/api.py:447
msgid "Invalid items list provided"
msgstr "Zadán neplatný seznam položek"

#: InvenTree/api.py:453
msgid "Filters must be provided as a dict"
msgstr "Filtry musí být uvedeny jako slovník"

#: InvenTree/api.py:460
msgid "Invalid filters provided"
msgstr "Poskytnuty neplatné filtry"

#: InvenTree/api.py:465
msgid "All filter must only be used with true"
msgstr ""

#: InvenTree/api.py:470
msgid "No items match the provided criteria"
msgstr "Zadaným kritériím neodpovídají žádné položky"

#: InvenTree/api.py:713
msgid "User does not have permission to view this model"
msgstr "Uživatel nemá právo zobrazit tento model"

#: InvenTree/auth_overrides.py:57
msgid "Email (again)"
msgstr "Email (znovu)"

#: InvenTree/auth_overrides.py:61
msgid "Email address confirmation"
msgstr "Potvrzení emailové adresy"

#: InvenTree/auth_overrides.py:84
msgid "You must type the same email each time."
msgstr "Pokaždé musíte zadat stejný email."

#: InvenTree/auth_overrides.py:126 InvenTree/auth_overrides.py:134
msgid "The provided primary email address is not valid."
msgstr "Zadaná primární e-mailová adresa je neplatná."

#: InvenTree/auth_overrides.py:140
msgid "The provided email domain is not approved."
msgstr "Zadaná e-mailová doména není povolena."

#: InvenTree/conversion.py:162
#, python-brace-format
msgid "Invalid unit provided ({unit})"
msgstr "Zadána neplatná jednotka ({unit})"

#: InvenTree/conversion.py:179
msgid "No value provided"
msgstr "Není k dispozici žádná hodnota"

#: InvenTree/conversion.py:206
#, python-brace-format
msgid "Could not convert {original} to {unit}"
msgstr "Nelze převést {original} na {unit}"

#: InvenTree/conversion.py:208 InvenTree/conversion.py:222
#: InvenTree/helpers.py:548 order/models.py:707 order/models.py:975
msgid "Invalid quantity provided"
msgstr "Vyplněno neplatné množství"

#: InvenTree/exceptions.py:109
msgid "Error details can be found in the admin panel"
msgstr "Podrobnosti o chybě lze nalézt v panelu administrace"

#: InvenTree/fields.py:138
msgid "Enter date"
msgstr "Zadejte datum"

#: InvenTree/fields.py:161
msgid "Invalid decimal value"
msgstr "Neplaté desetinné číslo"

#: InvenTree/fields.py:210 InvenTree/models.py:887 build/serializers.py:526
#: build/serializers.py:597 company/models.py:834 order/models.py:1579
#: part/models.py:3366
#: report/templates/report/inventree_build_order_report.html:172
#: stock/models.py:2708 stock/models.py:2832 stock/serializers.py:756
#: stock/serializers.py:918 stock/serializers.py:1048 stock/serializers.py:1100
#: stock/serializers.py:1419 stock/serializers.py:1508
#: stock/serializers.py:1684
msgid "Notes"
msgstr "Poznámky"

#: InvenTree/format.py:166
#, python-brace-format
msgid "Value '{name}' does not appear in pattern format"
msgstr "Hodnota '{name}' neodpovídá formátu vzoru"

#: InvenTree/format.py:177
msgid "Provided value does not match required pattern: "
msgstr "Poskytnutá hodnota neodpovídá požadovanému vzoru: "

#: InvenTree/helpers.py:552
msgid "Cannot serialize more than 1000 items at once"
msgstr "Nelze serializovat více než 1000 položek najednou"

#: InvenTree/helpers.py:558
msgid "Empty serial number string"
msgstr "Nevyplněné výrobní číslo"

#: InvenTree/helpers.py:587
msgid "Duplicate serial"
msgstr "Duplicitní výrobní číslo"

#: InvenTree/helpers.py:619 InvenTree/helpers.py:662 InvenTree/helpers.py:680
#: InvenTree/helpers.py:687 InvenTree/helpers.py:706
#, python-brace-format
msgid "Invalid group: {group}"
msgstr "Neplatná skupina: {group}"

#: InvenTree/helpers.py:650
#, python-brace-format
msgid "Group range {group} exceeds allowed quantity ({expected_quantity})"
msgstr "Rozsah skupiny {group} překračuje povolené množství ({expected_quantity})"

#: InvenTree/helpers.py:716
msgid "No serial numbers found"
msgstr "Nenalezena žádná výrobní čísla"

#: InvenTree/helpers.py:721
msgid "Number of unique serial numbers ({len(serials)}) must match quantity ({expected_quantity})"
msgstr "Počet jedinečných sériových čísel ({len(serials)}) musí odpovídat množství ({expected_quantity})"

#: InvenTree/helpers.py:840
msgid "Remove HTML tags from this value"
msgstr "Odstranit HTML tagy z této hodnoty"

#: InvenTree/helpers.py:919
msgid "Data contains prohibited markdown content"
msgstr "Data obsahují zakázaný markdown obsah"

#: InvenTree/helpers_model.py:131
msgid "Connection error"
msgstr "Chyba spojení"

#: InvenTree/helpers_model.py:136 InvenTree/helpers_model.py:143
msgid "Server responded with invalid status code"
msgstr "Server odpověděl s neplatným stavovým kódem"

#: InvenTree/helpers_model.py:139
msgid "Exception occurred"
msgstr "Došlo k výjimce"

#: InvenTree/helpers_model.py:149
msgid "Server responded with invalid Content-Length value"
msgstr "Server odpověděl s neplatnou hodnotou Content-Length"

#: InvenTree/helpers_model.py:152
msgid "Image size is too large"
msgstr "Velikost obrázku je příliš velká"

#: InvenTree/helpers_model.py:164
msgid "Image download exceeded maximum size"
msgstr "Stahování obrázku překročilo maximální velikost"

#: InvenTree/helpers_model.py:169
msgid "Remote server returned empty response"
msgstr "Vzdálený server vrátil prázdnou odpověď"

#: InvenTree/helpers_model.py:177
msgid "Supplied URL is not a valid image file"
msgstr "Zadaná URL adresa není platný soubor obrázku"

#: InvenTree/locales.py:20
msgid "Arabic"
msgstr "Arabština"

#: InvenTree/locales.py:21
msgid "Bulgarian"
msgstr "Bulharština"

#: InvenTree/locales.py:22
msgid "Czech"
msgstr "Čeština"

#: InvenTree/locales.py:23
msgid "Danish"
msgstr "Dánština"

#: InvenTree/locales.py:24
msgid "German"
msgstr "Němčina"

#: InvenTree/locales.py:25
msgid "Greek"
msgstr "Řečtina"

#: InvenTree/locales.py:26
msgid "English"
msgstr "Angličtina"

#: InvenTree/locales.py:27
msgid "Spanish"
msgstr "Španělština"

#: InvenTree/locales.py:28
msgid "Spanish (Mexican)"
msgstr "Španělština (Mexiko)"

#: InvenTree/locales.py:29
msgid "Estonian"
msgstr "Estonština"

#: InvenTree/locales.py:30
msgid "Farsi / Persian"
msgstr "Farsi / Perština"

#: InvenTree/locales.py:31
msgid "Finnish"
msgstr "Finština"

#: InvenTree/locales.py:32
msgid "French"
msgstr "Francouzština"

#: InvenTree/locales.py:33
msgid "Hebrew"
msgstr "Hebrejština"

#: InvenTree/locales.py:34
msgid "Hindi"
msgstr "Hindština"

#: InvenTree/locales.py:35
msgid "Hungarian"
msgstr "Maďarština"

#: InvenTree/locales.py:36
msgid "Italian"
msgstr "Italština"

#: InvenTree/locales.py:37
msgid "Japanese"
msgstr "Japonština"

#: InvenTree/locales.py:38
msgid "Korean"
msgstr "Korejština"

#: InvenTree/locales.py:39
msgid "Lithuanian"
msgstr "Litevština"

#: InvenTree/locales.py:40
msgid "Latvian"
msgstr "Lotyština"

#: InvenTree/locales.py:41
msgid "Dutch"
msgstr "Nizozemština"

#: InvenTree/locales.py:42
msgid "Norwegian"
msgstr "Norština"

#: InvenTree/locales.py:43
msgid "Polish"
msgstr "Polština"

#: InvenTree/locales.py:44
msgid "Portuguese"
msgstr "Portugalština"

#: InvenTree/locales.py:45
msgid "Portuguese (Brazilian)"
msgstr "Portugalština (Brazilská)"

#: InvenTree/locales.py:46
msgid "Romanian"
msgstr "Rumunština"

#: InvenTree/locales.py:47
msgid "Russian"
msgstr "Ruština"

#: InvenTree/locales.py:48
msgid "Slovak"
msgstr "Slovenština"

#: InvenTree/locales.py:49
msgid "Slovenian"
msgstr "Slovinština"

#: InvenTree/locales.py:50
msgid "Serbian"
msgstr "Srbština"

#: InvenTree/locales.py:51
msgid "Swedish"
msgstr "Švédština"

#: InvenTree/locales.py:52
msgid "Thai"
msgstr "Thajština"

#: InvenTree/locales.py:53
msgid "Turkish"
msgstr "Turečtina"

#: InvenTree/locales.py:54
msgid "Ukrainian"
msgstr "Ukrajinština"

#: InvenTree/locales.py:55
msgid "Vietnamese"
msgstr "Vietnamština"

#: InvenTree/locales.py:56
msgid "Chinese (Simplified)"
msgstr "Čínština (zjednodušená)"

#: InvenTree/locales.py:57
msgid "Chinese (Traditional)"
msgstr "Čínština (tradiční)"

#: InvenTree/magic_login.py:31
msgid "Log in to the app"
msgstr "Přihlásit se do aplikace"

#: InvenTree/magic_login.py:41 company/models.py:136 users/serializers.py:247
msgid "Email"
msgstr "E-mail"

#: InvenTree/models.py:107
msgid "Error running plugin validation"
msgstr "Chyba při ověření pluginu"

#: InvenTree/models.py:184
msgid "Metadata must be a python dict object"
msgstr "Metadata musí být objekt python dict"

#: InvenTree/models.py:190
msgid "Plugin Metadata"
msgstr "Metadata pluginu"

#: InvenTree/models.py:191
msgid "JSON metadata field, for use by external plugins"
msgstr "Pole metadat JSON pro použití externími pluginy"

#: InvenTree/models.py:374
msgid "Improperly formatted pattern"
msgstr "Nesprávně naformátovaný vzor"

#: InvenTree/models.py:381
msgid "Unknown format key specified"
msgstr "Neznámý formát klíče"

#: InvenTree/models.py:387
msgid "Missing required format key"
msgstr "Chybí požadovaný klíč"

#: InvenTree/models.py:398
msgid "Reference field cannot be empty"
msgstr "Referenční pole nemůže být prázdné"

#: InvenTree/models.py:406
msgid "Reference must match required pattern"
msgstr "Referenční číslo musí odpovídat požadovanému vzoru"

#: InvenTree/models.py:437
msgid "Reference number is too large"
msgstr "Referenční číslo je příliš velké"

#: InvenTree/models.py:688
msgid "Duplicate names cannot exist under the same parent"
msgstr "Duplicitní názvy nemohou existovat pod stejným nadřazeným názvem"

#: InvenTree/models.py:705
msgid "Invalid choice"
msgstr "Neplatný výběr"

#: InvenTree/models.py:734 common/models.py:1325 common/models.py:1752
#: common/models.py:2011 common/models.py:2136 common/serializers.py:488
#: company/models.py:593 generic/states/serializers.py:20 machine/models.py:24
#: part/models.py:1039 part/models.py:3837 plugin/models.py:53
#: report/models.py:203 stock/models.py:83
msgid "Name"
msgstr "Název"

#: InvenTree/models.py:740 build/models.py:242 common/models.py:123
#: common/models.py:2143 common/models.py:2256 company/models.py:521
#: company/models.py:825 order/models.py:429 order/models.py:1615
#: part/models.py:1062 part/models.py:3852 report/models.py:209
#: report/models.py:714 report/models.py:740
#: report/templates/report/inventree_build_order_report.html:117
#: stock/models.py:89
msgid "Description"
msgstr "Popis"

#: InvenTree/models.py:741 stock/models.py:90
msgid "Description (optional)"
msgstr "Popis (volitelně)"

#: InvenTree/models.py:756 common/models.py:2309
msgid "Path"
msgstr "Cesta"

#: InvenTree/models.py:887
msgid "Markdown notes (optional)"
msgstr "Poznámky (volitelné)"

#: InvenTree/models.py:918
msgid "Barcode Data"
msgstr "Data čárového kódu"

#: InvenTree/models.py:919
msgid "Third party barcode data"
msgstr "Data čárového kódu třetí strany"

#: InvenTree/models.py:925
msgid "Barcode Hash"
msgstr "Hash čárového kódu"

#: InvenTree/models.py:926
msgid "Unique hash of barcode data"
msgstr "Jedinečný hash dat čárového kódu"

#: InvenTree/models.py:1003
msgid "Existing barcode found"
msgstr "Nalezen existující čárový kód"

#: InvenTree/models.py:1084
msgid "Task Failure"
msgstr "Selhání úlohy"

#: InvenTree/models.py:1085
#, python-brace-format
msgid "Background worker task '{f}' failed after {n} attempts"
msgstr "Úloha na pozadí '{f}' se ani po {n} pokusech nezdařila"

#: InvenTree/models.py:1112
msgid "Server Error"
msgstr "Chyba serveru"

#: InvenTree/models.py:1113
msgid "An error has been logged by the server."
msgstr "Server zaznamenal chybu."

#: InvenTree/serializers.py:69 part/models.py:4455
msgid "Must be a valid number"
msgstr "Musí být platné číslo"

#: InvenTree/serializers.py:111 company/models.py:187 part/models.py:3184
msgid "Currency"
msgstr "Měna"

#: InvenTree/serializers.py:114 part/serializers.py:1335
msgid "Select currency from available options"
msgstr "Vyberte měnu z dostupných možností"

#: InvenTree/serializers.py:466
msgid "Invalid value"
msgstr "Neplatná hodnota"

#: InvenTree/serializers.py:503
msgid "Remote Image"
msgstr "Vzdálený obraz"

#: InvenTree/serializers.py:504
msgid "URL of remote image file"
msgstr "URL souboru vzdáleného obrázku"

#: InvenTree/serializers.py:522
msgid "Downloading images from remote URL is not enabled"
msgstr "Stahování obrázků ze vzdálené URL není povoleno"

#: InvenTree/serializers.py:529
msgid "Failed to download image from remote URL"
msgstr "Nepodařilo se stáhnout obrázek ze vzdálené adresy URL"

#: InvenTree/validators.py:30
msgid "Invalid physical unit"
msgstr "Neplatná fyzikální jednotka"

#: InvenTree/validators.py:36
msgid "Not a valid currency code"
msgstr "Neplatný kód měny"

#: InvenTree/validators.py:116 InvenTree/validators.py:132
msgid "Overage value must not be negative"
msgstr "Přidaná hodnota nesmí být záporná"

#: InvenTree/validators.py:134
msgid "Overage must not exceed 100%"
msgstr "Nesmí přesáhnout 100%"

#: InvenTree/validators.py:140
msgid "Invalid value for overage"
msgstr "Neplatná hodnota překročení"

#: build/api.py:39 order/api.py:104 order/api.py:263 order/serializers.py:128
msgid "Order Status"
msgstr "Stav objednávky"

#: build/api.py:65 build/models.py:254
msgid "Parent Build"
msgstr "Nadřazená sestava"

#: build/api.py:69 build/api.py:768 order/api.py:512 order/api.py:732
#: order/api.py:1106 order/api.py:1334 stock/api.py:522
msgid "Include Variants"
msgstr "Zahrnout varianty"

#: build/api.py:85 build/api.py:470 build/api.py:782 build/models.py:260
#: build/serializers.py:1254 build/serializers.py:1376
#: build/serializers.py:1427 company/models.py:1044 company/serializers.py:432
#: order/api.py:291 order/api.py:295 order/api.py:880 order/api.py:1119
#: order/api.py:1122 order/models.py:1704 order/models.py:1863
#: order/models.py:1864 part/api.py:1448 part/api.py:1451 part/api.py:1517
#: part/api.py:1827 part/models.py:457 part/models.py:3195 part/models.py:3339
#: part/models.py:3487 part/models.py:3508 part/models.py:3530
#: part/models.py:3666 part/models.py:4027 part/models.py:4299
#: part/models.py:4665 part/serializers.py:1255 part/serializers.py:1924
#: report/templates/report/inventree_bill_of_materials_report.html:110
#: report/templates/report/inventree_bill_of_materials_report.html:137
#: report/templates/report/inventree_build_order_report.html:109
#: report/templates/report/inventree_purchase_order_report.html:27
#: report/templates/report/inventree_return_order_report.html:24
#: report/templates/report/inventree_sales_order_report.html:27
#: report/templates/report/inventree_sales_order_shipment_report.html:28
#: report/templates/report/inventree_stock_location_report.html:102
#: stock/api.py:535 stock/serializers.py:119 stock/serializers.py:171
#: stock/serializers.py:481 stock/serializers.py:643 stock/serializers.py:948
#: templates/email/build_order_completed.html:17
#: templates/email/build_order_required_stock.html:17
#: templates/email/low_stock_notification.html:15
#: templates/email/overdue_build_order.html:16
#: templates/email/part_event_notification.html:15
msgid "Part"
msgstr "Díl"

#: build/api.py:105 build/api.py:108 part/api.py:1531 part/models.py:1080
#: part/models.py:3558 part/models.py:4136 part/serializers.py:1265
#: stock/api.py:818
msgid "Category"
msgstr "Kategorie"

#: build/api.py:116 build/api.py:120
msgid "Ancestor Build"
msgstr "Sestava předků"

#: build/api.py:137 order/api.py:122
msgid "Assigned to me"
msgstr "Přiřazeno mě"

#: build/api.py:152
#: report/templates/report/inventree_build_order_report.html:152
msgid "Issued By"
msgstr "Vystavil"

#: build/api.py:167
msgid "Assigned To"
msgstr "Přiřazeno"

#: build/api.py:202
msgid "Created before"
msgstr "Vytvořeno před"

#: build/api.py:206
msgid "Created after"
msgstr "Vytvořeno po"

#: build/api.py:210
msgid "Has start date"
msgstr "Má počáteční datum"

#: build/api.py:218
msgid "Start date before"
msgstr "Datum začátku před"

#: build/api.py:222
msgid "Start date after"
msgstr "Datum začátku po"

#: build/api.py:226
msgid "Has target date"
msgstr "Má cílové datum"

#: build/api.py:234
msgid "Target date before"
msgstr "Cílové datum před"

#: build/api.py:238
msgid "Target date after"
msgstr "Cílové datum po"

#: build/api.py:242
msgid "Completed before"
msgstr "Dokončeno před"

#: build/api.py:246
msgid "Completed after"
msgstr "Dokončeno po"

#: build/api.py:249 order/api.py:219
msgid "Min Date"
msgstr "Min. datum"

#: build/api.py:272 order/api.py:238
msgid "Max Date"
msgstr "Max datum"

#: build/api.py:297 build/api.py:300 part/api.py:224
msgid "Exclude Tree"
msgstr "Vyloučit strom"

#: build/api.py:409
msgid "Build must be cancelled before it can be deleted"
msgstr "Sestavení musí být zrušeno před odstraněním"

#: build/api.py:453 build/serializers.py:1392 part/models.py:4333
msgid "Consumable"
msgstr "Spotřební materiál"

#: build/api.py:456 build/serializers.py:1395 part/models.py:4327
msgid "Optional"
msgstr "Volitelné"

#: build/api.py:459 common/setting/system.py:429 part/models.py:1211
#: part/serializers.py:1663 part/serializers.py:1672 stock/api.py:588
msgid "Assembly"
msgstr "Sestava"

#: build/api.py:462
msgid "Tracked"
msgstr "Sledováno"

#: build/api.py:465 build/serializers.py:1398 part/models.py:1229
msgid "Testable"
msgstr "Testovatelné"

#: build/api.py:475 order/api.py:925
msgid "Order Outstanding"
msgstr "Objednávka nevyřízená"

#: build/api.py:485 order/api.py:884
msgid "Allocated"
msgstr "Přiděleno"

#: build/api.py:495 company/models.py:889 company/serializers.py:427
#: templates/email/build_order_required_stock.html:19
#: templates/email/low_stock_notification.html:17
#: templates/email/part_event_notification.html:18
msgid "Available"
msgstr "Dostupné"

#: build/api.py:805 build/models.py:116
#: report/templates/report/inventree_build_order_report.html:105
#: stock/serializers.py:92 templates/email/build_order_completed.html:16
#: templates/email/overdue_build_order.html:15
msgid "Build Order"
msgstr "Výrobní příkaz"

#: build/models.py:117 users/ruleset.py:37
msgid "Build Orders"
msgstr "Výrobní příkazy"

#: build/models.py:162
msgid "Assembly BOM has not been validated"
msgstr "Kusovník sestavy ještě nebyl schválen"

#: build/models.py:169
msgid "Build order cannot be created for an inactive part"
msgstr "Výrobní příkaz nesmí být vytvořen pro neaktivní díl"

#: build/models.py:176
msgid "Build order cannot be created for an unlocked part"
msgstr ""

#: build/models.py:188
msgid "Invalid choice for parent build"
msgstr "Neplatná volba nadřazeného sestavení"

#: build/models.py:197 order/models.py:363
msgid "Responsible user or group must be specified"
msgstr "Musí být specifikován odpovědný uživatel nebo skupina"

#: build/models.py:202
msgid "Build order part cannot be changed"
msgstr "Díl výrobního příkazu nelze změnit"

#: build/models.py:207 order/models.py:376
msgid "Target date must be after start date"
msgstr "Cílové datum musí být po datu zahájení"

#: build/models.py:235
msgid "Build Order Reference"
msgstr "Referenční číslo výrobního příkazu"

#: build/models.py:236 build/serializers.py:1389 order/models.py:601
#: order/models.py:1140 order/models.py:1572 order/models.py:2403
#: part/models.py:4348
#: report/templates/report/inventree_bill_of_materials_report.html:139
#: report/templates/report/inventree_purchase_order_report.html:28
#: report/templates/report/inventree_return_order_report.html:26
#: report/templates/report/inventree_sales_order_report.html:28
msgid "Reference"
msgstr "Reference"

#: build/models.py:245
msgid "Brief description of the build (optional)"
msgstr "Stručný popis sestavení (nepovinné)"

#: build/models.py:255
msgid "BuildOrder to which this build is allocated"
msgstr "Výrobní příkaz, kterému je tento výrobní příkaz přidělen"

#: build/models.py:264
msgid "Select part to build"
msgstr "Vyber téma, které chceš stavět"

#: build/models.py:269
msgid "Sales Order Reference"
msgstr "Referenční číslo prodejní objednávky"

#: build/models.py:274
msgid "SalesOrder to which this build is allocated"
msgstr "Prodejní objednávka, které je tento výrobní příkaz přidělen"

#: build/models.py:279 build/serializers.py:1104
msgid "Source Location"
msgstr "Zdrojové umístění"

#: build/models.py:285
msgid "Select location to take stock from for this build (leave blank to take from any stock location)"
msgstr "Vyberte lokaci, ze které chcete brát zásoby pro sestavu (nechte prázdné, chcete-li brát zásoby z libovolné lokace)"

#: build/models.py:291
msgid "Destination Location"
msgstr "Cílová lokace"

#: build/models.py:296
msgid "Select location where the completed items will be stored"
msgstr "Vyberte lokaci, kde budou dokončené položky uloženy"

#: build/models.py:300
msgid "Build Quantity"
msgstr "Množství sestav"

#: build/models.py:303
msgid "Number of stock items to build"
msgstr "Počet skladových položek k sestavení"

#: build/models.py:307
msgid "Completed items"
msgstr "Dokončené položky"

#: build/models.py:309
msgid "Number of stock items which have been completed"
msgstr "Počet skladových položek, které byly dokončeny"

#: build/models.py:313
msgid "Build Status"
msgstr "Stav sestavení"

#: build/models.py:318
msgid "Build status code"
msgstr "Stavový kód sestavení"

#: build/models.py:327 build/serializers.py:376 order/serializers.py:785
#: stock/models.py:1023 stock/serializers.py:84 stock/serializers.py:1651
msgid "Batch Code"
msgstr "Kód dávky"

#: build/models.py:331 build/serializers.py:377
msgid "Batch code for this build output"
msgstr "Dávkový kód pro tento výstup sestavení"

#: build/models.py:335 order/models.py:466 order/serializers.py:166
#: part/models.py:1286
msgid "Creation Date"
msgstr "Datum vytvoření"

#: build/models.py:341
msgid "Build start date"
msgstr "Datum zahájení sestavení"

#: build/models.py:342
msgid "Scheduled start date for this build order"
msgstr "Plánované datum zahájení této objednávky"

#: build/models.py:348
msgid "Target completion date"
msgstr "Cílové datum dokončení"

#: build/models.py:350
msgid "Target date for build completion. Build will be overdue after this date."
msgstr "Cílové datum dokončení sestavení. Sestavení bude po tomto datu v prodlení."

#: build/models.py:355 order/models.py:654 order/models.py:2442
msgid "Completion Date"
msgstr "Datum dokončení"

#: build/models.py:363
msgid "completed by"
msgstr "dokončil"

#: build/models.py:372
msgid "Issued by"
msgstr "Vystavil"

#: build/models.py:373
msgid "User who issued this build order"
msgstr "Uživatel, který vystavil tento výrobní příkaz"

#: build/models.py:382 common/models.py:132 order/api.py:172
#: order/models.py:491 part/models.py:1303
#: report/templates/report/inventree_build_order_report.html:158
msgid "Responsible"
msgstr "Odpovědný"

#: build/models.py:383
msgid "User or group responsible for this build order"
msgstr "Uživatel nebo skupina odpovědná za tento výrobní příkaz"

#: build/models.py:388 stock/models.py:1016
msgid "External Link"
msgstr "Externí odkaz"

#: build/models.py:390 common/models.py:1899 part/models.py:1114
#: stock/models.py:1018
msgid "Link to external URL"
msgstr "Odkaz na externí URL"

#: build/models.py:395
msgid "Build Priority"
msgstr "Priorita sestavení"

#: build/models.py:398
msgid "Priority of this build order"
msgstr "Priorita tohoto výrobního příkazu"

#: build/models.py:406 common/models.py:102 common/models.py:116
#: order/api.py:158 order/models.py:438
msgid "Project Code"
msgstr "Kód projektu"

#: build/models.py:407
msgid "Project code for this build order"
msgstr "Kód projektu pro tento výrobní příkaz"

#: build/models.py:680 build/models.py:803
msgid "Failed to offload task to complete build allocations"
msgstr "Nepodařilo se uvolnit úlohu pro dokončení přidělení sestavy"

#: build/models.py:703
#, python-brace-format
msgid "Build order {build} has been completed"
msgstr "Výrobní příkaz {build} byl dokončen"

#: build/models.py:709
msgid "A build order has been completed"
msgstr "Výrobní příkaz byl dokončen"

#: build/models.py:881 build/serializers.py:424
msgid "Serial numbers must be provided for trackable parts"
msgstr "U sledovatelných dílů musí být uvedena sériová čísla"

#: build/models.py:991 build/models.py:1076
msgid "No build output specified"
msgstr "Nebyl specifikováno žádný výstup sestavení"

#: build/models.py:994
msgid "Build output is already completed"
msgstr "Výstup sestavení je již dokončen"

#: build/models.py:997
msgid "Build output does not match Build Order"
msgstr "Výstup neodpovídá výrobnímu příkazu"

#: build/models.py:1079 build/serializers.py:303 build/serializers.py:352
#: build/serializers.py:972 order/models.py:704 order/serializers.py:601
#: order/serializers.py:780 part/serializers.py:1657 stock/models.py:863
#: stock/models.py:1730 stock/serializers.py:727
msgid "Quantity must be greater than zero"
msgstr "Množství musí být vyšší než nula"

#: build/models.py:1083 build/serializers.py:307
msgid "Quantity cannot be greater than the output quantity"
msgstr "Množství nemůže být větší než výstupní množství"

#: build/models.py:1148 build/serializers.py:615
#, python-brace-format
msgid "Build output {serial} has not passed all required tests"
msgstr "Výstup sestavy {serial} neprošel všemi požadavky"

#: build/models.py:1534
msgid "Build Order Line Item"
msgstr "Řádková položka výrobního příkazu"

#: build/models.py:1558
msgid "Build object"
msgstr "Vytvořit objekt"

#: build/models.py:1570 build/models.py:1829 build/serializers.py:291
#: build/serializers.py:337 build/serializers.py:1410 common/models.py:1255
#: order/models.py:1555 order/models.py:2288 order/serializers.py:1670
#: order/serializers.py:2131 part/models.py:3353 part/models.py:4321
#: part/serializers.py:275
#: report/templates/report/inventree_bill_of_materials_report.html:138
#: report/templates/report/inventree_build_order_report.html:113
#: report/templates/report/inventree_purchase_order_report.html:29
#: report/templates/report/inventree_sales_order_report.html:29
#: report/templates/report/inventree_sales_order_shipment_report.html:47
#: report/templates/report/inventree_sales_order_shipment_report.html:49
#: report/templates/report/inventree_stock_location_report.html:104
#: report/templates/report/inventree_test_report.html:90
#: report/templates/report/inventree_test_report.html:169
#: stock/serializers.py:135 stock/serializers.py:179 stock/serializers.py:718
#: templates/email/build_order_completed.html:18
msgid "Quantity"
msgstr "Množství"

#: build/models.py:1571
msgid "Required quantity for build order"
msgstr "Vyžadované množství pro výrobní příkaz"

#: build/models.py:1655
msgid "Build item must specify a build output, as master part is marked as trackable"
msgstr "Položka sestavení musí specifikovat výstup sestavení, protože hlavní díl je označen jako sledovatelný"

#: build/models.py:1666
#, python-brace-format
msgid "Allocated quantity ({q}) must not exceed available stock quantity ({a})"
msgstr "Zabrané množství ({q}) nesmí překročit dostupné skladové množství ({a})"

#: build/models.py:1687 order/models.py:2237
msgid "Stock item is over-allocated"
msgstr "Skladová položka je nadměrně zabrána"

#: build/models.py:1692 order/models.py:2240
msgid "Allocation quantity must be greater than zero"
msgstr "Zabrané množství musí být větší než nula"

#: build/models.py:1698
msgid "Quantity must be 1 for serialized stock"
msgstr "Množství musí být 1 pro zřetězený sklad"

#: build/models.py:1758
msgid "Selected stock item does not match BOM line"
msgstr "Vybraná skladová položka neodpovídá řádku kusovníku"

#: build/models.py:1819 build/serializers.py:955 build/serializers.py:1263
#: order/serializers.py:1507 order/serializers.py:1528
#: report/templates/report/inventree_sales_order_shipment_report.html:29
#: stock/models.py:452 stock/serializers.py:101 stock/serializers.py:825
#: stock/serializers.py:1357 stock/serializers.py:1469
msgid "Stock Item"
msgstr "Skladové položky"

#: build/models.py:1820
msgid "Source stock item"
msgstr "Zdrojová skladová položka"

#: build/models.py:1830
msgid "Stock quantity to allocate to build"
msgstr "Skladové množství pro sestavení"

#: build/models.py:1839
msgid "Install into"
msgstr "Instalovat do"

#: build/models.py:1840
msgid "Destination stock item"
msgstr "Cílová skladová položka"

#: build/serializers.py:116
msgid "Build Level"
msgstr "Úroveň sestavení"

#: build/serializers.py:125 part/stocktake.py:219
msgid "Part Name"
msgstr "Název dílu"

#: build/serializers.py:143
msgid "Project Code Label"
msgstr "Popisek kódu projektu"

#: build/serializers.py:155
msgid "Create Child Builds"
msgstr "Vytvořit podřízené sestavení"

#: build/serializers.py:156
msgid "Automatically generate child build orders"
msgstr "Automaticky generovat potomky"

#: build/serializers.py:239 build/serializers.py:981
msgid "Build Output"
msgstr "Vytvořit výstup"

#: build/serializers.py:251
msgid "Build output does not match the parent build"
msgstr "Vytvořený výstup neodpovídá nadřazenému sestavení"

#: build/serializers.py:255
msgid "Output part does not match BuildOrder part"
msgstr "Výstupní díl se neshoduje s dílem výrobního příkazu"

#: build/serializers.py:259
msgid "This build output has already been completed"
msgstr "Výstup sestavení je již dokončen"

#: build/serializers.py:273
msgid "This build output is not fully allocated"
msgstr "Tento stavební výstup není plně přiřazen"

#: build/serializers.py:292 build/serializers.py:338
msgid "Enter quantity for build output"
msgstr "Zadejte množství pro výstup sestavení"

#: build/serializers.py:360
msgid "Integer quantity required for trackable parts"
msgstr "Celé množství požadované pro sledovatelné díly"

#: build/serializers.py:366
msgid "Integer quantity required, as the bill of materials contains trackable parts"
msgstr "Je vyžadována celočíselná hodnota množství, protože kusovník obsahuje sledovatelné díly"

#: build/serializers.py:383 order/serializers.py:801 order/serializers.py:1674
#: stock/serializers.py:738
msgid "Serial Numbers"
msgstr "Sériová čísla"

#: build/serializers.py:384
msgid "Enter serial numbers for build outputs"
msgstr "Zadejte sériová čísla pro sestavení výstupů"

#: build/serializers.py:389 build/serializers.py:514 build/serializers.py:584
#: build/serializers.py:1267 build/serializers.py:1271 order/serializers.py:769
#: order/serializers.py:916 order/serializers.py:2022 part/serializers.py:1275
#: stock/serializers.py:110 stock/serializers.py:647 stock/serializers.py:749
#: stock/serializers.py:913 stock/serializers.py:1041 stock/serializers.py:1501
#: stock/serializers.py:1789 users/models.py:551
msgid "Location"
msgstr "Lokace"

#: build/serializers.py:390
msgid "Stock location for build output"
msgstr "Skladové umístění pro výstup sestavy"

#: build/serializers.py:405
msgid "Auto Allocate Serial Numbers"
msgstr "Automaticky zvolit sériová čísla"

#: build/serializers.py:407
msgid "Automatically allocate required items with matching serial numbers"
msgstr "Automaticky přidělit požadované položky s odpovídajícími sériovými čísly"

#: build/serializers.py:440 order/serializers.py:894 stock/api.py:1077
#: stock/models.py:1753
msgid "The following serial numbers already exist or are invalid"
msgstr "Následující sériová čísla již existují nebo jsou neplatná"

#: build/serializers.py:482 build/serializers.py:538 build/serializers.py:622
msgid "A list of build outputs must be provided"
msgstr "Musí být uveden seznam výstupů sestavy"

#: build/serializers.py:515
msgid "Stock location for scrapped outputs"
msgstr "Skladové umístění pro sešrotované výstupy"

#: build/serializers.py:521
msgid "Discard Allocations"
msgstr "Zahodit alokace"

#: build/serializers.py:522
msgid "Discard any stock allocations for scrapped outputs"
msgstr "Vyřadit všechny přidělené zásoby pro vyřazené výstupy"

#: build/serializers.py:527
msgid "Reason for scrapping build output(s)"
msgstr "Důvod vyřazení výstupu(ů) sestavy"

#: build/serializers.py:585
msgid "Location for completed build outputs"
msgstr "Umístění dokončených výstupů sestavy"

#: build/serializers.py:593
msgid "Accept Incomplete Allocation"
msgstr "Přijmout neúplné přidělení"

#: build/serializers.py:594
msgid "Complete outputs if stock has not been fully allocated"
msgstr "Dokončit výstupy pokud zásoby nebyly plně přiděleny"

#: build/serializers.py:709
msgid "Consume Allocated Stock"
msgstr "Spotřebovat přidělené zásoby"

#: build/serializers.py:710
msgid "Consume any stock which has already been allocated to this build"
msgstr "Spotřebovat všechny zásoby, které již byly přiděleny této sestavě"

#: build/serializers.py:716
msgid "Remove Incomplete Outputs"
msgstr "Odstranit neúplné výstupy"

#: build/serializers.py:717
msgid "Delete any build outputs which have not been completed"
msgstr "Odstranit všechny výstupy sestavy, které nebyly dokončeny"

#: build/serializers.py:744
msgid "Not permitted"
msgstr "Není povoleno"

#: build/serializers.py:745
msgid "Accept as consumed by this build order"
msgstr "Přijmout jako spotřebované tímto výrobním příkazem"

#: build/serializers.py:746
msgid "Deallocate before completing this build order"
msgstr "Uvolnit před dokončením tohoto výrobního příkazu"

#: build/serializers.py:773
msgid "Overallocated Stock"
msgstr "Nadměrně přidělené zásoby"

#: build/serializers.py:776
msgid "How do you want to handle extra stock items assigned to the build order"
msgstr "Jak chcete zacházet s extra skladovými položkami přiřazenými k tomuto výrobnímu příkazu"

#: build/serializers.py:787
msgid "Some stock items have been overallocated"
msgstr "Některé skladové položky byly nadměrně přiděleny"

#: build/serializers.py:792
msgid "Accept Unallocated"
msgstr "Přijmout nepřidělené"

#: build/serializers.py:794
msgid "Accept that stock items have not been fully allocated to this build order"
msgstr "Přijmout, že skladové položky nebyly plně přiřazeny k tomuto výrobnímu příkazu"

#: build/serializers.py:805
msgid "Required stock has not been fully allocated"
msgstr "Požadované zásoby nebyly plně přiděleny"

#: build/serializers.py:810 order/serializers.py:444 order/serializers.py:1575
msgid "Accept Incomplete"
msgstr "Přijmout neúplné"

#: build/serializers.py:812
msgid "Accept that the required number of build outputs have not been completed"
msgstr "Přijmout, že nebyl dokončen požadovaný počet výstupů sestavy"

#: build/serializers.py:823
msgid "Required build quantity has not been completed"
msgstr "Požadované množství sestavy nebylo dokončeno"

#: build/serializers.py:835
msgid "Build order has open child build orders"
msgstr ""

#: build/serializers.py:838
msgid "Build order must be in production state"
msgstr ""

#: build/serializers.py:841
msgid "Build order has incomplete outputs"
msgstr "Výrobní příkaz má neúplné výstupy"

#: build/serializers.py:880
msgid "Build Line"
msgstr "Linka sestavy"

#: build/serializers.py:888
msgid "Build output"
msgstr "Výstup sestavy"

#: build/serializers.py:896
msgid "Build output must point to the same build"
msgstr "Výstup sestavy musí odkazovat na stejnou sestavu"

#: build/serializers.py:927
msgid "Build Line Item"
msgstr "Řádková položka sestavy"

#: build/serializers.py:945
msgid "bom_item.part must point to the same part as the build order"
msgstr "bom_item.part musí ukazovat na stejný díl jako výrobní příkaz"

#: build/serializers.py:961 stock/serializers.py:1370
msgid "Item must be in stock"
msgstr "Položka musí být skladem"

#: build/serializers.py:1004 order/serializers.py:1561
#, python-brace-format
msgid "Available quantity ({q}) exceeded"
msgstr "Dostupné množství ({q}) překročeno"

#: build/serializers.py:1010
msgid "Build output must be specified for allocation of tracked parts"
msgstr "Pro přidělení sledovaných dílů musí být zadán výstup sestavy"

#: build/serializers.py:1018
msgid "Build output cannot be specified for allocation of untracked parts"
msgstr "Výstup sestavy nelze zadat pro přidělení nesledovaných dílů"

#: build/serializers.py:1042 order/serializers.py:1834
msgid "Allocation items must be provided"
msgstr "Položky přidělení musí být poskytnuty"

#: build/serializers.py:1106
msgid "Stock location where parts are to be sourced (leave blank to take from any location)"
msgstr "Skladové místo, odkud se mají díly odebírat (ponechte prázdné, pokud chcete odebírat z libovolného místa)"

#: build/serializers.py:1115
msgid "Exclude Location"
msgstr "Vynechat lokace"

#: build/serializers.py:1116
msgid "Exclude stock items from this selected location"
msgstr "Vyloučit skladové položky z tohoto vybraného umístění"

#: build/serializers.py:1121
msgid "Interchangeable Stock"
msgstr "Zaměnitelné zásoby"

#: build/serializers.py:1122
msgid "Stock items in multiple locations can be used interchangeably"
msgstr "Skladové položky na více místech lze používat zaměnitelně"

#: build/serializers.py:1127
msgid "Substitute Stock"
msgstr "Náhradní zásoby"

#: build/serializers.py:1128
msgid "Allow allocation of substitute parts"
msgstr "Povolit přidělování náhradních dílů"

#: build/serializers.py:1133
msgid "Optional Items"
msgstr "Volitelné položky"

#: build/serializers.py:1134
msgid "Allocate optional BOM items to build order"
msgstr "Přiřazení volitelných položek kusovníku k objednávce sestavy"

#: build/serializers.py:1156
msgid "Failed to start auto-allocation task"
msgstr "Nepodařilo se spustit úlohu automatického přidělování"

#: build/serializers.py:1230
msgid "BOM Reference"
msgstr "Reference v kusovníku"

#: build/serializers.py:1236
msgid "BOM Part ID"
msgstr "ID dílu kusovníku"

#: build/serializers.py:1243
msgid "BOM Part Name"
msgstr "Název dílu kusovníku"

#: build/serializers.py:1278 build/serializers.py:1434
msgid "Build"
msgstr ""

#: build/serializers.py:1286 company/models.py:662 order/api.py:304
#: order/api.py:309 order/api.py:508 order/serializers.py:593
#: stock/models.py:959 stock/serializers.py:631
msgid "Supplier Part"
msgstr "Díl dodavatele"

#: build/serializers.py:1294 stock/serializers.py:662
msgid "Allocated Quantity"
msgstr "Přidělené množství"

#: build/serializers.py:1371
msgid "Build Reference"
msgstr ""

#: build/serializers.py:1381
msgid "Part Category Name"
msgstr "Název kategorie dílů"

#: build/serializers.py:1401 common/setting/system.py:453 part/models.py:1223
msgid "Trackable"
msgstr "Sledovatelné"

#: build/serializers.py:1404
msgid "Inherited"
msgstr ""

#: build/serializers.py:1407 part/models.py:4381
msgid "Allow Variants"
msgstr "Povolit varianty"

#: build/serializers.py:1412 build/serializers.py:1416 part/models.py:4179
#: part/models.py:4657 stock/api.py:831
msgid "BOM Item"
msgstr "Položka kusovníku"

#: build/serializers.py:1445
msgid "Allocated Stock"
msgstr "Přidělené zásoby"

#: build/serializers.py:1447 company/serializers.py:424
#: order/serializers.py:1271 part/serializers.py:943 part/serializers.py:1690
msgid "On Order"
msgstr "Na objednávku"

#: build/serializers.py:1449 order/serializers.py:1272 part/serializers.py:1694
msgid "In Production"
msgstr "Ve výrobě"

#: build/serializers.py:1451 part/serializers.py:964
msgid "External Stock"
msgstr "Externí zásoby"

#: build/serializers.py:1452 part/serializers.py:1723
msgid "Available Stock"
msgstr "Dostupné zásoby"

#: build/serializers.py:1454
msgid "Available Substitute Stock"
msgstr "Dostupné náhradní zásoby"

#: build/serializers.py:1457
msgid "Available Variant Stock"
msgstr "Dostupná varianta skladu"

#: build/status_codes.py:11 generic/states/tests.py:21
#: generic/states/tests.py:131 order/status_codes.py:12
#: order/status_codes.py:44 order/status_codes.py:76 order/status_codes.py:102
msgid "Pending"
msgstr "Nevyřízeno"

#: build/status_codes.py:12
msgid "Production"
msgstr "Výroba"

#: build/status_codes.py:13 order/status_codes.py:14 order/status_codes.py:51
#: order/status_codes.py:81
msgid "On Hold"
msgstr "Pozastaveno"

#: build/status_codes.py:14 order/status_codes.py:16 order/status_codes.py:53
#: order/status_codes.py:84
msgid "Cancelled"
msgstr "Zrušeno"

#: build/status_codes.py:15 generic/states/tests.py:23 importer/models.py:529
#: importer/status_codes.py:27 order/status_codes.py:15
#: order/status_codes.py:52 order/status_codes.py:83
msgid "Complete"
msgstr "Hotovo"

#: build/tasks.py:193
msgid "Stock required for build order"
msgstr "Zásoby potřebné pro výrobní příkaz"

#: build/tasks.py:267
msgid "Overdue Build Order"
msgstr "Opožděný výrobní příkaz"

#: build/tasks.py:272
#, python-brace-format
msgid "Build order {bo} is now overdue"
msgstr "Objednávka sestavy {bo} je nyní opožděná"

#: common/api.py:710
msgid "Is Link"
msgstr "Je odkaz"

#: common/api.py:718
msgid "Is File"
msgstr "Je soubor"

#: common/api.py:761
msgid "User does not have permission to delete these attachments"
msgstr "Uživatel nemá oprávnění k odstranění těchto příloh"

#: common/api.py:778
msgid "User does not have permission to delete this attachment"
msgstr "Uživatel nemá oprávnění k odstranění této přílohy"

#: common/currency.py:120
msgid "Invalid currency code"
msgstr "Neplatný kód měny"

#: common/currency.py:122
msgid "Duplicate currency code"
msgstr "Duplicitní kód měny"

#: common/currency.py:127
msgid "No valid currency codes provided"
msgstr "Nejsou uvedeny žádné platné kódy měn"

#: common/currency.py:144
msgid "No plugin"
msgstr "Žádný plugin"

#: common/models.py:89
msgid "Updated"
msgstr "Aktualizováno"

#: common/models.py:90
msgid "Timestamp of last update"
msgstr "Časové razítko poslední aktualizace"

#: common/models.py:117
msgid "Unique project code"
msgstr "Jedinečný kód projektu"

#: common/models.py:124
msgid "Project description"
msgstr "Popis projektu"

#: common/models.py:133
msgid "User or group responsible for this project"
msgstr "Uživatel nebo skupina odpovědná za tento projekt"

#: common/models.py:721 common/models.py:1187 common/models.py:1225
msgid "Settings key"
msgstr "Tlačítko nastavení"

#: common/models.py:725
msgid "Settings value"
msgstr "Hodnota nastavení"

#: common/models.py:780
msgid "Chosen value is not a valid option"
msgstr "Zvolená hodnota není platnou možností"

#: common/models.py:796
msgid "Value must be a boolean value"
msgstr "Hodnota musí být logická hodnota"

#: common/models.py:804
msgid "Value must be an integer value"
msgstr "Hodnota musí být celé číslo"

#: common/models.py:812
msgid "Value must be a valid number"
msgstr "Hodnota musí být platné číslo"

#: common/models.py:837
msgid "Value does not pass validation checks"
msgstr "Hodnota neprošla kontrolou platnosti"

#: common/models.py:859
msgid "Key string must be unique"
msgstr "Klíčový text musí být jedinečný"

#: common/models.py:1233 common/models.py:1234 common/models.py:1338
#: common/models.py:1339 common/models.py:1584 common/models.py:1585
#: common/models.py:1915 common/models.py:1916 common/models.py:2297
#: importer/models.py:97 part/models.py:3376 part/models.py:3463
#: part/models.py:3537 part/models.py:3565 plugin/models.py:322
#: plugin/models.py:323 report/templates/report/inventree_test_report.html:105
#: users/models.py:130 users/models.py:503
msgid "User"
msgstr "Uživatel"

#: common/models.py:1256
msgid "Price break quantity"
msgstr "Množství cenové slevy"

#: common/models.py:1263 company/serializers.py:567 order/models.py:1632
#: order/models.py:2734
msgid "Price"
msgstr "Cena"

#: common/models.py:1264
msgid "Unit price at specified quantity"
msgstr "Jednotková cena při stanoveném množství"

#: common/models.py:1315 common/models.py:1500
msgid "Endpoint"
msgstr "Koncový bod"

#: common/models.py:1316
msgid "Endpoint at which this webhook is received"
msgstr "Koncový bod, ve kterém je tento webhook přijímán"

#: common/models.py:1326
msgid "Name for this webhook"
msgstr "Název tohoto webhooku"

#: common/models.py:1330 common/models.py:2156 common/models.py:2263
#: company/models.py:164 company/models.py:799 machine/models.py:39
#: part/models.py:1246 plugin/models.py:68 stock/api.py:591 users/models.py:201
#: users/models.py:556 users/serializers.py:333
msgid "Active"
msgstr "Aktivní"

#: common/models.py:1330
msgid "Is this webhook active"
msgstr "Je tento webhook aktivní"

#: common/models.py:1346 users/models.py:178
msgid "Token"
msgstr "Token"

#: common/models.py:1347
msgid "Token for access"
msgstr "Token pro přístup"

#: common/models.py:1355
msgid "Secret"
msgstr "Tajný klíč"

#: common/models.py:1356
msgid "Shared secret for HMAC"
msgstr "Sdílený tajný klíč pro HMAC"

#: common/models.py:1464
msgid "Message ID"
msgstr "ID zprávy"

#: common/models.py:1465
msgid "Unique identifier for this message"
msgstr "Unikátní identifikátor pro tuto zprávu"

#: common/models.py:1473
msgid "Host"
msgstr "Hostitel"

#: common/models.py:1474
msgid "Host from which this message was received"
msgstr "Hostitel, od kterého byla tato zpráva přijata"

#: common/models.py:1482
msgid "Header"
msgstr "Záhlaví"

#: common/models.py:1483
msgid "Header of this message"
msgstr "Záhlaví této zprávy"

#: common/models.py:1490
msgid "Body"
msgstr "Tělo"

#: common/models.py:1491
msgid "Body of this message"
msgstr "Tělo zprávy"

#: common/models.py:1501
msgid "Endpoint on which this message was received"
msgstr "Koncový bod, na kterém byla zpráva přijata"

#: common/models.py:1506
msgid "Worked on"
msgstr "Pracoval na"

#: common/models.py:1507
msgid "Was the work on this message finished?"
msgstr "Byla práce na této zprávě dokončena?"

#: common/models.py:1633
msgid "Id"
msgstr "ID"

#: common/models.py:1635 part/serializers.py:281
msgid "Title"
msgstr "Název"

#: common/models.py:1637 common/models.py:1898 company/models.py:149
#: company/models.py:445 company/models.py:512 company/models.py:816
#: order/models.py:444 order/models.py:1585 order/models.py:2056
#: part/models.py:1113
#: report/templates/report/inventree_build_order_report.html:164
msgid "Link"
msgstr "Odkaz"

#: common/models.py:1639
msgid "Published"
msgstr "Zveřejněno"

#: common/models.py:1641
msgid "Author"
msgstr "Autor"

#: common/models.py:1643
msgid "Summary"
msgstr "Souhrn"

#: common/models.py:1646
msgid "Read"
msgstr "Přečteno"

#: common/models.py:1646
msgid "Was this news item read?"
msgstr "Byla tato novinka přečtena?"

#: common/models.py:1663 company/models.py:160 part/models.py:1124
#: report/templates/report/inventree_bill_of_materials_report.html:126
#: report/templates/report/inventree_bill_of_materials_report.html:148
#: report/templates/report/inventree_return_order_report.html:35
msgid "Image"
msgstr "Obrazek"

#: common/models.py:1663
msgid "Image file"
msgstr "Soubor obrázku"

#: common/models.py:1675
msgid "Target model type for this image"
msgstr "Cílový typ modelu pro tento obrázek"

#: common/models.py:1679
msgid "Target model ID for this image"
msgstr "Cílové ID modelu pro tento obrázek"

#: common/models.py:1701
msgid "Custom Unit"
msgstr "Vlastní jednotka"

#: common/models.py:1719
msgid "Unit symbol must be unique"
msgstr "Symbol jednotky musí být unikátní"

#: common/models.py:1734
msgid "Unit name must be a valid identifier"
msgstr "Název jednotky musí být platný identifikátor"

#: common/models.py:1753
msgid "Unit name"
msgstr "Název jednotky"

#: common/models.py:1760
msgid "Symbol"
msgstr "Symbol"

#: common/models.py:1761
msgid "Optional unit symbol"
msgstr "Volitelný symbol jednotky"

#: common/models.py:1767
msgid "Definition"
msgstr "Definice"

#: common/models.py:1768
msgid "Unit definition"
msgstr "Definice jednotky"

#: common/models.py:1826 common/models.py:1889 stock/models.py:2827
#: stock/serializers.py:258
msgid "Attachment"
msgstr "Příloha"

#: common/models.py:1843
msgid "Missing file"
msgstr "Chybějící soubor"

#: common/models.py:1844
msgid "Missing external link"
msgstr "Chybějící externí odkaz"

#: common/models.py:1881
msgid "Model type"
msgstr ""

#: common/models.py:1882
msgid "Target model type for image"
msgstr ""

#: common/models.py:1890
msgid "Select file to attach"
msgstr "Vyberte soubor k přiložení"

#: common/models.py:1906
msgid "Comment"
msgstr "Komentář"

#: common/models.py:1907
msgid "Attachment comment"
msgstr "Komentář přílohy"

#: common/models.py:1923
msgid "Upload date"
msgstr "Datum nahrání"

#: common/models.py:1924
msgid "Date the file was uploaded"
msgstr "Datum, kdy byl soubor nahrán"

#: common/models.py:1928
msgid "File size"
msgstr "Velikost souboru"

#: common/models.py:1928
msgid "File size in bytes"
msgstr "Velikost souboru v bytech"

#: common/models.py:1966 common/serializers.py:637
msgid "Invalid model type specified for attachment"
msgstr "Uveden neplatný typ modelu pro přílohu"

#: common/models.py:1987
msgid "Custom State"
msgstr "Vlastní stav"

#: common/models.py:1988
msgid "Custom States"
msgstr "Vlastní stavy"

#: common/models.py:1993
msgid "Reference Status Set"
msgstr "Nastavení referenčního stavu"

#: common/models.py:1994
msgid "Status set that is extended with this custom state"
msgstr ""

#: common/models.py:1998 generic/states/serializers.py:18
msgid "Logical Key"
msgstr "Logický klíč"

#: common/models.py:2000
msgid "State logical key that is equal to this custom state in business logic"
msgstr ""

#: common/models.py:2005 common/models.py:2244 company/models.py:600
#: report/templates/report/inventree_test_report.html:104 stock/models.py:2819
msgid "Value"
msgstr "Hodnota"

#: common/models.py:2006
msgid "Numerical value that will be saved in the models database"
msgstr ""

#: common/models.py:2012
msgid "Name of the state"
msgstr "Název stavu"

#: common/models.py:2021 common/models.py:2250 generic/states/serializers.py:22
#: part/serializers.py:283
msgid "Label"
msgstr "Popisek"

#: common/models.py:2022
msgid "Label that will be displayed in the frontend"
msgstr "Štítek, který bude zobrazen na webu"

#: common/models.py:2029 generic/states/serializers.py:24
msgid "Color"
msgstr "Barva"

#: common/models.py:2030
msgid "Color that will be displayed in the frontend"
msgstr "Barva, která bude zobrazena ve frontendu"

#: common/models.py:2038 part/serializers.py:285
msgid "Model"
msgstr "Model"

#: common/models.py:2039
msgid "Model this state is associated with"
msgstr "Model, ke kterému je tento stav přiřazen"

#: common/models.py:2054
msgid "Model must be selected"
msgstr "Musí být vybrán model"

#: common/models.py:2057
msgid "Key must be selected"
msgstr "Musí být vybrán klíč"

#: common/models.py:2060
msgid "Logical key must be selected"
msgstr "Musí být vybrán logický klíč"

#: common/models.py:2064
msgid "Key must be different from logical key"
msgstr "Klíč se musí lišit od logického klíče"

#: common/models.py:2071
msgid "Valid reference status class must be provided"
msgstr ""

#: common/models.py:2077
msgid "Key must be different from the logical keys of the reference status"
msgstr ""

#: common/models.py:2084
msgid "Logical key must be in the logical keys of the reference status"
msgstr ""

#: common/models.py:2091
msgid "Name must be different from the names of the reference status"
msgstr "Název se musí lišit od názvů referenčního statusu"

#: common/models.py:2131 common/models.py:2238 part/models.py:3876
msgid "Selection List"
msgstr "Výběrové pole"

#: common/models.py:2132
msgid "Selection Lists"
msgstr "Výběrová pole"

#: common/models.py:2137
msgid "Name of the selection list"
msgstr "Název výběrového pole"

#: common/models.py:2144
msgid "Description of the selection list"
msgstr "Popis výběrového pole"

#: common/models.py:2150 part/models.py:1251
msgid "Locked"
msgstr "Uzamčeno"

#: common/models.py:2151
msgid "Is this selection list locked?"
msgstr "Je tento seznam výběrů uzamčen?"

#: common/models.py:2157
msgid "Can this selection list be used?"
msgstr "Může být tento seznam výběru použit?"

#: common/models.py:2165
msgid "Source Plugin"
msgstr "Zdrojový plugin"

#: common/models.py:2166
msgid "Plugin which provides the selection list"
msgstr "Plugin, který poskytuje seznam výběru"

#: common/models.py:2171
msgid "Source String"
msgstr "Zdrojový řetězec"

#: common/models.py:2172
msgid "Optional string identifying the source used for this list"
msgstr "Volitelný řetězec identifikující zdroj použitý pro tento seznam"

#: common/models.py:2181
msgid "Default Entry"
msgstr "Výchozí položka"

#: common/models.py:2182
msgid "Default entry for this selection list"
msgstr "Výchozí položka pro tento seznam výběru"

#: common/models.py:2187
msgid "Created"
msgstr "Vytvořeno"

#: common/models.py:2188
msgid "Date and time that the selection list was created"
msgstr "Datum a čas vytvoření výběrového seznamu"

#: common/models.py:2193
msgid "Last Updated"
msgstr "Poslední aktualizace"

#: common/models.py:2194
msgid "Date and time that the selection list was last updated"
msgstr "Datum a čas poslední aktualizace výběrového seznamu"

#: common/models.py:2228
msgid "Selection List Entry"
msgstr "Položka seznamu výběrů"

#: common/models.py:2229
msgid "Selection List Entries"
msgstr "Položky seznamu výběrů"

#: common/models.py:2239
msgid "Selection list to which this entry belongs"
msgstr "Seznam výběru, do kterého tato položka patří"

#: common/models.py:2245
msgid "Value of the selection list entry"
msgstr ""

#: common/models.py:2251
msgid "Label for the selection list entry"
msgstr ""

#: common/models.py:2257
msgid "Description of the selection list entry"
msgstr ""

#: common/models.py:2264
msgid "Is this selection list entry active?"
msgstr ""

#: common/models.py:2282
msgid "Barcode Scan"
msgstr ""

#: common/models.py:2286 importer/models.py:523 part/models.py:4041
msgid "Data"
msgstr "Data"

#: common/models.py:2287
msgid "Barcode data"
msgstr "Data čárového kódu"

#: common/models.py:2298
msgid "User who scanned the barcode"
msgstr "Uživatel, který naskenoval čárový kód"

#: common/models.py:2303 importer/models.py:66
msgid "Timestamp"
msgstr ""

#: common/models.py:2304
msgid "Date and time of the barcode scan"
msgstr "Datum a čas skenování čárového kódu"

#: common/models.py:2310
msgid "URL endpoint which processed the barcode"
msgstr ""

#: common/models.py:2317 order/models.py:1622 plugin/serializers.py:93
msgid "Context"
msgstr "Kontext"

#: common/models.py:2318
msgid "Context data for the barcode scan"
msgstr ""

#: common/models.py:2325
msgid "Response"
msgstr ""

#: common/models.py:2326
msgid "Response data from the barcode scan"
msgstr ""

#: common/models.py:2332 report/templates/report/inventree_test_report.html:103
#: stock/models.py:2813
msgid "Result"
msgstr "Výsledek"

#: common/models.py:2333
msgid "Was the barcode scan successful?"
msgstr "Bylo skenování čárového kódu úspěšné?"

#: common/notifications.py:332
#, python-brace-format
msgid "New {verbose_name}"
msgstr "Nový {verbose_name}"

#: common/notifications.py:334
msgid "A new order has been created and assigned to you"
msgstr "Byla vytvořena nová objednávka a přiřazena k vám"

#: common/notifications.py:340
#, python-brace-format
msgid "{verbose_name} canceled"
msgstr "{verbose_name} zrušeno"

#: common/notifications.py:342
msgid "A order that is assigned to you was canceled"
msgstr "Objednávka, která je vám přidělena, byla zrušena"

#: common/notifications.py:348 common/notifications.py:355 order/api.py:559
msgid "Items Received"
msgstr "Přijaté položky"

#: common/notifications.py:350
msgid "Items have been received against a purchase order"
msgstr ""

#: common/notifications.py:357
msgid "Items have been received against a return order"
msgstr ""

#: common/notifications.py:505
msgid "Error raised by plugin"
msgstr ""

#: common/serializers.py:451
msgid "Is Running"
msgstr ""

#: common/serializers.py:457
msgid "Pending Tasks"
msgstr ""

#: common/serializers.py:463
msgid "Scheduled Tasks"
msgstr "Naplánované úlohy"

#: common/serializers.py:469
msgid "Failed Tasks"
msgstr "Neúspěšné úlohy"

#: common/serializers.py:484
msgid "Task ID"
msgstr "ID úlohy"

#: common/serializers.py:484
msgid "Unique task ID"
msgstr "Unikátní ID úlohy"

#: common/serializers.py:486
msgid "Lock"
msgstr ""

#: common/serializers.py:486
msgid "Lock time"
msgstr "Čas uzamčení"

#: common/serializers.py:488
msgid "Task name"
msgstr "Jméno úkolu"

#: common/serializers.py:490
msgid "Function"
msgstr "Funkce"

#: common/serializers.py:490
msgid "Function name"
msgstr "Název funkce"

#: common/serializers.py:492
msgid "Arguments"
msgstr "Argumenty"

#: common/serializers.py:492
msgid "Task arguments"
msgstr "Argumenty úlohy"

#: common/serializers.py:495
msgid "Keyword Arguments"
msgstr "Argumenty klíčových slov"

#: common/serializers.py:495
msgid "Task keyword arguments"
msgstr "Argumenty klíčových slov úlohy"

#: common/serializers.py:605
msgid "Filename"
msgstr "Název souboru"

#: common/serializers.py:612 importer/models.py:86 report/api.py:41
#: report/models.py:277 report/serializers.py:53
msgid "Model Type"
msgstr "Typ modelu"

#: common/serializers.py:640
msgid "User does not have permission to create or edit attachments for this model"
msgstr "Uživatel nemá oprávnění k vytváření nebo úpravám příloh pro tento model"

#: common/serializers.py:684 common/serializers.py:787
msgid "Selection list is locked"
msgstr ""

#: common/setting/system.py:97
msgid "No group"
msgstr "Žádná skupina"

#: common/setting/system.py:156
msgid "Site URL is locked by configuration"
msgstr "Adresa URL webu je uzamčena konfigurací"

#: common/setting/system.py:167
msgid "Restart required"
msgstr "Je vyžadován restart"

#: common/setting/system.py:168
msgid "A setting has been changed which requires a server restart"
msgstr "Bylo změněno nastavení, které vyžaduje restart serveru"

#: common/setting/system.py:174
msgid "Pending migrations"
msgstr "Nevyřízené migrace"

#: common/setting/system.py:175
msgid "Number of pending database migrations"
msgstr "Počet nevyřízených migrací databáze"

#: common/setting/system.py:180
msgid "Instance ID"
msgstr ""

#: common/setting/system.py:181
msgid "Unique identifier for this InvenTree instance"
msgstr ""

#: common/setting/system.py:186
msgid "Announce ID"
msgstr ""

#: common/setting/system.py:188
msgid "Announce the instance ID of the server in the server status info (unauthenticated)"
msgstr ""

#: common/setting/system.py:194
msgid "Server Instance Name"
msgstr "Název instance serveru"

#: common/setting/system.py:196
msgid "String descriptor for the server instance"
msgstr "Textový popisovač pro instanci serveru"

#: common/setting/system.py:200
msgid "Use instance name"
msgstr "Použít název instance"

#: common/setting/system.py:201
msgid "Use the instance name in the title-bar"
msgstr "Použít název instance v liště"

#: common/setting/system.py:206
msgid "Restrict showing `about`"
msgstr "Omezit zobrazování `o aplikaci`"

#: common/setting/system.py:207
msgid "Show the `about` modal only to superusers"
msgstr "Zobrazovat okno `o aplikaci` pouze superuživatelům"

#: common/setting/system.py:212 company/models.py:108 company/models.py:109
msgid "Company name"
msgstr "Jméno společnosti"

#: common/setting/system.py:213
msgid "Internal company name"
msgstr "Interní název společnosti"

#: common/setting/system.py:217
msgid "Base URL"
msgstr "Základní URL"

#: common/setting/system.py:218
msgid "Base URL for server instance"
msgstr "Základní URL pro instanci serveru"

#: common/setting/system.py:224
msgid "Default Currency"
msgstr "Výchozí měna"

#: common/setting/system.py:225
msgid "Select base currency for pricing calculations"
msgstr "Vyberte základní měnu pro cenové kalkulace"

#: common/setting/system.py:231
msgid "Supported Currencies"
msgstr "Podporované měny"

#: common/setting/system.py:232
msgid "List of supported currency codes"
msgstr "Seznam podporovaných kódů měn"

#: common/setting/system.py:238
msgid "Currency Update Interval"
msgstr "Interval aktualizace měny"

#: common/setting/system.py:239
msgid "How often to update exchange rates (set to zero to disable)"
msgstr "Jak často aktualizovat směnné kurzy (pro vypnutí nastavte na nulu)"

#: common/setting/system.py:241 common/setting/system.py:281
#: common/setting/system.py:294 common/setting/system.py:302
#: common/setting/system.py:309 common/setting/system.py:318
#: common/setting/system.py:567 common/setting/system.py:587
#: common/setting/system.py:684 common/setting/system.py:1068
msgid "days"
msgstr "dny"

#: common/setting/system.py:245
msgid "Currency Update Plugin"
msgstr "Plugin aktualizace měny"

#: common/setting/system.py:246
msgid "Currency update plugin to use"
msgstr "Plugin pro aktualizaci měn k použití"

#: common/setting/system.py:251
msgid "Download from URL"
msgstr "Stáhnout z URL"

#: common/setting/system.py:252
msgid "Allow download of remote images and files from external URL"
msgstr "Povolit stahování vzdálených obrázků a souborů z externích URL"

#: common/setting/system.py:257
msgid "Download Size Limit"
msgstr "Limit velikosti stahování"

#: common/setting/system.py:258
msgid "Maximum allowable download size for remote image"
msgstr "Maximální povolená velikost stahování vzdáleného obrázku"

#: common/setting/system.py:264
msgid "User-agent used to download from URL"
msgstr "User-agent použitý ke stažení z adresy URL"

#: common/setting/system.py:266
msgid "Allow to override the user-agent used to download images and files from external URL (leave blank for the default)"
msgstr "Povolit přepsání user-agenta používaného ke stahování obrázků a souborů z externí adresy URL (ponechte prázdné pro výchozí)"

#: common/setting/system.py:271
msgid "Strict URL Validation"
msgstr "Přísná validace URL"

#: common/setting/system.py:272
msgid "Require schema specification when validating URLs"
msgstr "Vyžadovat specifikaci schématu při ověřování adres URL"

#: common/setting/system.py:277
msgid "Update Check Interval"
msgstr "Interval kontroly aktualizací"

#: common/setting/system.py:278
msgid "How often to check for updates (set to zero to disable)"
msgstr "Jak často kontrolovat aktualizace (nastavte na nulu pro vypnutí)"

#: common/setting/system.py:284
msgid "Automatic Backup"
msgstr "Automatické Zálohování"

#: common/setting/system.py:285
msgid "Enable automatic backup of database and media files"
msgstr "Povolit automatické zálohování databáze a mediálních souborů"

#: common/setting/system.py:290
msgid "Auto Backup Interval"
msgstr "Interval automatického zálohování"

#: common/setting/system.py:291
msgid "Specify number of days between automated backup events"
msgstr "Zadejte počet dní mezi automatickými zálohovými událostmi"

#: common/setting/system.py:297
msgid "Task Deletion Interval"
msgstr "Interval mazání úloh"

#: common/setting/system.py:299
msgid "Background task results will be deleted after specified number of days"
msgstr "Výsledky úloh na pozadí budou odstraněny po zadaném počtu dní"

#: common/setting/system.py:306
msgid "Error Log Deletion Interval"
msgstr "Interval odstranění protokolu chyb"

#: common/setting/system.py:307
msgid "Error logs will be deleted after specified number of days"
msgstr "Záznamy chyb budou odstraněny po zadaném počtu dní"

#: common/setting/system.py:313
msgid "Notification Deletion Interval"
msgstr "Interval pro odstranění oznámení"

#: common/setting/system.py:315
msgid "User notifications will be deleted after specified number of days"
msgstr "Uživatelská oznámení budou smazána po zadaném počtu dní"

#: common/setting/system.py:322
msgid "Barcode Support"
msgstr "Podpora čárových kódů"

#: common/setting/system.py:323
msgid "Enable barcode scanner support in the web interface"
msgstr "Povolit podporu pro skenování čárových kódů ve webovém rozhraní"

#: common/setting/system.py:328
msgid "Store Barcode Results"
msgstr "Ukládat výsledky čárových kódů"

#: common/setting/system.py:329
msgid "Store barcode scan results in the database"
msgstr "Ukládat výsledky skenování čárových kódů v databázi"

#: common/setting/system.py:334
msgid "Barcode Scans Maximum Count"
msgstr "Maximální počet naskenovaných čárových kódů"

#: common/setting/system.py:335
msgid "Maximum number of barcode scan results to store"
msgstr "Maximální počet uložených výsledků skenování čárových kódů"

#: common/setting/system.py:340
msgid "Barcode Input Delay"
msgstr "Zpoždění vstupu čárového kódu"

#: common/setting/system.py:341
msgid "Barcode input processing delay time"
msgstr "Doba zpoždění zpracování vstupu čárového kódu"

#: common/setting/system.py:347
msgid "Barcode Webcam Support"
msgstr "Podpora webové kamery pro čárové kódy"

#: common/setting/system.py:348
msgid "Allow barcode scanning via webcam in browser"
msgstr "Povolit skenování čárových kódů přes webovou kameru v prohlížeči"

#: common/setting/system.py:353
msgid "Barcode Show Data"
msgstr "Zobrazovat data čárových kódů"

#: common/setting/system.py:354
msgid "Display barcode data in browser as text"
msgstr "Zobrazovat data čárových kódů v prohlížeči jako text"

#: common/setting/system.py:359
msgid "Barcode Generation Plugin"
msgstr "Plugin pro generování čárových kódů"

#: common/setting/system.py:360
msgid "Plugin to use for internal barcode data generation"
msgstr ""

#: common/setting/system.py:365
msgid "Part Revisions"
msgstr "Revize dílu"

#: common/setting/system.py:366
msgid "Enable revision field for Part"
msgstr "Povolit pole revize pro díl"

#: common/setting/system.py:371
msgid "Assembly Revision Only"
msgstr "Revize pouze pro sestavy"

#: common/setting/system.py:372
msgid "Only allow revisions for assembly parts"
msgstr "Povolit revize pouze pro sestavy"

#: common/setting/system.py:377
msgid "Allow Deletion from Assembly"
msgstr "Povolit odstranění ze sestavy"

#: common/setting/system.py:378
msgid "Allow deletion of parts which are used in an assembly"
msgstr "Povolit odstranění dílů, které jsou použity v sestavě"

#: common/setting/system.py:383
msgid "IPN Regex"
msgstr "IPN Regex"

#: common/setting/system.py:384
msgid "Regular expression pattern for matching Part IPN"
msgstr "Regulární vzorec výrazu pro odpovídající IPN dílu"

#: common/setting/system.py:387
msgid "Allow Duplicate IPN"
msgstr "Povolit duplicitní IPN"

#: common/setting/system.py:388
msgid "Allow multiple parts to share the same IPN"
msgstr "Povolit více dílům sdílet stejný IPN"

#: common/setting/system.py:393
msgid "Allow Editing IPN"
msgstr "Povolit editaci IPN"

#: common/setting/system.py:394
msgid "Allow changing the IPN value while editing a part"
msgstr "Povolit změnu IPN při úpravách dílu"

#: common/setting/system.py:399
msgid "Copy Part BOM Data"
msgstr "Kopírovat data BOM dílu"

#: common/setting/system.py:400
msgid "Copy BOM data by default when duplicating a part"
msgstr "Kopírovat data BOM ve výchozím nastavení při duplikování dílu"

#: common/setting/system.py:405
msgid "Copy Part Parameter Data"
msgstr "Kopírovat data parametrů dílu"

#: common/setting/system.py:406
msgid "Copy parameter data by default when duplicating a part"
msgstr "Kopírovat data parametrů ve výchozím nastavení při duplikování dílu"

#: common/setting/system.py:411
msgid "Copy Part Test Data"
msgstr "Kopírovat zkušební data dílu"

#: common/setting/system.py:412
msgid "Copy test data by default when duplicating a part"
msgstr "Kopírovat testovací data ve výchozím nastavení při duplikování dílu"

#: common/setting/system.py:417
msgid "Copy Category Parameter Templates"
msgstr "Kopírovat šablony parametrů kategorie"

#: common/setting/system.py:418
msgid "Copy category parameter templates when creating a part"
msgstr "Kopírování šablon parametrů kategorie při vytváření dílu"

#: common/setting/system.py:423 part/models.py:4035 report/models.py:357
#: report/models.py:563 report/serializers.py:90 report/serializers.py:131
#: stock/serializers.py:247
msgid "Template"
msgstr "Šablona"

#: common/setting/system.py:424
msgid "Parts are templates by default"
msgstr "Díly jsou ve výchozím nastavení šablony"

#: common/setting/system.py:430
msgid "Parts can be assembled from other components by default"
msgstr "Díly lze ve výchozím nastavení sestavit z jiných komponentů"

#: common/setting/system.py:435 part/models.py:1217 part/serializers.py:1677
#: part/serializers.py:1683
msgid "Component"
msgstr "Komponent"

#: common/setting/system.py:436
msgid "Parts can be used as sub-components by default"
msgstr "Díly lze ve výchozím nastavení použít jako dílčí komponenty"

#: common/setting/system.py:441 part/models.py:1235
msgid "Purchaseable"
msgstr "Možné zakoupit"

#: common/setting/system.py:442
msgid "Parts are purchaseable by default"
msgstr "Díly jsou zakoupitelné ve výchozím nastavení"

#: common/setting/system.py:447 part/models.py:1241 stock/api.py:592
msgid "Salable"
msgstr "Prodejné"

#: common/setting/system.py:448
msgid "Parts are salable by default"
msgstr "Díly jsou prodejné ve výchozím nastavení"

#: common/setting/system.py:454
msgid "Parts are trackable by default"
msgstr "Díly jsou sledovatelné ve výchozím nastavení"

#: common/setting/system.py:459 part/models.py:1257
msgid "Virtual"
msgstr "Nehmotné (virtuální)"

#: common/setting/system.py:460
msgid "Parts are virtual by default"
msgstr "Díly jsou nehmotné (virtuální) ve výchozím nastavení"

#: common/setting/system.py:465
msgid "Show Import in Views"
msgstr "Zobrazit Import v zobrazeních"

#: common/setting/system.py:466
msgid "Display the import wizard in some part views"
msgstr "Zobrazit průvodce importem v některých zobrazeních dílu"

#: common/setting/system.py:471
msgid "Show related parts"
msgstr "Zobrazit související díly"

#: common/setting/system.py:472
msgid "Display related parts for a part"
msgstr "Zobrazit související díly pro díl"

#: common/setting/system.py:477
msgid "Initial Stock Data"
msgstr "Počáteční údaje zásob"

#: common/setting/system.py:478
msgid "Allow creation of initial stock when adding a new part"
msgstr "Povolit vytvoření počátečního skladu při přidání nové části"

#: common/setting/system.py:483
msgid "Initial Supplier Data"
msgstr "Počáteční údaje dodavatele"

#: common/setting/system.py:485
msgid "Allow creation of initial supplier data when adding a new part"
msgstr "Povolit vytvoření počátečních dat dodavatele při přidávání nového dílu"

#: common/setting/system.py:491
msgid "Part Name Display Format"
msgstr "Formát zobrazení jména dílu"

#: common/setting/system.py:492
msgid "Format to display the part name"
msgstr "Formát pro zobrazení názvu dílu"

#: common/setting/system.py:498
msgid "Part Category Default Icon"
msgstr "Výchozí ikona kategorie dílu"

#: common/setting/system.py:499
msgid "Part category default icon (empty means no icon)"
msgstr "Výchozí ikona kategorie dílu (prázdné znamená bez ikony)"

#: common/setting/system.py:504
msgid "Enforce Parameter Units"
msgstr "Vynutit jednotky parametru"

#: common/setting/system.py:506
msgid "If units are provided, parameter values must match the specified units"
msgstr "Pokud jsou uvedeny jednotky, musí hodnoty parametrů odpovídat zadaným jednotkám"

#: common/setting/system.py:512
msgid "Minimum Pricing Decimal Places"
msgstr "Minimální počet desetinných míst u cen"

#: common/setting/system.py:514
msgid "Minimum number of decimal places to display when rendering pricing data"
msgstr "Minimální počet desetinných míst k zobrazení u cenových údajů"

#: common/setting/system.py:525
msgid "Maximum Pricing Decimal Places"
msgstr "Maximální počet desetinných míst u cen"

#: common/setting/system.py:527
msgid "Maximum number of decimal places to display when rendering pricing data"
msgstr "Maximální počet desetinných míst k zobrazení u cenových údajů"

#: common/setting/system.py:538
msgid "Use Supplier Pricing"
msgstr "Použít ceny dodavatele"

#: common/setting/system.py:540
msgid "Include supplier price breaks in overall pricing calculations"
msgstr "Zahrnout cenová zvýhodnění dodavatelů do celkových cenových kalkulací"

#: common/setting/system.py:546
msgid "Purchase History Override"
msgstr "Přepsání historie nákupu"

#: common/setting/system.py:548
msgid "Historical purchase order pricing overrides supplier price breaks"
msgstr "Historické ceny nákupních objednávek mají přednost před cenovými zvýhodněními dodavatele"

#: common/setting/system.py:554
msgid "Use Stock Item Pricing"
msgstr "Použít ceny skladových položek"

#: common/setting/system.py:556
msgid "Use pricing from manually entered stock data for pricing calculations"
msgstr "Použít ceny z ručně zadaných skladových údajů pro cenové kalkulace"

#: common/setting/system.py:562
msgid "Stock Item Pricing Age"
msgstr "Stáří cen skladových položek"

#: common/setting/system.py:564
msgid "Exclude stock items older than this number of days from pricing calculations"
msgstr "Vyloučit skladové položky starší než tento počet dní z cenových kalkulací"

#: common/setting/system.py:571
msgid "Use Variant Pricing"
msgstr "Použít cenu varianty"

#: common/setting/system.py:572
msgid "Include variant pricing in overall pricing calculations"
msgstr "Zahrnutí cen variant do celkových cenových kalkulací"

#: common/setting/system.py:577
msgid "Active Variants Only"
msgstr "Pouze aktivní varianty"

#: common/setting/system.py:579
msgid "Only use active variant parts for calculating variant pricing"
msgstr "Pro výpočet ceny varianty použijte pouze aktivní díly varianty"

#: common/setting/system.py:585
msgid "Pricing Rebuild Interval"
msgstr "Interval přestavby cen"

#: common/setting/system.py:586
msgid "Number of days before part pricing is automatically updated"
msgstr "Počet dní před automatickou aktualizací cen dílů"

#: common/setting/system.py:592
msgid "Internal Prices"
msgstr "Interní ceny"

#: common/setting/system.py:593
msgid "Enable internal prices for parts"
msgstr "Povolit interní ceny pro díly"

#: common/setting/system.py:598
msgid "Internal Price Override"
msgstr "Přepis interní ceny"

#: common/setting/system.py:600
msgid "If available, internal prices override price range calculations"
msgstr "Pokud jsou k dispozici, interní ceny mají přednost před výpočty cenového rozpětí"

#: common/setting/system.py:606
msgid "Enable label printing"
msgstr "Povolit tisk štítků"

#: common/setting/system.py:607
msgid "Enable label printing from the web interface"
msgstr "Povolit tisk štítků z webového rozhraní"

#: common/setting/system.py:612
msgid "Label Image DPI"
msgstr "DPI rozlišení štítků"

#: common/setting/system.py:614
msgid "DPI resolution when generating image files to supply to label printing plugins"
msgstr "Rozlišení DPI při generování obrazových souborů, které se dodávají do zásuvných modulů pro tisk štítků"

#: common/setting/system.py:620
msgid "Enable Reports"
msgstr "Povolit reporty"

#: common/setting/system.py:621
msgid "Enable generation of reports"
msgstr "Povolit generování reportů"

#: common/setting/system.py:626
msgid "Debug Mode"
msgstr "Režim ladění chyb"

#: common/setting/system.py:627
msgid "Generate reports in debug mode (HTML output)"
msgstr "Generovat reporty v režimu ladění (HTML výstup)"

#: common/setting/system.py:632
msgid "Log Report Errors"
msgstr "Zaznamenávat chyby reportů"

#: common/setting/system.py:633
msgid "Log errors which occur when generating reports"
msgstr "Zaznamenávat chyby, které se vyskytnou při vytváření reportů"

#: common/setting/system.py:638 plugin/builtin/labels/label_sheet.py:29
#: report/models.py:365
msgid "Page Size"
msgstr "Velikost stránky"

#: common/setting/system.py:639
msgid "Default page size for PDF reports"
msgstr "Výchozí velikost stránky pro PDF reporty"

#: common/setting/system.py:644
msgid "Globally Unique Serials"
msgstr "Globálně unikátní sériová čísla"

#: common/setting/system.py:645
msgid "Serial numbers for stock items must be globally unique"
msgstr "Sériová čísla pro skladové položky musí být globálně unikátní"

#: common/setting/system.py:650
msgid "Autofill Serial Numbers"
msgstr "Automaticky vyplnit sériová čísla"

#: common/setting/system.py:651
msgid "Autofill serial numbers in forms"
msgstr "Automaticky vyplnit sériová čísla ve formulářích"

#: common/setting/system.py:656
msgid "Delete Depleted Stock"
msgstr "Odstranit vyčerpané zásoby"

#: common/setting/system.py:657
msgid "Determines default behavior when a stock item is depleted"
msgstr "Určuje výchozí chování při vyčerpání zásoby položky"

#: common/setting/system.py:662
msgid "Batch Code Template"
msgstr "Šablona kódu dávky"

#: common/setting/system.py:663
msgid "Template for generating default batch codes for stock items"
msgstr "Šablona pro generování výchozích kódů dávky pro skladové položky"

#: common/setting/system.py:667
msgid "Stock Expiry"
msgstr "Expirace zásob"

#: common/setting/system.py:668
msgid "Enable stock expiry functionality"
msgstr "Povolit funkci expirace zásob"

#: common/setting/system.py:673
msgid "Sell Expired Stock"
msgstr "Prodat prošlé zásoby"

#: common/setting/system.py:674
msgid "Allow sale of expired stock"
msgstr "Povolit prodej prošlých zásob"

#: common/setting/system.py:679
msgid "Stock Stale Time"
msgstr "Čas stáří zásob"

#: common/setting/system.py:681
msgid "Number of days stock items are considered stale before expiring"
msgstr "Počet dnů, po které jsou skladové položky považovány za nevyužité před uplynutím doby expirace"

#: common/setting/system.py:688
msgid "Build Expired Stock"
msgstr "Sestavit prošlé zásoby"

#: common/setting/system.py:689
msgid "Allow building with expired stock"
msgstr "Povolit sestavování s prošlými zásobami"

#: common/setting/system.py:694
msgid "Stock Ownership Control"
msgstr "Kontrola vlastnictví zásob"

#: common/setting/system.py:695
msgid "Enable ownership control over stock locations and items"
msgstr "Umožnit kontrolu vlastnictví nad skladovými místy a položkami"

#: common/setting/system.py:700
msgid "Stock Location Default Icon"
msgstr "Výchozí ikona umístění zásob"

#: common/setting/system.py:701
msgid "Stock location default icon (empty means no icon)"
msgstr "Výchozí ikona umístění zásob (prázdné znamená bez ikony)"

#: common/setting/system.py:706
msgid "Show Installed Stock Items"
msgstr "Zobrazit nainstalované skladové položky"

#: common/setting/system.py:707
msgid "Display installed stock items in stock tables"
msgstr "Zobrazit nainstalované skladové položky ve skladových tabulkách"

#: common/setting/system.py:712
msgid "Check BOM when installing items"
msgstr "Zkontrolovat BOM při instalaci položek"

#: common/setting/system.py:714
msgid "Installed stock items must exist in the BOM for the parent part"
msgstr "Nainstalované skladové položky musí existovat v BOM pro nadřazený díl"

#: common/setting/system.py:720
msgid "Allow Out of Stock Transfer"
msgstr "Povolit převod mimo sklad"

#: common/setting/system.py:722
msgid "Allow stock items which are not in stock to be transferred between stock locations"
msgstr "Umožnit přesun skladových položek, které nejsou na skladě, mezi skladovými místy"

#: common/setting/system.py:728
msgid "Build Order Reference Pattern"
msgstr "Referenční vzor objednávky sestavy"

#: common/setting/system.py:729
msgid "Required pattern for generating Build Order reference field"
msgstr "Požadovaný vzor pro generování referenčního pole Objednávka sestavy"

#: common/setting/system.py:734 common/setting/system.py:788
#: common/setting/system.py:808 common/setting/system.py:844
msgid "Require Responsible Owner"
msgstr "Vyžadovat odpovědného vlastníka"

#: common/setting/system.py:735 common/setting/system.py:789
#: common/setting/system.py:809 common/setting/system.py:845
msgid "A responsible owner must be assigned to each order"
msgstr "Ke každé objednávce musí být přiřazen odpovědný vlastník"

#: common/setting/system.py:740
msgid "Require Active Part"
msgstr "Vyžadovat aktivní díl"

#: common/setting/system.py:741
msgid "Prevent build order creation for inactive parts"
msgstr "Zabránit vytváření výrobních příkazů pro neaktivní díly"

#: common/setting/system.py:746
msgid "Require Locked Part"
msgstr ""

#: common/setting/system.py:747
msgid "Prevent build order creation for unlocked parts"
msgstr ""

#: common/setting/system.py:752
msgid "Require Valid BOM"
msgstr "Vyžadovat schválený kusovník"

#: common/setting/system.py:753
msgid "Prevent build order creation unless BOM has been validated"
msgstr "Zabránit vytváření výrobních příkazů, dokud není schválen kusovník"

#: common/setting/system.py:758
msgid "Require Closed Child Orders"
msgstr ""

#: common/setting/system.py:760
msgid "Prevent build order completion until all child orders are closed"
msgstr ""

#: common/setting/system.py:766
msgid "Block Until Tests Pass"
msgstr "Blokovat, dokud testy neprojdou"

#: common/setting/system.py:768
msgid "Prevent build outputs from being completed until all required tests pass"
msgstr "Zabránit dokončení výstupů sestavy, dokud neprojdou všechny požadované testy"

#: common/setting/system.py:774
msgid "Enable Return Orders"
msgstr "Povolit vracení objednávek"

#: common/setting/system.py:775
msgid "Enable return order functionality in the user interface"
msgstr "Povolit funkci vrácení objednávky v uživatelském rozhraní"

#: common/setting/system.py:780
msgid "Return Order Reference Pattern"
msgstr "Referenční vzor návratové objednávky"

#: common/setting/system.py:782
msgid "Required pattern for generating Return Order reference field"
msgstr "Požadovaný vzor pro vygenerování referenčního pole Návratová objednávka"

#: common/setting/system.py:794
msgid "Edit Completed Return Orders"
msgstr "Úprava dokončených návratových objednávek"

#: common/setting/system.py:796
msgid "Allow editing of return orders after they have been completed"
msgstr "Umožnit úpravu návratových objednávek po jejich dokončení"

#: common/setting/system.py:802
msgid "Sales Order Reference Pattern"
msgstr "Referenční vzor prodejní objednávky"

#: common/setting/system.py:803
msgid "Required pattern for generating Sales Order reference field"
msgstr "Požadovaný vzor pro generování referenčního pole prodejní objednávky"

#: common/setting/system.py:814
msgid "Sales Order Default Shipment"
msgstr "Výchozí přeprava prodejní objednávky"

#: common/setting/system.py:815
msgid "Enable creation of default shipment with sales orders"
msgstr "Povolit vytvoření výchozí přepravy s prodejními objednávkami"

#: common/setting/system.py:820
msgid "Edit Completed Sales Orders"
msgstr "Úprava dokončených prodejních objednávek"

#: common/setting/system.py:822
msgid "Allow editing of sales orders after they have been shipped or completed"
msgstr "Umožnit úpravy prodejních objednávek po jejich odeslání nebo dokončení"

#: common/setting/system.py:828
msgid "Mark Shipped Orders as Complete"
msgstr "Označit odeslané objednávky jako dokončené"

#: common/setting/system.py:830
msgid "Sales orders marked as shipped will automatically be completed, bypassing the \"shipped\" status"
msgstr "Prodejní objednávky označené jako odeslané se automaticky dokončí a obejdou stav „odesláno“"

#: common/setting/system.py:836
msgid "Purchase Order Reference Pattern"
msgstr "Referenční vzor nákupní objednávky"

#: common/setting/system.py:838
msgid "Required pattern for generating Purchase Order reference field"
msgstr "Požadovaný vzor pro generování referenčního pole nákupní objednávky"

#: common/setting/system.py:850
msgid "Edit Completed Purchase Orders"
msgstr "Úprava dokončených nákupních objednávek"

#: common/setting/system.py:852
msgid "Allow editing of purchase orders after they have been shipped or completed"
msgstr "Umožnit úpravy nákupních objednávek po jejich odeslání nebo dokončení"

#: common/setting/system.py:858
msgid "Convert Currency"
msgstr ""

#: common/setting/system.py:859
msgid "Convert item value to base currency when receiving stock"
msgstr ""

#: common/setting/system.py:864
msgid "Auto Complete Purchase Orders"
msgstr "Automatické dokončování nákupních objednávek"

#: common/setting/system.py:866
msgid "Automatically mark purchase orders as complete when all line items are received"
msgstr "Automaticky označit nákupní objednávky jako kompletní, jakmile jsou přijaty všechny řádkové položky"

#: common/setting/system.py:873
msgid "Enable password forgot"
msgstr "Povolit pole zapomenutého hesla"

#: common/setting/system.py:874
msgid "Enable password forgot function on the login pages"
msgstr "Povolení funkce zapomenutého hesla na přihlašovacích stránkách"

#: common/setting/system.py:879
msgid "Enable registration"
msgstr "Povolit registrace"

#: common/setting/system.py:880
msgid "Enable self-registration for users on the login pages"
msgstr "Povolit samoregistraci uživatelů na přihlašovacích stránkách"

#: common/setting/system.py:885
msgid "Enable SSO"
msgstr "Povolit SSO"

#: common/setting/system.py:886
msgid "Enable SSO on the login pages"
msgstr "Povolit SSO na přihlašovacích stránkách"

#: common/setting/system.py:891
msgid "Enable SSO registration"
msgstr "Povolit SSO registraci"

#: common/setting/system.py:893
msgid "Enable self-registration via SSO for users on the login pages"
msgstr "Povolit samoregistraci uživatelů prostřednictvím SSO na přihlašovacích stránkách"

#: common/setting/system.py:899
msgid "Enable SSO group sync"
msgstr ""

#: common/setting/system.py:901
msgid "Enable synchronizing InvenTree groups with groups provided by the IdP"
msgstr ""

#: common/setting/system.py:907
msgid "SSO group key"
msgstr ""

#: common/setting/system.py:908
msgid "The name of the groups claim attribute provided by the IdP"
msgstr ""

#: common/setting/system.py:913
msgid "SSO group map"
msgstr ""

#: common/setting/system.py:915
msgid "A mapping from SSO groups to local InvenTree groups. If the local group does not exist, it will be created."
msgstr ""

#: common/setting/system.py:921
msgid "Remove groups outside of SSO"
msgstr ""

#: common/setting/system.py:923
msgid "Whether groups assigned to the user should be removed if they are not backend by the IdP. Disabling this setting might cause security issues"
msgstr ""

#: common/setting/system.py:929
msgid "Email required"
msgstr "Vyžadován e-mail"

#: common/setting/system.py:930
msgid "Require user to supply mail on signup"
msgstr "Požadovat, aby uživatel při registraci zadal e-mail"

#: common/setting/system.py:935
msgid "Auto-fill SSO users"
msgstr "Automaticky vyplnit SSO uživatele"

#: common/setting/system.py:936
msgid "Automatically fill out user-details from SSO account-data"
msgstr "Automaticky vyplnit údaje o uživateli z údajů o účtu SSO"

#: common/setting/system.py:941
msgid "Mail twice"
msgstr "Mail dvakrát"

#: common/setting/system.py:942
msgid "On signup ask users twice for their mail"
msgstr "Při registraci dvakrát požádat uživatele o zadání e-mailu"

#: common/setting/system.py:947
msgid "Password twice"
msgstr "Heslo dvakrát"

#: common/setting/system.py:948
msgid "On signup ask users twice for their password"
msgstr "Při registraci dvakrát požádat uživatele o heslo"

#: common/setting/system.py:953
msgid "Allowed domains"
msgstr "Povolené domény"

#: common/setting/system.py:955
msgid "Restrict signup to certain domains (comma-separated, starting with @)"
msgstr "Omezit registraci na určité domény (oddělené čárkou a začínající @)"

#: common/setting/system.py:961
msgid "Group on signup"
msgstr "Skupina při registraci"

#: common/setting/system.py:963
msgid "Group to which new users are assigned on registration. If SSO group sync is enabled, this group is only set if no group can be assigned from the IdP."
msgstr ""

#: common/setting/system.py:969
msgid "Enforce MFA"
msgstr "Vynutit MFA"

#: common/setting/system.py:970
msgid "Users must use multifactor security."
msgstr "Uživatelé musí používat vícefaktorové zabezpečení."

#: common/setting/system.py:975
msgid "Check plugins on startup"
msgstr "Zkontrolovat pluginy při spuštění"

#: common/setting/system.py:977
msgid "Check that all plugins are installed on startup - enable in container environments"
msgstr "Zkontrolujte, zda jsou při spuštění nainstalovány všechny pluginy - povolit v kontejnerových prostředích"

#: common/setting/system.py:984
msgid "Check for plugin updates"
msgstr "Zkontrolovat aktualizace pluginů"

#: common/setting/system.py:985
msgid "Enable periodic checks for updates to installed plugins"
msgstr "Povolit pravidelné kontroly aktualizací nainstalovaných pluginů"

#: common/setting/system.py:991
msgid "Enable URL integration"
msgstr "Povolit integraci URL"

#: common/setting/system.py:992
msgid "Enable plugins to add URL routes"
msgstr "Povolit plug-inům přidávat trasy URL"

#: common/setting/system.py:998
msgid "Enable navigation integration"
msgstr "Povolit integraci navigace"

#: common/setting/system.py:999
msgid "Enable plugins to integrate into navigation"
msgstr "Povolit integrování pluginů do navigace"

#: common/setting/system.py:1005
msgid "Enable app integration"
msgstr "Povolit integraci aplikací"

#: common/setting/system.py:1006
msgid "Enable plugins to add apps"
msgstr "Povolit pluginům přidávát aplikace"

#: common/setting/system.py:1012
msgid "Enable schedule integration"
msgstr "Povolit integraci plánu"

#: common/setting/system.py:1013
msgid "Enable plugins to run scheduled tasks"
msgstr "Povolit pluginům spouštění naplánovaných úloh"

#: common/setting/system.py:1019
msgid "Enable event integration"
msgstr "Povolit integraci událostí"

#: common/setting/system.py:1020
msgid "Enable plugins to respond to internal events"
msgstr "Povolit pluginům reagovat na interní události"

#: common/setting/system.py:1026
msgid "Enable interface integration"
msgstr ""

#: common/setting/system.py:1027
msgid "Enable plugins to integrate into the user interface"
msgstr ""

#: common/setting/system.py:1033
msgid "Enable project codes"
msgstr ""

#: common/setting/system.py:1034
msgid "Enable project codes for tracking projects"
msgstr ""

#: common/setting/system.py:1039
msgid "Stocktake Functionality"
msgstr "Funkce inventury"

#: common/setting/system.py:1041
msgid "Enable stocktake functionality for recording stock levels and calculating stock value"
msgstr "Povolit funkci inventury pro evidenci stavu zásob a výpočet hodnoty zásob"

#: common/setting/system.py:1047
msgid "Exclude External Locations"
msgstr "Vyloučit externí umístění"

#: common/setting/system.py:1049
msgid "Exclude stock items in external locations from stocktake calculations"
msgstr "Vyloučit skladové položky na externích místech z výpočtů inventury"

#: common/setting/system.py:1055
msgid "Automatic Stocktake Period"
msgstr "Perioda automatické inventury"

#: common/setting/system.py:1057
msgid "Number of days between automatic stocktake recording (set to zero to disable)"
msgstr "Počet dní mezi automatickým záznamem inventury (pro vypnutí nastavte nulu)"

#: common/setting/system.py:1063
msgid "Report Deletion Interval"
msgstr "Interval mazání reportů"

#: common/setting/system.py:1065
msgid "Stocktake reports will be deleted after specified number of days"
msgstr "Reporty o inventuře se po určitém počtu dní vymažou"

#: common/setting/system.py:1072
msgid "Display Users full names"
msgstr "Zobrazit celá jména uživatelů"

#: common/setting/system.py:1073
msgid "Display Users full names instead of usernames"
msgstr "Zobrazit plná jména uživatelů namísto uživatelských jmen"

#: common/setting/system.py:1078
msgid "Display User Profiles"
msgstr ""

#: common/setting/system.py:1079
msgid "Display Users Profiles on their profile page"
msgstr ""

#: common/setting/system.py:1084
msgid "Enable Test Station Data"
msgstr "Povolit data zkušební stanice"

#: common/setting/system.py:1085
msgid "Enable test station data collection for test results"
msgstr "Povolit sběr dat ze zkušební stanice pro výsledky testů"

#: common/setting/system.py:1090
msgid "Create Template on Upload"
msgstr ""

#: common/setting/system.py:1092
msgid "Create a new test template when uploading test data which does not match an existing template"
msgstr ""

#: common/setting/user.py:23
msgid "Inline label display"
msgstr "Zobrazení štítků na řádku"

#: common/setting/user.py:25
msgid "Display PDF labels in the browser, instead of downloading as a file"
msgstr "Zobrazit štítky PDF v prohlížeči namísto stahování jako soubor"

#: common/setting/user.py:31
msgid "Default label printer"
msgstr "Výchozí tiskárna štítků"

#: common/setting/user.py:32
msgid "Configure which label printer should be selected by default"
msgstr "Konfigurovat tiskárnu štítků, která má být vybrána jako výchozí"

#: common/setting/user.py:37
msgid "Inline report display"
msgstr "Zobrazení reportů na řádku"

#: common/setting/user.py:39
msgid "Display PDF reports in the browser, instead of downloading as a file"
msgstr "Zobrazit reporty PDF v prohlížeči namísto stahování jako soubor"

#: common/setting/user.py:45
msgid "Search Parts"
msgstr "Hledat díly"

#: common/setting/user.py:46
msgid "Display parts in search preview window"
msgstr "Zobrazit díly v náhledu hledání"

#: common/setting/user.py:51
msgid "Search Supplier Parts"
msgstr "Hledat díly dodavatele"

#: common/setting/user.py:52
msgid "Display supplier parts in search preview window"
msgstr "Zobrazit díly dodavatele v náhledu hledání"

#: common/setting/user.py:57
msgid "Search Manufacturer Parts"
msgstr "Vyhledávání dílů výrobce"

#: common/setting/user.py:58
msgid "Display manufacturer parts in search preview window"
msgstr "Zobrazit díly výrobce v náhledu hledání"

#: common/setting/user.py:63
msgid "Hide Inactive Parts"
msgstr "Skrýt neaktivní díly"

#: common/setting/user.py:64
msgid "Excluded inactive parts from search preview window"
msgstr "Vyloučené neaktivní části z okna náhledu vyhledávání"

#: common/setting/user.py:69
msgid "Search Categories"
msgstr "Hledat kategorie"

#: common/setting/user.py:70
msgid "Display part categories in search preview window"
msgstr "Zobrazit kategorie dílů v náhledu hledání"

#: common/setting/user.py:75
msgid "Search Stock"
msgstr "Hledat zásoby"

#: common/setting/user.py:76
msgid "Display stock items in search preview window"
msgstr "Zobrazit skladové položky v náhledu hledání"

#: common/setting/user.py:81
msgid "Hide Unavailable Stock Items"
msgstr "Skrýt nedostupné skladové položky"

#: common/setting/user.py:83
msgid "Exclude stock items which are not available from the search preview window"
msgstr "Vyloučit skladové položky, které nejsou dostupné z okna náhledu hledání"

#: common/setting/user.py:89
msgid "Search Locations"
msgstr "Hledat umístění"

#: common/setting/user.py:90
msgid "Display stock locations in search preview window"
msgstr "Zobrazit skladová umístění v náhledu hledání"

#: common/setting/user.py:95
msgid "Search Companies"
msgstr "Hledat společnosti"

#: common/setting/user.py:96
msgid "Display companies in search preview window"
msgstr "Zobrazit společnosti v náhledu hledání"

#: common/setting/user.py:101
msgid "Search Build Orders"
msgstr "Hledat výrobní příkazy"

#: common/setting/user.py:102
msgid "Display build orders in search preview window"
msgstr "Zobrazit výrobní příkazy v náhledu hledání"

#: common/setting/user.py:107
msgid "Search Purchase Orders"
msgstr "Hledat nákupní objednávky"

#: common/setting/user.py:108
msgid "Display purchase orders in search preview window"
msgstr "Zobrazit nákupní objednávky v náhledu hledání"

#: common/setting/user.py:113
msgid "Exclude Inactive Purchase Orders"
msgstr "Vyloučit neaktivní nákupní objednávky"

#: common/setting/user.py:114
msgid "Exclude inactive purchase orders from search preview window"
msgstr "Vyloučit neaktivní nákupní objednávky z okna náhledu vyhledávání"

#: common/setting/user.py:119
msgid "Search Sales Orders"
msgstr "Hledat prodejní objednávky"

#: common/setting/user.py:120
msgid "Display sales orders in search preview window"
msgstr "Zobrazit prodejní objednávky v náhledu hledání"

#: common/setting/user.py:125
msgid "Exclude Inactive Sales Orders"
msgstr "Vyloučit neaktivní prodejní objednávky"

#: common/setting/user.py:126
msgid "Exclude inactive sales orders from search preview window"
msgstr "Vyloučit neaktivní prodejní objednávky z okna náhledu vyhledávání"

#: common/setting/user.py:131
msgid "Search Sales Order Shipments"
msgstr ""

#: common/setting/user.py:132
msgid "Display sales order shipments in search preview window"
msgstr ""

#: common/setting/user.py:137
msgid "Search Return Orders"
msgstr "Vyhledávání vrácených objednávek"

#: common/setting/user.py:138
msgid "Display return orders in search preview window"
msgstr "Zobrazit vrácené objednávky v okně náhledu vyhledávání"

#: common/setting/user.py:143
msgid "Exclude Inactive Return Orders"
msgstr "Vyloučit neaktivní vrácené objednávky"

#: common/setting/user.py:144
msgid "Exclude inactive return orders from search preview window"
msgstr "Vyloučit neaktivní vrácené objednávky z okna náhledu vyhledávání"

#: common/setting/user.py:149
msgid "Search Preview Results"
msgstr "Náhled výsledků vyhledávání"

#: common/setting/user.py:151
msgid "Number of results to show in each section of the search preview window"
msgstr "Počet výsledků, které se mají zobrazit v každé části okna náhledu vyhledávání"

#: common/setting/user.py:157
msgid "Regex Search"
msgstr "Regex hledání"

#: common/setting/user.py:158
msgid "Enable regular expressions in search queries"
msgstr "Povolit regulární výrazy ve vyhledávacích dotazech"

#: common/setting/user.py:163
msgid "Whole Word Search"
msgstr "Vyhledávání celého slova"

#: common/setting/user.py:164
msgid "Search queries return results for whole word matches"
msgstr "Vyhledávací dotazy vracejí výsledky pro shody celých slov"

#: common/setting/user.py:169
msgid "Search Notes"
msgstr ""

#: common/setting/user.py:171
msgid "Search queries return results for matches from the item's notes"
msgstr ""

#: common/setting/user.py:177
msgid "Show Quantity in Forms"
msgstr "Zobrazit množství ve formulářích"

#: common/setting/user.py:178
msgid "Display available part quantity in some forms"
msgstr "Zobrazit dostupné množství dílů v některých formulářích"

#: common/setting/user.py:183
msgid "Escape Key Closes Forms"
msgstr "Klávesa Escape zavře formuláře"

#: common/setting/user.py:184
msgid "Use the escape key to close modal forms"
msgstr "Zavřít modální formuláře pomocí klávesy escape"

#: common/setting/user.py:189
msgid "Fixed Navbar"
msgstr "Pevná navigační lišta"

#: common/setting/user.py:190
msgid "The navbar position is fixed to the top of the screen"
msgstr "Pozice navigační lišty je pevně nastavena na horní okraj obrazovky"

#: common/setting/user.py:195
msgid "Navigation Icons"
msgstr ""

#: common/setting/user.py:196
msgid "Display icons in the navigation bar"
msgstr ""

#: common/setting/user.py:201
msgid "Date Format"
msgstr "Formát data"

#: common/setting/user.py:202
msgid "Preferred format for displaying dates"
msgstr "Preferovaný formát pro zobrazení datumů"

#: common/setting/user.py:215
msgid "Part Scheduling"
msgstr "Plánování dílů"

#: common/setting/user.py:216
msgid "Display part scheduling information"
msgstr "Zobrazit informace o plánování dílů"

#: common/setting/user.py:221
msgid "Part Stocktake"
msgstr "Inventura dílu"

#: common/setting/user.py:223
msgid "Display part stocktake information (if stocktake functionality is enabled)"
msgstr "Zobrazit informace o skladových zásobách dílů (pokud je povolena funkce inventury)"

#: common/setting/user.py:229
msgid "Table String Length"
msgstr "Délka textu v tabulce"

#: common/setting/user.py:230
msgid "Maximum length limit for strings displayed in table views"
msgstr "Maximální délka textu v zobrazeních tabulek"

#: common/setting/user.py:235
msgid "Show Last Breadcrumb"
msgstr ""

#: common/setting/user.py:236
msgid "Show the current page in breadcrumbs"
msgstr ""

#: common/setting/user.py:241
msgid "Receive error reports"
msgstr "Přijímat zprávy o chybách"

#: common/setting/user.py:242
msgid "Receive notifications for system errors"
msgstr "Dostávat oznámení o systémových chybách"

#: common/setting/user.py:247
msgid "Last used printing machines"
msgstr "Poslední použité tiskárny"

#: common/setting/user.py:248
msgid "Save the last used printing machines for a user"
msgstr "Uložte poslední použité tiskárny pro uživatele"

#: common/validators.py:35
msgid "No attachment model type provided"
msgstr ""

#: common/validators.py:41
msgid "Invalid attachment model type"
msgstr ""

#: common/validators.py:82
msgid "Minimum places cannot be greater than maximum places"
msgstr "Minimální počet míst nesmí být větší než maximální počet míst"

#: common/validators.py:94
msgid "Maximum places cannot be less than minimum places"
msgstr "Maximální počet míst nesmí být menší než minimální počet míst"

#: common/validators.py:105
msgid "An empty domain is not allowed."
msgstr "Prázdná doména není povolena."

#: common/validators.py:107
#, python-brace-format
msgid "Invalid domain name: {domain}"
msgstr "Neplatný název domény: {domain}"

#: common/validators.py:123
msgid "Value must be uppercase"
msgstr ""

#: common/validators.py:129
msgid "Value must be a valid variable identifier"
msgstr ""

#: company/api.py:141
msgid "Part is Active"
msgstr "Díl je aktivní"

#: company/api.py:145
msgid "Manufacturer is Active"
msgstr "Výrobce je aktivní"

#: company/api.py:278
msgid "Supplier Part is Active"
msgstr "Díl dodavatele je aktivní"

#: company/api.py:282
msgid "Internal Part is Active"
msgstr "Interní díl je aktivní"

#: company/api.py:287
msgid "Supplier is Active"
msgstr "Dodavatel je aktivní"

#: company/api.py:299 company/models.py:498 company/serializers.py:444
#: part/serializers.py:577
msgid "Manufacturer"
msgstr "Výrobce"

#: company/api.py:306 company/models.py:97 company/models.py:370
#: stock/api.py:849
msgid "Company"
msgstr "Společnost"

#: company/api.py:316
msgid "Has Stock"
msgstr ""

#: company/models.py:98
msgid "Companies"
msgstr "Společnosti"

#: company/models.py:114
msgid "Company description"
msgstr "Popis společnosti"

#: company/models.py:115
msgid "Description of the company"
msgstr "Popis společnosti"

#: company/models.py:121
msgid "Website"
msgstr "Webová stránka"

#: company/models.py:122
msgid "Company website URL"
msgstr "Webové stránky společnosti"

#: company/models.py:128
msgid "Phone number"
msgstr "Telefonní číslo"

#: company/models.py:130
msgid "Contact phone number"
msgstr "Kontaktní telefonní číslo"

#: company/models.py:137
msgid "Contact email address"
msgstr "Kontaktní e-mailová adresa"

#: company/models.py:142 company/models.py:274 order/models.py:500
#: users/models.py:563
msgid "Contact"
msgstr "Kontakt"

#: company/models.py:144
msgid "Point of contact"
msgstr "Kontaktní místo"

#: company/models.py:150
msgid "Link to external company information"
msgstr "Odkaz na externí informace o společnosti"

#: company/models.py:164
msgid "Is this company active?"
msgstr "Je tato společnost aktivní?"

#: company/models.py:169
msgid "Is customer"
msgstr "Je zákazník"

#: company/models.py:170
msgid "Do you sell items to this company?"
msgstr "Prodáváte zboží této společnosti?"

#: company/models.py:175
msgid "Is supplier"
msgstr "Je dodavatel"

#: company/models.py:176
msgid "Do you purchase items from this company?"
msgstr "Zakupujete zboží od této společnosti?"

#: company/models.py:181
msgid "Is manufacturer"
msgstr "Je výrobce"

#: company/models.py:182
msgid "Does this company manufacture parts?"
msgstr "Vyrábí tato společnost díly?"

#: company/models.py:190
msgid "Default currency used for this company"
msgstr "Výchozí měna používaná pro tuto společnost"

#: company/models.py:313 order/models.py:510
msgid "Address"
msgstr "Adresa"

#: company/models.py:314
msgid "Addresses"
msgstr "Adresy"

#: company/models.py:371
msgid "Select company"
msgstr "Vyberte společnost"

#: company/models.py:376
msgid "Address title"
msgstr "Název adresy"

#: company/models.py:377
msgid "Title describing the address entry"
msgstr "Název popisující záznam adresy"

#: company/models.py:383
msgid "Primary address"
msgstr "Primární adresa"

#: company/models.py:384
msgid "Set as primary address"
msgstr "Nastavit jako primární adresu"

#: company/models.py:389
msgid "Line 1"
msgstr "Řádek 1"

#: company/models.py:390
msgid "Address line 1"
msgstr "1. řádek adresy"

#: company/models.py:396
msgid "Line 2"
msgstr "Řádek 2"

#: company/models.py:397
msgid "Address line 2"
msgstr "2. řádek adresy"

#: company/models.py:403 company/models.py:404
msgid "Postal code"
msgstr "PSČ"

#: company/models.py:410
msgid "City/Region"
msgstr "Město/Region"

#: company/models.py:411
msgid "Postal code city/region"
msgstr "PSČ město/region"

#: company/models.py:417
msgid "State/Province"
msgstr "Stát/kraj"

#: company/models.py:418
msgid "State or province"
msgstr "Stát nebo provincie"

#: company/models.py:424
msgid "Country"
msgstr "Země"

#: company/models.py:425
msgid "Address country"
msgstr "Adresovaná země"

#: company/models.py:431
msgid "Courier shipping notes"
msgstr "Doručovací poznámky pro kurýra"

#: company/models.py:432
msgid "Notes for shipping courier"
msgstr "Poznámky pro kurýra"

#: company/models.py:438
msgid "Internal shipping notes"
msgstr "Interní přepravní poznámky"

#: company/models.py:439
msgid "Shipping notes for internal use"
msgstr "Doručovací poznámky pro interní použití"

#: company/models.py:446
msgid "Link to address information (external)"
msgstr "Odkaz na informace o adrese (externí)"

#: company/models.py:470 company/models.py:587 company/models.py:809
#: company/serializers.py:458
msgid "Manufacturer Part"
msgstr "Výrobce dílu"

#: company/models.py:487 company/models.py:777 stock/models.py:948
#: stock/serializers.py:480
msgid "Base Part"
msgstr "Základní díl"

#: company/models.py:489 company/models.py:779
msgid "Select part"
msgstr "Zvolte díl"

#: company/models.py:499
msgid "Select manufacturer"
msgstr "Vyberte výrobce"

#: company/models.py:505 company/serializers.py:466 order/serializers.py:665
#: part/serializers.py:587
msgid "MPN"
msgstr "MPN"

#: company/models.py:506 stock/serializers.py:625
msgid "Manufacturer Part Number"
msgstr "Číslo dílu výrobce"

#: company/models.py:513
msgid "URL for external manufacturer part link"
msgstr "URL pro odkaz na díl externího výrobce"

#: company/models.py:522
msgid "Manufacturer part description"
msgstr "Popis dílu výrobce"

#: company/models.py:575
msgid "Manufacturer Part Parameter"
msgstr ""

#: company/models.py:594
msgid "Parameter name"
msgstr "Název parametru"

#: company/models.py:601
msgid "Parameter value"
msgstr "Hodnota parametru"

#: company/models.py:608 part/models.py:1204 part/models.py:3844
msgid "Units"
msgstr "Jednotky"

#: company/models.py:609
msgid "Parameter units"
msgstr "Jednotky parametru"

#: company/models.py:717
msgid "Pack units must be compatible with the base part units"
msgstr "Jednotky balení musí být kompatibilní s jednotkami základních dílů"

#: company/models.py:724
msgid "Pack units must be greater than zero"
msgstr "Jednotky balení musí být větší než nula"

#: company/models.py:738
msgid "Linked manufacturer part must reference the same base part"
msgstr "Odkazovaný díl výrobce musí odkazovat na stejný základní díl"

#: company/models.py:787 company/serializers.py:436 company/serializers.py:454
#: order/models.py:626 part/serializers.py:561
#: plugin/builtin/suppliers/digikey.py:25 plugin/builtin/suppliers/lcsc.py:26
#: plugin/builtin/suppliers/mouser.py:24 plugin/builtin/suppliers/tme.py:26
#: stock/api.py:516 templates/email/overdue_purchase_order.html:16
msgid "Supplier"
msgstr "Dodavatel"

#: company/models.py:788
msgid "Select supplier"
msgstr "Vyberte dodavatele"

#: company/models.py:794 part/serializers.py:572
msgid "Supplier stock keeping unit"
msgstr "Skladová evidence dodavatele"

#: company/models.py:800
msgid "Is this supplier part active?"
msgstr "Je tento díl dodavatele aktivní?"

#: company/models.py:810
msgid "Select manufacturer part"
msgstr "Vyberte díl výrobce"

#: company/models.py:817
msgid "URL for external supplier part link"
msgstr "Adresa URL pro odkaz na externí díl dodavatele"

#: company/models.py:826
msgid "Supplier part description"
msgstr "Popis dílu dodavatele"

#: company/models.py:833 order/serializers.py:819 order/serializers.py:2027
#: part/models.py:4356 part/models.py:4697
#: report/templates/report/inventree_bill_of_materials_report.html:140
#: report/templates/report/inventree_purchase_order_report.html:32
#: report/templates/report/inventree_return_order_report.html:27
#: report/templates/report/inventree_sales_order_report.html:32
#: report/templates/report/inventree_stock_location_report.html:105
#: stock/serializers.py:838
msgid "Note"
msgstr "Poznámka"

#: company/models.py:842 part/models.py:2182
msgid "base cost"
msgstr "základní cena"

#: company/models.py:843 part/models.py:2183
msgid "Minimum charge (e.g. stocking fee)"
msgstr "Minimální poplatek (např. poplatek za skladování)"

#: company/models.py:850 order/serializers.py:811 stock/models.py:979
#: stock/serializers.py:1666
msgid "Packaging"
msgstr "Balení"

#: company/models.py:851
msgid "Part packaging"
msgstr "Balení dílu"

#: company/models.py:856
msgid "Pack Quantity"
msgstr "Počet kusů v balení"

#: company/models.py:858
msgid "Total quantity supplied in a single pack. Leave empty for single items."
msgstr "Celkové množství dodávané v jednom balení. Pro jednotlivé položky ponechte prázdné."

#: company/models.py:877 part/models.py:2189
msgid "multiple"
msgstr "více"

#: company/models.py:878
msgid "Order multiple"
msgstr "Objednat více"

#: company/models.py:890
msgid "Quantity available from supplier"
msgstr "Množství dostupné od dodavatele"

#: company/models.py:896
msgid "Availability Updated"
msgstr "Dostupnost aktualizována"

#: company/models.py:897
msgid "Date of last update of availability data"
msgstr "Datum poslední aktualizace údajů o dostupnosti"

#: company/models.py:1025
msgid "Supplier Price Break"
msgstr ""

#: company/serializers.py:166
msgid "Return the string representation for the primary address. This property exists for backwards compatibility."
msgstr ""

#: company/serializers.py:185
msgid "Default currency used for this supplier"
msgstr "Výchozí měna používaná pro tohoto dodavatele"

#: company/serializers.py:221
msgid "Company Name"
msgstr "Jméno společnosti"

#: company/serializers.py:420 part/serializers.py:940 stock/serializers.py:498
msgid "In Stock"
msgstr "Skladem"

#: data_exporter/mixins.py:324 data_exporter/mixins.py:393
msgid "Error occurred during data export"
msgstr ""

#: data_exporter/mixins.py:375
msgid "Data export plugin returned incorrect data format"
msgstr ""

#: data_exporter/serializers.py:74
msgid "Export Format"
msgstr ""

#: data_exporter/serializers.py:75
msgid "Select export file format"
msgstr ""

#: data_exporter/serializers.py:82
msgid "Export Plugin"
msgstr ""

#: data_exporter/serializers.py:83
msgid "Select export plugin"
msgstr ""

#: generic/states/fields.py:146
msgid "Additional status information for this item"
msgstr ""

#: generic/states/fields.py:160
msgid "Custom status key"
msgstr ""

#: generic/states/serializers.py:16 plugin/models.py:45 users/models.py:119
msgid "Key"
msgstr "Klíč"

#: generic/states/serializers.py:26
msgid "Custom"
msgstr ""

#: generic/states/serializers.py:37
msgid "Class"
msgstr ""

#: generic/states/serializers.py:40
msgid "Values"
msgstr "Hodnoty"

#: generic/states/tests.py:22 order/status_codes.py:13
msgid "Placed"
msgstr "Umístěno"

#: generic/states/validators.py:21
msgid "Invalid status code"
msgstr ""

#: importer/models.py:70
msgid "Data File"
msgstr "Datový soubor"

#: importer/models.py:71
msgid "Data file to import"
msgstr ""

#: importer/models.py:80
msgid "Columns"
msgstr "Sloupce"

#: importer/models.py:87
msgid "Target model type for this import session"
msgstr ""

#: importer/models.py:93
msgid "Import status"
msgstr "Stav importu"

#: importer/models.py:103
msgid "Field Defaults"
msgstr ""

#: importer/models.py:110
msgid "Field Overrides"
msgstr ""

#: importer/models.py:117
msgid "Field Filters"
msgstr ""

#: importer/models.py:250
msgid "Some required fields have not been mapped"
msgstr ""

#: importer/models.py:407
msgid "Column is already mapped to a database field"
msgstr ""

#: importer/models.py:412
msgid "Field is already mapped to a data column"
msgstr ""

#: importer/models.py:421
msgid "Column mapping must be linked to a valid import session"
msgstr ""

#: importer/models.py:426
msgid "Column does not exist in the data file"
msgstr ""

#: importer/models.py:433
msgid "Field does not exist in the target model"
msgstr ""

#: importer/models.py:437
msgid "Selected field is read-only"
msgstr ""

#: importer/models.py:442 importer/models.py:513
msgid "Import Session"
msgstr ""

#: importer/models.py:446
msgid "Field"
msgstr ""

#: importer/models.py:448
msgid "Column"
msgstr ""

#: importer/models.py:517
msgid "Row Index"
msgstr ""

#: importer/models.py:520
msgid "Original row data"
msgstr ""

#: importer/models.py:525 machine/models.py:110
msgid "Errors"
msgstr "Chyby"

#: importer/models.py:527 part/api.py:864
msgid "Valid"
msgstr ""

#: importer/operations.py:28 importer/operations.py:49
msgid "Unsupported data file format"
msgstr ""

#: importer/operations.py:40
msgid "Failed to open data file"
msgstr ""

#: importer/operations.py:51
msgid "Invalid data file dimensions"
msgstr ""

#: importer/serializers.py:91
msgid "Invalid field defaults"
msgstr ""

#: importer/serializers.py:104
msgid "Invalid field overrides"
msgstr ""

#: importer/serializers.py:117
msgid "Invalid field filters"
msgstr ""

#: importer/serializers.py:178
msgid "Rows"
msgstr ""

#: importer/serializers.py:179
msgid "List of row IDs to accept"
msgstr ""

#: importer/serializers.py:192
msgid "No rows provided"
msgstr ""

#: importer/serializers.py:196
msgid "Row does not belong to this session"
msgstr ""

#: importer/serializers.py:199
msgid "Row contains invalid data"
msgstr ""

#: importer/serializers.py:202
msgid "Row has already been completed"
msgstr ""

#: importer/status_codes.py:13
msgid "Initializing"
msgstr ""

#: importer/status_codes.py:18
msgid "Mapping Columns"
msgstr ""

#: importer/status_codes.py:21
msgid "Importing Data"
msgstr ""

#: importer/status_codes.py:24
msgid "Processing Data"
msgstr ""

#: importer/validators.py:21
msgid "Data file exceeds maximum size limit"
msgstr ""

#: importer/validators.py:26
msgid "Data file contains no headers"
msgstr ""

#: importer/validators.py:29
msgid "Data file contains too many columns"
msgstr ""

#: importer/validators.py:32
msgid "Data file contains too many rows"
msgstr ""

#: importer/validators.py:53
msgid "Value must be a valid dictionary object"
msgstr ""

#: machine/machine_types/label_printer.py:212
msgid "Copies"
msgstr "Kopie"

#: machine/machine_types/label_printer.py:213
msgid "Number of copies to print for each label"
msgstr "Počet kopií, které se mají tisknout pro každý štítek"

#: machine/machine_types/label_printer.py:228
msgid "Connected"
msgstr "Připojeno"

#: machine/machine_types/label_printer.py:229 order/api.py:1679
msgid "Unknown"
msgstr "Neznámý"

#: machine/machine_types/label_printer.py:230
msgid "Printing"
msgstr "Tisk"

#: machine/machine_types/label_printer.py:231
msgid "No media"
msgstr "Žádná média"

#: machine/machine_types/label_printer.py:232
msgid "Paper jam"
msgstr "Zaseknutí papíru"

#: machine/machine_types/label_printer.py:233
msgid "Disconnected"
msgstr "Odpojeno"

#: machine/machine_types/label_printer.py:240
msgid "Label Printer"
msgstr "Tiskárna štítků"

#: machine/machine_types/label_printer.py:241
msgid "Directly print labels for various items."
msgstr "Přímo vytisknout štítky pro různé položky."

#: machine/machine_types/label_printer.py:247
msgid "Printer Location"
msgstr "Umístění tiskárny"

#: machine/machine_types/label_printer.py:248
msgid "Scope the printer to a specific location"
msgstr "Určení rozsahu tiskárny na konkrétní místo"

#: machine/models.py:25
msgid "Name of machine"
msgstr ""

#: machine/models.py:29
msgid "Machine Type"
msgstr ""

#: machine/models.py:29
msgid "Type of machine"
msgstr ""

#: machine/models.py:34 machine/models.py:146
msgid "Driver"
msgstr ""

#: machine/models.py:35
msgid "Driver used for the machine"
msgstr ""

#: machine/models.py:39
msgid "Machines can be disabled"
msgstr ""

#: machine/models.py:95
msgid "Driver available"
msgstr ""

#: machine/models.py:100
msgid "No errors"
msgstr ""

#: machine/models.py:105
msgid "Initialized"
msgstr ""

#: machine/models.py:117
msgid "Machine status"
msgstr ""

#: machine/models.py:145
msgid "Machine"
msgstr ""

#: machine/models.py:151
msgid "Machine Config"
msgstr ""

#: machine/models.py:156
msgid "Config type"
msgstr ""

#: order/api.py:118
msgid "Order Reference"
msgstr "Označení objednávky"

#: order/api.py:146 order/api.py:1139
msgid "Outstanding"
msgstr ""

#: order/api.py:162
msgid "Has Project Code"
msgstr ""

#: order/api.py:176 order/models.py:475
msgid "Created By"
msgstr ""

#: order/api.py:180
msgid "Created Before"
msgstr "Vytvořeno před"

#: order/api.py:184
msgid "Created After"
msgstr "Vytvořeno po"

#: order/api.py:188
msgid "Has Start Date"
msgstr ""

#: order/api.py:196
msgid "Start Date Before"
msgstr ""

#: order/api.py:200
msgid "Start Date After"
msgstr ""

#: order/api.py:204
msgid "Has Target Date"
msgstr ""

#: order/api.py:212
msgid "Target Date Before"
msgstr "Cílové datum před"

#: order/api.py:216
msgid "Target Date After"
msgstr "Cílové datum po"

#: order/api.py:267
msgid "Has Pricing"
msgstr ""

#: order/api.py:320 order/api.py:774 order/api.py:1375
msgid "Completed Before"
msgstr "Dokončeno před"

#: order/api.py:324 order/api.py:778 order/api.py:1379
msgid "Completed After"
msgstr "Dokončeno po"

#: order/api.py:491 order/api.py:876 order/api.py:1102 order/models.py:1685
#: order/models.py:1803 order/models.py:1854 order/models.py:2002
#: order/models.py:2168 order/models.py:2690 order/models.py:2756
msgid "Order"
msgstr "Objednávka"

#: order/api.py:495 order/api.py:914
msgid "Order Complete"
msgstr ""

#: order/api.py:527 order/api.py:531 order/serializers.py:676
msgid "Internal Part"
msgstr "Interní díl"

#: order/api.py:549
msgid "Order Pending"
msgstr ""

#: order/api.py:899
msgid "Completed"
msgstr "Dokončeno"

#: order/api.py:1155
msgid "Has Shipment"
msgstr ""

#: order/api.py:1673 order/models.py:539 order/models.py:1686
#: order/models.py:1804
#: report/templates/report/inventree_purchase_order_report.html:14
#: stock/serializers.py:128 templates/email/overdue_purchase_order.html:15
msgid "Purchase Order"
msgstr "Nákupní objednávka"

#: order/api.py:1675 order/models.py:1080 order/models.py:1855
#: order/models.py:2003 order/models.py:2169
#: report/templates/report/inventree_build_order_report.html:135
#: report/templates/report/inventree_sales_order_report.html:14
#: report/templates/report/inventree_sales_order_shipment_report.html:15
#: templates/email/overdue_sales_order.html:15
msgid "Sales Order"
msgstr "Prodejní objednávka"

#: order/api.py:1677 order/models.py:2340 order/models.py:2691
#: order/models.py:2757
#: report/templates/report/inventree_return_order_report.html:13
#: templates/email/overdue_return_order.html:15
msgid "Return Order"
msgstr ""

#: order/models.py:89
#: report/templates/report/inventree_purchase_order_report.html:31
#: report/templates/report/inventree_sales_order_report.html:31
msgid "Total Price"
msgstr "Celková cena"

#: order/models.py:90
msgid "Total price for this order"
msgstr ""

#: order/models.py:95 order/serializers.py:75
msgid "Order Currency"
msgstr ""

#: order/models.py:98 order/serializers.py:76
msgid "Currency for this order (leave blank to use company default)"
msgstr ""

#: order/models.py:323
msgid "This order is locked and cannot be modified"
msgstr ""

#: order/models.py:370
msgid "Contact does not match selected company"
msgstr ""

#: order/models.py:377
msgid "Start date must be before target date"
msgstr ""

#: order/models.py:430
msgid "Order description (optional)"
msgstr ""

#: order/models.py:439
msgid "Select project code for this order"
msgstr ""

#: order/models.py:445 order/models.py:1586 order/models.py:2057
msgid "Link to external page"
msgstr ""

#: order/models.py:452
msgid "Start date"
msgstr ""

#: order/models.py:453
msgid "Scheduled start date for this order"
msgstr ""

#: order/models.py:459 order/models.py:1593 order/serializers.py:269
#: report/templates/report/inventree_build_order_report.html:125
msgid "Target Date"
msgstr "Cílené datum"

#: order/models.py:461
msgid "Expected date for order delivery. Order will be overdue after this date."
msgstr ""

#: order/models.py:481
msgid "Issue Date"
msgstr "Datum vystavení"

#: order/models.py:482
msgid "Date order was issued"
msgstr "Datum vystavení objednávky"

#: order/models.py:490
msgid "User or group responsible for this order"
msgstr ""

#: order/models.py:501
msgid "Point of contact for this order"
msgstr ""

#: order/models.py:511
msgid "Company address for this order"
msgstr ""

#: order/models.py:602 order/models.py:1141
msgid "Order reference"
msgstr ""

#: order/models.py:611 order/models.py:1165 order/models.py:2428
#: stock/serializers.py:612 stock/serializers.py:1010 users/models.py:544
msgid "Status"
msgstr "Stav"

#: order/models.py:612
msgid "Purchase order status"
msgstr ""

#: order/models.py:627
msgid "Company from which the items are being ordered"
msgstr "Společnost, od které se položky objednávají"

#: order/models.py:638
msgid "Supplier Reference"
msgstr "Reference dodavatele"

#: order/models.py:639
msgid "Supplier order reference code"
msgstr "Referenční kód objednávky dodavatele"

#: order/models.py:648
msgid "received by"
msgstr "přijal"

#: order/models.py:655 order/models.py:2443
msgid "Date order was completed"
msgstr "Datum dokončení objednávky"

#: order/models.py:664 order/models.py:1733
msgid "Destination"
msgstr "Místo určení"

#: order/models.py:665 order/models.py:1737
msgid "Destination for received items"
msgstr ""

#: order/models.py:711
msgid "Part supplier must match PO supplier"
msgstr "Dodavatel dílu se musí shodovat s dodavatelem PO"

#: order/models.py:971
msgid "Quantity must be a positive number"
msgstr "Množství musí být kladné"

#: order/models.py:1152 order/models.py:2415 stock/models.py:1001
#: stock/models.py:1002 stock/serializers.py:1405
#: templates/email/overdue_return_order.html:16
#: templates/email/overdue_sales_order.html:16
msgid "Customer"
msgstr "Zákazník"

#: order/models.py:1153
msgid "Company to which the items are being sold"
msgstr "Společnost, jíž se položky prodávají"

#: order/models.py:1166
msgid "Sales order status"
msgstr "Stav prodejní objednávky"

#: order/models.py:1177 order/models.py:2435
msgid "Customer Reference "
msgstr "Reference zákazníka "

#: order/models.py:1178 order/models.py:2436
msgid "Customer order reference code"
msgstr "Referenční kód objednávky zákazníka"

#: order/models.py:1182 order/models.py:2009
msgid "Shipment Date"
msgstr "Datum odeslání"

#: order/models.py:1191
msgid "shipped by"
msgstr "odesláno společností"

#: order/models.py:1230
msgid "Order is already complete"
msgstr "Objednávka je již dokončena"

#: order/models.py:1233
msgid "Order is already cancelled"
msgstr "Objednávka je již zrušena"

#: order/models.py:1237
msgid "Only an open order can be marked as complete"
msgstr "Pouze otevřená objednávka může být označena jako kompletní"

#: order/models.py:1241
msgid "Order cannot be completed as there are incomplete shipments"
msgstr "Objednávku nelze dokončit, protože dodávky jsou nekompletní"

#: order/models.py:1246
msgid "Order cannot be completed as there are incomplete allocations"
msgstr ""

#: order/models.py:1251
msgid "Order cannot be completed as there are incomplete line items"
msgstr "Objednávka nemůže být dokončena, protože jsou neúplné řádkové položky"

#: order/models.py:1535 order/models.py:1548
msgid "The order is locked and cannot be modified"
msgstr ""

#: order/models.py:1556
msgid "Item quantity"
msgstr "Množství položky"

#: order/models.py:1573
msgid "Line item reference"
msgstr "Označení řádkové položky"

#: order/models.py:1580
msgid "Line item notes"
msgstr "Poznámky k řádkovým položkám"

#: order/models.py:1595
msgid "Target date for this line item (leave blank to use the target date from the order)"
msgstr "Cílové datum pro tuto řádkovou položku (pro použití cílového data z objednávky ponechte prázdné)"

#: order/models.py:1616
msgid "Line item description (optional)"
msgstr "Popis řádkové položky (nepovinné)"

#: order/models.py:1623
msgid "Additional context for this line"
msgstr "Dodatečný kontext pro tento řádek"

#: order/models.py:1633
msgid "Unit price"
msgstr "Cena za jednotku"

#: order/models.py:1647
msgid "Purchase Order Line Item"
msgstr "Řádková položka nákupní objednávky"

#: order/models.py:1671
msgid "Supplier part must match supplier"
msgstr "Dodavatelský díl musí odpovídat dodavateli"

#: order/models.py:1705
msgid "Supplier part"
msgstr "Díl dodavatele"

#: order/models.py:1712
msgid "Received"
msgstr "Doručeno"

#: order/models.py:1713
msgid "Number of items received"
msgstr "Počet přijatých položek"

#: order/models.py:1721 stock/models.py:1124 stock/serializers.py:679
msgid "Purchase Price"
msgstr "Nákupní cena"

#: order/models.py:1722
msgid "Unit purchase price"
msgstr "Jednotková nákupní cena"

#: order/models.py:1792
msgid "Purchase Order Extra Line"
msgstr ""

#: order/models.py:1821
msgid "Sales Order Line Item"
msgstr "Řádková položka prodejní objednávky"

#: order/models.py:1842
msgid "Virtual part cannot be assigned to a sales order"
msgstr "Virtuální díl nelze přiřadit k prodejní objednávce"

#: order/models.py:1847
msgid "Only salable parts can be assigned to a sales order"
msgstr "K prodejní objednávce lze přiřadit pouze prodejné díly"

#: order/models.py:1873
msgid "Sale Price"
msgstr "Prodejní cena"

#: order/models.py:1874
msgid "Unit sale price"
msgstr "Jednotková prodejní cena"

#: order/models.py:1883 order/status_codes.py:50
msgid "Shipped"
msgstr "Odesláno"

#: order/models.py:1884
msgid "Shipped quantity"
msgstr "Odeslané množství"

#: order/models.py:1978
msgid "Sales Order Shipment"
msgstr ""

#: order/models.py:2010
msgid "Date of shipment"
msgstr "Datum odeslání"

#: order/models.py:2016
msgid "Delivery Date"
msgstr "Datum doručení"

#: order/models.py:2017
msgid "Date of delivery of shipment"
msgstr "Datum doručení zásilky"

#: order/models.py:2025
msgid "Checked By"
msgstr "Kontroloval(a)"

#: order/models.py:2026
msgid "User who checked this shipment"
msgstr "Uživatel, který zkontroloval tuto zásilku"

#: order/models.py:2033 order/models.py:2265 order/serializers.py:1685
#: order/serializers.py:1809
#: report/templates/report/inventree_sales_order_shipment_report.html:14
msgid "Shipment"
msgstr "Doprava"

#: order/models.py:2034
msgid "Shipment number"
msgstr "Číslo zásilky"

#: order/models.py:2042
msgid "Tracking Number"
msgstr "Sledovací číslo"

#: order/models.py:2043
msgid "Shipment tracking information"
msgstr "Informace o sledování zásilky"

#: order/models.py:2050
msgid "Invoice Number"
msgstr "Číslo faktury"

#: order/models.py:2051
msgid "Reference number for associated invoice"
msgstr "Referenční číslo přiřazené faktury"

#: order/models.py:2074
msgid "Shipment has already been sent"
msgstr "Zásilka již byla odeslána"

#: order/models.py:2077
msgid "Shipment has no allocated stock items"
msgstr "Zásilka nemá žádné přidělené skladové položky"

#: order/models.py:2157
msgid "Sales Order Extra Line"
msgstr ""

#: order/models.py:2186
msgid "Sales Order Allocation"
msgstr ""

#: order/models.py:2209 order/models.py:2211
msgid "Stock item has not been assigned"
msgstr "Skladová položka nebyla přiřazena"

#: order/models.py:2218
msgid "Cannot allocate stock item to a line with a different part"
msgstr "Nelze přidělit skladovou položku na řádek s jiným dílem"

#: order/models.py:2221
msgid "Cannot allocate stock to a line without a part"
msgstr "Nelze přidělit skladovou položku na řádek bez dílu"

#: order/models.py:2224
msgid "Allocation quantity cannot exceed stock quantity"
msgstr "Přidělené množství nesmí překročit množství zásob"

#: order/models.py:2243 order/serializers.py:1555
msgid "Quantity must be 1 for serialized stock item"
msgstr "Množství musí být 1 pro serializovanou skladovou položku"

#: order/models.py:2246
msgid "Sales order does not match shipment"
msgstr "Prodejní objednávka neodpovídá zásilce"

#: order/models.py:2247 plugin/base/barcodes/api.py:642
msgid "Shipment does not match sales order"
msgstr "Zásilka neodpovídá prodejní objednávce"

#: order/models.py:2255
msgid "Line"
msgstr "Řádek"

#: order/models.py:2266
msgid "Sales order shipment reference"
msgstr "Odkaz na zásilku z prodejní objednávky"

#: order/models.py:2279 order/models.py:2698
msgid "Item"
msgstr "Položka"

#: order/models.py:2280
msgid "Select stock item to allocate"
msgstr "Vyberte skladovou položku pro přidělení"

#: order/models.py:2289
msgid "Enter stock allocation quantity"
msgstr "Zadejte množství pro přidělení zásob"

#: order/models.py:2404
msgid "Return Order reference"
msgstr "Reference návratové objednávky"

#: order/models.py:2416
msgid "Company from which items are being returned"
msgstr "Společnost, od které se vrací položky"

#: order/models.py:2429
msgid "Return order status"
msgstr "Stav návratové objednávky"

#: order/models.py:2656
msgid "Return Order Line Item"
msgstr ""

#: order/models.py:2669
msgid "Stock item must be specified"
msgstr ""

#: order/models.py:2673
msgid "Return quantity exceeds stock quantity"
msgstr ""

#: order/models.py:2678
msgid "Return quantity must be greater than zero"
msgstr ""

#: order/models.py:2683
msgid "Invalid quantity for serialized stock item"
msgstr ""

#: order/models.py:2699
msgid "Select item to return from customer"
msgstr "Vyberte položku pro vrácení od zákazníka"

#: order/models.py:2714
msgid "Received Date"
msgstr "Datum přijetí"

#: order/models.py:2715
msgid "The date this this return item was received"
msgstr "Datum přijetí této vrácené položky"

#: order/models.py:2727
msgid "Outcome"
msgstr "Výsledek"

#: order/models.py:2728
msgid "Outcome for this line item"
msgstr "Výsledky pro tuto položku"

#: order/models.py:2735
msgid "Cost associated with return or repair for this line item"
msgstr "Náklady spojené s návratem nebo opravou této položky"

#: order/models.py:2745
msgid "Return Order Extra Line"
msgstr ""

#: order/serializers.py:89
msgid "Order ID"
msgstr ""

#: order/serializers.py:89
msgid "ID of the order to duplicate"
msgstr ""

#: order/serializers.py:95
msgid "Copy Lines"
msgstr ""

#: order/serializers.py:96
msgid "Copy line items from the original order"
msgstr ""

#: order/serializers.py:102
msgid "Copy Extra Lines"
msgstr ""

#: order/serializers.py:103
msgid "Copy extra line items from the original order"
msgstr ""

#: order/serializers.py:116
#: report/templates/report/inventree_purchase_order_report.html:22
#: report/templates/report/inventree_return_order_report.html:19
#: report/templates/report/inventree_sales_order_report.html:22
msgid "Line Items"
msgstr "Řádkové položky"

#: order/serializers.py:121
msgid "Completed Lines"
msgstr "Dokončené řádky"

#: order/serializers.py:172
msgid "Duplicate Order"
msgstr ""

#: order/serializers.py:173
msgid "Specify options for duplicating this order"
msgstr ""

#: order/serializers.py:249
msgid "Invalid order ID"
msgstr ""

#: order/serializers.py:388
msgid "Supplier Name"
msgstr "Název dodavatele"

#: order/serializers.py:430
msgid "Order cannot be cancelled"
msgstr "Objednávku nelze zrušit"

#: order/serializers.py:445 order/serializers.py:1576
msgid "Allow order to be closed with incomplete line items"
msgstr "Povolit uzavření objednávky s neúplnými řádkovými položkami"

#: order/serializers.py:455 order/serializers.py:1586
msgid "Order has incomplete line items"
msgstr "Objednávka má nedokončené řádkové položky"

#: order/serializers.py:608
msgid "Order is not open"
msgstr "Objednávka není otevřena"

#: order/serializers.py:629
msgid "Auto Pricing"
msgstr "Automatická cena"

#: order/serializers.py:631
msgid "Automatically calculate purchase price based on supplier part data"
msgstr "Automaticky vypočítat nákupní cenu na základě údajů o dílech dodavatele"

#: order/serializers.py:641
msgid "Purchase price currency"
msgstr "Měna nákupní ceny"

#: order/serializers.py:649
msgid "Merge Items"
msgstr "Sloučit položky"

#: order/serializers.py:651
msgid "Merge items with the same part, destination and target date into one line item"
msgstr "Sloučit položky se stejným dílem, místem určení a cílovým datem do jedné řádkové položky"

#: order/serializers.py:658 part/serializers.py:571
msgid "SKU"
msgstr "Číslo zboží (SKU)"

#: order/serializers.py:672 part/models.py:1089 part/serializers.py:412
msgid "Internal Part Number"
msgstr "Interní číslo dílu"

#: order/serializers.py:680
msgid "Internal Part Name"
msgstr "Interní název dílu"

#: order/serializers.py:696
msgid "Supplier part must be specified"
msgstr "Musí být uveden díl dodavatele"

#: order/serializers.py:699
msgid "Purchase order must be specified"
msgstr "Objednávka musí být zadána"

#: order/serializers.py:707
msgid "Supplier must match purchase order"
msgstr "Dodavatel musí odpovídat objednávce"

#: order/serializers.py:708
msgid "Purchase order must match supplier"
msgstr "Objednávka musí odpovídat dodavateli"

#: order/serializers.py:754 order/serializers.py:1656
msgid "Line Item"
msgstr "Řádková položka"

#: order/serializers.py:760
msgid "Line item does not match purchase order"
msgstr "Řádková položka neodpovídá nákupní objednávce"

#: order/serializers.py:770 order/serializers.py:917 order/serializers.py:2023
msgid "Select destination location for received items"
msgstr "Vyberte cílové umístění pro přijaté položky"

#: order/serializers.py:786
msgid "Enter batch code for incoming stock items"
msgstr "Zadat kód šarže pro příchozí skladové položky"

#: order/serializers.py:793 stock/models.py:1083 users/models.py:143
msgid "Expiry Date"
msgstr ""

#: order/serializers.py:794
msgid "Enter expiry date for incoming stock items"
msgstr ""

#: order/serializers.py:802
msgid "Enter serial numbers for incoming stock items"
msgstr "Zadat sériová čísla pro příchozí skladové položky"

#: order/serializers.py:812
msgid "Override packaging information for incoming stock items"
msgstr "Přepsat informace o obalu pro příchozí skladové položky"

#: order/serializers.py:820 order/serializers.py:2028
msgid "Additional note for incoming stock items"
msgstr "Dodatečná poznámka pro příchozí skladové položky"

#: order/serializers.py:827
msgid "Barcode"
msgstr "Čárový kód"

#: order/serializers.py:828
msgid "Scanned barcode"
msgstr "Naskenovaný čárový kód"

#: order/serializers.py:844
msgid "Barcode is already in use"
msgstr "Tento čárový kód se již používá"

#: order/serializers.py:867
msgid "An integer quantity must be provided for trackable parts"
msgstr "U sledovatelných dílů musí být uvedeno celočíselné množství"

#: order/serializers.py:934 order/serializers.py:2047
msgid "Line items must be provided"
msgstr "Musí být uvedeny řádkové položky"

#: order/serializers.py:950
msgid "Destination location must be specified"
msgstr "Místo určení musí být specifikováno"

#: order/serializers.py:961
msgid "Supplied barcode values must be unique"
msgstr "Hodnoty dodaných čárových kódů musí být unikátní"

#: order/serializers.py:1092
msgid "Shipments"
msgstr "Zásilky"

#: order/serializers.py:1096
msgid "Completed Shipments"
msgstr "Dokončené zásilky"

#: order/serializers.py:1283
msgid "Sale price currency"
msgstr ""

#: order/serializers.py:1331
msgid "Allocated Items"
msgstr "Přidělené položky"

#: order/serializers.py:1458
msgid "No shipment details provided"
msgstr ""

#: order/serializers.py:1519 order/serializers.py:1665
msgid "Line item is not associated with this order"
msgstr ""

#: order/serializers.py:1538
msgid "Quantity must be positive"
msgstr "Množství musí být kladné"

#: order/serializers.py:1675
msgid "Enter serial numbers to allocate"
msgstr ""

#: order/serializers.py:1697 order/serializers.py:1817
msgid "Shipment has already been shipped"
msgstr ""

#: order/serializers.py:1700 order/serializers.py:1820
msgid "Shipment is not associated with this order"
msgstr ""

#: order/serializers.py:1755
msgid "No match found for the following serial numbers"
msgstr ""

#: order/serializers.py:1762
msgid "The following serial numbers are unavailable"
msgstr ""

#: order/serializers.py:1989
msgid "Return order line item"
msgstr ""

#: order/serializers.py:1999
msgid "Line item does not match return order"
msgstr ""

#: order/serializers.py:2002
msgid "Line item has already been received"
msgstr ""

#: order/serializers.py:2039
msgid "Items can only be received against orders which are in progress"
msgstr ""

#: order/serializers.py:2131
msgid "Quantity to return"
msgstr ""

#: order/serializers.py:2143
msgid "Line price currency"
msgstr ""

#: order/status_codes.py:17 order/status_codes.py:54 stock/status_codes.py:16
msgid "Lost"
msgstr "Ztraceno"

#: order/status_codes.py:18 order/status_codes.py:55 stock/status_codes.py:24
msgid "Returned"
msgstr "Vráceno"

#: order/status_codes.py:47 order/status_codes.py:79
msgid "In Progress"
msgstr "Zpracovává se"

#: order/status_codes.py:105
msgid "Return"
msgstr "Vrátit zpět"

#: order/status_codes.py:108
msgid "Repair"
msgstr "Oprava"

#: order/status_codes.py:111
msgid "Replace"
msgstr "Náhrada"

#: order/status_codes.py:114
msgid "Refund"
msgstr "Vrácení peněz"

#: order/status_codes.py:117
msgid "Reject"
msgstr "Odmítnout"

#: order/tasks.py:44
msgid "Overdue Purchase Order"
msgstr ""

#: order/tasks.py:49
#, python-brace-format
msgid "Purchase order {po} is now overdue"
msgstr ""

#: order/tasks.py:112
msgid "Overdue Sales Order"
msgstr "Opožděná prodejní objednávka"

#: order/tasks.py:117
#, python-brace-format
msgid "Sales order {so} is now overdue"
msgstr "Prodejní objednávka {so} je nyní opožděná"

#: order/tasks.py:177
msgid "Overdue Return Order"
msgstr ""

#: order/tasks.py:182
#, python-brace-format
msgid "Return order {ro} is now overdue"
msgstr ""

#: part/api.py:115
msgid "Starred"
msgstr ""

#: part/api.py:117
msgid "Filter by starred categories"
msgstr ""

#: part/api.py:134 stock/api.py:255
msgid "Depth"
msgstr ""

#: part/api.py:134
msgid "Filter by category depth"
msgstr ""

#: part/api.py:152 stock/api.py:273
msgid "Top Level"
msgstr ""

#: part/api.py:154
msgid "Filter by top-level categories"
msgstr ""

#: part/api.py:167 stock/api.py:288
msgid "Cascade"
msgstr ""

#: part/api.py:169
msgid "Include sub-categories in filtered results"
msgstr ""

#: part/api.py:189
msgid "Parent"
msgstr ""

#: part/api.py:191
msgid "Filter by parent category"
msgstr ""

#: part/api.py:226
msgid "Exclude sub-categories under the specified category"
msgstr ""

#: part/api.py:438
msgid "Has Results"
msgstr ""

#: part/api.py:611
msgid "Incoming Purchase Order"
msgstr ""

#: part/api.py:625
msgid "Outgoing Sales Order"
msgstr ""

#: part/api.py:637
msgid "Stock produced by Build Order"
msgstr ""

#: part/api.py:719
msgid "Stock required for Build Order"
msgstr ""

#: part/api.py:865
msgid "Validate entire Bill of Materials"
msgstr "Schválit celý kusovník"

#: part/api.py:871
msgid "This option must be selected"
msgstr ""

#: part/api.py:907
msgid "Is Variant"
msgstr ""

#: part/api.py:915
msgid "Is Revision"
msgstr "Je revizí"

#: part/api.py:925
msgid "Has Revisions"
msgstr "Má revize"

#: part/api.py:1116
msgid "BOM Valid"
msgstr "Kusovník schválen"

#: part/api.py:1775
msgid "Assembly part is testable"
msgstr ""

#: part/api.py:1784
msgid "Component part is testable"
msgstr ""

#: part/api.py:1835
msgid "Uses"
msgstr ""

#: part/models.py:90 part/models.py:4137
#: templates/email/part_event_notification.html:16
msgid "Part Category"
msgstr "Kategorie dílu"

#: part/models.py:91 users/ruleset.py:32
msgid "Part Categories"
msgstr "Kategorie dílů"

#: part/models.py:109 part/models.py:1134
msgid "Default Location"
msgstr "Výchozí umístění"

#: part/models.py:110
msgid "Default location for parts in this category"
msgstr "Výchozí umístění dílů v této kategorii"

#: part/models.py:115 stock/models.py:212
msgid "Structural"
msgstr "Strukturální"

#: part/models.py:117
msgid "Parts may not be directly assigned to a structural category, but may be assigned to child categories."
msgstr "Díly nesmějí být přímo zařazeny do strukturální kategorie, ale mohou být zařazeny do jejích podkategorií."

#: part/models.py:126
msgid "Default keywords"
msgstr "Výchozí klíčová slova"

#: part/models.py:127
msgid "Default keywords for parts in this category"
msgstr "Výchozí klíčová slova pro díly v této kategorii"

#: part/models.py:134 stock/models.py:96 stock/models.py:194
msgid "Icon"
msgstr "Ikona"

#: part/models.py:135 part/serializers.py:152 part/serializers.py:171
#: stock/models.py:195
msgid "Icon (optional)"
msgstr "Ikona (volitelná)"

#: part/models.py:179
msgid "You cannot make this part category structural because some parts are already assigned to it!"
msgstr ""

#: part/models.py:458 part/serializers.py:130 part/serializers.py:343
#: users/ruleset.py:33
msgid "Parts"
msgstr "Díly"

#: part/models.py:510
msgid "Cannot delete this part as it is locked"
msgstr ""

#: part/models.py:513
msgid "Cannot delete this part as it is still active"
msgstr ""

#: part/models.py:518
msgid "Cannot delete this part as it is used in an assembly"
msgstr "Tento díl nelze odstranit, protože je použit v sestavě"

#: part/models.py:556
msgid "Invalid choice for parent part"
msgstr ""

#: part/models.py:604 part/models.py:611
#, python-brace-format
msgid "Part '{self}' cannot be used in BOM for '{parent}' (recursive)"
msgstr "Díl '{self}' nelze použít v kusovníku '{parent}' (rekurzivní)"

#: part/models.py:623
#, python-brace-format
msgid "Part '{parent}' is  used in BOM for '{self}' (recursive)"
msgstr "Díl '{parent}' je využit v kusovníku '{self}' (rekurzivní)"

#: part/models.py:690
#, python-brace-format
msgid "IPN must match regex pattern {pattern}"
msgstr ""

#: part/models.py:698
msgid "Part cannot be a revision of itself"
msgstr ""

#: part/models.py:705
msgid "Cannot make a revision of a part which is already a revision"
msgstr ""

#: part/models.py:712
msgid "Revision code must be specified"
msgstr ""

#: part/models.py:719
msgid "Revisions are only allowed for assembly parts"
msgstr "Revize jsou povoleny pouze pro sestavy"

#: part/models.py:726
msgid "Cannot make a revision of a template part"
msgstr ""

#: part/models.py:732
msgid "Parent part must point to the same template"
msgstr ""

#: part/models.py:829
msgid "Stock item with this serial number already exists"
msgstr "Skladová položka s tímto sériovým číslem již existuje"

#: part/models.py:971
msgid "Duplicate IPN not allowed in part settings"
msgstr ""

#: part/models.py:983
msgid "Duplicate part revision already exists."
msgstr ""

#: part/models.py:992
msgid "Part with this Name, IPN and Revision already exists."
msgstr ""

#: part/models.py:1007
msgid "Parts cannot be assigned to structural part categories!"
msgstr ""

#: part/models.py:1039
msgid "Part name"
msgstr "Název dílu"

#: part/models.py:1044
msgid "Is Template"
msgstr "Je šablonou"

#: part/models.py:1045
msgid "Is this part a template part?"
msgstr ""

#: part/models.py:1055
msgid "Is this part a variant of another part?"
msgstr ""

#: part/models.py:1056
msgid "Variant Of"
msgstr ""

#: part/models.py:1063
msgid "Part description (optional)"
msgstr ""

#: part/models.py:1070
msgid "Keywords"
msgstr "Klíčová slova"

#: part/models.py:1071
msgid "Part keywords to improve visibility in search results"
msgstr "Klíčová slova dílu pro zlepšení vyhledávání"

#: part/models.py:1081
msgid "Part category"
msgstr "Kategorie dílu"

#: part/models.py:1088 part/serializers.py:926
#: report/templates/report/inventree_stock_location_report.html:103
msgid "IPN"
msgstr ""

#: part/models.py:1096
msgid "Part revision or version number"
msgstr "Číslo revize nebo verze dílu"

#: part/models.py:1097 report/models.py:215
msgid "Revision"
msgstr "Revize"

#: part/models.py:1106
msgid "Is this part a revision of another part?"
msgstr ""

#: part/models.py:1107
msgid "Revision Of"
msgstr ""

#: part/models.py:1132
msgid "Where is this item normally stored?"
msgstr ""

#: part/models.py:1178
msgid "Default Supplier"
msgstr "Výchozí dodavatel"

#: part/models.py:1179
msgid "Default supplier part"
msgstr "Výchozí díl dodavatele"

#: part/models.py:1186
msgid "Default Expiry"
msgstr ""

#: part/models.py:1187
msgid "Expiry time (in days) for stock items of this part"
msgstr ""

#: part/models.py:1195 part/serializers.py:977
msgid "Minimum Stock"
msgstr ""

#: part/models.py:1196
msgid "Minimum allowed stock level"
msgstr ""

#: part/models.py:1205
msgid "Units of measure for this part"
msgstr ""

#: part/models.py:1212
msgid "Can this part be built from other parts?"
msgstr "Lze tento díl sestavit z jiných dílů?"

#: part/models.py:1218
msgid "Can this part be used to build other parts?"
msgstr "Lze tento díl použít k sestavení jiných dílů?"

#: part/models.py:1224
msgid "Does this part have tracking for unique items?"
msgstr "Lze u tohoto dílu sledovat jednotlivé položky?"

#: part/models.py:1230
msgid "Can this part have test results recorded against it?"
msgstr ""

#: part/models.py:1236
msgid "Can this part be purchased from external suppliers?"
msgstr ""

#: part/models.py:1242
msgid "Can this part be sold to customers?"
msgstr "Lze tento díl prodávat zákazníkům?"

#: part/models.py:1246
msgid "Is this part active?"
msgstr "Je tento díl aktivní?"

#: part/models.py:1252
msgid "Locked parts cannot be edited"
msgstr ""

#: part/models.py:1258
msgid "Is this a virtual part, such as a software product or license?"
msgstr "Je to virtuální díl, například softwarový produkt nebo licence?"

#: part/models.py:1264
msgid "BOM checksum"
msgstr "Kontrolní součet kusovníku"

#: part/models.py:1265
msgid "Stored BOM checksum"
msgstr "Uložený kontrolní součet kusovníku"

#: part/models.py:1273
msgid "BOM checked by"
msgstr "Kusovník zkontroloval"

#: part/models.py:1278
msgid "BOM checked date"
msgstr "Datum kontroly kusovníku"

#: part/models.py:1294
msgid "Creation User"
msgstr ""

#: part/models.py:1304
msgid "Owner responsible for this part"
msgstr ""

#: part/models.py:1309
msgid "Last Stocktake"
msgstr "Poslední inventura"

#: part/models.py:2190
msgid "Sell multiple"
msgstr ""

#: part/models.py:3185
msgid "Currency used to cache pricing calculations"
msgstr ""

#: part/models.py:3201
msgid "Minimum BOM Cost"
msgstr "Minimální cena kusovníku"

#: part/models.py:3202
msgid "Minimum cost of component parts"
msgstr ""

#: part/models.py:3208
msgid "Maximum BOM Cost"
msgstr "Maximální cena kusovníku"

#: part/models.py:3209
msgid "Maximum cost of component parts"
msgstr ""

#: part/models.py:3215
msgid "Minimum Purchase Cost"
msgstr "Minimální nákupní cena"

#: part/models.py:3216
msgid "Minimum historical purchase cost"
msgstr ""

#: part/models.py:3222
msgid "Maximum Purchase Cost"
msgstr "Maximální nákupní cena"

#: part/models.py:3223
msgid "Maximum historical purchase cost"
msgstr ""

#: part/models.py:3229
msgid "Minimum Internal Price"
msgstr ""

#: part/models.py:3230
msgid "Minimum cost based on internal price breaks"
msgstr ""

#: part/models.py:3236
msgid "Maximum Internal Price"
msgstr ""

#: part/models.py:3237
msgid "Maximum cost based on internal price breaks"
msgstr ""

#: part/models.py:3243
msgid "Minimum Supplier Price"
msgstr ""

#: part/models.py:3244
msgid "Minimum price of part from external suppliers"
msgstr ""

#: part/models.py:3250
msgid "Maximum Supplier Price"
msgstr ""

#: part/models.py:3251
msgid "Maximum price of part from external suppliers"
msgstr ""

#: part/models.py:3257
msgid "Minimum Variant Cost"
msgstr "Minimální cena variant"

#: part/models.py:3258
msgid "Calculated minimum cost of variant parts"
msgstr ""

#: part/models.py:3264
msgid "Maximum Variant Cost"
msgstr "Maximální cena variant"

#: part/models.py:3265
msgid "Calculated maximum cost of variant parts"
msgstr ""

#: part/models.py:3271 part/models.py:3285
msgid "Minimum Cost"
msgstr "Minimální cena"

#: part/models.py:3272
msgid "Override minimum cost"
msgstr ""

#: part/models.py:3278 part/models.py:3292
msgid "Maximum Cost"
msgstr "Maximální cena"

#: part/models.py:3279
msgid "Override maximum cost"
msgstr ""

#: part/models.py:3286
msgid "Calculated overall minimum cost"
msgstr ""

#: part/models.py:3293
msgid "Calculated overall maximum cost"
msgstr ""

#: part/models.py:3299
msgid "Minimum Sale Price"
msgstr ""

#: part/models.py:3300
msgid "Minimum sale price based on price breaks"
msgstr ""

#: part/models.py:3306
msgid "Maximum Sale Price"
msgstr ""

#: part/models.py:3307
msgid "Maximum sale price based on price breaks"
msgstr ""

#: part/models.py:3313
msgid "Minimum Sale Cost"
msgstr "Minimální prodejní cena"

#: part/models.py:3314
msgid "Minimum historical sale price"
msgstr ""

#: part/models.py:3320
msgid "Maximum Sale Cost"
msgstr "Maximální prodejní cena"

#: part/models.py:3321
msgid "Maximum historical sale price"
msgstr ""

#: part/models.py:3340
msgid "Part for stocktake"
msgstr ""

#: part/models.py:3345
msgid "Item Count"
msgstr "Počet položek"

#: part/models.py:3346
msgid "Number of individual stock entries at time of stocktake"
msgstr ""

#: part/models.py:3354
msgid "Total available stock at time of stocktake"
msgstr ""

#: part/models.py:3358 part/models.py:3441 part/serializers.py:273
#: report/templates/report/inventree_test_report.html:106
msgid "Date"
msgstr "Datum"

#: part/models.py:3359
msgid "Date stocktake was performed"
msgstr ""

#: part/models.py:3367
msgid "Additional notes"
msgstr "Další poznámky"

#: part/models.py:3377
msgid "User who performed this stocktake"
msgstr ""

#: part/models.py:3383
msgid "Minimum Stock Cost"
msgstr "Minimální cena zásob"

#: part/models.py:3384
msgid "Estimated minimum cost of stock on hand"
msgstr ""

#: part/models.py:3390
msgid "Maximum Stock Cost"
msgstr "Maximální cena zásob"

#: part/models.py:3391
msgid "Estimated maximum cost of stock on hand"
msgstr ""

#: part/models.py:3447
msgid "Report"
msgstr ""

#: part/models.py:3448
msgid "Stocktake report file (generated internally)"
msgstr ""

#: part/models.py:3453
msgid "Part Count"
msgstr "Počet dílů"

#: part/models.py:3454
msgid "Number of parts covered by stocktake"
msgstr ""

#: part/models.py:3464
msgid "User who requested this stocktake report"
msgstr ""

#: part/models.py:3474
msgid "Part Sale Price Break"
msgstr ""

#: part/models.py:3586
msgid "Part Test Template"
msgstr ""

#: part/models.py:3612
msgid "Invalid template name - must include at least one alphanumeric character"
msgstr ""

#: part/models.py:3633 part/models.py:3803
msgid "Choices must be unique"
msgstr ""

#: part/models.py:3644
msgid "Test templates can only be created for testable parts"
msgstr ""

#: part/models.py:3655
msgid "Test template with the same key already exists for part"
msgstr ""

#: part/models.py:3672
msgid "Test Name"
msgstr "Název testu"

#: part/models.py:3673
msgid "Enter a name for the test"
msgstr ""

#: part/models.py:3679
msgid "Test Key"
msgstr ""

#: part/models.py:3680
msgid "Simplified key for the test"
msgstr ""

#: part/models.py:3687
msgid "Test Description"
msgstr ""

#: part/models.py:3688
msgid "Enter description for this test"
msgstr ""

#: part/models.py:3692 report/models.py:271
msgid "Enabled"
msgstr ""

#: part/models.py:3692
msgid "Is this test enabled?"
msgstr ""

#: part/models.py:3697
msgid "Required"
msgstr ""

#: part/models.py:3698
msgid "Is this test required to pass?"
msgstr ""

#: part/models.py:3703
msgid "Requires Value"
msgstr ""

#: part/models.py:3704
msgid "Does this test require a value when adding a test result?"
msgstr ""

#: part/models.py:3709
msgid "Requires Attachment"
msgstr ""

#: part/models.py:3711
msgid "Does this test require a file attachment when adding a test result?"
msgstr ""

#: part/models.py:3717 part/models.py:3865
msgid "Choices"
msgstr ""

#: part/models.py:3718
msgid "Valid choices for this test (comma-separated)"
msgstr ""

#: part/models.py:3751
msgid "Part Parameter Template"
msgstr ""

#: part/models.py:3778
msgid "Checkbox parameters cannot have units"
msgstr ""

#: part/models.py:3783
msgid "Checkbox parameters cannot have choices"
msgstr ""

#: part/models.py:3820
msgid "Parameter template name must be unique"
msgstr ""

#: part/models.py:3838
msgid "Parameter Name"
msgstr ""

#: part/models.py:3845
msgid "Physical units for this parameter"
msgstr ""

#: part/models.py:3853
msgid "Parameter description"
msgstr ""

#: part/models.py:3859
msgid "Checkbox"
msgstr ""

#: part/models.py:3860
msgid "Is this parameter a checkbox?"
msgstr ""

#: part/models.py:3866
msgid "Valid choices for this parameter (comma-separated)"
msgstr ""

#: part/models.py:3877
msgid "Selection list for this parameter"
msgstr ""

#: part/models.py:3913
msgid "Part Parameter"
msgstr ""

#: part/models.py:3939
msgid "Parameter cannot be modified - part is locked"
msgstr ""

#: part/models.py:3977
msgid "Invalid choice for parameter value"
msgstr ""

#: part/models.py:4028
msgid "Parent Part"
msgstr ""

#: part/models.py:4036 part/models.py:4144 part/models.py:4145
msgid "Parameter Template"
msgstr ""

#: part/models.py:4042
msgid "Parameter Value"
msgstr ""

#: part/models.py:4092
msgid "Part Category Parameter Template"
msgstr ""

#: part/models.py:4151
msgid "Default Value"
msgstr ""

#: part/models.py:4152
msgid "Default Parameter Value"
msgstr ""

#: part/models.py:4283
msgid "BOM item cannot be modified - assembly is locked"
msgstr "Položku kusovníku nelze změnit - sestava je uzamčena"

#: part/models.py:4290
msgid "BOM item cannot be modified - variant assembly is locked"
msgstr ""

#: part/models.py:4300
msgid "Select parent part"
msgstr "Vyberte nadřazený díl"

#: part/models.py:4310
msgid "Sub part"
msgstr ""

#: part/models.py:4311
msgid "Select part to be used in BOM"
msgstr ""

#: part/models.py:4322
msgid "BOM quantity for this BOM item"
msgstr ""

#: part/models.py:4328
msgid "This BOM item is optional"
msgstr ""

#: part/models.py:4334
msgid "This BOM item is consumable (it is not tracked in build orders)"
msgstr ""

#: part/models.py:4341
msgid "Overage"
msgstr ""

#: part/models.py:4342
msgid "Estimated build wastage quantity (absolute or percentage)"
msgstr ""

#: part/models.py:4349
msgid "BOM item reference"
msgstr "Reference položky kusovníku"

#: part/models.py:4357
msgid "BOM item notes"
msgstr "Poznámky k položce kusovníku"

#: part/models.py:4363
msgid "Checksum"
msgstr ""

#: part/models.py:4364
msgid "BOM line checksum"
msgstr ""

#: part/models.py:4369
msgid "Validated"
msgstr "Schváleno"

#: part/models.py:4370
msgid "This BOM item has been validated"
msgstr "Tato položka kusovníku ještě nebyla schválena"

#: part/models.py:4375
msgid "Gets inherited"
msgstr ""

#: part/models.py:4376
msgid "This BOM item is inherited by BOMs for variant parts"
msgstr ""

#: part/models.py:4382
msgid "Stock items for variant parts can be used for this BOM item"
msgstr ""

#: part/models.py:4467 stock/models.py:848
msgid "Quantity must be integer value for trackable parts"
msgstr ""

#: part/models.py:4477 part/models.py:4479
msgid "Sub part must be specified"
msgstr ""

#: part/models.py:4624
msgid "BOM Item Substitute"
msgstr ""

#: part/models.py:4645
msgid "Substitute part cannot be the same as the master part"
msgstr ""

#: part/models.py:4658
msgid "Parent BOM item"
msgstr ""

#: part/models.py:4666
msgid "Substitute part"
msgstr ""

#: part/models.py:4682
msgid "Part 1"
msgstr ""

#: part/models.py:4690
msgid "Part 2"
msgstr ""

#: part/models.py:4691
msgid "Select Related Part"
msgstr ""

#: part/models.py:4698
msgid "Note for this relationship"
msgstr ""

#: part/models.py:4717
msgid "Part relationship cannot be created between a part and itself"
msgstr ""

#: part/models.py:4722
msgid "Duplicate relationship already exists"
msgstr ""

#: part/serializers.py:125
msgid "Parent Category"
msgstr ""

#: part/serializers.py:126
msgid "Parent part category"
msgstr ""

#: part/serializers.py:134 part/serializers.py:168
msgid "Subcategories"
msgstr "Podkategorie"

#: part/serializers.py:207
msgid "Results"
msgstr "Výsledky"

#: part/serializers.py:208
msgid "Number of results recorded against this template"
msgstr ""

#: part/serializers.py:235 part/serializers.py:253 stock/serializers.py:685
msgid "Purchase currency of this stock item"
msgstr "Nákupní měna této skladové položky"

#: part/serializers.py:278
msgid "Speculative Quantity"
msgstr ""

#: part/serializers.py:287
msgid "Model ID"
msgstr ""

#: part/serializers.py:313
msgid "File is not an image"
msgstr ""

#: part/serializers.py:344
msgid "Number of parts using this template"
msgstr ""

#: part/serializers.py:489
msgid "Original Part"
msgstr "Původní díl"

#: part/serializers.py:490
msgid "Select original part to duplicate"
msgstr "Vyberte původní díl, který má být duplikován"

#: part/serializers.py:495
msgid "Copy Image"
msgstr ""

#: part/serializers.py:496
msgid "Copy image from original part"
msgstr ""

#: part/serializers.py:502
msgid "Copy BOM"
msgstr "Kopírovat kusovník"

#: part/serializers.py:503
msgid "Copy bill of materials from original part"
msgstr ""

#: part/serializers.py:509
msgid "Copy Parameters"
msgstr ""

#: part/serializers.py:510
msgid "Copy parameter data from original part"
msgstr ""

#: part/serializers.py:516
msgid "Copy Notes"
msgstr ""

#: part/serializers.py:517
msgid "Copy notes from original part"
msgstr ""

#: part/serializers.py:535
msgid "Initial Stock Quantity"
msgstr ""

#: part/serializers.py:537
msgid "Specify initial stock quantity for this Part. If quantity is zero, no stock is added."
msgstr ""

#: part/serializers.py:544
msgid "Initial Stock Location"
msgstr ""

#: part/serializers.py:545
msgid "Specify initial stock location for this Part"
msgstr ""

#: part/serializers.py:562
msgid "Select supplier (or leave blank to skip)"
msgstr ""

#: part/serializers.py:578
msgid "Select manufacturer (or leave blank to skip)"
msgstr ""

#: part/serializers.py:588
msgid "Manufacturer part number"
msgstr ""

#: part/serializers.py:595
msgid "Selected company is not a valid supplier"
msgstr ""

#: part/serializers.py:604
msgid "Selected company is not a valid manufacturer"
msgstr ""

#: part/serializers.py:615
msgid "Manufacturer part matching this MPN already exists"
msgstr ""

#: part/serializers.py:622
msgid "Supplier part matching this SKU already exists"
msgstr ""

#: part/serializers.py:911 part/stocktake.py:222
msgid "Category Name"
msgstr "Název kategorie"

#: part/serializers.py:937
msgid "Building"
msgstr ""

#: part/serializers.py:952 part/stocktake.py:223 stock/serializers.py:1084
#: stock/serializers.py:1260 users/ruleset.py:36
msgid "Stock Items"
msgstr "Skladové položky"

#: part/serializers.py:955
msgid "Revisions"
msgstr ""

#: part/serializers.py:958
msgid "Suppliers"
msgstr "Dodavatelé"

#: part/serializers.py:961 templates/email/low_stock_notification.html:16
#: templates/email/part_event_notification.html:17
msgid "Total Stock"
msgstr ""

#: part/serializers.py:967
msgid "Unallocated Stock"
msgstr ""

#: part/serializers.py:973
msgid "Variant Stock"
msgstr ""

#: part/serializers.py:1006
msgid "Duplicate Part"
msgstr ""

#: part/serializers.py:1007
msgid "Copy initial data from another Part"
msgstr ""

#: part/serializers.py:1013
msgid "Initial Stock"
msgstr ""

#: part/serializers.py:1014
msgid "Create Part with initial stock quantity"
msgstr ""

#: part/serializers.py:1020
msgid "Supplier Information"
msgstr ""

#: part/serializers.py:1021
msgid "Add initial supplier information for this part"
msgstr ""

#: part/serializers.py:1029
msgid "Copy Category Parameters"
msgstr ""

#: part/serializers.py:1030
msgid "Copy parameter templates from selected part category"
msgstr ""

#: part/serializers.py:1035
msgid "Existing Image"
msgstr ""

#: part/serializers.py:1036
msgid "Filename of an existing part image"
msgstr ""

#: part/serializers.py:1053
msgid "Image file does not exist"
msgstr ""

#: part/serializers.py:1257
msgid "Limit stocktake report to a particular part, and any variant parts"
msgstr ""

#: part/serializers.py:1267
msgid "Limit stocktake report to a particular part category, and any child categories"
msgstr ""

#: part/serializers.py:1277
msgid "Limit stocktake report to a particular stock location, and any child locations"
msgstr ""

#: part/serializers.py:1283
msgid "Exclude External Stock"
msgstr ""

#: part/serializers.py:1284
msgid "Exclude stock items in external locations"
msgstr "Vyloučit skladové položky v externích umístěních"

#: part/serializers.py:1289
msgid "Generate Report"
msgstr ""

#: part/serializers.py:1290
msgid "Generate report file containing calculated stocktake data"
msgstr ""

#: part/serializers.py:1295
msgid "Update Parts"
msgstr "Aktualizovat díly"

#: part/serializers.py:1296
msgid "Update specified parts with calculated stocktake data"
msgstr ""

#: part/serializers.py:1304
msgid "Stocktake functionality is not enabled"
msgstr ""

#: part/serializers.py:1309
msgid "Background worker check failed"
msgstr "Kontrola procesů na pozadí se nezdařila"

#: part/serializers.py:1425
msgid "Minimum Price"
msgstr ""

#: part/serializers.py:1426
msgid "Override calculated value for minimum price"
msgstr ""

#: part/serializers.py:1433
msgid "Minimum price currency"
msgstr ""

#: part/serializers.py:1440
msgid "Maximum Price"
msgstr ""

#: part/serializers.py:1441
msgid "Override calculated value for maximum price"
msgstr ""

#: part/serializers.py:1448
msgid "Maximum price currency"
msgstr ""

#: part/serializers.py:1477
msgid "Update"
msgstr ""

#: part/serializers.py:1478
msgid "Update pricing for this part"
msgstr "Aktualizovat cenu pro díl"

#: part/serializers.py:1501
#, python-brace-format
msgid "Could not convert from provided currencies to {default_currency}"
msgstr ""

#: part/serializers.py:1508
msgid "Minimum price must not be greater than maximum price"
msgstr ""

#: part/serializers.py:1511
msgid "Maximum price must not be less than minimum price"
msgstr ""

#: part/serializers.py:1664
msgid "Select the parent assembly"
msgstr "Vybrat nadřazenou sestavu"

#: part/serializers.py:1678
msgid "Select the component part"
msgstr ""

#: part/serializers.py:1698
msgid "Can Build"
msgstr ""

#: part/serializers.py:1925
msgid "Select part to copy BOM from"
msgstr ""

#: part/serializers.py:1933
msgid "Remove Existing Data"
msgstr ""

#: part/serializers.py:1934
msgid "Remove existing BOM items before copying"
msgstr ""

#: part/serializers.py:1939
msgid "Include Inherited"
msgstr ""

#: part/serializers.py:1940
msgid "Include BOM items which are inherited from templated parts"
msgstr ""

#: part/serializers.py:1945
msgid "Skip Invalid Rows"
msgstr ""

#: part/serializers.py:1946
msgid "Enable this option to skip invalid rows"
msgstr ""

#: part/serializers.py:1951
msgid "Copy Substitute Parts"
msgstr ""

#: part/serializers.py:1952
msgid "Copy substitute parts when duplicate BOM items"
msgstr ""

#: part/stocktake.py:218
msgid "Part ID"
msgstr "ID dílu"

#: part/stocktake.py:220
msgid "Part Description"
msgstr "Popis dílu"

#: part/stocktake.py:221
msgid "Category ID"
msgstr "ID kategorie"

#: part/stocktake.py:224
msgid "Total Quantity"
msgstr ""

#: part/stocktake.py:225
msgid "Total Cost Min"
msgstr ""

#: part/stocktake.py:226
msgid "Total Cost Max"
msgstr ""

#: part/stocktake.py:284
msgid "Stocktake Report Available"
msgstr ""

#: part/stocktake.py:285
msgid "A new stocktake report is available for download"
msgstr ""

#: part/tasks.py:38
msgid "Low stock notification"
msgstr ""

#: part/tasks.py:40
#, python-brace-format
msgid "The available stock for {part.name} has fallen below the configured minimum level"
msgstr ""

#: plugin/api.py:78
msgid "Builtin"
msgstr ""

#: plugin/api.py:92
msgid "Mandatory"
msgstr ""

#: plugin/api.py:103
msgid "Sample"
msgstr ""

#: plugin/api.py:117 plugin/models.py:159
msgid "Installed"
msgstr "Nainstalováno"

#: plugin/api.py:184
msgid "Plugin cannot be deleted as it is currently active"
msgstr ""

#: plugin/base/action/api.py:34
msgid "No action specified"
msgstr "Činnost nebyla specifikována"

#: plugin/base/action/api.py:46
msgid "No matching action found"
msgstr "Nebyla nalezena odpovídající činnost"

#: plugin/base/barcodes/api.py:211
msgid "No match found for barcode data"
msgstr "Pro data čárového kódu nebyla nalezena shoda"

#: plugin/base/barcodes/api.py:215
msgid "Match found for barcode data"
msgstr "Pro data čárového kódu byla nalezena shoda"

#: plugin/base/barcodes/api.py:253 plugin/base/barcodes/serializers.py:77
msgid "Model is not supported"
msgstr ""

#: plugin/base/barcodes/api.py:258
msgid "Model instance not found"
msgstr ""

#: plugin/base/barcodes/api.py:287
msgid "Barcode matches existing item"
msgstr ""

#: plugin/base/barcodes/api.py:418
msgid "No matching part data found"
msgstr ""

#: plugin/base/barcodes/api.py:434
msgid "No matching supplier parts found"
msgstr ""

#: plugin/base/barcodes/api.py:437
msgid "Multiple matching supplier parts found"
msgstr ""

#: plugin/base/barcodes/api.py:450 plugin/base/barcodes/api.py:677
msgid "No matching plugin found for barcode data"
msgstr ""

#: plugin/base/barcodes/api.py:460
msgid "Matched supplier part"
msgstr ""

#: plugin/base/barcodes/api.py:528
msgid "Item has already been received"
msgstr ""

#: plugin/base/barcodes/api.py:576
msgid "No plugin match for supplier barcode"
msgstr ""

#: plugin/base/barcodes/api.py:625
msgid "Multiple matching line items found"
msgstr ""

#: plugin/base/barcodes/api.py:628
msgid "No matching line item found"
msgstr ""

#: plugin/base/barcodes/api.py:674
msgid "No sales order provided"
msgstr ""

#: plugin/base/barcodes/api.py:683
msgid "Barcode does not match an existing stock item"
msgstr "Čárový kód neodpovídá žádné existující skladové položce"

#: plugin/base/barcodes/api.py:699
msgid "Stock item does not match line item"
msgstr "Skladová položka se neshoduje s řádkovou položkou"

#: plugin/base/barcodes/api.py:729
msgid "Insufficient stock available"
msgstr ""

#: plugin/base/barcodes/api.py:742
msgid "Stock item allocated to sales order"
msgstr "Skladová položka byla přidělena prodejní objednávce"

#: plugin/base/barcodes/api.py:745
msgid "Not enough information"
msgstr ""

#: plugin/base/barcodes/mixins.py:308
#: plugin/builtin/barcodes/inventree_barcode.py:101
msgid "Found matching item"
msgstr ""

#: plugin/base/barcodes/mixins.py:374
msgid "Supplier part does not match line item"
msgstr ""

#: plugin/base/barcodes/mixins.py:377
msgid "Line item is already completed"
msgstr ""

#: plugin/base/barcodes/mixins.py:414
msgid "Further information required to receive line item"
msgstr ""

#: plugin/base/barcodes/mixins.py:422
msgid "Received purchase order line item"
msgstr ""

#: plugin/base/barcodes/mixins.py:429
msgid "Failed to receive line item"
msgstr ""

#: plugin/base/barcodes/serializers.py:53
msgid "Scanned barcode data"
msgstr ""

#: plugin/base/barcodes/serializers.py:62
msgid "Model name to generate barcode for"
msgstr ""

#: plugin/base/barcodes/serializers.py:67
msgid "Primary key of model object to generate barcode for"
msgstr ""

#: plugin/base/barcodes/serializers.py:137
msgid "Purchase Order to allocate items against"
msgstr ""

#: plugin/base/barcodes/serializers.py:143
msgid "Purchase order is not open"
msgstr ""

#: plugin/base/barcodes/serializers.py:161
msgid "Supplier to receive items from"
msgstr ""

#: plugin/base/barcodes/serializers.py:168
msgid "PurchaseOrder to receive items against"
msgstr ""

#: plugin/base/barcodes/serializers.py:174
msgid "Purchase order has not been placed"
msgstr ""

#: plugin/base/barcodes/serializers.py:182
msgid "Location to receive items into"
msgstr ""

#: plugin/base/barcodes/serializers.py:188
msgid "Cannot select a structural location"
msgstr ""

#: plugin/base/barcodes/serializers.py:196
msgid "Purchase order line item to receive items against"
msgstr ""

#: plugin/base/barcodes/serializers.py:202
msgid "Automatically allocate stock items to the purchase order"
msgstr ""

#: plugin/base/barcodes/serializers.py:215
msgid "Sales Order to allocate items against"
msgstr ""

#: plugin/base/barcodes/serializers.py:221
msgid "Sales order is not open"
msgstr ""

#: plugin/base/barcodes/serializers.py:229
msgid "Sales order line item to allocate items against"
msgstr ""

#: plugin/base/barcodes/serializers.py:236
msgid "Sales order shipment to allocate items against"
msgstr ""

#: plugin/base/barcodes/serializers.py:242
msgid "Shipment has already been delivered"
msgstr ""

#: plugin/base/barcodes/serializers.py:247
msgid "Quantity to allocate"
msgstr ""

#: plugin/base/label/label.py:39
msgid "Label printing failed"
msgstr ""

#: plugin/base/label/mixins.py:55
msgid "Error rendering label to PDF"
msgstr ""

#: plugin/base/label/mixins.py:69
msgid "Error rendering label to HTML"
msgstr ""

#: plugin/base/label/mixins.py:146
msgid "No items provided to print"
msgstr ""

#: plugin/base/ui/serializers.py:30
msgid "Plugin Name"
msgstr ""

#: plugin/base/ui/serializers.py:34
msgid "Feature Type"
msgstr ""

#: plugin/base/ui/serializers.py:39
msgid "Feature Label"
msgstr ""

#: plugin/base/ui/serializers.py:44
msgid "Feature Title"
msgstr ""

#: plugin/base/ui/serializers.py:49
msgid "Feature Description"
msgstr ""

#: plugin/base/ui/serializers.py:54
msgid "Feature Icon"
msgstr ""

#: plugin/base/ui/serializers.py:58
msgid "Feature Options"
msgstr ""

#: plugin/base/ui/serializers.py:61
msgid "Feature Context"
msgstr ""

#: plugin/base/ui/serializers.py:64
msgid "Feature Source (javascript)"
msgstr ""

#: plugin/builtin/barcodes/inventree_barcode.py:27
msgid "InvenTree Barcodes"
msgstr ""

#: plugin/builtin/barcodes/inventree_barcode.py:28
msgid "Provides native support for barcodes"
msgstr ""

#: plugin/builtin/barcodes/inventree_barcode.py:30
#: plugin/builtin/exporter/bom_exporter.py:61
#: plugin/builtin/exporter/inventree_exporter.py:17
#: plugin/builtin/exporter/part_parameter_exporter.py:36
#: plugin/builtin/integration/core_notifications.py:33
#: plugin/builtin/integration/currency_exchange.py:21
#: plugin/builtin/integration/part_notifications.py:25
#: plugin/builtin/labels/inventree_label.py:26
#: plugin/builtin/labels/inventree_machine.py:64
#: plugin/builtin/labels/label_sheet.py:71
#: plugin/builtin/suppliers/digikey.py:19 plugin/builtin/suppliers/lcsc.py:21
#: plugin/builtin/suppliers/mouser.py:19 plugin/builtin/suppliers/tme.py:21
msgid "InvenTree contributors"
msgstr ""

#: plugin/builtin/barcodes/inventree_barcode.py:34
msgid "Internal Barcode Format"
msgstr ""

#: plugin/builtin/barcodes/inventree_barcode.py:35
msgid "Select an internal barcode format"
msgstr ""

#: plugin/builtin/barcodes/inventree_barcode.py:37
msgid "JSON barcodes (human readable)"
msgstr ""

#: plugin/builtin/barcodes/inventree_barcode.py:38
msgid "Short barcodes (space optimized)"
msgstr ""

#: plugin/builtin/barcodes/inventree_barcode.py:43
msgid "Short Barcode Prefix"
msgstr ""

#: plugin/builtin/barcodes/inventree_barcode.py:45
msgid "Customize the prefix used for short barcodes, may be useful for environments with multiple InvenTree instances"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:17
msgid "Levels"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:18
msgid "Number of levels to export"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:23
#: plugin/builtin/exporter/part_parameter_exporter.py:17
msgid "Stock Data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:23
#: plugin/builtin/exporter/part_parameter_exporter.py:17
msgid "Include part stock data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:27
#: plugin/builtin/exporter/part_parameter_exporter.py:21
msgid "Pricing Data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:27
#: plugin/builtin/exporter/part_parameter_exporter.py:21
msgid "Include part pricing data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:31
msgid "Supplier Data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:31
msgid "Include supplier data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:36
msgid "Manufacturer Data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:37
msgid "Include manufacturer data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:42
msgid "Substitute Data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:43
msgid "Include substitute part data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:48
msgid "Parameter Data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:49
msgid "Include part parameter data"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:58
msgid "Multi-Level BOM Exporter"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:59
msgid "Provides support for exporting multi-level BOMs"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:96
msgid "BOM Level"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:102
#, python-brace-format
msgid "Substitute {n}"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:108
#, python-brace-format
msgid "Supplier {n}"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:109
#, python-brace-format
msgid "Supplier {n} SKU"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:110
#, python-brace-format
msgid "Supplier {n} MPN"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:116
#, python-brace-format
msgid "Manufacturer {n}"
msgstr ""

#: plugin/builtin/exporter/bom_exporter.py:117
#, python-brace-format
msgid "Manufacturer {n} MPN"
msgstr ""

#: plugin/builtin/exporter/inventree_exporter.py:14
msgid "InvenTree Generic Exporter"
msgstr ""

#: plugin/builtin/exporter/inventree_exporter.py:15
msgid "Provides support for exporting data from InvenTree"
msgstr ""

#: plugin/builtin/exporter/part_parameter_exporter.py:33
msgid "Part Parameter Exporter"
msgstr ""

#: plugin/builtin/exporter/part_parameter_exporter.py:34
msgid "Exporter for part parameter data"
msgstr ""

#: plugin/builtin/integration/core_notifications.py:32
msgid "InvenTree Notifications"
msgstr ""

#: plugin/builtin/integration/core_notifications.py:34
msgid "Integrated outgoing notification methods"
msgstr ""

#: plugin/builtin/integration/core_notifications.py:39
#: plugin/builtin/integration/core_notifications.py:78
msgid "Enable email notifications"
msgstr "Povolit e-mailová oznámení"

#: plugin/builtin/integration/core_notifications.py:40
#: plugin/builtin/integration/core_notifications.py:79
msgid "Allow sending of emails for event notifications"
msgstr ""

#: plugin/builtin/integration/core_notifications.py:45
msgid "Enable slack notifications"
msgstr ""

#: plugin/builtin/integration/core_notifications.py:47
msgid "Allow sending of slack channel messages for event notifications"
msgstr ""

#: plugin/builtin/integration/core_notifications.py:53
msgid "Slack incoming webhook url"
msgstr ""

#: plugin/builtin/integration/core_notifications.py:54
msgid "URL that is used to send messages to a slack channel"
msgstr ""

#: plugin/builtin/integration/core_notifications.py:164
msgid "Open link"
msgstr "Otevřít odkaz"

#: plugin/builtin/integration/currency_exchange.py:22
msgid "InvenTree Currency Exchange"
msgstr ""

#: plugin/builtin/integration/currency_exchange.py:23
msgid "Default currency exchange integration"
msgstr ""

#: plugin/builtin/integration/part_notifications.py:24
msgid "Part Notifications"
msgstr ""

#: plugin/builtin/integration/part_notifications.py:26
msgid "Notify users about part changes"
msgstr ""

#: plugin/builtin/integration/part_notifications.py:31
msgid "Send notifications"
msgstr ""

#: plugin/builtin/integration/part_notifications.py:32
msgid "Send notifications for part changes to subscribed users"
msgstr ""

#: plugin/builtin/integration/part_notifications.py:49
msgid "Changed part notification"
msgstr ""

#: plugin/builtin/integration/part_notifications.py:59
#, python-brace-format
msgid "The part `{part.name}` has been triggered with a `{part_action}` event"
msgstr ""

#: plugin/builtin/labels/inventree_label.py:23
msgid "InvenTree PDF label printer"
msgstr ""

#: plugin/builtin/labels/inventree_label.py:24
msgid "Provides native support for printing PDF labels"
msgstr ""

#: plugin/builtin/labels/inventree_label.py:32
#: plugin/builtin/labels/label_sheet.py:77
msgid "Debug mode"
msgstr ""

#: plugin/builtin/labels/inventree_label.py:33
#: plugin/builtin/labels/label_sheet.py:78
msgid "Enable debug mode - returns raw HTML instead of PDF"
msgstr ""

#: plugin/builtin/labels/inventree_machine.py:61
msgid "InvenTree machine label printer"
msgstr ""

#: plugin/builtin/labels/inventree_machine.py:62
msgid "Provides support for printing using a machine"
msgstr ""

#: plugin/builtin/labels/inventree_machine.py:164
msgid "last used"
msgstr ""

#: plugin/builtin/labels/inventree_machine.py:181
msgid "Options"
msgstr ""

#: plugin/builtin/labels/label_sheet.py:30
msgid "Page size for the label sheet"
msgstr ""

#: plugin/builtin/labels/label_sheet.py:35
msgid "Skip Labels"
msgstr "Přeskočit štítky"

#: plugin/builtin/labels/label_sheet.py:36
msgid "Skip this number of labels when printing label sheets"
msgstr ""

#: plugin/builtin/labels/label_sheet.py:42
msgid "Border"
msgstr ""

#: plugin/builtin/labels/label_sheet.py:43
msgid "Print a border around each label"
msgstr ""

#: plugin/builtin/labels/label_sheet.py:48 report/models.py:371
msgid "Landscape"
msgstr ""

#: plugin/builtin/labels/label_sheet.py:49
msgid "Print the label sheet in landscape mode"
msgstr ""

#: plugin/builtin/labels/label_sheet.py:54
msgid "Page Margin"
msgstr ""

#: plugin/builtin/labels/label_sheet.py:55
msgid "Margin around the page in mm"
msgstr ""

#: plugin/builtin/labels/label_sheet.py:68
msgid "InvenTree Label Sheet Printer"
msgstr ""

#: plugin/builtin/labels/label_sheet.py:69
msgid "Arrays multiple labels onto a single sheet"
msgstr ""

#: plugin/builtin/labels/label_sheet.py:121
msgid "Label is too large for page size"
msgstr "Štítek je příliš velký pro velikost stránky"

#: plugin/builtin/labels/label_sheet.py:160
msgid "No labels were generated"
msgstr "Nebyly vygenerovány žádné štítky"

#: plugin/builtin/suppliers/digikey.py:16
msgid "Supplier Integration - DigiKey"
msgstr "Integrace dodavatele - DigiKey"

#: plugin/builtin/suppliers/digikey.py:17
msgid "Provides support for scanning DigiKey barcodes"
msgstr ""

#: plugin/builtin/suppliers/digikey.py:26
msgid "The Supplier which acts as 'DigiKey'"
msgstr ""

#: plugin/builtin/suppliers/lcsc.py:18
msgid "Supplier Integration - LCSC"
msgstr "Integrace dodavatele - LCSC"

#: plugin/builtin/suppliers/lcsc.py:19
msgid "Provides support for scanning LCSC barcodes"
msgstr ""

#: plugin/builtin/suppliers/lcsc.py:27
msgid "The Supplier which acts as 'LCSC'"
msgstr ""

#: plugin/builtin/suppliers/mouser.py:16
msgid "Supplier Integration - Mouser"
msgstr "Integrace dodavatele - Mouser"

#: plugin/builtin/suppliers/mouser.py:17
msgid "Provides support for scanning Mouser barcodes"
msgstr ""

#: plugin/builtin/suppliers/mouser.py:25
msgid "The Supplier which acts as 'Mouser'"
msgstr ""

#: plugin/builtin/suppliers/tme.py:18
msgid "Supplier Integration - TME"
msgstr "Integrace dodavatele - TME"

#: plugin/builtin/suppliers/tme.py:19
msgid "Provides support for scanning TME barcodes"
msgstr ""

#: plugin/builtin/suppliers/tme.py:27
msgid "The Supplier which acts as 'TME'"
msgstr ""

#: plugin/installer.py:229 plugin/installer.py:307
msgid "Only staff users can administer plugins"
msgstr ""

#: plugin/installer.py:232
msgid "Plugin installation is disabled"
msgstr ""

#: plugin/installer.py:269
msgid "Installed plugin successfully"
msgstr ""

#: plugin/installer.py:274
#, python-brace-format
msgid "Installed plugin into {path}"
msgstr ""

#: plugin/installer.py:298
msgid "Plugin was not found in registry"
msgstr ""

#: plugin/installer.py:301
msgid "Plugin is not a packaged plugin"
msgstr ""

#: plugin/installer.py:304
msgid "Plugin package name not found"
msgstr ""

#: plugin/installer.py:324
msgid "Plugin uninstalling is disabled"
msgstr ""

#: plugin/installer.py:328
msgid "Plugin cannot be uninstalled as it is currently active"
msgstr ""

#: plugin/installer.py:332
msgid "Plugin is not installed"
msgstr ""

#: plugin/installer.py:348
msgid "Plugin installation not found"
msgstr ""

#: plugin/installer.py:363
msgid "Uninstalled plugin successfully"
msgstr ""

#: plugin/models.py:38
msgid "Plugin Configuration"
msgstr ""

#: plugin/models.py:39
msgid "Plugin Configurations"
msgstr ""

#: plugin/models.py:46
msgid "Key of plugin"
msgstr ""

#: plugin/models.py:54
msgid "PluginName of the plugin"
msgstr ""

#: plugin/models.py:61 plugin/serializers.py:119
msgid "Package Name"
msgstr ""

#: plugin/models.py:63
msgid "Name of the installed package, if the plugin was installed via PIP"
msgstr ""

#: plugin/models.py:68
msgid "Is the plugin active"
msgstr ""

#: plugin/models.py:168
msgid "Sample plugin"
msgstr ""

#: plugin/models.py:176
msgid "Builtin Plugin"
msgstr ""

#: plugin/models.py:184
msgid "Mandatory Plugin"
msgstr ""

#: plugin/models.py:189
msgid "Package Plugin"
msgstr ""

#: plugin/models.py:268
msgid "Plugin"
msgstr "Plugin"

#: plugin/models.py:315
msgid "Method"
msgstr ""

#: plugin/plugin.py:312
msgid "No author found"
msgstr ""

#: plugin/registry.py:611
#, python-brace-format
msgid "Plugin '{p}' is not compatible with the current InvenTree version {v}"
msgstr "Plugin '{p}' není kompatibilní s aktuální verzí InvenTree {v}"

#: plugin/registry.py:614
#, python-brace-format
msgid "Plugin requires at least version {v}"
msgstr "Plugin vyžaduje alespoň verzi {v}"

#: plugin/registry.py:616
#, python-brace-format
msgid "Plugin requires at most version {v}"
msgstr "Plugin vyžaduje nanejvýš verzi {v}"

#: plugin/samples/integration/sample.py:52
msgid "Enable PO"
msgstr ""

#: plugin/samples/integration/sample.py:53
msgid "Enable PO functionality in InvenTree interface"
msgstr ""

#: plugin/samples/integration/sample.py:58
msgid "API Key"
msgstr "API klíč"

#: plugin/samples/integration/sample.py:59
msgid "Key required for accessing external API"
msgstr ""

#: plugin/samples/integration/sample.py:63
msgid "Numerical"
msgstr ""

#: plugin/samples/integration/sample.py:64
msgid "A numerical setting"
msgstr ""

#: plugin/samples/integration/sample.py:69
msgid "Choice Setting"
msgstr ""

#: plugin/samples/integration/sample.py:70
msgid "A setting with multiple choices"
msgstr ""

#: plugin/samples/integration/sample_currency_exchange.py:15
msgid "Sample currency exchange plugin"
msgstr ""

#: plugin/samples/integration/sample_currency_exchange.py:18
msgid "InvenTree Contributors"
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:27
msgid "Enable Part Panels"
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:28
msgid "Enable custom panels for Part views"
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:33
msgid "Enable Purchase Order Panels"
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:34
msgid "Enable custom panels for Purchase Order views"
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:39
msgid "Enable Broken Panels"
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:40
msgid "Enable broken panels for testing"
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:45
msgid "Enable Dynamic Panel"
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:46
msgid "Enable dynamic panels for testing"
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:98
msgid "Part Panel"
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:131
msgid "Broken Dashboard Item"
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:133
msgid "This is a broken dashboard item - it will not render!"
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:139
msgid "Sample Dashboard Item"
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:141
msgid "This is a sample dashboard item. It renders a simple string of HTML content."
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:147
msgid "Context Dashboard Item"
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:161
msgid "Admin Dashboard Item"
msgstr ""

#: plugin/samples/integration/user_interface_sample.py:162
msgid "This is an admin-only dashboard item."
msgstr ""

#: plugin/serializers.py:86
msgid "Source File"
msgstr "Zdrojový soubor"

#: plugin/serializers.py:87
msgid "Path to the source file for admin integration"
msgstr ""

#: plugin/serializers.py:94
msgid "Optional context data for the admin integration"
msgstr ""

#: plugin/serializers.py:110
msgid "Source URL"
msgstr "Zdrojová adresa URL"

#: plugin/serializers.py:112
msgid "Source for the package - this can be a custom registry or a VCS path"
msgstr ""

#: plugin/serializers.py:121
msgid "Name for the Plugin Package - can also contain a version indicator"
msgstr ""

#: plugin/serializers.py:128
msgid "Version"
msgstr "Verze"

#: plugin/serializers.py:130
msgid "Version specifier for the plugin. Leave blank for latest version."
msgstr ""

#: plugin/serializers.py:135
msgid "Confirm plugin installation"
msgstr "Potvrdit instalaci pluginu"

#: plugin/serializers.py:137
msgid "This will install this plugin now into the current instance. The instance will go into maintenance."
msgstr ""

#: plugin/serializers.py:150
msgid "Installation not confirmed"
msgstr "Instalace nebyla potvrzena"

#: plugin/serializers.py:152
msgid "Either packagename of URL must be provided"
msgstr ""

#: plugin/serializers.py:188
msgid "Full reload"
msgstr ""

#: plugin/serializers.py:189
msgid "Perform a full reload of the plugin registry"
msgstr ""

#: plugin/serializers.py:195
msgid "Force reload"
msgstr ""

#: plugin/serializers.py:197
msgid "Force a reload of the plugin registry, even if it is already loaded"
msgstr ""

#: plugin/serializers.py:204
msgid "Collect plugins"
msgstr ""

#: plugin/serializers.py:205
msgid "Collect plugins and add them to the registry"
msgstr ""

#: plugin/serializers.py:232
msgid "Activate Plugin"
msgstr "Aktivovat plugin"

#: plugin/serializers.py:233
msgid "Activate this plugin"
msgstr "Aktivovat tento plugin"

#: plugin/serializers.py:253
msgid "Delete configuration"
msgstr ""

#: plugin/serializers.py:254
msgid "Delete the plugin configuration from the database"
msgstr "Odstranit konfiguraci pluginu z databáze"

#: report/api.py:44 report/serializers.py:98 report/serializers.py:148
msgid "Items"
msgstr "Položky"

#: report/api.py:121
msgid "Plugin not found"
msgstr "Plugin nebyl nalezen"

#: report/api.py:123
msgid "Plugin is not active"
msgstr "Plugin není aktivní"

#: report/api.py:125
msgid "Plugin does not support label printing"
msgstr "Plugin nepodporuje tisk štítků"

#: report/api.py:173
msgid "Invalid label dimensions"
msgstr "Neplatné rozměry štítku"

#: report/api.py:188 report/api.py:267
msgid "No valid items provided to template"
msgstr ""

#: report/helpers.py:43
msgid "A4"
msgstr "A4"

#: report/helpers.py:44
msgid "A3"
msgstr "A3"

#: report/helpers.py:45
msgid "Legal"
msgstr "Legal"

#: report/helpers.py:46
msgid "Letter"
msgstr "Letter"

#: report/models.py:117
msgid "Template file with this name already exists"
msgstr ""

#: report/models.py:204
msgid "Template name"
msgstr ""

#: report/models.py:210
msgid "Template description"
msgstr ""

#: report/models.py:216
msgid "Revision number (auto-increments)"
msgstr ""

#: report/models.py:222
msgid "Attach to Model on Print"
msgstr ""

#: report/models.py:224
msgid "Save report output as an attachment against linked model instance when printing"
msgstr ""

#: report/models.py:265
msgid "Filename Pattern"
msgstr ""

#: report/models.py:266
msgid "Pattern for generating filenames"
msgstr ""

#: report/models.py:271
msgid "Template is enabled"
msgstr ""

#: report/models.py:278
msgid "Target model type for template"
msgstr ""

#: report/models.py:298
msgid "Filters"
msgstr "Filtry"

#: report/models.py:299
msgid "Template query filters (comma-separated list of key=value pairs)"
msgstr ""

#: report/models.py:358 report/models.py:564
msgid "Template file"
msgstr ""

#: report/models.py:366
msgid "Page size for PDF reports"
msgstr ""

#: report/models.py:372
msgid "Render report in landscape orientation"
msgstr ""

#: report/models.py:486
#, python-brace-format
msgid "Report generated from template {self.name}"
msgstr ""

#: report/models.py:511
msgid "Error generating report"
msgstr ""

#: report/models.py:570
msgid "Width [mm]"
msgstr "Šířka [mm]"

#: report/models.py:571
msgid "Label width, specified in mm"
msgstr "Šířka štítku zadaná v mm"

#: report/models.py:577
msgid "Height [mm]"
msgstr "Výška [mm]"

#: report/models.py:578
msgid "Label height, specified in mm"
msgstr "Výška štítku uvedená v mm"

#: report/models.py:688
msgid "Error printing labels"
msgstr ""

#: report/models.py:707
msgid "Snippet"
msgstr ""

#: report/models.py:708
msgid "Report snippet file"
msgstr ""

#: report/models.py:715
msgid "Snippet file description"
msgstr ""

#: report/models.py:733
msgid "Asset"
msgstr ""

#: report/models.py:734
msgid "Report asset file"
msgstr ""

#: report/models.py:741
msgid "Asset file description"
msgstr ""

#: report/serializers.py:91
msgid "Select report template"
msgstr ""

#: report/serializers.py:99 report/serializers.py:149
msgid "List of item primary keys to include in the report"
msgstr ""

#: report/serializers.py:132
msgid "Select label template"
msgstr ""

#: report/serializers.py:140
msgid "Printing Plugin"
msgstr ""

#: report/serializers.py:141
msgid "Select plugin to use for label printing"
msgstr ""

#: report/templates/label/part_label.html:31
#: report/templates/label/stockitem_qr.html:21
#: report/templates/label/stocklocation_qr.html:20
msgid "QR Code"
msgstr "QR kód"

#: report/templates/label/part_label_code128.html:31
#: report/templates/label/stocklocation_qr_and_text.html:31
msgid "QR code"
msgstr "QR kód"

#: report/templates/report/inventree_bill_of_materials_report.html:100
msgid "Bill of Materials"
msgstr "Kusovník"

#: report/templates/report/inventree_bill_of_materials_report.html:133
msgid "Materials needed"
msgstr ""

#: report/templates/report/inventree_build_order_report.html:98
#: report/templates/report/inventree_purchase_order_report.html:40
#: report/templates/report/inventree_sales_order_report.html:40
#: report/templates/report/inventree_sales_order_shipment_report.html:37
#: report/templates/report/inventree_test_report.html:84
#: report/templates/report/inventree_test_report.html:162
msgid "Part image"
msgstr "Obrázek dílu"

#: report/templates/report/inventree_build_order_report.html:121
msgid "Issued"
msgstr ""

#: report/templates/report/inventree_build_order_report.html:146
msgid "Required For"
msgstr ""

#: report/templates/report/inventree_purchase_order_report.html:15
msgid "Supplier was deleted"
msgstr ""

#: report/templates/report/inventree_purchase_order_report.html:30
#: report/templates/report/inventree_sales_order_report.html:30
msgid "Unit Price"
msgstr "Jednotková cena"

#: report/templates/report/inventree_purchase_order_report.html:55
#: report/templates/report/inventree_return_order_report.html:48
#: report/templates/report/inventree_sales_order_report.html:55
msgid "Extra Line Items"
msgstr "Řádkové položky navíc"

#: report/templates/report/inventree_purchase_order_report.html:72
#: report/templates/report/inventree_sales_order_report.html:72
msgid "Total"
msgstr "Celkem"

#: report/templates/report/inventree_return_order_report.html:25
#: report/templates/report/inventree_sales_order_shipment_report.html:45
#: report/templates/report/inventree_test_report.html:88 stock/models.py:1006
#: stock/serializers.py:163
msgid "Serial Number"
msgstr "Sériové číslo"

#: report/templates/report/inventree_sales_order_shipment_report.html:23
msgid "Allocations"
msgstr ""

#: report/templates/report/inventree_sales_order_shipment_report.html:47
msgid "Batch"
msgstr "Šarže"

#: report/templates/report/inventree_stock_location_report.html:97
msgid "Stock location items"
msgstr "Položky skladového umístění"

#: report/templates/report/inventree_test_report.html:21
msgid "Stock Item Test Report"
msgstr "Report o testu skladové položky"

#: report/templates/report/inventree_test_report.html:97
msgid "Test Results"
msgstr "Výsledky testu"

#: report/templates/report/inventree_test_report.html:102
msgid "Test"
msgstr "Test"

#: report/templates/report/inventree_test_report.html:129
msgid "Pass"
msgstr "Vyhovuje"

#: report/templates/report/inventree_test_report.html:131
msgid "Fail"
msgstr "Nevyhovuje"

#: report/templates/report/inventree_test_report.html:138
msgid "No result (required)"
msgstr ""

#: report/templates/report/inventree_test_report.html:140
msgid "No result"
msgstr ""

#: report/templates/report/inventree_test_report.html:153
#: stock/serializers.py:668
msgid "Installed Items"
msgstr "Instalované položky"

#: report/templates/report/inventree_test_report.html:167
msgid "Serial"
msgstr ""

#: report/templatetags/report.py:144
msgid "Asset file does not exist"
msgstr ""

#: report/templatetags/report.py:201 report/templatetags/report.py:277
msgid "Image file not found"
msgstr ""

#: report/templatetags/report.py:302
msgid "part_image tag requires a Part instance"
msgstr ""

#: report/templatetags/report.py:349
msgid "company_image tag requires a Company instance"
msgstr ""

#: stock/api.py:255
msgid "Filter by location depth"
msgstr ""

#: stock/api.py:275
msgid "Filter by top-level locations"
msgstr ""

#: stock/api.py:290
msgid "Include sub-locations in filtered results"
msgstr ""

#: stock/api.py:311 stock/serializers.py:1256
msgid "Parent Location"
msgstr "Nadřazená místo"

#: stock/api.py:312
msgid "Filter by parent location"
msgstr ""

#: stock/api.py:554
msgid "Part name (case insensitive)"
msgstr ""

#: stock/api.py:560
msgid "Part name contains (case insensitive)"
msgstr ""

#: stock/api.py:566
msgid "Part name (regex)"
msgstr ""

#: stock/api.py:571
msgid "Part IPN (case insensitive)"
msgstr ""

#: stock/api.py:577
msgid "Part IPN contains (case insensitive)"
msgstr ""

#: stock/api.py:583
msgid "Part IPN (regex)"
msgstr ""

#: stock/api.py:595
msgid "Minimum stock"
msgstr ""

#: stock/api.py:599
msgid "Maximum stock"
msgstr ""

#: stock/api.py:602
msgid "Status Code"
msgstr ""

#: stock/api.py:642
msgid "External Location"
msgstr "Externí umístění"

#: stock/api.py:741
msgid "Consumed by Build Order"
msgstr ""

#: stock/api.py:751
msgid "Installed in other stock item"
msgstr ""

#: stock/api.py:840
msgid "Part Tree"
msgstr "Strom dílů"

#: stock/api.py:862
msgid "Updated before"
msgstr ""

#: stock/api.py:866
msgid "Updated after"
msgstr ""

#: stock/api.py:870
msgid "Stocktake Before"
msgstr ""

#: stock/api.py:874
msgid "Stocktake After"
msgstr ""

#: stock/api.py:879
msgid "Expiry date before"
msgstr ""

#: stock/api.py:883
msgid "Expiry date after"
msgstr ""

#: stock/api.py:886 stock/serializers.py:673
msgid "Stale"
msgstr ""

#: stock/api.py:987
msgid "Quantity is required"
msgstr "Množství je povinné"

#: stock/api.py:992
msgid "Valid part must be supplied"
msgstr ""

#: stock/api.py:1019
msgid "The given supplier part does not exist"
msgstr ""

#: stock/api.py:1029
msgid "The supplier part has a pack size defined, but flag use_pack_size not set"
msgstr ""

#: stock/api.py:1056
msgid "Serial numbers cannot be supplied for a non-trackable part"
msgstr ""

#: stock/models.py:70
msgid "Stock Location type"
msgstr "Typ skladového umístění"

#: stock/models.py:71
msgid "Stock Location types"
msgstr "Typy skladových umístění"

#: stock/models.py:97
msgid "Default icon for all locations that have no icon set (optional)"
msgstr ""

#: stock/models.py:155 stock/models.py:968
msgid "Stock Location"
msgstr "Skladové umístění"

#: stock/models.py:156 users/ruleset.py:35
msgid "Stock Locations"
msgstr "Skladová umístění"

#: stock/models.py:205 stock/models.py:1133
msgid "Owner"
msgstr "Správce"

#: stock/models.py:206 stock/models.py:1134
msgid "Select Owner"
msgstr ""

#: stock/models.py:214
msgid "Stock items may not be directly located into a structural stock locations, but may be located to child locations."
msgstr "Skladové položky nelze umístit přímo do strukturálních skladových umístění, ale lze je umístit do podřízených skladových umístění."

#: stock/models.py:221 users/models.py:499
msgid "External"
msgstr "Externí"

#: stock/models.py:222
msgid "This is an external stock location"
msgstr "Toto je externí skladové umístění"

#: stock/models.py:228
msgid "Location type"
msgstr "Typ umístění"

#: stock/models.py:232
msgid "Stock location type of this location"
msgstr "Typ tohoto skladového umístění"

#: stock/models.py:304
msgid "You cannot make this stock location structural because some stock items are already located into it!"
msgstr "Toto skladové umístění nemůžete označit jako strukturální, protože již obsahuje skladové položky!"

#: stock/models.py:562
msgid "Part must be specified"
msgstr "Díl musí být zadán"

#: stock/models.py:827
msgid "Stock items cannot be located into structural stock locations!"
msgstr "Skladové položky nelze umístit do strukturálních skladových umístění!"

#: stock/models.py:854 stock/serializers.py:516
msgid "Stock item cannot be created for virtual parts"
msgstr "Nelze vytvořit skladovou položku pro virtuální díl"

#: stock/models.py:871
#, python-brace-format
msgid "Part type ('{self.supplier_part.part}') must be {self.part}"
msgstr ""

#: stock/models.py:881 stock/models.py:894
msgid "Quantity must be 1 for item with a serial number"
msgstr "Množství musí být 1 pro položku se sériovým číslem"

#: stock/models.py:884
msgid "Serial number cannot be set if quantity greater than 1"
msgstr ""

#: stock/models.py:906
msgid "Item cannot belong to itself"
msgstr "Položka nemůže patřit sama sobě"

#: stock/models.py:911
msgid "Item must have a build reference if is_building=True"
msgstr ""

#: stock/models.py:924
msgid "Build reference does not point to the same part object"
msgstr ""

#: stock/models.py:938
msgid "Parent Stock Item"
msgstr "Nadřazená skladová položka"

#: stock/models.py:950
msgid "Base part"
msgstr ""

#: stock/models.py:960
msgid "Select a matching supplier part for this stock item"
msgstr "Vyberte odpovídající díl dodavatele pro tuto skladovou položku"

#: stock/models.py:972
msgid "Where is this stock item located?"
msgstr "Kde se tato skladová položka nachází?"

#: stock/models.py:980 stock/serializers.py:1667
msgid "Packaging this stock item is stored in"
msgstr "Balení, ve kterém je tato skladová položka uložena"

#: stock/models.py:986
msgid "Installed In"
msgstr "Instalováno v"

#: stock/models.py:991
msgid "Is this item installed in another item?"
msgstr "Je tato položka nainstalována v jiné položce?"

#: stock/models.py:1010
msgid "Serial number for this item"
msgstr "Sériové číslo pro tuto položku"

#: stock/models.py:1027 stock/serializers.py:1652
msgid "Batch code for this stock item"
msgstr "Kód šarže pro tuto skladovou položku"

#: stock/models.py:1032
msgid "Stock Quantity"
msgstr ""

#: stock/models.py:1042
msgid "Source Build"
msgstr ""

#: stock/models.py:1045
msgid "Build for this stock item"
msgstr ""

#: stock/models.py:1052
msgid "Consumed By"
msgstr ""

#: stock/models.py:1055
msgid "Build order which consumed this stock item"
msgstr "Výrobní příkaz, který spotřeboval tuto skladovou položku"

#: stock/models.py:1064
msgid "Source Purchase Order"
msgstr "Zdrojová nákupní objednávka"

#: stock/models.py:1068
msgid "Purchase order for this stock item"
msgstr "Nákupní objednávka pro tuto skladovou položku"

#: stock/models.py:1074
msgid "Destination Sales Order"
msgstr ""

#: stock/models.py:1085
msgid "Expiry date for stock item. Stock will be considered expired after this date"
msgstr ""

#: stock/models.py:1103
msgid "Delete on deplete"
msgstr ""

#: stock/models.py:1104
msgid "Delete this Stock Item when stock is depleted"
msgstr "Odstranit tuto skladovou položku po vyčerpání zásob"

#: stock/models.py:1125
msgid "Single unit purchase price at time of purchase"
msgstr ""

#: stock/models.py:1156
msgid "Converted to part"
msgstr ""

#: stock/models.py:1721
msgid "Part is not set as trackable"
msgstr ""

#: stock/models.py:1727
msgid "Quantity must be integer"
msgstr ""

#: stock/models.py:1735
#, python-brace-format
msgid "Quantity must not exceed available stock quantity ({self.quantity})"
msgstr ""

#: stock/models.py:1741
msgid "Serial numbers must be provided as a list"
msgstr ""

#: stock/models.py:1746
msgid "Quantity does not match serial numbers"
msgstr ""

#: stock/models.py:1868 stock/models.py:2777
msgid "Test template does not exist"
msgstr ""

#: stock/models.py:1886
msgid "Stock item has been assigned to a sales order"
msgstr "Skladová položka byla přidělena prodejní objednávce"

#: stock/models.py:1890
msgid "Stock item is installed in another item"
msgstr "Skladová položka je nainstalována v jiné položce"

#: stock/models.py:1893
msgid "Stock item contains other items"
msgstr "Skladová položka obsahuje jiné položky"

#: stock/models.py:1896
msgid "Stock item has been assigned to a customer"
msgstr "Skladová položka byla přidělena zákazníkovi"

#: stock/models.py:1899 stock/models.py:2073
msgid "Stock item is currently in production"
msgstr "Skladová položka je ve výrobě"

#: stock/models.py:1902
msgid "Serialized stock cannot be merged"
msgstr ""

#: stock/models.py:1909 stock/serializers.py:1545
msgid "Duplicate stock items"
msgstr "Duplicitní skladové položky"

#: stock/models.py:1913
msgid "Stock items must refer to the same part"
msgstr "Skladové položky musí odkazovat na stejný díl"

#: stock/models.py:1921
msgid "Stock items must refer to the same supplier part"
msgstr "Skladové položky musí odkazovat na stejný díl dodavatele"

#: stock/models.py:1926
msgid "Stock status codes must match"
msgstr ""

#: stock/models.py:2196
msgid "StockItem cannot be moved as it is not in stock"
msgstr "Zásobová položka nemůže být přesunuta, protože není skladem"

#: stock/models.py:2678
msgid "Stock Item Tracking"
msgstr "Sledování skladových položek"

#: stock/models.py:2709
msgid "Entry notes"
msgstr ""

#: stock/models.py:2749
msgid "Stock Item Test Result"
msgstr "Výsledek testu skladové položky"

#: stock/models.py:2780
msgid "Value must be provided for this test"
msgstr ""

#: stock/models.py:2784
msgid "Attachment must be uploaded for this test"
msgstr ""

#: stock/models.py:2789
msgid "Invalid value for this test"
msgstr ""

#: stock/models.py:2813
msgid "Test result"
msgstr "Výsledek testu"

#: stock/models.py:2820
msgid "Test output value"
msgstr ""

#: stock/models.py:2828 stock/serializers.py:259
msgid "Test result attachment"
msgstr ""

#: stock/models.py:2832
msgid "Test notes"
msgstr ""

#: stock/models.py:2840
msgid "Test station"
msgstr ""

#: stock/models.py:2841
msgid "The identifier of the test station where the test was performed"
msgstr ""

#: stock/models.py:2847
msgid "Started"
msgstr ""

#: stock/models.py:2848
msgid "The timestamp of the test start"
msgstr ""

#: stock/models.py:2854
msgid "Finished"
msgstr ""

#: stock/models.py:2855
msgid "The timestamp of the test finish"
msgstr ""

#: stock/serializers.py:84
msgid "Generated batch code"
msgstr "Vygenerovaný kód šarže"

#: stock/serializers.py:93
msgid "Select build order"
msgstr "Vyberte výrobní příkaz"

#: stock/serializers.py:102
msgid "Select stock item to generate batch code for"
msgstr "Vyberte skladovou položku, pro kterou se má vygenerovat kód šarže"

#: stock/serializers.py:111
msgid "Select location to generate batch code for"
msgstr "Vyberte umístění, pro které se má vygenerovat kód šarže"

#: stock/serializers.py:120
msgid "Select part to generate batch code for"
msgstr "Vyberte díl, pro který se má vygenerovat kód šarže"

#: stock/serializers.py:129
msgid "Select purchase order"
msgstr "Vyberte nákupní objednávku"

#: stock/serializers.py:136
msgid "Enter quantity for batch code"
msgstr ""

#: stock/serializers.py:162
msgid "Generated serial number"
msgstr "Vygenerované sériové číslo"

#: stock/serializers.py:172
msgid "Select part to generate serial number for"
msgstr "Vyberte díl, pro který se má vygenerovat sériové číslo"

#: stock/serializers.py:180
msgid "Quantity of serial numbers to generate"
msgstr ""

#: stock/serializers.py:248
msgid "Test template for this result"
msgstr ""

#: stock/serializers.py:272
msgid "Template ID or test name must be provided"
msgstr ""

#: stock/serializers.py:304
msgid "The test finished time cannot be earlier than the test started time"
msgstr "Čas ukončení testu nesmí být dřívější než čas zahájení testu"

#: stock/serializers.py:341
msgid "Serial number is too large"
msgstr "Sériové číslo je příliš velké"

#: stock/serializers.py:487
msgid "Parent Item"
msgstr "Nadřazená položka"

#: stock/serializers.py:488
msgid "Parent stock item"
msgstr "Nadřazená skladová položka"

#: stock/serializers.py:508
msgid "Use pack size when adding: the quantity defined is the number of packs"
msgstr ""

#: stock/serializers.py:618
msgid "Supplier Part Number"
msgstr "Číslo dílu dodavatele"

#: stock/serializers.py:665 users/models.py:193
msgid "Expired"
msgstr ""

#: stock/serializers.py:671
msgid "Child Items"
msgstr "Podřízené položky"

#: stock/serializers.py:675
msgid "Tracking Items"
msgstr ""

#: stock/serializers.py:681
msgid "Purchase price of this stock item, per unit or pack"
msgstr "Nákupní cena této skladové položky za jednotku nebo balení"

#: stock/serializers.py:719
msgid "Enter number of stock items to serialize"
msgstr ""

#: stock/serializers.py:732
#, python-brace-format
msgid "Quantity must not exceed available stock quantity ({q})"
msgstr ""

#: stock/serializers.py:739
msgid "Enter serial numbers for new items"
msgstr "Zadejte sériová čísla pro nové položky"

#: stock/serializers.py:750 stock/serializers.py:1502 stock/serializers.py:1790
msgid "Destination stock location"
msgstr "Cílové skladové umístění"

#: stock/serializers.py:757
msgid "Optional note field"
msgstr "Volitelné pole pro poznámku"

#: stock/serializers.py:767
msgid "Serial numbers cannot be assigned to this part"
msgstr "K tomuto dílu nelze přiřadit sériová čísla"

#: stock/serializers.py:787
msgid "Serial numbers already exist"
msgstr "Sériová čísla již existují"

#: stock/serializers.py:826
msgid "Select stock item to install"
msgstr "Vyberte skladovou položku k instalaci"

#: stock/serializers.py:833
msgid "Quantity to Install"
msgstr ""

#: stock/serializers.py:834
msgid "Enter the quantity of items to install"
msgstr "Zadejte množství položek k instalaci"

#: stock/serializers.py:839 stock/serializers.py:919 stock/serializers.py:1049
#: stock/serializers.py:1101
msgid "Add transaction note (optional)"
msgstr ""

#: stock/serializers.py:847
msgid "Quantity to install must be at least 1"
msgstr ""

#: stock/serializers.py:855
msgid "Stock item is unavailable"
msgstr "Skladová položka je nedostupná"

#: stock/serializers.py:866
msgid "Selected part is not in the Bill of Materials"
msgstr "Vybraný díl není v kusovníku"

#: stock/serializers.py:879
msgid "Quantity to install must not exceed available quantity"
msgstr ""

#: stock/serializers.py:914
msgid "Destination location for uninstalled item"
msgstr "Cílové umístění pro odinstalovanou položku"

#: stock/serializers.py:949
msgid "Select part to convert stock item into"
msgstr ""

#: stock/serializers.py:962
msgid "Selected part is not a valid option for conversion"
msgstr "Vybraný díl není platnou volbou pro převod"

#: stock/serializers.py:979
msgid "Cannot convert stock item with assigned SupplierPart"
msgstr ""

#: stock/serializers.py:1013
msgid "Stock item status code"
msgstr "Stavový kód skladové položky"

#: stock/serializers.py:1042
msgid "Destination location for returned item"
msgstr "Cílové umístění pro vrácené položky"

#: stock/serializers.py:1085
msgid "Select stock items to change status"
msgstr "Vybrat skladové položky pro změnu stavu"

#: stock/serializers.py:1091
msgid "No stock items selected"
msgstr "Nejsou vybrány žádné skladové položky"

#: stock/serializers.py:1185 stock/serializers.py:1262
msgid "Sublocations"
msgstr "Podumístění"

#: stock/serializers.py:1257
msgid "Parent stock location"
msgstr "Nadřazené skladové umístění"

#: stock/serializers.py:1374
msgid "Part must be salable"
msgstr ""

#: stock/serializers.py:1378
msgid "Item is allocated to a sales order"
msgstr "Položka je přidělena prodejní objednávce"

#: stock/serializers.py:1382
msgid "Item is allocated to a build order"
msgstr "Položka je přidělena výrobnímu příkazu"

#: stock/serializers.py:1406
msgid "Customer to assign stock items"
msgstr "Zákazník, kterému mají být přiděleny skladové položky"

#: stock/serializers.py:1412
msgid "Selected company is not a customer"
msgstr ""

#: stock/serializers.py:1420
msgid "Stock assignment notes"
msgstr ""

#: stock/serializers.py:1430 stock/serializers.py:1695
msgid "A list of stock items must be provided"
msgstr "Musí být poskytnut seznam skladových položek"

#: stock/serializers.py:1509
msgid "Stock merging notes"
msgstr ""

#: stock/serializers.py:1514
msgid "Allow mismatched suppliers"
msgstr ""

#: stock/serializers.py:1515
msgid "Allow stock items with different supplier parts to be merged"
msgstr "Povolit sloučení skladových položek s různými díly dodavatele"

#: stock/serializers.py:1520
msgid "Allow mismatched status"
msgstr ""

#: stock/serializers.py:1521
msgid "Allow stock items with different status codes to be merged"
msgstr "Povolit sloučení skladových položek s různými stavovými kódy"

#: stock/serializers.py:1531
msgid "At least two stock items must be provided"
msgstr "Musí být poskytnuty alespoň dvě skladové položky"

#: stock/serializers.py:1598
msgid "No Change"
msgstr "Beze změny"

#: stock/serializers.py:1627
msgid "StockItem primary key value"
msgstr "Hodnota primárního klíče skladové položky"

#: stock/serializers.py:1639
msgid "Stock item is not in stock"
msgstr "Skladová položka není skladem"

#: stock/serializers.py:1685
msgid "Stock transaction notes"
msgstr ""

#: stock/serializers.py:1831
msgid "Next Serial Number"
msgstr ""

#: stock/serializers.py:1837
msgid "Previous Serial Number"
msgstr ""

#: stock/status_codes.py:11
msgid "OK"
msgstr "OK"

#: stock/status_codes.py:12
msgid "Attention needed"
msgstr "Vyžaduje pozornost"

#: stock/status_codes.py:13
msgid "Damaged"
msgstr "Poškozeno"

#: stock/status_codes.py:14
msgid "Destroyed"
msgstr "Zničeno"

#: stock/status_codes.py:15
msgid "Rejected"
msgstr "Odmítnuto"

#: stock/status_codes.py:19
msgid "Quarantined"
msgstr "V karanténě"

#: stock/status_codes.py:44
msgid "Legacy stock tracking entry"
msgstr "Původní položka sledování zásob"

#: stock/status_codes.py:46
msgid "Stock item created"
msgstr "Skladová položka vytvořena"

#: stock/status_codes.py:49
msgid "Edited stock item"
msgstr "Skladová položka upravena"

#: stock/status_codes.py:50
msgid "Assigned serial number"
msgstr "Přiřazeno výrobní číslo"

#: stock/status_codes.py:53
msgid "Stock counted"
msgstr "Stav zásob sečten"

#: stock/status_codes.py:54
msgid "Stock manually added"
msgstr "Zásoba přidána ručně"

#: stock/status_codes.py:55
msgid "Stock manually removed"
msgstr "Zásoba odebrána ručně"

#: stock/status_codes.py:58
msgid "Location changed"
msgstr "Umístění změněno"

#: stock/status_codes.py:59
msgid "Stock updated"
msgstr "Stav zásob byl aktualizován"

#: stock/status_codes.py:62
msgid "Installed into assembly"
msgstr "Nainstalováno do sestavy"

#: stock/status_codes.py:63
msgid "Removed from assembly"
msgstr "Odstraněno ze sestavy"

#: stock/status_codes.py:65
msgid "Installed component item"
msgstr "Instalovaná položka komponenty"

#: stock/status_codes.py:66
msgid "Removed component item"
msgstr "Odstraněná komponenta"

#: stock/status_codes.py:69
msgid "Split from parent item"
msgstr "Rozdělit od nadřazené položky"

#: stock/status_codes.py:70
msgid "Split child item"
msgstr "Rozdělit podřazený předmět"

#: stock/status_codes.py:73
msgid "Merged stock items"
msgstr "Skladové položky sloučeny"

#: stock/status_codes.py:76
msgid "Converted to variant"
msgstr "Převedeno na variantu"

#: stock/status_codes.py:79
msgid "Build order output created"
msgstr "Výstup výrobního příkazu vytvořen"

#: stock/status_codes.py:80
msgid "Build order output completed"
msgstr "Výstup výrobního příkazu dokončen"

#: stock/status_codes.py:81
msgid "Build order output rejected"
msgstr "Výstup výrobního příkazu odmítnut"

#: stock/status_codes.py:82
msgid "Consumed by build order"
msgstr "Spotřebováno výrobním příkazem"

#: stock/status_codes.py:85
msgid "Shipped against Sales Order"
msgstr "Odesláno na základě prodejní objednávky"

#: stock/status_codes.py:88
msgid "Received against Purchase Order"
msgstr "Přijato proti objednávce"

#: stock/status_codes.py:91
msgid "Returned against Return Order"
msgstr "Vráceno proti vratce"

#: stock/status_codes.py:94
msgid "Sent to customer"
msgstr "Odesláno zákazníkovi"

#: stock/status_codes.py:95
msgid "Returned from customer"
msgstr "Vráceno od zákazníka"

#: templates/403.html:6 templates/403.html:10 templates/403_csrf.html:7
msgid "Permission Denied"
msgstr "Přístup odepřen"

#: templates/403.html:11
msgid "You do not have permission to view this page."
msgstr "Nemáte oprávnění k prohlížení této stránky."

#: templates/403_csrf.html:11
msgid "Authentication Failure"
msgstr "Chyba ověření"

#: templates/403_csrf.html:12
msgid "You have been logged out from InvenTree."
msgstr "Byli jste odhlášeni z InvenTree."

#: templates/404.html:6 templates/404.html:10
msgid "Page Not Found"
msgstr "Stránka nebyla nalezena"

#: templates/404.html:11
msgid "The requested page does not exist"
msgstr "Požadovaná stránka neexistuje"

#: templates/500.html:6 templates/500.html:10
msgid "Internal Server Error"
msgstr "Interní chyba serveru"

#: templates/500.html:11
#, python-format
msgid "The %(inventree_title)s server raised an internal error"
msgstr ""

#: templates/500.html:12
msgid "Refer to the error log in the admin interface for further details"
msgstr ""

#: templates/503.html:11 templates/503.html:15
msgid "Site is in Maintenance"
msgstr "Stránky jsou v údržbě"

#: templates/503.html:17
msgid "The site is currently in maintenance and should be up again soon!"
msgstr "Stránky jsou v současné době v údržbě a měly by být brzy opět v provozu!"

#: templates/base.html:51
msgid "Server Restart Required"
msgstr "Vyžadován restart serveru"

#: templates/base.html:54
msgid "A configuration option has been changed which requires a server restart"
msgstr "Bylo změněno nastavení, které vyžaduje restart serveru"

#: templates/base.html:54 templates/base.html:64
msgid "Contact your system administrator for further information"
msgstr "Pro další informace kontaktujte správce systému"

#: templates/base.html:61
msgid "Pending Database Migrations"
msgstr ""

#: templates/base.html:64
msgid "There are pending database migrations which require attention"
msgstr ""

#: templates/email/build_order_completed.html:9
#: templates/email/canceled_order_assigned.html:9
#: templates/email/new_order_assigned.html:9
#: templates/email/overdue_build_order.html:9
#: templates/email/overdue_purchase_order.html:9
#: templates/email/overdue_return_order.html:9
#: templates/email/overdue_sales_order.html:9
#: templates/email/purchase_order_received.html:9
#: templates/email/return_order_received.html:9
msgid "Click on the following link to view this order"
msgstr ""

#: templates/email/build_order_required_stock.html:7
msgid "Stock is required for the following build order"
msgstr "Pro následující výrobní příkaz jsou potřeba zásoby"

#: templates/email/build_order_required_stock.html:8
#, python-format
msgid "Build order %(build)s - building %(quantity)s x %(part)s"
msgstr "Výrobní príkaz %(build)s - výroba %(quantity)s x %(part)s"

#: templates/email/build_order_required_stock.html:10
msgid "Click on the following link to view this build order"
msgstr "Klikněte na následující odkaz pro zobrazení tohoto výrobního příkazu"

#: templates/email/build_order_required_stock.html:14
msgid "The following parts are low on required stock"
msgstr ""

#: templates/email/build_order_required_stock.html:18
msgid "Required Quantity"
msgstr "Požadované množství"

#: templates/email/build_order_required_stock.html:38
#: templates/email/low_stock_notification.html:30
msgid "You are receiving this email because you are subscribed to notifications for this part "
msgstr "Tento e-mail jste obdrželi, protože jste přihlášeni k odběru oznámení o tomto dílu"

#: templates/email/low_stock_notification.html:9
#: templates/email/part_event_notification.html:9
msgid "Click on the following link to view this part"
msgstr "Klikněte na následující odkaz pro zobrazení tohoto dílu"

#: templates/email/low_stock_notification.html:18
#: templates/email/part_event_notification.html:19
msgid "Minimum Quantity"
msgstr "Minimální množství"

#: templates/email/part_event_notification.html:32
msgid "You are receiving this email because you are subscribed to notifications for this part or a category that it is part of "
msgstr ""

#: users/admin.py:101
msgid "Users"
msgstr "Uživatelé"

#: users/admin.py:102
msgid "Select which users are assigned to this group"
msgstr "Vyberte, kteří uživatelé jsou zařazeni do této skupiny"

#: users/admin.py:137
msgid "Personal info"
msgstr "Osobní údaje"

#: users/admin.py:139
msgid "Permissions"
msgstr "Oprávnění"

#: users/admin.py:142
msgid "Important dates"
msgstr "Důležité termíny"

#: users/authentication.py:30 users/models.py:157
msgid "Token has been revoked"
msgstr ""

#: users/authentication.py:33
msgid "Token has expired"
msgstr "Token vypršel"

#: users/models.py:100
msgid "API Token"
msgstr "API token"

#: users/models.py:101
msgid "API Tokens"
msgstr "API tokeny"

#: users/models.py:137
msgid "Token Name"
msgstr "Název tokenu"

#: users/models.py:138
msgid "Custom token name"
msgstr "Uživatelský název tokenu"

#: users/models.py:144
msgid "Token expiry date"
msgstr "Datum vypršení tokenu"

#: users/models.py:152
msgid "Last Seen"
msgstr ""

#: users/models.py:153
msgid "Last time the token was used"
msgstr "Čas posledního použití tokenu"

#: users/models.py:157
msgid "Revoked"
msgstr ""

#: users/models.py:235
msgid "Permission set"
msgstr "Nastavení oprávnění"

#: users/models.py:244
msgid "Group"
msgstr "Skupina"

#: users/models.py:248
msgid "View"
msgstr "Zobrazit"

#: users/models.py:248
msgid "Permission to view items"
msgstr "Oprávnění k zobrazení položek"

#: users/models.py:252
msgid "Add"
msgstr "Přidat"

#: users/models.py:252
msgid "Permission to add items"
msgstr "Oprávnění přidat položky"

#: users/models.py:256
msgid "Change"
msgstr "Změnit"

#: users/models.py:258
msgid "Permissions to edit items"
msgstr "Oprávnění k úpravě položek"

#: users/models.py:262
msgid "Delete"
msgstr "Odstranit"

#: users/models.py:264
msgid "Permission to delete items"
msgstr "Oprávnění k odstranění položek"

#: users/models.py:497
msgid "Bot"
msgstr ""

#: users/models.py:498
msgid "Internal"
msgstr ""

#: users/models.py:500
msgid "Guest"
msgstr ""

#: users/models.py:509
msgid "Language"
msgstr ""

#: users/models.py:510
msgid "Preferred language for the user"
msgstr ""

#: users/models.py:515
msgid "Theme"
msgstr ""

#: users/models.py:516
msgid "Settings for the web UI as JSON - do not edit manually!"
msgstr ""

#: users/models.py:521
msgid "Widgets"
msgstr ""

#: users/models.py:523
msgid "Settings for the dashboard widgets as JSON - do not edit manually!"
msgstr ""

#: users/models.py:530
msgid "Display Name"
msgstr ""

#: users/models.py:531
msgid "Chosen display name for the user"
msgstr ""

#: users/models.py:537
msgid "Position"
msgstr ""

#: users/models.py:538
msgid "Main job title or position"
msgstr ""

#: users/models.py:545
msgid "User status message"
msgstr ""

#: users/models.py:552
msgid "User location information"
msgstr ""

#: users/models.py:557
msgid "User is actively using the system"
msgstr ""

#: users/models.py:564
msgid "Preferred contact information for the user"
msgstr ""

#: users/models.py:570
msgid "User Type"
msgstr ""

#: users/models.py:571
msgid "Which type of user is this?"
msgstr ""

#: users/models.py:577
msgid "Organisation"
msgstr ""

#: users/models.py:578
msgid "Users primary organisation/affiliation"
msgstr ""

#: users/models.py:586
msgid "Primary Group"
msgstr ""

#: users/models.py:587
msgid "Primary group for the user"
msgstr ""

#: users/ruleset.py:31
msgid "Admin"
msgstr ""

#: users/ruleset.py:34
msgid "Stocktake"
msgstr "Inventura"

#: users/ruleset.py:38
msgid "Purchase Orders"
msgstr "Zakoupené objednávky"

#: users/ruleset.py:39
msgid "Sales Orders"
msgstr "Prodejní objednávky"

#: users/ruleset.py:40
msgid "Return Orders"
msgstr "Návratové objednávky"

#: users/serializers.py:236
msgid "Username"
msgstr "Uživatelské jméno"

#: users/serializers.py:239
msgid "First Name"
msgstr "Křestní jméno"

#: users/serializers.py:239
msgid "First name of the user"
msgstr "Křestní jméno uživatele"

#: users/serializers.py:243
msgid "Last Name"
msgstr "Příjmení"

#: users/serializers.py:243
msgid "Last name of the user"
msgstr "Příjmení uživatele"

#: users/serializers.py:247
msgid "Email address of the user"
msgstr "Emailová adresa uživatele"

#: users/serializers.py:323
msgid "Staff"
msgstr "Personál"

#: users/serializers.py:324
msgid "Does this user have staff permissions"
msgstr "Má tento uživatel oprávnění personálu"

#: users/serializers.py:329
msgid "Superuser"
msgstr "Super-uživatel"

#: users/serializers.py:329
msgid "Is this user a superuser"
msgstr "Je tento uživatel superuživatel"

#: users/serializers.py:333
msgid "Is this user account active"
msgstr "Je tento uživatelský účet aktivní"

#: users/serializers.py:345
msgid "Only a superuser can adjust this field"
msgstr ""

#: users/serializers.py:399
msgid "Only staff users can create new users"
msgstr ""

#: users/serializers.py:404
msgid "You do not have permission to create users"
msgstr ""

#: users/serializers.py:425
msgid "Your account has been created."
msgstr "Váš účet byl vytvořen."

#: users/serializers.py:427
msgid "Please use the password reset function to login"
msgstr "Pro přihlášení použijte funkci obnovení hesla"

#: users/serializers.py:433
msgid "Welcome to InvenTree"
msgstr "Vítejte v InvenTree"

