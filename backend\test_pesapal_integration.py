#!/usr/bin/env python
"""
Test script for Pesapal payment integration
Run this to test the complete payment flow
"""
import os
import sys
import django
from decimal import Decimal

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'deepforest_backend.settings')
django.setup()

from django.contrib.auth.models import User
from payments.models import Order, OrderItem
from payments.pesapal_client import PesapalClient
from payments.pesapal_webhooks import PesapalWebhookHandler
from ecommerce.models import Product, Category


def create_test_data():
    """Create test data for payment testing"""
    print("Creating test data...")
    
    # Create test user
    user, created = User.objects.get_or_create(
        username='testuser',
        defaults={
            'email': '<EMAIL>',
            'first_name': 'Test',
            'last_name': 'User'
        }
    )
    
    # Create test category and product
    category, created = Category.objects.get_or_create(
        name='Test Category',
        defaults={'description': 'Test category for payment testing'}
    )
    
    product, created = Product.objects.get_or_create(
        sku='TEST001',
        defaults={
            'name': 'Test Product',
            'description': 'Test product for payment testing',
            'price_ksh': Decimal('1000.00'),
            'category': category,
            'stock_quantity': 10
        }
    )
    
    return user, product


def test_payment_initiation():
    """Test payment initiation with Pesapal"""
    print("\n=== Testing Payment Initiation ===")
    
    user, product = create_test_data()
    
    # Create test order
    import time
    order_ref = f'TEST{user.id}{int(time.time())}'
    order = Order.objects.create(
        user=user,
        order_reference=order_ref,
        total_amount_ksh=Decimal('1000.00'),
        delivery_cost_ksh=Decimal('200.00'),
        delivery_address='123 Test Street, Nairobi',
        delivery_county='nairobi'
    )
    
    # Create order item
    OrderItem.objects.create(
        order=order,
        product=product,
        quantity=1,
        unit_price_ksh=product.price_ksh
    )
    
    # Test payment initiation
    try:
        client = PesapalClient()
        
        payment_data = {
            'amount': str(order.total_with_delivery),
            'description': f'DeepForest Hardware Order {order.order_reference}',
            'type': 'MERCHANT',
            'reference': order.order_reference,
            'first_name': user.first_name,
            'last_name': user.last_name,
            'email': user.email,
            'phone': '254700000000'
        }
        
        iframe_url = client.initiate_payment(payment_data)
        
        print(f"✓ Payment initiation successful!")
        print(f"  Order Reference: {order.order_reference}")
        print(f"  Amount: KSH {order.total_with_delivery}")
        print(f"  Iframe URL: {iframe_url[:100]}...")
        
        return order, iframe_url
        
    except Exception as e:
        print(f"✗ Payment initiation failed: {str(e)}")
        return None, None


def test_payment_status_query():
    """Test payment status query"""
    print("\n=== Testing Payment Status Query ===")
    
    try:
        client = PesapalClient()
        
        # Test with dummy data (will fail but shows the flow)
        result = client.query_payment_status('TEST001', 'dummy_tracking_id')
        
        print(f"✓ Status query successful: {result}")
        
    except Exception as e:
        print(f"✗ Status query failed (expected with dummy data): {str(e)}")


def test_webhook_processing():
    """Test webhook processing"""
    print("\n=== Testing Webhook Processing ===")
    
    try:
        webhook_handler = PesapalWebhookHandler()
        
        # Test callback data
        callback_data = {
            'pesapal_notification_type': 'CHANGE',
            'pesapal_transaction_tracking_id': 'dummy_tracking_id',
            'pesapal_merchant_reference': 'TEST001'
        }
        
        # This will fail because the order doesn't exist, but shows the flow
        result = webhook_handler.process_ipn_callback(callback_data)
        
        print(f"✓ Webhook processing result: {result}")
        
    except Exception as e:
        print(f"✗ Webhook processing failed (expected with dummy data): {str(e)}")


def main():
    """Run all tests"""
    print("🚀 Starting Pesapal Integration Tests")
    print("=" * 50)
    
    # Test 1: Payment Initiation
    order, iframe_url = test_payment_initiation()
    
    # Test 2: Payment Status Query
    test_payment_status_query()
    
    # Test 3: Webhook Processing
    test_webhook_processing()
    
    print("\n" + "=" * 50)
    print("✅ Pesapal Integration Tests Completed")
    
    if order and iframe_url:
        print(f"\n📋 Test Order Created:")
        print(f"   ID: {order.id}")
        print(f"   Reference: {order.order_reference}")
        print(f"   Total: KSH {order.total_with_delivery}")
        print(f"\n🔗 Payment URL (for manual testing):")
        print(f"   {iframe_url}")
        print(f"\n💡 Next Steps:")
        print(f"   1. Use the payment URL in an iframe for testing")
        print(f"   2. Configure your Pesapal credentials in .env file")
        print(f"   3. Set up webhook endpoints for production")


if __name__ == '__main__':
    main()
