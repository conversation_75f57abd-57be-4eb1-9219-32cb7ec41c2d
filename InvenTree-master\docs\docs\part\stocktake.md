---
title: Part Stocktake
---

## Part Stocktake

A *Stocktake* refers to a "snapshot" of stock levels for a particular part, at a specific point in time. Stocktake information is used for tracking a historical record of the quantity and value of part stock.

In particular, an individual *Stocktake* record tracks the following information:

- The date of the Stocktake event
- A reference to the [part](./index.md) which is being counted
- The total number of individual [stock items](../stock/index.md) available
- The total stock quantity of available stock
- The total cost of stock on hand

### Stock Items vs Stock Quantity

*Stock Items* refers to the number of stock entries (e.g. *"3 reels of capacitors"*). *Stock Quantity* refers to the total cumulative stock count (e.g. *"4,560 total capacitors"*).

### Cost of Stock on Hand

The total cost of stock on hand is calculated based on the provided pricing data. For stock items which have a recorded *cost* (e.g. *purchase price*), this value is used. If no direct pricing information is available for a particular stock item, the price range of the part itself is used.

!!! info "Cost Range"
    Cost data is provided as a *range* of values, accounting for any variability in available pricing data.

### Display Stocktake Data

Historical stocktake data for a particular part can be viewed in the *Stocktake* tab, available on the *Part* page.

This tab displays a chart of historical stock quantity and cost data, and corresponding tabulated data:

{% with id="stocktake_tab", url="part/part_stocktake_tab.png", description="Part stocktake tab" %}
{% include 'img.html' %}
{% endwith %}

If this tab is not visible, ensure that the *Part Stocktake* [user setting](../settings/user.md) is enabled in the *Display Settings* section.

{% with id="stocktake_tab_enable", url="part/part_stocktake_enable_tab.png", description="Enable stocktake tab" %}
{% include 'img.html' %}
{% endwith %}

!!! info "Permission Required"
    The stocktake tab will be unavailable if your user account does not have the [required permissions](#stocktake-permissions)

## Stocktake Reports

While a *Stocktake* entry records a historical snapshot of stock levels for a single *part*, a *Stocktake Report* is used to generate a report data file which contains stocktake entries for multiple parts. Stocktake reports can be generated for the entire range of parts available in the database, or a subset of parts as determined by user-configurable filters.

Stocktake reports can be [generated manually](#performing-a-stocktake) by the user, or (if enabled) [generated automatically](#automatic-stocktake) at a specified interval.

As there is a lot of data to crunch to build a report, stocktake reports are generated by the [background worker process](../settings/tasks.md). When the report is completed, it is saved to the database and made available for download.

!!! tip "Background Worker"
    If the background worker process is not running, stocktake reports will be unavailable!

Stocktake reports are made available for download as a tabulated `.csv` file, which can be opened in many external applications for further analysis.

## Stocktake Settings

There are a number of configuration options available in the [settings view](../settings/global.md):

{% with id="stocktake_settings", url="part/part_stocktake_settings.png", description="Stocktake settings" %}
{% include 'img.html' %}
{% endwith %}

### Enable Stocktake

Enable or disable stocktake functionality. Note that by default, stocktake functionality is disabled.

### Automatic Stocktake Period

Configure the number of days between genenration of [automatic stocktake reports](#automatic-stocktake). If this value is set to zero, automatic stocktake reports will not be generated.

### Delete Old Reports

Configure how many days stocktake reports will be retained, before being deleted automatically.

### Historical Stocktake Reports

The *Stocktake Settings* display also provides a table of historical stocktake reports:

{% with id="stocktake_report_table", url="part/part_stocktake_report_table.png", description="Stocktake reports" %}
{% include 'img.html' %}
{% endwith %}

## Stocktake Permissions

Stocktake data and actions are protected by the [stocktake role](../settings/permissions.md#role):

| Permission | Actions Available |
| --- | --- |
| `stocktake.view` | View historical stocktake data for parts |
| `stocktake.add` | Perform stocktake and generate reports |
| `stocktake.delete` | Delete stocktake records and reports |

## Performing a Stocktake

Manual stocktake can be performed via the web interface in a number of locations. The user can filter the parts for which the stocktake will be performed. A new stocktake entry will be generated for each selected part, and optionally a report can be generated for download.

When performing a stocktake, various options are presented to the user:

{% with id="stocktake_generate", url="part/part_stocktake_generate.png", description="Generate stocktake report" %}
{% include 'img.html' %}
{% endwith %}

| Option | Description |
| --- | --- |
| Part | Limit stocktake context to a part. If the selected part is a [template part](./index.md#template), any variant parts will also be included in the stocktake |
| Category | Limit stocktake context to a single [part category](./index.md#part-category). Parts which exist in child categories (under the selected parent category) will also be included. |
| Location | Limit stocktake context to a single [stock location](../stock/index.md#stock-location). Any parts which have stock items contained in this location (or any child locations) will be included in the stocktake |
| Generate Report | Select this option to generate a [stocktake report](#stocktake-reports) for the selected parts. |
| Update Parts | Select this option to save a new stocktake record for each selected part. |

### Part Stocktake

A stockake report for a single part can be generated from the *Stocktake Tab* on the part page:

{% with id="stocktake_part", url="part/part_stocktake_from_part.png", description="Generate part stocktake report" %}
{% include 'img.html' %}
{% endwith %}

### Category Stocktake

A stocktake report for a part category can be generated from the *Part Category* page:

{% with id="stocktake_category", url="part/part_stocktake_from_category.png", description="Generate category stocktake report" %}
{% include 'img.html' %}
{% endwith %}

### Location Stocktake

A stocktake report for a stock location can be generated from the *Stock Location* page:

{% with id="stocktake_location", url="part/part_stocktake_from_location.png", description="Generate location stocktake report" %}
{% include 'img.html' %}
{% endwith %}

### Automatic Stocktake

If enabled, stocktake reports can be generated automatically at a configured interval, specified in number of days. Automatic stocktake reports are performed on the entire database of parts.

### API Functionality

Stocktake actions can also be performed via the [API](../api/index.md).
